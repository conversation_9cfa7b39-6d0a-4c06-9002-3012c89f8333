<template>
  <div>
    <a-card :bordered="false" class="search-card" :bodyStyle="{ height: '100%', padding: '16px' }">
      <!-- 搜索区域 -->
      <div class="table-page-search-wrapper">
        <a-form layout="inline" labelAlign="left">
          <a-row :gutter="10" align="middle">
            <a-col style="height: 32px" :md="8" :sm="24" :xxl="6" :xl="8" :lg="8">
              <a-form-item label="电站" :colon="false">
                <ps-tree-select @change="getChild" :isQueryPs="1" :isPsaDisable="false" :checked="true"
                  style="width: 100%;" :hasMaintenanceStatusParams="true"/>
              </a-form-item>
            </a-col>
            <a-col style="height: 32px" :md="8" :sm="24" :xxl="6" :xl="8" :lg="8">
              <a-form-item label="电量保证类型" :colon="false">
                <a-select :maxTagCount="1" mode="multiple" @change="changeType" v-model="queryParams.elecEnsureType">
                  <a-select-option value="0" :key="''">全部</a-select-option>
                  <a-select-option :value="item.dataValue" v-for="(item, index) in dictMap.elec_ensure_type" :key="index">
                    {{ item.dataLable }}
                  </a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
            <a-col style="height: 32px" :md="6" :sm="12" :xxl="4" :xl="6" :lg="6">
              <a-form-item label="日期" style="margin-left: 8px" :colon="false">
                <a-date-picker class="search-time-picker" placeholder="请选择日期" @change="timeChange"
                  v-model="queryParams.date" :getCalendarContainer="(node) => node.parentNode"
                  :disabled-date="disabledDate" format="YYYY-MM-DD" :allowClear="false" valueFormat="YYYY-MM-DD">
                  <a-icon slot="suffixIcon" type="history" />
                </a-date-picker>
              </a-form-item>
            </a-col>
            <a-col style="height: 32px" :md="8" :sm="24" :xxl="6" :xl="8" :lg="8">
              <a-form-item label="装机容量范围" :colon="false">
                <div class="flex-start">
                  <div class="double-input flex-start">
                    <a-input-number class="rang-input-left" :min="0" :max="999999999" placeholder="请输入"
                      v-model="volumnMin" @change="rangChange('min')" />
                    <a-icon type="swap-right" />
                    <a-input-number class="rang-input-right" :min="0" :max="999999999" placeholder="请输入"
                      v-model="volumnMax" @change="rangChange('max')">
                    </a-input-number>
                  </div>
                  <a-select class="unit-select" :getPopupContainer="(node) => node.parentNode" @change="unitChange"
                    default-value="kW">
                    <a-select-option v-for="item in unitOptions" :value="item.value" :key="item.value">
                      {{ item.text }}
                    </a-select-option>
                  </a-select>
                </div>
              </a-form-item>
            </a-col>
            <a-button class="solar-eye-btn-primary margin-left-24" :loading="listLoading"
              @click="refreshScrollLoading(true)">
              查询
            </a-button>
            <div class="open-btn"></div>
          </a-row>
        </a-form>
      </div>
    </a-card>

    <a-card class="card-area" :style="{ height: height + 'px' }" @scroll="getScroll">
      <!--排序头部-->

      <div class="sort-header">
        <div class="desc">为您筛选到符合条件的电站&nbsp;<span class="desc-color">{{ total }}</span>&nbsp;/{{ selectLength }}座</div>
        <div class="sort-header-item" v-for="(item, index) in sortOptions"
          :class="item.state ? 'sort-header-item-active' : ''" @click="handleSort(index)" :key="item.psId">
          <span class="sort-header-item-text">{{ item.label }}</span>
          <svg-icon class="sort-header-item-icon"
            :iconClass="!item.isReverse ? 'sortMaxToMin' : 'sortMinToMax'"></svg-icon>
        </div>
      </div>
      <a-row :gutter="[16, 16]" style="margin:0;">
        <a-col v-for="(item, index) in dataSource" :span="6" :key="index"
          :style="{ paddingRight: index % 4 == 3 ? '0' : '8px', paddingLeft: '0' }">
          <div class="power-station-card" @click="goDetail(item.psId, item.psName)">

            <div class="power-station-card-top">
              <span class="ps-name" :class="{ 'solareye-color-off-line': haveReadPsIds.includes(item.psId) }">{{
                item.psName }}</span>
              <div v-if="item.statusSign !== null && item.statusSign !== ''" style="position: relative"
                class="solar-eye-seal-div">
                <img :src="navTheme == 'dark' ? darkSealBg : lightSealBg" />
                <span class="solar-eye-seal-name">{{ item.statusSignName }}</span>
              </div>
              <!-- <div style="position: relative" >
                <img style="width: 88px;height: 80px;" src="@/assets/images/public/seal.png"/>
                <span class="power-station-mark">{{item.statusSignName}}</span>
              </div> -->
            </div>
            <div class="power-station-card-bottom">
              <div class="rate-item">
                <!-- <div class="rate-item-left">
                  <p class="label">完整率</p>
                  <h1 class="num">{{item.intateRate == null ? '--' : item.intateRate + '%'}}</h1>
                </div>
                <a-progress
                  class="rate-item-right"
                  type="circle"
                  width="64px"
                  strokeLinecap="square"
                  strokeWidth="20"
                  :showInfo="false"
                  :stroke-color="{
                      '0%': '#FEC169',
                      '100%': '#FD8D6B',
                    }"
                  :percent="item.intateRate == null ? 0 :(item.intateRate < 7.5 ? item.intateRate : item.intateRate - 7.5) "
                /> -->
                <quality-gauge name="完整率" :value="item.intateRate" :theme="navTheme"></quality-gauge>
              </div>
              <!-- <div class="devide-line">

              </div>-->
              <div class="rate-item">
                <quality-gauge name="有效率" :value="item.effectiveRate" :theme="navTheme"></quality-gauge>
                <!-- <div class="rate-item-left">
                  <p class="label">有效率</p>
                  <h1 class="num">{{item.effectiveRate== null ? '--' : item.effectiveRate + '%'}}</h1>
                </div>
                <a-progress
                  class="rate-item-right"
                  type="circle"
                  width="64px"
                  strokeLinecap="square"
                  strokeWidth="20"
                  :showInfo="false"
                  :stroke-color="{
                      '0%': '#8C9CFE',
                      '100%': '#6CC1FF',
                    }"
                  :percent="item.intateRate == null ? 0 :(item.effectiveRate < 7.5 ? item.effectiveRate : item.effectiveRate - 7.5) "
                /> -->
              </div>
            </div>
          </div>
        </a-col>
      </a-row>
      <infinite-loading :identifier="infiniteId" v-if="dataSource.length !== 1" @infinite="loadData">
        <div slot="spinner">
          <a-spin size="large" class="card-spin"></a-spin>
        </div>
        <div slot="no-more">
          <p class="no-more-data">无更多数据</p>
        </div>
        <div slot="no-results">
          <img class="no-data-img" :src="noDataImg" />
          <p class="no-data-text">暂无数据</p>
        </div>
      </infinite-loading>
    </a-card>

  </div>
</template>

<script>
import InfiniteLoading from 'vue-infinite-loading'; // 滚动加载插件
import initDict from '@/mixins/initDict';
import { stationIntateAndEffectiveList } from '@/api/dataCenter/index';
import moment from 'moment';
import { simpleDebounce } from '@/utils/util'; // 防抖方法
import QualityGauge from './QualityGauge';
import { mixin } from '@/utils/mixin.js';
export default {
  name: 'QualityAnalyseHomepage',
  components: {
    QualityGauge,
    InfiniteLoading
  },
  mixins: [initDict, mixin],

  data () {
    this.loadData = simpleDebounce(this.loadData, 500);

    return {
      queryParams: {
        pageSize: 16,
        currentPage: 1,
        order: '',
        date: moment().subtract(1, 'days').format('YYYY-MM-DD'),
        isParNode: '1',
        treePsId: '',
        elecEnsureType: ['0'],
        totalCapcityBegin: '',
        totalCapcityEnd: ''
      },
      volumnMin: '',
      volumnMax: '',
      unit: 'kW',
      unitOptions: [ // 装机容量单位
        {
          value: 'kW',
          text: 'kW'
        }, {
          value: 'MW',
          text: 'MW'
        }, {
          value: 'GW',
          text: 'GW'
        }
      ],
      dataSource: [],
      haveReadPsIds: [],
      height: '',
      scrollTop: 0,
      firstLoad: false,
      sortOptions: [ // 排序配置项目
        {
          state: false,
          isReverse: false,
          label: '完整率',
          sortDec: ['intateRate desc', 'intateRate asc']
        }, {
          state: false,
          isReverse: false,
          label: '有效率',
          sortDec: ['effectiveRate desc', 'effectiveRate asc']
        }
      ],
      noDataImg: require('@/assets/images/public/no-data.png'), // 暂无数据图片
      infiniteId: +new Date(), // 滚动加载标识
      url: {
        list: '/pointManagement/list',
        export: '/pointManagement/export',
        detail: '/pointManagement/detail'
      },
      darkSealBg: require('../../../../assets/images/public/seal.png'),
      lightSealBg: require('../../../../assets/images/public/light_seal.png'),
      selectLength: 0,
      total: 0,
      listLoading: false,
      firstLoading: true
    };
  },
  computed: {
    isShowMenu () {
      return this.$store.state.user.isShowMenu;
    }
  },
  created () {
    this.firstLoad = true;
    setTimeout(() => {
      this.firstLoad = false;
    }, 500);
    this.getDict();
    this.loadData();
  },
  mounted () {
    this.getTableHeight();
  },
  activated () {
    let dom = document.getElementsByClassName('card-area');
    dom[0].scrollTop = this.scrollTop;
  },
  methods: {
    // 设置表格高度
    getTableHeight () {
      let $searchHeight = window.document.getElementsByClassName('search-card');
      let height = 0;
      if ($searchHeight[0]) {
        height = $searchHeight[0].clientHeight + 16;
      }
      this.height = window.innerHeight - height - 60 - 40 - 24 + (this.isShowMenu ? 0 : 124);
    },
    getScroll (e) {
      this.scrollTop = e.target.scrollTop;
    },
    async getDict () {
      this.getDictMap('elec_ensure_type');
    },
    getChild (val, node) {
      this.selectLength = node.psNum;
      this.queryParams.treePsId = node.psIds;
      this.queryParams.depCodes = node.depCodes;
      this.queryParams.psaIds = node.psaIds;
      this.queryParams.currentPage = 1;
      this.firstLoading = false;
      if (!node.isFirstReq) {
        this.refreshScrollLoading();
      }
    },
    changeType (val) {
      // this.refreshScrollLoading()
    },
    timeChange (val) {
      this.queryParams.currentPage = 1;
      // this.refreshScrollLoading()
    },
    disabledDate (current) {
      // 不能选择今天以后的
      return current && current > moment().subtract(1, 'days').endOf('day');
    },
    // 装机容量范围校验
    rangChange (type) {
      if (this.volumnMin !== '' &&
          this.volumnMax !== '' &&
          this.volumnMax < this.volumnMin) {
        if (type === 'max') {
          this.volumnMax = this.volumnMin;
        }
        if (type === 'min') {
          this.volumnMin = this.volumnMax;
        }
      }
    },
    unitChange (val) {
      this.unit = val;
    },
    // 容量单位换算
    exchangeKw (val) {
      if (val === null) {
        return null;
      }
      if (this.unit === 'kW') {
        return val;
      }

      if (this.unit === 'MW') {
        return val * 1000;
      }
      if (this.unit === 'GW') {
        return val * 1000000;
      }
    },
    // 重置滚动刷新
    refreshScrollLoading () {
      this.haveReadPsIds = [];
      this.sortOptions = [ // 排序配置项目
        {
          state: false,
          isReverse: false,
          label: '完整率',
          sortDec: ['intateRate desc', 'intateRate asc']
        }, {
          state: false,
          isReverse: false,
          label: '有效率',
          sortDec: ['effectiveRate desc', 'effectiveRate asc']
        }
      ];
      this.queryParams.order = '';
      this.dataSource = [];
      this.queryParams.currentPage = 1;
      this.infiniteId += 1;
    },

    // 涉及到滚动加载 覆盖mixin方法
    async loadData (loadMore) {
      if (this.firstLoading) {
        return;
      }
      this.queryParams.totalCapcityBegin = this.exchangeKw(this.volumnMin) == '' ? undefined : this.exchangeKw(this.volumnMin);
      this.queryParams.totalCapcityEnd = this.exchangeKw(this.volumnMax) == '' ? undefined : this.exchangeKw(this.volumnMax);
      let err = false;
      this.listLoading = true;
      // 电量保住类型改为多选
      let params = Object.assign({}, this.queryParams);
      params.elecEnsureType = params.elecEnsureType.join();
      let res = await stationIntateAndEffectiveList(params).catch(e => {
        err = true;
        return false;
      });
      if (err) return;

      if (this.queryParams.currentPage == 1) {
        this.dataSource = res.result_data.pageList;
      } else {
        this.dataSource = this.dataSource.concat(res.result_data.pageList);
      }
      this.total = res.result_data.rowCount;
      this.listLoading = false;
      if (res.result_code !== '1') {
        this.$message.error(res.result_msg);
        return;
      }
      if (loadMore) {
        const length = res.result_data.pageList.length;
        if (length < 16) {
          loadMore.complete();
          if (length != 0) {
            loadMore.loaded();
          }
        } else {
          loadMore.loaded();
        }
      }
      this.queryParams.currentPage++;
    },
    // 排序
    handleSort (index) {
      if (this.sortOptions[index].state) {
        this.sortOptions[index].isReverse = !this.sortOptions[index].isReverse;
      } else {
        this.sortOptions = this.sortOptions.map(item => {
          item.state = false;
          return item;
        });
        this.sortOptions[index].state = true;
      }

      this.queryParams.order = this.sortOptions[index].sortDec[Number(this.sortOptions[index].isReverse)];
      this.queryParams.currentPage = 1;
      this.dataSource = [];
      this.infiniteId += 1;
    },

    goDetail (psId, psName) {
      if (!this.haveReadPsIds.includes(psId)) {
        this.haveReadPsIds.push(psId);
      }
      this.$emit('changePageParams', {
        psId: psId,
        deviceName: '',
        defaultDate: this.queryParams.date,
        psName: psName
      });
      this.$emit('changePage', 2);
    }
  }

};
</script>

<style lang="less" scoped>
.solar-eye-seal-name {
  top: 24px;
  left: 24px;
}

.flex-start {
  display: flex;
  justify-content: flex-start;
  align-items: center;

}

.double-input {
  width: 240px;
  border-radius: 4px;
  border: 1px solid var(--zw-border-color--default);
  height: 32px;
  display: flex;
  justify-content: space-around;

  input {
    text-align: center;
  }

  .rang-input-left {
    width: 80px;
    box-sizing: border-box;
    border: none;
    box-shadow: none;
    background-color: transparent;

    :deep(.ant-input-number-handler-wrap) {
      display: none;
    }
  }

  .rang-input-right {
    width: 80px;
    box-sizing: border-box;
    border: none;
    box-shadow: none;
    background-color: transparent;

    :deep(.ant-input-number-handler-wrap) {
      display: none;
    }
  }
}

.double-input:hover {
  border: 1px solid var(--zw-primary-color--default);
}

.unit-select {
  width: 90px;
  margin-left: 8px;
}

.card-area {
  overflow-y: scroll;
}

.power-station-card {
  cursor: pointer;
  height: 258px;
  position: relative;
  border-radius: 4px;
  box-sizing: border-box;

  &-top {
    height: 54px;
    border-radius: 4px 4px 0px 0px;
    box-sizing: border-box;
    font-size: 14px;
    font-weight: 500;
    line-height: 54px;
    padding-left: 24px;
    display: flex;
    justify-content: space-between;

    .ps-name {
      width: 75%;
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
    }

    .power-station-mark {
      position: absolute;
      left: 15px;
      top: 7px;
      transform: rotate(30deg);
      font-size: 14px;
    }

  }

  &-bottom {
    display: flex;
    justify-content: space-evenly;
    align-items: center;
    padding: 16px 0;

    .devide-line {
      height: 64px;
      width: 1px;
      box-sizing: border-box;

    }

    .rate-item {
      height: 204px;
      display: flex;
      justify-content: space-around;
      align-items: center;

      &-left {
        text-align: center;
        justify-content: space-around;
        display: flex;
        flex-direction: column;
        margin-right: 8px;

        .label {
          font-size: 14px;
          font-weight: 400;

          margin: 0;
        }

        .num {
          font-size: 24px;
          margin: 0;

        }
      }

      &-right {
        width: 64px;
      }
    }
  }
}

.no-data-img {
  margin-top: 72px;
}

.no-data-text {
  margin-top: 25px;
  font-size: 20px;
  font-weight: 400;
}

.no-more-data {
  margin-top: 32px;
  padding-bottom: 60px;
  font-size: 16px;
}

.sort-header {
  margin: 0 0 10px;
}
</style>
