<!--  @creator l<PERSON><PERSON>
      @date 2021/02/24
      @description   健康度分析模块:组串离散率分析；-->
<template>
  <div class="isolar-layout">
    <a-row :gutter="16" ref="DispersionRateAnalysis">
      <a-col :md="4" :sm="24">
        <CheckDeviceTree
          @checkedPsKey="refreshData"
          checkDevice
          :parentDeviceType="defaultDeviceType"
          :resetChart="drawChart2"
          :isRealTime="true"
        ></CheckDeviceTree>
      </a-col>
      <a-col :md="20" :sm="24" style="height: 100%">
          <a-card
            :style="{height:chartShow ?cardHeight +'px' :''}"
            :bodyStyle="{ height: '100%' }"
            class="solareye-card"
            :title="title"
          >
            <a slot="extra" href="#">
              <PowerStationWeather v-if="psId" :psId="psId"
            /></a>
            <div class="right-info-row between" style="padding-top: 16px">
              <a-space>
                <label>诊断时间：</label>
                <a-date-picker
                  v-model="recordDate"
                  type="date"
                  format="YYYY/MM/DD"
                  value-format="YYYY/MM/DD"
                  placeholder="选择日期"
                  :getCalendarContainer="
                    (trigger) => trigger.parentNode || document.body
                  "
                  @change="clickSearch()"
                >
                  <template slot="dateRender" slot-scope="current">
                    <div
                      :class="`ant-calendar-date ${getCurrentClassName(
                        current
                      )}`"
                    >
                      {{ current.date() }}
                    </div>
                  </template>
                </a-date-picker>
              </a-space>
              <div @click="changeChartShowStatus" style="color: #878e9b;float:right">
                {{ chartShow ? "收起" : "展开"
                }}<a-icon
                  :type="chartShow ? 'up' : 'down'"
                  style="padding-left: 10px"
                />
              </div>
            </div>
            <div class="container-middle" v-show="chartShow">
              <a-spin :spinning="loadingChart">
                <a-row>
                  <a-col
                    v-for="key in 2"
                    :xs="24"
                    :sm="24"
                    :md="12"
                    :key="key"
                    class="echart-graf"
                    :style="{height:(cardHeight - 110) +'px'}"
                    :id="`echart-${key}`"
                    ref="`echart-${key}`"
                  ></a-col>
                </a-row>
              </a-spin>
            </div>
          </a-card>
          <a-card :style="{height:chartShow ? cardHeight +'px' : 1.62 *cardHeight +'px'}">
            <div  >
              <div class="table-operator">
                 <a-button
                class="solar-eye-btn-primary"
                :disabled="loadingTable"
                :loading="loadingExport"
                @click.stop.prevent="clickExport()"
                >导出</a-button
              >
              </div>

              <a-table
                :data-source="tableData"
                :pagination="pagination"
                @change="handleTableChange"
                :loading="loadingChart"
                :scroll="{ x: 1500,y: chartShow ?0.46 * cardHeight +'px' : 1.05 *cardHeight +'px'}"
                size="middle"
                ref="tableRate"
              >
                <a-table-column
                  width="150px"
                  key="deviceName"
                  align="center"
                  data-index="deviceName
                 "
                >
                  <span slot="title" style="color: #1890ff">
                    <a-select
                      style="width: 120px"
                      v-model="defaultDeviceType"
                      @change="changeDeviceType"
                    >
                      <a-select-option
                        v-for="(item, index) in deviceTypeOptions"
                        :key="index"
                        :value="item.deviceType"
                      >
                        {{ item.deviceName }}
                      </a-select-option>
                    </a-select>
                  </span>
                  <template slot-scope="text, record">{{
                    record.deviceName
                  }}</template>
                </a-table-column>

                <a-table-column
                  width="140px"
                  ellipsis="true"
                  :key="Math.random()"
                  data-index="recordDate"
                  title="时间"
                  align="center"
                  :sorter="true"
                >
                  <template slot-scope="text">
                    {{ dateFormat(text) }}
                  </template>
                </a-table-column>
                <a-table-column
                  width="150px"
                  key="dispersionRate"
                  data-index="dispersionRate"
                  title="时均离散率(%)"
                  align="center"
                  :sorter="true"
                >
                  <template slot-scope="text">
                    {{ dispersionRateTransfer(text) }}
                  </template>
                </a-table-column>
                <a-table-column
                  width="150px"
                  key="convertGeneratingCapacity"
                  data-index="convertGeneratingCapacity"
                  title="归一化功率"
                  align="center"
                  :sorter="true"
                >
                  <template slot-scope="text">
                    {{ convertGeneratingCapacityTransfer(text) || "--" }}
                  </template>
                </a-table-column>
                <a-table-column
                  width="160px"
                  key="averageInternalTemp"
                  data-index="averageInternalTemp"
                  title="时均机内温度(℃)"
                  align="center"
                  :sorter="true"
                >
                  <template slot-scope="text">
                    {{ textFixedOne(text) }}
                  </template>
                </a-table-column>
                <a-table-column
                  width="190px"
                  key="approximateOpenVoltage"
                  data-index="approximateOpenVoltage"
                  title="并网工作最大电压(V)"
                  align="center"
                  :sorter="true"
                >
                  <template slot-scope="text">
                    {{ textFixedOne(text) }}
                  </template>
                </a-table-column>

                <a-table-column-group title="组串平均电流(A)">
                  <a-table-column
                    v-for="cur in currentAvgChildren"
                    :key="cur.key"
                    :title="cur.title"
                    :data-index="cur.dataIndex"
                    :width="cur.width"
                    align="center"
                  />
                </a-table-column-group>
              </a-table>
            </div>
          </a-card>
      </a-col>
    </a-row>
  </div>
</template>
<script>
import moment from 'moment';
import { downloadFile } from '@/utils/util.js';
import {
  getDispersionRateLevel,
  getPowerStationType,
  indexAnalysisDynamic,
  downloadExcelDispersionRate
} from '@/api/health/healthapi.js';
import treeHeight from '@/mixins/health/leftTreeHeight';
import TypicalDate from '@/mixins/health/typicalDate';
import CheckDeviceTree from '@/components/health/funnel/checkDeviceTree';
import PowerStationWeather from '@/components/health/weather/PowerStationWeather.vue';
import echarts from '@/utils/enquireEchart';
let innerHeight = window.innerHeight - 60 - 40 - 24;
export default {
  components: {
    CheckDeviceTree,
    PowerStationWeather
  },
  name: 'RealtimeAnalysis',
  mixins: [treeHeight, TypicalDate],
  //   watch:{
  //     deviceType(val) {
  // this.changeDeviceType(val)
  //     }
  //   },
  data () {
    return {
      title: '',
      dateChange: 0,
      currentDevType: 0, // 当前电站默认选中的设备类型
      deviceTypeOptions: [],
      filteredDeviceTypes: [],
      currentAvgChildren: [],
      // deviceType: 1,
      currentKey: '',
      defaultDeviceType: '',
      defaultDeviceName: '',
      deviceType: 1,
      recordDate: '',
      psId: '',
      psName: '',
      rowCount1: 0,
      rowCount: 0,
      filterText: '',
      dataValue: [],
      tableColumnList: [],
      tableData: [],
      pagination: {
        current: 1,
        pageSize: 10,
        pageSizeOptions: ['10', '20', '30', '50'],
        defaultPageSize: 10,
        showSizeChanger: true,
        defaultCurrent: 1,
        showQuickJumper: true,
        showTotal: (total) => `共 ${total} 条`
      },
      isorter: {
        sortFiled: '',
        sortKind: ''
      },
      labelNames: [],
      accordion: true,
      data: [],
      pageList: {},
      loadingChart: true,
      loadingTable: true,
      loadingExport: false,
      rateLevel: [],
      flags: '0',
      chartShow: true,
      chartList: []
    };
  },

  created () {
    this.flags = '0'; // 标识是否刷新图表，0---全部刷新，1---只刷新表格
    // 从电站首页跳转过来，获取电站id
    if (this.$route.query.id) {
      this.psId = this.$route.query.id;
      this.currentKey = this.psId;
    }
    this.recordDate = moment().format('YYYY/MM/DD');
  },
  computed: {
    cardHeight () {
      return innerHeight > 700 ? (innerHeight - 16) / 2 : (800 - 16) / 2;
    }
  },
  methods: {
    dateFormat (text) {
      if (text === undefined) {
        return '';
      }
      return moment(text).format('YYYY-MM-DD HH:mm');
    },
    refreshData ({ psId, psName, psKeys, isSearch }) {
      this.psId = psId;
      this.psKeys = psKeys;
      this.psName = psName;
      this.title = psName;
      this.flags = '0';
      this.pagination.current = 1;
      if (this.currentDevType == 1 && isSearch != 1) {
        this.getData(true);
      } else {
        if ((this.defaultDeviceType == '')) { this.getData(); }
      }
      this.getTopicalDay(this.psId);
      // this.getDispersionRateLevel();
    },
    changeChartShowStatus () {
      this.chartShow = !this.chartShow;
    },
    // 选择离散率等级查询表格数据
    getLevelData () {
      this.flags = '1';
      this.pagination.current = 1;
      this.getData();
    },
    // 获取离散率等级
    getDispersionRateLevel () {
      getDispersionRateLevel()
        .then((res) => {
          if (res.result_code === '1') {
            this.rateLevel = res.result_data;
          }
        })
        .catch(function () {});
    },
    /**
     * @creator wangliang
     * @date 2020/09/24
     * @description   搜索框click事件，筛选电站
     */
    clickSearch () {
      // 时间搜索
      this.dataValue = [];
      this.flags = '0';
      this.dateChange = 1;
      this.getData();
    },
    // 遍历数组
    mapArr (arr, isx) {
      let xData = [];
      let yData = [];
      arr &&
        arr.map((item) => {
          xData.push(Object.keys(item)[0]);
          yData.push(Object.values(item)[0]);
        });
      return isx ? xData : yData;
    },
    // 获取数据
    getData (isSelectChange) {
      this.tableData = [];
      this.dataValue = [];
      this.currentAvgChildren = [];
      this.filteredDeviceTypes = [];
      this.loadingTable = true;
      let param = {
        psId: this.psId
      };
      if (this.flags == '0') {
        this.loadingChart = true;
      }
      const beforePromise = () => {
        if (!isSelectChange) {
          this.deviceTypeOptions = [];
          return new Promise((resolve, reject) => {
            getPowerStationType(param)
              .then((res) => {
                // 下拉项
                this.deviceTypeOptions = res.result_data.deviceGroup;
                for (let obj in this.deviceTypeOptions) {
                  if (res.result_data.deviceGroup[obj].isDefault === '1') {
                    // 如果当前电站的默认选中设备类型标识为0时，进行赋值重置
                    if (this.dateChange != 1) {
                      this.defaultDeviceName =
                        res.result_data.deviceGroup[obj].deviceName;
                      this.defaultDeviceType =
                        res.result_data.deviceGroup[obj].deviceType;
                      // this.deviceType= res.result_data.deviceGroup[obj].deviceType
                      this.currentDevType = 1;
                    }

                    this.dateChange = 0;
                  }
                }
                resolve && resolve();
              })
              .catch(function (err) {
                reject && reject(err);
                console.log(err);
              });
          });
        } else {
          return new Promise((resolve, reject) => {
            resolve();
          });
        }
      };
      let promise = beforePromise();
      promise.then(() => {
        let paramObj = {
          recordDate: this.recordDate,
          curPage: this.pagination.current,
          size: this.pagination.pageSize,
          psId: this.psId,
          psKeys: this.psKeys,
          deviceType: this.defaultDeviceType
        };
        // TODO 接口需要改
        indexAnalysisDynamic(Object.assign({}, paramObj, this.isorter))
          .then((res) => {
            this.loadingChart = false;
            this.loadingTable = false;
            if (res.result_code === '1') {
              let data = res.result_data;

              let currentAvgList = (this.currentAvgChildren =
                (res.result_data.pageBean.pageList[0] &&
                  res.result_data.pageBean.pageList[0]['currentAvgList']) ||
                []);
              if (currentAvgList.length) {
                this.currentAvgChildren = currentAvgList.map((c, index) => ({
                  width: 70,
                  title: c.pointName,
                  key: index,
                  dataIndex: `currentAvgList[${index}].currentAvg`
                }));
              } else {
                this.currentAvgChildren = [];
              }
              const pager = { ...this.pagination };
              pager.total = res.result_data.pageBean.rowCount;
              pager.pageSize = res.result_data.pageBean.size;
              pager.current = res.result_data.pageBean.curPage;
              this.pagination = pager;
              this.tableData = res.result_data.pageBean.pageList;
              if (res.result_data.pictureData) {
                this.chartList = res.result_data.pictureData;
              } else {
                this.chartList = [];
              }
              if (this.flags == '0') {
                this.drawChart(data);
              }
            }
          })
          .catch(function (err) {
            console.log(err);
            this.loadingChart = false;
            this.loadingTable = false;
          });
      });
    },
    // 切换电站
    drawChart2 (dataList) {
      // 切换电站
      if (!dataList) {
        // 清空pskey
        this.psKeys = [];
        // 清空图表数据
        this.chartList = [];
        this.currentDevType = 0;
        this.defaultDeviceType = '';
      }
      this.drawLine('时均离散率(%)', 1);
      this.drawLine('归一化功率(kWh)', 2);
    },
    drawChart (dataList) {
      if (!dataList) {
        // 清空pskey
        this.psKeys = [];
        // 清空图表数据
        this.chartList = [];
      }
      this.drawLine('时均离散率(%)', 1);
      this.drawLine('归一化功率(kWh)', 2);
    },
    handleTableChange (pagination, filters, sorter) {
      const pager = { ...this.pagination };
      if (Object.keys(sorter).length > 0) {
        this.isorter.sortFiled = this.filedToLowerCase(sorter.field);
        this.isorter.sortKind = sorter.order == 'ascend' ? 'asc' : 'desc';
      } else {
        this.isorter.sortFiled = '';
        this.isorter.sortKind = '';
      }
      pager.current = pagination.current;
      pager.pageSize = pagination.pageSize;
      this.pagination = pager;
      this.flags = '1';
      this.getData();
    },
    filedToLowerCase (str) {
      var arr = str.split('');
      var new_arr = arr.map((item) => {
        return item === item.toUpperCase() ? '_' + item.toLowerCase() : item;
      });
      return new_arr.join('');
    },
    dispersionRateTransfer (text) {
      if (text == null || text == '') {
        return '';
      }
      return (Number(text) * 100).toFixed(2);
    },
    convertGeneratingCapacityTransfer (text) {
      if (text == null || text == '') {
        return '';
      }
      return Number(text).toFixed(2);
    },
    textFixedOne (text) {
      return Number(text).toFixed(1);
    },
    changeDeviceType (val) {
      // 主页面内切换设备类型
      // 当切换设备类型时，就去清空echarts 图表的数据，并重绘图表
      this.drawChart(null);
      // this.deviceType = val
      this.pagination.current = 1;
      this.pagination.pageSize = 10;
      this.getData(true);
    },
    /**
     * @creator wangliang
     * @date 2020/09/08
     * @description 加载图标
     */
    drawLine (xName, chartNo) {
      let that = this;
      // 基于准备好的dom，初始化echarts实例
      if (!document.getElementById('echart-' + chartNo)) {
        return;
      }
      // 不重复初始化
      let myChart = echarts.getInstanceByDom(document.getElementById('echart-' + chartNo));
      if (!myChart) {
        myChart = echarts.init(document.getElementById('echart-' + chartNo));
        this.$once('hook:beforeDestroy', function () {
          echarts.dispose(myChart);
        });
      }
      // let myChart = echarts.init(document.getElementById("echart-" + chartNo));
      myChart.clear();
      var option = {
        tooltip: {
          trigger: 'axis'
        },
        // toolbox: {
        //     feature: {
        //         dataView: {show: true, readOnly: false},
        //         magicType: {show: true, type: ['line', 'bar']},
        //         restore: {show: true},
        //         saveAsImage: {show: true}
        //     }
        // },
        xAxis: {
          type: 'category',
          // name: xName,
          nameLocation: 'center',
          data: that.chartList.length > 0 && that.chartList[0].timeList,
          nameTextStyle: {
            verticalAlign: 'top',
            padding: [10, 0, 0, 0]
          }
        },
        dataZoom: [
          {
            type: 'inside'
          }
        ],
        yAxis: {
          type: 'value',
          name: chartNo == 1 ? '离散率：(%)' : '归一化功率'
        },
        series: (function () {
          let series = [];
          for (let index = 0; index < that.chartList.length; index++) {
            let obj = {
              name: that.chartList[index].deviceName,
              type: 'line',
              data:
                chartNo == 1
                  ? that.chartList[index].dispersionList
                  : that.chartList[index].convertList
            };
            series.push(obj);
          }
          return series;
        })()
        // series: [
        //   {
        //     data: yList,
        //     type: 'line',
        //     smooth: true,
        //     showBackground: false,
        //     itemStyle: {
        //       normal: {
        //         color: function (params) {
        //           let colorList = ["#0066ff", "#FFBB00", "#CC0000", "#008800"];
        //           return colorList[chartNo - 1];
        //         },
        //       },
        //     },
        //   }
        // ],
      };
      myChart.setOption(option, true);
      window.addEventListener('resize', () => {
        myChart && myChart.resize();
      });
    },

    /**
     * @creator wangliang
     * @date 2020/09/15
     * @description 导出
     */
    clickExport () {
      if (this.recordDate == null || this.recordDate == '') {
        this.$message.error('诊断时间不能为空');
        return;
      }
      if (this.tableData.length == 0) {
        this.$message.error('暂无数据，无法导出');
        return;
      }
      this.loadingExport = true;
      let paramsObj = {
        curPage: '',
        size: '',
        psId: this.psId,
        deviceType: this.defaultDeviceType,
        recordDate: moment(this.recordDate).format('YYYY/MM/DD')
      };
      // 请求导出接口
      downloadExcelDispersionRate(paramsObj)
        .then((res) => {
          if (res.result_code == '1') {
            downloadFile(res.result_data);
          }
          this.loadingExport = false;
        })
        .catch((error) => {
          this.loadingExport = false;
          console.log(error);
        });
    }
  }
};
</script>
<style lang="less" scoped>

</style>
