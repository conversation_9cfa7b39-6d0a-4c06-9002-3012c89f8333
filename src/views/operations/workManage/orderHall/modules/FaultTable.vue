<template>
  <vxe-table
      :data="data"
      ref="table"
      show-overflow
      highlight-hover-row
      size="small"
      resizable
      class="fault-table"
      aligin="left"
    >
      <vxe-table-column type="seq" :width="80" title="序号"></vxe-table-column>
      <vxe-table-column
        show-overflow="title"
        v-for="ele in columns"
        :key="ele.field"
        :field="ele.field"
        :title="ele.title"
      >
        <template v-slot="{ row }">
          <div class="over-flow">
            {{row[ele.field] || '--'}}
          </div>
        </template>
      </vxe-table-column>
    </vxe-table>
</template>
<script>
export default {
  props: {
    data: {
      type: Array,
      required: true
    },
    columns: {
      type: Array,
      required: true
    }
  }
};
</script>
<style lang="less" scoped>
.fault-table {
  border: 1px solid #DCDCDC;
  :deep(.vxe-body--column) {
    border-top: 1px solid #e4e4e4 !important;
  }
}
:deep(.vxe-body--row) {
  background: transparent !important;
}

:deep(.vxe-header--column .vxe-resizable.is--line:before) {
  background-color: transparent;
}
:deep(.vxe-header--column .vxe-resizable.is--line:before:hover) {
  background-color: #d9dddf;
}
:root[data-theme='dark'] {
  .table {
    border: 1px solid #354661;
    :deep(.vxe-body--row:hover) {
      background: transparent !important;
    }
    :deep(.vxe-body--row:nth-child(even)) {
      background: transparent !important;
    }
    :deep(.vxe-body--column) {
      border-top: 1px solid #354661 !important;
    }
  }

}
</style>
