import { getSopDetailForBus } from '@/api/config/digitalRegulation';
import uploadFileView from '@/components/com/fileUploadView';
import moment from 'moment';
import VueViewer from '@/mixins/VueViewer';
export const orderDispose = {
  props: {},
  components: {
    uploadFileView
  },
  mixins: [VueViewer],
  data () {
    let validateFileList = (rule, value, callback) => {
      let arr = [];
      if (value && value.length > 0) {
        value.forEach((item, index) => {
          if (arr.indexOf(item.fileName) == -1) {
            arr.push(item.fileName);
          }
        });
        if (arr.length < value.length) {
          callback(new Error('不允许上传相同得文件'));
        }
      }
      callback();
    };
    return {
      isRequired: true,
      validateFileList: validateFileList
    };
  },
  inject: ['order', 'type'],
  created () {},
  computed: {
    disabled () {
      return this.getStatus() || this.type() == '3';
    }
  },
  watch: {
    order: {
      handler (val) {
        if (this.order.orderStatus == '6' && this.order.orderSource == 4) {
          this.dispose.actualTimeResolve = moment().format('YYYY-MM-DD HH:mm');
          this.getRepairTime();
          if (this.$refs.orderDispose) {
            this.clearValidate();
          }
          this.dispose.resolveResult = '2';
          this.dispose.sceneCondition = '1';
        } else {
          this.dispose = Object.assign({}, this.dispose, this.order);
        }
      }
    },
    deep: true,
    immediate: true
  },
  methods: {
    getFileObj (res) {
      let arr = [];
      res.forEach(element => {
        arr.push({
          file: element.file,
          fileName: element.fileName,
          fileId: element.uid
        });
      });
      return arr;
    },
    getStatus () {
      let order = this.order();
      return (order.step > 5 && order.processDefKey == 'activiti4273Workflow') || (order.step > 5 && order.processDefKey == 'activiti4773Workflow') || (order.step > 2 && order.processDefKey ==
        'activiti4873Workflow') || (order.processDefKey == 'activiti5073Workflow' && order.step > 1) || order.flowSts == '4' || order.otherSts == '4';
    },
    setValidate (flag) { // 2 保存，1 提交
      this.isRequired = flag != 2;
      let that = this;
      return new Promise((resolve, reject) => {
        if (!that.isRequired) {
          resolve(this.dispose);
        } else {
          if (this.dispose.hasOwnProperty('handleResult') && this.dispose.handleResult == '2') {
            this.$message.warning('选择待协调后不能提交，请点击保存');
            return;
          }
          let order = this.order();
          // (order.type == 7 || order.type == 8) &&
          if ((this.type() == 7 || this.type() == 8) && this.rules.hasOwnProperty('overdueReason')) {
            let isEngLegacy = order.isEngLegacy == 1 && order.rectifyDeadline && (new Date().getTime() > new Date(order.rectifyDeadline).getTime());
            let isNotEngLegacy = order.predictRecoverTime && (new Date().getTime() > new Date(order.predictRecoverTime).getTime());
            if (isEngLegacy || isNotEngLegacy) {
              this.rules.overdueReason[0].required = true;
            } else {
              this.rules.overdueReason[0].required = false;
            }
          }
          if (that.order().taskType == '10') {
            that.dispose.uploadFileList = [...that.oldPatrolInspecList, ...that.$refs.ossFileUpload.uploadedFileList];
          }
          this.$refs.orderDispose.validate(valid => {
            if (valid) {
              if (!that.disabled) {
                that.dispose.completeTime = moment().format('YYYY-MM-DD HH:mm');
                that.getRepairTime && that.getRepairTime();
              }
              if (that.order().taskType == '10') {
                let obj = {
                  patrolInspecList: that.getPatrolInspecList(that.dispose.uploadFileList),
                  folderNames: [...that.$refs.ossFileUpload.folderNames, ...that.folderNames]
                };
                resolve(obj);
              } else {
                resolve(this.dispose);
              }
            } else {
              reject(new Error());
            }
          });
        }
      }).catch(err => {
        reject(err);
      });
    },
    clearValidate () {
      this.$refs.orderDispose.clearValidate();
      this.$refs.orderDispose.resetFields();
    },
    fieldChange (val) {
      this.$refs.orderDispose.clearValidate(val);
    },
    getStepList () {
      let deviceType = this.order().deviceTypeId ? this.order().deviceTypeId.split(',') : '';
      let val = deviceType ? deviceType[deviceType.length - 1] : 0;
      getSopDetailForBus({
        taskType: this.order().taskType,
        deviceType: val,
        riskLevel: this.order().riskLevel
      }).then((res) => {
        this.dispose.stepVoList = [];
        if (res.result_data && res.result_data.steps) {
          this.dispose.stepVoList = res.result_data.steps.map((item) => {
            return {
              stepName: item.stepName,
              stepNo: item.stepNo,
              workDesc: '',
              uploadPictureList: [],
              uploadFileList: [],
              stepContent: item.stepContent,
              uploadExampleFileList: item.uploadFileList
            };
          });
        }
      });
    },
    // 示例图片预览
    handlePreview (fileList) {
      this.stopEvent();
      this.viewerImage({ 'images': fileList });
    },
    // 阻止冒泡方法
    stopEvent (e) {
      e = e || window.event;
      if (e.stopPropagation) { // W3C阻止冒泡方法
        e.stopPropagation();
      } else {
        e.cancelBubble = true; // IE阻止冒泡方法
      }
    }
  }
};
