<template>
  <div class="instrument">
    <a-spin :spinning="loading">
      <div v-if='realTotal' class="solar-eye-search-model">
        <div class="solar-eye-search-content">
          <instrument-search ref="search" @queryData="queryData" />
        </div>
      </div>
      <div v-if='realTotal' class="solar-eye-gap"></div>
      <div v-if='realTotal' class="solar-eye-main-content">
        <div class="add-box"><throttle-button perms='preTestInstrument:add' @click="initEditModal('1')" label="新增" /></div>
          <vxe-table
            ref="instrument"
            :data="dataSource"
            :seq-config="{ startIndex: (pageData.curPage - 1) * pageData.size }"
            :height="tableHeight - 90"
            size="small"
            :resizable='false'
            show-overflow
            highlight-hover-row
            headerAlign='left'
            class="my-table"
          >
            <vxe-table-column type="seq" :width="80" title="序号"></vxe-table-column>
            <vxe-table-column
              v-for="item in columnList"
              :key="item.name"
              :min-width="item.width || 150"
              :field="item.name"
              :title="item.title"
              show-overflow="title"
            >
              <template #default="{ row }">
                <span v-if="item.name === 'isEnable'" :title='getLabel( row.isEnable,statusEnum)'>
                  <a-switch v-model="row.isEnable" @change="(val) => handleChange(val, row)" />
                  {{row.isEnable ? '启用':'禁用' }}
                </span>
                <span :title='getLabel(row[item.name])' v-else>{{ getLabel(row[item.name]) }}</span>
              </template>
            </vxe-table-column>
            <vxe-table-column title="操作" :visible='showAction' fixed="right" width="160" :resizable="false">
              <template v-slot="{ row }">
                <throttle-button
                  title="编辑"
                  icon="edit"
                  class="operation-btn-hover"
                  @click="initEditModal('2', row)"
                  perms='preTestInstrument:edit'
                />
                <throttle-button
                  title="删除"
                  icon="delete"
                  class="operation-btn-hover"
                  @click="removeInstrument(row)"
                  perms='preTestInstrument:delete'
                />
              </template>
            </vxe-table-column>
          </vxe-table>
          <page-pagination
            :pageSize="pageData.size"
            :current="pageData.curPage"
            :total="total"
            @size-change="pageChangeEvent"
          />
      </div>
      <div v-else class="empty-content">
        <img src="@/assets/images/preExperiment/empty.png" alt="" />
        <a-button class="ant-btn-primary" perms='preTestInstrument:add' @click="initEditModal('1')">立即新增</a-button>
      </div>
    </a-spin>

    <edit-modal ref="editModal" @queryData="queryData" />
  </div>
</template>

<script>
import InstrumentSearch from '@views/preExperiment/instrument/modules/Search.vue';
import initDict from '@/mixins/initDict';
import { LeftMixin } from '@/mixins/LeftMixin';
import tableHeight from '@/mixins/tableHeightAndSearchModel';
import EditModal from './modules/EditModal';
import {
  deleteInstrumentConfigApi,
  instrumentConfigListApi,
  updateInstrumentConfigApi
} from '@api/isolarErp/preExperiment';
export default {
  name: 'Instrument',
  components: { InstrumentSearch, EditModal },
  mixins: [initDict, LeftMixin, tableHeight],
  provide () {
    return {
      statusEnum: this.statusEnum
    };
  },
  data () {
    return {
      columnList: [
        { title: '仪器型号', name: 'instrumentModel' },
        { title: '仪器名称', name: 'instrumentName' },
        { title: '仪器状态', name: 'isEnable' }
      ],
      total: 0,
      dataSource: [],
      realTotal: 0,
      pageData: {
        curPage: 1,
        size: 10
      },
      loading: false,
      statusEnum: [
        {
          value: 1,
          label: '启用'
        },
        {
          value: 0,
          label: '禁用'
        }
      ]
    };
  },
  mounted () {
    this.queryData();
  },
  methods: {
    pageChangeEvent (curPage, size) {
      Object.assign(this.pageData, { curPage, size });
      this.queryData();
    },
    queryData () {
      this.loading = true;
      const _self = this;
      const searchRef = this.$refs.search;
      const queryParams = Object.assign({}, (searchRef && searchRef.queryParams) || {}, this.pageData);
      instrumentConfigListApi(queryParams)
        .then((res) => {
          if (res.result_code === '1') {
            _self.loading = false;
            _self.dataSource = res.result_data.rows;
            _self.realTotal = res.result_data.realTotal;
            _self.total = res.result_data.total;
            console.log(_self.realTotal, typeof _self.realTotal);
          }
        })
        .catch(() => {
          this.loading = false;
          this.realTotal = 0;
        });
    },
    removeInstrument ({ id }) {
      this.$confirm({
        title: '确定要删除该仪器吗？',
        okText: '确定',
        cancelText: '取消',
        onOk: () => {
          deleteInstrumentConfigApi({ id }).then((res) => {
            if (res.result_code === '1') {
              this.$message.success('删除成功');
              this.queryData();
            }
          });
        },
        onCancel: () => {}
      });
    },
    initEditModal (type, row) {
      this.$refs.editModal.init(type, row);
    },
    handleChange (val, row) {
      const map = { ...row, isEnable: val ? 1 : 0 };
      updateInstrumentConfigApi(map)
        .then((res) => {
          if (res.result_code === '1') {
            this.$message.destroy();
            this.$message.success({
              content: '更新成功',
              key: 'updateInstrumentConfig'
            });
            this.queryData();
          }
        })
        .catch(() => {
          this.queryData();
        });
    }
  },
  computed: {
    showAction () {
      return this.showHandle('preTestInstrument:edit,preTestInstrument:delete');
    }
  }
};
</script>

<style lang="less" scoped>
.instrument {
  height: 100%;

  .add-box {
    display: flex;
    justify-content: flex-end;
    margin-bottom: 12px;
  }
  .empty-content {
    width: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    background: var(--zw-card-bg-color--default);
    height: calc(100vh - 100px);
  }
}
</style>
