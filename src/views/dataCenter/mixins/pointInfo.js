import {
  insightToolsInsightAnalysisData
} from '@/api/dataCenter/index';
import moment from 'moment';
import clone from 'xe-utils/clone';
import { flattenDeep, uniq, groupBy } from 'lodash';
export default {
  data () {
    return {
      isFromRoute: 0, // 诊断跳转最后一次才触发  这样loading不会消失
      list: [],
      pointListTemplt: [],
      dataSource: [], // 数据
      columnMap: [], // 表头
      unit: [] // 所选择的单位执行栈
    };
  },
  methods: {
    disabledDate (current) {
      // 不能选择今天以后的
      return current && current > moment().endOf('day');
    },
    // 查询echarts数据
    getInsightToolsInsightAnalysisData (fromRoute) {
      this.isFromRoute = fromRoute ? this.isFromRoute + 1 : 0;
      this.list = [];
      // 处理传参
      this.disposeParams();
      let commonParams = {
        startTime: moment(this.chosenRange[0]).format('YYYY-MM-DD'),
        endTime: moment(this.chosenRange[1]).format('YYYY-MM-DD'),
        timeInterval: this.queryParams.timeInterval
      };
      let params = { ...commonParams, list: this.list };
      this.loading = true;
      // this.rightContentLoading = true;
      if (this.isFromRoute === 0 || this.isFromRoute === 2) {
        insightToolsInsightAnalysisData(params).then(res => {
          if (res && res.result_code === '1') {
            let data = res.result_data || {};
            // 处理table data数据
            this.disposeTableData(data);
            this.changeDataShowType();
            if (this.dataShowType === 'table') {
              this.$refs.indidatorTable.init('', this.tabType, this.dataSource || [], this.columnMap || []);
            }
            // 图表数据
            this.chartBaseData = data;
          }
          this.isFromRoute = 0;
          // 判断如果非多设备多测点的情况下姜dataGrid关闭，直接渲染单Y轴
          if (!this.showToggleGridIcon) this.toggleGridStatus = false;
          this.renderPointChart();
          // this.rightContentLoading = false;
          this.loading = false;
        }).catch(() => {
          this.loading = false;
          // this.rightContentLoading = false;
          this.fromRoute = 0;
        });
      }
    },
    disposeParams () {
      this.pointListTemplt = this.pointList.filter(item => item.key !== '');
      // 设备和测点都有的情况下遍历处理
      if (this.pointListTemplt && this.pointListTemplt.length && this.deviceList && this.deviceList.length) {
        this.getSameStation();
      }
    },
    // 获得相同的电站下的设备
    getSameStation () {
      let stationNameList = Array.from(new Set(this.deviceList.map(item => item.psName)));
      let stationList = [];
      stationNameList.forEach(item => {
        let obj = {
          psName: item,
          psKeys: {},
          deviceType: [],
          psId: ''
        };
        this.deviceList.forEach(list => {
          if (list.psName === item) {
            obj.psId = list.psId || list.psKey.split('_')[0];
            if (Object.keys(obj.psKeys) && Object.keys(obj.psKeys).length && Object.keys(obj.psKeys).includes(list.deviceType)) {
              obj.psKeys[list.deviceType].push(list.psKey);
            } else {
              this.$set(obj.psKeys, list.deviceType, [list.psKey]);
            }
            obj.deviceType.push(list.deviceType);
          }
        });
        stationList.push(obj);
      });
      if (stationList && stationList.length) {
        this.disposePoint(stationList);
      }
    },
    disposePoint (stationList) {
      // 处理测点
      let pointTypeData = Array.from(new Set(this.pointListTemplt.map(item => item.deviceType)));
      // 类型集合
      let pointTypeList = [];
      pointTypeData.forEach(item => {
        let arr = [];
        this.pointListTemplt.forEach(list => {
          if (list.deviceType === item) {
            arr.push(list);
          }
        });
        pointTypeList.push(arr);
      });
      this.disposeParamsInfo(stationList, pointTypeList);
    },
    // 参数拼接
    disposeParamsInfo (stationList, pointTypeList) {
      this.list = [];
      stationList.forEach(item => {
        pointTypeList.forEach(list => {
          let obj = {
            deviceType: '',
            psName: item.psName,
            psId: item.psId,
            pointList: [],
            psKeys: []
          };
          // 类型相同才添加
          if (item.deviceType.includes(list[0].deviceType)) {
            list.forEach(point => {
              obj.deviceType = point.deviceType;
              obj.psKeys = item.psKeys[point.deviceType];
              obj.pointList.push({ ...point, psKeys: item.psKeys[point.deviceType].join(',') });
            });
            this.list.push(obj);
          }
        });
      });
    },
    // 处理table数据
    disposeTableData (data) {
      if (data.yData && data.yData.length) {
        // 电站名称列表
        let psNameList = Array.from(new Set(data.yData.map(item => item.psName)));
        // 相同电站集合
        let psList = {};
        psNameList.forEach(item => {
          let obj = data.yData.filter(list => list.psName === item);
          psList[item] = obj;
        });
        // 拼表头 拼数据
        this.disposeTableHeader(psList, data.xData);
      } else {
        this.dataSource = []; // 数据
        this.columnMap = []; // 表头
      }
    },
    disposeTableHeader (data, xData) {
      this.columnMap = {
        time: '时间'
      };
      this.dataSource = xData.map(item => {
        return {
          time: item
        };
      });

      let index = 0; // 标题名称
      for (let key in data) {
        let psName = key;
        if (data[key] && data[key].length) {
          data[key].forEach(item => {
            if (item.data && item.data.length) {
              item.data.forEach(every => {
                if (every.pointName && every.pointName.length) {
                  every.pointName.forEach((list, sort) => {
                    let name = `name${index}`;
                    let obj = {};
                    this.$set(obj, name, psName + '/' + every.title + '/' + list + '(' + every.unit[sort] + ')');
                    this.columnMap = Object.assign({}, this.columnMap, obj);
                    index++;
                    if (every.data && every.data.length) {
                      // 处理表体
                      this.disposeDataSource(name, every.data[sort]);
                    }
                  });
                }
              });
            }
          });
        }
      }
    },
    // 处理表体
    disposeDataSource (name, data) {
      this.dataSource = this.dataSource.map((item, index) => {
        let obj = item;
        // this.$set(obj, name, data[index]);
        obj[name] = data[index];
        return obj;
      });
    },
    // 更新单位
    updateUnit (val, e) {
      if (e.node.dataRef.isCheckAll && e.checked) {
        const clonedUnit = clone(this.unit, true);
        const { children } = e.node.dataRef;
        const childPointList = children.map(item => item.point);
        const newUnit = clonedUnit.filter(item => !childPointList.includes(item.point));
        this.unit = newUnit.concat(children);
      } else if (e.node.dataRef.isCheckAll && !e.checked) {
        const clonedUnit = clone(this.unit, true);
        const { children } = e.node.dataRef;
        const childPointList = children.map(item => item.point);
        const newUnit = clonedUnit.filter(item => !childPointList.includes(item.point));
        this.unit = newUnit;
      } else if (e.checked) {
        this.unit.push(e.node.dataRef);
      } else {
        this.unit = this.unit.filter(item => item.point !== e.node.dataRef.point);
      }
    },
    // 常用测点的回显设置Y轴单位
    updateUnitFromCommonPoint (pointList) {
      this.unit = pointList.map(item => ({
        ...item,
        point: item.point || item.pointKey,
        unit: item.unit || item.pointUnit
      }));
    },
    // 渲染测点图表数据
    renderPointChart () {
      // 当前状态是否为Y轴平铺状态
      const toggleGrid = this.toggleGridStatus;
      const newOptions = clone(this.chartBaseOptions, true);
      const { xData, yData } = this.chartBaseData;
      // dataZoom
      newOptions.dataZoom[0].xAxisIndex = xData.map((_, index) => index);
      newOptions.dataZoom[1].xAxisIndex = xData.map((_, index) => index);
      // 所有需要画的图例
      const legendList = flattenDeep(yData.map((item) => {
        return item.data.map(secondItem => {
          return secondItem.data.map((_, thirdIndex) => {
            return {
              name: `${item.psName}/${secondItem.title}/${secondItem.pointName[thirdIndex]}`,
              point: secondItem.point[thirdIndex],
              pointName: secondItem.pointName[thirdIndex],
              unit: secondItem.unit[thirdIndex]
            };
          });
        });
      }));
      // 由于测点单位可能会和响应的数据的单位不一致，所以需要根据响应的数据来更新手动选择测点的单位
      this.unit.forEach(item => {
        const findItem = legendList.find(o => o.point == item.point) || {};
        item.unit = findItem.unit;
      });
      // 计算最新两个测点当作显示的y轴
      const len = this.unit.length;
      const last = this.unit[len - 1]; // 从单位执行站中选出倒数第一个
      const penultimate = this.unit[len - 2]; // 从单位执行站中选出倒数第二个
      // console.log('unit', this.unit);
      const yUnit = [penultimate, last].reduce((acc, cur) => {
        if (!this.$isEmpty(cur)) acc.push(cur);
        return acc;
      }, []);
      // 克隆出一份新内存，重新组装测点排序数据
      const clonedUnit = clone(this.unit, true);
      const filterUnit = clonedUnit.slice(-2) || [];
      // 当前Y轴所显示的测点
      const yUnitByPoint = yUnit.map(item => item.point);
      // 排除yUnit以外的数据
      const oddData = clonedUnit.filter(item => !yUnitByPoint.includes(item.point));
      // 然后根据最新选择的两个测点排在前面
      const newUnit = [...filterUnit, ...oddData];

      // 所有去重后的测点
      const pointList = uniq(legendList.map(item => item.point));
      // console.log('pointList', pointList, pointList.length);
      // 将数据扁平，组装成一维数组
      const flattenList = flattenDeep(yData.map((item) => {
        return item.data.map(secondItem => {
          return secondItem.data.map((thirdItem, thirdIndex) => {
            return {
              name: `${item.psName}/${secondItem.title}/${secondItem.pointName[thirdIndex]}`,
              psName: item.psName, // 电站名称
              title: secondItem.title, // 设备名称
              unit: secondItem.unit[thirdIndex], // 单位
              point: secondItem.point[thirdIndex], // 测点
              pointName: secondItem.pointName[thirdIndex], // 测点名称
              deviceType: item.deviceType, // 设备类型
              psKey: secondItem.psKey, // 设备唯一标识
              data: thirdItem
            };
          });
        });
      }));
      // 筛选出最后选择的测点所对应的数据
      const filterFlattenList = flattenList.filter(item => yUnitByPoint.includes(item.point));
      // 从原数据中去除这两个测点的数据
      const oddFlattenList = flattenList.filter(item => !yUnitByPoint.includes(item.point));
      // 系列数据
      newOptions.series = [...filterFlattenList, ...oddFlattenList].map((item, index) => {
        return this.makeSeries(
          toggleGrid ? pointList.findIndex(o => o == item.point) : 0,
          toggleGrid ? pointList.findIndex(o => o == item.point)
            : newUnit.findIndex(o => o.unit == item.unit),
          {
            name: `${item.psName}/${item.title}/${item.pointName}`,
            data: item.data.map(o => ({
              value: o,
              psName: item.psName, // 电站名称
              title: item.title, // 设备名称
              unit: item.unit, // 单位
              point: item.point, // 测点
              pointName: item.pointName, // 测点名称
              deviceType: item.deviceType, // 设备类型
              psKey: item.psKey // 设备唯一标识
            }))
          }, 'line');
      });

      // y轴数据
      if (toggleGrid) {
        newOptions.yAxis = pointList.map((item, index) => {
          const current = legendList.find(legendItem => legendItem.point == item);
          return this.makeYAxis(index, { name: current.pointName + '(' + current.unit + ')', type: 'value' }, false);
        });
      } else {
        // 取最新的两个作为Y轴单位
        if (this.$isEmpty(yUnit)) {
          newOptions.yAxis = this.makeYAxis(0, { name: '', type: 'value' }, true);
        } else {
          const leftAndRightYAxis = filterUnit.map((item, index) => {
            return this.makeYAxis(0, {
              // name: legendList.find(legendItem => legendItem.point == item.point).unit,
              type: 'value',
              yAxisIndex: index,
              alignTicks: true
            }, true);
          });
          const restYAxis = oddData.map((item, index) => {
            return this.makeYAxis(0, {
              // name: legendList.find(legendItem => legendItem.point == item.point).unit,
              type: 'value',
              show: false,
              yAxisIndex: index + leftAndRightYAxis.length,
              alignTicks: true
            }, true);
          });
          newOptions.yAxis = [...leftAndRightYAxis, ...restYAxis];
        }
      }
      // x轴数据
      if (toggleGrid) {
        newOptions.xAxis = pointList.map((_, index) => {
          return this.makeXAxis(index, { data: xData }, pointList.length);
        });
      } else {
        newOptions.xAxis = this.makeXAxis(0, { data: xData }, 1);
      }
      // tooltip
      newOptions.tooltip = {
        className: 'solar-eye-tooptip deep-anly-tooptip',
        triggerOn: 'mousemove',
        enterable: true,
        confine: true,
        trigger: 'axis',
        alwaysShowContent: false,
        appendToBody: true,
        position: (point, params, dom, rect, size) => {
          let content = document.getElementById('insight-chart');
          let minTop = content.scrollTop;
          let maxTop = content.scrollTop + content.offsetHeight - dom.offsetHeight - 30;
          let minLeft = 0;
          let maxLeft = content.offsetWidth - dom.offsetWidth;
          let top = 0;
          let left = 0;
          if (minTop <= point[1] && point[1] <= maxTop) {
            top = point[1];
          } else if (point[1] < minTop) {
            top = minTop;
          } else {
            top = maxTop;
          }
          if (minLeft < point[0] + 20 && point[0] + 20 <= maxLeft) {
            left = point[0] + 20;
          } else {
            left = point[0] - dom.offsetWidth - 20;
          }
          return { top: top, 'left': left };
        },

        formatter: (params) => {
          const totalData = params.map(item => ({
            ...item.data,
            marker: item.marker
          }));
          const groupMap = groupBy(totalData, 'psName');
          const newGroupMap = Object.keys(groupMap).reduce((acc, cur) => {
            // 把对应的设备名归类
            acc[cur] = groupBy(groupMap[cur], 'title');
            return acc;
          }, {});
          let customerHtml = '';
          customerHtml += `<div style="display: flex">`;
          for (const key in newGroupMap) {
            customerHtml += `<div style="display: flex;margin-right: 16px">`;
            for (const item in newGroupMap[key]) {
              customerHtml += '<div>';
              if (Object.keys(groupMap).length > 1) customerHtml += `<div class="ps-name">${key}</div>`;
              customerHtml += `<div class="device-item">${item}</div>`;
              newGroupMap[key][item].forEach(every => {
                const value = this.$isEmpty(every.value) ? '--' : every.value;
                customerHtml += `
                <div class="point-item">
                  ${every.marker}
                  ${every.pointName}: ${value} ${every.unit ? '(' + every.unit + ')' : ''}
                </div>`;
              });
              customerHtml += `</div>`;
            }
            customerHtml += `</div>`;
          }
          customerHtml += `</div>`;
          const axisValue = params[0].axisValue;
          return (
            `
              <div>
                <span>${axisValue}</span>
                ${customerHtml}
              </div>
            `
          );
        }
      };
      // grid分割
      if (this.showToggleGridIcon && toggleGrid) {
        newOptions.grid = pointList.map((_, index) => {
          return this.makeGrid(this.baseHeight + this.gridHeight * index, index, pointList.length);
        });
      } else {
        newOptions.grid = { left: '10%', right: '10%' };
      }
      // 图例数据
      newOptions.legend.data = legendList;
      // 单个y轴时 图表占满 多y轴固定高度
      let height = 0;
      if (toggleGrid) {
        height = this.baseHeight * 2 + this.gridHeight * (pointList.length);
      } else {
        height = $('.right-area').height() - 130;
      }
      this.myChart.resize({ height });
      // 处理图表颜色
      const isDarkColor = this.navTheme == 'dark' ? '#ffffff' : '#333';
      newOptions.legend.textStyle.color = isDarkColor;
      newOptions.legend.pageIconColor = isDarkColor;
      newOptions.legend.pageTextStyle.color = isDarkColor;
      console.log('newOptions', newOptions);
      this.myChart.setOption(newOptions, true);
    },
    handleToggleGrid () {
      this.toggleGridStatus = !this.toggleGridStatus;
      this.renderPointChart();
    },
    // 日期切换
    rangeDateChange (val) {
      if (val && val.length > 0) {
        let dayIntervel = val[1].diff(val[0], 'day');
        if (dayIntervel === 0) {
          this.timeIntervalOptions = this.timeIntervalOptionsAll.day;
        } else if (dayIntervel >= 1 && dayIntervel < 10) {
          this.timeIntervalOptions = this.timeIntervalOptionsAll.week;
        } else if (dayIntervel >= 10 && dayIntervel <= 31) {
          this.timeIntervalOptions = this.timeIntervalOptionsAll.month;
        } else {
          this.$message.info('日期范围最多一个月');
          this.timeRange = [moment(val[1]).subtract(1, 'month').calendar(), moment(val[1])];
          this.queryParams.startTime = moment(moment(val[1]).subtract(1, 'month').calendar()).format('YYYY-MM-DD');
          this.queryParams.endTime = moment(val[1]).format('YYYY-MM-DD');
          this.timeIntervalOptions = this.timeIntervalOptionsAll.month;
          this.queryParams.timeInterval = this.timeIntervalOptions[0].value;
          return;
        }
        this.queryParams.timeInterval = this.timeIntervalOptions[0].value;
        this.queryParams.startTime = moment(val[0]).format('YYYY-MM-DD');
        this.queryParams.endTime = moment(val[1]).format('YYYY-MM-DD');
      } else {
        this.timeIntervalOptions = this.timeIntervalOptionsAll.day;
        this.queryParams.timeInterval = this.timeIntervalOptions[0].value;
        this.queryParams.startTime = moment().format('YYYY-MM-DD');
        this.queryParams.endTime = moment().format('YYYY-MM-DD');
      }
      this.getInsightToolsInsightAnalysisData();
      this.getWeatherInfo();
    }
  }
};
