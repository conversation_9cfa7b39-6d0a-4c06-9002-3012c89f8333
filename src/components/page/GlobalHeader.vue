<template>
  <!-- , width: fixedHeader ? `calc(100% - ${sidebarOpened ? 256 : 80}px)` : '100%'  -->
  <a-layout-header
    v-if="!headerBarFixed"
    :class="[fixedHeader && 'ant-header-fixedHeader', sidebarOpened ? 'ant-header-side-opened' : 'ant-header-side-closed', ]"
    :style="{ padding: '0' }" v-webpBg>

    <div v-if="mode === 'sidemenu'" class="header" :class="theme">
      <a-icon
        v-if="device==='mobile'"
        class="trigger"
        :type="collapsed ? 'menu-fold' : 'menu-unfold'"
        @click="toggle"></a-icon>
      <a-icon
        v-else
        class="trigger"
        :type="collapsed ? 'menu-unfold' : 'menu-fold'"
        @click="toggle"/>

      <span v-if="device === 'desktop'">欢迎进入 SolarEye 平台</span>
      <spn v-else>SolarEye</spn>

      <user-menu :theme="theme"/>
    </div>
    <!-- 顶部导航栏模式 -->
    <div v-else :class="['top-nav-header-index', theme]">
      <div class="header-index-wide">
        <div class="header-index-left" :style="topMenuStyle.headerIndexLeft" id="guide-step1">
          <logo ref="logoIntro" class="top-nav-header" :show-title="device !== 'mobile'" :style="topMenuStyle.topNavHeader"/>
          <div :style="topMenuStyle.topSmenuStyle">
            <s-menu
              mode="horizontal"
              :menu="menus"
              :theme="theme" v-webpBg></s-menu>
          </div>
          <!-- <a-icon
            v-else
            class="trigger"
            :type="collapsed ? 'menu-fold' : 'menu-unfold'"
            @click="toggle"></a-icon> -->
        </div>
        <user-menu class="header-index-right" :theme="theme" :style="topMenuStyle.headerIndexRight" @change="changeEvent"/>
      </div>
    </div>
  </a-layout-header>
</template>

<script>
import UserMenu from '../tools/UserMenu';
import SMenu from '../menu/';
import Logo from '../tools/Logo';
import { mixin } from '@/utils/mixin.js';
export default {
  name: 'GlobalHeader',
  components: {
    UserMenu,
    SMenu,
    Logo
  },
  mixins: [mixin],
  props: {
    mode: {
      type: String,
      // sidemenu, topmenu
      default: 'sidemenu'
    },
    menus: {
      type: Array,
      required: true
    },
    theme: {
      type: String,
      required: false,
      default: 'dark'
    },
    collapsed: {
      type: Boolean,
      required: false,
      default: false
    },
    device: {
      type: String,
      required: false,
      default: 'desktop'
    }
  },
  data () {
    return {
      headerBarFixed: false,
      // update-begin--author:sunjianlei---date:20190508------for: 顶部导航栏过长时显示更多按钮-----
      topMenuStyle: {
        headerIndexLeft: {},
        topNavHeader: {},
        headerIndexRight: {},
        topSmenuStyle: {}
      },
      chatStatus: ''
    };
  },
  watch: {
    /** 监听设备变化 */
    device () {
      if (this.mode === 'topmenu') {
        this.buildTopMenuStyle();
      }
    },
    /** 监听导航栏模式变化 */
    mode (newVal) {
      if (newVal === 'topmenu') {
        this.buildTopMenuStyle();
      }
    }
  },
  // 顶部导航栏过长时显示更多按钮-----
  mounted () {
    window.addEventListener('scroll', this.handleScroll);
    if (this.mode === 'topmenu') {
      this.buildTopMenuStyle();
    }
    // }
  },
  methods: {
    handleScroll () {
      if (this.autoHideHeader) {
        let scrollTop = window.pageYOffset || document.documentElement.scrollTop || document.body.scrollTop;
        if (scrollTop > 100) {
          this.headerBarFixed = true;
        } else {
          this.headerBarFixed = false;
        }
      } else {
        this.headerBarFixed = false;
      }
    },
    toggle () {
      this.$emit('toggle');
    },
    // 顶部导航栏过长时显示更多按钮-----
    buildTopMenuStyle () {
      if (this.mode === 'topmenu') {
        this.topMenuStyle.topNavHeader = { 'min-width': this.$refs.logoIntro.isTrue() ? '300px' : '' };
        this.topMenuStyle.topSmenuStyle = { 'width': 'calc(100% - 300px)', background: 'var(--blue)' };
        this.$nextTick(() => {
          let rightWidth = document.getElementsByClassName('header-index-right')[0].offsetWidth + 'px';
          this.topMenuStyle.headerIndexLeft = { 'width': `calc(100% - ${rightWidth})` };
        });
        // if (this.device === 'mobile') {
        //   // 手机端需要清空样式，否则显示会错乱
        //   this.topMenuStyle.topNavHeader = {};
        //   this.topMenuStyle.topSmenuStyle = {};
        //   this.topMenuStyle.headerIndexRight = {};
        //   this.topMenuStyle.headerIndexLeft = {};
        // } else {
        //   let rightWidth = '510px';
        //   let isTrue = this.$refs.logoIntro.isTrue();
        //   this.topMenuStyle.topNavHeader = { 'min-width': isTrue ? '300px' : '250px' };
        //   this.topMenuStyle.topSmenuStyle = { 'width': isTrue ? 'calc(100% - 300px)' : 'calc(100% - 250px)' };
        //   this.topMenuStyle.headerIndexRight = { 'min-width': rightWidth };
        //   this.topMenuStyle.headerIndexLeft = { 'width': `calc(100% - ${rightWidth})` };
        // }
      }
    },
    // update-begin--author:sunjianlei---date:20190508------for: 顶部导航栏过长时显示更多按钮-----
    changeEvent (value) {
      let rightWidth = document.getElementsByClassName('header-index-right')[0].offsetWidth + (value ? 207 : -207) + 'px';
      this.topMenuStyle.headerIndexLeft = { 'width': `calc(100% - ${rightWidth})` };
    }
  }
};
</script>

<style lang="less" scoped>
  /* update_begin author:scott date:20190220 for: 缩小首页布局顶部的高度*/

  @height: 56px;

  .layout {

    .top-nav-header-index {
      background: var(--blue);

      .header-index-wide {
        //margin-left: 10px;

        .ant-menu.ant-menu-horizontal {
          height: 40px;
          margin: 8px 0;
          line-height: 40px !important;
        }
        .ant-menu-item-selected {
          border-bottom: 2px solid #000000;
        }
      }
      .trigger {
        line-height: 64px;
        &:hover {
          background: rgba(0, 0, 0, 0.05);
        }
      }
    }

    .header {
      z-index: 2;
      color: white;
      height: @height;
      background-color: @primary-color;
      transition: background 300ms;

      /* dark 样式 */
      &.dark {
        color: #000000;
        box-shadow: 0 0 4px rgba(0, 0, 0, 0.2);
        background-color: white !important;
      }
    }

    .header, .top-nav-header-index {
      &.dark .trigger:hover {
        background: rgba(0, 0, 0, 0.05);
      }
    }
  }

  .ant-layout-header {
    height: @height;
    line-height: @height;
  }
  // /deep/ .ant-menu-item{
  //   font-size: 16px;
  // }

  /* update_end author:scott date:20190220 for: 缩小首页布局顶部的高度*/

</style>
<style>
.highlightClass {
  border:2px;
  border-radius: 8px;
  box-shadow: rgb(250 250 250 / 80%) 0px 0px 1px 2px,
  rgb(33 33 33 / 60%) 0px 0px 0px 5000px!important;
}

.tooltipClass {
  width: 400px;
  font-size: 18px;
  font-weight: 400;
  color: var(--white);
  line-height: 20px;
  background: rgba(121, 121, 122, 0.6);
}
.introjs-bottom-left-aligned {
  left: 45% !important;
}

.introjs-tooltip{
  max-width: 400px !important;
}

.introjs-tooltip-title {
  font-size: 18px;
  font-weight: normal;
  color: var(--white);
}

.introjs-arrow.top {
  border-color: transparent transparent rgb(121, 121, 122,0.6);
}
.introjs-arrow.left {
  border-color: transparent rgb(121, 121, 122,0.6) transparent transparent;
}
.introjs-arrow.right {
  border-color: transparent transparent transparent rgb(121, 121, 122,0.6);
}
.introjs-skipbutton {
  color: #FFFFFF;
}

</style>
