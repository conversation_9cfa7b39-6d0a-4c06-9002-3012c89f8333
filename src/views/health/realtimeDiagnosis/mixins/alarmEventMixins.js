import {
  deleteAlarmEvent,
  batchDistributeEvent
} from '@/api/health/AlarmEvents.js';
import { getSystemCodeListWithChild } from '@/api/health/safetyEvents.js';
import { TENANT_ID, USER_INFO } from '@/store/mutation-types';
export const AlarmEventMixins = {
  data () {
    return {
      // 分页参数
      total: 0,
      page: 1,
      size: 10,
      data: [],
      pageSizeOptions: [5, 10, 15, 20, 50, 100, 500]
    };
  },
  methods: {
    // 表格分页选项变化事件
    sizeChange (current, pageSize) {
      this.page = current;
      this.size = pageSize;
      this.getList(true);
    },
    // 表格当前页码变化事件
    pageChange (val, refreshScroll) {
      if (val) {
        this.page = val;
      }
      this.getList(refreshScroll);
    },
    // 派发、隔离、删除、关闭等弹框关闭事件，type: 1 工作台页面操作； 2 分析页面操作
    viewClose (type, flag) {
      if (type == '1') {
        if (flag) {
          this.getList(false);
        }
      } else {
        if (flag) {
          this.onClose(true);
        }
      }
    },
    // 批量派发
    batchDistributeEvent (row, type, orderSource) {
      // 低效-电站
      let isTab3DeviceType11 = this.type == '3' && row.deviceType == '11';
      let userInfo = Vue.ls.get(USER_INFO);
      let that = this;
      let arr = [];
      let params = {
        sysTenantId: Vue.ls.get(TENANT_ID),
        orgCode: (userInfo.orgCode ? userInfo.orgCode : ''),
        userId: (userInfo.id ? userInfo.id : ''),
        userAccount: (userInfo.username ? userInfo.username : ''),
        workNo: (userInfo.workNo ? userInfo.workNo : ''),
        needHandover: (userInfo.needHandover ? userInfo.needHandover : ''),
        hasAllData: (userInfo.hasAllData ? userInfo.hasAllData : ''),
        lang: '_zh_CN'
      };
      if (row.displayList && row.displayList.length) {
        row.displayList.forEach(item => {
          arr.push({
            psKey: item.psKey,
            alarmType: item.alarmType,
            findTimeString: item.happenTime,
            psId: item.psId,
            physicsPsId: item.psId,
            psaId: item.psaId,
            faultDeviceName: item.deviceNameShow,
            alarmRemark: item.alarmRemark,
            alarmReason: item.alarmReason,
            isFarm: item.isFarm,
            faultStatus: item.faultStatus,
            deviceType: item.deviceType,
            id: item.id,
            orderSource: orderSource,
            ...params
          });
        });
      } else {
        arr.push({
          psKey: row.psKey,
          alarmType: row.alarmType,
          findTimeString: row.happenTime,
          psId: row.psId,
          physicsPsId: row.psId,
          psaId: row.psaId,
          faultDeviceName: row.deviceNameShow,
          alarmRemark: row.alarmRemark,
          alarmReason: row.alarmReason,
          isFarm: row.isFarm,
          faultStatus: row.faultStatus,
          deviceType: row.deviceType,
          id: row.id,
          orderSource: orderSource,
          ...params
        });
      }
      // 低效-电站 特殊参数
      if (isTab3DeviceType11) {
        arr.forEach(item => {
          item.alarmRemarkAndReason = `${item.alarmRemark}/${item.alarmReason}`;
        });
      }
      this.$confirm({
        title: '派发确认',
        content: `是否派发此条${this.getAlarmName()}？`,
        okText: '确定',
        cancelText: '取消',
        onOk: () => {
          that.pageloading = true;
          batchDistributeEvent({ list: arr, groupBy: isTab3DeviceType11 ? 'psId' : 'psKey' }).then(res => {
            if (res.success) {
              that.$message.success(`${`此条${this.getAlarmName()}`}派发成功`);
              that.pageloading = false;
              if (type == '2') {
                that.onClose(true);
              } else {
                that.getList(false);
              }
            } else {
              that.pageloading = false;
              that.$message.warning(res.result);
            }
          }).catch(() => {
            that.pageloading = false;
          });
        }
      });
    },
    // 删除,type为1时是列表操作，type为2时是分析页面操作
    doDelete (row, type) {
      // 折叠删除
      let id = '';
      if (row.displayList && row.displayList.length) {
        let arr = [];
        row.displayList.forEach(item => {
          arr.push(item.id);
        });
        id = arr.join();
      } else {
        id = row.id;
      }
      let that = this;
      this.$confirm({
        title: '提交确认',
        content: '是否确认此条数据为误报信息，删除后不可撤回！',
        okText: '确定',
        cancelText: '取消',
        onOk: function () {
          that.pageloading = true;
          deleteAlarmEvent({ id: id }).then(res => {
            if (res.result_code == '1') {
              that.viewClose(type, true);
              that.$message.success('删除成功!');
            }
            that.pageloading = false;
          }).catch(() => {
            that.pageloading = false;
          });
        },
        onCancel: function () {
          if (type == '1') {
            that.viewClose('1', false);
          }
        }
      });
    },
    // 工单详情跳转
    workOrderDetail (row, type) {
      if (!row.orderId) {
        this.$message.warning('此条告警未绑定工单');
        return;
      }
      if (row.workOrderAuth != '1') {
        this.$message.warning('该租户无权查看工单详情！');
        return;
      }
      if (!row.projectBusinessType) {
        this.$message.warning('无工单来源，请检查！');
        return;
      }
      let obj = { id: row.orderId, isFarm: row.isFarm, isOrder: true, processInstanceId: row.processInstanceId, flowStsName: row.flowStsName };
      // 分布式 - 工商业
      if (row.projectBusinessType == '5') {
        this.$refs.orderForm.init('3', obj, '/distributed/orderHall/modules/OrderHallForm');
        return;
      }
      let isAlarmHistory = false;
      if (type == 'alarm') {
        let time = new Date('2022/05/28 22:00').getTime();
        let distributeTime = new Date(row.distributeTime).getTime();
        isAlarmHistory = this.dealStatus == '03' && distributeTime < time;
      }
      if (row.isFarm == '1' || isAlarmHistory) {
        if (row.isNew == '1') {
          this.$refs.orderForm.init('3', obj, '/operations/workManage/orderHall/modules/OrderForm');
        } else {
          this.$refs.orderForm.init('3', obj, '/operations/workManage/workOrder/modules/orderForm');
        }
      } else {
        // this.$refs.orderForm.init('3', obj, '/operations/workManage/unPlanedWork/modules/orderForm');
        this.$refs.orderForm.init('3', obj, '/orderHall/modules/OrderHallForm');
      }
    },
    // 根据权限标识和外部指示状态进行权限判断
    show (perms) {
      return this.showHandle(perms);
    },
    // 对象数组去重
    arrUnique (arr, key) {
      let returnArr = [];
      let obj = {};
      returnArr = arr && arr.length > 0 && arr.reduce((cur, next) => {
        // eslint-disable-next-line no-unused-expressions
        obj[next[key]] ? '' : obj[next[key]] = true && cur.push(next);
        return cur;
      }, []);
      return returnArr;
    },
    // 下拉选择框-按名称模糊搜索
    filterOption (input, option) {
      return (
        option.componentOptions.children[0].text.toLowerCase().indexOf(input.toLowerCase()) >= 0
      );
    },
    getAlarmName () {
      let alarmName = '';
      if (this.type == '1' || this.type == '2') {
        alarmName = '故障';
      } else if (this.type == '3') {
        alarmName = '缺陷';
      } else {
        alarmName = '隐患';
      }
      return alarmName;
    },
    async getAlarmTypes () {
      let res = await getSystemCodeListWithChild({
        firstTypeCode: '0072'
      });
      this.alarmTypes = res.result_data['0072'].reduce((prev, cur) => {
        let obj = {
          label: cur.secondName,
          value: cur.secondTypeCode,
          children: res.result_data[cur.remark].map(el => {
            return {
              label: el.secondName,
              value: el.secondTypeCode
            };
          })
        };
        return prev.concat([obj]);
      }, [{
        value: '',
        label: '全部'
      }]);
    }
  }
};
