<template>
  <a-drawer
    width="100%"
    title="批量调整"
    :visible="visible"
    @close="onClose"
    :get-container="getDom"
    :wrap-style="{ position: 'absolute' }"
    :afterVisibleChange="afterVisibleChange">
    <a-spin :spinning="loading" style="height:100%;">
      <a-row :gutter="16" style="height:100%;">
        <a-col :span="4" class="left-content">
          <a-tree ref="deviceTree" :treeData="treeData" :selectedKeys="selectedKeys" @select="onClick" class="left-tree">
            <!-- <template #title="{ title, selectable }">
              <span :title='title' :class="selectable ? '' : 'unSelectable'">{{title}} </span>
            </template> -->
          </a-tree>
        </a-col>
        <a-col :span="20" class="right-content">
          <div  class="solar-eye-search-model">
            <a-row :gutter="24" style="margin: 0">
              <a-col :xxl="6" :xl="8" :md="12">
                <div class="search-item">
                  <span class="search-label">设备类型</span>
                   <a-select v-model="queryParams.deviceType" @change="deviceTypeChange">
                      <a-select-option v-if="deviceType != '4'" value="1">逆变器</a-select-option>
                      <a-select-option value="4">汇流箱</a-select-option>
                    </a-select>
                </div>
              </a-col>
              <template v-if="isSelf">
              <a-col :xxl="6" :xl="8" :md="12">
                <div class="search-item">
                  <span class="search-label">生产厂商</span>
                  <a-select v-model="queryParams.factoryId" @change="manufacturersChange"  :filter-option="filterOption"
                      show-search placeholder="请选择" allowClear>
                    <a-select-option v-for="item in manufacturersList" :key="item.id" :value="item.id">
                      {{ item.factory }}
                    </a-select-option>
                  </a-select>
                </div>
              </a-col>
              <a-col :xxl="6" :xl="8" :md="12">
                <div class="search-item">
                  <span class="search-label">设备型号</span>
                  <a-select  v-model="queryParams.modelId" :filter-option="filterOption" allowClear show-search placeholder="请选择">
                    <a-select-option v-for="item in deviceModelList" :key="item.id" :value="item.id">
                      {{ item.data }}
                    </a-select-option>
                  </a-select>
                </div>
              </a-col>
              </template>

              <a-col :xxl="6" :xl="8" :md="12" >
                <div class="search-item ">
                  <div class="search-label">
                    <a-button class="solar-eye-btn-primary"  @click="pageChange">查询</a-button>
                  </div>
                </div>
              </a-col>
            </a-row>
          </div>
          <a-col class="solar-eye-main-content">
            <div class="operation" style="height: 32px">
              <div class="operation-btn">
                <a-radio-group v-model="tab"  @change="tabChange"  size="default">
                    <a-radio-button v-if="show('station:baseInfo') && isSelf" value="1">设备基本信息</a-radio-button>
                    <a-radio-button v-if="show('station:configInfo')" value="2">配置信息</a-radio-button>
                </a-radio-group>
                <div class="right-btn" v-if="showTable">
                  <a-button @click="pageChange" class="solar-eye-btn-primary-cancel">取消</a-button>
                  <a-button @click="saveData" :disabled="getSaveBtnDisabled()" class="solar-eye-btn-primary">保存</a-button>
                </div>
              </div>
            </div>
            <vxe-table v-if="tab == '1' && show('station:baseInfo')" :data="baseData" :height="tableHeight-24" key="one" ref="baseTable" align="center"
              resizable border show-overflow highlight-hover-row size="small" @checkbox-all="onSelectChange" @checkbox-change="onSelectChange">
              <vxe-table-column type="checkbox" width="80" align="center"></vxe-table-column>
              <vxe-table-column show-overflow="title" field="deviceTypeName" title="设备类型" min-width="80">
              </vxe-table-column>
              <vxe-table-column show-overflow="title" field="deviceName" title="设备名称" min-width="120">
              </vxe-table-column>
              <vxe-table-column show-overflow="title" field="factory" title="生产厂商" min-width="140">
                <template v-slot="{ row, rowIndex }">
                  <a-select v-model="row.factory" @change="(value) => selectChange(value, row, rowIndex, 'factory', 'diffManufacturers', 'setManufacturersData')"
                    :filter-option="filterOption" show-search :class="row.diffManufacturers ? 'invalid' : ''">
                    <a-select-option v-for="item in row.manufacturersList" :key="item.id" :value="item.id">
                      {{ item.factory }}
                    </a-select-option>
                  </a-select>
                </template>
              </vxe-table-column>
              <vxe-table-column show-overflow="title" field="model" title="设备型号" min-width="120">
                <template v-slot="{ row, rowIndex }">
                  <a-select v-model="row.model" @change="(value) => selectChange(value, row, rowIndex, 'model', 'diffDeviceModel', 'setDeviceModelData')"
                    :filter-option="filterOption" show-search placeholder="请选择" @focus="deviceModelFocus(row, rowIndex)"
                    :class="!row.modelValid || row.diffDeviceModel ? 'invalid' : ''">
                    <a-select-option v-for="item in row.deviceModelList" :key="item.id" :value="item.id">
                      {{ item.data }}
                    </a-select-option>
                  </a-select>
                </template>
              </vxe-table-column>
              <vxe-table-column :visible="queryDeviceType == '1'" show-overflow="title" field="ratedPower" title="逆变器额定功率(kWp)" min-width="100">
                <template v-slot="{ row, rowIndex }">
                  <a-input-number @change="(val) => powerChange(val, rowIndex, 'ratedPower')" :max="999999999" :min="0" :precision="6"
                    v-model="row.ratedPower" placeholder="输入逆变器额定功率" :class="!row.ratedPowerValid ? 'invalid' : ''"/>
                </template>
              </vxe-table-column>
              <vxe-table-column :visible="queryDeviceType == '1'" show-overflow="title" field="installedPower" title="装机功率(kWp)" min-width="100">
                <template v-slot="{ row, rowIndex }">
                  <a-input-number @change="(val) => powerChange(val, rowIndex, 'installedPower')" :max="999999999" :min="0" :precision="6"
                    v-model="row.installedPower" placeholder="输入装机功率"  :class="!row.installedPowerValid ? 'invalid' : ''"/>
                </template>
              </vxe-table-column>
              <vxe-table-column show-overflow="title" field="updateUser" title="修改人" min-width="80">
                <template v-slot="{ row }">
                  {{row.updateUser || '--'}}
                </template>
              </vxe-table-column>
              <vxe-table-column show-overflow="title" field="updateTime" title="修改时间" min-width="120">
                <template v-slot="{ row }">
                  <span :title="row.updateTime ? moment(row.updateTime).format('YYYY-MM-DD HH:mm:ss') : '--'">
                    {{row.updateTime ? moment(row.updateTime).format('YYYY-MM-DD HH:mm:ss') : '--'}}
                  </span>
                </template>
              </vxe-table-column>
            </vxe-table>
            <vxe-table v-if="tab == '2' && show('station:configInfo')" :data="configData" :height="tableHeight-24" ref="configTable" align="center"
              resizable border show-overflow highlight-hover-row size="small" key="two" @checkbox-all="onSelectChange" @checkbox-change="onSelectChange">
              <vxe-table-column type="checkbox" width="80" align="center"  fixed="left"></vxe-table-column>
              <vxe-table-column show-overflow="title" field="deviceType" fixed="left" title="设备类型" width="120">
              </vxe-table-column>
               <vxe-table-column show-overflow="title" sortable title="设备名称" fixed="left"  min-width="160" field="deviceName">
          </vxe-table-column>
           <vxe-table-column show-overflow="title" title="配置方式"  min-width="160" field="isAdjust">
           <template v-slot="{ row, rowIndex }">
                <a-switch checked-children="手动" un-checked-children="AI" v-if="row.isAdjust!=undefined" v-model="row.isAdjust"  />
                  <span v-else>--</span>
                </template>
          </vxe-table-column>
              <vxe-table-column v-for="(item, index) in columnList" :key="index" show-overflow="title" :field="item.field" :title="item.title"  min-width="100">
                <template v-slot="{ row, rowIndex }">
                <a-switch checked-children="开" un-checked-children="关" v-if="row[item.field]!=undefined" :disabled=" !row.isAdjust || row['isEnable' + index] == '0'" v-model="row[item.field]"  @change="(val) => stringChange(val, rowIndex, item.field)" />
                  <!-- <a-select v-if="row[item.field]" v-model="row[item.field]" @change="(val) => stringChange(val, rowIndex, item.field)">
                    <a-select-option value="1">开启</a-select-option>
                    <a-select-option value="0">关闭</a-select-option>
                  </a-select> -->
                  <span v-else>--</span>
                </template>
              </vxe-table-column>
            </vxe-table>
            <page-pagination v-if="showTable" :pageSize="queryParams.pageSize" :current="queryParams.currentPage" :total="total" @size-change="sizeChange" />
          </a-col>
        </a-col>
      </a-row>
    </a-spin>
  </a-drawer>
</template>
<script>
import {
  getDeviceTypeTree,
  selectAttributeConfigsByPid,
  selectConfigInfosByPid,
  realTimeUpdate,
  updateConfigs
} from '@/api/health/healthapi.js';
import { DeviceMixins } from '../../mixins/device.js';
import { getManufacturersList, getDeviceModel } from '@/api/dataCenter/index';
import { USER_INFO } from '@/store/mutation-types';
import moment from 'moment';
export default {
  name: 'StationAdd',
  mixins: [ DeviceMixins ],
  props: {
    isSelf: {
      type: Boolean,
      default: false
    }
  },
  data () {
    return {
      visible: false,
      loading: false,
      treeData: [],
      selectedKeys: [],
      queryParams: {},
      columnList: [],
      configData: [],
      configArr: [],
      baseData: [],
      selection: [],
      deviceType: '1',
      psKey: '',
      userInfo: '',
      tableHeight: 0,
      queryDeviceType: '1',
      total: 0,
      inverterArr: ['factory', 'model', 'ratedPower', 'installedPower'],
      boxArr: ['factory', 'model'],
      showTable: true
    };
  },
  created () {
    this.userInfo = Vue.ls.get(USER_INFO);
  },
  mounted () {
    this.getTableHeight();
    window.onresize = () => {
      this.getTableHeight();
    };
  },
  methods: {
    moment,
    getDom () {
      return document.getElementsByClassName('station-model')[0];
    },
    init (params) {
      this.initData();
      this.queryParams = Object.assign(this.queryParams, params);
      if (!this.show('station:baseInfo') && this.show('station:configInfo')) {
        this.tab = '2';
      }
      if (!this.show('station:baseInfo') && !this.show('station:configInfo')) {
        this.showTable = false;
      }
      if (!this.isSelf && this.show('station:configInfo')) {
        this.tab = '2';
      }
      this.getTreeData({ psId: params.psId });
      this.getTableData();
      this.getDeviceModelList(this.queryParams.deviceType, this.queryParams.factoryId, this.queryParams.factoryId);
    },
    initData (psId) {
      this.visible = true;
      this.psId = psId;
      this.deviceType = '1';
      this.queryParams = {
        deviceType: '1',
        factoryId: undefined,
        modelId: undefined,
        id: undefined,
        currentPage: '1',
        pageSize: '10'
      };
      this.tab = '1';
    },
    getTableHeight () {
      this.$nextTick(() => {
        if (document.getElementsByClassName('solar-eye-search-content') && document.getElementsByClassName('solar-eye-search-content')[0]) {
          this.tableHeight = document.body.offsetHeight - document.getElementsByClassName('solar-eye-search-content')[0].offsetHeight - 266;
        }
      });
    },
    onClose () {
      this.visible = false;
      this.$emit('refresh');
    },
    getTreeData (obj) {
      getDeviceTypeTree(obj).then((res) => {
        this.treeData = res.result_data.filter(item => {
          return !(item.pid == '0' && item.deviceType == '5');
        });
        this.treeData = this.arrayToTree(this.treeData, '0');
        this.selectedKeys = [this.treeData[0].key];
      });
    },
    //  高性能数组转树 此方法利用引用类型的内存 需要给出初始pid
    arrayToTree (items, minPid) {
      const result = []; // 存放结果集
      const itemMap = {}; //
      for (const item of items) {
        const id = item.id;
        const pid = item.pid;

        if (!itemMap[id] || !items.find(ele => ele.id == pid)) {
          itemMap[id] = {
            children: []
          };
        }
        itemMap[id] = {
          ...item,
          title: item.deviceName,
          key: item.psKey,
          children: itemMap[id]['children']
        };

        const treeItem = itemMap[id];

        if (pid === minPid) {
          result.push(treeItem);
        } else {
          if (!itemMap[pid] || !items.find(ele => ele.id == pid)) {
            itemMap[pid] = {
              children: []
            };
          }
          itemMap[pid].children.push(treeItem);
        }
      }
      return result;
    },
    onClick (val, event) {
      this.selectedKeys = [event.node.dataRef.psKey];
      this.deviceType = event.node.dataRef.deviceType;
      this.psKey = event.node.dataRef.psKey;
      this.queryParams.deviceType = this.deviceType == '4' ? '4' : '1';
      this.queryParams.id = event.node.dataRef.id;
      this.tab = '1';
      if (!this.isSelf && this.show('station:configInfo')) {
        this.tab = '2';
      }
      this.pageChange();
    },
    // 生产厂商
    deviceTypeChange () {
      this.queryParams.factoryId = undefined;
      this.queryParams.modelId = undefined;
      this.getManufacturersList(this.queryParams.deviceType);
      this.getDeviceModelList(this.queryParams.deviceType, this.queryParams.factoryId, this.queryParams.factoryId);
      this.deviceModelList = [];
    },
    manufacturersChange (val) {
      this.queryParams.modelId = undefined;
      this.getDeviceModelList(this.queryParams.deviceType, val, val);
    },
    // 表格分页选项变化事件
    sizeChange (current, pageSize) {
      this.queryParams.currentPage = current;
      this.queryParams.pageSize = pageSize;
      this.getTableData();
    },
    pageChange () {
      this.queryParams.currentPage = 1;
      this.getTableData();
    },
    async getTableData () {
      this.loading = true;
      this.queryDeviceType = this.queryParams.deviceType;
      this.selection = [];
      this.$refs.baseTable && await this.$refs.baseTable.clearAll();
      this.$refs.configTable && await this.$refs.configTable.clearAll();
      if (this.tab == '1') {
        let res1 = await getManufacturersList({ deviceType: this.queryParams.deviceType }).catch(() => { this.loading = false; }); ;
        res1.result_data && (this.manufacturersList = res1.result_data);
        selectAttributeConfigsByPid(this.queryParams).then(res => {
          if (res.result_code == '1') {
            this.baseData = res.result_data.pageList;
            this.baseData.forEach((item, index) => {
              item.manufacturersList = this.manufacturersList;
              item.deviceModelList = this.deviceModelList;
              item.versionList = this.versionList;
              let arrName = this.queryParams.deviceType == '1' ? 'inverterArr' : 'boxArr';
              this[arrName].forEach(ele => {
                item[ele + 'Valid'] = true;
              });
              item.diffManufacturers = false;
              item.diffDeviceModel = false;
              item.diffVersion = false;
              item.index = index;
              item.isChange = false;
            });
            this.total = res.result_data.rowCount;
          }

          this.$refs.baseTable.refreshColumn();
          this.loading = false;
        }).catch(() => { this.loading = false; });
      } else {
        this.configData = [];
        this.columnList = [];
        selectConfigInfosByPid(this.queryParams).then(res => {
          if (res.result_code == '1') {
            this.configArr = res.result_data.pageList;
            this.total = res.result_data.rowCount;
            let maxLength = 0;
            let maxIndex = 0;
            this.configArr.forEach((item, index) => {
              if (item.length > maxLength) {
                maxLength = item.length;
                maxIndex = index;
              }
              item.index = index;
            });
            this.columnList = this.configArr[maxIndex].map(item => {
              return {
                field: item.key,
                title: item.name,
                isEnable: item.isEnable,
                isAdjust: item.isAdjust
              };
            });
            this.configArr.forEach((item, index) => {
              let obj = {};
              obj.deviceName = item[0].deviceName;
              obj.deviceType = item[0].deviceType;
              obj.isAdjust = item[0].isAdjust ? item[0].isAdjust == 1 : undefined;
              obj.index = index;
              this.columnList.forEach((ele, index2) => {
                let data = item.find(v => { return v.key == ele.field; });
                obj[ele.field] = data ? data.value == 1 : undefined;
                obj[ele.isAdjust] = data ? data.isAdjust == 1 : undefined;
                obj['isEnable' + index2] = item[index2].isEnable;
              });
              this.configData.push(obj);
            });
            this.$refs.configTable.refreshColumn();
          }
          this.loading = false;
        }).catch(() => { this.loading = false; });
      }
    },
    selectChange (val, row, index, prop, diffProp, setFun, option) {
      let data = row.deviceModelList.filter(item => item.id === val);
      if (data && data.length) {
        row.model = data[0].data;
      }
      if (this.selection.length > 0) {
        let arr = this.baseData.filter(item => {
          return this.selection.includes(item.index) && item.index != index;
        });
        let flag = false;
        arr.forEach(item => {
          if (item[prop + 'Id'] != row[prop + 'Id']) {
            this.baseData[item.index][diffProp] = true;
            flag = true;
          }
        });
        if (flag) {
          this.$confirm({
            title: '提示',
            content: '所选数据不一致，是否修改',
            okText: '确定',
            cancelText: '取消',
            onOk: () => {
              this[setFun](val, index, prop, option);
            },
            onCancel: () => {
              this.baseData[index][prop] = row[prop + 'Id'] * 1;
              arr.forEach(item => {
                this.baseData[item.index][diffProp] = false;
              });
              this.$forceUpdate();
            }
          });
        } else {
          this[setFun](val, index, prop, option);
        }
      } else {
        this[setFun](val, index, prop, option);
      }
    },
    setManufacturersData (val, index, prop) {
      this.baseData[index].model = undefined;
      this.baseData[index].modelId = undefined;
      this.baseData[index].version = undefined;
      this.baseData[index].versionId = undefined;
      this.baseData[index].factoryId = val;
      this.baseData[index].isChange = true;
      this.baseData[index].versionList = [];
      this.baseData[index].initVersionlList = true;
      getDeviceModel({ deviceType: this.queryParams.deviceType, id: val == '--' ? '' : val, factoryId: val == '--' ? '' : val }).then(res => {
        this.baseData[index].deviceModelList = res.result_data.deviceModel;
        this.setTableData(index, 'diffManufacturers', prop);
      });
    },
    deviceModelFocus (row, index) {
      if (!row.initDeviceModelList) {
        getDeviceModel({ deviceType: this.queryParams.deviceType, id: row.factoryId == '--' ? '' : row.factoryId, factoryId: row.factoryId == '--' ? '' : row.factoryId }).then(res => {
          this.baseData[index].deviceModelList = res.result_data.deviceModel;
          row.initDeviceModelList = true;
          this.$forceUpdate();
        });
      }
    },
    setDeviceModelData (val, index, prop) {
      this.baseData[index].version = undefined;
      this.baseData[index].versionId = undefined;
      this.baseData[index].modelId = val;
      this.baseData[index].isChange = true;
      this.baseData[index].initVersionlList = true;
      this.setTableData(index, 'diffDeviceModel', prop);
    },
    setVerionData (val, index, prop, option) {
      this.baseData[index].communicateVersion = option.data.key;
      this.baseData[index].versionId = val;
      this.baseData[index].isChange = true;
      getDeviceModel({ deviceType: this.queryParams.deviceType, id: this.baseData[index].factoryId == '--' ? '' : this.baseData[index].factoryId, factoryId: this.baseData[index].factoryId == '--' ? '' : this.baseData[index].factoryId }).then(res => {
        this.baseData[index].deviceModelList = res.result_data.deviceModel;
        this.setTableData(index, 'diffVersion', prop);
      });
    },
    setTableData (index, diffProp, prop) {
      this.baseData[index].updateUser = this.userInfo.realname;
      this.baseData[index].updateTime = new Date();
      this.baseData[index][prop + 'Valid'] = true;
      this.selection.forEach(key => {
        this.boxArr.forEach(item => {
          this.baseData[key][item] = this.baseData[index][item + 'Id'] ? this.baseData[index][item + 'Id'] * 1 : undefined;
          this.baseData[key][item + 'Id'] = this.baseData[index][item + 'Id'] ? this.baseData[index][item + 'Id'] * 1 : undefined;
        });
        this.baseData[key].communicateVersion = this.baseData[index].communicateVersion;
        this.baseData[key].updateUser = this.baseData[index].updateUser;
        this.baseData[key].updateTime = this.baseData[index].updateTime;
        this.baseData[key][diffProp] = false;
        this.baseData[key][prop + 'Valid'] = true;
        this.baseData[key].deviceModelList = this.baseData[index].deviceModelList;
        this.baseData[key].initDeviceModelList = true;
        this.baseData[key].versionList = this.baseData[index].versionList;
        this.baseData[key].initVersionlList = true;
        this.baseData[key].isChange = true;
      });

      this.$forceUpdate();
    },
    onSelectChange (val) {
      this.selection = val.records.map(item => {
        return item.index;
      });
    },
    powerChange (val, index, field) {
      this.baseData[index].isChange = true;
      this.selection.forEach(key => {
        this.baseData[key][field] = val;
        this.baseData[key].isChange = true;
      });
      if (val) {
        this.baseData[index][field + 'Valid'] = true;
      }
      this.baseData[index].updateUser = this.userInfo.realname;
      this.baseData[index].updateTime = new Date();
      this.selection.forEach(key => {
        this.baseData[key].updateUser = this.baseData[index].updateUser;
        this.baseData[key].updateTime = this.baseData[index].updateTime;
        if (val) {
          this.baseData[key][field + 'Valid'] = true;
        }
      });
    },
    stringChange (val, index, field) {
      this.selection.forEach(key => {
        if (this.configData[key][field]) {
          this.configData[key][field] = val;
          this.configData[key].isChange = true;
        }
      });
      let arr = Array.from(new Set([...this.selection, index]));
      arr.forEach(key => {
        this.configArr[key].forEach(item => {
          if (item.key == field) {
            item.value = val;
          }
        });
      });
    },
    saveData () {
      if (this.tab == '1') {
        let flag = false;
        this.baseData.forEach(item => {
          let arrName = this.queryParams.deviceType == '1' ? 'inverterArr' : 'boxArr';
          this[arrName].forEach(ele => {
            if (!item[ele]) {
              item[ele + 'Valid'] = false;
              flag = true;
            }
          });
        });
        this.$forceUpdate();
        if (flag) {
          return false;
        }
        let arr = this.baseData.filter(item => {
          return item.isChange;
        });
        this.loading = true;
        realTimeUpdate({ dcDevices: arr, deviceType: this.queryDeviceType, psId: this.queryParams.psId }).then(res => {
          if (res.result_code == '1') {
            this.$message.success('保存成功！');
          }
          this.loading = false;
        }).catch(() => { this.loading = false; });
      } else {
        this.loading = true;
        let params = JSON.parse(JSON.stringify(this.configArr));
        params.map((item, index) => {
          item.map(child => {
            if (typeof child.value == 'boolean') {
              child.value = child.value ? 1 : 0;
            }
            child.isAdjust = this.configData[index].isAdjust ? 1 : 0;
          });
        });
        updateConfigs({ dcConfigDtos: params, psId: this.queryParams.psId }).then(res => {
          if (res.result_code == '1') {
            this.$message.success('保存成功！');
          }
          this.loading = false;
        }).catch(() => { this.loading = false; });
      }
    },
    tabChange () {
      this.pageChange();
    },
    getSaveBtnDisabled () {
      if ((this.tab == '1' && this.baseData.length == 0) || (this.tab == '2' && this.configData.length == 0)) {
        return true;
      } else {
        return false;
      }
    },
    // 根据权限标识和外部指示状态进行权限判断
    show (perms) {
      return this.showHandle(perms);
    }
  }
};
</script>
<style lang="less" scoped>
:deep(.ant-spin-container) {
  height: 100%;
}
:deep(.ant-drawer-body) {
  height: calc(100% - 55px);
  padding: 0 12px;
}
.left-content {
  height: 100%;
  padding: 24px 0;
  border-right: 1px solid #eee;
  .left-tree{
    height: 100%;
    overflow: auto;

  }
}
.right-content {
  height: 100%;
  padding: 2px 16px 24px !important;
  .right-btn {
    position: absolute;
    right: 0;
  }
}
.solar-eye-main-content .operation-btn {
  justify-content: flex-start;
}
:deep(.invalid .ant-select-selection ) {
  border-color: red !important;
  svg {
    color: red;
  }
}
:deep(.vxe-cell > .invalid.ant-input-number) {
  border: 1px solid red;
}

</style>
