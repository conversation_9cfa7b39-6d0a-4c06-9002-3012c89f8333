<!-- 员工列表 -->
<template>
  <div id="personalRecord">
    <a-spin :spinning="pageloading" :tip="pageloading && syncBtn ? '数据同步中，请耐心等待':''">
      <a-row :gutter="24">
        <a-col :xs="24" :lg="doubleRight ? 4 : 0">
          <funnel v-show="doubleRight" v-model="params.depId" :isDeptTree="true" @listenChange="getChild" :all="false" :isproject="false" />
        </a-col>
        <a-col :xs="24" :lg="doubleRight ? 20 : 24">
          <div id="employee_list" class="solar-eye-search-model">
            <a-row :gutter="24" class="solar-eye-search-content">
              <a-col :span="24">
                <div class="search-item">
                  <div style="height: unset;">
                    <a-checkbox v-model="checkedAll" @change="checkedAllClick">全部({{ countInfo.all }})</a-checkbox>
                    <a-checkbox-group v-model="allStatusList" @change="statusClick">
                      <a-checkbox value="3">试用人员({{ countInfo.onTrial }})</a-checkbox>
                      <a-checkbox value="1">正式人员({{ countInfo.formal }})</a-checkbox>
                      <a-checkbox v-if="showResignation" value="2">离职人员({{ countInfo.quit }})</a-checkbox>
                      <a-checkbox value="4">证书即将到期人员({{ countInfo.certificateStart }})</a-checkbox>
                      <a-checkbox value="5">证书过期人员({{ countInfo.certificateEnd }})</a-checkbox>
                    </a-checkbox-group>
                  </div>
                </div>
              </a-col>
              <a-col :xxl="5" :xl="8" :md="12">
                <div class="search-item">
                  <span class="search-label">所属部门</span>
                  <funnel-select @on-change="showOrHide" @listenChange="getChild" v-model="params.depId" :isproject="false" :isDeptTree="true" :show="doubleRight" style="width: 100%;" />
                </div>
              </a-col>
              <a-col :xxl="4" :xl="8" :md="12">
                <div class="search-item">
                  <span class="search-label">姓名</span>
                  <a-input v-model="params.empName" @blur="params.empName = $trim($event)" allowClear size="default"
                    placeholder="请输入姓名" style="width: 100%;"></a-input>
                </div>
              </a-col>
              <a-col :xxl="4" :xl="8" :md="12">
                <div class="search-item">
                  <span class="search-label">员工类型</span>
                  <a-select v-model="params.property" mode="multiple" allowClear :maxTagCount='1'
                    :maxTagsearch-labelLength="2" placeholder="请选择" size="default" style="width: 100%;">
                    <a-select-option v-for="item in dictMap.property" :key="item.dataValue" :value="item.dataValue">
                      {{item.dataLable}}
                    </a-select-option>
                  </a-select>
                </div>
              </a-col>
              <span v-show="toggleSearchStatus">
                <!-- <a-col :xxl="6" :xl="8" :md="12">
                  <div class="search-item">
                    <span class="search-label">证书名称</span>
                    <a-select v-model="params.certificateName" mode='multiple' :maxTagCount="1" placeholder="请选择"
                      allowClear style="width: 100%;">
                      <a-select-option v-for="item in dictMap.certificate" :key="item.dataValue"
                        :value="item.dataValue">{{item.dataLable}}</a-select-option>
                    </a-select>
                  </div>
                </a-col> -->
                <a-col :xxl="4" :xl="8" :md="12">
                  <div class="search-item">
                    <span class="search-label">职位</span>
                    <a-select v-model="params.position" option-filter-prop="children" :filter-option="filterOption"
                      mode='multiple' :maxTagCount="1" placeholder="请选择" allowClear style="width: 100%;">
                      <a-select-option v-for="item in dictMap.position" :key="item.dataValue" :value="item.dataValue">
                        {{item.dataLable}}
                      </a-select-option>
                    </a-select>
                  </div>
                </a-col>
                <a-col :xxl="6" :xl="8" :md="12">
                  <div class="search-item">
                    <span class="search-label">入职时间</span>
                    <a-range-picker v-model="startDate" :placeholder="['选择日期', '选择日期']" format="YYYY-MM-DD"
                      value-format="YYYY-MM-DD" style="width: 100%;" />
                  </div>
                </a-col>
              </span>
              <a-col :xxl="4" :xl="6" :md="8">
                <div class="search-item">
                  <throttle-button label="查询" @click="pageChange(1)" />
                  <span class="com-color" @click="handleToggleSearch">
                    {{ toggleSearchStatus ? "收起" : "展开" }}
                    <a-icon :type="toggleSearchStatus ? 'up' : 'down'" />
                  </span>
                </div>
              </a-col>
            </a-row>
          </div>

          <div class="solar-eye-gap"></div>
          <a-col class="solar-eye-main-content">
            <!-- 操作按钮 -->
            <div class="operation" style="height: 32px">
              <div class="operation-btn">
                <a-dropdown>
                  <a class="ant-dropdown-link" @click="e => e.preventDefault()">筛选列
                    <a-icon type="form" />
                  </a>
                  <a-menu slot="overlay">
                    <a-checkbox-group v-model="checkColumn" @change="refreshTableColumn">
                      <a-checkbox class="drop-down" v-for="column in columns" :key="column.field" :value="column.field">
                        {{column.title}}
                      </a-checkbox>
                    </a-checkbox-group>
                  </a-menu>
                </a-dropdown>
                <erp-button label="新增" perms="920107101102" size="default" @click="add"></erp-button>
                <erp-button label="删除" perms="920107101104" size="default" :disabled="!multipleTable.length"
                  @click="delEmpInfo">
                </erp-button>
                <erp-button label="薪资导入" perms="920107101111" size="default" @click="handleImportPay"></erp-button>
                <erp-button label="薪资导出" perms="920107101112" size="default" @click="payExportModel = true">
                </erp-button>
                <erp-button label="薪资模板" perms="920107101113" size="default" @click="downloadPayTemplate"></erp-button>
                <erp-button label="导入" perms="920107101107" size="default" @click="handleImport"></erp-button>
                <erp-button label="导出" perms="920107101105" size="default" @click="exportFaultily"></erp-button>
                <erp-button label="导出详情" perms="920107101110" size="default" @click="exportDetail"></erp-button>
                <erp-button label="组织关系" perms="920107101115" size="default" @click="()=>shipVisible = true"></erp-button>
                <erp-button label="模板下载" perms="920107101108" size="default" @click="downloadExcel"></erp-button>
                <a-button size="default" v-has="'person:syncBtn'" :loading="syncBtn" @click="getSyncAll">同步数据</a-button>
              </div>
            </div>
            <vxe-table ref="vxeTable" :data="data" :height="tableHeight - 24" resizable align="center" border
              show-overflow highlight-hover-row size="small" @sort-change="sortChange" @checkbox-all="selectionChange"
              @checkbox-change="selectionChange" :seq-config="{ startIndex:(params.curPage - 1) * params.size }">
              <vxe-table-column type="checkbox" fixed="left" width="60"></vxe-table-column>
              <vxe-table-column title="序号" type="seq" width="80" align="center"></vxe-table-column>
              <vxe-table-column v-for="info in columns" :key="info.field" :title="info.title" :field="info.field"
                :sortable="getSortable(info.field)" show-overflow="title" min-width="160"
                :visible="checkColumn.includes(info.field)">
                <template v-slot="{ row }">
                  <span>{{getLabel(row[info.field], dictMap[info.dict])}}</span>
                </template>
              </vxe-table-column>
              <vxe-table-column :visible="showHandle(perms)" field="action" title="操作" fixed="right" width="160"
                :resizable="false" class-name="fixed-right-column-160">
                <template v-slot="{ row }">
                  <erp-button title="编辑" perms="920107101103" size="default" type="primary" icon="edit"
                    @click="editInfo(row)"></erp-button>
                  <erp-button title="详情" perms="920107101106" size="default" icon="file-text" @click="detail(row)">
                  </erp-button>
                  <erp-button title="调动轨迹" perms="920107101116" size="default" icon="interaction"
                    @click="transferTrajectory(row)"></erp-button>
                </template>
              </vxe-table-column>
              <template v-slot:empty>
                <span>查询无数据</span>
              </template>
            </vxe-table>
            <page-pagination :pageSize="params.size" :current="params.curPage" :total="total"
              @size-change="sizeChange" />
          </a-col>
        </a-col>
      </a-row>
      <!-- 导入 -->
      <upload v-model="upload.open" :upload="upload" @fileUpload="fileUpload" />

      <a-modal title="薪资导出" :visible="payExportModel" @ok="exportPay" :confirm-loading="pageloading"
        :maskClosable="false" @cancel="payExportModel = false" okText="确定" cancelText="取消">
        <a-range-picker :placeholder="['起始月份', '结束月份']" style="width: 100%" format="YYYY-MM" :allowClear="false"
          :value="psyMonthRange" :mode="['month', 'month']" @panelChange="changeExportMonth" />
      </a-modal>
      <a-modal v-model="shipVisible" title="时间选择" @cancel="()=> ym =''" >
      <span>*时间不选时默认导出当前在职人员</span>
      <div style="margin-top: 16px"><span>年月</span> <a-month-picker v-model="ym" :allowClear="true" format="YYYY-MM" valueFormat="YYYY-MM" /></div>
        <template slot="footer">
        <a-button type="primary" :loading="loading" @click="exportOrgRelation">
          导出
        </a-button>
        </template>
    </a-modal>
      <!-- 员工表单 -->
      <drawer-view ref="personalRecordForm" @cancel="closeMpdel" parent-id="personalRecord" />
    </a-spin>
  </div>
</template>
<script>
import moment from 'moment';
import { getSyncAll } from '@/api/api';
import initDict from '@/mixins/initDict';
import { LeftMixin } from '@/mixins/LeftMixin';
import upload from '@/components/erp/upload/upload';
import tableHeight from '@/mixins/tableHeightAndSearchModel';
import { getEmployeeList, getEmployeeCount, deleteEmployee, exportEmployeeList, downloadExcleTemplate, exportEmployeeBaseInfoList, downloadSalaryListTemplate, exportSalaryList, exportOrgRelation, checkUserIsHROrEngineer } from '@/api/isolarErp/employee/employee';
const count = { 'all': 0, 'onTrial': 0, 'formal': 0, 'quit': 0, 'certificateStart': 0, 'certificateEnd': 0 };
export default {
  name: 'index',
  mixins: [initDict, LeftMixin, tableHeight],
  components: {
    upload
  },
  data () {
    return {
      first: true,
      doubleRight: false,
      perms: '920107101103,920107101106',
      params: {
        curPage: 1,
        size: 10,
        depId: null,
        startTime: null,
        endTime: null,
        empName: '',
        certificateName: [],
        position: [],
        Sts: '',
        property: [],
        sortFiled: null, // 列表字段值
        sortKind: null // 排序 其中desc是降序，asc升序
      },
      paramsCopy: {},
      countInfo: count,
      checkColumn: ['empName', 'departName', 'empId', 'tel', 'position', 'workSts', 'property', 'sex', 'isleader',
        'createTime', 'updateTime'
      ], // 筛选列
      columns: [{
        field: 'empName',
        title: '姓名'
      },
      {
        field: 'departName',
        title: '所属部门'
      },
      {
        field: 'empId',
        title: '工号'
      },
      {
        field: 'tel',
        title: '联系方式'
      },
      {
        field: 'position',
        title: '职位',
        dict: 'position'
      },
      {
        field: 'workSts',
        title: '员工状态',
        dict: 'work_sts_emp'
      },
      {
        field: 'property',
        title: '员工类型',
        dict: 'property'
      },
      {
        field: 'sex',
        title: '性别',
        dict: 'sex'
      },
      {
        field: 'isleader',
        title: '是否是负责人',
        dict: 'yes_no_emp'
      },
      {
        field: 'createTime',
        title: '创建时间'
      },
      {
        field: 'updateTime',
        title: '修改时间'
      }
      ],
      // 人员状态多选框
      showResignation: false,
      statusdata: ['1', '3', '4', '5'],
      allStatusList: [],
      // 入职时间选择
      startDate: [],
      // 表格相关参数
      data: [],
      // 分页参数
      total: 0,
      // 选中的数据
      multipleTable: [],
      // 导入参数
      upload: {
        title: '',
        url: '',
        open: false
      },
      // 新增员工
      param: {
        type: null, // 1:新增 2:编辑 3:详情,
        title: null,
        account: null,
        open: false
      },
      firstLoad: false,
      payExportModel: false, // 薪资导出选择月份弹窗
      psyMonthRange: [moment().startOf('year'), moment()], // 薪资导出选择月份
      syncBtn: false,
      shipVisible: false,
      ym: ''
    };
  },
  created () {
    this.tabKind = '1';
    // 加载数据字典
    this.getDictMap('position,certificate,property,sex,yes_no_emp,work_sts_emp');
  },
  methods: {
    moment,
    // 判断是否显示排序
    getSortable (field) {
      if (field == 'createTime' || field == 'updateTime') {
        return 'custom';
      } else {
        return false;
      }
    },
    // 获取查看离职人员信息的权限
    initCheckUserRole () {
      checkUserIsHROrEngineer({}).then((res) => {
        this.firstLoad = true;
        let data = res.result_code == '1' ? res.result_data : false;
        this.showResignation = data;
        if (data && !this.statusdata.includes('2')) {
          this.statusdata.push('2');
        }
        this.allStatusList = this.defAll = this.statusdata;
        this.queryData();
      });
    },
    /*
          表格列刷新
        */
    refreshTableColumn () {
      this.$nextTick(() => {
        let $table = this.$refs.vxeTable;
        $table && $table.refreshColumn();
      });
    },
    // 查询表格列表数据
    async queryData () {
      let self = this;
      self.pageloading = true;
      let $table = this.$refs.vxeTable;
      if ($table) {
        await $table.clearScroll();
        $table.clearCheckboxRow(); // 清除选中
      }
      self.multipleTable = [];
      let params = Object.assign({}, this.params);
      let { selectId, startDate, allStatusList, showResignation, sortFiled, sortKind } = this;
      Object.assign(params, {
        'depId': selectId,
        'startTime': startDate[0] || null,
        'endTime': startDate[1] || null,
        'certificateName': params.certificateName.join(','),
        'position': params.position.join(','),
        'property': !params.property.length ? null : params.property,
        'Sts': allStatusList,
        'resignation': showResignation,
        'sortFiled': sortFiled,
        'sortKind': sortKind
      });
      this.paramsCopy = Object.assign({}, params);
      getEmployeeList(params).then((res) => {
        self.data = res.result_data.rows;
        self.total = res.result_data.total;
        self.pageloading = false;
      }).catch(() => {
        self.data = [];
        self.total = 0;
        self.pageloading = false;
      });
      self.countStsData(params);
    },
    // 统计各状态数据量
    countStsData (map) {
      let obj = Object.assign({}, map);
      obj.Sts = this.defAll;
      getEmployeeCount(obj).then((res) => {
        let data = Object.assign({}, count, res.result_data || {});
        data.all = Number(data.onTrial) + Number(data.formal);
        if (map.resignation) {
          data.all += Number(data.quit);
        }
        this.countInfo = data;
      }).catch(() => {
        this.countInfo = Object.assign({}, count);
      });
    },
    // 关闭弹窗
    closeMpdel () {
      this.param = {
        type: null, // 1:新增 2:编辑 3:详情,
        title: null,
        account: null,
        open: false
      };
      this.pageChange(1);
    },
    // 新增员工数据
    add () {
      this.param = {
        type: '1', // 1:新增 2:编辑 3:详情,
        title: '新增员工',
        account: null,
        open: true
      };
      this.$refs.personalRecordForm.init('1', this.param, '/isolarerp/employee/employeeList/empForm');
    },
    // 编辑
    editInfo (row) {
      this.param = {
        type: '2', // 1:新增 2:编辑 3:详情,
        title: '编辑',
        account: row.userAccount,
        open: true,
        isgId: row.isgId ? row.isgId : ''
      };
      this.$refs.personalRecordForm.init('2', this.param, '/isolarerp/employee/employeeList/empForm');
    },
    // 删除
    delEmpInfo () {
      let self = this;
      self.$confirm({
        title: '确定要删除吗?',
        okText: '确定',
        cancelText: '取消',
        onOk () {
          let ids = [];
          self.multipleTable.forEach((column, index) => {
            ids.push(column.empId);
          });
          let requestMap = {
            ids: ids
          };
          deleteEmployee(requestMap).then((res) => {
            self.$message.success(res.result_msg);
            self.pageChange(1);
          });
        }
      });
    },

    // 详情
    detail (row) {
      this.param = {
        type: '3', // 1:新增 2:编辑 3:详情,
        title: '员工详情',
        account: row.userAccount,
        open: true
      };
      this.$refs.personalRecordForm.init('3', this.param, '/isolarerp/employee/employeeList/empForm');
    },
    // 调动轨迹
    transferTrajectory (row) {
      this.$refs.personalRecordForm.init('3', row, '/isolarerp/employee/employeeList/trajectory');
    },
    afterClose () {
      this.param = {
        type: null, // 1:新增 2:编辑 3:详情,
        title: '',
        account: null,
        open: true
      };
    },
    // 薪资导入
    handleImportPay () {
      this.upload = {
        title: '导入薪资',
        url: '/employeeManager/v1/importSaraly',
        open: true
      };
    },
    // 薪资模板下载
    downloadPayTemplate () {
      let self = this;
      self.pageloading = true;
      downloadSalaryListTemplate({}).then((res) => {
        self.$downloadFile(res.result_data);
        self.pageloading = false;
      }).catch((e) => {
        self.pageloading = false;
      });
    },
    changeExportMonth (val) {
      this.psyMonthRange = val;
    },
    // 薪资导出
    exportPay () {
      let self = this;
      self.pageloading = true;
      let { selectName, psyMonthRange, paramsCopy } = this;
      let requestMap = Object.assign({
        'name': selectName,
        'timeBegain': moment(psyMonthRange[0]).format('YYYY-MM') + '-01',
        'timeEnd': moment(psyMonthRange[1]).format('YYYY-MM') + '-01'
      }, paramsCopy);
      exportSalaryList(requestMap).then((res) => {
        self.$downloadFile(res.result_data);
        self.payExportModel = false;
        self.pageloading = false;
      }).catch((e) => {
        self.pageloading = false;
      });
    },
    // 导入
    handleImport () {
      this.upload = {
        title: '员工信息导入',
        url: '/employeeManager/v1/importEmployeeInfo',
        open: true
      };
    },
    // 导出
    exportFaultily () {
      let self = this;
      self.pageloading = true;
      let requestMap = Object.assign({}, this.paramsCopy);
      requestMap.name = self.selectName;
      exportEmployeeList(requestMap).then((res) => {
        self.$downloadFile(res.result_data);
        self.pageloading = false;
      }).catch((e) => {
        self.pageloading = false;
      });
    },
    // 导出详情
    exportDetail () {
      let self = this;
      self.pageloading = true;
      exportEmployeeBaseInfoList(this.paramsCopy).then((res) => {
        self.$downloadFile(res.result_data);
        self.pageloading = false;
      }).catch((e) => {
        self.pageloading = false;
      });
    },
    // 组织关系
    exportOrgRelation () {
      let self = this;
      this.shipVisible = false;
      self.pageloading = true;
      let requestMap = {
        'depId': self.paramsCopy.depId,
        ym: self.ym
      };
      this.ym = '';
      exportOrgRelation(requestMap).then((res) => {
        self.$downloadFile(res.result_data);
        self.pageloading = false;
      }).catch((e) => {
        self.pageloading = false;
      });
    },
    // 模板下载
    downloadExcel () {
      let self = this;
      self.pageloading = true;
      downloadExcleTemplate({}).then((res) => {
        self.$downloadFile(res.result_data);
        self.pageloading = false;
      }).catch((e) => {
        self.pageloading = false;
      });
    },
    // 行选中
    selectionChange (val) {
      this.multipleTable = val.records;
    },
    // 分页事件
    pageChange (page) {
      this.params.curPage = page;
      if (this.first) {
        this.first = false;
        this.initCheckUserRole();
      } else {
        this.queryData();
      }
    },
    // 分页事件
    sizeChange (current, size) {
      Object.assign(this.params, {
        'curPage': current,
        'size': size
      });
      this.queryData();
    },
    // 文件上传
    fileUpload (result, map) {
      let self = this;
      self.upload.open = false;
      // 导入失败，提示是否下载错误信息
      if (result.status == '0') {
        self.$confirm({
          title: result.msg,
          okText: '确定',
          cancelText: '取消',
          centered: true,
          onOk () {
            self.$downloadFile(result.file.result_data);
          }
        });
      } else if (result.status == '2') {
        // 覆盖信息
        let repeatEmpIds = result.data.repeatEmpId.join(',');
        self.$confirm({
          title: result.msg,
          content: (h) => <div style = "color:red;">{ repeatEmpIds }</div>,
          onOk () {
            importExl(result.data).then((res) => {
              if (res.result_data.status == '0') {
                self.$confirm({
                  title: res.result_data.msg,
                  okText: '确定',
                  cancelText: '取消',
                  centered: true,
                  onOk () {
                    self.$downloadFile(res.result_data.file.result_data);
                  }
                });
              } else {
                self.$message.success(res.result_data.msg);
                self.pageChange(1);
              }
            });
          }
        });
      } else {
        self.$message.success(result.msg);
        self.pageChange(1);
      }
    },
    /** modify by mmzuo
       *  同步第三方数据
       *
       */
    getSyncAll () {
      this.pageloading = true;
      this.syncBtn = true;
      getSyncAll().then(res => {
        this.syncBtn = false;
        this.pageloading = false;
        this.params.curPage = 1;
        this.queryData();
        this.$message.success('数据已从第三方平台同步成功');
      }).catch(() => {
        this.syncBtn = false;
        this.pageloading = false;
      });
    },
    filterOption (input, option) {
      return (
        option.componentOptions.children[0].text.toUpperCase().indexOf(input.toUpperCase()) >= 0
      );
    }
  }
};
</script>
