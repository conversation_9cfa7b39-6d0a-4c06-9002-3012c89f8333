<template>
  <div class="less-then-100 ant-form-explain" v-if='showMsg'>
    <a-icon type="exclamation-circle" />&nbsp;
    <span>{{msg}}</span>
  </div>
</template>

<script>
import PropTypes from 'ant-design-vue/es/_util/vue-types';
export default {
  name: 'OrderRealCostCheck',
  props: {
    count: PropTypes.number,
    msg: PropTypes.string.def('当前单位为“元”，请确认金额准确填写'),
    minCount: PropTypes.number.def(100),
    disabled: PropTypes.number.def(false)
  },
  computed: {
    showMsg () {
      const { disabled, count, minCount } = this;
      return !!(count && count < minCount) && !disabled;
    }
  }
};
</script>

<style lang='less' scoped>
  .less-then-100{
    color: var(--zw-primary-color--default);
    position: absolute;
    width: max-content;
  }
</style>
