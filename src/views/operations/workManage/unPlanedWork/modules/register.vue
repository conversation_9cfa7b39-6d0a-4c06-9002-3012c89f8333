<!-- 任务基础表单 -->
<template>
  <div>
    <a-form-model :model="baseForm" :rules="base_rules" ref="unPlanedForm" :labelCol="{ style: 'width: 120px' }" :wrapperCol="{ style: 'width: calc(100% - 120px)' }">
      <a-row>
        <!-- 共有 -->
        <a-col :xl="8" :sm="12" :xs="24">
          <a-form-model-item label="电站名称" prop="psaId">
            <a-input v-if="order().processDefKey == 'activiti3273Workflow'" v-model="order().psaName" :disabled="disabled" style="width: 100%" />
            <psa-select v-else @change="psaIdChange" v-model="baseForm.psaId" :disabled="disabled"></psa-select>
          </a-form-model-item>
        </a-col>
        <a-col :xl="8" :sm="12" :xs="24">
          <a-form-model-item label="任务类型" prop="taskType">
            <a-select
              v-model="baseForm.taskType"
              :disabled="isDisabled || !baseForm.psaId"
              @change="changeType"
              placeholder="请选择"
              style="width: 100%">
              <a-select-option v-for="item in taskList" :key="item.dataValue" :value="item.dataValue">{{
                item.dataLable
              }}</a-select-option>
            </a-select>
          </a-form-model-item>
        </a-col>

        <a-col :xl="8" :sm="12" :xs="24" v-if="isShowSub">
          <a-form-model-item label="任务子类" prop="workSubclass">
            <a-select
              v-model="baseForm.workSubclass"
              :disabled="isDisabled"
              @change="changeSubTask"
              placeholder="请选择"
              style="width: 100%">
              <a-select-option
                v-for="item in subDict"
                :key="item.dataValue"
                :value="String(item.dataValue)">{{ item.dataLable }}</a-select-option>
            </a-select>
          </a-form-model-item>
        </a-col>

        <!--缺陷、故障-->
        <a-col :xl="8" :sm="12" :xs="24" v-if="isFault">
          <a-form-model-item label="工程遗留" prop="isEngLegacy">
            <a-radio-group v-model="baseForm.isEngLegacy" @change="changeIsEngLegacy" :disabled="isDisabled">
              <a-radio :value="1">是</a-radio>
              <a-radio :value="0">否</a-radio>
            </a-radio-group>
          </a-form-model-item>
        </a-col>
      </a-row>
    </a-form-model>

    <base-form v-if="!isFault && baseForm.taskType != '7'"
      ref="fillForm" :baseInfo="baseForm" :type="type" :options="options"></base-form>

    <faultForm v-if="(isFault && baseForm.isEngLegacy == '0') || baseForm.taskType == '7'"
      ref="fillForm" :baseInfo="baseForm" :type="type" :options="options"></faultForm>

    <historicalLegacy v-if="isFault && baseForm.isEngLegacy == '1'"
      ref="fillForm" :baseInfo="baseForm" :type="type" :options="options"></historicalLegacy>
  </div>
</template>

<script>
import moment from 'moment';
import initDict from '@/mixins/initDict';
import { getDeviecTypeTree } from '@/api/common_gy/common.js';
import baseForm from './form/baseForm';
import faultForm from './form/faultForm';
import historicalLegacy from './form/historicalLegacy';
import { getExperimentDate } from '@api/operations/digitalOrder';
import { USER_INFO } from '@/store/mutation-types';
import { getPsaModel } from '@/api/operations/digitalOrder';
import { getSubDict } from './column.js';
export default {
  name: 'taskInfo',
  mixins: [initDict],
  props: {
    task: {
      type: Object,
      default: null
    },
    disabled: {
      type: Boolean,
      default: false
    }
  },
  inject: ['type', 'order'],
  components: {
    baseForm,
    historicalLegacy,
    faultForm
  },
  data () {
    return {
      // 任务表单基础信息
      baseForm: {
        id: undefined,
        psaId: undefined, // 电站名称
        taskType: undefined, // 任务类型
        workSubclass: undefined, // 任务子类
        isEngLegacy: undefined, // 工程遗留
        jobCategory: undefined
      },
      isShowSub: false,
      subDict: [],
      options: [],
      base_rules: {
        psaId: [{
          required: true,
          message: '请选择电站'
        }], // 电站名称
        taskType: [{
          required: true,
          message: '请选择任务类型'
        }], // 任务类型
        workSubclass: [{
          required: true,
          message: '请选择任务子类',
          trigger: 'change'
        }],

        isEngLegacy: [{
          required: true,
          message: '请选择工程遗留',
          trigger: 'change'
        }]
      }
    };
  },
  mounted () {},
  computed: {
    isFault () {
      return ['8'].includes(this.baseForm.taskType);
    },
    taskList () {
      // eslint-disable-next-line vue/no-side-effects-in-computed-properties
      return this.dictMap.pdca_task_type.splice(1);
    },
    subTaskList () {
      return this.dictMap.pdca_sub_task_type.filter((item) => item.dataValue == '2');
    },
    isDisabled () {
      return !(['1'].includes(this.type()));
    }
  },
  watch: {
    'dictMap' () {
      if (this.dictMap.pdca_overhaul_category.length > 0) {
        this.dealDict(this.baseForm.taskType, true);
      }
    }
  },
  created () {
    this.getDictMap('pdca_task_type,pdca_sub_task_type,pdca_overhaul_category,pdca_sub_task_type_inspection,pdca_sub_task_type_clean,pdca_sub_task_type_grass');
  },
  methods: {
    moment,
    initData (detail, type) {
      for (let item in detail) {
        if (this.baseForm.hasOwnProperty(item)) {
          this.baseForm[item] = detail[item];
        }
      }
      if (type == 10) {
        if (['1', '2'].includes(this.baseForm.taskType)) {
          this.getStationModelList();
        }
        this.changeType(this.baseForm.taskType, true);
        if (this.baseForm.taskType == '1' && detail.workSubclass == '2') {
          this.baseForm = {
            ...this.baseForm,
            cleanCap: detail.cleanCap
          };
        }
      }

      this.$nextTick(() => {
        if (detail.orderSource == '6') {
          this.$refs.fillForm.getHandlePerson(detail.id);
        }
      });
    },
    initRegister (detail, type) {
      let validateForm = '';
      this.$nextTick(() => {
        validateForm = this.$refs.fillForm;
        if (detail && validateForm) {
          this.getDeviecTypeTree(detail.psaId, detail.taskType == '8');
        }
        validateForm.getDetail(detail, type);
        this.dealDict(this.baseForm.taskType, true);
      });
    },
    // 获取设备数
    getDeviecTypeTree (psaId, isDevice) {
      let params = {
        psId: psaId || this.baseForm.psaId
      };
      getDeviecTypeTree(params).then((res) => {
        this.options = this.formatData(res.result);
        if (isDevice) {
          this.options.unshift({
            ancestors: '0',
            children: [],
            deviceTypeLevel: 0,
            label: '非设备',
            name: '非设备',
            pid: 0,
            value: 0
          });
        }
      }).catch(() => {
        this.options = [];
      });
    },
    formatData (data) {
      for (var i = 0; i < data.length; i++) {
        if (data[i].children.length < 1) {
          data[i].children = undefined;
        } else {
          this.formatData(data[i].children);
        }
      }
      return data;
    },
    /*
        组织电站树change事件，电站类型=【32-屋顶（居民）】时，任务类型下拉列表只展示【巡检，清洗，工程维修】，其他电站还按照现有类型显示
        原： val, node
      */
    psaIdChange (val) {
      if (this.type() != 10) {
        for (let item in this.baseForm) {
          if (item != 'psaId') {
            this.baseForm[item] = undefined;
          }
        }
      }
    },
    getStationModelList () {
      getPsaModel({
        psaId: this.baseForm.psaId
      }).then(res => {
        let resData = res.result_data;
        let arr = [];
        for (let psMode in resData) {
          if (resData[psMode]) {
            arr.push(resData[psMode]);
          }
        }
        this.$refs.fillForm.baseForm.stationEnvironment = Array.from(new Set(arr));
      });
    },

    //     任务类型change事件
    //     设置校验规则
    changeType (value) {
      if (this.type() != 10) {
        this.baseForm.workSubclass = '';
        this.baseForm.isEngLegacy = '';
        this.dealDict(value);
      }
      this.$nextTick(() => {
        let fillForm = this.$refs.fillForm;
        if (['7'].includes(value) && fillForm) {
          this.setFindUser();
        } else {
          fillForm && fillForm.initForm(this.baseForm.psaId);
        }
        this.getDeviecTypeTree(this.baseForm.psaId, value == '8');
      });

      if (!['1', '2'].includes(value)) {
        this.$refs.fillForm.baseForm.stationEnvironment = '';
      } else {
        this.getStationModelList();
      }
    },
    dealDict (value, isInit) {
      if (!isInit) {
        this.baseForm.workSubclass = '';
      }

      this.isShowSub = ['1', '2', '3', '5', '10'].includes(value);
      this.subDict = getSubDict(value, this.dictMap);
    },
    setFindUser () {
      // 设置默认发现时间及发现人
      let user = Vue.ls.get(USER_INFO);
      let fillForm = this.$refs.fillForm;
      let baseForm = fillForm.baseForm;
      baseForm.findUserName = user.realname;
      baseForm.findUser = user.id;
      baseForm.findTime = moment().format('YYYY-MM-DD HH:mm');
    },
    changeIsEngLegacy () {
      this.$nextTick(() => {
        this.setFindUser();
      });
    },
    registerValidate (type) {
      return new Promise((resolve, reject) => {
        this.$refs.unPlanedForm.validate((valid) => {
          if (valid) {
            resolve(this.baseForm);
          } else {
            reject(new Error());
          }
        });
      }).catch((err) => {
        reject(err);
      });
    },
    getValidate (type) { // 0 保存 1 提交
      let validateForm;
      validateForm = this.$refs.fillForm.validateForm(type);
      return new Promise((resolve, reject) => {
        Promise.all([this.registerValidate(type), validateForm]).then((res) => {
          if (res[0] && res[1]) {
            let params = Object.assign({}, res[1]);
            params.stationEnvironment = Array.isArray(params.stationEnvironment) ? params.stationEnvironment.join(',') : '';
            resolve(Object.assign({}, res[0], params));
          } else {
            reject(new Error());
          }
        }).catch((err) => {
          reject(err);
        });
      });
    },
    clearValidate () {},
    changeSubTask (val) {
      if (this.baseForm.taskType == '3') {
        getExperimentDate({
          psaId: this.baseForm.psaId,
          workSubclass: val
        }).then((res) => {
          this.$refs.fillForm.baseForm.beforeExperimentDate = res.result_data;
        }).catch(() => {});
      } else if (this.baseForm.taskType == '10') {
        this.tangoWorkSubclassChange();
      }
    },
    tangoWorkSubclassChange () {
      this.$refs.fillForm.baseForm.liablePerson = '';
      this.$refs.fillForm.getAssignList(this.baseForm.psaId, undefined);
    }
  }
};
</script>

<style lang="less" scoped>
  .link-span {
    color: #ff8f33;
    text-decoration: underline;
    cursor: pointer;
  }
</style>
