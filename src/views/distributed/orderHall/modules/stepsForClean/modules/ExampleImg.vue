<template>
  <div class='example-img-box' @click='viewImage' :style='$attrs.imgStyle'>
    <img style='width: 76px;height: 56px;border-radius: 2px;' :src='`${baseUrl}/${name}`'/>
    <span class='example-text'>
     <svg width="13px" height="13px" viewBox="0 0 13 13" style='vertical-align: top'>
        <g id="页面-1" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
            <g id="待指派任务" transform="translate(-483.000000, -800.000000)" fill="#28A991" fill-rule="nonzero">
                <g id="编组-11" transform="translate(281.000000, 730.000000)">
                    <g id="编组-15" transform="translate(188.000000, 0.000000)">
                        <g id="编组-23" transform="translate(14.000000, 68.000000)">
                            <g id="编组-26" transform="translate(0.000000, 2.000000)">
                                <path d="M6.5,0 C2.91610294,0 0,2.91607786 0,6.5 C0,10.0843734 2.91562661,13 6.5,13 C10.0843734,13 13,10.0843734 13,6.5 C13,2.91610294 10.0843734,0 6.5,0 Z M9.63666201,5.42786426 L5.90260025,9.20248802 C5.9016727,9.20341557 5.8997925,9.20389189 5.89886493,9.20529577 C5.89746106,9.20622332 5.89746106,9.20810352 5.89605718,9.20903109 C5.86619973,9.2379359 5.82937303,9.25565985 5.7948527,9.27526398 C5.77760507,9.28506606 5.76361644,9.29998224 5.74541619,9.30697657 C5.68946166,9.32936339 5.63022307,9.34102058 5.57100955,9.34102058 C5.51131971,9.34102058 5.45115356,9.32936339 5.39472272,9.30604901 C5.37607122,9.29812713 5.36115503,9.28225831 5.34343108,9.27248132 C5.30891075,9.25290225 5.27301161,9.23562955 5.24315415,9.20624842 C5.2422266,9.20532086 5.24175028,9.20344067 5.24082272,9.2025131 C5.23989517,9.20110922 5.23801497,9.20110922 5.2370874,9.19970535 L3.40059084,7.31239346 C3.22104501,7.12770845 3.22523159,6.83251827 3.4099166,6.65297244 C3.59460161,6.47390293 3.88931548,6.47716193 4.06933762,6.6622982 L5.57424347,8.20871869 L8.97393184,4.77172731 C9.15488155,4.58844617 9.45054803,4.58659105 9.63335286,4.76799199 C9.81575659,4.94939295 9.81761171,5.24458312 9.63666201,5.42786426 Z" id="形状"></path>
                            </g>
                        </g>
                    </g>
                </g>
            </g>
        </g>
    </svg>
     查看示例
   </span>
  </div>
</template>

<script>
import VueViewer from '@/mixins/VueViewer';
const baseUrl = 'https://staticres.isolareye.com/clean/images';
export default {
  mixins: [VueViewer],
  props: {
    name: {
      type: String,
      default: () => ''
    }
  },
  data () {
    return {
      baseUrl
    };
  },
  mounted () {},
  methods: {
    viewImage () {
      this.viewerImage({ 'images': [{ path: this.baseUrl + '/' + this.name }] });
    }
  }
};
</script>

<style lang='less' scoped>
.example-img-box{
  width: 102px;
  height: 102px;
  box-sizing: border-box;
  padding: 9px;
  border: 1px dashed #d9d9d9;
  display: flex;
  flex-direction: column;
  justify-content: space-around;
  border-radius: 4px;
  cursor: pointer;
  align-items: center;
  .example-text{
    text-align: center;
    line-height: 12px;
    font-size: 12px;
  }
}
</style>
