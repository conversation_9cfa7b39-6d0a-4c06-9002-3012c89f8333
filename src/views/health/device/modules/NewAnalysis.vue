<template>
  <div class="drawer-form-com">
    <a-spin :spinning="loading" class="width-100 height-100">
      <div class="drawer-form-content">
        <DetailLayout class="m-t-12" :labelList="baseLabelList" :form="rowInfo" />
        <DetailLayout :labelList="diagnosisLabelList" :form="rowInfo" title="诊断信息" />
        <DetailLayout v-if="alarmRemark != '76'" :labelList="faultLabelList" :form="rowInfo" title="故障图片" />
        <template v-else>
          <div ref="pendWave_mappvm" class="voice-div"></div>
          <div ref="pendWave_mapstft" class="voice-div"></div>
          <audio controlsList="nodownload noplaybackrate" class="audio-div" v-if="rowInfo.voice" controls>
            <source :src="rowInfo.voice" type="audio/wav" />
          </audio>
        </template>
      </div>
    </a-spin>
  </div>
</template>
<script>
import WaveSurfer from 'wavesurfer.js';
import SpectrogramPlugin from 'wavesurfer.js/dist/plugin/wavesurfer.spectrogram';
import Colormap from 'colormap';
import { diagnosisDetail } from '@/api/health/AlarmEvents';

export default {
  name: 'NewAnalysis',
  data () {
    return {
      rowInfo: {},
      loading: false,
      searchTime: [],
      alarmRemark: '',
      audioContext: null,
      srcFile: null,
      srcPuth: null,
      sampleRateS: null,
      wavesurfer_map1: null,
      wavesurfer_map2: null,
      baseLabelList: [
        { label: '电站名称', key: 'psName' },
        { label: '设备类型', key: 'deviceTypeName' },
        { label: '设备名称', key: 'deviceName' },
        { label: '厂家名称', key: 'maker' },
        { label: '设备型号', key: 'deviceModel' }
      ],
      diagnosisLabelList: [
        { label: '诊断等级', key: 'alarmLevelName' },
        { label: '诊断类型', key: 'alarmRemarkName' },
        { label: '诊断原因', key: 'alarmReasonName' },
        { label: '最新发生时间', key: 'lastHappenTime' },
        { label: '发生次数', key: 'updateTimes' }
      ],
      faultLabelList: [{ key: 'pic', label: '故障截图', type: 'file:picture-card' }]
    };
  },
  mounted () {
    if (window.AudioContext || window.webkitAudioContext) {
      this.audioContext = new (window.AudioContext || window.webkitAudioContext)();
    } else {
      alert('您的浏览器不支持 Web Audio API');
    }
  },
  methods: {
    init (alarmRemark, row) {
      this.loading = true;
      this.$nextTick(async () => {
        const result = await diagnosisDetail({ id: row.id }).catch(() => (this.loading = false));
        if (alarmRemark == '76') {
          this.rowInfo = Object.assign({}, result, {
            voiceByte: result.media.voiceByte ? result.media.voiceByte[0] : '',
            voice: result.media.voice && result.media.voice[0] ? result.media.voice[0].path : ''
          });
        } else {
          this.rowInfo = Object.assign({}, result, result.media || {});
        }
        this.alarmRemark = alarmRemark;
        this.loading = false;
        if (alarmRemark == '76') this.setVoiceData(this.rowInfo);
      });
      return '详情';
    },
    // 声音文件解码
    fileAnalyze () {
      const reader = new FileReader();
      reader.readAsArrayBuffer(this.srcFile);
      reader.onload = (e) => {
        let intArr = new Int8Array(e.target.result);
        const sampleRateArr = intArr.slice(24, 28);
        const sampleRate =
          (sampleRateArr[0] & 0xff) |
          ((sampleRateArr[1] & 0xff) << 8) |
          ((sampleRateArr[2] & 0xff) << 16) |
          ((sampleRateArr[3] & 0xff) << 24);
        this.sampleRateS = sampleRate;
        this.audioContext.decodeAudioData(
          e.target.result,
          (buffer) => {
            this.drawingsWavstft();
          },
          (error) => {
            console.error('解码失败:', error);
          }
        );
      };
    },
    // 绘制声纹图
    drawingsWavstft () {
      const colors = Colormap({
        colormap: 'jet',
        nshades: 256,
        format: 'float'
      });
      this.offlineCtx = new OfflineAudioContext(1, this.sampleRateS * 1, this.sampleRateS);
      this.wavesurfer_map1 = WaveSurfer.create({
        container: this.$refs.pendWave_mappvm,
        waveColor: '#FF55C5',
        audioContext: this.offlineCtx,
        progressColor: '#00BFBF',
        height: 0,
        plugins: [
          SpectrogramPlugin.create({
            container: this.$refs.pendWave_mapstft,
            colorMap: colors,
            height: 256,
            frequencyMax: this.sampleRateS / 2,
            labels: true
          })
        ]
      });
      this.wavesurfer_map1.loadBlob(this.srcPuth);
    },
    // 组件创建前销毁 WaveSurfer 实例
    resbeforeDestroy () {
      if (this.wavesurfer_map1) {
        this.wavesurfer_map1.destroy();
        this.wavesurfer_map1 = null;
      }
      if (this.wavesurfer_map2) {
        this.wavesurfer_map2.destroy();
      }
    },
    setVoiceData (row) {
      let str = row.voiceByte;
      let bstr = atob(str);
      let n = bstr.length;
      let u8arr = new Uint8Array(n);
      while (n--) {
        u8arr[n] = bstr.charCodeAt(n);
      }
      this.srcFile = new File([u8arr], 'example.wav', { type: 'audio/wav' });
      const blob = new Blob([this.srcFile], { type: 'audio/wav' });
      this.srcPuth = blob;
      this.resbeforeDestroy();
      this.fileAnalyze();
    },
    reset () {
      this.rowInfo = {};
    }
  }
};
</script>
<style lang="less" scoped>
.search-head {
  padding: 16px 24px;
  color: var(--zw-text-1-color--default);

  .device-name {
    margin-right: 40px;
  }
}

.search-div {
  .search-label {
    margin-right: 10px;
    color: var(--zw-text-1-color--default);
  }

  .search-date {
    width: 225px;
  }
}

.img-div {
  width: 482px;
  height: 420px;
  border-radius: 4px;
  background: var(--zw-primary-common-light-color--default);
  padding: 16px 16px 24px;
  margin-left: 24px;
  color: var(--zw-text-1-color--default);
  font-size: 18px;
  line-height: 26px;
  cursor: pointer;

  .title {
    margin-bottom: 12px;
  }

  .content {
    height: calc(100% - 35px);
    width: 100%;
    background: var(--zw-primary-common-light-color--default);
    color: var(--zw-text-1-color--default);
    display: flex;
    align-items: center;
    justify-content: center;

    .normal-img {
      width: 100%;
      height: 100%;
    }

    .no-img {
      object-fit: contain;
      width: 120px;
    }
  }
}

.voice-div {
  padding: 0 24px;
}

.audio-div {
  margin: 24px;
}

audio::-webkit-media-controls-panel {
}

/* 隐藏控制条上的三个点图标 */
audio::-webkit-media-controls-overflow-button {
  display: none;
}

/* 隐藏声音图标 */
audio::-webkit-media-controls-context-menu-button {
  display: none !important;
}

.img-div:hover {
  box-shadow: 0px 4px 10px 0px var(--zw-primary-color--default);
}
</style>
