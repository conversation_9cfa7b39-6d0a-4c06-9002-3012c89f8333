<template>
  <div class="materials-management-page" id="drawerViewDetail">
    <left-menu ref="leftMenu" v-model="show_menu" @menu-change="menuChange" :isNeedClose="isNeedClose" />
    <div class="right-content" v-bind:style="{'width': show_menu ? 'calc(100%)' : 'calc(100% - 200px)',marginLeft: show_menu ?0:'16px'}">
      <a-tabs v-model="activeKey" v-if="!iframe_path" class="right-bar">
        <a-tab-pane key="1" tab="台账" force-render>
          <template v-if="page_component">
            <component ref="pageComponent" v-bind:is="page_component" :path="path"></component>
          </template>
        </a-tab-pane>
        <a-tab-pane key="2" tab="填报" :disabled="!has_add" force-render>
          <template v-if="form_component">
            <component parentId="drawerViewDetail" :isAdd='activeKey' ref="formComponent" v-bind:is="form_component" @cancel="refresh"></component>
          </template>
        </a-tab-pane>
      </a-tabs>
       <template v-else>
            <iframe-page-view v-if="!iframeView" :path="iframe_path" @close="closeCurrentMenu"/>
            <iframe-view v-if="iframeView" :path="iframe_path" @close="closeCurrentMenu" />
        </template>
    </div>
  </div>
</template>

<script>
import leftMenu from './modules/leftMenu';
import iframePageView from '@/components/layouts/IframePageView';
import iframeView from '@/components/layouts/IframeView';
export default {
  name: 'menuManage',
  components: {
    leftMenu,
    iframePageView,
    iframeView
  },
  watch: {
    show_menu () {
      this.$refs.pageComponent.refreshColumn();
    }
  },
  provide () {
    return {
      closeCurrentMenu: this.closeCurrentMenu
    };
  },
  data () {
    return {
      activeKey: '1',
      show_menu: false,
      path: null, // 台账路由
      page_component: null, // 台账页面路径
      has_add: false, // 是否有填报权限
      form_component: null, // 表单页面路径
      iframe_path: null,
      isNeedClose: false,
      iframeView: false
    };
  },
  computed () {

  },
  methods: {
    /*
        菜单变化事件
      */
    menuChange (menu) {
      this.isNeedClose = false;
      this.iframeView = !this.iframeView;
      if (menu) {
        this.iframe_path = null;
        if (menu.component === 'layouts/IframePageView' || menu.component === 'layouts/IframeView') {
          this.page_component = this.form_component = null;
          this.has_add = false;
          this.iframe_path = menu;
          if (menu.component === 'layouts/IframeView') {
            this.iframeView = true;
          } else {
            this.iframeView = false;
          }
        } else {
          this.page_component = () => import('@/views/' + menu.component);
          setTimeout(() => this.getPageComponent(menu), 100);
          this.path = menu.path;
          this.has_add = menu.has_add;
          if (menu.has_add && menu.form_component) {
            this.form_component = () => import('@/views' + menu.form_component);
            setTimeout(() => this.getFormComponent(menu), 100);
          } else {
            this.activeKey = '1';
          }
        }
      }
    },
    getPageComponent(menu) {
      if(!this.$refs.pageComponent) {
        this.page_component = () => import('@/views/' + menu.component);;
        setTimeout(() => this.getPageComponent(menu), 100);
      }
    },
    getFormComponent(menu) {
      if(!this.$refs.formComponent) {
        this.page_component = () => import('@/views' + menu.form_component);;
        setTimeout(() => this.getFormComponent(menu), 100);
      } else {
        this.initForm();
      }
    },
    /*
        初始化表单
      */
    initForm () {
      let self = this;
      let time = window.setInterval(function () {
        if (time && self.$refs.formComponent) {
          self.$refs.formComponent.init('1', null);
          window.clearInterval(time);
          time = null;
        }
      }, 50);
    },
    /*
        表单保存完后切换到列表页，并刷新数据
      */
    refresh (isTrue = false) { // true 停留在填报页面，其他切换到台账
      if (!isTrue) {
        this.activeKey = '1';
        this.initForm();
      }
      this.$refs.pageComponent.pageChange();
    },
    closeCurrentMenu () { // 打开新的标签，菜单默认选中上一个
      this.activeKey = '1';
      this.isNeedClose = true;
    }
  }
};
</script>

<style lang="less" scoped>
  .materials-management-page{
    width: 100%;
    height: 100%;
    position: relative;
    // padding-bottom: 12px;
    display: flex;
    .right-content{
      height: 100%;
      overflow: hidden;
      margin-left: 16px;
      :deep(.ant-tabs-line){
        height: 100%;
      }
      :deep(.ant-tabs-bar){
        margin: 0;
      }
      :deep(.ant-tabs-content){
        height: calc(100% - 44px);
      }
      :deep(.ant-tabs-tabpane){
        height: 100%;
        width: 100%;
      }
    }
  }
</style>
