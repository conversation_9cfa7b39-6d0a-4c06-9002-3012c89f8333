<!-- 部门人员 -->
<template>
  <div class="depart-emp-modal">
    <template v-if="selectedKey">
      <a-row :gutter="24" class="solar-eye-search-content">
        <a-col :xxl="6" :xl="8" :md="12">
          <div class="search-item">
            <span class="search-label">姓名</span>
            <a-input v-model="params.empName" @blur="params.empName = $trim($event)" size="default" allowClear></a-input>
          </div>
        </a-col>
        <a-col :xxl="4" :xl="8" :md="12">
          <div class="search-item">
            <throttle-button :disabled="loading" :loading="loading" label="查询" @click="searchClick"/>
          </div>
        </a-col>
      </a-row>
      <a-spin :spinning="loading">
        <!--表格渲染-->
        <vxe-table :data="empItems" :height="457" ref="empTable" align="center" border show-header-overflow show-overflow highlight-hover-row size="small"
         resizable :seq-config="{ startIndex: (params.curPage - 1) * params.size }">
          <vxe-table-column type="seq" width="80" title="序号"></vxe-table-column>
          <vxe-table-column show-overflow="title" v-for="item in columns" :key="item.field" min-width="120" :title="item.title">
            <template v-slot="{ row }">
              <span v-if="item.dict">{{getLabel(row[item.field], dictMap[item.dict])}}</span>
              <span v-else>{{getLabel(row[item.field],null)}}</span>
            </template>
          </vxe-table-column>
          <template v-slot:empty>
            <span>查询无数据</span>
          </template>
        </vxe-table>
        <!--分页组件-->
        <page-pagination :pageSize="params.size" :current="params.curPage" :total="total" @size-change="sizeChange"/>
      </a-spin>
    </template>
    <a-card v-else :bordered="false" style="height:200px">
      <a-empty>
        <span slot="description"> 请先选择一个部门! </span>
      </a-empty>
    </a-card>
  </div>
</template>

<script>
import { getEmployeeList } from '@/api/isolarErp/employee/employee';
export default {
  props: {
    selectedKey: {
      type: Object,
      default: null
    },
    dictMap: {
      type: Object,
      default: () => {
        return {};
      }
    }
  },
  data () {
    return {
      loading: false,
      columns: [
        { field: 'departName', title: '所属部门' }, { field: 'empName', title: '姓名' },
        { field: 'sex', title: '性别', dict: 'sex' }, { field: 'empId', title: '工号' },
        { field: 'tel', title: '联系方式' }, { field: 'position', title: '职位', dict: 'position' },
        { field: 'property', title: '员工类型', dict: 'property' }, { field: 'workSts', title: '员工状态', dict: 'work_sts_emp' },
        { field: 'isleader', title: '是否是负责人', dict: 'yes_no_emp' }
      ],
      empItems: [],
      params: {
        Sts: ['1', '3'],
        empName: undefined,
        depId: undefined,
        resignation: false,
        curPage: 1,
        size: 10
      },
      total: 0
    };
  },
  methods: {
    /*
        查询点击事件
      */
    searchClick () {
      this.params.curPage = 1;
      this.query();
    },
    /*
        初始化
      */
    init (record) {
      this.params.depId = record.id;
      this.query();
    },
    /*
        查询方法
      */
    async query () {
      this.loading = true;
      let $table = this.$refs.empTable;
      $table && await $table.clearScroll();
      getEmployeeList(this.params).then(res => {
        this.loading = false;
        this.empItems = res.result_data.rows;
        this.total = res.result_data.total;
      }).catch(() => {
        this.loading = false;
        this.empItems = [];
        this.total = 0;
      });
    },
    /*
        分页事件
      */
    sizeChange (p, e) {
      Object.assign(this.params, { curPage: p, size: e });
      this.query();
    }
  }
};
</script>

<style lang="less" scoped>
  .depart-emp-modal{
    width: 100%;
    padding-right: 12px;
  }
  .search-label{
    width: 80px;
  }
</style>
