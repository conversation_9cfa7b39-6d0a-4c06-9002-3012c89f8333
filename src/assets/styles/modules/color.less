// 颜色相关
// 不涉及主题切换的颜色样式
:root {
  // 基础颜色-全面不重复-不增不删
  --black: #000000;
  --black1: #222222;
  --black2: #24262B;
  --white: #FFFFFF;
  --white1: #FEFEFE;
  --white2: #FAFAFA;
  --white3: #F8F8F8;
  --white4: #F3F3F3;
  --white5: #F1F0F0;
  --white6: #FFF0ED;
  --white7: #F0F0F0;
  --white8: #F0F0F1;
  --white9: #F0F0EF;
  --gray: #FFF7E3;
  --gray1: #FFF4E2;
  --gray2: #E8E8E8;
  --gray3: #B7BCC4;
  --gray4: #DCDCDC;
  --gray5: #C5C5C5;
  --gray6: #CCCCCC;
  --gray7: #BDBDBD;
  --gray8: #EEEEEE;
  --gray9: #FFF2E5;
  --gray10: #FFE6CD;
  --gray11: #FDDDDD;
  --gray12: #9097A2;
  --gray13: #999999;
  --gray14: #656970;
  --gray15: #666666;
  --gray16: #474A52;
  --red: #422525;
  --red1: #572D2E;
  --red2: #761E15;
  --red3: #A53D33;
  --red4: #B53E37;
  --red5: #C55348;
  --red6: #D54941;
  --red7: #DF7066;
  --red8: #EA736D;
  --red9: #EEB6B3;
  --orange: #4A2C20;
  --orange1: #523124;
  --orange2: #803701;
  --orange3: #B0652D;
  --orange4: #CD6100;
  --orange5: #D57934;
  --orange6: #FF8100;
  --orange7: #E88E4B;
  --orange8: #FFA64A;
  --orange9: #FFBA73;
  --orange10: #FFB66B;
  --orange11: #FF9200;
  --yellow: #E1A500;
  --yellow1: #C18D00;
  --yellow2: #FFCA38;
  --yellow3: #F3DB99;
  --green: #21392B;
  --green1: #254131;
  --green2: #0F6433;
  --green3: #3E895E;
  --green4: #258C60;
  --green5: #2BA471;
  --green6: #68C89F;
  --green7: #53A275;
  --green8: #69B389;
  --green9: #AADBC6;
  --green10: #E3F9E9;
  --green11: #CEFAD8;
  --blue: #061934;
  --blue1: #142033;
  --blue2: #0D264A;
  --blue3: #122B51;
  --blue4: #143769;
  --blue5: #1C375E;
  --blue6: #133464;
  --blue7: #173460;
  --blue8: #1E3360;
  --blue9: #1E3555;
  --blue10: #202E4E;
  --blue11: #203D67;
  --blue12: #123363;
  --blue13: #2F4F7E;
  --blue14: #294976;
  --blue15: #123A73;
  --blue16: #1A427B;
  --blue17: #0A4778;
  --blue18: #1C355A;
  --blue19: #0B63A7;
  --blue20: #416FEB;
  --blue21: #3360D8;
  --blue22: #1057C9;
  --blue23: #1366EC;
  --blue24: #5E8EFF;
  --blue25: #5F9CFF;
  --blue26: #0090FF;
  --blue27: #33A6FF;
  --blue28: #6D7483;
  --blue29: #64748C;
  --blue30: #4F678A;
  --blue31: #6CBFFF;
  --blue32: #A1C2F7;
  --blue33: #F2F3FF;
  --blue34: #DEEBFF;
  --blue35: #0880ED;
  --blue36: #08A4ED;
  --purple: #8E56DD;
  --purple1: #E8DDF8;

  --zw-common-white-color--default: var(--white); // 部分区域使用的白色
  --zw-common-black-color--default: var(--black); // 一些遮罩场景使用（透明度0.4和0.6）
  --zw-common-other-purple-color--default: var(--purple); // 辅助色-其他-紫色
  --zw-common-other-purple-color--hover: var(--purple1); // 辅助色-其他-紫色hover
}

// 主题-浅色
:root[data-theme='light'] {
  --zw-primary-color--default: var(--orange6); // 主色
  --zw-primary-color--hover: var(--orange8); // 主色- hover状态时
  --zw-primary-color--active: var(--orange4); // 主色- active状态时
  --zw-primary-color--disable: var(--orange9); // 主色- disable状态时
  --zw-primary-partial-areas-color--hover: var(--gray9); // 主色-部分区域hover状态时的背景
  --zw-primary-common-light-color--default: var(--gray10); // 主色-常用浅色背景
  --zw-primary-common-dark-color--default: var(--orange10); // 主色-常用深色背景
  --zw-text-1-color--default: var(--black1); // 文字-一级
  --zw-text-2-color--default: var(--gray15); // 文字-二级
  --zw-text-3-color--default: var(--gray13); // 文字-三级
  --zw-text-color--disable: var(--gray7); // 文字-禁用
  --zw-text-color--default: var(--gray5); // 未选中的颜色
  --zw-text-reset-grey-color--default: var(--gray6); // 离线置灰
  --zw-border-color--default: var(--gray4); // 输入框等其他框类的边框线
  --zw-divider-color--default: var(--gray2); // 分割线
  --zw-primary-bg-color--default: var(--white7); // 整体页面背景
  --zw-primary-bg-color--hover: var(--white4); // hover状态时的背景色
  --zw-table-bg-color--hover: var(--white3); // 表格hover状态时的背景色
  --zw-small-area-bg-color--default: var(--white9); // 小区域使用的背景色
  --zw-table-header-bg-color--default: var(--white8); // 表格标题栏背景色
  --zw-card-light-bg-color--default: var(--white5); // 卡片背景上的浅灰色
  --zw-input-bg-color--disable: var(--gray8); // 输入框禁用时的背景色
  --zw-card-bg-color--default: var(--white1); // 页面卡片背景色
  --zw-conduct-color--normal: var(--blue23); // 辅助色-进行-normal
  --zw-conduct-color--hover: var(--blue25); // 辅助色-进行-hover
  --zw-conduct-color--active: var(--blue22); // 辅助色-进行-active
  --zw-conduct-color--disable: var(--blue32); // 辅助色-进行-disable
  --zw-conduct-bg-1-color--default: var(--blue33); // 辅助色-进行-背景色1
  --zw-conduct-bg-2-color--default: var(--blue34); // 辅助色-进行-背景色2
  --zw-proceed-color--normal: var(--green5); // 辅助色-合格-normal
  --zw-proceed-color--hover: var(--green6); // 辅助色-合格-hover
  --zw-proceed-color--active: var(--green4); // 辅助色-合格-active
  --zw-proceed-color--disable: var(--green9); // 辅助色-合格-disable
  --zw-proceed-bg-1-color--default: var(--green10); // 辅助色-合格-背景色1
  --zw-proceed-bg-2-color--default: var(--green11); // 辅助色-合格-背景色2
  --zw-attention-color--normal: var(--yellow); // 辅助色-注意-normal
  --zw-attention-color--hover: var(--yellow2); // 辅助色-注意-hover
  --zw-attention-color--active: var(--yellow1); // 辅助色-注意-active
  --zw-attention-color--disable: var(--yellow3); // 辅助色-注意-disable
  --zw-attention-bg-1-color--default: var(--gray); // 辅助色-注意-背景色1
  --zw-attention-bg-2-color--default: var(--gray1); // 辅助色-注意-背景色2
  --zw-warning-color--normal: var(--red6); // 辅助色-警示-normal
  --zw-warning-color--hover: var(--red8); // 辅助色-警示-hover
  --zw-warning-color--active: var(--red4); // 辅助色-警示-active
  --zw-warning-color--disable: var(--red9); // 辅助色-警示-disable
  --zw-warning-bg-1-color--default: var(--white6); // 辅助色-警示-背景色1
  --zw-warning-bg-2-color--default: var(--gray11); // 辅助色-警示-背景色2
  --zw-other-top-bg-color--default: var(--gray16); // 辅助色-其他-顶部背景色
  --zw-other-top-bg-color--hover: var(--gray14); // 辅助色-其他-顶部背景色上的hover
}

// 主题-深色
:root[data-theme='dark'] {
  --zw-primary-color--default: var(--blue27); // 主色
  --zw-primary-color--hover: var(--blue31); // 主色- hover状态时
  --zw-primary-color--active: var(--blue26); // 主色- active状态时
  --zw-primary-color--disable: var(--blue17); // 主色- disable状态时
  --zw-primary-partial-areas-color--hover: var(--blue7); // 主色-部分区域hover状态时的背景
  --zw-primary-common-light-color--default: var(--blue6); // 主色-常用浅色背景
  --zw-primary-common-dark-color--default: var(--blue19); // 主色-常用深色背景
  --zw-text-1-color--default: var(--white2); // 文字-一级
  --zw-text-2-color--default: var(--gray3); // 文字-二级
  --zw-text-3-color--default: var(--gray12); // 文字-三级
  --zw-text-color--disable: var(--blue28); // 文字-禁用
  --zw-text-color--default: var(--blue29); // 未选中的颜色
  --zw-text-reset-grey-color--default: var(--blue30); // 离线置灰
  --zw-border-color--default: var(--blue13); // 输入框等其他框类的边框线
  --zw-divider-color--default: var(--blue4); // 分割线
  --zw-primary-bg-color--default: var(--blue1); // 整体页面背景
  --zw-primary-bg-color--hover: var(--blue5); // hover状态时的背景色
  --zw-table-bg-color--hover: var(--blue14); // 表格hover状态时的背景色
  --zw-small-area-bg-color--default: var(--blue3); // 小区域使用的背景色
  --zw-table-header-bg-color--default: var(--blue11); // 表格标题栏背景色
  --zw-card-light-bg-color--default: var(--blue18); // 卡片背景上的浅灰色
  --zw-input-bg-color--disable: var(--blue9); // 输入框禁用时的背景色
  --zw-card-bg-color--default: var(--blue2); // 页面卡片背景色
  --zw-conduct-color--normal: var(--blue24); // 辅助色-进行-normal
  --zw-conduct-color--hover: var(--blue20); // 辅助色-进行-hover
  --zw-conduct-color--active: var(--blue21); // 辅助色-进行-active
  --zw-conduct-color--disable: var(--blue15); // 辅助色-进行-disable
  --zw-conduct-bg-1-color--default: var(--blue10); // 辅助色-进行-背景色1
  --zw-conduct-bg-2-color--default: var(--blue8); // 辅助色-进行-背景色2
  --zw-proceed-color--normal: var(--green7); // 辅助色-合格-normal
  --zw-proceed-color--hover: var(--green8); // 辅助色-合格-hover
  --zw-proceed-color--active: var(--green3); // 辅助色-合格-active
  --zw-proceed-color--disable: var(--green2); // 辅助色-合格-disable
  --zw-proceed-bg-1-color--default: var(--green); // 辅助色-合格-背景色1
  --zw-proceed-bg-2-color--default: var(--green1); // 辅助色-合格-背景色2
  --zw-attention-color--normal: var(--orange5); // 辅助色-注意-normal
  --zw-attention-color--hover: var(--orange7); // 辅助色-注意-hover
  --zw-attention-color--active: var(--orange3); // 辅助色-注意-active
  --zw-attention-color--disable: var(--orange2); // 辅助色-注意-disable
  --zw-attention-bg-1-color--default: var(--orange); // 辅助色-注意-背景色1
  --zw-attention-bg-2-color--default: var(--orange1); // 辅助色-注意-背景色2
  --zw-warning-color--normal: var(--red5); // 辅助色-警示-normal
  --zw-warning-color--hover: var(--red7); // 辅助色-警示-hover
  --zw-warning-color--active: var(--red3); // 辅助色-警示-active
  --zw-warning-color--disable: var(--red2); // 辅助色-警示-disable
  --zw-warning-bg-1-color--default: var(--red); // 辅助色-警示-背景色1
  --zw-warning-bg-2-color--default: var(--red1); // 辅助色-警示-背景色2
  --zw-other-top-bg-color--default: var(--blue12); // 辅助色-其他-顶部背景色
  --zw-other-top-bg-color--hover: var(--blue16); // 辅助色-其他-顶部背景色上的hover
}

// 表格状态颜色
.color-table-noSubmit {
  border: none;
  background: var(--zw-common-other-purple-color--default);
}

.color-table-waitBegin {
  border: none;
  background: var(--zw-warning-color--normal);
}

.color-table-undo {
  border: none;
  background: var(--zw-primary-color--default);
}

.color-table-processing {
  border: none;
  background: var(--zw-conduct-color--normal);
}

.color-table-check {
  border: none;
  background: var(--zw-attention-color--normal);
}

.color-table-finish {
  border: none;
  background: var(--zw-proceed-color--normal);
}

.color-table-stop {
  border: none;
  background: var(--zw-text-color--default);
}

// 抽屉标题后面状态颜色
.color-noSubmit {
  color: var(--zw-common-other-purple-color--default);
  background: var(--zw-common-other-purple-color--hover);
}

.color-waitBegin {
  color: var(--zw-warning-color--normal);
  background: var(--zw-warning-bg-2-color--default);
}

.color-undo {
  color: var(--zw-primary-color--default);
  background: var(--zw-primary-common-light-color--default);
}

.color-processing {
  color: var(--zw-conduct-color--normal);
  background: var(--zw-conduct-bg-1-color--default);
}

.color-check {
  color: var(--zw-attention-color--normal);
  background: var(--zw-attention-bg-2-color--default);
}

.color-finish {
  color: var(--zw-proceed-color--normal);
  background: var(--zw-proceed-bg-1-color--default);
}

.color-stop {
  color: var(--zw-text-color--default);
  background: var(--zw-primary-bg-color--default);
}

.text-1-color {
  color: var(--zw-text-1-color--default);
}

.text-2-color {
  color: var(--zw-text-2-color--default);
}

.text-3-color {
  color: var(--zw-text-3-color--default);
}
