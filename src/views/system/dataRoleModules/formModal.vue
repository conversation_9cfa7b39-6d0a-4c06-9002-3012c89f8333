<template>
  <a-modal title="新增" :width="800" :visible="visible"  @cancel="handleCancel">
    <a-spin :spinning="loading">
      <base-form ref="modalBaseForm"></base-form>
    </a-spin>
    <template slot="footer">
      <throttle-button @click="handleCancel" type="default" label="关闭"></throttle-button>
      <throttle-button :loading="loading" @click="submitCurrForm" label="确定"></throttle-button>
    </template>
  </a-modal>
</template>

<script>
import baseForm from './form';
import { dataRoleAdd } from '@/api/api';
export default {
  components: {
    baseForm
  },
  data () {
    return {
      visible: false,
      loading: false
    };
  },
  methods: {
    /*
        初始化
      */
    init (record) {
      this.visible = this.confirmLoading = true;
      this.$nextTick(() => {
        this.$refs.modalBaseForm.init(record, 'add');
        this.confirmLoading = false;
      });
    },
    /*
        保存
      */
    submitCurrForm () {
      this.loading = true;
      this.$refs.modalBaseForm.validate().then((form) => {
        dataRoleAdd(form).then(res => {
          this.$emit('success');
          this.$message.success('操作成功');
          this.loading = false;
          this.handleCancel();
        }).catch(() => {
          this.loading = false;
        });
      }).catch(() => {
        this.loading = false;
      });
    },
    /*
        关闭回调
      */
    handleCancel () {
      this.$refs.modalBaseForm.init(null, 'add');
      this.visible = false;
    }
  }
};
</script>

<style lang="less" scoped>
  :deep(.ant-modal-footer) {
    text-align: center;
  }
</style>
