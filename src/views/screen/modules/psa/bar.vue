<template>
  <div class="double-bar" ref="doubleBar"></div>
</template>
<script>
// 引入基本模板
const echarts = require('echarts/lib/echarts');
// 引入折线图等组件
require('echarts/lib/chart/bar');
require('echarts/lib/chart/radar');
// 引入提示框和title组件，图例
require('echarts/lib/component/tooltip');
require('echarts/lib/component/title');
require('echarts/lib/component/legend');
export default {
  data () {
    return {

    };
  },
  methods: {
    initDoubleBar (data) {
      let x_data = (data.hasOwnProperty('x') ? data.x : []);
      let legend = ['告警', '故障'];
      let processedWarn = (data.hasOwnProperty('processedWarn') ? data.processedWarn : []);
      let allWarn = (data.hasOwnProperty('allWarn') ? data.allWarn : []);
      let processedFault = (data.hasOwnProperty('processedFault') ? data.processedFault : []);
      let allFault = (data.hasOwnProperty('allFault') ? data.allFault : []);
      let lines = [{
        todo: processedWarn,
        all: allWarn
      },
      {
        todo: processedFault,
        all: allFault
      }
      ];
      if (!this.$refs.doubleBar) { // dom 不存在时不画
        return;
      }
      // 不重复初始化
      let doubleBar = echarts.getInstanceByDom(this.$refs.doubleBar);
      if (!doubleBar) {
        doubleBar = echarts.init(this.$refs.doubleBar);
        this.$once('hook:beforeDestroy', function () {
          echarts.dispose(doubleBar);
        });
      }
      let option = {
        title: {
          text: '告警/故障状态',
          top: '0',
          left: '0',
          textStyle: {
            fontSize: 16,
            color: 'rgba(255, 255, 255, 0.8)'
          }
        },
        grid: {
          x: 10,
          y: 60,
          x2: 10,
          y2: 10,
          containLabel: true
        },
        tooltip: {
          show: 'true',
          trigger: 'axis',
          axisPointer: {
            type: 'shadow',
            shadowStyle: {
              color: 'rgba(150,150,150,0.1)'
            }
          },
          backgroundColor: 'rgba(55, 71, 89, 0.9)',
          borderColor: '#69C7EB',
          borderWidth: '1',
          textStyle: {
            color: '#ffffff'
          },
          formatter: function (params) {
            if (params.length == 0) {
              return '';
            }
            const data = params[0];
            const index = data.dataIndex;
            if (params.length == 4) {
              return `${data.axisValue}<br /><span style="display:inline-block;margin-right:5px;border-radius:10px;width:10px;height:10px;background-color:rgba(89, 129, 180, 1)"></span>告警总数: ${allWarn[index]}<br /><span style="display:inline-block;margin-right:5px;border-radius:10px;width:10px;height:10px;background-color:rgba(5, 189, 255, 1)"></span>已处理告警: ${processedWarn[index]}<br /><span style="display:inline-block;margin-right:5px;border-radius:10px;width:10px;height:10px;background-color:rgba(216, 127, 90, 1)"></span>故障总数: ${allFault[index]}<br /><span style="display:inline-block;margin-right:5px;border-radius:10px;width:10px;height:10px;background-color:rgba(239, 190, 69, 1)"></span>已处理故障: ${processedFault[index]}`;
            } else {
              if (data.componentIndex == 0 || data.componentIndex == 1) {
                return `${data.axisValue}<br /><span style="display:inline-block;margin-right:5px;border-radius:10px;width:10px;height:10px;background-color:rgba(89, 129, 180, 1)"></span>告警总数: ${allWarn[index]}<br /><span style="display:inline-block;margin-right:5px;border-radius:10px;width:10px;height:10px;background-color:rgba(5, 189, 255, 1)"></span>已处理告警: ${processedWarn[index]}`;
              } else {
                return `${data.axisValue}<br /><span style="display:inline-block;margin-right:5px;border-radius:10px;width:10px;height:10px;background-color:rgba(216, 127, 90, 1)"></span>故障总数: ${allFault[index]}<br /><span style="display:inline-block;margin-right:5px;border-radius:10px;width:10px;height:10px;background-color:rgba(239, 190, 69, 1)"></span>已处理故障: ${processedFault[index]}`;
              }
            }
          }
        },
        legend: {
          icon: 'roundRect',
          top: '0',
          right: '0',
          itemWidth: 16,
          itemHeight: 8,
          textStyle: {
            textAlign: 'center',
            fontSize: 14,
            color: 'rgba(255,255,255,0.7)'
          },
          data: legend
        },
        xAxis: [{
          type: 'category',
          axisLine: {
            show: false,
            lineStyle: {
              color: '#363e83'
            }
          },
          axisLabel: {
            inside: false,
            textStyle: {
              fontWeight: 'normal',
              color: 'rgba(255, 255, 255, 0.7)',
              fontSize: 12
            }
          },
          axisTick: false,
          data: x_data
        }, {
          type: 'category',
          axisLine: {
            show: false
          },
          axisTick: false,
          axisLabel: {
            show: false
          },
          splitArea: {
            show: false
          },
          splitLine: {
            show: false
          },
          data: x_data
        }],
        yAxis: {
          type: 'value',
          name: '条',
          axisTick: false,
          axisLine: {
            show: false,
            lineStyle: {
              color: 'rgba(255, 255, 255, 0.7)',
              fontSize: 12
            }
          },
          splitLine: {
            show: true,
            lineStyle: {
              color: 'rgba(96, 130, 163, 0.19)'
            }
          },
          axisLabel: {
            show: true
          },
          minInterval: 1,
          min: function (value) {
            if (!isFinite(value.min)) {
              return 0;
            }
          },
          max: function (value) {
            if (!isFinite(value.max)) {
              return 100;
            }
            if (value.max < 1) {
              return 1;
            }
          }
        },
        series: this.getSeries(legend, lines)
      };
      doubleBar.setOption(option, true);
    },
    getSeries (legend, data) {
      const color = [{
        start: '#158AC6',
        end: '#27C6C8',
        bg: 'rgba(89, 129, 180, 1)'
      },
      {
        start: '#FC935A',
        end: '#B4474D',
        bg: 'rgba(216, 127, 90, 1)'
      }
      ];
      let series = [];
      legend.forEach((item, index) => {
        series.push({
          name: item,
          type: 'bar',
          stack: item,
          xAxisIndex: 0,
          data: data[index].todo,
          barMaxWidth: 20,
          barGap: '100%',
          itemStyle: {
            normal: {
              color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [{
                offset: 0,
                color: color[index].start
              }, {
                offset: 1,
                color: color[index].end
              }])
            }
          },
          z: 2
        }, {
          name: item,
          type: 'bar',
          xAxisIndex: 1,
          data: data[index].all,
          barMaxWidth: 20,
          barGap: 1,
          itemStyle: {
            normal: {
              color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [{
                offset: 0,
                color: color[index].start
              }, {
                offset: 1,
                color: color[index].end
              }])
            }
          },
          z: -1
        });
      });
      return series;
    }
  }
};
</script>

<style lang="less" scoped>
  .double-bar {
    width: 100%;
    height: 100%;
  }
</style>
<style>
  .all-warn {
    width: 10px;
    height: 10px;
    background: #212D3A;
    border-radius: 5px;
  }
</style>
