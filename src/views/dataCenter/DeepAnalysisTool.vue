<!--洞察工具-->
<template>
  <div>
    <a-spin size="big" :spinning="loading">
      <a-card :bordered="false" class="search-card" :bodyStyle="{ height: '100%', padding: '0px' }">
        <!-- 搜索区域 -->
        <div class="solar-eye-search-model">
          <div class="solar-eye-search-content">
            <a-row a-row :gutter="24" style="margin: 0">
              <a-col :span="5">
                <div class="search-item">
                  <span class="search-label">电站</span>
                  <ps-tree-select v-if="showPsTree" @change="changePs" :isPsName="psName" v-model="queryParams.psId"
                    :isPsaDisable="true" isInsight="1" :isQueryPs="1" style="width: 100%;" />
                </div>
              </a-col>
              <a-col :span="5">
                <div class="search-item">
                  <span class="search-label">图元选择</span>
                  <a-select v-model="chartType" @change="changeChartType">
                    <a-select-opt-group>
                      <span slot="label">柱状图</span>
                      <a-select-option value="bar">
                        <svg-icon iconClass="data-center-bar-chart"></svg-icon>
                        簇状柱状图
                      </a-select-option>
                      <a-select-option value="stack">
                        <svg-icon iconClass="data-center-bar-chart2"></svg-icon>
                        堆积柱状图
                      </a-select-option>
                    </a-select-opt-group>
                    <a-select-opt-group label="折线图">
                      <a-select-option value="line">
                        <svg-icon iconClass="data-center-line-chart"></svg-icon>
                        折线图
                      </a-select-option>
                    </a-select-opt-group>
                    <a-select-opt-group label="散点图">
                      <a-select-option value="scatter">
                        <svg-icon iconClass="data-center-point-chart"></svg-icon>
                        散点图
                      </a-select-option>
                    </a-select-opt-group>
                  </a-select>
                </div>
              </a-col>
              <a-col :span="5">
                <div class="search-item">
                  <span class="search-label">日期</span>
                  <a-range-picker class="search-time-range" format="YYYY-MM-DD" :placeholder="['起始时间', '结束时间']"
                    @change="timeChange" :allowClear="true" v-model="timeRange" :disabled-date="disabledDate" />
                </div>
              </a-col>
              <a-col :span="5">
                <div class="search-item">
                  <span class="search-label">时间间隔</span>
                  <a-select class="search-select-thinner" :getPopupContainer="(node) => node.parentNode"
                    :options="timeIntervalOptions" @change="changeTimeInterval"
                    v-model="queryParams.timeInterval"></a-select>
                </div>
              </a-col>
              <a-col :span="4" v-if="isShowXYBtn">
                <div class="search-item">
                  <span class="search-label">互换XY轴</span>
                  <a-switch v-model="isReverseXY" @change="exChangeXY" />
                </div>
              </a-col>
            </a-row>
          </div>
        </div>
      </a-card>
      <div class="solar-eye-deep-tool">
        <div class="expand-icon" :title="isShowTree ? '收起' : '展开'" :class="{ 'right-expand': !isShowTree }"
          @click="isShowTree = !isShowTree"></div>
        <div class="left" v-show="isShowTree">
          <div :style="{ height: height + 'px', padding: '16px 10px 16px 16px', position: 'relative' }">
            <h1 class="tree-title">选择设备</h1>
            <div class="reset-btn" @click="resetChart">重置</div>
            <a-tree ref="deviceTree" checkable checkStrictly :treeData="treeDataDevice" v-model="bindCheckedDevice"
              @expand="deviceExpand" @check="changeDevice" class="main-tree" :expandedKeys="deviceExpandKeys">
              <template #title="{ title }">
                <span :title='title'>{{ title.length > 14 ? title.substring(0, 14) + '...' : title }} </span>
              </template>
            </a-tree>
          </div>
        </div>
        <div class="left point-left" :style="{ marginLeft: isShowTree ? '16px' : '0', width: '238px' }">
          <div :style="{ height: height + 'px', padding: '16px 10px 16px 16px', position: 'relative' }">
            <h1 class="tree-title">选择测点</h1>
            <a-tree ref="pointTree" checkable :treeData="treeDataPoint" v-model="bindChecked" @expand="pointExpand"
              @check="changePoint" class="main-tree" :expandedKeys="pointExpandKeys" style="height: calc(100% - 28px)">
              <template #title="{ title }">
                <span :title='title'>{{ title.length > 8 ? title.substring(0, 8) + '...' : title }} </span>
              </template>
            </a-tree>
          </div>
        </div>
        <div class="right" :style="{ width: isShowTree ? 'calc(100% - 542px)' : 'calc(100% - 238px)', marginLeft: '0' }">
          <div id="card" :style="{ height: height + 'px', overflow: 'scroll' }">
            <div class="analyse-chart" :style="{ width: '100%', marginTop: '20px' }" id="deep-analyse-chart"></div>
          </div>
        </div>
      </div>
    </a-spin>
  </div>
</template>
<script>
import initDict from '@/mixins/initDict';
import moment from 'moment';
import echarts from '@/utils/enquireEchart';
import { getDeviceTypeTree } from '@/api/health/healthapi';
import { queryDeviceTypePointByPsId } from '@/api/health/AlarmEvents.js';
import {
  insightToolsInsightAnalysisData,
  insightToolsGetDefaultPs
} from '@/api/dataCenter/index';

import { mixin } from '@/utils/mixin.js';
export default {
  name: 'DeepAnalysisTool',
  mixins: [initDict, mixin],

  data () {
    return {
      unitArr: [],
      pointsUnitChange: [{ 'p1': '日发电量' }, { 'p2': '总发电量' }, { 'p24': '总有功功率' }, { 'p25': '总无功功率' }, { 'p14': '总直流功率' }, { 'p1006': '总直流功率' }],
      pointsNeedExchange: ['p1', 'p2', 'p24', 'p25', 'p14', 'p1006'],
      queryParams: {
        devices: [],
        timeInterval: 5,
        points: [],
        psId: '',
        startTime: moment().format('YYYY-MM-DD'),
        endTime: moment().format('YYYY-MM-DD')
      },

      treeDataPoint: [], // 测点树
      treeDataDevice: [ // 设备树
      ],
      baseChartOption: { // 图表基础配置
        grid: [],
        legend: {
          data: [],
          type: 'scroll',
          textStyle: {
            color: ''
          },
          pageIconColor: '',
          pageTextStyle: {
            color: ''
          },
          itemHeight: 20
        },
        tooltip: {
          trigger: 'axis',
          alwaysShowContent: false,
          appendToBody: true
        },
        xAxis: [],
        axisPointer: {
          link: [{ xAxisIndex: 'all' }],
          snap: true,
          type: 'line',
          show: true
        },
        dataZoom: [{
          type: 'slider', // slider表示有滑动块的，
          show: true,
          showDetail: false,
          bottom: 0,
          xAxisIndex: [], // 表示x轴折叠
          start: 0, // 数据窗口范围的起始百分比
          end: 100// 数据窗口范围的结束百分比
        }, {
          type: 'inside',
          xAxisIndex: []
        }],
        yAxis: [],
        series: []
      },
      timeIntervalOptions: [],
      timeIntervalOptionsAll: {
        day: [
          {
            label: '5min',
            value: 5
          }, {
            label: '15min',
            value: 15
          }, {
            label: '30min',
            value: 30
          }, {
            label: '60min',
            value: 60
          }
        ],
        week: [
          {
            label: '15min',
            value: 15
          }, {
            label: '30min',
            value: 30
          }, {
            label: '60min',
            value: 60
          }
        ],
        month: [
          {
            label: '30min',
            value: 30
          }, {
            label: '60min',
            value: 60
          }
        ]
      },
      chartBaseData: {}, // 缓存统一格式的的配置
      loading: false,
      firstLoad: true,
      psIdReady: false,
      isReverseXY: false,
      isShowTree: true,
      timeRange: [moment(), moment()], // 时间范围
      selectedPoints: [], // 选中的设备测点key
      selectedPointsData: [], // 选中的设备测点数据
      allPoints: [], // 所有的设备测点数据
      bindChecked: [], // 选中测点双向绑定
      bindCheckedDevice: [], // 选中设备双向绑定

      gridHeight: 180, // 图表实例中一个y轴高度
      baseHeight: 60, // 图表实例中距离底部的高度
      myChart: null, // 图表实例
      chartType: 'line', // 图元
      height: 100,
      chartHeight: 600,
      // 单设备多测点时，记录勾选与取消的测点
      checkedPoint: '',
      checkedFlag: false,
      cancelCheckedPoint: '',
      cancelCheckedFlag: false,
      unitPosition: [],
      psName: '',
      pointExpandKeys: [],
      deviceExpandKeys: [],
      fromRoute: false,
      firdtLoading: true,
      allDevices: [],
      showPsTree: true
    };
  },
  computed: {
    isShowXYBtn () {
      let temp = this.queryParams.devices.length ? 1 : 0;
      return (this.selectedPoints.length * temp == 2) && this.chartType == 'scatter';
    },
    isShowMenu () {
      return this.$store.state.user.isShowMenu;
    }
  },
  activated () {
    if (window.comefromAlarmCenter && !this.firdtLoading) {
      window.comefromAlarmCenter = false;
      this.resetCheckedPointAndFlag();
      this.initUnitPosition();
      this.resetData();
      this.dealRouterEvent();
    }
    this.firdtLoading = false;
  },
  created () {
    this.timeIntervalOptions = this.timeIntervalOptionsAll.day;
    this.getDefaultPsId();
  },
  mounted () {
    this.initUnitPosition();
    this.initChart();
    this.getTableHeight();
  },
  watch: {
    isShowTree (val) {
      setTimeout(() => {
        this.myChart.resize();
      }, 200);
    },
    'navTheme' () {
      if (this.myChart) {
        this.randerChart(Object.assign(this.chartBaseData));
      }
    }
  },
  methods: {
    async getDefaultPsId () {
      if (window.comefromAlarmCenter) {
        this.dealRouterEvent();
      } else {
        let res = await insightToolsGetDefaultPs();
        if (res.result_data && res.result_data.ps_id) {
          this.queryParams.psId = res.result_data.ps_id;
          this.psName = res.result_data.ps_name;
          this.psIdReady = true;
          this.getDevice();
          this.getPoint();
          this.refreshChart();
        }
      }
    },
    dealRouterEvent () {
      this.showPsTree = false;
      window.comefromAlarmCenter = false;
      this.fromRoute = true;
      this.queryParams.psId = this.$route.params.psId;
      this.psName = this.$route.params.psName;
      this.psIdReady = true;
      setTimeout(() => {
        this.showPsTree = true;
      }, 50);
      this.bindCheckedDevice = this.$route.params.list.devices.map(item => { return item.psKey; });
      this.queryParams.devices = this.$route.params.list.devices;
      this.chartType = 'line';
      this.timeRange = this.$route.params.time;
      this.queryParams.startTime = this.timeRange[0].format('YYYY-MM-DD');
      this.queryParams.endTime = this.timeRange[1].format('YYYY-MM-DD');
      let dayIntervel = this.timeRange[1].diff(this.timeRange[0], 'day');
      if (dayIntervel === 0) {
        this.timeIntervalOptions = this.timeIntervalOptionsAll.day;
      } else if (dayIntervel <= 7) {
        this.timeIntervalOptions = this.timeIntervalOptionsAll.week;
      } else if (dayIntervel <= 31) {
        this.timeIntervalOptions = this.timeIntervalOptionsAll.month;
      }
      this.queryParams.timeInterval = this.timeIntervalOptions[0].value;
      if (this.$route.params.list.points) {
        this.bindChecked = this.$route.params.list.points.map(item => { return item.point; });
        this.selectedPoints = this.bindChecked;
      }
      if (this.$route.params.list.devices.length == '1' && this.$route.params.list.points.length > 0) {
        this.checkedFlag = true;
        this.cancelCheckedFlag = false;
        this.checkedPoint = this.$route.params.list.points[0].point;
        this.cancelCheckedPoint = '';
      }
      this.getDevice(true);
      this.getPoint(true);
    },
    // 初始化双Y轴单位、位置变量
    initUnitPosition () {
      this.unitPosition = [
        {
          point: undefined,
          position: 'left',
          unit: undefined
        },
        {
          point: undefined,
          position: 'right',
          unit: undefined
        }
      ];
    },
    // 重置勾选测点及标识
    resetCheckedPointAndFlag () {
      this.checkedPoint = '';
      this.checkedFlag = false;
      this.cancelCheckedPoint = '';
      this.cancelCheckedFlag = false;
    },
    // 测点树展开事件
    pointExpand (expandedKeys) {
      this.pointExpandKeys = expandedKeys;
    },
    // 设备树展开事件
    deviceExpand (expandedKeys) {
      this.deviceExpandKeys = expandedKeys;
    },
    // 改变电站
    changePs (val, node) {
      this.queryParams.psId = node.id;
      this.resetData();
      this.getDevice();
      this.getPoint();
      this.refreshChart();
    },
    resetData () {
      this.selectedPoints = [];
      this.selectedPointsData = [];
      this.queryParams.devices = [];
      this.bindChecked = {};
      this.bindCheckedDevice = {};
      this.pointExpandKeys = [];
      this.deviceExpandKeys = [];
    },

    // 获取设备
    async getDevice (isExpand) {
      let res = await getDeviceTypeTree({
        psId: this.queryParams.psId
      });
      this.allDevices = res.result_data || [];
      this.treeDataDevice = this.arrayToTree(this.allDevices, '0');
      if (isExpand) {
        let arr = this.allDevices.filter(item => {
          return this.bindCheckedDevice.includes(item.psKey);
        });
        arr.forEach(item => {
          this.deviceExpandKeys = [...this.deviceExpandKeys, ...this.getParrntNodeIds(item)];
        });
        this.deviceExpandKeys = Array.from(new Set(this.deviceExpandKeys));
      }
    },
    //  高性能数组转树 此方法利用引用类型的内存 需要给出初始pid
    arrayToTree (items, minPid) {
      const result = []; // 存放结果集
      const itemMap = {}; //
      for (const item of items) {
        const id = item.id;
        const pid = item.pid;

        if (!itemMap[id] || !items.find(ele => ele.id == pid)) {
          itemMap[id] = {
            children: []
          };
        }

        itemMap[id] = {
          ...item,
          title: item.deviceName,
          key: item.psKey,
          checkable: ['1', '4', '5', '6', '7', '8', '12', '13', '29', '31', '23', '24', '26', '37', '301', '26', '23', '24', '37'].includes(item.deviceType),
          selectable: false,
          children: itemMap[id]['children']
        };

        const treeItem = itemMap[id];

        if (pid === minPid) {
          result.push(treeItem);
        } else {
          if (!itemMap[pid] || !items.find(ele => ele.id == pid)) {
            itemMap[pid] = {
              children: []
            };
          }
          itemMap[pid].children.push(treeItem);
        }
      }
      return result;
    },
    // 改变设备
    changeDevice (val, e) {
      this.bindChecked.checked = [];
      this.queryParams.devices = e.checkedNodes.map(item => {
        const {
          deviceType,
          psKey
        } = item.data.props;
        return {
          deviceType,
          psKey
        };
      });

      // 设备变化时 如果是单设备多测点状态 则重新获取最近点击的测点数据
      if (this.getNowChartConfig() == '1n' && e.checked) {
        const { points } = this.getRealDeviceAndPoint();
        if (points.length == 1) {
          this.checkedPoint = points[points.length - 1].point;
          this.checkedFlag = true;
        }
      }

      this.refreshChart();
    },

    // 获取测点
    async getPoint (isExpand) {
      let res;
      res = await queryDeviceTypePointByPsId({
        psId: this.queryParams.psId,
        deviceType: ['1', '4', '5', '6', '7', '8', '12', '13', '29', '31', '23', '24', '26', '37', '301', '26', '23', '24', '37'],
        source: this.$route.params.source,
        point: this.$route.params.source == 1 ? this.$route.params.list.points[0].point : '',
        psKey: this.$route.params.source == 1 ? this.$route.params.list.devices[0].psKey : ''
      });
      // 转换测点数据
      this.treeDataPoint = res.result_data.map(item => {
        return {
          ...item,
          title: item.deviceTypeName,
          key: item.deviceType,
          checkable: true,
          selectable: false,
          isCheckAll: true,
          children: item.pointInfo && item.pointInfo.map(el => {
            return {
              ...el,
              title: el.pointName,
              key: el.point,
              deviceType: item.deviceType,
              checkable: true,
              selectable: false,
              isCheckAll: false
            };
          })
        };
      });

      // 存储所有测点
      this.allPoints = res.result_data.reduce((prev, cur) => {
        return prev.concat(cur.pointInfo.map(item => {
          return {
            ...item,
            deviceType: cur.deviceType,
            key: item.point
          };
        }));
      }, []);
      this.selectedPointsData = this.allPoints.filter(item => this.selectedPoints.includes(item.key));
      if (isExpand) {
        this.pointExpandKeys = this.$route.params.list.types;
      }

      this.unitArr = this.allPoints.map(item => item.unit);
      if (isExpand) {
        this.refreshChart(true);
      }
    },

    // 改变测点
    changePoint (val, e) {
      this.selectedPoints = val;
      this.selectedPointsData = this.allPoints.filter(item => this.selectedPoints.includes(item.key));
      // 单设备多测点时，获取勾选或取消的测点及勾选标识
      if (e.checked && e.node.dataRef.isCheckAll) {
        this.initUnitPosition();
      }

      const { devices } = this.getRealDeviceAndPoint();
      if (this.getNowChartConfig() == '1n' && e.node._props.dataRef.deviceType == devices[0].deviceType) {
        let key = '';
        if (e.node.dataRef.isCheckAll) {
          key = this.selectedPoints.find(item => {
            return !['1', '4', '5', '6', '7', '8', '12', '13', '29', '31', '23', '24', '26', '37', '301', '26', '23', '24', '37'].includes(item);
          });
        } else {
          key = e.node.dataRef.key;
        }
        this.checkedFlag = e.checked;
        this.cancelCheckedFlag = !e.checked;
        this.checkedPoint = e.checked ? key : '';
        this.cancelCheckedPoint = e.checked ? '' : key;
      }
      this.refreshChart();
    },

    // // 单设备多测点时，获取勾选或取消的测点及勾选标识
    // getCheckedPointAndFlag(arr1, arr2) {
    //   if(arr1.length > arr2.length) {
    //     this.cancelCheckedPoint = arr1.filter(item => arr2.indexOf(item) == -1)[0];
    //     this.checkedFlag = false;
    //     this.cancelCheckedFlag = true;
    //   } else {
    //     this.checkedPoint = arr2[arr2.length - 1 ];
    //     this.cancelCheckedFlag = false;
    //     this.checkedFlag = true;
    //   }
    // },

    // 初始化图表
    initChart () {
      if (this.myChart == null) {
        let chartDom = document.getElementById('deep-analyse-chart');
        this.myChart = echarts.init(chartDom);
        this.unitArr = [];
        const options = this.baseChartOption;
        options && this.myChart.setOption(options);
        this.myChart.on('datazoom', e => {
          this.myChart.dispatchAction({
            type: 'hideTip'
          });
        });
        this.$once('hook:beforeDestroy', function () {
          echarts.dispose(this.myChart);
        });
      }
    },

    // 渲染图表
    async refreshChart (fromRoute) {
      if (!this.queryParams.psId) {
        return;
      }
      this.loading = true;
      // 通过key数组获取 测点数据数组
      // let pointList = this.allPoints.filter(item => this.selectedPoints.includes(item.key));
      let commonParams = {
        startTime: this.queryParams.startTime,
        endTime: this.queryParams.endTime,
        timeInterval: this.queryParams.timeInterval,
        psId: this.queryParams.psId
      };
      let data = {};
      if (fromRoute) {
        data = this.$route.params.list;
        let points = data.points.map(item => { return item.point; });
        data.points = this.allPoints.filter(item => { return points.includes(item.point); });
      } else {
        data = this.getRealDeviceAndPoint();
      }
      // const { points, devices, types } = this.getRealDeviceAndPoint();

      let list = data.types.map(item => {
        return {
          deviceType: item,
          psKeys: data.devices.filter(el => el.deviceType == item).map(el => el.psKey),
          pointList: data.points.filter(el => el.deviceType == item).map(el => {
            return {
              ...el,
              psKeys: data.devices.filter(el => el.deviceType == item).map(el => el.psKey).join(',')
            };
          })
        };
      });
      this.loading = true;
      let res;
      res = await insightToolsInsightAnalysisData({
        ...commonParams,
        list
      }).catch(() => { this.loading = false; });
      this.loading = false;

      let yData = res.result_data.yData;
      let xData = res.result_data.xData;
      this.chartBaseData = {
        xData,
        yData: this.exchangeChartData(yData, fromRoute)
      };
      this.randerChart(Object.assign(this.chartBaseData), fromRoute);
    },

    exchangeChartData (yData, fromRoute) {
      if (this.getNowChartConfig(fromRoute) == '00') {
        return [];
      }

      if (this.getNowChartConfig(fromRoute) == '1n') {
        return yData.reduce((prev, cur) => {
          return prev.concat(cur.data);
        }, []);
      }

      if (this.getNowChartConfig(fromRoute) == 'n1' || this.getNowChartConfig(fromRoute) == 'nn') {
        const dealData = x => {
          let arr = [];
          if (x.data[0] && x.data[0].point) {
            x.data[0].point.map((el, subScript) => {
              arr.push({
                title: '',
                pointName: [],
                unit: [],
                data: [],
                point: '',
                psKey: ''
              });
              x.data.map((item, index) => {
                arr[subScript].title = item.pointName[subScript];
                arr[subScript].pointName.push(item.title);
                arr[subScript].unit.push(item.unit[subScript]);
                arr[subScript].data.push(item.data[subScript]);
                arr[subScript].point = item.point[subScript];
                arr[subScript].psKey = item.psKey;
              });
            });
          }
          return arr;
        };
        let Arr = yData.reduce((prev, cur) => {
          return prev.concat(dealData(cur));
        }, []);
        return Arr;
      }
    },

    randerChart (data, fromRoute) {
      if (!this.queryParams.psId || !data.yData) {
        return;
      }
      this.isReverseXY = false;
      let { xData, yData } = JSON.parse(JSON.stringify(data));
      let options = this.baseChartOption;
      // x轴数据缩放和 图表内部滚动鼠标缩放
      options.dataZoom[0].xAxisIndex = yData.map((item, index) => index);
      options.dataZoom[1].xAxisIndex = yData.map((item, index) => index);
      // 迭代赋值 提示框 html
      options.tooltip = {
        enterable: true,
        confine: true,
        trigger: this.is2PointScatterChart(fromRoute) && this.chartType == 'scatter' ? 'item' : 'axis',
        alwaysShowContent: false,
        appendToBody: true,
        // 提示框浮层内容格式器，支持字符串模板和回调函数两种形式。
        formatter: params => {
          if (this.is2PointScatterChart(fromRoute) && this.chartType == 'scatter') { // XY轴未测点的散点图提示框
            let temp = yData.find(item => item.title == params.seriesName);
            let unit1 = temp.unit[Number(this.isReverseXY)];
            let unit2 = temp.unit[Number(!this.isReverseXY)];
            let value1 = params.data[0];
            let value2 = params.data[1];
            if (temp.point && this.pointsNeedExchange.includes(temp.point[0])) {
              unit1 = 'k' + (unit1 == 'var' ? 'Var' : unit1);
              value1 = value1 ? parseFloat((value1 / 1000).toFixed(4)) : '--';
            } else {
              value1 = value1 ? (value1 * 1).toFixed(2) : '--';
            }

            if (temp.point && this.pointsNeedExchange.includes(temp.point[1])) {
              unit2 = 'k' + (unit2 == 'var' ? 'Var' : unit2);
              value2 = value2 ? parseFloat((value2 / 1000).toFixed(4)) : '--';
            } else {
              value2 = value2 ? (value2 * 1).toFixed(2) : '--';
            }

            return params.seriesName +
                '</br>' +
                params.marker +
                yData[0].pointName[Number(this.isReverseXY)] +
                ' ' + value1 +
                (unit1 == '' ? '' : ' (' + unit1 + ') ') +
                yData[0].pointName[Number(!this.isReverseXY)] +
                value2 +
                (unit2 == '' ? '' : ' (' + unit2 + ') ');
          } else {
            let strStyle = `<style>.devicePoints{padding:0 10px} .tooltip{align-items:flex-start}</style>`;
            let axisValue = params[0].axisValue;
            if (this.getNowChartConfig() == 'nn' || this.getNowChartConfig() == '1n') {
              const groupParam = (array, f) => {
                let groups = {};
                array.forEach(el => {
                  let group = JSON.stringify(f(el));
                  let deviceName = this.getNowChartConfig() == 'nn' ? group.split(',')[0] : group;
                  el.deviceName = deviceName.replace(/\"/g, ' ');
                  groups[group] = groups[group] || [];
                  groups[group].push(el);
                });
                return Object.keys(groups).map((group) => {
                  return groups[group];
                });
              };
              params = groupParam(params, el => {
                return this.getNowChartConfig() == 'nn' ? el.data.name2 + ',' + el.data.psKey : el.data.name1;
              });
              let strList = '';
              let arr = [];
              params.forEach((param, num) => {
                let str = param.reduce((prev, cur, index) => {
                  let curUnit = this.getUnit(cur.axisIndex, cur.seriesName, yData) == 'var' ? 'Var' : this.getUnit(cur.axisIndex, cur.seriesName, yData);
                  let pointName = this.getNowChartConfig() == 'nn' ? cur.data.name1 : cur.data.name2;
                  if (this.pointsUnitChange.map(item => { return Object.values(item)[0]; }).indexOf(pointName) != -1) {
                    cur.value = cur.value ? parseFloat((cur.value / 1000).toFixed(4)) : '--';
                    curUnit = 'k' + curUnit;
                  } else {
                    cur.value = cur.value ? (cur.value * 1).toFixed(2) : '--';
                  }
                  return prev + cur.marker +
                      `${pointName}:
                    ${(cur.value === '' ? '--' : cur.value)}${curUnit == '' ? '' : (' (' + curUnit + ') ')}</br>`;
                }, `<strong>${param[0].deviceName}</strong></br>`);
                arr.push(str);
                // strList += `<div class='devicePoints'> ${str}</div>`;
              });
              arr.forEach((item, num) => {
                if (num % 3 == 0) {
                  strList += `<div><div class=" tooltip str-list"><div class='devicePoints'> ${item}</div>`;
                } else if (num % 3 == 2) {
                  strList += `<div class='devicePoints'> ${item}</div></div>`;
                } else {
                  strList += `<div class='devicePoints'> ${item}</div>`;
                }
              });
              if (arr.length % 3 != 0) {
                strList += '</div>';
              }
              return `${strStyle} <div><span>${axisValue}</span><br>${strList}</div>`;
            } else if (this.getNowChartConfig() == 'n1') {
              let strList = '';
              let arr = [];
              params.forEach((cur) => {
                let curUnit = this.getUnit(cur.axisIndex, cur.seriesName, yData) == 'var' ? 'Var' : this.getUnit(cur.axisIndex, cur.seriesName, yData);
                if (this.pointsUnitChange.map(item => { return Object.values(item)[0]; }).indexOf(cur.data.name1) != -1) {
                  cur.value = cur.value ? parseFloat((cur.value / 1000).toFixed(4)) : '--'; curUnit = 'k' + curUnit;
                } else {
                  cur.value = cur.value ? (cur.value * 1).toFixed(2) : '--';
                }
                let data = `<strong>${cur.data.name2}</strong></br>` + cur.marker +
                    `${cur.data.name1}:${(cur.value === '' ? '--' : cur.value)}${curUnit == '' ? '' : (' (' + curUnit + ') ')}`;
                arr.push(data);
              });
              arr.forEach((item, num) => {
                if (num % 3 == 0) {
                  strList += `<div><div class=" tooltip str-list"><div class='devicePoints'> ${item}</div>`;
                } else if (num % 3 == 2) {
                  strList += `<div class='devicePoints'> ${item}</div></div>`;
                } else {
                  strList += `<div class='devicePoints'> ${item}</div>`;
                }
              });
              if (arr.length % 3 != 0) {
                strList += '</div>';
              }
              return strStyle + '<div><span>' + axisValue + '</span><br>' + strList + '</div>';
            }
          }
        },
        position: (point, params, dom, rect, size) => {
          let content = document.getElementById('card');
          let minTop = content.scrollTop;
          let maxTop = content.scrollTop + content.offsetHeight - dom.offsetHeight - 30;
          let minLeft = 0;
          let maxLeft = content.offsetWidth - dom.offsetWidth;
          let top = 0;
          let left = 0;
          if (minTop <= point[1] && point[1] <= maxTop) {
            top = point[1];
          } else if (point[1] < minTop) {
            top = minTop;
          } else {
            top = maxTop;
          }
          if (minLeft < point[0] + 20 && point[0] + 20 <= maxLeft) {
            left = point[0] + 20;
          } else {
            left = point[0] - dom.offsetWidth - 20;
          }
          return { top: top, 'left': left };
        },
        className: 'solar-eye-tooptip deep-anly-tooptip',
        triggerOn: 'mousemove'
      };

      if (this.is2PointScatterChart(fromRoute) && this.chartType == 'scatter') {
        if (yData.length == 1) { // 单设备2测点数据处理
          let arr = yData[0].data[0].reduce((prev, cur, index) => {
            return prev.concat([[yData[0].data[0][index], yData[0].data[1][index]]]);
          }, []);
          yData[0].data = arr;
        } else { // 多设备2测点数据处理
          let arr = [];
          yData[0].data.map((el, subScript) => {
            arr.push({
              title: '',
              pointName: [],
              unit: [],
              data: [],
              point: ''
            });
            yData.map((item, index) => {
              arr[subScript].point = item.point[subScript];
              arr[subScript].title = item.pointName[subScript];
              arr[subScript].pointName.push(item.title);
              arr[subScript].unit.push(item.unit[subScript]);
              arr[subScript].data = item.data[subScript].map((itemData, indexData) => [yData[0].data[subScript][indexData], yData[1].data[subScript][indexData]]);
            });
          });
          yData = arr;
        }
        options.grid = this.makeGrid(this.baseHeight + this.gridHeight * 0, 0, 1);
        options.xAxis = this.makeYAxis(0, {
          type: 'value',
          axisLabel: {
            formatter: function (value) {
              let pointsUnitChange = ['p1', 'p2', 'p24', 'p25', 'p14', 'p1006']; // 需转换单位的 point
              if (pointsUnitChange.includes(yData[0].point[0])) {
                return (value ? parseFloat((value / 1000).toFixed(4)) : '--') + `${'k' + (yData[0].unit[0] == 'var' ? 'Var' : yData[0].unit[0])}`;
              } else {
                return (value ? (value * 1).toFixed(2) : '--') + (yData[0].unit[0] == 'var' ? 'Var' : yData[0].unit[0]);
              }
            }
          },
          axisTick: { show: false }
          // nameGap: 10,
          // name:yData[0].pointName[0],
        });
        options.yAxis = this.makeYAxis(0, {
          type: 'value',
          // name:yData[0].pointName +  '(' + yData[0].unit + ')',
          axisLabel: {
            formatter: function (value) {
              let pointsUnitChange = ['p1', 'p2', 'p24', 'p25', 'p14', 'p1006']; // 需转换单位的 point
              if (pointsUnitChange.includes(yData[0].point[1])) {
                return (value ? parseFloat((value / 1000).toFixed(4)) : '--') + `${'k' + (yData[0].unit[1] == 'var' ? 'Var' : yData[0].unit[1])}`;
              } else {
                return (value ? (value * 1).toFixed(2) : '--') + (yData[0].unit[1] == 'var' ? 'Var' : yData[0].unit[1]);
              }
            }
          },
          axisTick: { show: false }
          // name:yData[0].pointName[1],
        });
        options.series = yData.map((item, index) => {
          return this.makeSeries(0, 0, {
            name: item.title,
            data: item.data
          }, this.chartType);
        });
        options.legend.data = yData.pointName;
      } else {
        options.xAxis = yData.map((item, index) => this.makeXAxis(index, { data: xData }, yData.length));
        let realtion = this.getNowChartConfig(fromRoute);
        options.grid = yData.map((item, index) => {
          return this.makeGrid(this.baseHeight + this.gridHeight * index, index, yData.length);
        });
        options.series = yData.reduce((prev, cur, index) => {
          let arr = cur.data && cur.data.map((el, subScript) => {
            let yAxisIndex = realtion == '1n' ? this.unitArr.indexOf(cur.unit[subScript]) : index;
            // makeSeries给series中配置每项的yAxisIndex，yAxisIndex对应Y轴单位序号,xAxisIndex=index代表测点序号
            let dataArr = [];
            el.forEach(v => {
              dataArr.push({
                value: v,
                name1: cur.title,
                name2: cur.pointName[subScript],
                psKey: cur.psKey
              });
            });
            return this.makeSeries(index, yAxisIndex, {
              name: cur.title + '/' + cur.pointName[subScript],
              data: dataArr,
              unit: cur.unit[subScript]
            }, this.chartType);
          });
          return prev.concat(arr);
        }, []);
        // 给yAxis对象设置多组分组，分别代表不同的Y轴
        options.yAxis = this.getyAxis(yData, realtion, fromRoute);
        // 迭代赋值图例
        options.legend.data = yData.reduce((prev, cur) => {
          let arr = cur.pointName.map(item => cur.title + '/' + item);
          return prev.concat(arr);
        }, []);
      }
      console.log('===options====', options);
      let isDarkColor = this.navTheme == 'dark' ? '#ffffff' : '#333';
      options.legend.textStyle.color = isDarkColor;
      options.legend.pageIconColor = isDarkColor;
      options.legend.pageTextStyle.color = isDarkColor;
      if (options.xAxis[0]) {
        options.xAxis[0].data = options.xAxis[0].data.map(item => {
          return item.split(' ')[0] + '\n' + item.split(' ')[1];
        });
      }
      // 清空之前图表实例 避免多次渲染
      // this.myChart.clear()
      // 单个y轴时 图表占满 多y轴固定高度
      let height;
      if (yData.length <= 1 || this.is2PointScatterChart(fromRoute)) {
        height = this.baseHeight * 2 + this.height - 160;
      } else {
        height = this.baseHeight * 2 + this.gridHeight * (yData.length);
      }
      this.myChart.resize({ height });
      this.myChart.setOption(options, true);
    },

    getUnit (axisIndex, name, yData) {
      let index = yData[axisIndex].pointName.findIndex(item => name.includes(item));
      return yData[axisIndex].unit[index];
    },

    getyAxis (yData, realtion, fromRoute) {
      // 不是但设备多测点时，将勾选、取消测点及标识重置
      if (realtion != '1n') {
        this.resetCheckedPointAndFlag();
      }
      if (realtion == '00') {
        this.initUnitPosition();
      }
      if (realtion == 'nn' || realtion == 'n1') { // 多设备多测点 和 单测点n设备时右侧标题显示单位
        let pointsUnitChange = ['p1', 'p2', 'p24', 'p25', 'p14', 'p1006'];
        return yData.map((item, index) => {
          let name;
          if (pointsUnitChange.includes(item.point)) {
            name = item.title +
                  '\n' +
                  (item.unit[0] == '' ? '' : '(') +
                  'k' +
                  (item.unit[0] == 'var' ? 'Var' : item.unit[0]) +
                  (item.unit[0] == '' ? '' : ')');
          } else {
            name = item.title +
                  '\n' +
                  (item.unit[0] == '' ? '' : '(') +
                  (item.unit[0] == 'var' ? 'Var' : item.unit[0]) +
                  (item.unit[0] == '' ? '' : ')');
          }
          return this.makeYAxis(index, {
            name: name,
            axisLabel: {
              formatter: function (value) {
                // 需转换单位的 point
                if (pointsUnitChange.includes(item.point)) {
                  return value ? parseFloat((value / 1000).toFixed(4)) : '--';
                } else {
                  return value ? (value * 1).toFixed(2) : '--';
                }
              }
            }
          });
        }
        );
      } else if (realtion == '1n') { // 单设备多测点显示最新选择的数据的划分的轴坐标及单位
        let yAxis = [];
        this.unitArr.forEach(item => {
          yAxis.push({
            scale: true,
            show: false,
            unit: item
          });
        });
        // 勾选测点
        if (this.checkedFlag) {
          let unit = '';
          // 获取新增测点的单位
          if (this.selectedPoints.length > 0) {
            if (fromRoute) {
              unit = this.$route.params.list.points[0].unit;
            } else {
              unit = this.allPoints.find(item => item.point == this.checkedPoint).unit;
            }
          }
          // 新增测点与旧双Y轴数组比较，重置双y轴显示单位、测点、位置
          if (unit === this.unitPosition[0].unit) {
            this.unitPosition[0].point = this.checkedPoint;
          } else if (unit === this.unitPosition[1].unit) {
            this.unitPosition[1].point = this.checkedPoint;
          } else if (!this.unitPosition[0].point) {
            this.unitPosition[0].point = this.checkedPoint;
            this.unitPosition[0].unit = unit;
          } else if (this.unitPosition[0].point && !this.unitPosition[1].point) {
            this.unitPosition[1].point = this.checkedPoint;
            this.unitPosition[1].unit = unit;
          } else {
            this.unitPosition.forEach(item => {
              if (item.point != this.lastSelectTwoPoints[0]) {
                item.point = this.checkedPoint;
                item.unit = unit;
              }
            });
          }
          // 取消勾选测点
        } else if (this.cancelCheckedFlag) {
          if (this.selectedPoints.length > 0) {
            this.setUnitPosition(this.selectedPoints, this.allPoints);
          } else {
            this.initUnitPosition();
          }
          // 其他情况，如初始进入页面
        } else {
          yData && yData[0] && yData[0].point.forEach((el, idx) => {
            this.lastSelectTwoPoints.forEach((point, index) => {
              if (point == el) {
                if (index == 0) {
                  this.unitPosition[index].position = 'left';
                } else {
                  this.unitPosition[index].position = 'right';
                }
                this.unitPosition[index].point = yData[0].point[idx];
                this.unitPosition[index].unit = yData[0].unit[idx];
              }
            });
          });
          if (this.unitPosition[0].unit == this.unitPosition[1].unit) {
            this.unitPosition[1].unit = undefined;
            this.unitPosition[1].point = undefined;
          }
        }
        this.resetCheckedPointAndFlag();
        yData && yData[0] && yData[0].point.forEach((el, idx) => {
          this.unitPosition.forEach((item, index) => {
            if (item.point == el) {
              let yAxisIndex = this.unitArr.indexOf(yData[0].unit[idx]);
              yAxis[yAxisIndex].show = true;
              yAxis[yAxisIndex].point = yData[0].point[idx];
              yAxis[yAxisIndex].pointName = yData[0].pointName[idx];
              yAxis[yAxisIndex].unit = yData[0].unit[idx];
              yAxis[yAxisIndex].position = index == '0' ? 'left' : 'right';
            }
          });
        });
        let pointsUnitChange = ['p1', 'p2', 'p24', 'p25', 'p14', 'p1006'];
        return yAxis.map((item, index) => this.makeYAxis(
          0,
          {
            scale: true,
            show: item.show,
            name: `${pointsUnitChange.includes(item.point)
              ? ('k' + (item.unit == 'var' ? 'Var' : item.unit)) : (item.unit == 'var' ? 'Var' : item.unit)}`,
            axisLabel: {
              formatter: function (value) {
                // 需转换单位的 point
                if (pointsUnitChange.includes(item.point)) {
                  return value ? parseFloat((value / 1000).toFixed(4)) : '--';
                } else {
                  return value ? (value * 1).toFixed(2) : '--';
                }
              }
            },
            position: item.position

          }, true));
      }
    },
    // 单设备多测点-取消勾选测点时，设置双Y轴单位、显示位置
    setUnitPosition (selectArr, pointArr) {
      if (selectArr.length > 0) {
        if (this.lastSelectTwoPoints.length == 2) {
          let unit1 = pointArr.find(item => item.key == this.lastSelectTwoPoints[0]).unit;
          let unit2 = pointArr.find(item => item.key == this.lastSelectTwoPoints[1]).unit;
          // 取消的测点是否为最近两个勾选的测点
          if (this.unitPosition[0].point == this.cancelCheckedPoint) {
            if (this.unitPosition[1].point) {
              if (unit1 == unit2) {
                this.setLeftUnitPoistion(this.lastSelectTwoPoints[1], unit2);
              } else {
                this.unitPosition[0].point = this.unitPosition[1].point == this.lastSelectTwoPoints[0] ? this.lastSelectTwoPoints[1] : this.lastSelectTwoPoints[0];
                this.unitPosition[0].unit = this.unitPosition[1].point == this.lastSelectTwoPoints[0] ? unit2 : unit1;
              }
            } else {
              if (unit1 == unit2) {
                this.setLeftUnitPoistion(this.lastSelectTwoPoints[1], unit2);
              } else {
                this.unitPosition = [
                  {
                    point: this.lastSelectTwoPoints[1],
                    position: 'left',
                    unit: unit2
                  },
                  {
                    point: this.lastSelectTwoPoints[0],
                    position: 'right',
                    unit: unit1
                  }
                ];
              }
            }
          } else if (this.unitPosition[1].point == this.cancelCheckedPoint) {
            if (unit1 == unit2) {
              this.setLeftUnitPoistion(this.lastSelectTwoPoints[1], unit2);
            } else {
              this.unitPosition[1].point = this.unitPosition[0].point == this.lastSelectTwoPoints[0] ? this.lastSelectTwoPoints[1] : this.lastSelectTwoPoints[0];
              this.unitPosition[1].unit = this.unitPosition[0].point == this.lastSelectTwoPoints[0] ? unit2 : unit1;
            }
          } else if (!this.unitPosition[1].point && unit1 != unit2) {
            this.unitPosition[1].point = this.lastSelectTwoPoints[0];
            this.unitPosition[1].unit = unit1;
          }
        } else if (this.lastSelectTwoPoints.length == 1) {
          let unit = pointArr.find(item => item.key == this.lastSelectTwoPoints[0]).unit;
          this.setLeftUnitPoistion(this.lastSelectTwoPoints[0], unit);
        }
      }
    },
    // 设置左边单Y轴
    setLeftUnitPoistion (point, unit) {
      this.unitPosition = [
        {
          point: point,
          position: 'left',
          unit: unit
        },
        {
          point: undefined,
          position: 'right',
          unit: undefined
        }
      ];
    },

    is2PointScatterChart (fromRoute) {
      let list = {};
      if (fromRoute) {
        list = this.$route.params.list;
      } else {
        list = this.getRealDeviceAndPoint();
      }
      let points = list.points;
      let devices = list.devices;
      // 只有某一种设备选择两个测点且该种设备类型有勾选的设备时 才可作为散点图的情况
      return devices.length >= 1 && points.length == 2 && points.every(item => item.deviceType == points[0].deviceType);
    },

    exChangeXY () {
      let option = this.myChart.getOption();
      option.series = option.series.map(item => {
        item.data = item.data.map((el, index) => [el[1], el[0]]);
        return item;
      });

      let { xAxis, yAxis } = option;
      option.xAxis = yAxis;
      option.yAxis = xAxis;
      this.myChart.setOption(option, true);
    },
    makeXAxis (gridIndex, opt, length) {
      let color = this.navTheme == 'dark' ? '#fff' : '#9B9B9B';
      return echarts.util.merge({
        type: 'category',
        axisLine: { onZero: false, lineStyle: { color: 'rgba(51, 51, 51, .14)' } },
        axisTick: { show: false },
        axisLabel: { show: gridIndex == length - 1, color: color }, // 从上往下排列 最下面一个显示x轴label
        splitLine: { show: false, lineStyle: { color: 'rgba(51, 51, 51, .14)' } },
        gridIndex: gridIndex
      }, opt || {}, true);
    },
    makeYAxis (gridIndex, opt, is1n) {
      let isDark = this.navTheme == 'dark'; let color = isDark ? '#fff' : '#9B9B9B';
      return echarts.util.merge({
        type: 'value',
        gridIndex: gridIndex,
        nameLocation: is1n ? 'end' : 'middle',
        nameGap: is1n ? 20 : 80,
        axisPointer: { show: false },
        axisLine: { onZero: false, show: false },
        axisLabel: { show: true, color: color, showMaxLabel: gridIndex == 0 }, // y轴刻度线 最上面一个y轴才显示顶部刻度label
        nameTextStyle: {
          color: isDark ? '#fff' : 'rgba(0, 0, 0, 0.8)'
        },
        splitLine: {
          lineStyle: {
            color: isDark ? ['#354661'] : ['#d6d6d6']
          }
        }
      }, opt || {}, true);
    },
    makeGrid (top, index, length) {
      let isDark = this.navTheme == 'dark'; let colorBg = isDark ? (index % 2 == 1 ? '#111C2D' : '#152236') : (index % 2 == 1 ? 'rgba(255, 143, 51, 0.06)' : '#fff');
      let borderColor = isDark ? '#3B495C' : '#d6d6d6';
      return echarts.util.merge({
        top: top,
        bottom: length == index ? 60 : undefined,
        show: true,
        right: 100,
        borderColor: borderColor,
        backgroundColor: colorBg, // 隔行背景色设置
        height: length == 1 ? this.height - 160 : this.gridHeight // 根据y轴个数不同计算不同的高度
      }, {}, true);
    },
    makeSeries (index, yIndex, opt, type) {
      return echarts.util.merge({
        type: type == 'stack' ? 'bar' : type,
        stack: type == 'stack' ? 'total' + index : undefined,
        symbol: 'circle',
        symbolSize: type == 'scatter' ? 8 : 4,
        barGap: 0,
        xAxisIndex: index,
        yAxisIndex: yIndex,
        large: true,
        cursor: 'default',
        emphasis: {
          focus: 'series'
        },
        smooth: true
      }, opt || {}, true);
    },

    // 获取过滤后的测点设备关系
    getRealDeviceAndPoint () {
      console.log('this.queryParams.devices', this.queryParams.devices, this.selectedPointsData);
      // 过滤出选择的设备类型集合
      let types = this.queryParams.devices.reduce((prev, cur) => {
        return prev.concat(
          prev.includes(cur.deviceType) ? [] : [cur.deviceType]
        );
      }, []);

      // 过滤掉 未选择测点的设备类型
      types = types.filter(item => this.selectedPointsData.some(el => el.deviceType == item));

      // 过滤掉 未选择设备的测点
      let points = this.selectedPointsData.filter(item => types.includes(item.deviceType));
      // 过滤出选择了测点的设备数据
      let devices = this.queryParams.devices.filter(item => types.includes(item.deviceType));
      console.log('points', points,
        devices,
        types);
      return {
        points,
        devices,
        types
      };
    },

    // 获取当前选中的 并且过滤后的设备和测点数量类型 前面设备 后面测点
    getNowChartConfig (fromRoute) {
      let list = {};
      if (fromRoute) {
        list = this.$route.params.list;
      } else {
        list = this.getRealDeviceAndPoint();
      }
      let points = list.points;
      let devices = list.devices;

      if (devices.length == 0 || points.length == 0) {
        return '00';
      }
      if (devices.length > 1 && points.length > 1) { // 多设备多测点
        return 'nn';
      }
      if (devices.length == 1 && points.length >= 1) { // 单设备n测点
        // 取起作用的单设备的对应类型测点的最后两个测点
        // 过滤掉 未选择设备的测点
        let arr = this.selectedPointsData.filter(item => item.deviceType == devices[0].deviceType);
        // 调整顺序
        let temp = this.selectedPoints.reduce((prev, cur) => {
          return prev.concat(arr.find(el => el.key == cur) || []);
        }, []);
        this.lastSelectTwoPoints = temp.map(item => item.key).slice(-2);
        return '1n';
      }
      if (devices.length > 1 && points.length == 1) { // 多设备单测点
        return 'n1';
      }
    },
    // 修改时间间隔
    changeTimeInterval (val) {
      this.refreshChart();
    },
    // 修改时间范围
    timeChange (val) {
      if (val && val.length > 0) {
        let dayIntervel = val[1].diff(val[0], 'day');
        if (dayIntervel === 0) {
          this.timeIntervalOptions = this.timeIntervalOptionsAll.day;
        } else if (dayIntervel <= 7) {
          this.timeIntervalOptions = this.timeIntervalOptionsAll.week;
        } else if (dayIntervel <= 31) {
          this.timeIntervalOptions = this.timeIntervalOptionsAll.month;
        } else {
          this.$message.info('日期范围最多一个月');
          this.timeRange = [moment(val[1]).subtract(1, 'month').calendar(), moment(val[1])];
          this.queryParams.startTime = moment(moment(val[1]).subtract(1, 'month').calendar()).format('YYYY-MM-DD');
          this.queryParams.endTime = moment(val[1]).format('YYYY-MM-DD');
          this.timeIntervalOptions = this.timeIntervalOptionsAll.month;
          this.queryParams.timeInterval = this.timeIntervalOptions[0].value;
          return;
        }
        this.queryParams.timeInterval = this.timeIntervalOptions[0].value;
        this.queryParams.startTime = moment(val[0]).format('YYYY-MM-DD');
        this.queryParams.endTime = moment(val[1]).format('YYYY-MM-DD');
      } else {
        this.timeIntervalOptions = this.timeIntervalOptionsAll.day;
        this.queryParams.timeInterval = this.timeIntervalOptions[0].value;
        this.queryParams.startTime = moment().format('YYYY-MM-DD');
        this.queryParams.endTime = moment().format('YYYY-MM-DD');
      }
      this.refreshChart();
    },

    disabledDate (current) {
      // 不能选择今天以后的
      return current && current > moment().endOf('day');
    },
    // 改变图元
    changeChartType (val) {
      this.randerChart(Object.assign(this.chartBaseData));
    },
    // 设置表格高度
    getTableHeight () {
      let $searchHeight = window.document.getElementsByClassName('search-card');
      let height = 0;
      if ($searchHeight[0]) {
        height = $searchHeight[0].clientHeight + 16;
      }
      this.height = window.innerHeight - height - 60 - 40 - 24 + (this.isShowMenu ? 0 : 124);
    },
    resetChart () {
      this.selectedPoints = [];
      this.selectedPointsData = [];
      this.queryParams.devices = [];
      this.bindChecked = {};
      this.bindCheckedDevice = {};
      this.refreshChart();
    },
    // 设备树-获取默认设备所有父节点
    getParrntNodeIds (treeNode, parsArr) {
      parsArr = parsArr || [];
      let parNode = this.allDevices.find(item => {
        return treeNode.pid == item.id;
      });
      if (parNode) {
        parsArr.push(parNode.psKey);
        this.getParrntNodeIds(parNode, parsArr);
      }
      return parsArr;
    }
  }
};
</script>

<style lang="less" scoped>
.expand-icon {
  height: 48px;
  width: 16px;
  position: absolute;
  left: 272px;
  z-index: 1;
  cursor: pointer;
  background: url('../../assets/images/health/deepAnalysisTool/expand_left_light.png') no-repeat;
}

.expand-icon:hover {
  background: url('../../assets/images/health/deepAnalysisTool/expand_left_light_hover.png') no-repeat;
}

.right-expand {
  left: 0;
  position: fixed;
  background: url('../../assets/images/health/deepAnalysisTool/expand_right_light.png') no-repeat;
  z-index: 99;
}

.right-expand:hover {
  background: url('../../assets/images/health/deepAnalysisTool/expand_right_light_hover.png') no-repeat;
}

:root[data-theme='dark'] {
  .expand-icon {
    background: url('../../assets/images/health/deepAnalysisTool/expand_left_dark.png') no-repeat;
  }

  .expand-icon:hover {
    background: url('../../assets/images/health/deepAnalysisTool/expand_left_dark_hover.png') no-repeat;
  }

  .right-expand {
    background: url('../../assets/images/health/deepAnalysisTool/expand_right_dark.png') no-repeat;
  }

  .right-expand:hover {
    background: url('../../assets/images/health/deepAnalysisTool/expand_right_dark_hover.png') no-repeat;
  }

  .left,
  .point-left,
  .right {
    background: #172130;
  }

  .right {
    border-left: 1px solid #364457;
  }

  .reset-btn {
    color: #267DCF;
  }

  .left,
  .point-left,
  .right {
    ::-webkit-scrollbar-track {
      background: transparent;
    }

    ::-webkit-scrollbar {
      background: transparent;
    }
  }

}

.str-list {
  display: flex;
}

.flex-end {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  width: 100%;
  font-size: 20px;
  color: #9B9B9B;
}

.select-img {
  width: 20px;
  height: 20px;
}

.tree-title {
  font-size: 14px;
  font-weight: 600;
  color: #333333;
  padding-left: 8px;
}

.reset-btn {
  position: absolute;
  right: 24px;
  top: 16px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 600;
  color: #FF8F33;
}

.left {
  width: 288px;
  background: #fff;
}

.right {
  background: #fff;
  border-left: 1px solid #EEEEEE;
  box-shadow: 0px 4px 0px 0px rgba(21, 60, 104, 0.09);
  border-radius: 0px 4px 4px 0px;

}

.point-left {
  border-radius: 4px 0 0 4px;
  box-shadow: 0px 0px 6px 0px rgba(21, 60, 104, 0.09);
}

.left,
.point-left,
.right {
  ::-webkit-scrollbar-track {
    background: transparent;
  }

  ::-webkit-scrollbar {
    background: transparent;
  }
}

:deep(.main-tree) {
  overflow: auto;
  height: calc(100% - 28px);
}
</style>
