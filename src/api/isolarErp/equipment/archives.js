import { axios } from '@/utils/erp-request';

// 获取设备档案列表数据
export function getArchivesList (data) {
  return axios({
    url: '/equipment/v1/archives/list',
    method: 'post',
    data
  });
}
// 删除设备档案
export function deleteErpDeviceInfo (data) {
  return axios({
    url: '/equipment/v1/archives/deleteErpDeviceInfo',
    method: 'post',
    data
  });
}
// 新增设备档案
export function insertErpDeviceInfo (data) {
  return axios({
    url: '/equipment/v1/archives/insertErpDeviceInfo',
    method: 'post',
    data
  });
}
// 根据电站档案psaId获取实体电站信息
export function getErpPowerStationInfoByPsaId (data) {
  return axios({
    url: '/system/v1/erpProjectManager/getErpPowerStationInfoByPsaId',
    method: 'post',
    data
  });
}
// 更新设备档案
export function updateErpDeviceInfo (data) {
  return axios({
    url: '/equipment/v1/archives/updateErpDeviceInfo',
    method: 'post',
    data
  });
}
// 导出档案列表数据
export function exportArchives (data) {
  return axios({
    url: '/equipment/v1/archives/exportArchives',
    method: 'post',
    data
  });
}

// 根据档案id获取设备档案数据
export function getErpDeviceInfo (data) {
  return axios({
    url: '/equipment/v1/archives/getErpDeviceInfo',
    method: 'post',
    data
  });
}

// 根据设备型号获取详细技术参数固定部分
export function getErpDeviceInfoDetallBaseByDeviceType (data) {
  return axios({
    url: '/equipment/v1/archives/getErpDeviceInfoDetallBaseByDeviceType',
    method: 'post',
    data
  });
}
// 图片预览
export function filePreview (data) {
  return axios({
    url: '/equipment/v1/archives/filePreview',
    method: 'post',
    data
  });
}
// 获取设备档案生产厂家列表
export function getErpDeviceInfoMaker (data) {
  return axios({
    url: '/equipment/v1/archives/getErpDeviceInfoMaker',
    method: 'post',
    data
  });
}
// 获取设备档案设备型号列表
export function getErpDeviceInfoDeviceModel (data) {
  return axios({
    url: '/equipment/v1/archives/getErpDeviceInfoDeviceModel',
    method: 'post',
    data
  });
}
// 获取设备档案安装单位列表
export function getErpDeviceInfoInstallCompany (data) {
  return axios({
    url: '/equipment/v1/archives/getErpDeviceInfoInstallCompany',
    method: 'post',
    data
  });
}
// 批量修改设备类型
export function updateEquipmentType (data) {
  return axios({
    url: '/equipment/v1/archives/updateEquipmentType',
    method: 'post',
    data
  });
}
// 导出设备档案详情数据-设备台账页面使用
export function exportErpDeviceInfoExecl (data) {
  return axios({
    url: '/equipment/v1/archives/exportErpDeviceInfoExecl',
    method: 'post',
    data
  });
}
// 查询档案列表(去重)-资产类型配置页面使用
export function getDistinctErpDeviceInfoList (data) {
  return axios({
    url: '/equipment/v1/archives/getDistinctErpDeviceInfoList',
    method: 'post',
    data
  });
}
