export const baseInfo = [
  { label: '电站名称', key: 'psaName' },
  { label: '任务类型', key: 'taskTypeName' },
  {
    label: '任务子类',
    key: 'workSubclassName',
    func: (params) => {
      return ['1', '2', '3', '5', '10'].includes(params.taskType);
    }
  },
  {
    label: '工程遗留',
    key: 'isEngLegacy',
    slot: 'isEngLegacy',
    func: (params) => {
      return params.taskType == '8';
    }
  }
];

export const baseDetail = [
  { label: '计划时间', key: 'planTime', slot: 'planTime' },
  { label: '任务名称', key: 'taskName' },
  { slot: 'stationEnvironment' },
  { label: '任务内容', key: 'taskDescription', span: 24 },
  {
    label: '清洗方式',
    key: 'cleanType',
    dict: 'pdca_clean_type',
    func: (params) => {
      return params.taskType == 1;
    }
  },
  {
    label: '除草方式',
    key: 'grassType',
    dict: 'pdca_grass_type',
    func: (params) => {
      return params.taskType == 2;
    }
  },
  {
    label: (params) => {
      return (params.taskType == '1' ? '清洗' : '除草') + '容量(MW)';
    },
    labelFunc: true,
    key: 'cleanCap',
    func: (params) => {
      return ['1', '2'].includes(params.taskType);
    }
  },
  {
    label: '上次试验时间',
    key: 'beforeExperimentDate',
    func: (params) => {
      return params.taskType == 3;
    }
  },
  {
    label: '设备类型',
    key: 'deviceTypeIdName',
    func: (params) => {
      return isShowDevice(params);
    }
  },
  {
    label: '设备名称',
    key: 'deviceName',
    func: (params) => {
      return isShowDevice(params);
    }
  },
  {
    label: '设备编号',
    key: 'deviceNo',
    lineClamp: 2,
    func: (params) => {
      return isShowDevice(params);
    }
  },
  { slot: 'deviceNumber' },
  {
    label: '工作状态',
    key: 'workStatusName',
    func: (params) => {
      return workStatus(params);
    }
  },
  {
    label: '责任人',
    key: 'liablePersonName',
    func: (params) => {
      return ((params.step >= 5 && params.processDefKey == 'activiti4273Workflow') ||
        (params.step >= 5 && params.processDefKey == 'activiti4773Workflow') ||
        (params.step >= 2 && params.processDefKey == 'activiti4873Workflow') ||
        (params.step >= 1 && params.processDefKey == 'activiti5073Workflow') ||
        params.flowSts == '4' || params.liablePersonName);
    }
  },
  {
    label: '作业类别',
    key: 'jobCategoryName',
    func: (params) => {
      return params.taskType != 10;
    }
  },
  {
    label: '作业内容',
    key: 'jobContentName',
    func: (params) => {
      return params.taskType != 10;
    }
  },
  {
    label: '风险等级',
    key: 'riskLevelName',
    func: (params) => {
      return params.taskType != 10;
    }
  },
  {
    label: '作业开始日期',
    key: 'riskJobDate',
    func: (params) => {
      return params.taskType != 10;
    }
  },
  { label: '附件', key: 'uploadFileList', type: 'file:text', span: 24 },
  {
    label: '执行人',
    key: 'flyingHandName',
    span: 24,
    func: (params) => {
      return ['activiti3373Workflow', 'activiti3273Workflow'].includes(params.processDefKey) && params.uniqueType != '5';
    }
  }
];

function isShowDevice (params) {
  return ['4', '5', '6', '9', '10'].includes(params.taskType) && params.taskType != '10';
}

function workStatus (params) {
  return params.uniqueType && params.uniqueType != '9' && params.processDefKey != 'activiti3273Workflow';
}

export const faultDetail = [
  { label: '设备状态', key: 'deviceTypeName' },
  {
    label: '缺陷来源',
    key: 'orderSourceName',
    func: (params) => {
      return isOrder(params);
    }
  },
  {
    label: '实体电站',
    key: 'psName',
    func: (params) => {
      return params.orderSource == '4';
    }
  },
  {
    label: (params) => {
      return params.taskType == '7' ? '故障描述' : '缺陷描述';
    },
    labelFunc: true,
    key: 'taskDescription',
    span: '24'
  },
  {
    label: '设备类型',
    key: 'deviceTypeIdName'
  },
  {
    label: '设备名称',
    key: 'deviceName',
    func: (params) => {
      return params.deviceTypeId != 0 && params.orderSource != '6';
    }
  },
  {
    label: '设备编号',
    key: 'deviceNo',
    func: (params) => {
      return params.deviceTypeId != 0 && params.orderSource != '6';
    }
  },
  {
    label: '生产厂家',
    key: 'maker',
    func: (params) => {
      return params.deviceTypeId != 0 && params.maker && params.orderSource != '6';
    }
  },
  {
    label: '设备型号',
    key: 'deviceModel',
    func: (params) => {
      return params.deviceTypeId != 0 && params.deviceModel && params.orderSource != '6';
    }
  },
  {
    label: (params) => {
      return params.taskType == '7' ? '故障名称' : '缺陷名称';
    },
    labelFunc: true,
    key: 'defectNameName'
  },
  {
    label: (params) => {
      return params.taskType == '7' ? '故障类别' : '缺陷类别';
    },
    labelFunc: true,
    key: 'defectTypeName'
  },
  { label: '发现时间', key: 'findTime' },
  {
    label: '发现人',
    key: 'findUserName',
    func: (params) => {
      return params.orderSource != '6';
    }
  },
  { label: '预计消除时间', key: 'predictRecoverTime' },
  {
    label: '责任人',
    key: 'liablePersonName',
    func: (params) => {
      return isOrder(params) && params.orderSource != '6';
    }
  },
  {
    label: '协作人',
    key: 'executorNames',
    func: (params) => {
      return params.uniqueType != 5 && params.orderSource == '6';
    }
  },
  {
    label: '主负责人',
    key: 'liablePersonName',
    func: (params) => {
      return params.uniqueType != 5 && params.orderSource == '6';
    }
  },
  {
    label: '作业类别',
    key: 'jobCategoryName',
    func: (params) => {
      return params.orderSource != '6';
    }
  },
  {
    label: '作业内容',
    key: 'jobContentName',
    func: (params) => {
      return params.orderSource != '6';
    }
  },
  {
    label: '风险等级',
    key: 'riskLevelName',
    func: (params) => {
      return params.orderSource != '6';
    }
  },
  {
    label: '作业开始日期',
    key: 'riskJobDate',
    func: (params) => {
      return params.orderSource != '6';
    }
  },
  {
    label: '附件',
    key: 'uploadFileList',
    type: 'file:text',
    span: 24,
    func: (params) => {
      return params.taskType != 9;
    }
  }
];

function isOrder (params) {
  return params.orderSource && params.isWorkOrder == 1;
}

export const legacyDetail = [
  { label: '缺陷名称', key: 'defectNameName' },
  { label: '发现人', key: 'findUserName' },
  { label: '发现时间', key: 'findTime' },
  { label: '发现地点', key: 'findPlaceName' },
  { label: '设备类型', key: 'deviceTypeIdName' },
  { label: '缺陷类别', key: 'defectTypeName' },
  { label: '缺陷描述', key: 'taskDescription', span: 24 },
  { label: '预计消除时间', key: 'predictRecoverTime' },
  { label: '整改要求', key: 'rectifyReq', span: 24 },
  { label: '作业类别', key: 'jobCategoryName' },
  { label: '作业内容', key: 'jobContentName' },
  { label: '风险等级', key: 'riskLevelName' },
  { label: '作业开始日期', key: 'riskJobDate' },
  { label: '附件', key: 'uploadFileList', span: 24, type: 'file:text' }
];
