import { postAction } from '@/api/manage';
const baseUrl = process.env.VUE_APP_API_ERP_URL;
// 非工作计划工单

const getPage = (params) => postAction(baseUrl + '/pdca/workOrder/page', params);
const postOrderApprove = (params) => postAction(baseUrl + '/pdca/workOrder/approve', params);
const cancelOrder = (params) => postAction(baseUrl + '/pdca/workOrder/cancel', params);
const getWorkDetail = (params) => postAction(baseUrl + '/pdca/workOrder/detail', params);
const getCount = (params) => postAction(baseUrl + '/pdca/workOrder/count', params);
const getExperimentDate = (params) => postAction(baseUrl + '/pdca/workOrder/beforeExperimentDate', params);
const getPsaModel = (params) => postAction(baseUrl + '/pdca/workOrder/psaModel', params);
const getWorkExport = (params) => postAction(baseUrl + '/pdca/workOrder/export', params);
const getWorkExportDetail = (params) => postAction(baseUrl + '/pdca/workOrder/exportDetail', params);
const getOrderSource = (params) => postAction(baseUrl + '/pdca/workOrder/orderSource', params);
const delayOrTerminateApprove = (params) => postAction(baseUrl + '/pdca/workOrder/delayOrTerminate/approve', params);
const delayOrTerminateDelete = (params) => postAction(baseUrl + '/pdca/workOrder/delayOrTerminate/delete', params);
const delayOrTerminateDetail = (params) => postAction(baseUrl + '/pdca/workOrder/delayOrTerminate/detail', params);
const delayOrTerminateSponsor = (params) => postAction(baseUrl + '/pdca/workOrder/delayOrTerminate/sponsor', params);
const delayOrTerminateQuery = (params) => postAction(baseUrl + '/pdca/workOrder/delayOrTerminate/query', params);
const hangUp = (params) => postAction(baseUrl + '/pdca/workOrder/hangUp', params);

export {
  getPage,
  cancelOrder,
  postOrderApprove,
  getWorkDetail,
  getCount,
  getExperimentDate,
  getPsaModel,
  getWorkExport,
  getWorkExportDetail,
  getOrderSource,
  delayOrTerminateApprove,
  delayOrTerminateDelete,
  delayOrTerminateDetail,
  delayOrTerminateSponsor,
  delayOrTerminateQuery,
  hangUp
};
