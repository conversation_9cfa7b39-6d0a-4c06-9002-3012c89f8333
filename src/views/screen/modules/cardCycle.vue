<template>
  <!-- 氮化物、等效植树、二氧化碳量 -->
  <div class="card-cycle-model">
    <a-carousel dot-position="right" :autoplay="true">
      <div v-for="(item, index) in cardItems" :key="index" class="card-item">
        <template v-if="item.name == 'SO₂' || item.name=='SO₂减排'">
          <div class="card-icon icon_so2"></div>
        </template>
        <template v-else-if="item.name == '植树' || item.name=='等效植树'">
          <div class="card-icon icon_tree"></div>
        </template>
        <template v-else-if="item.name == 'CO₂' || item.name=='CO₂减排'">
          <div class="card-icon icon_co2"></div>
        </template>
        <template v-else>
          <div class="card-icon icon_rain"></div>
        </template>
        <div class="card-name">
          <span>{{item.name}}</span>
        </div>
        <div class="card-data">
          <div class="data-style">{{item.value}}</div>
          <div class="data-unit">{{item.unit}}</div>
        </div>
      </div>
    </a-carousel>
  </div>
</template>
<script>
import {
  getEnvironmentalData
} from '@/api/screen/screen';
export default {
  props: {
    isNeedRequest: {
      type: Boolean,
      default: true
    }
  },
  data () {
    return {
      cardItems: []
    };
  },
  mounted () {
    if (this.isNeedRequest) {
      this.getEnvironmental();
    }
  },
  methods: {
    getEnvironmental (isData) {
      if (isData) {
        window.setTimeout(function () {
          $('.slick-list').addClass('slick-list-height');
        }, 1000);
        this.cardItems = isData;
        return;
      }
      const self = this;
      getEnvironmentalData({}).then(res => {
        self.cardItems = (res.result_code == '1' ? res.result_data : []);
        window.setTimeout(function () {
          $('.slick-list').addClass('slick-list-height');
        }, 1000);
      });
    }
  }
};
</script>

<style lang="less" scoped>
  .card-cycle-model{
    margin-top: 15px;
    position: relative;
    height: 100px;
    width: 15vw
  }
  :deep(.slick-dots-right) {
    visibility: hidden;
  }
  :deep(.ant-carousel) {
    width: 15vw;
    height: 88px !important;
  }
  :deep(.slick-list) {
    width: 15vw;
  }
  :deep(.slick-slide) {
    opacity: 0.4;
  }
  :deep(.slick-active) {
    opacity: 1;
  }
  .card-item{
    width: 15vw !important;
    height: 44px !important;
    display: flex !important;
    align-items: center;
    line-height: 44px;
    font-size: 0.9375vw;
    color: #D8D8D8;
    .card-icon{
      width: 38px;
      height: 32px;
    }
    .icon_co2{
      background: url(../../../assets/images/screen/icon_co2.png) no-repeat;
      background-size: 100% 100%;
    }
    .icon_so2{
      background: url(../../../assets/images/screen/icon_so2.png) no-repeat;
      background-size: 100% 100%;
    }
    .icon_rain{
      background: url(../../../assets/images/screen/icon_rain.png) no-repeat;
      background-size: 100% 100%;
    }
    .icon_tree{
      background: url(../../../assets/images/screen/icon_tree.png) no-repeat;
      background-size: 100% 100%;
    }
    .card-name{
      padding: 0 13px;
      width: 6vw;
      font-family: PingFangSC-Medium, PingFang SC;
      font-weight: 500;
      color: #e2e2e2;
      line-height: 42px;
      -webkit-background-clip: text;
    }
    .card-data{
      width: calc(8vw - 38px);
      display: flex;
      align-items: center;
    }
    .data-unit{
      width: 56px;
      padding-left: 12px;
      font-family: PingFangSC-Medium, PingFang SC;
      font-weight: 500;
      color: #D8D8D8;
      -webkit-background-clip: text;
    }
    .data-style{
      width: calc(100% - 56px);
      text-align: right;
      font-size: 1.04vw;
      font-family: Arial-Black, Arial;
      font-weight: 900;
      color: #60B2F3;
    }
  }
</style>
<style>
  .slick-list-height{
    height: 88px !important;
  }
</style>
