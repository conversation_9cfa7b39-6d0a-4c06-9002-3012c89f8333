<template>
    <div class="point-content-info">
       <div class="common-point">
        <div class="point-title" v-show="commonList.length">
          <span class="text-blod">常用测点</span>
          <svg-icon @click="changeList" style="font-size: 16px; cursor: pointer;" :iconClass="isExpand ? 'arrow-down' : 'arrow-up'"></svg-icon>
        </div>
        <div class="common-point-list" v-show="isExpand">
          <div class="list" v-for="(item,index) in commonList" :key="item.id" :class="commonIndex === index ? 'solareye-color-primary text-blod' : ''"
            @mouseover="showIcon(item, index)" @mouseout="hideIcon(item, index)" @click="selectedCommonPoint(item, index)">
              <a-tooltip placement="rightTop">
                <template #title>
                  <div class="tips-info">
                    <span>{{ item.templateName }}</span>
                    <span class="describe">
                      <span>{{ item.deviceNameContent }}</span>
                      <span>{{ item.pointContent }}</span>
                    </span>
                  </div>
                </template>
                <div class="flex-between">
                  <div class="name">{{ item.templateName }}</div>
                  <svg-icon @click.stop="deleteCommonPoint(item)" v-show="item.isShow" style="font-size: 16px; cursor: pointer;" iconClass="remove"></svg-icon>
                </div>
              </a-tooltip>
          </div>
        </div>
       </div>
       <div class="custom-point">
        <div class="point-title">
          <a-tooltip placement='right' @mouseenter="showPointTooltip = true" @mouseleave="showPointTooltip = false"
            :visible="showPointTooltip" overlayClassName="insight-warning-tooltip">
            <template slot='title'>
              <span>请选择{{ tipsText }}</span>
            </template>
            <span class='text-blod'>自定义测点</span>
          </a-tooltip>
        </div>
        <div class="custom-point-list" :style="{height: customPointHeight + 'px'}">
          <a-tree ref="pointTree" checkable :treeData="treeDataPoint" v-model="bindChecked" @expand="pointExpand"
              @check="changePoint" class="main-tree" :expandedKeys="pointExpandKeys">
            <template #title="{ title }">
              <span :title='title'>{{ title }} </span>
            </template>
          </a-tree>
        </div>
       </div>
    </div>
</template>

<script>
import {
  queryDeviceTypePointByMultiPsId, viewTemplateList, deleteViewTemplate
} from '@/api/health/AlarmEvents.js';
import { debounce } from 'xe-utils';
export default {
  name: 'CustomPointArea',
  inject: ['updateUnit'],
  props: {
    fromPoint: {
      type: Boolean,
      default: false
    },
    isPoint: {
      type: Boolean,
      default: false
    },
    psList: {
      type: Array,
      default: () => []
    },
    bindCheckedList: {
      type: Array,
      default: () => []
    },
    pointExpandKeysList: {
      type: Array,
      default: () => []
    }
  },
  data () {
    return {
      showPointTooltip: false, // 请选择测点的动效提示
      tipsText: '', // 提示文字
      queryParams: {
        psId: []
      },
      // deviceType: [ '1', '4', '26', '37', '23', '24', '6', '13', '31', '30', '12', '29', '5', '7', '301' ],
      deviceType: [ '3', '17', '1', '4', '26', '37', '23', '24', '6', '13', '31', '30', '12', '29', '5', '7', '301', '8' ],
      selectedPoints: [], // 选中的设备测点key
      selectedPointsData: [], // 选中的设备测点数据
      customPointHeight: 100, // 测点高度
      bindChecked: [], // 选中测点双向绑定
      pointExpandKeys: [],
      unitPosition: [],
      treeDataPoint: [], // 测点树
      isExpand: true, // 常用测点是否展开
      commonIndex: null, // 常用测点默认选中
      commonList: [] // 常用测点列表
    };
  },
  watch: {
    psList: {
      async handler (val, old) {
        // 存psIdList
        if (val && val.length) {
          this.queryParams.psId = val.map(item => item.value);
          this.reset();
          await this.getPoint();
          // 保存常用测点数据
          if (this.fromPoint) {
            this.disposePoint();
          }
        }
      },
      immediate: true,
      deep: true
    },
    bindCheckedList: {
      handler (val, old) {
        this.selectedPointsDataTemplt = [];
        // 回显节点
        this.bindChecked = (val || []).map(item => item.point || item.pointKey);
        this.selectedPointsDataTemplt = val;
      },
      immediate: true,
      deep: true
    },
    pointExpandKeysList: {
      handler (val, old) {
        this.pointExpandKeys = this.pointExpandKeysList;
      },
      immediate: true,
      deep: true
    }
  },
  created () {
    // this.getPoint();
  },
  async mounted () {
    this.refreshPoint();
  },
  methods: {
    // 重置
    reset () {
      this.selectedPointsData = [];
      this.pointExpandKeys = [];
      this.bindChecked = [];
      if (!this.fromPoint) {
        this.$emit('selectedPoint', { selectedPointsData: this.selectedPointsData, changeRouter: false });
      }
      // this.getPoint();
    },
    // 回显数据处理 由于多个电站情况下选择的测点值  回显的时候判断测点是否包含当时选择的测点
    disposePoint () {
      this.$nextTick(() => {
        // 获取所有测点的key比对
        let allPointKeys = [];
        let allPointList = [];
        if (this.treeDataPoint && this.treeDataPoint.length) {
          this.treeDataPoint.forEach(item => {
            if (item.children && item.children.length) {
              allPointList = allPointList.concat(item.children);
            }
          });
        }
        allPointKeys = allPointList.map(item => item.key);
        this.selectedPointsData = [];
        this.selectedPointsDataTemplt.forEach(item => {
          if (allPointKeys.includes(item.pointKey || item.key)) {
            this.selectedPointsData.push({
              deviceType: item.deviceType,
              key: item.pointKey || item.point,
              point: item.pointKey || item.point,
              pointName: item.pointName,
              unit: item.pointUnit || item.unit
            });
          }
        });
        this.$emit('selectedPoint', { selectedPointsData: this.selectedPointsData, changeRouter: false });
      });
    },
    // 刷新测点
    async refreshPoint () {
      await viewTemplateList({}).then(res => {
        if (res.result_code === '1') {
          this.commonList = res.result_data || [];
          // 添加描述
          if (this.commonList && this.commonList.length) {
            this.commonList.forEach(item => {
              let pointList = item.points.map(item => item.pointName);
              let deviceNameList = [];
              let stationList = [];
              if (item.devices && item.devices.length) {
                deviceNameList = item.devices.map(item => item.psName + '/' + item.deviceName);
                // 电站psId集合
                stationList = Array.from(new Set(item.devices.map(item => item.psId)));
              }
              // 回显节点
              let types = Array.from(item.points.map(item => item.deviceType));
              let deviceList = [];
              stationList.forEach(list => {
                let obj = {
                  list: {
                    devices: (item.devices || []).filter(one => one.psId === list)
                  },
                  psId: list
                };
                this.$set(obj, 'psName', obj.list.devices[0].psName);
                deviceList.push(obj);
              });
              // 如果有设备 信息只存第一个
              if (deviceList && deviceList.length) {
                this.$set(deviceList[0].list, 'points', item.points);
                this.$set(deviceList[0].list, 'types', types);
              } else {
                // 如果没有设备  存入测点信息
                deviceList = [
                  {
                    list: {
                      devices: [],
                      points: item.points,
                      types
                    }
                  }
                ];
              }
              this.$set(item, 'deviceNameContent', deviceNameList.join('、'));
              this.$set(item, 'pointContent', pointList.join('、'));
              this.$set(item, 'infoList', deviceList);
            });
          }
        }
      }).catch();
      this.getCustomPointHeight();
    },
    // 改变测点
    changePoint (val, e) {
      // 获取当前选中的测点信息
      this.updateUnit(val, e);
      this.selectedPoints = val;
      this.selectedPointsData = this.allPoints.filter(item => this.selectedPoints.includes(item.key));
      // 没有测点的设备类型
      let noPoint = this.deviceType.filter(item => this.selectedPoints.includes(item));
      if (noPoint && noPoint.length) {
        noPoint.forEach(item => {
          this.selectedPointsData.push({
            deviceType: item,
            key: ''
          });
        });
      }
      this.$emit('selectedPoint', { selectedPointsData: this.selectedPointsData, changeRouter: true });
    },
    // 请选择电站的判断条件
    interceptorPsa (params, isShow) {
      this.tipsText = params;
      return new Promise((resolve, reject) => {
        this.showPointTooltip = isShow;
        setTimeout(() => {
          $('.insight-warning-tooltip').addClass('animation-jelly');
        }, 200);
        setTimeout(() => {
          $('.insight-warning-tooltip').removeClass('animation-jelly');
        }, 500);
        resolve(false);
      });
    },
    // 初始化双Y轴单位、位置变量
    initUnitPosition () {
      this.unitPosition = [
        {
          point: undefined,
          position: 'left',
          unit: undefined
        },
        {
          point: undefined,
          position: 'right',
          unit: undefined
        }
      ];
    },
    // 获取测点
    async getPoint () {
      let res;
      res = await queryDeviceTypePointByMultiPsId({
        psIdList: this.queryParams.psId,
        deviceType: this.deviceType,
        source: this.$route.params.source,
        point: this.$route.params.source == 1 && this.isPoint ? this.$route.params.list.points[0].point : '',
        psKey: this.$route.params.source == 1 && this.isPoint ? this.$route.params.list.devices[0].psKey : ''
      });
      // 测点类型与值集合 tips使用
      let pointsDeviceList = {};
      // 转换测点数据
      this.treeDataPoint = res.result_data.map(item => {
        this.$set(pointsDeviceList, item.deviceType, item.deviceTypeName);
        return {
          ...item,
          title: item.deviceTypeName,
          key: item.deviceType,
          checkable: true,
          selectable: false,
          isCheckAll: true,
          children: item.pointInfo && item.pointInfo.map(el => {
            return {
              ...el,
              title: el.pointName,
              key: el.point,
              deviceType: item.deviceType,
              checkable: true,
              selectable: false,
              isCheckAll: false
            };
          })
        };
      });
      this.$emit('pointsDeviceList', pointsDeviceList);
      // 存储所有测点
      this.allPoints = res.result_data.reduce((prev, cur) => {
        return prev.concat(cur.pointInfo.map(item => {
          return {
            ...item,
            deviceType: cur.deviceType,
            key: item.point
          };
        }));
      }, []);
      this.selectedPointsData = this.allPoints.filter(item => this.selectedPoints.includes(item.key));
      if (this.fromPoint) {
        this.pointExpandKeys = this.pointExpandKeysList;
      }

      this.unitArr = this.allPoints.map(item => item.unit);
    },
    // 删除常用测点
    deleteCommonPoint (row) {
      this.$confirm({
        title: '确认删除该测点?',
        okText: '确定',
        cancelText: '取消',
        onOk: () => {
          deleteViewTemplate({
            templateId: row.id
          })
            .then(res => {
              if (res.result_code === '1') {
                this.$message.success('删除成功!');
                this.refreshPoint();
              } else {
                this.$message.warning('删除失败');
              }
            })
            .catch(err => {
              console.log(err);
            });
        }
      });
    },
    // 测点树展开事件
    pointExpand (expandedKeys) {
      this.pointExpandKeys = expandedKeys;
    },
    // 展开收起
    changeList (val) {
      if (val === 'fromRouter') {
        // 诊断跳转过来收起
        this.isExpand = false;
      } else {
        this.isExpand = !this.isExpand;
      }
      this.getCustomPointHeight();
    },
    // 获取自定义测点高度
    getCustomPointHeight () {
      this.$nextTick(() => {
        let $searchHeight = window.document.getElementsByClassName('common-point-list');
        let height = 0;
        if ($searchHeight[0]) {
          height = $searchHeight[0].clientHeight;
        }
        this.customPointHeight = window.innerHeight - height - 56 - 40 - 24 - 24 - 21 - 24;
      });
    },
    // 鼠标移入显示close
    showIcon (row) {
      this.$set(row, 'isShow', true);
    },
    // 鼠标移出隐藏close
    hideIcon (row) {
      this.$set(row, 'isShow', false);
    },
    // 选中常用节点 回显
    selectedCommonPoint: debounce(function (item, index) {
      this.commonIndex = index;
      this.$emit('echoInfo', item.infoList);
    }, 500)
  }
};
</script>
<style lang="less" scoped>
.point-content-info {
  .point-title {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
  .common-point {
    .common-point-list {
      display: flex;
      flex-direction: column;
      .list {
        display: flex;
        padding-top: 20px;
        cursor: pointer;
        justify-content: space-between;
        align-items: center;
        &:hover {
          color: var(--zw-primary-color--default);
        }
        .name {
          width: 160px;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
        }
      }
    }
  }
  .custom-point {
    margin-top: 24px;
    .custom-point-list {
      height: calc(100% - 320px);
    }
  }
}
.tips-info {
  padding: 5px;
  display: flex;
  flex-direction: column;
  .describe {
    display: flex;
    flex-direction: column;
    padding-top: 10px;
    border-top: 1px solid var(--zw-border-color--default);
    margin-top: 5px;
    & > span {
      display: flex;
    }
  }
}
:deep(.main-tree) {
  overflow: auto;
  height: calc(100% - 40px);
}
</style>
<style lang='less'>
.point-tooltip-info{
  .ant-tooltip-content{
    min-width: 450px;
    max-width: 850px;
  }
}
</style>
