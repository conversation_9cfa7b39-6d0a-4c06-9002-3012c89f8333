import { getSopDetailForBus } from '@/api/config/digitalRegulation';

export default {
  props: {
    formData: {
      type: Object,
      default: {}
    },
    rowInfo: {
      type: Object,
      default: {}
    }
  },
  data () {
    return {
      receiveCommonKeys: ['cleanCap', 'cleanType', 'grassType', 'liablePerson', 'beforeExperimentDate',
        'taskDescription', 'findTime', 'predictRecoverTime', 'findPlace', 'findUser',
        'rectifyReq', 'deviceTypeId', 'deviceId', 'deviceName', 'deviceNo', 'isEngLegacy',
        'defectName', 'defectType', 'deviceType', 'riskLevel', 'erpSqJobRiskMappingId', 'totalRepairTime',
        'riskJobDate', 'uploadFileList', 'powerCutRange', 'defectHandleCondition'],
      enumTypes: {
        3: '详情',
        6: '领取',
        5: '指派',
        7: '执行',
        8: '验收',
        4: '审批'
      },
      executeCommonKeys: ['sceneCondition', 'faultCap', 'totalLossPower', 'overdueReason', 'meteoMaker', 'conditionRemark', 'stepVoList', 'sceneFileList']
    };
  },
  methods: {
    formatTangoTaskParams () {
      const map = {
        'id': this.formData.id,
        'btnType': '2',
        'taskId': this.formData.taskId,
        'flowSts': this.formData.flowSts,
        'workFlowId': this.formData.taskId,
        'taskDefKey': this.formData.taskDefKey,
        'processInstanceId': this.formData.processInstanceId
      };
      map.processDefKey = this.formData.flowDefKey;
      map.auditStatus = '3';
      map.psaId = this.formData.psaId;
      return map;
    },
    getCommonParams () {
      const order = this.formData;
      const rowInfo = this.rowInfo;
      return {
        id: order.id,
        taskId: order.taskId,
        taskDefKey: rowInfo.taskDefKey || order.taskDefKey,
        processInstanceId: rowInfo.processInstanceId || order.processInstanceId,
        processDefKey: rowInfo.processDefKey || order.processDefKey,
        otherSts: order.otherSts,
        updateTime: order.updateTime
      };
    },
    getStatus () {
      let order = this.rowInfo;
      return (order.step > 5 && order.processDefKey == 'activiti4273Workflow') || (order.step > 5 && order.processDefKey == 'activiti4773Workflow') || (order.step > 2 && order.processDefKey ==
        'activiti4873Workflow') || (order.processDefKey == 'activiti5073Workflow' && order.step > 1) || order.flowSts == '4' || order.otherSts == '4';
    },
    getStepList () {
      const deviceTypeId = this.formData.deviceTypeId || [];
      let val = deviceTypeId ? deviceTypeId[deviceTypeId.length - 1] : 0;
      getSopDetailForBus({
        taskType: this.formData.taskType,
        deviceType: val,
        riskLevel: this.formData.riskLevel
      }).then((res) => {
        this.formData.stepVoList = [];
        if (res.result_data && res.result_data.steps) {
          this.formData.stepVoList = res.result_data.steps.map((item) => {
            return {
              stepName: item.stepName,
              stepNo: item.stepNo,
              workDesc: '',
              uploadPictureList: [],
              uploadFileList: [],
              stepContent: item.stepContent,
              uploadExampleFileList: item.uploadFileList
            };
          });
        }
      });
    }
  }
};
