<template>
    <a-modal
      title="安全验证"
     :visible="visible"
      @ok="handleOk"
      @cancel="handleCancel"
      width="480px"
      wrapClassName="security_modal"
      :maskClosable="false"
    >
    <get-code :mobile="phone" @ok="getCode" ref="getCode" v-if="visible"></get-code>
     <template slot="footer">
        <a-button key="submit" type="primary" size="large" :loading="confirmLoading" @click="handleOk">
          确定
        </a-button>
      </template>
    </a-modal>
</template>
<script>
import GetCode from '@/components/com/GetCode';
export default {
  components: {
    GetCode
  },
  data () {
    return {
      visible: false,
      confirmLoading: false
    };
  },
  methods: {
    showModal (res) {
      this.phone = res ? res.phone : '';
      this.visible = true;
    },
    getCode (params) {
      if (params) {
        this.$emit('ok', params);
        this.visible = false;
      }
      this.confirmLoading = false;
    },
    handleOk (e) {
      this.confirmLoading = true;
      this.$refs.getCode.validateForm();
    },
    handleCancel () {
      this.visible = false;
      this.confirmLoading = false;
    }
  }
};
</script>
<style lang="less" scoped>
  :deep(.security_modal) {
    .ant-modal-header {
      border-bottom: none;
    }
    .ant-modal-footer {
      border-top: none;
      text-align: center;
      padding-bottom: 24px;
      button {
        width: 416px
      }
    }
    .ant-modal-title {
      font-size: 16px;
      font-weight: 600;
    }
    .ant-modal-header {
      padding: 32px 32px 0;
    }
    .ant-modal-close {
      top: 12px;
    }
    .ant-modal-body {
      padding: 16px 32px 0;
    }
  }
</style>
