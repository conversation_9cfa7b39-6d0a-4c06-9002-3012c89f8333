<template>
  <a-drawer
    width="100%"
    title="新增电站"
    :visible="visible"
    destroyOnClose
    @close="onClose(false)"
    :get-container="getDom"
    :wrap-style="{ position: 'absolute' }">
    <a-spin :spinning="loading" style="height:100%;">
      <div class="title">
        <span class="before"></span>
        <span>基本参数</span>
      </div>
      <div class="form-content" style="height: calc(100vh - 320px)">
        <a-form-model ref="baseForm" :model="baseForm" :rules="baseFormRules" :labelCol="{ style:'width: 160px' }" :wrapperCol="{ style:'width: calc(100% - 160px)' }">
          <a-row>
            <a-col :xl='8' :sm='12' :xs='24'>
              <a-form-model-item label="电站名称" prop="psName">
                <a-input v-model="baseForm.psName" :maxLength="50" @blur="baseForm.psName = $trim($event)" placeholder="输入电站名称"/>
              </a-form-model-item>
            </a-col>
            <a-col :xl='8' :sm='12' :xs='24'>
              <a-form-model-item label="上层组织" prop="psaId">
                <ps-tree-select ref="tree" @change="getStationChange" v-model="baseForm.psaId" :isPsaDisable="true"  style="width: 100%;" />
              </a-form-model-item>
            </a-col>
            <a-col :xl='8' :sm='12' :xs='24'>
              <a-form-model-item label="电站状态" prop="validFlag">
                <a-select v-model="baseForm.validFlag" placeholder="请选择">
                  <a-select-option value="1">已接入</a-select-option>
                  <a-select-option value="2">已关闭</a-select-option>
                  <a-select-option value="3">未接入</a-select-option>
                </a-select>
              </a-form-model-item>
            </a-col>
            <a-col :xl='8' :sm='12' :xs='24'>
              <a-form-model-item label="装机功率(kWp)" prop="totalCapcity">
                <a-input-number :max="999999999999999" :min="0" :precision="6" v-model="baseForm.totalCapcity" placeholder="输入装机功率" style="width: 100%;"/>
              </a-form-model-item>
            </a-col>
            <a-col :xl='8' :sm='12' :xs='24'>
              <a-form-model-item label="经度" prop="longitude">
                <a-input readOnly v-model="baseForm.longitude" @click="setLoaction" placeHolder="设置经度"/>
              </a-form-model-item>
            </a-col>
            <a-col :xl='8' :sm='12' :xs='24'>
              <a-form-model-item label="纬度" prop="latitude">
                <a-input readOnly v-model="baseForm.latitude" @click="setLoaction" placeHolder="设置纬度"/>
              </a-form-model-item>
            </a-col>
            <a-col :xl='8' :sm='12' :xs='24'>
              <a-form-model-item label="所属区域(省/市/区)" prop="psLocationList">
                <g-cascader :options="options" v-model="baseForm.psLocationList" @change="getLabel"  placeholder="请选择" style="width:100%;" />
              </a-form-model-item>
            </a-col>
            <a-col :xl='8' :sm='12' :xs='24'>
              <a-form-model-item label="详细地址" prop="addressDetail">
                <a-input v-model="baseForm.addressDetail" :maxLength="50" @blur="baseForm.addressDetail = $trim($event)" placeholder="输入详细地址"/>
              </a-form-model-item>
            </a-col>
            <a-col :xl='8' :sm='12' :xs='24'>
              <a-form-model-item label="建站时间" prop="installDate">
                <a-date-picker v-model="baseForm.installDate" format="YYYY-MM-DD" placeholder="请选择" style="width:100%;"></a-date-picker>
              </a-form-model-item>
            </a-col>
          </a-row>
        </a-form-model>
      </div>
      <!-- <div class="title">
        <span class="before"></span>
        <span>采集器</span>
        <div class="operation-btn">
          <a-button v-show="list.length < 1000" class="solar-eye-btn-primary" @click="addForm">添加</a-button>
        </div>
      </div>
      <div class="form-list" id="formList">
        <RecycleScroller
          class="scroller"
          :items="list"
          :item-size="64"
          v-if="list.length"
          key-field="key"
        >
          <template v-slot="{ item, index }">
            <a-form-model :ref="'collectorForm' + index" :model="item" :rules="collectorFormRules" :labelCol="{ style:'width: 140px' }" :wrapperCol="{ style:'width: calc(100% - 140px)' }">
              <a-row>
                <a-col :span="5">
                  <a-form-model-item label="厂家" prop="factory">
                    <a-input v-model="item.factory" :maxLength="50" @blur="baseForm.factory = $trim($event)" placeholder="输入厂家名称"/>
                  </a-form-model-item>
                </a-col>
                <a-col :span="4">
                  <a-form-model-item label="用途" prop="purpose" :labelCol="{ style:'width: 80px' }" :wrapperCol="{ style:'width: calc(100% - 80px)' }">
                    <a-select v-model="item.purpose"  placeholder="请选择" :ref="'select' + index">
                      <a-select-option v-for="item in purposeOptions" :value="item.secondTypeCode" :key="item.secondTypeCode">
                        {{ item.secondName }}
                      </a-select-option>
                    </a-select>
                  </a-form-model-item>
                </a-col>
                <a-col :span="5">
                  <a-form-model-item label="通讯模块SN" prop="sn">
                    <a-input v-model="item.sn" :maxLength="20" placeholder="输入SN号"/>
                  </a-form-model-item>
                </a-col>
                <a-col :span="5">
                  <a-form-model-item label="描述" prop="description" :labelCol="{ style:'width: 80px' }" :wrapperCol="{ style:'width: calc(100% - 80px)' }">
                    <a-input v-model="item.description" :maxLength="100" placeholder="输入描述"/>
                  </a-form-model-item>
                </a-col>
                <a-col :span="5">
                  <div class="form-btn">
                    状态: <span class="status">未接入</span>
                    <a-button size="default" @click="deleteForm(index)" class="solar-eye-btn-primary-cancel">删除</a-button>
                  </div>

                </a-col>
              </a-row>
            </a-form-model>
          </template>
        </RecycleScroller>
      </div> -->
      <div class="drawer-form-foot">
        <a-button  @click="onClose(false)" class="solar-eye-btn-primary-cancel">取消</a-button>
        <a-button class="solar-eye-btn-primary" @click="saveData">保存</a-button>
      </div>
    </a-spin>
    <location ref="location" @setLngLat="setLngLat"/>
  </a-drawer>
</template>
<script>
import { areaTree } from '@/api/isolarErp/safetyquality/safetyRisk';
import { StationMixins } from '../../mixins/station.js';
import { addPowerStation } from '@/api/health/healthapi.js';
import moment from 'moment';
export default {
  name: 'StationAdd',
  mixins: [StationMixins],
  data () {
    return {
      visible: false,
      loading: false
    };
  },
  created () {
    this.queryArea();
  },
  methods: {
    moment,
    getDom () {
      // this.$nextTick(() => {
      //   document.getElementById('formList').addEventListener('scroll', this.scrollEvent, true);
      // });
      return document.getElementsByClassName('station-model')[0];
    },
    init () {
      this.visible = true;
      this.baseForm = {
        psName: '',
        psaId: '',
        validFlag: undefined,
        totalCapcity: '',
        longitude: '',
        latitude: '',
        psLocationList: [],
        psLocation: '',
        addressDetail: '',
        installDate: '',
        saveFlag: false
      };
      this.list = [];
    },
    onClose (flag) {
      this.visible = false;
      this.saveFlag = false;
      this.$refs.baseForm.clearValidate();
      if (flag) {
        this.$emit('refresh');
      }
    },
    getStationChange (val, node) {
      this.baseForm.psaId = node.id;
    },
    // 查询范围 下拉宽内容 国省市区
    queryArea () {
      areaTree({}).then((res) => {
        if (res.result_code == '1') {
          this.options = res.result_data;
          if (this.areaCode) {
            this.place = this.familyTree(res.result_data, this.areaCode);
          }
        }
      });
    },
    // 根据节点找到父节点一直到顶级节点
    familyTree (array, id) {
      let temp = [];
      const forFn = function (arr, id) {
        for (let i = 0; i < arr.length; i++) {
          let item = arr[i];
          if (item.value == id) {
            temp.unshift(item.pid);
            forFn(array, item.pid);
            break;
          } else {
            if (item.children) {
              forFn(item.children, id);
            }
          }
        }
      };
      forFn(array, id);
      temp.push(id);
      let type = temp.filter(item => item != '0' && item != '-1');
      return type;
    },
    async saveData () {
      this.saveFlag = true;
      let valid1 = await this.$refs.baseForm.validate().catch(() => {});
      let valid2 = true;
      this.list.forEach((item, index) => {
        if (!item.factory || !item.purpose || !item.sn) {
          valid2 = false;
        }
        if (this.$refs['collectorForm' + index]) {
          this.$refs['collectorForm' + index].validateField(['factory', 'purpose', 'sn']);
        }
      });
      if (valid1 && valid2) {
        let obj = {
          ...this.baseForm,
          collector: this.list
        };
        obj.installDate = obj.installDate ? moment(obj.installDate).format('YYYY-MM-DD HH:mm:ss') : undefined;
        this.loading = true;
        addPowerStation(obj).then(res => {
          if (res.result_code == '1') {
            this.onClose(true);
            this.$message.success('新增电站成功！');
          }
          this.loading = false;
        }).catch(() => { this.loading = false; });
      }
    }
  }
};
</script>
<style lang="less" scoped>
:deep(.ant-drawer-body) {
  padding: 24px 0;
  height: calc(100% - 55px);
}
:deep(.ant-form-item) {
  width: 100%;
  display: inline-flex;
}
:deep(.ant-spin-container) {
  height: 100%;
}
.title {
  font-size: 16px;
  font-weight: 500;
  color: #5b5b5b;
  padding-bottom: 12px;
  border-bottom: 1px solid #eee;
  margin-bottom: 24px;
  position: relative;

  .before {
    width: 4px;
    height: 15px;
    border-left: 4px solid #FF8F33;
    margin-right: 16px;
    border-radius: 0px 2px 2px 0px;
  }
}
.form-content {
  padding: 0 24px;
}
.form-btn {
  padding: 4px 24px 0 48px;
  .status {
    margin: 0 24px 0 8px;
    color: #9D9997;
  }
}
.form-list {
  height: calc(100% - 344px);
  overflow: auto;
}
.operation-btn {
  position: absolute;
  right: 24px;
  top: -6px;
  button + button {
    margin-left: 8px;
  }
}
:root[data-theme='dark'] {
  .title {
    color: #fff;
    border-bottom: 1px solid #354661;
    .before {
      border-left-color: #60CAFE;
    }
  }

}
.scroller {
  height: 100%;
}
</style>
