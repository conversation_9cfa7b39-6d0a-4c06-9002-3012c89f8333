/*
 * @Author: your name
 * @Date: 2021-03-15 17:04:17
 * @LastEditTime: 2021-03-26 15:57:18
 * @LastEditors: Please set LastEditors
 * @Description: In User Settings Edit
 * @FilePath: \solareye-vue2.0\src\api\common.js
 */
import {
  postAction
} from '@/api/common_gy/erp-manage';
// 通过账号获取电站列表
export const getPSByUserAccount = (params) => postAction('/common/management/getPSByUserAccount', params);

// 通过电站Id获取运维人员列表
export const userListByPSId = (params) => postAction('/common/management/userListByPSId', params);

// 通过电站Id获取运维人员列表
export const getAssignListByPSId = (params) => postAction('/common/management/getAssignListByPSId', params);

// 获取字典信息
export const getDictData = (params) => postAction('/common/management/getDictData', params);

// 获取电站等级
export const getStationInfo = (params) => postAction('/common/management/getStationInfo', params);

// 获取设备类型级联
export const getDeviecTypeTree = (params) => postAction('/common/management/getDeviecTypeTree', params);

// 获取侧边栏树
export const getTreeByUser = (params) => postAction('/common/management/getErpProjectTreeByAccount', params);

// 获取用户信息
export const getUserInfoByName = (params) => postAction('/common/management/getUserInfoByName', params);

// 下载模板文件
export const downLoadTmp = (params) => postAction('/common/management/downLoadTmp', params);

// 获取省市区树
export const getAreaTree = (params) => postAction('/common/management/getAreaTree', params);
