import WujieVue from 'wujie-vue2';
import hostMap from './hostMap';
import { logoutToSolareye } from '@/utils/index';
import store from '@/store';
// import credentialsFetch from './fetch';
import lifecycles from './lifecycle';

import {
  EventTargetPlugin
} from 'wujie-polyfill';
const isProduction = process.env.NODE_ENV === 'production';
const { setupApp, preloadApp, bus } = WujieVue;
Vue.use(WujieVue);

Vue.config.productionTip = false;


// 子应用token失效回调
const tokenLose = () => {
  store.dispatch('Logout').then(() => {
    console.log('tokenLose')
    logoutToSolareye();
  });
};
console.log('store', store)
const microFromName = 'health';
const degrade = window.localStorage.getItem('degrade') === 'true' || !window.Proxy || !window.CustomElementRegistry;
const params = {
  microFrom: microFromName,
  theme: store.getters.theme,
  color: store.getters.color
};
const props = {
  tokenLose,
  ...params
};
/**
 * 大部分业务无需设置 attrs
 * 此处修正 iframe 的 src，是防止github pages csp报错
 * 因为默认是只有 host+port，没有携带路径
 */
const attrs = isProduction ? { src: hostMap('//localhost:3000/') } : {};
/**
 * 配置应用，主要是设置默认配置
 * preloadApp、startApp的配置会基于这个配置做覆盖
 */
setupApp({
  name: 'common',
  url: hostMap('//localhost:3001/'),
  attrs,
  exec: true,
  props,
  // fetch: credentialsFetch,
  degrade,
  ...lifecycles,
  plugins: [{
    // element 为真正插入的元素，iframeWindow 为子应用的 window, rawElement为原始插入元素
    appendOrInsertElementHook (element, iframeWindow, rawElement) {
      if (
        element.nodeName === 'svg' && (element.getAttribute('aria-hidden') === 'true' || element.style.display === 'none' || element.style.visibility === 'hidden' || (element.style.height === '0px' && element.style.width === '0px'))
      ) {
        iframeWindow.__WUJIE.styleSheetElements.push(element);
      }
    }

  }, EventTargetPlugin()]
});
if (window.localStorage.getItem('preload') !== 'false') {
  // 预加载
  preloadApp({
    name: 'common'
    // props
  });
}

export {
  bus,
  tokenLose,
  microFromName
};
