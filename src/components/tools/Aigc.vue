<!-- Aigc弹窗组件 -->
<template>
  <a-drawer
    :closable="false"
    :visible="visible"
    :get-container="getBody"
    :maskStyle="{opacity:0,background:'transparent',animation:'none'}"
    maskClosable
    width="576"
    :z-index='99999999'
    @close="onClose"
    :bodyStyle="{padding:0,margin:0,overflow:'hidden',height:'100%'}"
  >
    <iframe class="chat-area" ref="chat"
            @load="loadIfream"
            v-if="visible"
            allow="clipboard-read; clipboard-write "
            :src="`${AigcURL}?query=${query}`" />
  </a-drawer>
</template>

<script>
import { ACCESS_TOKEN, TENANT_ID } from '../../store/mutation-types';

export default {
  name: 'Aigc',
  props: {},
  data () {
    return {
      testURL: 'http://*********:8001/#/index',
      AigcURL: process.env.VUE_APP_AIGC_URL,
      prefix: process.env.VUE_APP_AIGC_API_URL,
      visible: false,
      query: {
      }
    };
  },
  created () {
    this.initPage();
    window.addEventListener('message', this.eventLister, true);
  },
  beforeDestroy () {
    window.removeEventListener('message', this.eventLister, true);
  },
  methods: {
    initPage () {
      console.log(this.prefix);
      const params = {
        'sysTenantId': Vue.ls.get(TENANT_ID),
        'token': Vue.ls.get(ACCESS_TOKEN),
        prefix: this.prefix,
        'pageParams': {
          'type': 1
        },
        'statusHeight': 0
      };
      this.query = encodeURI(JSON.stringify(params));
    },
    getBody () {
      return document.body;
    },
    eventLister (e) {
      if (e.data == 'close') {
        this.onClose();
      }
    },
    open () {
      this.visible = true;
    },
    onClose () {
      this.visible = false;
    },

    loadIfream () {
      let timer = setTimeout(() => {
        this.$refs.chat.contentWindow.postMessage(1, '*');
        clearTimeout(timer);
        timer = null;
      }, 1000);
    }
  }
};
</script>

<style lang="less" scoped>
.ant-drawer-body{
  padding: 0!important;
}
.chat-area{
  width: 100%;
  height: 100%;
  margin:0;
  border: 0;
  padding: 0;
}
</style>
