<template>
  <a-drawer
    width="100%"
    :visible="drawerInfo.visible"
    :body-style="{ padding: '0' }"
    :drawerStyle="{ padding: 0 }"
    @close="onClose"
    :get-container="dom"
    :wrap-style="{ position: 'absolute' }"
    title="选择"
  >
    <a-row>
      <a-form-model
        :model="form"
        :rules="rules"
        layout="horizontal"
        v-bind="{
          labelCol: { span: 4 },
          wrapperCol: { span: 20 },
        }"
        ref="ruleForm"
        class="solareye-transfer"
      >
        <a-form-model-item label="设备类型" style="margin: 16px 0 0; width: 50%">
          <a-input v-model="deviceName" readOnly></a-input>
        </a-form-model-item>
        <a-col :span="1"></a-col>
        <a-col :span="8">
          <div :style="{ height: height + 'px' }" class="share-content" style="float:right">
            <div class="share-content-title">
              <a-checkbox :indeterminate="indeterminateLeft" v-model="checkedLeft" @change="onCheckLeftAllChange" :disabled="drawerInfo.type == 'view'">
                已选{{ leftSelected.length }}/{{ filterLeftData.length }}
              </a-checkbox>
              <div>
                只看无此类设备
                <a-switch v-model="leftMeto" />
              </div>
            </div>
            <div style="padding: 16px">
              <ps-tree-select v-if="drawerInfo.visible" @change="getChild" :isPsaDisable="false" :isQueryPs="1" :isPsManage="true" style="width: 100%;" />
            <div :style="{ height: boxHeight + 'px' }">
              <a-checkbox-group
                :options="filterLeftData"
                v-model="leftSelected"
                @change="onChangeLeft"
                :disabled="drawerInfo.type == 'view'"
                style="overflow: auto; margin-top: 8px; width: 100%"
                :style="{ height: boxHeight + 'px' }"
              >
                <span slot="label" slot-scope="option" :style="{ color: option.hasMeteo ? '#FF8F33' : '' }">{{
                  option.psName
                }}</span>
              </a-checkbox-group>
            </div>
            </div>

          </div>
        </a-col>
        <a-col :span="3" :style="{ height: height + 'px' }" class="middle-btn ant-transfer-operation">
          <a-button :disabled="leftSelected.length === 0" @click="moveToRight"><a-icon type="right" /> 右移</a-button>
          <a-button :disabled="rightSelected.length === 0" @click="moveToLeft"> <a-icon type="left" style="margin-bottom: 4px" /> 左移</a-button>
        </a-col>
        <a-col :span="8">
          <div :style="{ height: height + 'px' }" class="share-content" style="float:left">
            <div class="share-content-title">
              <a-checkbox :indeterminate="indeterminateRight" v-model="checkedRight" @change="onCheckRightAllChange" :disabled="drawerInfo.type == 'view'">
                已选{{ rightSelected.length }}/{{ filterRightftData.length }}
              </a-checkbox>
              <div>
                只看无此类设备
                <a-switch v-model="rightMeto" />
              </div>
            </div>
          <div style="padding: 16px">
            <ps-tree-select v-if="drawerInfo.visible" @change="getRightChild" :isPsaDisable="false" :isQueryPs="1" :isPsManage="true"   style="width: 100%;" />
            <div :style="{ height: boxHeight + 'px' }">
              <a-checkbox-group
                :options="filterRightftData"
                v-model="rightSelected"
                @change="onChangeRight"
                :disabled="drawerInfo.type == 'view'"
                style="overflow: auto; width: 100%; margin-top: 8px"
                :style="{ height: boxHeight + 'px' }"
              >
                <span slot="label" slot-scope="option" :style="{ color: option.hasMeteo ? '#FF8F33' : '' }">{{
                  option.psName
                }}</span>
              </a-checkbox-group>
            </div>
             </div>
          </div>
        </a-col>
      </a-form-model>
    </a-row>
  </a-drawer>
</template>
<script>
import { selectMeteoPsInfo } from '@/api/health/healthapi.js';

let innerHeight = window.innerHeight - 60 - 40 - 24;
export default {
  name: 'ShareStationDrawer',
  inject: ['provideData'],
  props: {
    psId: {
      type: [String, Number],
      default: ''
    },
    deviceName: {
      type: String,
      default: ''
    }
  },
  data () {
    return {
      drawerInfo: {
        title: '',
        visible: false,
        type: ''
      },
      form: {
        deviceType: '环境监测仪'
      },
      leftSource: [],
      leftSelected: [],
      rightSource: [],
      rightSelected: [],
      tagetKeys: [],
      rules: {},
      indeterminate: false,
      height: innerHeight - 180,
      rightSourceList: [],
      leftMeto: false,
      rightMeto: false,
      checkedLeft: false,
      checkedRight: false,
      indeterminateRight: true,
      indeterminateLeft: true,
      rightPsId: '',
      treeLsit: [],
      isRightSearch: false
    };
  },
  created () {

  },
  watch: {},
  computed: {
    boxHeight () {
      return this.height - 140;
    },
    filterLeftData () {
      return this.leftSource.filter((item) => {
        if (!this.leftMeto) {
          return this.rightSourceList.indexOf(item.psId) === -1;
        } else {
          return this.rightSourceList.indexOf(item.psId) === -1 && !item.hasMeteo;
        }
      });
    },
    filterRightftData () {
      return this.rightSource.filter((item) => {
        let treeLsit = this.treeLsit;
        let isSelectTree = treeLsit && treeLsit.length > 0;
        if (this.rightMeto) {
          if (treeLsit) {
            return treeLsit.indexOf(item.psId) > -1 && !item.hasMeteo;
          } else {
            return !item.hasMeteo;
          }
        } else {
          if (isSelectTree || this.isRightSearch) {
            return treeLsit.indexOf(item.psId) > -1;
          } else {
            return true;
          }
        }
      });
    }
  },
  mounted () {
    this.dom = document.getElementsByClassName('station-model')[0];
  },
  methods: {
    /**
     * 初始化抽屉
     * params {Object} column 点击列的对象
     * params {Object} title ，type 等基本信息
     * */
    initDrawer (column, obj) {
      this.loading = false;
      this.drawerInfo = Object.assign({}, this.drawerInfo, obj);
      for (let item in this.form) {
        this.psId = '';
        this.rightPsId = '';
        if (item != 'id') {
          this.form[item] = '';
        }
      }
      if (obj.type != 'add') {
        this.tagetKeys = column.sharePsList;
      }
      this.getShareStation({ psId: this.provideData.psId }, true);
      this.getShareStation({ psId: this.provideData.psId }, false);
    },
    onCheckLeftAllChange (e) {
      this.indeterminateLeft = !e.target.checked;
      this.leftSelected = [];
      if (e.target.checked) {
        this.filterLeftData.forEach((item) => {
          this.leftSelected.push(item.psId);
        });
      }
    },
    onCheckRightAllChange (e) {
      this.indeterminateRight = !e.target.checked;
      this.rightSelected = [];
      if (e.target.checked) {
        this.filterRightftData.forEach((item) => {
          this.rightSelected.push(item.psId);
        });
      }
    },
    getChild (val, node) {
      let params = {
        treePsId: '',
        treeOrgId: '',
        treePsaId: ''
      };
      if (node.isPsa == '0' && !node.isPs) {
        params.treeOrgId = node.id;
      } else if (node.isPsa == '1') {
        params.treePsaId = node.id;
      } else if (node.isPs == 1) {
        params.treePsId = node.id;
      }
      params.depCodes = node.orgCode;
      params.psId = this.provideData.psId;
      this.getShareStation(params, true);
    },
    getRightChild (val, node) {
      let params = {
        treePsId: '',
        treeOrgId: '',
        treePsaId: ''
      };
      if (node.isPsa == '0' && !node.isPs) {
        params.treeOrgId = node.id;
      } else if (node.isPsa == '1') {
        params.treePsaId = node.id;
      } else if (node.isPs == 1) {
        params.treePsId = node.id;
      }
      params.depCodes = node.orgCode;
      params.psId = this.provideData.psId;
      selectMeteoPsInfo(params)
        .then((res) => {
          this.treeLsit = res.result_data.map((item) => {
            return item.psId + '';
          });
          this.isRightSearch = true;
        })
        .catch(() => {});
      // let arr = obj.node.children;
      // this.treeLsit = [];
      // if (arr && arr.length > 0) {
      //   arr.forEach((item) => {
      //     if (item.children) {
      //       this.getTreePsList(item.children);
      //     } else {
      //       this.treeLsit.push(item.value);
      //     }
      //   });
      // } else {
      //   this.treeLsit.push(obj.psId);
      // }
      // console.log(this.treeLsit);
      // this.isRightSearch = true;
    },
    getTreePsList (arr) {
      // let psIdList = []
      arr.forEach((item) => {
        if (item.children) {
          this.getTreePsList(item.children);
        } else {
          this.treeLsit.push(item.value);
        }
      });
    },
    getShareStation (params, flag) {
      params.type = flag ? 0 : 1;
      selectMeteoPsInfo(params)
        .then((res) => {
          res.result_data.map((item) => {
            item.psId = item.psId.toString();
            item.value = item.psId.toString();
            item.lable = item.psName;
            if (!flag) {
              this.rightSourceList.push(item.psId);
            }
          });
          if (flag) {
            this.leftSource = res.result_data;
          } else {
            this.rightSource = res.result_data;
          }
        })
        .catch(() => {});
    },
    onChangeLeft (value) {
      if (this.leftSelected.length === this.filterLeftData.length) {
        this.indeterminateLeft = false;
        this.checkedLeft = true;
      } else {
        this.indeterminateLeft = true;
        this.checkedLeft = false;
      }
    },
    onChangeRight () {
      if (this.rightSelected.length === this.filterRightftData.length) {
        this.indeterminateRight = false;
        this.checkedRight = true;
      } else {
        this.indeterminateRight = true;
        this.checkedRight = false;
      }
    },
    onClose () {
      this.$emit('listenChange', this.rightSourceList);
      this.drawerInfo.visible = false;
    },
    moveToRight () {
      this.leftSource.forEach((item) => {
        if (this.leftSelected.indexOf(item.psId) > -1) {
          this.rightSourceList.push(item.psId);
          this.rightSource.push(item);
        }
      });
      this.$forceUpdate();
      this.indeterminateLeft = true;
      this.leftSelected = [];
    },
    moveToLeft () {
      this.rightSourceList = [];
      this.rightSource = this.rightSource.filter((item) => {
        if (this.rightSelected.indexOf(item.psId) === -1) {
          this.rightSourceList.push(item.psId);
        }
        return this.rightSelected.indexOf(item.psId) === -1;
      });
      this.indeterminateRight = true;
      this.rightSelected = [];
    }
  }
};
</script>
<style scoped lang='less'>
:deep(.ant-checkbox-group-item) {
  display: block;
  line-height: 24px;
}

.share-content-title {
  display: flex;
  padding: 21px 12px;
  justify-content: space-between;
}
.share-content {
  border-radius: 4px;
  margin: 16px;
  @media screen and (max-width: 1920px)  {
    width: 70%;
  }
  @media screen and(max-width: 1366px) {
    width: 100%;
  }
}
.middle-btn {
  display: inline-flex;
  align-items: center;
  flex-direction: row;
  justify-content: center;
  color: white !important;
}
</style>
