import { axios } from '@/utils/erp-request';
const url = process.env.VUE_APP_TANGO_BASE_URL;
// const url =  "http://192.168.157.178:8088"

/**
 * 文件管理-电站模型数据列表 + 检索
 * @param {Object} psaId ，uploadDate， dataType
 */
export function getModelResList (data) {
  return axios({
    url: url + '/model/resources/modelResList',
    method: 'post',
    data
  });
}

/**
 * 资源管理:电站模型数据-上传文件
 * @export
 * @param {*} psaId ，uploadFileList， dataType
 * @return {*}
 */
export function uploadModelRes (data) {
  return axios({
    url: url + '/model/resources/modelResUpload',
    method: 'post',
    data
  });
}

export function replaceAllViewMap (data) {
  return axios({
    url: url + '/model/resources/replace',
    method: 'post',
    data
  });
}

export function checkFileExits (data) {
  return axios({
    url: url + '/model/resources/checkFile',
    method: 'post',
    data
  });
}

/**
 * 资源管理:电站模型数据-批量下载 + 下载
 * @export
 * @param {*}  psaId ，uploadFileList，dataType
 * @return {*}
 */
export function modelResDownload (data) {
  return axios({
    url: url + '/model/resources/modelResDownload',
    method: 'post',
    data
  });
}

/**
 * 资源管理:电站模型数据批量删除
 * @export
 * @param {*} fileIds
 * @return {*}
 */
export function modelResDelete (data) {
  return axios({
    url: url + '/model/resources/modelResDelete',
    method: 'post',
    data
  });
}

/**
 * 资源管理:电站任务资源数据-列表 + 检索
 * @export
 * @param {*}   taskNum uploadDate, taskId
 * @return {*}
 */
export function getTaskResList (data) {
  return axios({
    url: url + '/model/resources/taskResList',
    method: 'post',
    data
  });
}

/**
 * 资源管理:电站任务资源数据--批量下载 + 下载
 * @export
 * @param {*}  taskNum ，uploadFileList， taskId
 * @return {*}
 */
export function taskResDownload (data) {
  return axios({
    url: url + '/model/resources/taskResDownload',
    method: 'post',
    data
  });
}

/**
 * 资源管理:电站任务资源数据批量删除
 * @export
 * @param {*} fileIds
 * @return {*}
 */
export function taskResDelete (data) {
  return axios({
    url: url + '/model/resources/taskResDelete',
    method: 'post',
    data
  });
}

/**
 * 目录管理:获取目录
 * @export
 * @param {*} fileIds
 * @return {*}
 */
export function getCatalogue (data) {
  return axios({
    url: url + '/model/resources/getCatalogue',
    method: 'post',
    data
  });
}

/**
 * OSS凭证获取
 * @export
 * @param {*} data
 * @return {*}
 */
export function getStsToken (data) {
  return axios({
    url: url + '/model/resources/getStsToken',
    method: 'post',
    data
  });
}

export function checkMap (data) {
  return axios({
    url: url + '/model/resources/checkMap',
    method: 'post',
    data
  });
}

export function deleteUpload (data) {
  return axios({
    url: url + '/task/delectFile',
    method: 'post',
    data
  });
}

export function checkPatRouteAndVisLight (data) {
  return axios({
    url: url + '/model/resources/checkPatRouteAndVisLight',
    method: 'post',
    data
  });
}
