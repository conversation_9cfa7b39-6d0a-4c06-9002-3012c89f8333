<template>
  <div class="container">
    <div class="preview-box" >
      <div v-for="(item, index) in testProjectList" :key="index">
        <strong class="name">{{ index + 1 }}.试验项目：</strong>
        <span>{{ getLabel(item.projectName, dictFn().pre_test_project_type) }}</span>
        <TableItem
          v-for="(rowTableItem, rowTableItemIndex) in item.projectTableList"
          :key="rowTableItemIndex"
          :row-list="rowTableItem.projectTitleList.filter((j) => j.titleType === 1)"
          :col-list="rowTableItem.projectTitleList.filter((j) => j.titleType === 2)"
          :project-table-remark="rowTableItem.projectTableRemark"
          :is-have-sub-title="rowTableItem.isHaveSubTitle"
        />
      </div>
    </div>
  </div>
</template>

<script>
import TableItem from '@views/preExperiment/template/modules/TableItem.vue';
import { transformRequestParam } from '../js/utils';
export default {
  inject: ['dictFn'],
  components: { TableItem },
  data () {
    return {
      testProjectList: [],
      loading: false
    };
  },
  methods: {
    reset () {},
    init (_, data) {
      this.afterVisibleChange(data);
      return '预览';
    },
    afterVisibleChange (data) {
      this.testProjectList = transformRequestParam(data).projectList.reverse();
    }
  }
};
</script>

<style lang="less" scoped>
.container {
  background: #e7e7e7;
  padding: 12px 0;
  display: flex;
  justify-content: center;
  overflow: hidden;
  min-height: 100%;
  .preview-box {
    width: 50vw;
    background: #fefefe;
    padding: 12px;
    overflow: auto;
    .name {
      margin-bottom: 16px;
      display: inline-block;
      margin-left: 24px;
    }
  }
}

:root[data-theme='dark'] {
  .container {
    background: #172134;
    .preview-box {
      background: #0e182f;
    }
  }
}
</style>
