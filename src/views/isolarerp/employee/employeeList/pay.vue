<template>
  <!-- 员工薪资 -->
  <a-row>
    <a-spin :spinning="loading">
      <a-col :span="24" class="top-btn-group">
        <a-button v-if="type" size="default" class="solar-eye-btn-primary" @click="addPayInfo" style="margin-right:10px;">新增</a-button>
        <a-button v-if="type" size="default" @click="deletePay">删除</a-button>
      </a-col>
      <a-col :span="24">
        <vxe-table class="pay-table" ref="payTable" :data="payList" :edit-rules="rules" :edit-config="{mode:'row', showStatus: true}" resizable
          max-height="457" align="center" border show-overflow highlight-hover-row size="small">
          <vxe-table-column v-if="type" type="checkbox" fixed="left" width="60"></vxe-table-column>
          <vxe-table-column title="序号" type="seq" width="80"></vxe-table-column>
          <vxe-table-column v-for="item in columns" :key="item.field" :title="item.title" :field="item.field" show-overflow="title" min-width="160">
            <template v-slot="{ row }">
              <template v-if="row.edit">
                <template v-if="item.type == 'month'">
                  <a-month-picker formatter="YYYY-MM" v-model="row[item.field]" :placeholder="'请选择' + item.title" />
                </template>
                <template v-if="item.type == 'text'">
                  <a-input v-model="row[item.field]" :maxLength="item.length" @blur="row[item.field] = $trim($event)"
                    :placeholder="'请输入' + item.title"></a-input>
                </template>
                <template v-if="item.type == 'number'">
                  <a-input-number :min="0" :max="item.max ? item.max : 9999999" :precision="item.precision ? item.precision : 2" v-model="row[item.field]"
                    :placeholder="'请输入' + item.title" />
                </template>
              </template>
              <template v-else>
                <span>{{row[item.field]}}</span>
              </template>
            </template>
          </vxe-table-column>
          <template v-slot:empty>
            <span>查询无数据</span>
          </template>
        </vxe-table>
      </a-col>
    </a-spin>
  </a-row>
</template>

<script>
import { deleteSalary } from '@/api/isolarErp/employee/employee';
import moment from 'moment';

export default {
  props: {
    type: { // true 新增或者删除 false 详情
      type: Boolean,
      default: false
    },
    empId: {
      type: [String, Number],
      default: ''
    }
  },
  data () {
    return {
      payList: [],
      loading: false,
      editStatus: true,
      columns: [
        { field: 'ym', title: '年月', type: 'month' },
        { field: 'realWorkDays', title: '实际出勤天数', type: 'number', max: 99, precision: 1 },
        { field: 'monthSalary', title: '月薪资', type: 'number' },
        { field: 'monthFixedSalary', title: '月固定薪资', type: 'number' },
        { field: 'monthKpiSalary', title: '月绩效工资', type: 'number' },
        { field: 'monthAssessLvl', title: '月度考核等级', type: 'text', length: 10 },
        { field: 'monthAssessRatio', title: '月度考核系数', type: 'text', length: 10 },
        { field: 'workRatio', title: '在岗系数', type: 'text', length: 10 },
        { field: 'holidayBenefits', title: '过节福利', type: 'text', length: 100 },
        { field: 'overtimeSalary', title: '加班费', type: 'number' },
        { field: 'htSubsidy', title: '高温补贴', type: 'number' },
        { field: 'absentDeduct', title: '缺勤扣款', type: 'number' },
        { field: 'trafficSubsidy', title: '交通补贴', type: 'number' },
        { field: 'telSubsidy', title: '话费补贴', type: 'number' },
        { field: 'marryFertilityBenefits', title: '结婚生育福利', type: 'text', length: 100 },
        { field: 'birthdayBenefits', title: '生日福利', type: 'text', length: 100 },
        { field: 'otherBenefits', title: '其他', type: 'text', length: 200 },
        { field: 'totalSalary', title: '应发合计', type: 'number' },
        { field: 'realSalary', title: '实发工资', type: 'number' },
        { field: 'sbDeduct', title: '社保个人总扣除', type: 'number' },
        { field: 'gjjDeduct', title: '公积金个人总扣除', type: 'number' },
        { field: 'pslTax', title: '个税', type: 'number' },
        { field: 'sbGjjReturn', title: '社保公积金退还/补收', type: 'number' }
      ],
      rules: {
        ym: [
          { required: true, message: '请选择年月', trigger: 'change' }
        ],
        overtimeSalary: [
          { required: true, message: '请填写加班费', trigger: 'change' }
        ],
        totalSalary: [
          { required: true, message: '请填写应发合计', trigger: 'change' }
        ],
        realSalary: [
          { required: true, message: '请填写实发工资', trigger: 'change' }
        ]
      }
    };
  },
  created () {},
  methods: {
    moment,
    // 初始化数据
    init (data) {
      data = data || [];
      data.forEach(item => {
        item.edit = false;
      });
      this.payList = data;
    },
    // 新增
    addPayInfo () {
      let row = {
        ym: null,
        overtimeSalary: null,
        shouldSalary: null,
        actualSalary: null,
        edit: true
      };
      this.payList.unshift(row);
    },
    // 删除
    deletePay () {
      const self = this;
      const select = self.$refs.payTable.getCheckboxRecords();
      if (select.length < 1) {
        self.$message.warning('请至少勾选一条数据');
        return;
      }
      self.$confirm({
        title: '确定要删除吗?',
        okText: '确定',
        cancelText: '取消',
        onOk () {
          let ids = [];
          let selectNoIds = [];
          select.forEach(item => {
            if (item.id) {
              ids.push(item.id);
            } else {
              selectNoIds.push(item);
            }
          });
          if (ids.length == 0) {
            self.remove(select);
          } else {
            self.loading = true;
            deleteSalary({ 'ids': ids }).then(res => {
              self.loading = false;
              self.$message.info('数据删除成功');
              self.remove(select);
            });
          }
        }
      });
    },
    remove (select) {
      let _XIDS = [];
      select.forEach(item => {
        _XIDS.push(item._XID);
      });
      this.payList = this.payList.filter(item => !_XIDS.includes(item._XID));
    },
    // 主页-获取数据
    getData () {
      return this.payList;
    },
    // 数据校验
    async validate () {
      const errMap = await this.$refs['payTable'].validate(true).catch(errMap => errMap);
      if (errMap) {
        return errMap;
      }
      return this.checkData();
    },
    checkData () {
      try {
        return null;
      // eslint-disable-next-line no-unreachable
      } catch (e) {
        this.$message.warning(e.message);
        return true;
      }
    }
  }
};
</script>

<style lang="less" scoped>
</style>
