import uuid from 'uuid/v4';
export const columns = [
  {
    title: '标题',
    dataIndex: 'name',
    key: 'name',
    width: '25%'
  },
  {
    title: '类型',
    dataIndex: 'type',
    key: 'type',
    width: '15%'
  },
  {
    title: '内容设置',
    dataIndex: 'content',
    key: 'content',
    width: '40%'
  },
  {
    title: '必填',
    key: 'isRequired',
    dataIndex: 'isRequired',
    width: '10%'
  },
  {
    title: '操作',
    key: 'action',
    dataIndex: 'action',
    width: '10%'
  }
];

export const initRowTitleItem = () => {
  return {
    rowTitleList: [{
      rowTitle: undefined,
      childRowTitleList: []
    }],
    colTitleList: [
      {
        colTitle: undefined,
        colType: '3',
        content: undefined,
        isRequired: true
      }
    ],
    isCanEdit: false,
    projectRemark: undefined,
    childRowTitleChecked: false
  };
};

export const initProjectItem = () => {
  const rowTitleItem = initRowTitleItem();
  return {
    projectName: undefined,
    isEnable: 1,
    projectTitleList: [
      rowTitleItem
    ],
    key: uuid()
  };
};

export const transformRequestParam = (toTransformData) => {
  const paramObject = {};
  const deviceTypeId = toTransformData.deviceTypeId;
  paramObject.deviceTypeId = deviceTypeId ? deviceTypeId[deviceTypeId.length - 1] : null;
  paramObject.deviceTypePath = deviceTypeId ? deviceTypeId.join(',') : '';
  paramObject.projectList = [];
  paramObject.delProjectIds = toTransformData.delProjectIds.join(',');
  for (let i = 0; i < toTransformData.testProjectList.length; i++) {
    const currentProject = toTransformData.testProjectList[i];
    paramObject.projectList.push({
      projectName: currentProject.projectName,
      isEnable: currentProject.isEnable,
      projectSort: i + 1,
      projectTableList: [],
      id: currentProject.id
    });
    for (let j = 0; j < currentProject.projectTitleList.length; j++) {
      const currentProjectTitle = currentProject.projectTitleList[j];
      const projectTitleList = [];

      if (currentProjectTitle.childRowTitleChecked) {
        const childRowList = currentProjectTitle.rowTitleList.reduce((acc, cur) => {
          const len = cur.childRowTitleList.length;
          const childSort = new Array(len).fill(0).map((_, index) => index + 1).join(',');
          const list = cur.childRowTitleList.map((item, index) => ({
            ...item,
            rowTitle: cur.rowTitle,
            childSort: index === 0 ? childSort : null
          }));
          acc.push(...list);
          return acc;
        }, []);

        childRowList.forEach((item, childRowIndex) => {
          projectTitleList.push({
            rowTitle: item.rowTitle,
            rowSubTitle: item.childRowTitle,
            titleType: 1,
            rowSort: childRowIndex + 1,
            childSort: item.childSort
            // tableSort: j + 1,
          });
        });
      } else {
        currentProjectTitle.rowTitleList.forEach((item, rowIndex) => {
          projectTitleList.push({
            rowTitle: item.rowTitle,
            rowSubTitle: null,
            titleType: 1,
            rowSort: rowIndex + 1
            // tableSort: j + 1
          });
        });
      }
      currentProjectTitle.colTitleList.forEach((item, colIndex) => {
        projectTitleList.push({
          rowTitle: item.colTitle,
          rowType: item.colType,
          isRequired: item.isRequired ? 1 : 0,
          titleType: 2,
          rowSort: colIndex + 1,
          rowContent: ['4', '5'].includes(item.colType) ? null : item.content,
          projectTitleOptionList: ['4', '5'].includes(item.colType) ? item.content.map((l, i) => ({ optionContent: l, seq: i + 1 })) : null
        });
      });
      paramObject.projectList[i].projectTableList.push({
        tableSort: j + 1,
        isHaveSubTitle: currentProjectTitle.childRowTitleChecked ? 1 : 0,
        projectTableRemark: currentProjectTitle.projectTableRemark,
        isCanEdit: currentProjectTitle.isCanEdit ? 1 : 0,
        projectTitleList
      });
    }
  }
  return paramObject;
};
export const transformResponseParam = (projectList = []) => {
  const testProjectList = [];
  for (let i = 0; i < projectList.length; i++) {
    const projectItem = projectList[i];
    const projectTableList = projectItem.projectTableList;
    const projectTitleList = [];
    for (let j = 0; j < projectTableList.length; j++) {
      let rowsList = [];
      const allRowsList = projectTableList[j].projectTitleList.filter(item => item.titleType === 1);
      if (projectTableList[j].isHaveSubTitle) {
        rowsList = allRowsList.filter(l => l.childSort);
      } else {
        rowsList = allRowsList;
      }
      const colsList = projectTableList[j].projectTitleList.filter(item => item.titleType === 2);
      projectTitleList.push({
        isCanEdit: !!projectTableList[j].isCanEdit,
        projectTableRemark: projectTableList[j].projectTableRemark,
        childRowTitleChecked: !!projectTableList[j].isHaveSubTitle,
        rowTitleList: rowsList.map(item => {
          const isHaveSubTitle = projectTableList[j].isHaveSubTitle;
          return {
            rowTitle: item.rowTitle,
            childRowTitleList: isHaveSubTitle ? allRowsList
              .filter(n => item.rowTitle === n.rowTitle)
              .map(n => ({ childRowTitle: n.rowSubTitle })) : []
          };
        }),
        colTitleList: colsList.map(item => {
          let content;
          if (['4', '5'].includes(item.rowType)) {
            content = item.projectTitleOptionList.map(n => n.optionContent);
          } else {
            content = item.rowContent;
          }
          return {
            colTitle: item.rowTitle,
            colType: item.rowType,
            content,
            isRequired: item.isRequired
          };
        })
      });
    }
    testProjectList.push({
      id: projectItem.id,
      projectName: projectItem.projectName,
      isEnable: projectItem.isEnable,
      projectTitleList
    });
  }
  return testProjectList;
};
