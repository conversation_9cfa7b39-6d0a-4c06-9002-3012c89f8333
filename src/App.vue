<template>
  <a-config-provider :locale="locale">
    <div id="app" style="height: 100%">
      <transition :name="direction" mode="out-in">
        <router-view />
      </transition>
    </div>
  </a-config-provider>
</template>

<script>
import zhCN from 'ant-design-vue/lib/locale-provider/zh_CN';
import enquireScreen from '@/utils/device';
import moment from 'moment';
import 'moment/locale/zh-cn';
import { captureJSError, captureVueError } from '@/utils/webTracking';
import config from '@/defaultSettings';
import { updateTheme } from '@/components/tools/setting';
import { USER_INFO } from '@/store/mutation-types';
import store from './store';
moment.locale('zh-cn'); ;
export default {
  data () {
    return {
      abc: 'cdn',
      locale: zhCN,
      isClose: true
    };
  },
  computed: {
    direction () {
      const viewDir = this.$store.state.user.direction;
      let tranName = '';
      if (viewDir === 'left') {
        tranName = 'view-out';
      } else if (viewDir === 'right') {
        tranName = 'view-in';
      } else {
        tranName = 'fade';
      }
      return tranName;
    }
  },
  created () {
    let that = this;
    // new DevicePixelRatio().init()
    if (document.getElementById('solaryLoading')) document.getElementById('solaryLoading').remove();
    if (process.env.NODE_ENV === 'development') { // 本地开发时根据分辨率大小
      enquireScreen((deviceType) => {
        // tablet
        if (deviceType === 0) {
          that.$store.commit('TOGGLE_DEVICE', 'mobile');
          that.$store.dispatch('setSidebar', false);
        } else if (deviceType === 1) { // mobile
          that.$store.commit('TOGGLE_DEVICE', 'mobile');
          that.$store.dispatch('setSidebar', false);
        } else {
          that.$store.commit('TOGGLE_DEVICE', 'desktop');
          that.$store.dispatch('setSidebar', true);
        }
      });
    } else { // 线上根据userAgent 判断是移动端还是手机端
      if (this.isMobile()) {
        that.$store.commit('TOGGLE_DEVICE', 'mobile');
        that.$store.dispatch('setSidebar', false);
      } else {
        that.$store.commit('TOGGLE_DEVICE', 'desktop');
        that.$store.dispatch('setSidebar', true);
      }
    }
    // 接受父页面发来的信息
    window.addEventListener('message', function (event) {
      var data = event.data;
      if (data) {
        switch (data.cmd) {
          case 'baseInfo':
            // 处理业务逻辑
            document.documentElement.setAttribute('data-theme', data.params.navTheme);
            updateTheme();
            store.dispatch('ToggleTheme', data.params.navTheme);
            Vue.ls.set(USER_INFO, data.params.userInfo);
            store.commit('SET_INFO', data.params.userInfo);
            break;
        }
      }
    });
  },
  mounted () {
    window.addEventListener('error', event => {
      captureJSError(event);
    });
    window.addEventListener('unhandledrejection', event => {
      captureJSError(event);
    });
  },
  errorCaptured: (err, vm, info) => {
    captureVueError(err, vm, info);
  },
  methods: {
    isMobile () {
      let flag = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
      return flag;
    }
  }
};
</script>
<style>
body {
  width: 100vw;
  overflow:hidden;
}
  #animated {
    -webkit-animation-duration: 0.8s;
    animation-duration: 0.8s;
    -webkit-animation-fill-mode: both;
    animation-fill-mode: both;
  }
  #animated .infinite {
    -webkit-animation-iteration-count: infinite;
    animation-iteration-count: infinite;
  }
  @-webkit-keyframes slideInLeft {
    from {
      -webkit-transform: translate3d(-100%, 0, 0);
      transform: translate3d(-100%, 0, 0);
      visibility: visible;
    }
    to {
      -webkit-transform: translate3d(0, 0, 0);
      transform: translate3d(0, 0, 0);
    }
  }

  @keyframes slideInLeft {
    from {
      -webkit-transform: translate3d(-100%, 0, 0);
      transform: translate3d(-100%, 0, 0);
      visibility: visible;
    }

    to {
      -webkit-transform: translate3d(0, 0, 0);
      transform: translate3d(0, 0, 0);
    }
  }

  .slideInLeft {
    -webkit-animation-name: slideInLeft;
    animation-name: slideInLeft;
  }

  @-webkit-keyframes slideInRight {
    from {
      -webkit-transform: translate3d(100%, 0, 0);
      transform: translate3d(100%, 0, 0);
      visibility: visible;
    }

    to {
      -webkit-transform: translate3d(0, 0, 0);
      transform: translate3d(0, 0, 0);
    }
  }

  @keyframes slideInRight {
    from {
      -webkit-transform: translate3d(100%, 0, 0);
      transform: translate3d(100%, 0, 0);
      visibility: visible;
    }

    to {
      -webkit-transform: translate3d(0, 0, 0);
      transform: translate3d(0, 0, 0);
    }
  }

  .slideInRight {
    -webkit-animation-name: slideInRight;
    animation-name: slideInRight;
  }

  @-webkit-keyframes slideOutLeft {
    from {
      -webkit-transform: translate3d(0, 0, 0);
      transform: translate3d(0, 0, 0);
    }

    to {
      visibility: hidden;
      -webkit-transform: translate3d(-100%, 0, 0);
      transform: translate3d(-100%, 0, 0);
    }
  }

  @keyframes slideOutLeft {
    from {
      -webkit-transform: translate3d(0, 0, 0);
      transform: translate3d(0, 0, 0);
    }

    to {
      visibility: hidden;
      -webkit-transform: translate3d(-100%, 0, 0);
      transform: translate3d(-100%, 0, 0);
    }
  }

  .slideOutLeft {
    -webkit-animation-name: slideOutLeft;
    animation-name: slideOutLeft;
  }

  @-webkit-keyframes slideOutRight {
    from {
      -webkit-transform: translate3d(0, 0, 0);
      transform: translate3d(0, 0, 0);
    }

    to {
      visibility: hidden;
      -webkit-transform: translate3d(100%, 0, 0);
      transform: translate3d(100%, 0, 0);
    }
  }

  @keyframes slideOutRight {
    from {
      -webkit-transform: translate3d(0, 0, 0);
      transform: translate3d(0, 0, 0);
    }

    to {
      visibility: hidden;
      -webkit-transform: translate3d(100%, 0, 0);
      transform: translate3d(100%, 0, 0);
    }
  }

  .slideOutRight {
    -webkit-animation-name: slideOutRight;
    animation-name: slideOutRight;
  }

  @-webkit-keyframes inRight {
    0% {
      -webkit-transform: translate3d(100%, 0, 0);
      transform: translate3d(100%, 0, 0);
    }

    to {
      -webkit-transform: translateZ(0);
      transform: translateZ(0);
    }
  }

  @keyframes inRight {
    0% {
      -webkit-transform: translate3d(100%, 0, 0);
      transform: translate3d(100%, 0, 0);
    }

    to {
      -webkit-transform: translateZ(0);
      transform: translateZ(0);
    }
  }

  @-webkit-keyframes outLeft {
    0% {
      -webkit-transform: translateZ(0);
      transform: translateZ(0);
    }

    to {
      -webkit-transform: translate3d(100%, 0, 0);
      transform: translate3d(100%, 0, 0);
    }
  }

  @keyframes outLeft {
    0% {
      -webkit-transform: translateZ(0);
      transform: translateZ(0);
    }

    to {
      -webkit-transform: translate3d(100%, 0, 0);
      transform: translate3d(100%, 0, 0);
    }
  }

  .fade-enter-active,
  .fade-leave-active {
    transition: all 0.2s ease;
  }

  .fade-enter,
  .fade-leave-active {
    opacity: 0;
  }

  .view-in-enter-active,
  .view-out-leave-active {
    position: absolute;
    top: 0;
    width: 100%;
    -webkit-animation-duration: 0.3s;
    animation-duration: 0.3s;
    -webkit-animation-fill-mode: both;
    animation-fill-mode: both;
    -webkit-backface-visibility: hidden;
    backface-visibility: hidden;
  }

  .view-in-enter-active {
    -webkit-animation-name: inRight;
    animation-name: inRight;
  }

  .view-out-leave-active {
    -webkit-animation-name: outLeft;
    animation-name: outLeft;
  }

  .mouse-cover-canvas{
    top:189px!important;
    left:594px!important;

  }

</style>
