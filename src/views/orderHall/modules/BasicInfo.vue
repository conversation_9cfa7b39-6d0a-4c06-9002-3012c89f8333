<template>
  <div v-if="showBasic">
    <div v-if="['3', '7','8'].includes(type)">
    <BasicInfoDetail :baseForm="formData" :dictMap="dictMap" />
  </div>
  <div v-else>
    <a-row :gutter="24">
      <a-col :span="24">
        <div class="order-dispose">
          <div class="title-box">
            <span class="before"></span>
            <span>基本信息</span>
          </div>
        </div>
      </a-col>
    </a-row>

    <a-row :gutter="24">
      <a-col :xl="8" :sm="12" :xs="24">
        <a-form-model-item label="任务类型" prop="taskType" :rules="{ required: true, message: '此项为必填项' }">
          <a-select size="default" :disabled="isDiabled" v-model="formData.taskTypeName" placeholder="请选择">
            <a-select-option
              v-for="item in dictMap.ops_task_type"
              :key="item.dataId"
              :pId="item.dataId"
              :value="item.dataValue"
              >{{ item.dataLable }}</a-select-option
            >
          </a-select>
        </a-form-model-item>
      </a-col>
       <a-col :xl="8" :sm="12" :xs="24" v-if="formData.taskType === '8'">
        <a-form-model-item label="工程遗留" prop="isEngLegacy" :rules="{ required: true, message: '此项为必填项' }">
          <a-radio-group v-model="formData.isEngLegacy" disabled>
            <a-radio :value="1">是</a-radio>
            <a-radio :value="0">否</a-radio>
          </a-radio-group>
        </a-form-model-item>
      </a-col>
      <!-- 判断是否存在子类 -->
      <a-col :xl="8" :sm="12" :xs="24" v-if=" ['2', '3', '4'].includes(formData.taskType)">
        <a-form-model-item label="任务子类" prop="workSubclass" :rules="{ required: true, message: '此项为必填项' }">
          <a-select :disabled="isDiabled" v-model="formData.workSubclass" placeholder="请选择">
            <a-select-option v-for="item in dictMap.ops_task_sub_type" :key="item.dataId" :value="item.dataValue">{{
              item.dataLable
            }}</a-select-option>
          </a-select>
        </a-form-model-item>
      </a-col>

      <template v-if="['7', '8'].includes(formData.taskType)">
        <a-col :xl="8" :sm="12" :xs="24">
          <a-form-model-item label="工单来源">
            <a-select :disabled="isDiabled" v-model="formData.orderSource" placeholder="请选择">
              <a-select-option v-for="item in dictMap.two_order_source" :key="item.key" :value="item.dataValue">{{
                item.dataLable
              }}</a-select-option>
            </a-select>
          </a-form-model-item>
        </a-col>
      </template>
    </a-row>
    <a-row :gutter="24">
      <a-col :xl="8" :sm="12" :xs="24">
        <a-form-model-item label="电站名称" prop="psaId" :rules="{ required: true, message: '此项为必填项' }">
          <psa-select :disabled="isDiabled" v-model="formData.psaId"></psa-select>
        </a-form-model-item>
      </a-col>
      <a-col :xl="8" :sm="12" :xs="24" v-if="formData.orderSource === '4'">
        <a-form-model-item label="实体电站" prop="psName">
          <a-input :disabled="isDiabled" v-model="formData.psName" style="width: 100%" />
        </a-form-model-item>
      </a-col>
      <template v-if="['7', '8'].includes(formData.taskType)">
        <a-col :xl="8" :sm="12" :xs="24">
          <a-form-model-item label="发现时间" prop="findTime" :rules="{ required: true, message: '此项为必填项' }">
            <a-date-picker
              style="width: 100%"
              show-time
              format="YYYY-MM-DD HH:mm"
              :allowClear="false"
              valueFormat="YYYY-MM-DD HH:mm"
              placeholder="请选择"
              v-model="formData.findTime"
              :disabled-date="disabledFindDate"
              @ok="findTimeChange"
            />
          </a-form-model-item>
        </a-col>
        <a-col :xl="8" :sm="12" :xs="24" v-if="formData.taskType === '8' && formData.isEngLegacy == 1">
          <a-form-model-item label="发现地点" prop="findPlace" :rules="{ required: true, message: '此项为必填项' }">
            <a-select v-model="formData.findPlace" placeholder="请输入" style="width: 100%" show-search>
              <a-select-option v-for="item in dictMap.find_place" :key="item.key" :value="item.dataValue">{{
                item.dataLable
              }}</a-select-option>
            </a-select>
          </a-form-model-item>
        </a-col>
        <a-col :span="24" v-if="['7', '8', '12'].includes(formData.taskType)">
          <a-form-model-item :label="formData.taskType === '7' ? '故障描述' : '缺陷描述'" prop="taskDescription">
            <a-textarea placeholder="请输入" v-model="formData.taskDescription"></a-textarea>
          </a-form-model-item>
        </a-col>
        <a-col :xl="8" :sm="12" :xs="24">
          <a-form-model-item label="发现人" prop="findUserName" :rules="{ required: true, message: '此项为必填项' }">
            <a-input :disabled="isDiabled" v-model="formData.findUserName"></a-input>
          </a-form-model-item>
        </a-col>
        <a-col :xl="8" :sm="12" :xs="24">
          <a-form-model-item label="预计消除时间" prop="predictRecoverTime">
            <a-input :disabled="isDiabled" v-model="formData.predictRecoverTime"></a-input>
          </a-form-model-item>
        </a-col>
      </template>
      <template v-if="['12'].includes(formData.taskType)">
        <a-col :span="24">
          <a-form-model-item label="任务描述" prop="taskDescription" :rules="{ required: true, message: '此项为必填项' }">
            <a-textarea placeholder="请输入" v-model="formData.taskDescription"></a-textarea>
          </a-form-model-item>
        </a-col>
      </template>
      <a-col :xl="8" :sm="12" :xs="24" v-if="!['7', '8'].includes(formData.taskType)">
        <a-form-model-item label="计划日期" prop="planTime" mode="month">
          <a-range-picker
            style="width: 100%"
            :disabled="!['7', '8'].includes(formData.taskType)"
            v-model="formData.planTime"
            format="YYYY-MM"
          />
        </a-form-model-item>
      </a-col>
      <!-- 除草：2 清洗：1-->
      <template v-if="['1', '2'].includes(formData.taskType)">
        <a-col :xl="8" :sm="12" :xs="24">
          <a-form-model-item label="电站环境" prop="stationEnvironment">
            <a-select
              maxTagCount="1"
              :disabled="isDiabled"
              v-model="formData.stationEnvironment"
              placeholder="请选择"
              style="width: 100%"
              mode="multiple"
            >
              <a-select-option v-for="item in dictMap.psa_model" :key="item.dataValue" :value="item.dataValue">{{
                item.dataLable
              }}</a-select-option>
            </a-select>
          </a-form-model-item>
        </a-col>
        <a-col :xl="8" :sm="12" :xs="24">
          <a-form-model-item
            :label="(formData.taskType === '1' ? '清洗' : '除草') + '容量(MW)'"
            prop="cleanCap"
            :rules="{ required: true, message: '此项为必填项' }"
          >
            <a-input-number v-model="formData.cleanCap" :min="0" :max="9999.9999" :precision="4" style="width: 100%" />
          </a-form-model-item>
        </a-col>
        <a-col :xl="8" :sm="12" :xs="24" v-if="formData.taskType === '2'">
          <a-form-model-item label="除草方式" prop="grassType" :rules="{ required: true, message: '此项为必填项' }">
            <a-select v-model="formData.grassType">
              <a-select-option v-for="item in dictMap.pdca_grass_type" :key="item.dataValue" :value="item.dataValue">{{
                item.dataLable
              }}</a-select-option>
            </a-select>
          </a-form-model-item>
        </a-col>
        <a-col :xl="8" :sm="12" :xs="24" v-if="formData.taskType === '1'">
          <a-form-model-item label="清洗方式" prop="cleanType" :rules="{ required: true, message: '此项为必填项' }">
            <a-select v-model="formData.cleanType" :="isDisabled">
              <a-select-option v-for="item in dictMap.pdca_clean_type" :key="item.dataValue" :value="item.dataValue">{{
                item.dataLable
              }}</a-select-option>
            </a-select>
          </a-form-model-item>
        </a-col>
      </template>
      <!-- 试验 红外检测-->
      <template v-if="formData.taskType === '3'">
        <a-col :xl="8" :sm="12" :xs="24">
          <a-form-model-item label="上次试验时间" prop="beforeExperimentDate" key="beforeExperimentDate">
            <a-input disabled v-model="formData.beforeExperimentDate"></a-input>
          </a-form-model-item>
        </a-col>
      </template>
      <a-col :xl="8" :sm="12" :xs="24">
        <a-form-model-item label="负责人" prop="liablePerson" :rules="{ required: true, message: '此项为必填项' }">
          <a-select
            :disabled="type === '6'"
            v-model="formData.liablePerson"
            placeholder="请选择"
            style="width: 100%"
            allowClear
          >
            <a-select-option v-for="(item, k) in assignList" :hidden="item.hidden" :key="k" :value="item.username">
              {{ item.realName }}
            </a-select-option>
          </a-select>
        </a-form-model-item>
      </a-col>

      <template v-if="formData.isEngLegacy == '1'">
        <a-col :span="24">
          <a-form-model-item label="整改要求" prop="rectifyReq">
            <a-textarea size="default" :max-length="1000" v-model="formData.rectifyReq"></a-textarea>
          </a-form-model-item>
        </a-col>
      </template>
    </a-row>
  </div>
  </div>
</template>

<script>
import moment from 'moment';
import initDict from '@/mixins/initDict';
import { getAssignListByPSId } from '@/api/common_gy/common.js';
import BasicInfoDetail from './BasicInfoDetail';
import XEUtils from 'xe-utils';
import { USER_INFO } from '@/store/mutation-types';
import { getDate } from '@/api/config/digitalRegulation';
import { getExperimentDate } from '@api/operations/digitalOrder';
export default {
  name: 'basic-info',
  mixins: [initDict],
  components: { BasicInfoDetail },
  props: {
    type: {
      type: String,
      default: () => {
        return null;
      }
    },
    formData: {
      type: Object,
      default: () => {
        return {};
      }
    },
    dictMap: {
      type: Object,
      default: () => {
        return {};
      }
    },
    isDroneOrder: {
      type: Boolean,
      default: false
    }
  },
  data () {
    return {
      assignList: [],
      isDiabled: true,
      XEUtils
    };
  },
  mounted () {

  },
  methods: {
    // 发现时间不能超过系统当前时间
    disabledFindDate (current) {
      return current && current > moment().endOf('day');
    },
    // 根据电站获取负责人
    getAssignListByPSId () {
      const formData = this.formData;
      const params = {
        psId: formData.psaId,
        initUserAccount: undefined
      };
      getAssignListByPSId(params)
        .then((res) => {
          this.assignList = res.result || [];
          // 领取时默认当前账号
          if (this.type === '6') {
            let user = Vue.ls.get(USER_INFO);
            this.formData.liablePerson = user.username;
            this.assignList.push({
              username: user.username,
              realName: user.realname
            });
          } else if (this.type === '5') {
            // 指派时清空
            this.formData.liablePerson = undefined;
          }
        })
        .catch(() => {
          this.assignList = [];
        });
    },
    engLegacyChnage () {
      this.formData.deviceTypeId = [];
      this.formData.deviceId = undefined;
      this.formData.deviceNo = undefined;
      this.formData.defectName = undefined;
    },
    findTimeChange () {
      const findTime = this.formData.findTime;
      getDate({
        taskType: this.formData.taskType,
        scale: this.formData.cleanCap,
        subTaskType: this.formData.defectType,
        endDate: findTime && findTime.split(' ')[0]
      }).then((res) => {
        if (res.result_code == 1) {
          this.formData.predictRecoverTime = res.result_data + ' ' + findTime.split(' ')[1];
        }
      });
    }
  },
  computed: {
    showBasic () {
      if (['3', '6', '5', '8', '7'].includes(this.type)) {
        // otherSts:0标识迁移数据，代表流程已走完
        if (['2', '3', '4', '5', '6', '0'].includes(this.formData.otherSts)) {
          return true;
        }
      }
      return false;
    }
  },
  watch: {
    'formData.psaId' (val) {
      this.getAssignListByPSId();
    },
    'formData.predictRecoverTime' (val) {
      this.$forceUpdate();
    },
    'formData.taskType' (val) {
      // 检测试验获取上次试验时间
      if (val === '3') {
        getExperimentDate({
          psaId: this.formData.psaId,
          workSubclass: this.formData.workSubclass
        }).then((res) => {
          this.formData.beforeExperimentDate = res.result_data;
        }).catch(() => {});
      }
    }
  }
};
</script>
