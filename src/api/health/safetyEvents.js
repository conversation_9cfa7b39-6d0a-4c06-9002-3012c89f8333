import { axios } from '@/utils/erp-request';
const url = process.env.VUE_APP_Health_BASE_URL;

// 获取安全告警事件列表
export function getAlarmEvents (data) {
  return axios({
    url: url + '/hikvision/list',
    method: 'post',
    data
  });
}
// 导出告警事件
export function exportAlarmEvents (data) {
  return axios({
    url: url + '/hikvision/export',
    method: 'post',
    data
  });
}
// 复核告警事件
export function reviewAlarmEvent (data) {
  return axios({
    url: url + '/hikvision/review',
    method: 'post',
    data
  });
}
// 关闭告警事件
export function closeAlarmEvent (data) {
  return axios({
    url: url + '/hikvision/close',
    method: 'post',
    data
  });
}
// 隔离告警事件
export function isolateEvents (data) {
  return axios({
    url: url + '/hikvision/isolate',
    method: 'post',
    data
  });
}
// 健康轨迹列表
export function healthTrajectoryList (data) {
  return axios({
    url: url + '/hikvision/healthTrajectory/list',
    method: 'post',
    data
  });
}
// 获取待处理、已隔离、已派发总条数
export function getTotal (data) {
  return axios({
    url: url + '/hikvision/dataStatistical',
    method: 'post',
    data
  });
}

// 取得设备待处理历史故障信息
export function getFaultOptions (data) {
  return axios({
    url: url + '/hikvision/getFaultByPsKey',
    method: 'post',
    data
  });
}
// 获取health字典
export function getSystemCodeListWithChild (data) {
  return axios({
    url: url + '/systemCode/getSystemCodeListWithChild',
    method: 'post',
    data
  });
}

// 根据电站id（psaid）获取设备
export function getDeviceByPsIds (data) {
  return axios({
    url: url + '/hikvision/getDeviceByPsIds',
    method: 'post',
    data
  });
}

// 根据电站id（psaid）获取设备对应区域
export function getRegionByPsId (data) {
  return axios({
    url: url + '/hikvision/getRegionByPsId',
    method: 'post',
    data
  });
}

// 根据区域获取下级监控点视频预览流
export function getPreviewURLsByRegionCode (data) {
  return axios({
    url: url + '/hikvision/getPreviewURLsByRegionCode',
    method: 'post',
    data
  });
}

// 根据区域获取下级监控点事件回放
export function getEventPlayBack (data) {
  return axios({
    url: url + '/hikvision/getEventPlayBack',
    method: 'post',
    data
  });
}

// 获取历史图片或视频
export function getHistoryInfo (data) {
  return axios({
    url: url + '/hikvision/getAlarmFileByRegion',
    method: 'post',
    data
  });
}
