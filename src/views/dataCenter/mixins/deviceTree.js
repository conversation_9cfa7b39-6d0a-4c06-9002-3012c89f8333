import {
  getDeviceTypeTree
} from '@/api/health/healthapi.js';
export const DeviceTree = {
  data () {
    return {
      treeData: [],
      selectNode: [],
      treeLoading: true,
      selectedKeys: {}
    };
  },
  methods: {
    onClick (val, event) {
      if (!val.selectable) return false;
      this.setSelectNodeInfo(val);
      this.removeClass();
    },
    removeClass () {
      this.$nextTick(() => {
        document.getElementsByClassName('drag-over')[0] && document.getElementsByClassName('drag-over')[0].classList.remove('drag-over');
      });
    },
    getPsTreeMenu (obj) {
      this.treeLoading = true;
      getDeviceTypeTree(obj).then((res) => {
        if (res.result_data) {
          this.treeData = res.result_data.filter(item => {
            return !(item.pid == '0' && item.deviceType == '5');
          });
          this.treeData = this.arrayToTree(this.treeData, '0');
          this.selectNode = this.treeData[0];
          this.selectedKeys = [this.treeData[0].key];
        } else {
          this.treeData = [];
        }
        this.treeLoading = false;
      }).catch(() => {
        this.treeLoading = false;
      });
    },
    //  高性能数组转树 此方法利用引用类型的内存 需要给出初始pid
    arrayToTree (items, minPid) {
      const result = []; // 存放结果集
      const itemMap = {}; //
      this.collectorWire = [];
      for (const item of items) {
        if (item.deviceType == 3 && !item.deviceName.includes('升压站')) {
          this.collectorWire.push(item);
        }
        const id = item.id;
        const pid = item.pid;
        if (!itemMap[id] || !items.find(ele => ele.id == pid)) {
          itemMap[id] = {
            children: []
          };
        }
        // let selectable = ['11'].includes(item.deviceType) || (item.isLocal == '1' && ['301', '302', '7', '5'].includes(item.deviceType)) || (item.isLocal != 0 && item.deviceType != '15');
        itemMap[id] = {
          ...item,
          title: item.deviceName,
          key: item.psKey,
          selectable: true,
          disabled: false,
          children: itemMap[id]['children']
        };

        const treeItem = itemMap[id];

        if (pid === minPid) {
          result.push(treeItem);
        } else {
          if (!itemMap[pid] || !items.find(ele => ele.id == pid)) {
            itemMap[pid] = {
              children: []
            };
          }
          itemMap[pid].children.push(treeItem);
        }
      }
      return result;
    }
  }

};
