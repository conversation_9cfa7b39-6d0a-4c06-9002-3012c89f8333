<template>
  <!-- 学历信息 -->
  <a-row>
    <a-spin :spinning="loading">
      <a-col v-show="(show || type) && !isgId" :span="24" class="top-btn-group">
        <a-button size="default" class="solar-eye-btn-primary" @click="addEducation" style="margin-right:10px;">新增</a-button>
        <a-button size="default" @click="delEducation">删除</a-button>
      </a-col>
      <a-col :span="24">
        <vxe-table class="emp-table" ref="educationTable" :data="educationTable" :edit-rules="!isgId ?rules:[]" resizable :edit-config="{mode:'row', showStatus: true}"
         max-height="457" align="center" border show-overflow highlight-hover-row size="small">
          <vxe-table-column type="checkbox" fixed="left" width="60"></vxe-table-column>
          <vxe-table-column title="序号" type="seq" width="80"></vxe-table-column>
          <vxe-table-column field="startDate" title="开始时间" show-overflow="title" width="160">
            <template v-slot="{ row }">
              <a-date-picker v-model="row.startDate" v-if="show || row.edit" format="YYYY-MM-DD" valueFormat="YYYY-MM-DD" size="default" style="width: 95%;"/>
              <span v-else>{{getLabel(row.startDate,null)}}</span>
            </template>
          </vxe-table-column>
          <vxe-table-column field="endDate"  title="结束时间" show-overflow="title" width="160">
            <template v-slot="{ row }">
              <a-date-picker v-model="row.endDate" v-if="show || row.edit" format="YYYY-MM-DD" valueFormat="YYYY-MM-DD" size="default" style="width: 95%;"/>
              <span v-else>{{getLabel(row.endDate,null)}}</span>
            </template>
          </vxe-table-column>
          <vxe-table-column field="education" title="学历" show-overflow="title" width="200">
            <template v-slot="{ row }">
              <a-select v-model="row.education" v-if="show || row.edit" size="default" style="width:100%;">
                <a-select-option v-for="(option,index) in dictMap['education']" :key="index" :value="option.dataValue">{{option.dataLable}}</a-select-option>
              </a-select>
              <span v-else>{{getLabel(row.education,dictMap['education'])}}</span>
            </template>
          </vxe-table-column>
          <vxe-table-column title="学校" show-overflow="title" min-width="200">
            <template v-slot="{ row }">
              <a-input v-model="row.school" v-if="show || row.edit" :maxLength='50' size="default" @blur="row.school = $trim($event)" style="width: 95%;"></a-input>
              <span v-else>{{getLabel(row.school,null)}}</span>
            </template>
          </vxe-table-column>
          <vxe-table-column title="专业" show-overflow="title" min-width="200">
            <template v-slot="{ row }">
              <a-input v-model="row.subject" v-if="show || row.edit" :maxLength='100' size="default" @blur="row.subject = $trim($event)" style="width: 95%;"></a-input>
              <span v-else>{{getLabel(row.subject,null)}}</span>
            </template>
          </vxe-table-column>
          <vxe-table-column :visible="type" title="操作" fixed="right" width="140" :resizable="false">
            <template slot-scope="scope">
              <template v-if="scope.row.edit">
                <a-button size="default" icon="save" :loading="loading" title="保存" @click="saveRow(scope.$rowIndex)"></a-button>
                <a-button size="default" icon="arrow-left" title="取消" @click="cacelRow(scope.$rowIndex)"></a-button>
              </template>
              <template v-else>
                <a-button size="default" type="primary" icon="form" title="编辑" @click="editRow(scope.$rowIndex)"></a-button>
              </template>
            </template>
          </vxe-table-column>
          <template v-slot:empty>
            <span>查询无数据</span>
          </template>
        </vxe-table>
      </a-col>
    </a-spin>
  </a-row>
</template>

<script>
import initDict from '@/mixins/initDict';
import { delErpEmpEduById, saveEmpEdu } from '@/api/isolarErp/employee/employee';
import { TENANT_ID, USER_NAME } from '@/store/mutation-types';
import moment from 'moment';
export default {
  mixins: [initDict],
  props: {
    type: {
      type: Boolean,
      default: false
    },
    empId: {
      type: [String, Number],
      default: ''
    },
    isgId: {
      type: String,
      default: ''
    }
  },
  data () {
    return {
      educationTable: [],
      show: false,
      loading: false,
      oldRow: null,
      rules: {
        startDate: [
          { required: true, message: '请选择开始时间', trigger: 'change' }
        ],
        endDate: [
          { required: true, message: '请选择结束时间', trigger: 'change' }
        ],
        education: [
          { required: true, message: '请选择学历', trigger: 'change' }
        ],
        school: [
          { required: true, message: '请填写毕业院校', trigger: 'blur' }
        ],
        subject: [
          { required: true, message: '请填写专业', trigger: 'blur' }
        ]
      }
    };
  },
  created () {
    this.getDictMap('education');
  },
  methods: {
    moment,
    // 初始化数据
    init (data, type) {
      this.show = (type && type != '3');
      data = (data || []);
      data.forEach(item => {
        item.edit = false;
      });
      this.educationTable = data;
    },
    // 新增
    addEducation () {
      let row = {
        startDate: '',
        endDate: '',
        education: '',
        school: '',
        subject: ''
      };
      if (this.type) {
        for (let item of this.educationTable) {
          if (item.edit) {
            this.$message.warning('请先保存当前编辑的数据!');
            return;
          }
        }
        row.edit = true;
        this.oldRow = null;
      }
      this.educationTable.unshift(row);
    },
    // 行编辑
    editRow (index) {
      for (let item of this.educationTable) {
        if (item.edit) {
          this.$message.warning('请先保存当前编辑的数据!');
          return;
        }
      }
      this.oldRow = Object.assign({}, this.educationTable[index]);
      this.educationTable[index].edit = true;
    },
    // 行保存
    async saveRow (index) {
      const self = this;
      self.loading = true;
      const err = await self.$refs['educationTable'].validate(self.educationTable[index]).catch(errMap => errMap);
      if (err) {
        self.loading = false;
        return;
      }
      const check = self.checkData();
      if (check) {
        self.loading = false;
        return;
      }
      self.$confirm({
        title: '确定保存修改吗?',
        okText: '确定',
        cancelText: '取消',
        onOk () {
          let row = self.educationTable[index];
          row.sysTenantId = Vue.ls.get(TENANT_ID);
          row.userAccount = Vue.ls.get(USER_NAME);
          row.empId = self.empId;
          saveEmpEdu({ 'erpEmpEdu': [row] }).then(res => {
            self.loading = false;
            if (res.result_code == '1') {
              self.$message.success('操作成功');
              self.educationTable[index].edit = false;
              self.$emit('refresh');
            } else {
              self.$message.warning(res.result_msg);
            }
          }).catch(() => {
            self.loading = false;
          });
        },
        onCancel () {
          self.loading = false;
        }
      });
    },
    // 行-取消保存
    cacelRow (index) {
      const self = this;
      self.$confirm({
        title: '确定放弃修改吗?',
        okText: '确定',
        cancelText: '取消',
        onOk () {
          // 用于区分修改的是已有数据还是新加的数据
          if (self.oldRow) {
            self.educationTable[index] = self.oldRow;
            const data = self.educationTable;
            self.educationTable = [];
            self.$nextTick(() => {
              self.educationTable = data;
            });
          } else {
            self.educationTable.splice(index, 1);
          }
        }
      });
    },
    // 删除
    delEducation () {
      const self = this;
      const select = self.$refs.educationTable.getCheckboxRecords();
      if (select.length < 1) {
        self.$message.warning('请至少勾选一条数据');
        return;
      }
      self.$confirm({
        title: '确定要删除吗?',
        okText: '确定',
        cancelText: '取消',
        onOk () {
          if (self.type) {
            let ids = [];
            select.forEach(item => {
              if (item.id) {
                ids.push(item.id);
              }
            });
            if (ids.length == 0) {
              self.remove(select);
            } else {
              self.loading = true;
              delErpEmpEduById({ 'ids': ids }).then(res => {
                self.loading = false;
                if (res.result_code == '1') {
                  self.$message.info('数据删除成功');
                  self.remove(select);
                } else {
                  self.$message.warning(res.result_msg);
                }
              }).catch(() => {
                self.loading = false;
              });
            }
          } else {
            self.remove(select);
          }
        }
      });
    },
    remove (select) {
      let _XIDS = [];
      select.forEach(item => {
        _XIDS.push(item._XID);
      });
      this.educationTable = this.educationTable.filter(item => !_XIDS.includes(item._XID));
    },
    // 主页-获取数据
    getData () {
      return this.educationTable;
    },
    // 数据校验
    async validate () {
      const errMap = await this.$refs['educationTable'].validate(true).catch(errMap => errMap);
      if (errMap) {
        return errMap;
      }
      return this.checkData();
    },
    checkData () {
      try {
        let start = [];
        let end = [];
        this.educationTable.forEach((item, index) => {
          if (item.startDate > item.endDate) {
            throw new Error('学历信息第' + (index + 1) + '行时间区间有误');
          }
          start.push(item.startDate);
          end.push(item.endDate);
        });
        start = start.sort();
        end = end.sort();
        // 判断时间区间交叉
        start.forEach((item, index) => {
          if (index > 0 && item <= end[index - 1]) {
            throw new Error('学历信息时间区间有重叠');
          }
        });
        return null;
      } catch (e) {
        this.$message.warning(e.message);
        return true;
      }
    }
  }
};
</script>

<style lang="less" scoped>
</style>
