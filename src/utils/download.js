function dataURLtoBlob (dataurl) {
  var arr = dataurl.split(',');
  var mime = arr[0].match(/:(.*?);/)[1];
  var bstr = atob(arr[1]);
  var n = bstr.length;
  var u8arr = new Uint8Array(n);
  while (n--) {
    u8arr[n] = bstr.charCodeAt(n);
  }
  return new Blob([u8arr], {
    type: mime
  });
}

function downloadFile (url, name = 'What\'s the fuvk') {
  var a = document.createElement('a');
  a.setAttribute('href', url);
  a.setAttribute('download', name);
  a.setAttribute('target', '_blank');
  let clickEvent = document.createEvent('MouseEvents');
  clickEvent.initEvent('click', true, true);
  a.dispatchEvent(clickEvent);
}

function downloadFileByBase64 (_base64, name) {
  let base64 = 'data:image/png;base64,' + _base64;
  var myBlob = dataURLtoBlob(base64);
  var myUrl = URL.createObjectURL(myBlob);
  downloadFile(myUrl, name);
}

function download (res, codeName) {
  let base64Code = codeName ? res[codeName] : res.fileBase64Code;
  downloadFileByBase64(base64Code, res.fileName || new Date().getTime() + '.xlsx');
}

export default download;
