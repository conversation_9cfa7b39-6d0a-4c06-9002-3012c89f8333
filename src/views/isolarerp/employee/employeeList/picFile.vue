<template>
  <!-- 照片附件 -->
  <a-row>
    <a-spin :spinning="loading">
      <a-col :span="24">
        <vxe-table class="emp-table" ref="picFileTable" :data="picFileList" resizable
          max-height="457" align="center" border show-overflow highlight-hover-row size="small">
          <vxe-table-column title="序号" type="seq" width="80"></vxe-table-column>
          <vxe-table-column title="类型" field="dataLable" show-overflow="title" min-width="130"></vxe-table-column>
          <vxe-table-column title="附件名称" field="fileName" show-overflow="title" min-width="200">
            <template v-slot="{ row }">
              <template v-if="!row.fileName">
                <span style="color: #d6d6d6;">请上传</span>
              </template>
              <template v-else>
                <span>{{row.fileName}}</span>
              </template>
            </template>
          </vxe-table-column>
          <vxe-table-column title="操作" width="200" fixed="right" align="center">
            <template slot-scope="scope">
              <upload-vue v-show="(show || type) && !scope.row.fileName" v-model="scope.$rowIndex"
                :accept="scope.$rowIndex < 4 ? '.jpg, .png, .jpeg, .bmp': '.pdf, .doc, .docx'"
                :addWaterMarker="scope.$rowIndex == 2 || scope.$rowIndex == 3 "
                :empName="empName" :isPicFile="true" @change="fileChange" />
              <a-button v-show="setDisabled(scope.row.fileName)" size="default" icon="read" :loading="loading"
                title="照片预览" @click="lookOrDown(scope.row,'look')"></a-button>
              <a-button v-show="scope.row.fileName" size="default" icon="arrow-down" :loading="loading"
                title="照片下载" @click="lookOrDown(scope.row,'down')"></a-button>
              <a-button v-show="(show || type) && scope.row.fileName" size="default" icon="delete" :loading="loading"
                title="附件删除" @click="deleteAnnex(scope.$rowIndex)"></a-button>
            </template>
          </vxe-table-column>
          <template v-slot:empty>
            <span>查询无数据</span>
          </template>
        </vxe-table>
      </a-col>
    </a-spin>
    <pic-view v-model="visible" :open="visible" :path="picPath" :isImg="isImg" @change="close" />
  </a-row>
</template>

<script>
import moment from 'moment';
import { downloadBizAnnex, delPhotoById, savePhoto } from '@/api/isolarErp/employee/employee';
import { TENANT_ID, USER_NAME } from '@/store/mutation-types';
import initDict from '@/mixins/initDict';
import uploadVue from './uploadViewImg';
import picView from './picView';
export default {
  components: {
    uploadVue,
    picView
  },
  mixins: [initDict],
  props: {
    type: {
      type: Boolean,
      default: false
    },
    empId: {
      type: [String, Number],
      default: ''
    },
    empName: {
      type: String,
      default: undefined
    }
  },
  data () {
    return {
      show: false,
      picFileList: [],
      loading: false,
      visible: false,
      isImg: true,
      picPath: '',
      pdfViewer: '',
      timeInterval: null
    };
  },
  created () {
    this.getDictMap('emp_pic_type');
    // 本地调试需要public目录
    let path = process.env.NODE_ENV == 'development' ? '/static/' : './static/';
    this.pdfViewer = path + 'pdfjs-dist/web/viewer.html?file=';
  },
  methods: {
    moment,
    // 初始化数据
    init (data, type) {
      this.show = (type && type != '3');
      data = (data || []);
      this.getPicFileList(data);
    },
    // 设置图片附件信息
    getPicFileList (data) {
      const types = [];
      data.forEach(item => {
        types.push(item.empPicType);
      });
      const self = this;
      if (self.dictMap['emp_pic_type'] && self.dictMap['emp_pic_type'].length > 0) {
        clearInterval(self.timeInterval);
        self.timeInterval = null;
        let picFileList = [];
        self.dictMap['emp_pic_type'].forEach(item => {
          if (!types.includes(item.dataValue)) {
            let obj = {
              'empPicType': item.dataValue,
              'dataLable': item.dataLable,
              'file': '',
              'fileName': '',
              'empPicAnnexName': ''
            };
            picFileList.push(obj);
          } else {
            let photoList = data.filter(photo => (photo.empPicType == item.dataValue));
            if (photoList.length > 0) {
              let obj = photoList[0];
              obj.dataLable = item.dataLable;
              obj.fileName = obj.annexName;
              picFileList.push(obj);
            }
          }
        });
        self.picFileList = picFileList;
        return;
      }
      if (!self.timeInterval) {
        self.timeInterval = setInterval(function () {
          self.getPicFileList(data);
        }, 100);
      }
    },
    // 主页-获取数据
    getData () {
      let data = JSON.parse(JSON.stringify(this.picFileList));
      return data;
    },
    // 设置不显示
    setDisabled (data) {
      if (data && data.indexOf('.doc') == -1 && data.indexOf('.docx') == -1) {
        return true;
      } else {
        return false;
      }
    },
    // 照片更新
    fileChange (index, obj) {
      var testmsg = obj.fileName.substring(obj.fileName.lastIndexOf('.') + 1);
      this.picFileList[index].file = obj.file;
      this.picFileList[index].path = 'data:image/jpg;base64,'.concat(obj.file);
      this.picFileList[index].fileName = this.empName + '_' + this.picFileList[index].dataLable + '.' + testmsg;
      this.$nextTick(() => {
        this.$refs.picFileTable.updateData();
      });
      // 个人主页实时上传
      if (this.type) {
        this.loading = true;
        let row = Object.assign({}, this.picFileList[index]);
        row.sysTenantId = Vue.ls.get(TENANT_ID);
        row.userAccount = Vue.ls.get(USER_NAME);
        row.empId = this.empId;
        savePhoto({ 'photo': [row] }).then(res => {
          this.loading = false;
          if (res.result_code == '1') {
            this.$message.success('操作成功');
            this.$emit('refresh');
          } else {
            this.$message.warning(res.result_msg);
          }
        });
      }
    },
    // 图片删除
    deleteAnnex (index) {
      let self = this;
      self.$confirm({
        title: '确定删除此附件吗?',
        okText: '确定',
        cancelText: '取消',
        onOk () {
          let id = self.picFileList[index].id;
          // 个人主页实时删除
          if (self.type && id) {
            self.loading = true;
            delPhotoById({ 'ids': [id] }).then(res => {
              self.loading = false;
              if (res.result_code == '1') {
                self.$message.success('数据删除成功');
                self.remove(index);
              } else {
                self.$message.warning(res.result_msg);
              }
            });
          } else {
            self.remove(index);
          }
        }
      });
    },
    remove (index) {
      let self = this;
      self.picFileList[index].file = '';
      self.picFileList[index].path = '';
      self.picFileList[index].fileName = '';
      self.$nextTick(() => {
        self.$refs.picFileTable.updateData();
      });
    },
    // 图片预览\下载
    lookOrDown (row, type) {
      const self = this;
      self.loading = true;
      let fileId = (row.hasOwnProperty('fileId') ? row.fileId : '');
      let annexName = row.annexName || row.fileName;
      let path = (row.hasOwnProperty('path') ? row.path : '');
      // path有值表示图片是本地上传的，不需要请求后台加载图片
      if (path) {
        self.loading = false;
        if (type == 'look') {
          let data = {
            fileBase64Code: path.split(',')[1],
            fileType: (annexName.substring(annexName.length - 3, annexName.length + 1) == 'pdf' ? 'application/pdf' : '')
          };
          self.picPath = self.getFilePath(data);
          self.visible = true;
        } else {
          const result_data = {
            fileBase64Code: path,
            fileType: '',
            fileName: annexName
          };
          self.$downloadFile(result_data);
        }
      } else {
        downloadBizAnnex({ 'fileId': fileId }).then(res => {
          self.loading = false;
          if (res.result_code == '1') {
            if (type == 'look') {
              self.picPath = self.getFilePath(res.result_data);
              self.visible = true;
            } else {
              self.$downloadFile(res.result_data);
            }
          } else {
            self.$message.warning(res.result_msg);
          }
        }).catch(() => {
          self.loading = false;
        });
      }
    },
    getFilePath (data) {
      if (!data) {
        return '';
      }
      if (data.fileType == 'application/pdf') {
        this.isImg = false;
        let atob = window.atob(data.fileBase64Code);
        let l = atob.length;
        let u8Arr = new Uint8Array(l);
        while (l--) {
          u8Arr[l] = atob.charCodeAt(l);
        }
        if (window.createObjectURL != undefined) { // basic
          return window.createObjectURL(new Blob([u8Arr], {
            type: 'application/pdf'
          })) + '#toolbar=1';
        } else if (window.webkitURL != undefined) { // webkit or chrome
          return window.webkitURL.createObjectURL(new Blob([u8Arr], {
            type: 'application/pdf'
          })) + '#toolbar=1';
        } else if (window.URL != undefined) { // mozilla(firefox)
          return window.URL.createObjectURL(new Blob([u8Arr], {
            type: 'application/pdf'
          })) + '#toolbar=1';
        }
        completion();
      } else {
        this.isImg = true;
        return 'data:image/jpg;base64,'.concat(data.fileBase64Code);
      }
    },
    // 关闭预览窗口
    close () {
      this.visible = false;
      this.$nextTick(() => {
        this.picPath = '';
        this.isImg = true;
      });
    }
  }
};
</script>

<style lang="less" scoped>
</style>
