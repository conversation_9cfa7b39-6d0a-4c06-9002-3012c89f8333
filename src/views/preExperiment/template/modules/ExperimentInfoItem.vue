<template>
  <div class='experiment-info-item'>
    <a-row>
      <a-col :xl="8" :sm="12" :xs="24">
        <a-form-model-item :label="`${experimentLen-projectIndex}.试验项目`"   :prop="'testProjectList.' + projectIndex + '.projectName'" :rules="{ required: true, message: '此项为必填项' }">
          <a-select v-model="experimentItemData.projectName" placeholder="请选择试验项目" :filter-option="$filterOption"  option-filter-prop="children" :showSearch='true'>
            <a-select-option
              v-for="item in dictFn().pre_test_project_type"
              :key="item.dataId"
              :value="item.dataValue"
            >{{ item.dataLable }}</a-select-option
            >
          </a-select>
        </a-form-model-item>
      </a-col>
      <a-col :xl="8" :sm="12" :xs="24">
        <a-form-model-item label="状态"  :prop="'testProjectList.' + projectIndex + '.isEnable'" :rules="{ required: true, message: '此项为必填项',type: 'number' }">
          <a-radio-group  v-model="experimentItemData.isEnable">
            <a-radio  :value="1">启用</a-radio>
            <a-radio  :value="0">禁用</a-radio>
          </a-radio-group>
        </a-form-model-item>
      </a-col>
      <a-col :xl="8" :sm="12" :xs="24" class='experiment-item-add'>
        <div class='ant-row ant-form-item operation-btn'>
          <span class='operation-btn-cursor' @click='addTableFn(projectIndex)'><a-icon style='margin-right: 8px' type='plus-circle'/>新增</span>
          <span class='operation-btn-cursor' v-if='experimentLen>1' @click='removePreExperimentFn(projectIndex, experimentItemData.id)'><a-icon style='margin-right: 8px' type='delete'/>删除项目</span>
        </div>
      </a-col>
    </a-row>
    <div class='row-col-table-box'>
      <RowAndColTableItem
        v-for='(item,tableItemIndex) in experimentItemData.projectTitleList'
        :rowAndColTableLen='experimentItemData.projectTitleList.length'
        :projectIndex='projectIndex'
        :tableItemIndex='tableItemIndex'
        :key='tableItemIndex'
        :tableItem='item' />
    </div>
  </div>
</template>

<script>
import RowAndColTableItem from '@views/preExperiment/template/modules/RowAndColTableItem.vue';
export default {
  components: { RowAndColTableItem },
  inject: ['addTableFn', 'removePreExperimentFn', 'dictFn'],
  props: {
    experimentItemData: {
      type: Object,
      default: () => ({})
    },
    projectIndex: {
      type: Number,
      default: () => 0
    },
    // 试验项目个数
    experimentLen: {
      type: Boolean,
      default: false
    }
  }

};
</script>

<style lang='less' scoped>
  .experiment-info-item{
    &:not(:last-child){
      margin-bottom: 40px;
    }
    :deep(.experiment-item-add){
      display: flex;
      justify-content: flex-end;
      .operation-btn{
        display: flex;
        align-items: center;
        .operation-btn-cursor:not(:last-child){
          margin-right: 24px;
        }
        .operation-btn-cursor{
          display: inline-block;
          height: 40px;
          line-height: 40px;
        }
      }
    }
    .row-col-table-box{
      // border-radius: 3px;
      // background: #F8F8F8;
      // padding: 16px 16px 24px;
      margin-left: 24px;
      //margin-bottom: 40px;
    }
  }

</style>
