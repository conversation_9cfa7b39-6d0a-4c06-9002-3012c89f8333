<!-- 我的下载 -->
<template>
  <div>
    <div class="desc solar-eye-pure-bg">
      <a-icon type="exclamation-circle" />&nbsp;&nbsp;根据文件大小不同，文件准备时间约为5分钟至2小时，准备完成后首次自动下载，链接30天内可重复下载，请耐心等待！
    </div>
    <div class="solar-eye-main-content">
      <a-spin :spinning="loading">
        <vxe-table ref="excelTable" :data="tableList" :seq-config="{startIndex: (params.curPage - 1) * params.size}"
          resizable border align="center" :height="tableHeight" size="small">
          <vxe-table-column field="annexName" title="文件名称"></vxe-table-column>
          <vxe-table-column field="createTime" title="导出时间"></vxe-table-column>
          <vxe-table-column field="status" title="导出状态">
            <template v-slot:default="{ row }">
              <div v-show="row.status ==1" class="bg-table bg-success" @click="downloadEvent(row.id)">
                <a-icon type="download" />&nbsp;&nbsp;可下载连接
              </div>
              <div v-show="row.status ==0" class="bg-table bg-prepar">
                <a-icon type="loading" /> &nbsp;&nbsp;文件准备中
              </div>
              <div v-show="row.status ==-1" class="bg-table bg-error">
                <a-icon type="close-circle" />&nbsp;&nbsp; 导出失败
              </div>
              <div v-show="row.status ==-2" class="bg-table bg-prepar">
                <a-icon type="close-circle" />&nbsp;&nbsp;数据为空无法导出
              </div>
            </template>
          </vxe-table-column>
          <vxe-table-column field="expirationTxt" title=" "></vxe-table-column>
          <vxe-table-column field="annexSizeTxt" title="文件大小"></vxe-table-column>
          <vxe-table-column field="downloadTimes" title="下载次数"></vxe-table-column>
          <vxe-table-column :visible="showHandle('msg:detail')" title="操作" fixed="right" :width="120" align="center" :resizable="false" class-name="fixed-right-column-120">
            <template v-slot:default="{ row }">
              <throttle-button icon="download" title="下载" v-show="row.status==1" @click="downloadEvent(row.id)" />
              <throttle-button icon="delete" title="删除" v-show="row.status!=0" @click="deleteEvent(row.id)" />
            </template>
          </vxe-table-column>
        </vxe-table>
        <!--分页组件-->
        <page-pagination :pageSize="params.size" :current="params.curPage" :total="total"
          @size-change="pageChangeEvent" />
      </a-spin>
    </div>
  </div>
</template>

<script>
import {
  OSSFileList,
  deleteOSSFile,
  getUrlExportFile
} from '@/api/config/downBigExcel';
import tableHeight from '@/mixins/tableHeightAndSearchModel';
export default {
  name: 'downBigExcel',
  mixins: [tableHeight],
  data () {
    return {
      loading: false,
      // 查询条件
      params: {
        curPage: 1,
        size: 10
      },
      total: 0,
      tableList: []
    };
  },
  created () {
    this.getList();
  },
  methods: {
    // 分页事件
    pageChangeEvent (page, size) {
      Object.assign(this.params, {
        curPage: page,
        size: size
      });
      this.getList();
    },
    // 数据查询
    async getList () {
      this.loading = true;
      let params = Object.assign({}, this.params);
      let excelTable = this.$refs.excelTable;
      excelTable && await excelTable.clearScroll();
      OSSFileList(params)
        .then(res => {
          this.total = res.result_data.total;
          this.tableList = res.result_data.rows;
          this.loading = false;
          this.serachReadyingFile();
          this.autoDownLoad();
        })
        .catch(() => {
          this.total = 0;
          this.tableList = [];
          this.loading = false;
        });
    },
    serachReadyingFile () {
      let readyList = this.getReadyList(true);
      if (readyList.length == 0) {
        return;
      }
      for (let i = 0; i < readyList.length; i++) {
        let timer = setInterval(() => {
          getUrlExportFile({
            id: readyList[i]
          }).then(res => {
            if (res.result_code == '1') {
              if (typeof res.result_data == 'number') {
                if (res.result_data != 0) {
                  this.clearForTime(timer * readyList.length);
                }
              } else {
                this.clearForTime(timer * readyList.length);
                window.location.href = res.result_data;
              }
            }
          });
        }, 5000);
      }
      // this.clearTimer()
    },
    clearForTime (timer) {
      let end = setInterval(function () {}, 5000);
      for (let i = 0; i <= end; i++) { // 解决多个文件准备中，重复下载的问题
        clearInterval(i);
      }
      if (timer) {
        this.getList();
      }
    },
    clearTimer () { // 清除页面中的所有定时器
      this.$once('hook:beforeDestroy', () => {
        this.clearForTime();
      });
      this.$once('hook:deactivated', () => {
        this.clearForTime();
      });
    },
    /** 获取准备中的列表
       * params isReady true 返回准备中的列表， fasle 返回需要自动下载的id
       */
    getReadyList (isReady) {
      let list = [];
      let autoDownList = [];
      this.tableList.forEach(item => {
        if (item.status == 0) {
          list.push(item.id);
        }
        if (item.downloadTimes == 0 && item.status == 1) {
          autoDownList.push(item.id);
        }
      });
      return isReady ? list : autoDownList;
    },
    autoDownLoad () {
      // 所有的请求结束之后，刷新列表，然后定时刷新在请求的数据
      let autoDownList = this.getReadyList(false);
      let all = [];
      if (autoDownList.length == 0) {
        return;
      }
      for (let i = 0; i < autoDownList.length; i++) {
        let downLoad = new Promise((resolve, reject) => {
          getUrlExportFile({
            id: autoDownList[i]
          }).then(res => {
            if (res.result_code == '1') {
              resolve(res.result_data);
            }
          });
        });
        all.push(downLoad);
      }
      Promise.all(all).then(res => {
        res.forEach(item => {
          window.location.href = item;
        });
        this.getList();
      });
    },
    // 表格列刷新
    refreshTableColumn () {
      this.$refs.excelTable.refreshColumn();
    },
    downloadEvent (id) {
      this.clearForTime();
      getUrlExportFile({
        id: id
      }).then(res => {
        if (res.result_code == '1') {
          window.location.href = res.result_data;
          this.getList();
        }
      });
    },
    deleteEvent (id) {
      this.$confirm({
        title: '确定要删除吗?',
        okText: '确定',
        cancelText: '取消',
        onOk: () => {
          deleteOSSFile({
            id: id
          })
            .then(res => {
              if (res.result_code == 1) {
                this.$message.success(res.result_msg || '删除成功!');
                this.getList();
              } else {
                this.$message.warning(res.result_msg || '删除失败');
              }
            })
            .catch(err => {
              console.log(err);
            });
        }
      });
    }
  }
};
</script>

<style lang="less" scoped>
  .desc {
    text-align: center;
    color: red;
    background: rgba(249, 237, 219, 0.7);
    padding: 8px;
  }

  .bg-table {
    width: 80%;

    &.bg-success {
      color: #1890ff;
      cursor: pointer;
    }

    &.bg-error {
      color: red;
    }

    &.bg-prepar {
      color: #ff8f33;
    }
  }
  :deep(.fixed-right-column-120) {
    height: 44px;
  }
</style>
