<template>
  <a-drawer
    width="100%"
    :visible="visible"
    title="映射"
    @close="close(false)"
    :get-container="getDom"
    :wrap-style="{ position: 'absolute' }"
  >
    <a-spin :spinning="loading" style="height: 100%;">
      <div class="map-content">
        <div class="left-content">
          <div class="select-div flex-center">
            <div>接入来源：</div>
            <a-select v-model="leftData.source" disabled class="select">
              <a-select-option v-for="item in sourceOption" :key="item.value" :value="item.value">
                {{ item.label }}
              </a-select-option>
            </a-select>
          </div>
          <div class="select-div flex-center">
            <div>协议任务：</div>
            <a-select v-model="leftData.task" disabled class="select">
              <a-select-option v-for="item in taskOption" :key="item.value" :value="item.value">
                {{ item.label }}
              </a-select-option>
            </a-select>
          </div>
          <div class="select-div flex-center">
            <div>规则分类：</div>
            <a-select v-model="leftData.groupInstance" disabled class="select">
              <a-select-option v-for="item in groupInstanceOption" :key="item.value" :value="item.value">
                {{ item.label }}
              </a-select-option>
            </a-select>
          </div>
          <div class="select-div flex-center">
            <div>参数标识：</div>
            <a-select v-model="leftData.paramCode" disabled class="select">
              <a-select-option v-for="item in paramCodeOption" :key="item.value" :value="item.value">
                {{ item.label }}
              </a-select-option>
            </a-select>
          </div>
        </div>
        <div class="right-content flex-center">
          <div class="select-box">
            <div class="select-head flex-center">
              {{ iotTotal }}项（{{ sourceOption.length > 0 ? sourceOption[0].label : '' }}）
            </div>
            <div class="select-content">
              <a-input-search v-model="iotDeviceModel" palceholder="请输入关键字搜索" @search="getIotModelList(1)" allowClear/>
              <div class="content-div">
                <div class="left-item flex-center" v-for="(item, key) in iotList" :key="item.dcDeviceModelId">
                  <div class="left-label over-flow" :class="{'active-text': activeIot == item.dcDeviceModelId}"
                   @click="setActiveIot(item, key)" :title="item.p110010">
                    {{ item.p110010 }}
                  </div>
                  <div v-if="item.deviceModel" class="flex-center">
                    <span class="link over-flow" :title="item.deviceModel">
                      <svg-icon class="link-icon" iconClass="link"/>{{ item.deviceModel }}
                    </span>
                    <span title="解绑"><svg-icon class="unbind-icon" iconClass="link-unlink" @click="doUnbind(item)"/></span>
                  </div>
                </div>
              </div>
              <page-pagination :pageSize="iotPage.pageSize" :current="iotPage.currentPage"
                :total="iotTotal" @size-change="iotSizeChange"/>
            </div>
          </div>
          <div class="select-box">
            <div class="select-head flex-center">
              {{ standardTotal }}项（{{ 'SolarEye' }}）
            </div>
            <div class="select-content">
              <a-input-search v-model="standardDeviceModel" palceholder="请输入关键字搜索" @search="getStandardModelList(1)" allowClear />
              <div class="content-div">
                <a-radio-group v-model="activeStandard" @change="standardChange">
                  <a-radio v-for="item in standardList" :style="radioStyle" :key="item.id" 
                    :value="item.id" class="over-flow" :title="item.deviceModel">
                    {{ item.deviceModel }}
                  </a-radio>
                </a-radio-group>
              </div>
              <page-pagination :pageSize="standardPage.pageSize" :current="standardPage.currentPage"
                :total="standardTotal" @size-change="standardSizeChange"/>
            </div>
          </div>
          <div></div>
        </div>
      </div>
      <div class="bottom-div">
        <a-button class="solar-eye-btn-primary-cancel" @click="close">取消</a-button>
        <a-button class="solar-eye-btn-primary" @click="doSave" :disabled="changeArr.length == 0">保存</a-button>
      </div>
    </a-spin>
  </a-drawer>
</template>
<script>
import { getModelMappingParam, getIotModelList, getStandardModelList, modelMapping } from '@/api/dataCenter/mapping.js';
export default {
  props: {
    parentId: {
      type: String,
      default: ''
    },
    source: {
      type: String,
      default: ''
    }
  },
  components: { },
  data() {
    return {
      visible: false,
      loading: false,
      total: 0,
      iotPage: {
        currentPage: 1,
        pageSize: 10
      },
      standardPage: {
        currentPage: 1,
        pageSize: 10
      },
      radioStyle: {
        display: 'block',
        height: '21px',
        lineHeight: '21px',
        marginTop: '20px',
        maxWidth: '410px'
      },
      value: undefined,
      leftData: {
        source: undefined,
        task: undefined,
        groupInstance: undefined,
        paramCode: undefined
      },
      row: {},
      iotList: [],
      standardList: [],
      iotTotal: 0,
      standardTotal: 0,
      activeStandard: null,
      activeIot: null,
      iotDeviceModel: undefined,
      standardDeviceModel: undefined,
      changeArr: [],
      activeIotIndex: -1,
      sourceOption: [],
      taskOption: [],
      groupInstanceOption: [],
      paramCodeOption: [],
    }
  },
  methods: {
    // 抽屉绑定dom元素
    getDom () {
      return document.getElementsByClassName(this.parentId)[0];
    },
    // 页面初始化
    initDrawer (row) {
      getModelMappingParam(row).then(res => {
        let data = res.result_data;
        this.setOptionAndValue(data, 'source');
        this.setOptionAndValue(data, 'task');
        this.setOptionAndValue(data, 'groupInstance');
        this.setOptionAndValue(data, 'paramCode');
      })
      this.initData(row);
      this.getIotModelList();
      this.getStandardModelList();
    },
    // 获取左侧下拉框选项及初始值
    setOptionAndValue(data, prop) {
      let obj = data[prop];
      for (let key in obj) {
        if (obj.hasOwnProperty(key)) {
          this[prop + 'Option'].push({
            label: obj[key],
            value: key
          })
        }
      }
      let option = this[prop + 'Option'];
      this.leftData[prop] = option.length > 0 ? option[0].value : '';
    },
    // 数据初始化
    initData(row) {
      this.visible = true;
      this.row = row;
      this.activeIot = null;
      this.activeStandard = null;
      this.changeArr = [];
      this.iotPage = {
        currentPage: 1,
        pageSize: 10
      };
      this.standardPage = {
        currentPage: 1,
        pageSize: 10
      };
      this.iotDeviceModel = undefined;
      this.standardDeviceModel = undefined;
      this.sourceOption = [];
      this.taskOption = [];
      this.groupInstanceOption = [];
      this.paramCodeOption = [];
    },
    // 获取三方列表
    getIotModelList(page) {
      if(page) {
        this.iotPage.currentPage = 1;
      }
      let params = {
        ...this.row,
        ...this.iotPage,
        deviceModel: this.iotDeviceModel,
        source: this.source
      }
      this.loading = true;
      this.activeIotIndex = -1;
      this.activeIot = null;
      this.activeStandard = null;
      getIotModelList(params).then(res => {
        if(res.result_data.pageList && res.result_data.pageList.length > 0) {
          this.iotList = res.result_data.pageList;
          this.iotList.forEach(item => {
            if(this.changeArr.length > 0) {
              let res = this.changeArr.find(ele => { return ele.dcDeviceModelId == item.dcDeviceModelId });
              if(res) {
                item.deviceModel = res.deviceModel;
                item.erpDeviceMakerModelId = res.erpDeviceMakerModelId;
              }
            }
          })
          this.iotTotal = res.result_data.rowCount;
        } else {
          this.iotList = [];
          this.iotTotal = 0;
        }
        this.loading = false;
      }).catch(() => {
        this.loading = false;
        this.iotList = [];
        this.iotTotal = 0;
      })
    },
    // 获取solareye(标准)列表
    getStandardModelList(page) {
      if(page) {
        this.standardPage.currentPage = 1;
      }
      let params = {
        deviceModel: this.standardDeviceModel,
        ...this.standardPage
      }
      this.loading = true;
      getStandardModelList(params).then(res => {
        if(res.result_data.pageList && res.result_data.pageList.length > 0) {
          this.standardList = res.result_data.pageList;
          this.standardTotal = res.result_data.rowCount;
        } else {
          this.standardList = [];
          this.standardTotal = 0;
        }
        this.loading = false;
      }).catch(() => {
        this.loading = false;
        this.standardList = [];
        this.standardTotal = 0;
      })
    },
    // 选中要进行绑定的三方型号
    setActiveIot(data, index) {
      this.activeIot = data.dcDeviceModelId;
      this.activeStandard = data.erpDeviceMakerModelId;
      this.activeIotIndex = index;
    },
    // 三方型号解绑标准型号
    doUnbind(data) {
      data.deviceModel = null;
      data.erpDeviceMakerModelId = null;
      if(this.activeIot == data.dcDeviceModelId) {
        this.activeStandard = null;
      }
      let index = this.changeArr.findIndex(item => {
        return item.dcDeviceModelId == data.dcDeviceModelId
      })
      let obj = {
        versionId: null,
        dcDeviceModelId: data.dcDeviceModelId,
        deviceModel: null,
        erpDeviceMakerModelId: null
      };
      if(index > -1) {
        this.changeArr[index] = obj;
      } else {
        this.changeArr.push(obj)
      }
    },
    // 标准型号勾选绑定
    standardChange() {
      let data = this.standardList.find(item => {
        return item.id == this.activeStandard;
      })
      if(!this.activeIot) {
        this.$message.warning('请先选择左侧型号');
        this.activeStandard = null;
        return
      }
      this.iotList[this.activeIotIndex].deviceModel = data.deviceModel;
      this.iotList[this.activeIotIndex].erpDeviceMakerModelId = data.id;
      let index = this.changeArr.findIndex(item => {
        return item.dcDeviceModelId == this.activeIot
      })
      let obj = {
        versionId: data.versionId,
        dcDeviceModelId: this.activeIot,
        deviceModel: data.deviceModel,
        deviceModelId: data.deviceModelId,
        makerId: data.makerId,
        maker: data.maker,
        ancestors: data.ancestors,
        erpDeviceMakerModelId: data.id
      }
      if(index > -1) {
        this.changeArr[index] = obj;
      } else {
        this.changeArr.push(obj)
      }
    },
    // 三方列表页数变化
    iotSizeChange(current, size) {
      this.iotPage.pageSize = size;
      this.iotPage.currentPage = current;
      this.getIotModelList();
    },
    // 标准列表页数变化
    standardSizeChange(current, size) {
      this.standardPage.pageSize = size;
      this.standardPage.currentPage = current;
      this.getStandardModelList();
    },
    // 保存
    doSave() {
      // if(this.changeArr.length == 0) {
      //   this.$message.warning('未进行绑定/解绑操作，无法保存');
      //   return;
      // }
      let params = {
        mappingList: this.changeArr,
        taskId: this.row.taskId,
        sourceId: this.row.sourceId
      }
      this.loading = true;
      modelMapping(params).then(res => {
        this.close();
        this.$message.success('保存成功');
        this.loading = false;
      }).catch(() => { this.loading = false; })
    },
    // 关闭
    close () {
      this.visible = false;;
    }
  }
}
</script>
<style lang="less" scoped>
:deep(.ant-drawer-body) {
  height: calc(100% - 55px);
  padding: 0 24px;
}
:deep(.ant-spin-container) {
  height: 100%;
}
.map-content {
  display: flex;
  height: calc(100% - 88px);
  .left-content {
    height: calc(100% - 10px);
    width: 300px;
    border-right: 1px solid #E8E8E8;
    overflow: auto;
    padding: 14px 0 24px;
    margin-top: 10px;
    .select-div {
      margin-bottom: 30px;
      .select {
        width: 190px;
      }
    }
  }
  .right-content {
    width: calc(100% - 300px);
    padding: 32px 0 24px 24px;
    height: 100%;
    .select-box {
      border: 1px solid #DCDCDC;
      height: 100%;
      .select-head {
        background: #EAEAEA;
        height: 60px;
        width: 494px;
        padding-left: 48px;
        color: #3D3D3D;
      }
      .select-content {
        height: calc(100% - 62.5px);;
        padding: 24px 24px 16px 48px;
        .content-div {
          margin-top: 8px;
          height: calc(100% - 72px);
          overflow: auto;
          .left-item {
            margin-top: 20px;
            .left-label {
              cursor: pointer;
              max-width: 188px;
            }
            .operation-icon {
              cursor: pointer;
            }
          }
          .link {
            margin: 0 10px 0 12px;
            background: #F0F0F0;
            padding: 0 6px;
            max-width: 188px;
          }
        }
      
      }
    }
    .select-box + .select-box {
      margin-left: 36px;
    }
  }
}
.bottom-div {
  height: 64px;
  flex-direction: row;
  justify-content: center;
  align-items: center;
  display: flex;
  border-top: 1px solid #E8E8E8;
  margin-top: 24px;
  button + button {
    margin-left: 16px;
  }
}
.left-label:hover, .active-text {
  color: var(--zw-primary-color--default);
}
.link-icon {
  color: #666666;
  margin-right: 6px;
  font-size: 16px;
}
.unbind-icon {
  color: var(--zw-primary-color--default);
  font-size: 16px;
  cursor: pointer;
}
:root[data-theme='dark'] {
  .map-content {
    .left-content {
      border-right: 1px solid rgba(53, 70, 97, 0.6);
    }
    .right-content {
      .select-box {
        border: 1px solid rgba(53, 70, 97, 0.6);
        .select-head {
          background: rgba(78, 121, 177, 0.16);
          color: #fff;
        } 
        .select-content .content-div .link {
          background: rgba(78, 121, 177, 0.16);
        }
      }
    }
  }

  .left-label:hover, .active-text {
    color: #24CCFF;
  }
  .bottom-div {
    border-top: 1px solid rgba(53, 70, 97, 0.6);;
  }
  .link-icon {
    color: #fff;
  }
  .unbind-icon {
    color: #24CCFF;
  }
}
</style>