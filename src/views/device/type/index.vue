<!-- 设备类型 -->
<template>
<div >
  <a-spin :spinning="tabLoading">
    <div class="device-content">
      <div class="device-info">
        <div class="device-box">
          <div class="device-title">
            <span style="font-weight: bold;">设备类型</span>
            <span class="expanded-text" @click="expandedTree">{{expandedKeys.length === 1 || expandedKeys.length === 0 || !expandedKeys.includes(0) ? '展开' : '收起'}}全部</span>
          </div>
          <div class="tree-info">
            <div class="search-info">
              <a-input-search @search="searchInfo" class="search-input" v-model="keyword" allowClear placeholder="请输入关键词搜索" />
              <a-icon class="add-tree" v-has="'deviceType:add_health'" type="plus-circle" @click="addClick" />
            </div>
            <!-- 树-->
              <a-col :md="10" :sm="24"
                    v-bind:style="{'height':boxHeight + 'px','overflow': 'auto', 'width': '100%', 'paddingLeft': '15px'}">
                <template>
                  <a-tree :selectedKeys="selectedKeys" @select="onSelect" class="draggable-tree" block-node :treeData="treeData"  :replaceFields="{title: 'name', key: 'deviceTypeId'}"
                  @expand="onExpand"
                  :auto-expand-parent="autoExpandParent"
                  :expanded-keys="expandedKeys">
                    <template slot="title" slot-scope="{ deviceStatus, name, deviceTypeId, isAll }">
                      <div class="device-operation" @mouseover="hoverId = deviceTypeId" @mouseleave="hoverId = ''">
                        <div class="name-info">
                          <svg-icon v-if="deviceStatus === '0'" iconClass="forbidden"></svg-icon>
                          <a-tooltip placement="top">
                            <template #title>{{ name }}</template>
                            <div class="operation-name">{{ name }}</div>
                          </a-tooltip>
                        </div>
                        <div class="operation-btns" v-if="!isAll && hoverId === deviceTypeId">
                            <a-icon v-has="'deviceType:edit_health'" type="edit" @click.stop="editDevice(deviceTypeId)" />
                            <a-icon v-has="'deviceType:delete_health'" type="delete" @click.stop="delDevice(deviceTypeId)" />
                        </div>
                        <div v-else style="width: 53px;"></div>
                      </div>
                    </template>
                  </a-tree>
                  <div class="no-data" v-if="treeData && treeData.length === 0">暂无数据</div>
                </template>
            </a-col>
          </div>
        </div>
      </div>
    <div class="device-table">
    <!--工具栏-->
    <div  class="solar-eye-search-model">
     <a-row :gutter="24" class="solar-eye-search-content">
            <a-col :xxl="6" :xl="8" :md="12">
             <div class="search-item">
                <span class="search-label">设备型号</span>
          <DeviceModelList :deviceTypeId="deviceModelParams.deviceTypeId" :isMultiple="true" ref="deviceModelList" v-model="deviceModelParams.makerModelIds"  allow-clear/>
        </div>
      </a-col>
      <a-col :xxl="6" :xl="8" :md="12">
        <div class="search-item">
          <span class="search-label">生产厂家</span>
          <DeviceMakerList :deviceTypeId="deviceModelParams.deviceTypeId" :isMultiple="true" ref="deviceMakerList" v-model="deviceModelParams.makerIds"  allow-clear/>
        </div>
      </a-col>
      <a-col :xxl="3" :xl="6" :md="8">
        <div class="search-item">
          <throttle-button label="查询" @click="pageChange(1)" />
        <a-button :disabled="loading" :loading="loading" @click="resetParams()">重置</a-button>
        </div>
      </a-col>
    </a-row>
    </div>
    <div class="solar-eye-gap"></div>
      <a-col class="solar-eye-main-content">
      <!-- 操作按钮 -->
        <div class="operation" style="height: 32px">
          <div class="forbidden-content">
            <span class="content-name">{{ selectedDeviceType && selectedDeviceType.name }}</span>
            <div class="forbidden-btn" v-if="selectedDeviceType && selectedDeviceType.deviceStatus === '0'">
              <svg-icon iconClass="forbidden"></svg-icon>
              <span>禁用</span>
            </div>
          </div>
          <div class="operation-btn">
            <a-button v-has="'type:add_health'" class='solar-eye-btn-primary' @click="rowCilck('1')">新增</a-button>
            <a-button v-has="'type:delete_health'" size="default" class="solar-eye-btn-grey"
                  @click="deleteClick" :disabled="checkData.length === 0">删除</a-button>
          </div>
      </div>
        <!--表格渲染-->
        <vxe-table :data="tabData"  ref="multipleTable" align="left"
         :height="tableHeight - 24" @checkbox-all="tableHandleSelection" @checkbox-change="tableHandleSelection" size="small" :seq-config="{startIndex: (page - 1) * size}"  show-overflow highlight-hover-row resizable  class='my-table'>
         <vxe-table-column type="checkbox" width="50" fixed='left'></vxe-table-column>
         <vxe-table-column  type="seq"  :width="80" title="序号"></vxe-table-column>
         <vxe-table-column show-overflow="title" min-width="120"  field="id" title="设备型号ID">
          </vxe-table-column>
          <vxe-table-column show-overflow="title" min-width="220"  field="deviceModel" title="设备型号">
          </vxe-table-column>
          <vxe-table-column show-overflow="title" min-width="220"  :formatter="tabFormatter" field="deviceTypeName" title="设备类型" ></vxe-table-column>
          <vxe-table-column show-overflow="title" min-width="220"  :formatter="tabFormatter" field="maker" title="生产厂家"></vxe-table-column>
          <vxe-table-column show-overflow="title" min-width="120" :formatter="tabFormatter" field="createUserName" title="创建人" ></vxe-table-column>
          <vxe-table-column show-overflow="title" min-width="120" :formatter="tabFormatter" field="createTime"  title="创建时间" ></vxe-table-column>
          <vxe-table-column align="left" :visible="showHandle(perms)" fixed="right" title="操作"  width="120" :resizable="false" >
            <template v-slot="{ row }">
              <throttle-button v-has="'type:edit_health'" title="编辑" icon="edit" @click="rowCilck('2', row.id)" />
                <throttle-button v-has="'type:details_health'" title="详情" icon="file-text" @click="rowCilck('4', row.id)" />
            </template>
                      </vxe-table-column>
          <template v-slot:empty>
            <span class="com-color">查询无数据</span>
          </template>
        </vxe-table>
        <!--分页组件-->
        <page-pagination :pageSize="size" :current="page" :total="total" @size-change="sizeChange"/>
      </a-col>
    </div>
    </div>
    </a-spin>
    <!-- 新增、编辑 -->
    <a-modal v-model="detailOpen" :maskClosable="false" centered @cancel="cancel" :title="detailTitle" width="70%">
      <a-spin :spinning="saveLoad">
          <a-form-model :model="deviceForm" :rules="rules" ref="deviceForm" :label-col="{span: 6}" :wrapper-col="{span: 18}">
            <a-row :gutter="24">
              <a-col :sm="24" :md="11">
                <a-form-model-item label="类型名称" prop="name">
                  <a-input size="default" v-model.trim="deviceForm.name" :max-length="10" style="width: 100%"></a-input>
                  </a-form-model-item>
                  </a-col>
                  <a-col :sm="24" :md="4">
                    <div style="width: 100%;height: 1px;"></div>
                    </a-col>
                    <a-col :sm="24" :md="11">
                      <a-form-model-item label="父节点:" prop="pid">
                        <a-cascader :show-search="{ filter }" :allowClear="false" v-model="pid" :options="deviceParent" change-on-select size="default" :field-names="{ label: 'name', value: 'deviceTypeId', children: 'children' }"
                         placeholder="请选择父节点" style="width: 100%"></a-cascader>
                        </a-form-model-item>
                        </a-col>
                </a-row>
                <a-row>
                  <a-col :sm="24" :md="11">
                    <a-form-model-item label="设备类型状态" prop="deviceStatus">
                      <!-- 禁用 '0'  正常  '1' -->
                      <a-switch checked-children="正常" un-checked-children="禁用" v-model="deviceForm.deviceStatus" default-checked @change="onChange" />
                    </a-form-model-item>
                  </a-col>
                </a-row>
            <a-col :sm="24" :md="11">
              <a-form-model-item label="技术参数" style="margin-bottom:10px">
                <div class="erp-group-layout">
                  <div class="layout-search">
                    <a-input size="default" v-model="setItemDataName" @input="filterTable" suffix-icon="el-icon-search" placeholder="请输入关键词搜索"></a-input>
                  </div>
                  <a-checkbox-group v-model="setItemDataIds" @change="add">
                    <a-checkbox class="check-box"  v-for="item in projectList" :key="item.setItemDataId" :value="item.setItemDataId" :title="item.setItemDataName">{{ item.setItemDataName }}</a-checkbox>
                    <div v-show="projectList.length==0" class="no-data add-no-data com-color">查询无数据</div>
                  </a-checkbox-group>
                </div>
              </a-form-model-item>
            </a-col>
            <a-col :span="1">
            </a-col>
            <a-col :sm="24" :md="11">
              <a-col :span="24">
                <div class="erp-group-layout">
                  <div class="layout-selected">
                    <span>已选({{ deviceForm.technicalParameters.length }})</span>
                    <span class="layout-empty" @click="empty">清空</span>
                  </div>
                  <div class="layout-content"  v-for="item in deviceForm.technicalParameters" :key="item.setItemDataId">
                    <span class="data-name">{{ item.setItemDataName }}</span>
                    <a-icon class="close-right" type="close-circle" @click="changeSelectProj(item.setItemDataId)" />
                  </div>
                </div>
              </a-col>
            </a-col>
          </a-form-model>
      </a-spin>
      <template slot="footer">
        <div class="modal-footer">
          <a-button size="default" :disabled="saveLoad" @click="resetForm(true)">取消</a-button>
          <a-button size="default" :loading="saveLoad" type="primary" @click="submitForm()">确定</a-button>
        </div>
      </template>
    </a-modal>

    <drawer-view ref="deviceTypeForm" @cancel="queryClick" parentId="psaArchives" />
    </div>
</template>

<script>
import {
  insertErpDeviceType,
  // getParentDeviceTypeInfoList,
  updateErpDeviceType,
  deleteErpDeviceType,
  detailErpDeviceType,
  getDeviceModelPage,
  removeDeviceModel
} from '@/api/device/type';
import { getComDeviceTypeTree } from '@/api/common/common';
import disposeTree from '../disposeDeviceTree';
import DeviceMakerList from './DeviceMakerList';
import DeviceModelList from './DeviceModelList';
import initDict from '@/mixins/initDict';
import tableHeight from '@/mixins/tableHeightAndSearchModel';
export default {
  mixins: [initDict, tableHeight],
  components: {
    DeviceMakerList,
    DeviceModelList
  },
  data () {
    var validateEquipmentType = (rule, value, callback) => {
      if (value && /[\x21-\x2f\x3a-\x40\x5b-\x60\x7B-\x7F]/gi.test(value)) {
        callback(new Error('类型名称暂不支持特殊字符'));
      } else {
        callback();
      }
      // }
    };
    return {
      selectedKeys: [0],
      selectedDeviceType: {},
      isKeepKeys: false, // 是否保持展开测点
      isFirst: true,
      checkData: [],
      expandedKeys: [0], // 展开的节点
      allKeys: [], // 所有keys
      autoExpandParent: true,
      isExpanded: false,
      keyword: '',
      treeData: [],
      // 查询设备型号列表
      deviceModelParams: {
        makerModelIds: [],
        makerIds: [],
        deviceTypeId: ''
      },
      boxHeight: 400, // 树的高度
      hoverId: '', // 显示编辑 删除按钮
      perms: 'type:edit_health,type:details_health',
      showAll: true,
      deviceName: '',
      tabLoading: false,
      tabData: [],
      total: 0,
      size: 10,
      page: 1,
      addLoad: false,
      // 弹窗
      saveLoad: false,
      detailOpen: false,
      detailTitle: '',
      addOrEdit: '',
      setItemDataIds: [],
      deviceForm: {
        deviceTypeId: '',
        name: '',
        pid: [0],
        deviceStatus: true,
        technicalParameters: []
      },

      projectList: [],
      selectProj: [],
      deviceParent: [],
      rules: {
        name: [{
          required: true,
          message: '请输入类型名称',
          trigger: 'blur'
        }, {
          validator: validateEquipmentType,
          trigger: 'blur'
        },
        {
          min: 1,
          max: 50,
          message: '长度在1到50个字符',
          trigger: 'blur'
        }
        ],
        deviceStatus: [{
          required: true,
          message: '请选择显示状态',
          trigger: 'change'
        }],
        pid: [{
          required: true,
          message: '请选择父节点',
          trigger: 'change'
        }]
      },
      setItemDataName: '',
      filterProject: [],
      selectPorjIds: [],
      pid: [0],
      projectLoading: true
    };
  },
  created () {
    // 获取字典
    this.getDictMap('device_status,unit,device_type_set_item');
  },
  mounted () {
    this.boxHeight = window.innerHeight - 240;
    this.loadTree();
    this.pageChange(1);
  },
  methods: {
    // 搜索后默认选中全部
    searchInfo () {
      this.selectedKeys = [0];
      this.deviceModelParams.deviceTypeId = '';
      this.loadTree();
    },
    // 表格选中事件
    tableHandleSelection (val) {
      this.checkData = val.records;
    },
    // 重置
    resetParams () {
      this.$set(this.deviceModelParams, 'makerModelIds', []);
      this.$set(this.deviceModelParams, 'makerIds', []);
      this.$refs.deviceMakerList.reset();
      this.$refs.deviceModelList.reset();
      // 重置后设备类型id改变 触发设备类型和生产厂家deviceTypeId是以前的
      let deviceTypeIdTemplt = this.deviceModelParams.deviceTypeId;
      this.$set(this.deviceModelParams, 'deviceTypeId', '');
      setTimeout(() => {
        this.$set(this.deviceModelParams, 'deviceTypeId', deviceTypeIdTemplt);
        this.size = 10;
        this.pageChange(1);
      }, 50);
    },
    // 选中树节点
    onSelect (selectedKeys, info) {
      this.selectedKeys = selectedKeys;
      if (info && info.selectedNodes && info.selectedNodes.length) {
        this.selectedDeviceType = info.selectedNodes[0].data.props || {};
      }
      this.deviceModelParams.deviceTypeId = selectedKeys.length === 0 || selectedKeys[0] === 0 ? '' : selectedKeys[0];
      this.resetParams();
    },
    // 查询设备类型详情
    editDevice (id) {
      detailErpDeviceType({
        deviceTypeId: id
      })
        .then((res) => {
          if (res.result_code == '1') {
            let data = res.result_data;
            this.deviceForm = {
              deviceTypeId: data.deviceType.deviceTypeId,
              name: data.deviceType.name,
              pid: data.deviceType.pid,
              ancestors: data.deviceType.ancestors,
              deviceStatus: data.deviceType.deviceStatus === '1',
              technicalParameters: []
            };
            if (data.technicalParameters && data.technicalParameters.length) {
              this.deviceForm.technicalParameters = data.technicalParameters.map(item => {
                return {
                  setItemDataId: item.dataValue,
                  setItemDataName: item.dataLabel,
                  id: '',
                  dataSort: item.dataSort
                };
              });
            }
            this.addOrEdit = 'edit';
            this.initParentDeviceTypeInfoList('edit');
            this.detailTitle = '编辑设备类型';
            this.detailOpen = true;
            this.getProjectList(this.deviceForm.technicalParameters.map(item => item.setItemDataId));
          } else {
            this.$message.error(res.result_msg);
          }
        })
        .catch(() => {
        });
    },
    onExpand (expandedKeys) {
      this.expandedKeys = expandedKeys;
      this.autoExpandParent = false;
    },
    // 展开/收起树
    expandedTree (data) {
      let keys = this.expandedKeys.length === 1 || this.expandedKeys.length === 0 || data === 'expanded' ? this.allKeys : [0];
      this.expand(keys, {});
    },
    expand (expandedKeys, info) {
      this.expandedKeys = expandedKeys;
    },
    // 遍历树形数据，设置每一项的expanded属性，实现展开收起
    getAllKeys (data) {
      for (let i = 0; i < data.length; i++) {
        this.allKeys.push(data[i].deviceTypeId);
        if (data[i].children && data[i].children.length) {
          data[i].children.forEach(list => {
            this.getAllKeys([list]);
          });
        }
      }
    },
    // 删除设备类型
    delDevice (id) {
      this.$confirm({
        title: '提示',
        content: '确定删除此设备类型？',
        okText: '确定',
        cancelText: '取消',
        onOk: () => {
          this.isKeepKeys = true;
          return new Promise((resolve, reject) => {
            deleteErpDeviceType({
              deviceTypeId: id
            })
              .then((res) => {
                if (res.result_code == '1') {
                  this.$message.success('删除成功');
                  this.loadTree();
                  resolve();
                } else {
                  this.$message.error(res.result_msg);
                  resolve();
                }
              })
              .catch((err) => {
                reject(err);
              });
          });
        },
        onCancel () {}
      });
    },
    // type 新增：1  编辑：2   查看：4
    rowCilck (type, id) {
      let data = {
        id: id,
        isTips: true
      };
      this.$refs.deviceTypeForm.init(type, data, '/device/type/form');
    },
    // 查询
    async queryClick () {
      this.tabLoading = true;
      let params = Object.assign({}, this.deviceModelParams);
      params.size = this.size;
      params.curPage = this.page;
      params.makerModelIds = params.makerModelIds.join();
      params.makerIds = params.makerIds.join();
      getDeviceModelPage(params)
        .then((res) => {
          if (res.result_code == '1' && res.result_data.total > 0) {
            this.tabData = res.result_data.rows;
            this.total = res.result_data.total;
          } else {
            this.tabData = [];
            this.total = 0;
          }
          this.tabLoading = false;
        })
        .catch(() => {
          this.total = 0;
          this.tabData = [];
          this.tabLoading = false;
        });
    },
    // 新增
    addClick () {
      this.resetForm(true);
      this.addOrEdit = 'add';
      this.initParentDeviceTypeInfoList('add');
      this.detailTitle = '新增设备类型';
      this.detailOpen = true;
      this.getProjectList([]);
    },
    // 保存
    submitForm () {
      this.saveLoad = true;
      this.isKeepKeys = true;
      this.$refs['deviceForm'].validate((valid) => {
        if (valid) {
          this.save();
        } else {
          this.saveLoad = false;
          return false;
        }
      });
    },
    // 保存数据
    save () {
      this.deviceForm.technicalParameters.forEach(item => {
        item.itemData = item.setItemDataId;
      });
      let portName = this.addOrEdit === 'add' ? insertErpDeviceType : updateErpDeviceType;
      // 新增
      this.deviceForm.pid = this.pid[this.pid.length - 1];
      let params = Object.assign({}, this.deviceForm);
      params.deviceStatus = params.deviceStatus ? '1' : '0';
      portName(params)
        .then((res) => {
          this.saveLoad = false;
          if (res.result_code == '1') {
            this.resetForm(false);
            this.loadTree();
            this.$message.success('操作成功');
          } else {
            this.$message.warning(res.result_msg);
          }
        }).catch(() => {
          this.saveLoad = false;
        });
    },
    // 是否隐藏删除按钮
    showDel (row) {
      return !row.childErpDeviceTypeInfo || row.childErpDeviceTypeInfo.length == 0;
    },
    // 获取父类
    initParentDeviceTypeInfoList (type) {
      getComDeviceTypeTree({ deviceStatus: '1' }).then((res) => {
        if (res.result_code == '1' && res.result_data.length > 0) {
          // 添加全部
          let data = [
            {
              name: '全部',
              isAll: true,
              children: disposeTree.formatDeviceTreeData(res.result_data),
              deviceTypeId: 0
            }
          ];
          this.deviceParent = data;
          this.pid = [0];
          if (type === 'edit') {
            let data = this.deviceForm.ancestors.split(',').map(item => Number(item));
            this.pid = this.pid.concat(data);
            this.pid.pop();
          }
        }
      });
    },
    // 根据节点找到父节点一直到顶级节点
    familyTree (array, id) {
      let temp = [];
      const forFn = function (arr, id) {
        for (let i = 0; i < arr.length; i++) {
          let item = arr[i];
          if (item.deviceId == id) {
            temp.unshift(Number(item.parentId));
            forFn(array, item.parentId);
            break;
          } else {
            if (item.childErpDeviceTypeInfo) {
              forFn(item.childErpDeviceTypeInfo, id);
            }
          }
        }
      };
      forFn(array, id);
      temp.push(Number(id));
      let type = temp.filter(item => item != '0');
      return type;
    },
    // 关闭弹窗
    cancel () {
      this.resetForm(true);
    },
    // 取消
    resetForm (flag) {
      if (this.$refs['deviceForm']) {
        this.$refs['deviceForm'].resetFields();
      }
      this.addOrEdit = '';
      this.deviceForm = {
        deviceTypeId: '',
        name: '',
        pid: [0],
        deviceStatus: true,
        technicalParameters: []
      };
      this.saveLoad = false;
      this.detailOpen = false;
      this.selectPorjIds = [];
      this.setItemDataIds = [];
      this.projectList = [];
      this.setItemDataName = '';
      this.pid = '';
      if (!flag) {
        this.queryClick();
      }
    },
    // 删除
    deleteClick (id) {
      const self = this;
      self.$confirm({
        title: '提示',
        content: '确定要删除吗?',
        okText: '确定',
        cancelText: '取消',
        onOk () {
          let ids = [];
          self.checkData.forEach((column, index) => {
            ids.push(column.id);
          });
          removeDeviceModel({
            ids
          }).then((res) => {
            if (res.result_code == '1') {
              self.$message.success('操作成功');
              self.pageChange(1);
            } else {
              self.$message.warning(res.result_msg);
            }
          });
        }
      });
    },
    loadTree () {
      this.loading = true;
      this.allKeys = [];
      this.treeData = [];
      getComDeviceTypeTree({ keyword: this.keyword }).then((res) => {
        if (res && res.result_data && res.result_data.length) {
          // 添加全部
          let data = [
            {
              name: '全部',
              isAll: true,
              children: disposeTree.formatDeviceTreeData(res.result_data),
              deviceTypeId: 0
            }
          ];
          this.treeData = data;
        }
        if (this.isFirst) {
          this.expandedKeys = [0];
          this.isFirst = false;
        }
        this.getAllKeys(this.treeData);
        this.expandedKeys = this.isKeepKeys ? this.expandedKeys : [0];
        this.isKeepKeys = false;
        // 搜索 展开全部
        if (this.keyword) {
          this.expandedTree('expanded');
        }
        this.loading = false;
      }).catch(() => {
        this.treeData = [];
        this.getAllKeys(this.treeData);
        this.loading = false;
      });
    },
    // 展开所有
    showAllClick (value) {
      this.showAll = !value;
      if (value) {
        this.$refs.multipleTable.setAllTreeExpand(true);
      } else {
        this.$refs.multipleTable.clearTreeExpand();
      }
    },
    onSearch (value) {
      // let that = this;
      // if (value) {
      //   that.departTree = [];
      //   searchByKeyword({ keyWord: value }).then((res) => {
      //     that.departTree = res.result;
      //   });
      // } else {
      //   that.loadTree();
      // }
    },
    // 分页事件
    pageChange (e) {
      this.page = e;
      this.queryClick();
    },
    // 分页事件
    sizeChange (p, e) {
      this.page = p;
      this.size = e;
      this.queryClick();
    },
    // 获取固有的设备
    getProjectList (arr) {
      let dataList = [];
      this.projectList = [];
      this.filterProject = [];
      dataList = this.dictMap.device_type_set_item;
      dataList.forEach((item) => {
        let data = {};
        data.setItemDataId = item['dataValue'];
        data.setItemDataName = item['dataLable'];
        data.isRequired = '0';
        data.id = '';
        data.dataSort = item.dataSort;
        if (arr.indexOf(item['dataValue']) === -1) {
          this.projectList.push(data);
        } else {
          let number = arr.indexOf(item['dataValue']);
          this.deviceForm.technicalParameters[number].dataSort = data.dataSort;
        }
        this.filterProject.push(data);
      });
      this.projectList = this.sortProj(this.projectList);
      this.filterProject = this.sortProj(this.filterProject);
    },
    // 根据项目项目和是否必填字段返回列表数据
    filterTable () {
      let filterProject = Array.isArray(this.filterProject)
        ? this.filterProject
        : JSON.parse(this.filterProject);
      let projIds = [];
      this.deviceForm.technicalParameters.map(item => {
        projIds.push(item.setItemDataId);
      });
      this.projectList = filterProject.filter((item) => {
        return item.setItemDataName.indexOf(this.setItemDataName) > -1 && projIds.indexOf(item.setItemDataId) == -1;
      });
    },
    // 添加固有项目到已有项目
    add (value) {
      this.projectList = this.projectList.filter((item) => {
        if (this.setItemDataIds.indexOf(item.setItemDataId) > -1) {
          this.deviceForm.technicalParameters.push(item);
        }
        return this.setItemDataIds.indexOf(item.setItemDataId) === -1;
      });
      this.setItemDataIds = [];
      this.deviceForm.technicalParameters = this.sortProj(this.deviceForm.technicalParameters);
    },
    // 移除已选项目
    remove () {
      this.deviceForm.technicalParameters = this.deviceForm.technicalParameters.filter(
        (item) => {
          if (this.selectPorjIds.indexOf(item.setItemDataId) > -1) {
            this.projectList.push(item);
          }
          return this.selectPorjIds.indexOf(item.setItemDataId) === -1;
        }
      );
      this.selectPorjIds = [];
      this.projectList = this.sortProj(this.projectList);
    },
    /**
     *  排序项目名称
     *  params {* Array } arr
     * */
    sortProj (arr) {
      arr.sort((a, b) => {
        // return a.dataSort - b.dataSort; // 根据dataSort字段升序
        return a.setItemDataName.localeCompare(b.setItemDataName);
      });
      return arr;
    },
    /**
     *  改变已选项目的状态
     * params {*Array} 选中固有项目的id
     * params {*Boolean} checkBox的选中状态 true 表示选中，false取消选中
     * */
    changeSelectProj (val) {
      this.selectPorjIds.push(val);
      this.remove();
    },
    // 清空技术参数
    empty () {
      this.selectPorjIds = this.deviceForm.technicalParameters.map(item => item.setItemDataId);
      this.remove();
    }
  }
};
</script>

<style lang="less" scoped>
.device-content {
  display: flex;
  background-color: rgba(0,0,0,0);
  border-top: 0 !important;
}
.device-table {
  flex: 1;
  min-width: 800px;
  margin-left: 15px;
}
.device-info {
  display: flex;
  width: 350px;
  height: auto;
  .expanded-text {
    cursor: pointer;
  }
  .device-box {
    flex: 1;
    background-color: var(--zw-card-bg-color--default);
    border-radius: 5px;
    .device-title {
      display: flex;
      justify-content: space-between;
      padding: 15px 20px;
    }
  }
}
.erp-group-layout{
    margin-top: 10px;
    border: 1px solid var(--zw-border-color--default);
    height: 40vh;
    overflow: auto;
    padding: 10px;
    :deep(.ant-checkbox-wrapper) {
      width: 100%;
      margin-left: 8px;
    }
    .layout-search {
      display: flex;
      align-items: center;
      input {
        flex: 1;
      }
    }
    .layout-selected {
      display: flex;
      justify-content: space-between;
      margin-bottom: 20px;
      .layout-empty {
        color: var(--zw-conduct-color--normal);
        margin-right: 10px;
        cursor: pointer;
      }
    }
    .layout-content {
      display: flex;
      margin-bottom: 10px;
      align-items: center;
      &:hover {
        background-color: #f1f1f1;
        .close-right:hover {
          color: var(--zw-primary-color--default) !important;
        }
      }
      .close-right {
        width: 50px;
      }
      .data-name {
        flex: 1;
      }
    }

  }

  .middle{
    margin: auto;
  }
  .bottom-item{
    width: 100%;
    text-align: center;
    margin: 15px;
  }
  .tree-info {
    .search-info {
      margin: 10px 20px 16px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      .search-input {
        display: flex;
        flex: 1;
        border-radius: 50px;
      }
      .add-tree {
        width: 24px;
        text-align: right;
        color: var(--zw-primary-color--default);
        font-size: 16px;
        cursor: pointer;
      }
    }
    .device-operation {
      display: flex;
      justify-content: space-between;
      .name-info {
        display: flex;
        align-items: center;
      }
      .operation-name {
        margin-left:5px;
        width: 150px;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
      }
      .operation-btns {
        margin-right: 15px;
        & > i:nth-child(2) {
          display: inline-block;
          margin-left: 10px;
        }
      }
    }
  }
  .check-right{
    text-align: right;
  }

  .solar-eye-main-content {
    .operation {
      display: flex;
      align-items: center;
    }
    .forbidden-content {
      display: flex;
      align-items: center;
      flex: 1;
      .content-name {
        font-size: 16px;
        font-weight: bold;
        margin-right: 10px;
      }
      .forbidden-btn {
        background: rgba(197, 197, 197, 0.2);
        padding: 5px 10px;
      }
    }
    .operation-btn {
      width: 300px;
      margin-bottom: 0;
    }
  }
  .no-data {
    display: flex;
    width: 100%;
    height: 100px;
    align-items: center;
    justify-content: center;
    &.add-no-data {
      height: 50px;
    }
  }
  .check-box {
    margin-top: 8px;
    &:hover {
      background-color: var(--zw-card-light-bg-color--default);
    }
  }
  .operation-icon {
    font-size: 16px;
    margin-right: 15px !important;
  }
  .erp-group-layout {
    :deep(.ant-checkbox-inner) {
      display: none !important;
    }
  }
  :deep(.vxe-table--body .col--last) {
    display: flex !important;
    justify-content: flex-start !important;
    align-items: center !important;
    .vxe-cell {
      width: auto !important;
    }
  }
</style>
