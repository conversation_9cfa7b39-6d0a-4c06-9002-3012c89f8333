<!-- 查看流程图  -->
<template>
  <a-drawer title="流程图" width="100%" :visible="visible" @close="close" :get-container="getContainer" :wrap-style="{ position: 'absolute' }" v-bind:class="{'no-parentId': noContainer}">
    <a-spin :spinning="loading">
        <a-row :gutter="24" class="flow-detail">
        <template v-if="processInstanceId">
            <a-col :sm="24" :md="6" style="height: 65vh;overflow: auto; padding:24px">
            <a-timeline style="margin: auto;">
                <a-timeline-item v-for="(activity, index) in activities" :key="index" :color="activity.color">
                <p>{{activity.content}}</p>
                <pre style="white-space: pre-wrap;">{{activity.timestamp || activity.message}}</pre>
                </a-timeline-item>
            </a-timeline>
            </a-col>
            <a-col :sm="24" :md="18" class="solar-eye-flow-bg">
            <img :src="workFlowImag64" style="" />
            </a-col>
        </template>
        <!-- 未开启流程的情况 -->
        <template v-else>
            <a-col :span="24" class="solar-eye-flow-bg">
            <img :src="workFlowImag64" />
            </a-col>
        </template>
        </a-row>
    </a-spin>
  </a-drawer>
</template>
<script>
import { auditRecord } from '@/api/common_gy/plan.js';
import { showInitialProcessDiagram, listAuditInfo, showProcessDiagram } from '@/api/isolarErp/com/flowchart';
export default {
  name: 'FlowChartDrawer',
  props: {
    // 流程相关id
    processInstanceId: {
      type: String,
      required: ''
    },
    // 当前流程人员
    flowUser: {
      type: String,
      required: ''
    },
    // 流程来源区分-ERP、TWO
    type: {
      type: [String, Number],
      default: '1'
    },
    // 流程定义主键名称 字典flowchart_type 的label值
    processDefinitionKey: {
      type: String,
      required: false
    },
    /* 抽屉挂在的DOM节点ID，必要参数 */
    parentId: {
      type: String,
      required: true,
      default: ''
    },
    // 是否查看工单延期 终止流程审批记录
    isWorkOrder: {
      type: Boolean,
      default: false
    },
    // 是否查看任务 终止流程审批记录
    isTask: {
      type: Boolean,
      default: false
    }
  },
  watch: {
    propsUpdate: {
      immediate: true,
      handler (val, old) {
        if (JSON.stringify(val) != JSON.stringify(old)) {
          this.timeOut();
        }
      },
      deep: true
    }
  },
  computed: {
    propsUpdate () {
      const { processInstanceId, flowUser, type, processDefinitionKey, isWorkOrder } = this;
      return { processInstanceId, flowUser, type, processDefinitionKey, isWorkOrder };
    }
  },
  data () {
    return {
      loading: false,
      // 流程信息
      activities: [],
      // 流程图
      workFlowImag64: '',
      visible: false,
      noContainer: false
    };
  },
  methods: {
    openView () {
      let el = document.getElementById(this.parentId);
      let mains = document.querySelectorAll('.main');
      this.noContainer = !el && !mains.length;
      this.visible = true;
    },
    close () {
      this.visible = false;
    },
    getContainer () {
      let mains = document.querySelectorAll('.main');
      return document.getElementById(this.parentId) || (mains.length ? mains[mains.length - 1] : document.querySelector('.main-parent'));
    },
    /*
        定时器
      */
    timeOut () {
      let _this = this;
      if (window.flowTime) {
        clearTimeout(window.flowTime);
        window.flowTime = null;
      }
      window.flowTime = setTimeout(() => {
        _this.showFlowChart();
      }, 1000);
    },
    // 获取流程图
    showFlowChart () {
      let self = this;
      if (!self.processInstanceId && self.processDefinitionKey) {
        self.loading = true;
        let map = {
          processDefinitionKey: self.processDefinitionKey
        };
        showInitialProcessDiagram(map).then(res => {
          self.workFlowImag64 = res.result_data;
          self.loading = false;
        }).catch(() => {
          self.loading = false;
        });
      } else if (self.processInstanceId) {
        self.loading = true;
        let requestMap = {
          processInstanceId: self.processInstanceId,
          pProcessInstanceId: self.processInstanceId,
          flowUser: self.flowUser,
          isWorkOrder: self.isWorkOrder,
          isTask: self.isTask
        };
        const promise = new Promise((resolve, reject) => {
          showProcessDiagram(requestMap).then(res => {
            resolve(res.result_data);
          }).catch(err => {
            self.loading = false;
            reject(err);
          });
        });
        promise.then(function (value) {
          self.workFlowImag64 = value;
          if (self.type == '1') {
            listAuditInfo(requestMap).then(res => {
              res.result_data.forEach(item => {
                if (item.color) {
                  if (item.color == 'limegreen') {
                    item.color = 'green';
                  }
                } else {
                  item.color = 'blue';
                }
              });
              self.activities = res.result_data;
              self.loading = false;
            });
          } else {
            auditRecord(requestMap).then((response) => {
              response.result.forEach(item => {
                if (!item.color) {
                  item.color = 'blue';
                }
              });
              self.activities = response.result;
              self.loading = false;
            });
          }
        }, function () {
          self.loading = false;
        });
      }
    }
  },
  beforeDestroy () {
    if (window.flowTime) {
      clearTimeout(window.flowTime);
      window.flowTime = null;
    }
  }
};
</script>

<style lang="less" scoped>
  .no-parentId{
    position: fixed !important;
    :deep(.ant-drawer-content-wrapper) {
      width: 80% !important;
      height: 90%;
      margin: 3% 10%;
    }
  }
  .solar-eye-flow-bg{
    display: flex !important;
  }
  :deep(.ant-timeline) {
    padding-top: 5px;
  }
  .flow-detail  {
      margin: 0 !important;
  }
</style>
