<!-- 任务大厅 -->
<template>
  <div class="taskHall" id="taskHall">
    <ChartCard :itemList="itemList" @queryDataSource="chartCardChange"></ChartCard>
    <div class="solar-eye-gap"></div>
    <div class="solar-eye-search-model">
      <div class="solar-eye-search-content">
        <experiment-search ref="search" type="taskHall" @queryPageData="init" @reset="init" :dict="dictMap" />
      </div>
      <div class="split-line"></div>
    </div>
    <a-col class="solar-eye-main-content">
      <!-- 操作按钮 -->
      <div class="operation" style="height: 32px">
        <div class="operation-btn">
          <throttle-button label="新增" @click="add" perms="preTestTaskHall:add" />
          <throttle-button
            label="导出"
            :loading="exportLoading"
            :disabled="total < 1"
            class="solar-eye-btn-primary-cancel"
            @click="exportData"
            perms="preTestTaskHall:export"
          />
        </div>
      </div>
      <a-spin :spinning="loading">
        <!--列表-->
        <vxe-table
          :data="dataSource"
          :height="tableHeight - 150"
          ref="preTestRef"
          class="my-table"
          resizable
          show-overflow
          highlight-hover-row
          :seq-config="{ startIndex: (pageData.curPage - 1) * pageData.size }"
        >
          <vxe-table-column type="seq" :width="80" align="center" title="序号"></vxe-table-column>
          <vxe-table-column
            v-for="item in columnList"
            :key="item.name"
            show-overflow="title"
            :formatter="tabFormatter"
            :min-width="item.width || 150"
            :field="item.name"
            :title="item.comment"
          >
            <template v-slot:default="{ row }">
              <div v-if="item.name === 'flowStsName'" class="flex-start">
                <div :class="`table-statusCol color-table-${getColor(row.flowStsName)}`"></div>
                <div>{{ row.flowStsName }}</div>
              </div>
              <span v-else>{{ getLabel(row[item.name], null) }}</span>
            </template>
          </vxe-table-column>
          <vxe-table-column
            title="操作"
            fixed="right"
            width="160"
            :resizable="false"
            class-name="fixed-right-column-160"
          >
            <template v-slot="{ row }">
              <!--              <a-button type='link' size='small' class='operation-btn-hover'>详情</a-button>-->
              <throttle-button title="详情" icon="file-text" @click="handEvent('3', row)" class="operation-btn-hover" />
              <throttle-button
                v-if="stopShow(row)"
                title="终止"
                icon="stop"
                @click="handEvent('4', row)"
                class="operation-btn-hover"
              />
              <throttle-button
                v-if="downShow(row)"
                title="下载报告"
                :loading='reportLoading'
                icon="download"
                @click="handEvent('5', row)"
                class="operation-btn-hover"
              />
            </template>
          </vxe-table-column>
        </vxe-table>
        <!--分页组件-->
        <page-pagination
          :pageSize="pageData.size"
          :current="pageData.curPage"
          :total="total"
          @size-change="sizeChange"
        />
      </a-spin>
    </a-col>
    <add-modal ref="addModal" :dict="dictMap" @queryPageData="init" />
    <stop-modal ref="stopModal" @queryPageData="init" />
    <drawer-view ref="taskHallForm" @cancel="queryPageData" parentId="taskHall" />
  </div>
</template>

<script>
import ChartCard from './modules/ChartCard.vue';
import { headerList } from './js/HeaderList.js';
import { taskHall } from './js/Column.js';
import ExperimentSearch from './modules/Search.vue';
import tableHeight from '@/mixins/tableHeightAndSearchModel';
import initDict from '@/mixins/initDict';
import { getTaskHallPage, getTaskHallCount, checkReportApi } from '@api/isolarErp/taskHall.js';
import AddModal from '@views/experiment/modules/AddModal.vue';
import StopModal from '@views/experiment/modules/StopModal.vue';
import downBigExcel from '@/mixins/downBigExcel';
export default {
  mixins: [initDict, tableHeight, downBigExcel],
  components: { ChartCard, ExperimentSearch, AddModal, StopModal },
  provide () {
    return {
      downloadFileFn: this.downloadFile
    };
  },
  data () {
    return {
      total: 0,
      pageData: {
        curPage: 1, // 当前页
        size: 10, // 每页显示条数
        sortField: '',
        sortKind: ''
      },
      loading: false,
      dataSource: [],
      columnList: taskHall,
      itemList: headerList,
      flowSts: null,
      exportLoading: false
    };
  },
  created () {
    this.getDictMap('ops_task_type,ops_task_sub_type,pre_test_task_source');
  },
  mounted () {
    // this.init();
  },
  methods: {
    async init () {
      this.queryPageData();
      this.getCount();
    },
    // 分页事件
    sizeChange (p, e) {
      Object.assign(this.pageData, { curPage: p, size: e });
      this.queryPageData();
    },
    // 获取不同状态颜色
    getColor (taskStatusLabel) {
      switch (taskStatusLabel) {
        case '待领取':
          return 'waitBegin';
        case '执行中':
          return 'processing';
        case '验收中':
          return 'check';
        case '已完成':
          return 'finish';
        case '已终止':
          return 'stop';
      }
    },
    // 获取chartcard数值
    async getCount () {
      const queryParams = this.$refs.search.queryParams;
      const map = Object.assign({}, queryParams, this.pageData, { flowSts: this.flowSts });
      let res;
      try {
        res = await getTaskHallCount(map);
        res.result_data.flowStsVoList.forEach((item, index) => {
          if (this.itemList[index + 1]) this.itemList[index + 1]['num'] = item.count;
        });
        this.itemList[0]['num'] = res.result_data.taskCount;
      } catch (e) {
        throw new Error('Failed');
      }
    },
    // 分页table数据
    async queryPageData () {
      this.loading = true;
      const queryParams = this.$refs.search.queryParams;
      const map = Object.assign({}, queryParams, this.pageData, { flowSts: this.flowSts });
      let res;
      try {
        res = await getTaskHallPage(map);
        this.loading = false;
        this.dataSource = res.result_data.rows;
        this.total = res.result_data.total;
      } catch (e) {
        this.loading = false;
        this.dataSource = [];
        this.total = 0;
        throw new Error('Failed');
      }
    },
    async chartCardChange (value) {
      this.flowSts = value;
      this.loading = true;
      try {
        await this.queryPageData();
      } catch (e) {
        this.dataSource = [];
        this.total = 0;
      } finally {
        this.loading = false;
      }
    },
    // 新增入口
    add () {
      this.$refs.addModal.init(true);
    },
    handEvent (type, row) {
      switch (type) {
        case '3':
          this.$refs.taskHallForm.init('3', row, '/experiment/modules/detail/TaskDetail');
          break;
        case '4':
          this.stopTask(row);
          break;
        case '5':
          this.downloadReport(row);
          break;
        default:
          break;
      }
    },
    stopTask (row) {
      this.$refs.stopModal.init(row);
    },
    downloadReport (row) {
      if (['3', '4'].includes(row.flowSts)) {
        checkReportApi({ businessesId: row.id }).then((response) => {
          const { path, fileName } = response.result_data;
          this.downloadFile(path, fileName);
        });
      } else {
        this.exportExcelEvent('loading', 'poi-tl-100001', { testId: row.id }, 'docx');
      }
    },
    downloadFile (path, fileName) {
      const ext = this.getFileExtension(fileName);
      // let a = document.createElement('a');
      // a.setAttribute('href', path);
      // a.setAttribute('download', fileName);
      // a.setAttribute('target', ext === 'pdf' ? '_blank' : '_self');
      // let clickEvent = document.createEvent('MouseEvents');
      // clickEvent.initEvent('click', true, true);
      // a.dispatchEvent(clickEvent);
      if (ext === 'pdf') {
        window.open(path, '_blank');
      } else {
        this.$message.success('下载成功');
        window.location.href = path;
      }
    },
    exportData () {
      const queryParams = this.$refs.search.queryParams;
      this.exportExcelEvent('exportLoading', '300004', queryParams);
    },
    downShow (row) {
      return ['2', '3', '4'].includes(row.flowSts) && this.showHandle('preTestTaskHall:download');
    },
    stopShow (row) {
      return row.flowSts === '1' && this.showHandle('preTestTaskHall:stop');
    },
    getFileExtension (fileName) {
      let extension = fileName.substring(fileName.lastIndexOf('.') + 1);
      return extension.toLocaleLowerCase();
    }
  }
};
</script>
<style lang="less" scoped>
:deep(.vxe-table.size--small .vxe-header--column:not(.col--ellipsis)) {
  padding: 0;
}

:deep(.vxe-table .vxe-header--row .vxe-cell) {
  padding: 0;
}

:deep(.vxe-table .vxe-header--column) {
  line-height: 20px;
}

.sort-head {
  cursor: pointer;
  padding: 2px 0 10px;
}

:deep(.sort-head span) {
  display: inline-block;
  margin-top: 10px;
}

:deep(.up-icon) {
  top: 12px;
}

:deep(.down-icon) {
  top: 21px;
}

.sort-head:hover {
  background: #d8d8d8;
}

.taskHall {
  height: 100%;
  .solar-eye-search-model {
    border-radius: 4px 4px 0 0;
    .solar-eye-search-content {
      margin: 0 !important;
      padding: 0 12px 10px 12px;
    }
    .split-line {
      height: 1px;
      margin: 0 24px;
      background: var(--zw-divider-color--default);
    }
  }

  .solar-eye-main-content {
    &.no-radius {
      border-top-left-radius: 0;
      border-top-right-radius: 0;
      padding-top: 0;
      .operation {
        margin: 0 !important;
        padding-top: 16px;
        border-top: 1px solid var(--zw-divider-color--default);
      }
    }
    padding: 20px 24px 16px;
    border-radius: 0 0 4px 4px;
    .operation {
      margin-bottom: 12px;
      .operation-btn {
        height: 32px;
      }
    }
    .vxe-cell .operation-btn-hover.ant-btn:hover {
      color: var(--zw-primary-color--default) !important;
    }
  }
}
</style>
