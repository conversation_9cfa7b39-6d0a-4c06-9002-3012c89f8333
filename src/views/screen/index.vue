<template>
  <div class="screen-page">
   <component :is="compName"></component>
  </div>
</template>

<script>
import Common from './common';
import Custom from './custom';
import { USER_INFO } from '@/store/mutation-types';
export default {
  components: {
    Common,
    Custom
  },
  data () {
    return {

    };
  },
  computed: {
    compName () {
      let userInfo = Vue.ls.get(USER_INFO);
      let arr = userInfo.roles.filter(item => {
        return item.roleCode == 'r10055';
      });
      return arr.length > 0 ? Custom : Common; // 联调时放开
    }
  },
  mounted () {
    document.title = '大屏' + ' · SolarEye';
  },
  methods: {
  }
};
</script>
