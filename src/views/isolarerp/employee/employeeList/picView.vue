<template>
  <!-- 图片预览 -->
  <a-modal title="预览" v-model="open" footer :maskClosable="false" centered @cancel="cancel">
    <template v-if="path">
      <template v-if="isImg">
        <img alt="example" :src="path" />
      </template>
      <template v-else>
        <iframe :src="path" style="width: 80vw; height: 80vh;" />
      </template>
    </template>
    <template v-else>
      <img alt="example" :src="errorImg" />
    </template>
  </a-modal>
</template>

<script>
export default {
  props: {
    value: {
      type: Boolean,
      default: false
    },
    path: {
      type: String,
      default: ''
    },
    isImg: {
      type: Boolean,
      default: false
    },
    open: {
      type: Boolean,
      default: false
    }
  },
  data () {
    return {
      loading: false,
      errorImg: 'data:image/jpg;base64,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'
    };
  },
  methods: {
    cancel () {
      this.$emit('change', false);
      this.loading = false;
    }
  }
};
</script>

<style lang="less" scoped>
  :deep(.ant-modal) {
    width: unset !important;
    max-width: 80vw;
  }
  :deep(.ant-spin-container) {
    display: flex;
    img{
      margin: auto;
    }
  }
</style>
