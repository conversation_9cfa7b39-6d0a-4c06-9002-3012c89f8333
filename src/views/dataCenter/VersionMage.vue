<!-- 版本管理 -->
<template>
  <div class="version-mage" style="position: 'relative'">
    <a-row :gutter="16">
      <a-col :span="4">
        <a-card :style="{ height: innerHeight + 'px' }">
          <div class="table-page-search-wrapper">
            <a-select @change="(value) => changeEvent(value, 0)" v-model="queryParams.deviceType"
              style="width: 100%; margin-bottom: 16px">
              <a-select-option v-for="item in deviceTypeList" :key="item.code" :value="item.code">
                {{ item.name }}
              </a-select-option>
            </a-select>
            <a-input-search style="width: 65%" v-model="searchValue" @search="onSearchEvent" class="margin-16-bottom"
              placeholder="请输入厂商或型号" allowClear @pressEnter="onSearchEvent" />
            <a-button class="margin-16-bottom" style="margin-left: 16px"
              @click="handleAction({}, { title: '新增', visible: true, type: 'add' })">
              新增</a-button>
            <z-tree :nodes="treeData" :setting="setting" :showSearch="false" @onCreated="handleCreated"
              @onClick="onClick" :height="innerHeight - 150 + 'px'"></z-tree>
          </div>
        </a-card>
      </a-col>
      <a-col :span="20">

        <div class="solar-eye-gap"></div>
        <a-col class="solar-eye-main-content">
          <vxe-table v-loading="pageloading" :data="dataSource" ref="multipleTable" class="my-table"
            @sort-change="handleTableSortChange" :sort-config="{ remote: true }" resizable show-overflow
            highlight-hover-row size="small" @checkbox-all="onSelectChange" @checkbox-change="onSelectChange"
            :height="tableHeight - 24">
            <vxe-table-column type="checkbox" :width="80" align="center"></vxe-table-column>
            <vxe-table-column type="seq" :width="80" title="序号" align="center"></vxe-table-column>
            <vxe-table-column show-overflow="title" title="设备类型" :min-width="160" field="deviceTypeName">
            </vxe-table-column>
            <vxe-table-column show-overflow="title" title="设备厂商" field="factory" :min-width="160">
            </vxe-table-column>
            <vxe-table-column show-overflow="title" title="设备型号" sortable :min-width="160" field="model">
            </vxe-table-column>
            <vxe-table-column show-overflow="title" title="更新时间" field="updateTime" sortable :min-width="160">
            </vxe-table-column>
            <vxe-table-column show-overflow="title" title="更新人" field="updateUser" :min-width="160">
            </vxe-table-column>
            <vxe-table-column title="操作" :width="160" fixed="right" :resizable="false" class-name="fixed-right-column-160">
              <template slot-scope="scope">
                <a-button title="编辑" v-has="'pointMage:edit'" icon="edit"
                  @click="handleAction(scope.row, { title: '编辑', visible: true, type: 'edit' })"></a-button>
                <a-popconfirm title="删除后，无法恢复，请确认？" ok-text="确认" cancel-text="取消" @confirm="handleDelete(scope.row)"
                >
                  <a-button title="删除" v-has="'pointMage:edit'"
                    :style="{ color: scope.row.status ? '#c2c2c2' : '' }" icon="delete"></a-button>
                </a-popconfirm>
                <a-button title="查看" v-has="'pointMage:view'" icon="eye"
                  @click="handleAction(scope.row, { title: '查看', visible: true, type: 'view' })"></a-button>
              </template>
            </vxe-table-column>

            <template v-slot:empty>
              <span>查询无数据</span>
            </template>
          </vxe-table>
          <page-pagination :pageSize="queryParams.pageSize" :current="queryParams.currentPage" :total="total"
            @size-change="sizeChange" />
        </a-col>
        <version-mage-drawer ref="drawerForm" :deviceTypeList="deviceTypeList" @click="submitEvent">
        </version-mage-drawer>
      </a-col>
    </a-row>
  </div>
</template>
<script>
import {
  postAction
} from '@/api/manage';
import {
  dataCenterListMixin
} from './mixins/list';
import VersionMageDrawer from './modules/VersionMageDrawer';
import ZTree from '@/components/ZTree';
import {
  getVersionTree
} from '@/api/dataCenter/index';
import tableHeight from '@/mixins/tableHeightAndSearchModel';
const baseUrl = process.env.VUE_APP_Health_BASE_URL;
// const baseUrl = 'http://**********:8088'
export default {
  name: 'VersionMage',
  mixins: [dataCenterListMixin, tableHeight],
  components: {
    VersionMageDrawer,
    ZTree
  },
  data () {
    return {
      queryParams: {
        deviceType: '',
        factory: '',
        model: '',
        version: '',
        binding: '',
        status: '',
        pageSize: '10',
        currentPage: '1',
        order: ''
      },
      hiddenNodes: [],
      treeData: [],
      deviceTypeList: [],
      pointTypeList: [],
      ztreeObj: undefined,
      searchValue: '',
      url: {
        list: '/versionManagement/list',
        export: '/versionManagement/export',
        detail: '/versionManagement/detail',
        delete: '/versionManagement/delete'
      },
      setting: {
        check: {
          enable: false
        },
        data: {
          simpleData: {
            enable: true,
            pIdKey: 'pid'
          },
          key: {
            title: 'data',
            name: 'data'
          }
        },
        view: {
          showIcon: false,
          showLine: false
        }
      }
    };
  },
  created () {
    this.getDeviceTypeList(true)
      .then((res) => {
        if (res && res.length > 0) {
          this.queryParams.deviceType = res[0].code;
          this.getTreeList();
          this.loadData(1);
        }
      })
      .catch(() => {});
  },
  computed: {},
  methods: {
    handleCreated: function (ztreeObj) {
      // 初始化获取ztree 对象
      this.ztreeObj = ztreeObj;
      if (this.searchValue) {
        this.hiddenNodes = [];
        this.onSearchEvent();
        // this.ztreeObj.expandAll(true)
      } else {
        this.ztreeObj.expandAll(false);
      }
    },
    /** * 点击左侧的版本树
       * params {Object}
       * treeId {String}
       * treeNode{string}
       */
    onClick (event, treeId, treeNode) {
      this.initQueryParam();
      let arr = treeNode.getPath();
      arr.forEach((item, index) => {
        if (item.treeNode == 0) {
          this.queryParams.factory = item.data;
        }
        if (item.treeNode === 1) {
          this.queryParams.model = item.data;
        }
        if (item.treeNode == 2) {
          this.queryParams.version = item.data;
        }
      });
      this.loadData(1);
    },
    initQueryParam () {
      this.queryParams.factory = '';
      this.queryParams.version = '';
      this.queryParams.model = '';
    },
    /***
       *  change 事件
       * params { String } value v-model 绑定的值
       * params { Number } type 0 是设备名称 1 协议版本 2，测点绑定、3. 状态
       *
       */
    changeEvent (value, type) {
      this.selectedRowKeys = [];
      if (type === 0) {
        this.initQueryParam();
        this.getTreeList(value);
      }
      this.loadData(1);
    },
    getTreeList () {
      getVersionTree({
        deviceType: this.queryParams.deviceType,
        factoryOrModel: '' // 改为前端搜索 无须此参数赋值
      })
        .then((res) => {
          res.result_data.map((item) => {
            item.name = item.data;
            item.pid = Number(item.pid);
          });
          this.treeData = res.result_data;
        })
        .catch((err) => {
          console.log(err);
        });
    },
    onSearchEvent () {
      let that = this;
      this.ztreeObj.showNodes(that.hiddenNodes);

      // if(this.searchValue) {
      function filterFunc (node) {
        // 如果当前结点，或者其父结点可以找到，或者当前结点的子结点可以找到，则该结点不隐藏
        if (that.searchParent(that.searchValue, node) || that.searchChildren(that.searchValue, node.children)) {
          return false;
        }
        return true;
      }

      // 获取不符合条件的叶子结点
      that.hiddenNodes = this.ztreeObj.getNodesByFilter(filterFunc);

      console.log(that.hiddenNodes);

      // 隐藏不符合条件的叶子结点
      this.ztreeObj.hideNodes(that.hiddenNodes);
      if (that.searchValue) {
        this.ztreeObj.expandAll(true);
        this.$emit('onCheck');
      } else {
        this.ztreeObj.expandAll(false);
      }
      // } else {
      //   this.ztreeObj.showNodes(this.treeData)
      // }
    },
    searchParent (searchValue, node) {
      if (node == null) {
        return false;
      }
      if (node.name.indexOf(searchValue) != -1) {
        return true;
      }
      // 递归查找父结点
      return this.searchParent(searchValue, node.getParentNode());
    },
    searchChildren (searchValue, children) {
      if (children == null || children.length == 0) {
        return false;
      }
      for (var i = 0; i < children.length; i++) {
        var node = children[i];
        if (node.name.indexOf(searchValue) != -1) {
          return true;
        }
        // 递归查找子结点
        var result = this.searchChildren(searchValue, node.children);
        if (result) {
          return true;
        }
      }
      return false;
    },
    submitEvent (obj) {
      if (obj.isNeedRefresh) {
        this.loadData(1);
        this.queryParams.deviceType = obj.deviceType;
        this.queryParams.factory = '';
        this.queryParams.model = '';
        this.queryParams.version = '';
        this.queryParams.status = '';
        this.queryParams.binding = '';
        this.getTreeList();
      }
    },
    handleDelete: function (row) {
      if (!this.url.delete) {
        this.$message.error('请设置url.delete属性!');
        return;
      }
      var that = this;
      postAction(baseUrl + that.url.delete, {
        id: row.id,
        source: row.source == '8' ? '8' : null
      }).then((res) => {
        if (res.result_code === '1') {
          that.$message.success('删除成功');
          that.loadData();
          this.getTreeList();
        } else {
          that.$message.warning(res.message);
        }
      });
    },
    filterOption (input, option) {
      return option.componentOptions.children[0].text.toLowerCase().indexOf(input.toLowerCase()) >= 0;
    }
  }
};
</script>
