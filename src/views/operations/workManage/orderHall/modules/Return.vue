<template>
  <a-modal ref="modalview" :title="title" v-model="visible" :maskClosable="false" centered @cancel="cancel" width="450px">
    <a-spin :spinning="loading">
      <a-form-model ref="return" :model="form" :rules="rules" :label-col="{span: 0}" :wrapper-col="{span: 24}">
        <a-form-model-item label="" prop="auditOpinion">
          <a-textarea v-model="form.auditOpinion" :max-length="255" :auto-size="{ minRows: 4, maxRows: 6}"
                      @blur="form.auditOpinion = $trim($event)" placeholder="请输入退回原因" show-word-limit></a-textarea>
        </a-form-model-item>
      </a-form-model>
    </a-spin>
    <template slot="footer">
      <a-button size="default" :disabled="loading" @click="cancel()">取消</a-button>
      <a-button size="default" class="solar-eye-btn-primary" :disabled='!form.auditOpinion' :loading="loading" @click="commit()">确定</a-button>
    </template >
  </a-modal>
</template>

<script>
import { orderProcess } from '@api/operations/orderHall';
export default {
  name: 'Return',
  model: {
    prop: 'visible',
    event: 'change'
  },
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    row: {
      type: Object,
      default: () => {
        return {};
      }
    }
  },
  watch: {
    visible (val, old) {
      Object.assign(this.$data, this.$options.data());
    }
  },
  data () {
    return {
      title: '退回',
      loading: false,
      form: {
        auditOpinion: ''
      },
      rules: {
        auditOpinion: [{ required: true, message: '请输入退回原因' }]
      }
    };
  },
  methods: {
    commit () {
      const self = this;
      self.loading = true;
      self.$refs['return'].validate((valid) => {
        if (valid) {
          let map = {
            'handleType': 3,
            'type': '1',
            'returnReason': this.form.auditOpinion,
            'auditStatusProcess': '0',
            'auditOpinionProcess': this.form.auditOpinion,
            'id': this.row.id,
            'taskId': this.row.taskId,
            'orderType': this.row.orderType,
            'orderStatus': this.row.orderStatus,
            'processInstanceId': this.row.processInstanceId,
            'taskDefKey': this.row.taskDefKey
          };
          orderProcess(map).then((res) => {
            self.$message.success('操作成功');
            self.reset();
            self.loading = false;
          }).catch(() => {
            self.loading = false;
          });
        } else {
          self.loading = false;
          return false;
        }
      });
    },
    reset () {
      this.$emit('cancel');
      this.cancel();
    },
    // 关闭回调方法
    cancel () {
      this.loading = false;
      Object.assign(this.$data, this.$options.data());
      this.$refs['return'].clearValidate();
      this.$emit('change', false);
      this.visible = false;
      // 确保关闭后可以再次打开
      this.$refs.modalview.$destroyAll();
    }
  }
};
</script>
