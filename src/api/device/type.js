import { getAction, postAction } from '@/api/manage';
const baseUrl = process.env.VUE_APP_API_BASE_URL;

// 新增设备类型
export const insertErpDeviceType = (params) => postAction(baseUrl + '/device-type/save', params);
// 修改设备类型
export const updateErpDeviceType = (params) => postAction(baseUrl + '/device-type/edit', params);
// 删除设备类型
export const deleteErpDeviceType = (params) => postAction(baseUrl + '/device-type/remove', params);
// 设备型号-新增
export const saveDeviceModel = (params) => postAction(baseUrl + '/device-model/save', params);
// 设备型号-编辑
export const editDeviceModel = (params) => postAction(baseUrl + '/device-model/edit', params);
// 设备型号-删除
export const removeDeviceModel = (params) => postAction(baseUrl + '/device-model/remove', params);
// 设备类型详情
export const detailErpDeviceType = (params) => getAction(baseUrl + '/device-type/detail', params);
// 设备型号列表 -分页
export const getDeviceModelPage = (params) => getAction(baseUrl + '/device-model/page', params);
// 设备型号列表 - 非分页(更多)
export const getDeviceModelList = (params) => getAction(baseUrl + '/device-model/list', params);
// 设备型号-详情
export const getDeviceModelDetail = (params) => getAction(baseUrl + '/device-model/detail', params);
// 设备类型-查询技术参数
export const listParam = (params) => getAction(baseUrl + '/device-type/listParam', params);
// 设备型号-获取设备分支
export const getDeviceBranch = (params) => getAction(baseUrl + '/device-model/getDeviceBranch', params);
