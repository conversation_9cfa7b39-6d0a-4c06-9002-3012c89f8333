<!-- 测点管理 -->
<template>
  <div id="pointMage" class="point-mage">
    <a-spin :spinning="loading" >
     <div class="solar-eye-search-model">
        <!-- 搜索区域 -->
          <a-tabs size="default" @change="tabHandleClick" v-model="queryParams.pointType">
            <a-tab-pane tab="遥测" key="2"></a-tab-pane>
            <a-tab-pane tab="遥信" key="1"></a-tab-pane>
          </a-tabs>
          <a-form labelAlign="left"  class="solar-eye-search-content">
            <a-row :gutter="24" align="middle">
              <a-col  :md="8" :sm="24" :xxl="5" :xl="8" :lg="8">
                <a-form-item label="测点名称" :colon="false">
                  <a-input-search v-model="queryParams.pointName" placeholder="请输入测点名称" @search="value=>changeEvent(value, 0)" @pressEnter="(value) => changeEvent(value, 0)"></a-input-search>
                </a-form-item>
              </a-col>
              <a-col  :md="8" :sm="24" :xxl="5" :xl="8" :lg="8">
                <a-form-item label="设备类型" :colon="false">
                  <a-select @change="(value) => changeEvent(value, 1)" v-model="queryParams.deviceType">
                    <a-select-option value="" :key="''">全部</a-select-option>
                    <a-select-option
                      v-for="item in deviceTypeList"
                      :key="item.code"
                      :value="item.code"
                    >
                    {{item.name}}
                    </a-select-option>
                  </a-select>
                </a-form-item>
              </a-col>
              <a-col v-show="queryParams.pointType == '1'"  :md="8" :sm="24" :xxl="4" :xl="8" :lg="8">
                <a-form-item label="告警等级" style="margin-left: 8px" :colon="false">
                  <a-col :span="24">
                    <a-select
                      v-model="queryParams.alarmGrade"
                      :options="alarmGradeOptions"
                       @change="(value) => changeEvent(value, 2)">
                    </a-select>
                  </a-col>
                </a-form-item>
              </a-col>
              <a-col v-show="queryParams.pointType == '1'"  :md="8" :sm="24" :xxl="4" :xl="8" :lg="8">
                <a-form-item label="是否屏蔽" style="margin-left: 8px" :colon="false">
                  <a-col :span="24">
                    <a-select
                      v-model="queryParams.shieldFlag"
                      placeholder="请选择"
                      @change="(value) => changeEvent(value, 2)"
                    >
                      <a-select-option :value="-1">全部</a-select-option>
                      <a-select-option :value="1">是</a-select-option>
                      <a-select-option :value="0">否</a-select-option>
                    </a-select>
                  </a-col>
                </a-form-item>
              </a-col>
              <a-col  :md="8" :sm="24" :xxl="6" :xl="8" :lg="8">
                <a-form-item label="更新时间范围" style="margin-left: 8px" :colon="false">
                  <a-range-picker
                    v-model="timeRange"
                    format="YYYY-MM-DD"
                    @change="(value, data) => changeEvent(value, 3)"
                    :disabled-date="(current) => current.isAfter(Date(), 'day')"
                    ><a-icon slot="suffixIcon" type="calendar"
                  /></a-range-picker>
                </a-form-item>
              </a-col>

              <div class="open-btn"></div>
            </a-row>
          </a-form>
     </div>
          <div class="solar-eye-gap"></div>
        <a-col class="solar-eye-main-content">
          <!-- 操作按钮 -->
          <div class="operation" >
            <div class="operation-btn">
            <a-button v-has="'pointMage:update'" v-show="queryParams.pointType == '1'" @click="showEidtView" class="solar-eye-btn-primary">修改</a-button>
            <a-button v-has="'pointMage:export'" @click="handleExportPoint" class="solar-eye-btn-primary" :loading="exportLoading">导出</a-button>
          </div>
          </div>
        <vxe-table
          :data="dataSource"
          ref="multipleTable"
          class="my-table"
          @sort-change="handleTableSortChange"
          :sort-config="{ remote: true }"
          resizable
          show-overflow
          highlight-hover-row
          size="small"
          @checkbox-all="onSelectChange"
          @checkbox-change="onSelectChange"
          :height="tableHeight - 24"
          :seq-config="{ startIndex: (queryParams.currentPage - 1) * queryParams.pageSize }"
        >
          <vxe-table-column type="seq" :width="80" title="序号" align="center"></vxe-table-column>
          <vxe-table-column show-overflow="title" :formatter="tabFormatter" title="设备类型" min-width="160" field="deviceType"> </vxe-table-column>
          <vxe-table-column show-overflow="title" :formatter="tabFormatter" title="测点名称" field="pointName" :min-width="160"> </vxe-table-column>
          <vxe-table-column show-overflow="title" :formatter="tabFormatter" title="测点编码" sortable :min-width="160" field="pointCode">
          </vxe-table-column>
          <vxe-table-column :visible="queryParams.pointType == '1'" show-overflow="title" :formatter="tabFormatter" title="告警等级" field="alarmGrade" :min-width="160"> </vxe-table-column>
          <vxe-table-column :visible="queryParams.pointType == '1'" show-overflow="title" :formatter="tabFormatter" title="是否屏蔽" field="shieldFlag" :min-width="160"> </vxe-table-column>
          <vxe-table-column :visible="queryParams.pointType == '2'" show-overflow="title" :formatter="tabFormatter" title="存储单位" field="unit" :min-width="160"> </vxe-table-column>
          <vxe-table-column :visible="queryParams.pointType == '2'" show-overflow="title" :formatter="tabFormatter" title="保留位数" field="reservedBit" :min-width="160">
          </vxe-table-column>
          <vxe-table-column :visible="queryParams.pointType == '2'" show-overflow="title" :formatter="tabFormatter" title="下限值" field="lowerLimitCnValue" :min-width="160">
          </vxe-table-column>
          <vxe-table-column :visible="queryParams.pointType == '2'" show-overflow="title" :formatter="tabFormatter" title="上限值" field="upperLimitCnValue" :min-width="160">
          </vxe-table-column>
          <vxe-table-column :visible="queryParams.pointType == '2'" show-overflow="title" :formatter="tabFormatter" title="是否必接" field="upperLimitCnValue" :min-width="160">
            <template slot-scope="scope">
              <span>{{ scope.row.accessFlag ? '是' : '否' }}</span>
            </template>
          </vxe-table-column>
          <vxe-table-column show-overflow="title" :formatter="tabFormatter" title="创建时间" field="createTime" sortable :min-width="160">
          </vxe-table-column>
          <vxe-table-column show-overflow="title" :formatter="tabFormatter" title="测点排序" field="dataSort" sortable :min-width="160">
          </vxe-table-column>
          <vxe-table-column show-overflow="title" :formatter="tabFormatter" title="更新时间" field="updateTime" sortable :min-width="160">
          </vxe-table-column>
          <vxe-table-column show-overflow="title" :formatter="tabFormatter" title="更新人" field="updateUser" :min-width="160"> </vxe-table-column>
          <vxe-table-column :visible="queryParams.pointType == '2'" title="操作" :width="120" fixed="right" :resizable="false" class-name="fixed-right-column-120">
            <template slot-scope="scope">
              <a-button title="编辑" v-has="'pointMage:edit'" v-if="scope.row.pointType=='遥测'" icon="edit"
                @click="handleAction(scope.row, {title:'编辑', visible: true, type: 'edit',pointType:queryParams.pointType})"></a-button>
              <a-button title="查看" v-has="'pointMage:view'" v-if="scope.row.pointType=='遥测'" icon="eye"
                @click="handleAction(scope.row,{title:'查看', visible: true, type: 'view',pointType:queryParams.pointType})"></a-button>
            </template>
          </vxe-table-column>
          <template v-slot:empty>
            <span>查询无数据</span>
          </template>
        </vxe-table>
        <page-pagination
          :pageSize="queryParams.pageSize"
          :current="queryParams.currentPage"
          :total="total"
          @size-change="sizeChange"
        />
        </a-col>
    </a-spin>
    <point-mage-drawer ref="drawerForm" :deviceTypeList='deviceTypeList' @click="getDrawerEvent"></point-mage-drawer>
    <remote-signaling-edit ref="RemoteSignalingEdit" @close="getDrawerEvent" />
  </div>
</template>
<script>
import { dataCenterListMixin } from './mixins/list';
import PointMageDrawer from './modules/PointMageDrawer';
import RemoteSignalingEdit from './modules/RemoteSignalingEdit';
import { getSystemCodeList } from '@/api/health/healthapi.js';
import tableHeight from '@/mixins/tableHeightAndSearchModel';
export default {
  name: 'PointMage',
  mixins: [dataCenterListMixin, tableHeight],
  components: { PointMageDrawer, RemoteSignalingEdit },
  data () {
    return {
      queryParams: {
        pointName: '',
        deviceType: '',
        pointType: '2',
        updateTimeBegin: '',
        updateTimeEnd: '',
        pageSize: '10',
        currentPage: '1',
        order: '',
        alarmGrade: '',
        shieldFlag: -1
      },
      timeRange: [],
      deviceTypeList: [],
      pointTypeList: [],
      url: {
        list: '/pointManagement/list',
        export: '/pointManagement/export',
        detail: '/pointManagement/detail'
      },
      alarmGradeOptions: []
    };
  },
  created () {
    // this.timeRange = [moment(new Date()), moment(new Date())]
    // this.queryParams.updateTimeBegin = this.timeRange[0].format('YYYY/MM/DD')
    // this.queryParams.updateTimeEnd = this.timeRange[1].format('YYYY/MM/DD')
    this.getAlarmGradeOptions();
    this.getDeviceTypeList(true);
    this.loadData(1);
  },
  computed: {},
  methods: {
    /***
     *  change 事件
     * params { String } value v-model 绑定的值
     * params { Number } type 0 是测点名称 1 是设备类型，2 测点类型 3.时间范围
     *
     */
    changeEvent (value, type) {
      if (type === 3) {
        if (this.timeRange.length > 0) {
          this.queryParams.updateTimeBegin = this.timeRange[0].format('YYYY/MM/DD');
          this.queryParams.updateTimeEnd = this.timeRange[1].format('YYYY/MM/DD');
        } else {
          this.queryParams.updateTimeBegin = '';
          this.queryParams.updateTimeEnd = '';
        }
      }
      this.loadData(1);
    },
    tabHandleClick () {
      this.$nextTick(() => {
        if (this.queryParams.pointType == '2') {
          this.queryParams.alarmGrade = '';
          this.queryParams.shieldFlag = -1;
        }
        this.$refs.multipleTable.refreshColumn();
      });
      this.loadData(1);
    },
    handleExportPoint () {
      let params = this.queryParams;
      this.handleExport({
        pointName: params.pointName,
        deviceType: params.deviceType,
        pointType: params.pointType,
        updateTimeBegin: params.updateTimeBegin,
        updateTimeEnd: params.updateTimeEnd,
        alarmGrade: params.alarmGrade,
        shieldFlag: params.shieldFlag
      });
    },
    getDrawerEvent (flag) {
      if (flag) {
        this.loadData(1);
      }
    },
    showEidtView () {
      this.$refs.RemoteSignalingEdit.init();
    },
    // 获取告警等级下拉选项
    async getAlarmGradeOptions () {
      let res = await getSystemCodeList({ firstTypeCode: '0070' });
      let arr = res.result_data['0070'].map(item => {
        return {
          value: item.secondTypeCode,
          label: item.secondName
        };
      });
      this.alarmGradeOptions = [{ label: '全部', value: '' }, ...arr];
    }
  }
};
</script>
