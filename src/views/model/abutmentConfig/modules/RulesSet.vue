<template>
  <div>
    <template v-if="data.length > 0">
      <div class="btn-group">
        <a-button v-if="type == '1'" class="solar-eye-btn-primary" :disabled="data.length > 7" @click="doAdd">新增</a-button>
      </div>
      <vxe-table
        v-if="data.length > 0"
        :data="data"
        ref="table"
        resizable
        show-overflow
        highlight-hover-row
        size="small"
        :height="tableHeight"
        :seq-config="{ startIndex: (searchData.currentPage - 1) * searchData.pageSize }"
        class="my-table"
      >
        <vxe-table-column show-overflow="title" :formatter="tabFormatter" title="协议任务类别标记" field="groupNum"/>
        <vxe-table-column show-overflow="title" :formatter="tabFormatter" title="电站列表/设备列表/其他" field="groupParam">
          <template v-slot="{ row }">
            <span v-if="type == '2' || !row.isEdit"> {{ row.groupParam }}</span>
            <a-input v-else v-model="row.oldGroupParam" placeholder="请输入内容" :maxLength="60"
            @blur="row.oldGroupParam = $trim($event)"></a-input>
          </template>
        </vxe-table-column>
        <vxe-table-column v-if="type == '1'" title="操作" :width="140" fixed="right" :resizable="false">
          <template v-slot="{ row }">
            <template v-if="row.isEdit">
              <span class="table-icon" @click="doSave(row)" title="保存">
                <svg-icon class="operation-icon" iconClass="check"/>
              </span>
              <span class="table-icon" @click="doCancel(row)" title="取消">
                <svg-icon class="operation-icon" iconClass="close"/>
              </span>
            </template>
            <template v-else>
              <span class="table-icon" @click="doEdit(row)" title="编辑">
                <svg-icon class="operation-icon" iconClass="edit"/>
              </span>
              <span class="table-icon" @click="doDelete(row)"  title="删除">
                <svg-icon class="operation-icon" iconClass="delete"/>
              </span>
            </template>
          </template>
        </vxe-table-column>
        <template v-slot:empty>
          <span>查询无数据</span>
        </template>
      </vxe-table>
    </template>
    <div class="flex-center" v-else :style="{ height : tableHeight - 140 + 'px'}">
      <div class="empty-div">
        <img src="../../../../assets/images/model/no-data.png" alt="暂无数据">
        <div>暂无数据</div>
        <a-button class="solar-eye-btn-primary" :disabled="data.length > 7" @click="doAdd">新增</a-button>
      </div>
    </div>
  </div>
</template>
<script>
import { getParamGroupList, addOrUpdateParamGroup, deleteParamGroup, exportParamList } from '@/api/model/abutmentConfig.js';
export default {
  props: {
    type: {
      type: String,
      default: '1'
    },
    sourceId: {
      type: String,
      default: ''
    },
    taskId: {
      type: String,
      default: ''
    },
    row: {
      type: Object,
      default: ()=> {
        return {};
      }
    }
  },
  data () {
    return {
      data: [],
      searchData: {
        currentPage: 1,
        pageSize: 10
      },
      tableHeight: 440,
      actIndex: -1
    }
  },
  mounted () {
    this.getTableHeight();
    window.onresize = () => {
      this.getTableHeight();
    };
  },
  methods: {
    // 设置表格高度
    getTableHeight () {
      this.$nextTick(() => {
        this.tableHeight = document.body.offsetHeight - 310;
      });
    },
    // 获取列表
    getList() {
      this.actIndex = -1;
      this.searchData.taskId = this.taskId;
      this.searchData.sourceId = this.sourceId;
      this.$emit('setLoading', true);
      getParamGroupList(this.searchData).then(res => {
        if(res.result_data && res.result_data.pageList && res.result_data.pageList.length > 0) {
          res.result_data.pageList.forEach((item, index) => {
            item.isEdit = false;
            item.oldGroupParam = item.groupParam;
            item.index = index;
          })
          this.data = res.result_data.pageList;
        } else {
          this.data = [];
        }
        this.$emit('setLoading', false);
      }).catch(() => {  
        this.$emit('setLoading', false);
        this.data = [];
      })
    },
    doAdd() {
      if(this.actIndex > -1 && !this.data[this.actIndex].id) {
        this.$message.warning('存在新增未保存的数据！');
        return
      }
      this.data.forEach(item => {
        item.isEdit = false;
        item.oldGroupParam = item.groupParam;
      })
      let len = this.data.length;
      this.data.push({
        isEdit: true,
        groupNum: '规则参数' + (len + 1),
        groupParam: undefined,
        oldGroupParam: undefined,
        index: len
      })
      this.actIndex = len;
    },
    // 编辑
    doEdit(row) {
      if(this.actIndex > -1) {
        this.$message.warning('存在未保存的数据！');
        return;
      }
      row.isEdit = true;
      this.actIndex = row.index;
    },
    // 保存
    doSave(row) {
      let repeatData = this.data.filter(item => {
        return item.oldGroupParam == row.oldGroupParam;
      })
      if(!row.oldGroupParam) {
        this.$message.destroy();
        this.$message.warning('请输入内容！');
        return;
      } else if (row.oldGroupParam == row.groupParam) {
        row.isEdit = false;
        this.actIndex = -1;
        return
      } else if (repeatData.length > 1) {
        this.$message.destroy();
        this.$message.warning('规则参数已存在，请勿重复添加！');
        return;
      }
      this.doExport(row, 'save');
    },
    // 保存前，导出规则线束列表
    doExport(row, type) {
      this.$emit('setLoading', true);
      exportParamList({sourceId: this.sourceId, taskId: this.taskId}).then(res => {
        let obj = {
          fileName: res.result_data.fileName,
          fileBase64Code: res.result_data.strBase64
        }
        this.$downloadFile(obj);
        if(type == 'save') {
          this.doAddOrUpdate(row);
        } else {
          deleteParamGroup({id: row.id, taskId: this.taskId, sourceId: this.sourceId}).then(res=> {
            this.getList();
            setTimeout(() => {
              this.$message.success('删除成功！');
            }, 300);
          }).catch(() => { this.$emit('setLoading', false); })
        }
        this.$emit('lisChange');
      }).catch(() => { this.$emit('setLoading', false); })
    },
    // 新增或保存
    doAddOrUpdate(row) {
      let params = {
        sourceId: this.sourceId,
        source: this.row.source,
        agreement: this.row.agreement,
        taskId: this.taskId,
        groupParam: row.oldGroupParam,
        groupNum: row.groupNum,
        id: row.id
      }
      addOrUpdateParamGroup(params).then(res=> {
        this.getList();
        setTimeout(() => {
          this.$message.success('保存成功！');
        }, 300);
      }).catch(() => { this.$emit('setLoading', false); })
    },
    // 删除
    doDelete(row) {
      this.$confirm({
        title: '提示',
        content: '是否确认删除该数据？',
        okText: '确定',
        cancelText: '取消',
        onOk: () => {
          this.doExport(row, 'detete');
        }
      })
    },
    // 取消编辑
    doCancel(row) {
      if(!row.id) {
        this.data.splice(row.index, 1);
      } else {
        row.isEdit = false;
        row.oldGroupParam = row.groupParam;
      }
      this.actIndex = -1;
    },
  }
}
</script>
<style lang="less" scoped>
.my-table {
  margin-top: 4px;
}
.table-icon {
  cursor: pointer;
}
.table-icon + .table-icon {
  margin-left: 24px;
}
.empty-div {
  text-align: center;
  img {
    width: 160px;
  }
  div {
    margin-bottom: 24px;
    color: #666;
  }
}
:root[data-theme='dark'] {
  .empty-div div {
    color: #fff;
  }
}
</style>