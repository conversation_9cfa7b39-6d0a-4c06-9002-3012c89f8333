<template>
  <div class="drawer-form-com">
    <div class="drawer-form-content">
      <a-spin size="small" :spinning="loading" class="width-height-100">
        <detail-layout :labelList="baseInfoDetail" :form="formData.baseInfo" title="基本信息">
          <!--          <template v-slot:findUserName>-->
          <!--            <a-col :span="8" class="detail_layout_content">-->
          <!--              <span class="left">发现人</span>-->
          <!--              <span class="right">-->
          <!--                {{ form.faultManagementList ? form.faultManagementList[0].findUser || '&#45;&#45;' : '&#45;&#45;' }}-->
          <!--              </span>-->
          <!--            </a-col>-->
          <!--          </template>-->
        </detail-layout>
        <detail-layout :labelList="disposeDetail" :form="formData.execute" title="执行信息">
          <template v-slot:annex="execute">
            <a-col :span="24" class="detail_layout_content">
              <span class="left">附件</span>
              <span class="right">
                <div class="file-div detail-file">
                  <div class="file-label">签到：</div>
                  <file-upload-view
                    v-if="execute.attachmentsForCheckin && execute.attachmentsForCheckin.length > 0"
                    class="file-upload"
                    :disabled="true"
                    v-model="execute.attachmentsForCheckin"
                    listType="text"
                  />
                  <span v-else>--</span>
                </div>
                <div class="file-div detail-file">
                  <div class="file-label">处理前：</div>
                  <file-upload-view
                    v-if="execute.attachmentsBeforeTaskProcessing && execute.attachmentsBeforeTaskProcessing.length > 0"
                    :disabled="true"
                    v-model="execute.attachmentsBeforeTaskProcessing"
                    listType="text"
                  />
                  <span v-else>--</span>
                </div>
                <div class="file-div detail-file">
                  <div class="file-label">处理后：</div>
                  <file-upload-view
                    v-if="execute.resolveDocs && execute.resolveDocs.length > 0"
                    :disabled="true"
                    v-model="execute.resolveDocs"
                    listType="text"
                  />
                  <span v-else>--</span>
                </div>
              </span>
            </a-col>
          </template>
        </detail-layout>
      </a-spin>
    </div>
    <div class="drawer-form-foot">
      <throttle-button class="solar-eye-btn-primary-cancel" label="返回" @click="$emit('cancel')" />
    </div>
  </div>
</template>

<script>
import { ACTION_NAMES } from '@/utils/erpcommon';
import { workOrderDetail } from '@api/health/AlarmEvents';
import { handleNullToEmptyObj } from '@/views/health/config';

export default {
  name: 'FarmOrderDetail',

  data() {
    return {
      loading: false,
      formData: {
        baseInfo: {},
        execute: {}
      },
      baseInfoDetail: [
        { label: '电站名称', key: 'psaName' },
        {
          label: '实体电站',
          key: 'psName'
        },
        { label: '电站地址', key: 'psLocation' },
        { label: '户主电话', key: 'psTelephone' },
        { label: '缺陷来源', key: 'orderSourceName' },
        { label: '发现人', slot: 'findUserName' },
        { label: '预计消除时间', key: 'predictRecoverTime' },
        {
          label: '派发人',
          key: 'dispatchSingle'
        },
        {
          label: '工单派发时间',
          key: 'actualStartTime'
        },
        {
          label: '处理人',
          key: 'eliminateName'
        },
        {
          label: '运维商',
          key: 'operationalDealers'
        },
        {
          label: '备注',
          key: 'faultRemark',
          span: 24
        },
        {
          label: '附件',
          key: 'files',
          span: 24,
          type: 'file:text'
        }
      ],
      disposeDetail: [
        {
          label: '现场情况',
          key: 'sceneConditionName'
        },
        {
          label: '故障原因',
          key: 'faultReasonName'
        },
        { label: '处理结果', key: 'resolveResultName' },

        { label: '工作内容', key: 'workContentName' },
        { label: '情况说明', key: 'orderResolveRemark', span: 24, lineClamp: 3 },
        { label: '反馈时间', key: 'feedbackTime' },
        { label: '实际消除时间', key: 'realRecoverTime' },
        {
          label: '累计修复时间(h)',
          key: 'totalRepairTime'
        },
        { slot: 'annex' }
      ]
    };
  },
  methods: {
    init(type, row) {
      this.loading = true;
      workOrderDetail({ alarmId: row.id, defectId: row.defectId, psId: row.psId })
        .then((res) => {
          this.loading = false;
          Object.assign(this.formData, handleNullToEmptyObj(res));
        })
        .catch((err) => {
          this.loading = false;
          this.$message.error(err.message || '获取详情失败');
        });
      return ACTION_NAMES[type];
    }
  }
};
</script>

<style lang="less" scoped>
:deep(.file-div) {
  display: flex;

  .file-label {
    width: 56px;
    text-align: right;
  }

  .file-upload {
    padding-top: 6px;
  }
}

.detail-file {
  :deep(.ant-upload-list-item) {
    margin-top: 0;
  }
}
</style>
