<template>
  <div
    id="monitor-content"
    class="monitor-content"
    :class="{
      height32: deviceValue !== 'FullView' || params.psId != '107353' || fLoad
    }"
  >
  <StoreOverView :deviceParams="params" v-if="stationType == 'Storage'"></StoreOverView>
    <OverView :deviceParams="params" v-else></OverView>
    <div class="right" style="display: flex;" :style="{marginLeft: deviceValue === 'FullView' && params.psId == '107353' && !fLoad ? 0 : '260px'}">
      <header :class="{ 'right-header': deviceValue === 'FullView' && params.psId == '107353' }" v-show="deviceValue!='RackBMSDetail'">
        <div class="ps-status" v-show="deviceValue === 'FullView'|| stationType == 'Storage' ">{{stationType == 'Storage' ?'电站运行':'电站状态'}}</div>
        <div class="ps-name" :title="params.psName" :style="{maxWidth: deviceValue === 'Inverter'|| deviceValue === 'CombinerBox' ? '28%':'50%'}">{{ params.psName }}</div>
        <div class="select-ps">
          <ps-tree-select
            @change="getChild"
            :isPsName="params.psName"
            v-model="params.psId"
            :isQueryPs="1"
            :isBody="true"
            :isInput="false"
            style="width: 100%"
            :psCateGory="psCateGory"
            :hasMaintenanceStatusParams="true"
          />
        </div>
        <div class="weather" v-show="weatherInfo">
         <img
            v-if="weatherInfo.iconDir"
            :src="weatherInfo.iconDir"
            style="height: 30px; padding-right: 24px object-fit: contain"
          /><div style="padding-left: 16px">{{ weatherInfo.tempNight }}℃~{{ weatherInfo.tempDay }}℃</div>

          <a-divider type="vertical" />
          {{weatherInfo.windDir}}
        </div>
        <template>
          <div class="station_status margin_r_24" v-if="stationStatus && stationType != 'Storage'">
            <img :src="require(`@/assets/images/monitor/device/alarmType_${stationStatus}.png`)" />
            <div :class="['alarm_type_' + stationStatus]">{{ alarmType.name }}</div>
          </div>
          <div style="position: relative; width: 124px; height: 72px" class="margin_r_24">
            <a-dropdown :trigger="['click']">
              <a
                class="ant-dropdown-link"
                @click="(e) => e.preventDefault()"
                style="position: absolute"
                :style="{ top: baseInfo.statusSignName ? '0px' : '26px', right: baseInfo.statusSignName ? 0 : '10px' }"
              >
                <div v-if="baseInfo.statusSignName" style="position: relative" class="solar-eye-seal-div">
                  <img :src="darkSealBg" />
                  <span class="solar-eye-seal-detail">{{ baseInfo.statusSignName }}</span>
                </div>
                <span v-else>
                  <svg-icon iconClass="sign" style="color: '#AB4C2D'"></svg-icon>
                  <span class="solar-eye-seal">&nbsp;&nbsp;状态标牌</span>
                </span>
              </a>
              <a-menu slot="overlay" @click="handleMenuClick($event, index, item)" :theme="dark">
                <a-menu-item
                  v-for="item in menuItemList"
                  :key="item.secondTypeCode"
                  :style="{ background: getSelectColor(item.secondTypeCode, index) }"
                >
                  <a-tooltip placement="right">
                    <template slot="title">
                      <span>{{ item.remark }}</span>
                    </template>
                    <span>{{ item.secondName }}</span>
                  </a-tooltip>
                </a-menu-item>
              </a-menu>
            </a-dropdown>
          </div>
          <div class="margin_r_24 anomaly_device" :style="{opacity:stationType != 'Storage'?1:0}">
            异常设备<span style="font-size: 32px; font-weight: 500; color: #ff4d4f">{{ alarmDeviceCount }}</span>
          </div>
          <div class="right-static" @click="handleStaticClick"><span style="padding-left: 8px">设备统计&nbsp;&nbsp;></span></div>
        </template>

      </header>
      <div v-if="getDevice() || deviceValue === 'BoxChange'" style="margin-top:16px">
        <template v-if="getDevice()">
          <span class="search-label  margin_r_24">设备型号&nbsp;&nbsp;</span>
          <a-select class="type-select" v-model="params.model" style="width: 260px">
            <a-select-option value="">全部</a-select-option>
            <a-select-option v-for="item in modelList" :key="item" :value="item">{{ item }}</a-select-option>
          </a-select>
        </template>
        <template v-if="getDevice() || deviceValue === 'BoxChange'">
          <span class="search-label" style="margin-left: 24px">设备名称&nbsp;&nbsp;</span>
          <a-input placeholder="请输入设备名称" v-model="params.deviceName" style="width: 260px"></a-input>
        </template>

        <a-button
          class="solar-eye-btn-primary margin-left-24"
          v-if="getDevice() || deviceValue === 'BoxChange'"
          @click="searchEvent"
          :loading="loading"
          >查询</a-button
        >
        <a-button
          class="solar-eye-btn-grey margin-left-24"
          v-if="getDevice() || deviceValue === 'BoxChange'"
          @click="resetEvent"
          :loading="loading"
          >重置</a-button
        >
        </div>

      <a-spin v-if="fLoad && isFirst" style="line-height: 60vh"></a-spin>
      <template v-if="initFinish">
        <component v-show="typeList.length >0" v-bind:is="device" ref="device" @select="changeDevice" @loaded="changeLoading" @hook:mounted="isFinished" :psInfo="psInfo"></component>
        <footer v-show="typeList.length >0 && deviceValue !='RackBMSDetail'" :style="{ background: deviceValue === 'FullView' ? '' : 'var(--zw-card-bg-color--default)'}" :class="{'full-footer': deviceValue === 'FullView' && params.psId == '107353'}">
          <template v-for="item in typeList">
            <div class="flex-center" @click="changeDevice(item)" :class="{ selected: item.value == deviceValue }" :key="item.value" v-if="!item.hidden">
              {{ item.name }}
            </div>
          </template>
        </footer>
      </template>
    </div>
  </div>
</template>
<script>
import OverView from './modules/OverView.vue';
import StoreOverView from './modules/StoreOverView.vue';
import { getInverterModel, getPsBaseInfo, getFaultDeviceCount } from '@/api/monitor/device.js';
import { getSystemCodeList, getFuture15DaysWeatherByPsId } from '@/api/health/healthapi.js';
import { setStatusSign } from '@/api/monitor/runMonitor';
import moment from 'moment';
export default {
  components: { OverView, StoreOverView },
  data () {
    return {
      device: null,
      deviceValue: '',
      darkSealBg: require('@assets/images/public/seal_s.png'),
      menuItemList: [],
      deviceList: [
        { name: '电站全景', value: 'FullView', type: 0 },
        { name: '升压站', value: 'BoostVoltage', type: 6 },
        { name: '箱变', value: 'BoxChange', type: 99, deviceSubType: 4 },
        { value: 'Inverter', name: '逆变器', type: 1 },
        { value: 'CombinerBox', name: '汇流箱', type: 4 },
        { value: 'EnergyStorage', name: '储能单元', type: 47 },
        { value: 'CurrentConverter', name: '变流器', type: 37 },
        { value: 'BMS', name: '系统BMS', type: 23 },
        { value: 'RackBMS', name: 'Rack BMS', type: 24 },
        { name: '电表', value: 'ElectricMeter', type: 7 },
        { name: '环境检测仪', value: 'EnviMonitor', type: 5 },
        { value: 'RackBMSDetail', name: 'RackBMS详情', type: 24, hidden: true }

      ],
      params: {
        psId: '',
        psName: '',
        deviceName: '',
        model: '',
        source: 1,
        deviceType: '',
        deviceSubType: undefined
      },
      baseInfo: {
        deviceCount: '',
        weather: [],
        statusSignName: '',
        statusSign: '',
        inverterType: ''
      },
      status: {},
      alarmType: {
        status: '',
        name: ''
      },
      modelList: [],
      typeList: [],
      fLoad: false,
      isFirst: true,
      nameList: [],
      weatherInfo: {},
      loading: false,
      psInfo: {
        psKey: undefined,
        uuid: undefined
      },
      alarmDeviceCount: '',
      psCateGory: "'Storage'",
      initFinish: false
    };
  },
  props: {
    commonParams: {
      type: Object,
      default: () => {}
    },
    stationType: {
      type: Number,
      default: 4
    }
  },
  provide () {
    return {
      params: () => this.params,
      status: () => this.alarmType.status,
      isShowInverterType: () => this.isShowInverterType,
      fLoad: () => this.fLoad
    };
  },
  created () {
    this.params.psId = this.commonParams.psId;
    this.params.psName = this.commonParams.psName;
    this.params.source = this.commonParams.source;
    this.psCateGory = this.stationType == 'Storage' ? "'Storage'" : "'CentralizedPV','DistributedPV','CIPV'";
    this.getSystemCodeList();
    this.getPsBaseInfo();
    this.editWeatherInfo(this.params.psId);
    this.getTabs(true);
    // this.initData();
  },
  computed: {
    stationStatus () {
      // 1接入中 2电站故障停机 3电站通讯中断 4电站告警运行 5电站正常运行 6电站正常停机
      return this.alarmType.status ? (this.alarmType.status == 2 ? 1 : this.alarmType.status == 5 || this.alarmType.status == 6 ? 5 : 2) : '';
    },
    isShowInverterType () {
      let inverter = this.baseInfo.inverterType ? this.baseInfo.inverterType.split(',') : [];
      return inverter.length == 2 ? 2 : inverter.indexOf('15') > -1 ? 15 : 14;
    }
  },
  mounted () {
    let that = this;
    let timer = setInterval(() => {
      that.editWeatherInfo(this.params.psId);
    }, 1000 * 5 * 60);
    this.$once('hook:beforeDestroy', () => {
      clearInterval(timer);
      timer = null;
    });
    this.$once('hook:deactivated', () => {
      clearInterval(timer);
      timer = null;
    });
  },
  methods: {
    initData () {
      this.$emit('changePage', 2, this.deviceList[0].name);
      this.typeList = [];
      this.deviceValue = 'FullView';
      this.device = () => import('@/views/monitor/modules/FullView');
    },
    async isFinished () {
      this.isFirst = false;
      if (this.deviceValue === 'FullView') {
        let res = await this.getData();
        this.deviceValue === 'FullView' && this.$refs.device.initData(res);
      } else {
        // this.$refs.device.initData(this.params);
      }
    },
    changeLoading (isLoad) { // 监听子页面的loading变化
      this.loading = isLoad;
    },
    getData () {
      return new Promise((resolve, reject) => {
        getFaultDeviceCount({ psId: this.params.psId })
          .then((res) => {
            resolve(res);
          })
          .catch((res) => {
            reject(res);
          });
      });
    },
    async getTabs (isFirst) {
      if (this.isFirst) {
        this.typeList = [];
      } else {
        this.getPsBaseInfo();
      }

      this.fLoad = true;
      let res = await this.getData(); // 获取res之后处理tab，判断显示哪些tab，只有金阳时才需要初始化数据
      this.fLoad = false;
      let data = res.result_data;
      this.alarmType.status = data.sationStatus;
      this.alarmType.name = data.sationStatusName;
      this.alarmDeviceCount = data.alarmDeviceCount;
      this.dynamicGetTab(data.alarmList);
    },
    // 动态获取设备tab
    dynamicGetTab (res) {
      let arr = [];
      let subDevice = ['1', '2', '5', '6', '7', '8', '10', '11', '12'];
      let transformer = [];
      res.forEach((element) => {
        if (element.deviceCount > 0) {
          if ((element.deviceSubType && subDevice.includes(element.deviceSubType)) || element.deviceType == '3') {
            transformer.push(Number(element.deviceType));
          } else if (element.deviceSubType && element.deviceSubType == 4) {
            arr.push(99);
          } else {
            arr.push(Number(element.deviceType));
          }
        }
      });
      if (transformer.length > 0) {
        arr.unshift(6);
      }
      this.typeList = this.setTypeList(arr);
      if (this.typeList.length > 0) {
        this.changeDevice(this.typeList[0]);
      }
    },
    setTypeList (arr) {
      return this.deviceList.filter((item) => {
        return (item.type == 0 && this.params.psId == '107353') || arr.indexOf(item.type) > -1;
      });
    },
    getDevice () {
      // 判断是否需要设备类型
      let arr = ['Inverter', 'CombinerBox', 'EnergyStorage', 'CurrentConverter', 'RackBMS', 'BMS'];
      return arr.indexOf(this.deviceValue) > -1;
    },
    /**
     * 切换设备
     * params {obj} 组件名称、路径、类型等
     * params {item} 跳转时的过滤
     */
    async changeDevice (obj, item) {
      if (typeof obj === 'boolean') {
        this.getPsBaseInfo();
        return;
      }
      this.psInfo = item ? { psKey: item.ps_key || item.psKey, uuid: item.uuid } : { psKey: undefined, uuid: undefined };
      if (!item) this.nameList = [];
      const index = this.nameList.indexOf(item);
      const isBox = obj.deviceSubType && obj.deviceSubType == 4;
      if (item) {
        if (index === -1) {
          this.nameList.push(item);
        } else if (isBox || index === 0) {
          this.nameList = [];
        } else {
          this.nameList.splice(index, 1);
        }
      }
      this.deviceValue = obj.value;
      this.params.deviceName = '';
      this.params.model = '';
      this.params.deviceType = obj.type;
      this.params.deviceSubType = obj.deviceSubType;
      if (this.getDevice()) {
        this.getModelList();
      }
      // let name = item ? this.getUpDeviceName() : '';
      this.$emit('changePage', 2, obj.name, this.nameList);
      this.device = () => import('@/views/monitor/modules/' + obj.value);
    },
    // 获取上级设备名称
    getUpDeviceName () {
      let name = '';
      if (this.nameList.length == 0) return;
      this.nameList.forEach(item => {
        name = name + item.device_name + ' / ';
      });
      return name;
    },
    getPsBaseInfo () {
      // 电站基本信息
      return new Promise((resolve, reject) => {
        getPsBaseInfo({ psId: this.params.psId, source: this.params.source })
          .then((res) => {
            this.baseInfo = Object.assign({}, this.baseInfo, res.result_data);
            this.initFinish = true;
            resolve(res);
          })
          .catch((res) => {
            this.initFinish = true;
            reject(res);
          });
      });
    },
    /**
     * 选择电站
     * params {obj} 返回节点的所有信息
     */
    async getChild (value, obj) {
      this.$emit('click', obj);
      this.params.psId = obj.id;
      this.params.psName = obj.name;
      this.params.source = obj.source;
      this.$emit('changePageParams', {
        psId: obj.id,
        psName: obj.name,
        source: obj.source
      });
      this.isFirst = true;
      this.getTabs();
      // this.initData();
      this.editWeatherInfo(obj.id);
      let res = await this.getPsBaseInfo();
      this.baseInfo = res.result_data;
      if (this.getDevice()) {
        this.getModelList();
      }
    },
    getModelList () {
      // 获取设备类型
      this.params.model = '';
      getInverterModel({ psId: this.params.psId, source: this.params.source, deviceType: this.params.deviceType }).then(
        (res) => {
          this.modelList = res.result_data;
        }
      );
    },
    searchEvent () {
      this.$refs.device.initData();
    },
    resetEvent () {
      this.params.deviceName = '';
      this.params.model = '';
      this.$refs.device.initData();
    },
    editWeatherInfo (psId) {
      this.weatherInfo = {};
      getFuture15DaysWeatherByPsId({
        psId: psId
      }).then((res) => {
        if (res.result_code == 1) {
          let { forecast } = res.result_data;
          let todayInfo = forecast.find((item) => {
            return moment(item.predictDate).isSame(new Date(), 'day');
          });
          this.weatherInfo = {
            iconDir: this.mapWeatherIconById(todayInfo.conditionIdDay),
            tempNight: todayInfo.tempNight,
            tempDay: todayInfo.tempDay,
            windDir: moment(new Date()).isBetween(todayInfo.sunrise, todayInfo.sunset, 'second') ? todayInfo.windDirDay : todayInfo.windDirNight
          };
        } else {
          this.$message.error(res.result_msg);
        }
      });
    },
    mapWeatherIconById (conditionId, flag) {
      if (conditionId >= 0) {
        return require(`@/assets/images/weather/W${conditionId}.png`);
      }
      return null;
    },
    async getSystemCodeList () {
      let res = await getSystemCodeList({ firstTypeCode: '0059' });
      this.menuItemList = res.result_data['0059'];
      this.stationStatusOptions = [
        {
          value: 'ALL',
          label: '全部'
        },
        ...this.menuItemList.map((item) => {
          return {
            value: item.secondTypeCode,
            label: item.secondName
          };
        }),
        {
          value: -1,
          label: '未挂牌'
        }
      ];
    },
    getSelectColor (item, index) {
      return item === this.baseInfo.statusSign ? '#2A3E5B' : '';
    },
    handleMenuClick (e, index, item) {
      if (e.domEvent.target.innerText.length > 6) {
        // 屏蔽按enter键输入紊乱的情况
        return;
      }
      setStatusSign({
        psId: this.params.psId,
        statusSign: this.baseInfo.statusSign != e.key ? e.key : ''
      }).then((res) => {
        if (res.result_code === '1') {
          let statusSignName = this.baseInfo.statusSign != e.key ? e.domEvent.target.innerText : '';
          let statusSign = this.baseInfo.statusSign != e.key ? e.key : '';
          this.baseInfo.statusSignName = statusSignName;
          this.baseInfo.statusSign = statusSign;
        }
      });
    },
    handleStaticClick (e) {
      this.$emit('open', e);
    }
  }
};
</script>
<style scoped lang="less">
[class^='alarm_type_'] {
  position: absolute;
  left: 42px;
  font-weight: 500;
  letter-spacing: 2px;
  top: 5px;
}

.monitor-content {
  background: var(--zw-card-bg-color--default);
  border-radius: 4px;
  position: relative;
  height: calc(100% - 24px);
  &.height32 {
    height: calc(100% - 32px);
  }
  .margin_r_24 {
    margin-left: 24px;
  }
  &.bg {
    background: url('../../assets/images/monitor/device/ps-bg.png');
    background-size: cover;
  }
  &.bg1 {
    background: url('../../assets/images/monitor/device/static.png') no-repeat;
  }
  .right {
    display: flex;
    flex-direction: column;
    margin-left: 260px;
    height: 100%;
    main {
      flex: 1;
      overflow: auto;
    }
    header {
      margin-left: 20px;
      &.right-header {
        position: absolute;
        left: 260px;
        width: calc(100% - 280px);
        z-index: 99;
      }
      margin-top: 24px;
      height: 72px;
      background: linear-gradient(268deg, rgba(13, 38, 74, 0.9) 0%, rgba(0, 0, 0, 0.35) 39%, #0D264A 99%);
      box-shadow: 0px 6px 10px 0px rgba(16, 8, 2, 0.2);
      border-radius: 4px;
      opacity: 0.8;
      border: 2px solid var(--zw-border-color--default);
      backdrop-filter: blur(14px);
      display: flex;
      align-items: center;
      justify-content: flex-start;
       .right-static {
        font-size: 14px;
        cursor: pointer;
        text-align: right;
        margin-right: 16px;
          &:hover {
            color: var(--zw-primary-color--default);
        }

      }
      .ps-status {
        width: 120px;
        background: linear-gradient(90deg, rgba(36, 156, 255, 0.3) 7%, rgba(36, 156, 255, 0.1) 96%);
        font-size: 22px;
        height: 72px;
        line-height: 72px;
        font-family: 'YouSheBiaoTiHei';
        &::before {
          content: ' ';
          border: 2px solid #24ccff;
          margin-right: 16px;
          font-size: 22px;
          vertical-align: bottom;
        }
      }
      .ps-name {
        font-size: 18px;
        font-weight: 500;
        color: var(--white);
        margin-left: 24px;
        max-width: 28%;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }

      .select-ps {
        width: 38px;
        height: 24px;
        background: rgba(255, 255, 255, 0.25);
        border-radius: 16px;
        border: 1px solid rgba(200, 212, 226, 1);
        margin-left: 8px;
        position: relative;
        text-align: center;
      }
      .weather {
        width: 220px;
        height: 32px;
        background: var(--zw-input-bg-color--disable);
        border-radius: 16px;
        margin-left: 24px;
        display: flex;
        flex-direction: row;
        align-items: center;
        padding: 0 16px;
      }
    }
    footer {
      margin-left: 20px;
      z-index: 1;
      border-radius: 4px 4px 0px 0px;
      height: 78px;
      display: flex;
      justify-content: center;
      align-items: center;
      z-index:99;
      div {
        width: 120px;
        height: 54px;
        padding-bottom: 4px;
        background: linear-gradient(180deg, rgba(0, 6, 8, 0.6) 0%, rgba(3, 31, 40, 0) 100%);
        border-top: 4px solid;
        border-image: linear-gradient(90deg, rgba(102, 211, 244, 0.2) 0%, rgba(12, 149, 180, 0.2) 90%) 1 10;
        cursor:pointer;
      }
      div:hover {
        background: linear-gradient(180deg, #154B71 0%, rgba(21, 75, 113, 0) 100%);;
        border-image: none;
        border-color: #2470A0;
        box-shadow: 0px 2px 10px 2px rgba(24, 49, 68, 0.5);
      }
      div.selected {
        background: linear-gradient(180deg, #2E84BB 0%, rgba(33, 170, 218, 0) 100%);
        border-image: linear-gradient(90deg, #85E6FC 0%, #23CCFF 90%) 1 10;
        box-shadow: 0px 4px 6px 0px rgba(8, 51, 86, 0.5);
      }
      div + div {
        margin-left: 16px;
      }
    }
    .full-footer{
      position: absolute;
      left: 260px;
      width: calc(100% - 280px);
      bottom:0;
    }
  }
  .station_status {
    position: relative;
  }
  .anomaly_device {
    display: flex;
    align-items: center;
    flex:1;
    span {
      padding-left: 8px;
    }
  }
}
.solar-eye-seal-detail {
  position: absolute;
  left: 30px;
  top: 27px;
  transform: rotate(-34deg);
  font-weight: 500;
  color: #ff3546;
  font-size: 16px;
}
</style>
<style lang="less">
@import url('./less/popover.less');
</style>
