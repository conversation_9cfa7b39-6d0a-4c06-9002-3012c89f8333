<!-- 电站管理 -->
<template>
  <div class="station-model">
    <a-spin :spinning="loading">
      <div class="solar-eye-search-model" style="padding: 0">
          <div class="solar-eye-search-content">
            <a-row a-row :gutter="24" style="margin: 0">
              <!-- <a-col :span="5">
                <div class="search-item">
                  <span class="search-label">电站名称</span>
                    <ps-tree-select @change="refresh" :isPsaDisable="false" :isQueryPs="1" :isPsManage="true" style="width: 100%;" />
                </div>
              </a-col> -->
              <a-col :xxl="6" :xl="8" :md="12">
                <role-tree-select depLabel="所属组织" :hasDepDefaultValue="false" :depTreeAllowClear="true" @change="refresh" ref="roleTree" :isOnlyDep="true"></role-tree-select>
              </a-col>
              <a-col :xxl="6" :xl="8" :md="12">
                <div class="search-item">
                  <span class="search-label">所属项目</span>
                  <a-input v-model="queryParams.projectName" placeholder="请输入所属项目" :maxLength="255" @blur="queryParams.projectName = $trim($event)"/>
                </div>
              </a-col>
              <a-col :xxl="6" :xl="8" :md="12">
                <div class="search-item">
                  <span class="search-label">电站名称</span>
                  <a-input allowClear v-model="queryParams.psName" @blur="queryParams.psName = $trim($event)" size="default"
                    placeholder="请输入电站名称" style="width: 100%;"></a-input>
                </div>
              </a-col>
              <template v-if="toggleSearchStatus">
                <a-col :xxl="6" :xl="8" :md="12">
                  <div class="search-item">
                    <span class="search-label">电站PS_ID</span>
                    <a-input allowClear v-model="queryParams.psIdLike" @blur="queryParams.psIdLike = $trim($event)" size="default"
                      placeholder="请输入电站PS_ID" style="width: 100%;"></a-input>
                  </div>
                </a-col>
                <a-col :xxl="4" :xl="8" :md="12">
                  <div class="search-item">
                    <span class="search-label">电站来源</span>
                    <a-select v-model="queryParams.source">
                      <a-select-option value="">全部</a-select-option>
                      <a-select-option value="1">阳光云</a-select-option>
                      <a-select-option value="0">自主接入</a-select-option>
                    </a-select>
                  </div>
                </a-col>
                <a-col :xxl="5" :xl="8" :md="12">
                  <div class="search-item">
                    <span class="search-label">电站状态</span>
                    <a-select v-model="queryParams.validFlag">
                      <a-select-option value="">全部</a-select-option>
                      <a-select-option value="1">已接入</a-select-option>
                      <a-select-option value="2">已关闭</a-select-option>
                      <a-select-option value="3">未接入</a-select-option>
                    </a-select>
                  </div>
                </a-col>
                <a-col :xxl="5" :xl="8" :md="12">
                <div class="search-item">
                  <span class="search-label">通讯模块SN</span>
                    <a-input
                      v-model="queryParams.sn"
                      placeholder
                      clearable
                      @pressEnter="clickSearch"
                      allow-clear
                    ></a-input>
                </div>
                </a-col>
              </template>
              <a-col :xxl="4" :xl="8" :md="12">
                <div class="search-item">
                  <a-button class="solar-eye-btn-primary" @click.stop.prevent="clickSearch()">查询</a-button>
                  <a-button class="solar-eye-btn-primary-cancel" @click.stop.prevent="resetSearch()">重置</a-button>
                  <span class="com-color" @click="handleToggleSearch">
                    {{ toggleSearchStatus ? "收起" : "展开" }}
                    <a-icon :type="toggleSearchStatus ? 'up' : 'down'" />
                  </span>
                </div>
              </a-col>
            </a-row>
          </div>
        </div>
        <div class="solar-eye-gap"></div>
      <a-col class="solar-eye-main-content">
      <!-- 操作按钮 -->
        <div class="operation" style="height: 32px">
          <div class="operation-btn">
            <erp-button label="设备映射" perms="station:importStation" size="default" @click="handleImport('设备映射', '/manageDevice/importData', '1')"></erp-button>
            <erp-button label="新增" perms="station:add" size="default" @click="addStation"></erp-button>
<!--            <erp-button label="下载导入模板" perms="station:downloadTemplate" size="default" @click="exportTemplate" class="solar-eye-btn-primary-cancel"></erp-button>-->
            <erp-button label="智能硬件导入" perms="station:import" size="default" class="solar-eye-btn-primary-cancel"
              @click="handleImport('智能硬件导入', '/healthPowerStation/importDevice', '2')"></erp-button>
            <a-button :disabled="selectionRows.length < 1" v-if="show('station:exportDeatil')" @click="handleExport('detail')" class="solar-eye-btn-primary-cancel" :loading="exportDetailLoading">导出详情</a-button>
            <a-button v-if="show('station:export')" @click="handleExport('list')" class="solar-eye-btn-primary-cancel" :loading="exportLoading">导出</a-button>
          </div>
        </div>
          <vxe-table
            v-loading="pageloading"
            :data="dataSource"
            ref="multipleTable"
            class="my-table"
            @sort-change="handleTableSortChange"
            :sort-config="{ remote: true }"
            resizable
            show-overflow
            highlight-hover-row
            size="small"
            @checkbox-all="onSelectChange"
            @checkbox-change="onSelectChange"
            :height="tableHeight - 24"
          >
            <vxe-table-column type="checkbox" width="40"></vxe-table-column>
            <vxe-table-column field="psId" width="100" title="电站PS_ID"></vxe-table-column>
            <vxe-table-column show-overflow="title" :formatter="tabFormatter" title="电站名称" min-width="160" field="psName"> </vxe-table-column>
            <vxe-table-column show-overflow="title" :formatter="tabFormatter" title="电站来源" min-width="120" field="sourceName"> </vxe-table-column>
            <vxe-table-column show-overflow="title" :formatter="tabFormatter" title="接入来源" min-width="120" field="accessSourceName"> </vxe-table-column>
            <vxe-table-column show-overflow="title" :formatter="tabFormatter" title="所属项目" min-width="160" field="psaName">
            </vxe-table-column>
             <vxe-table-column show-overflow="title" :formatter="tabFormatter" title="电站状态" min-width="120" field="validFlagName">
            </vxe-table-column>
            <vxe-table-column show-overflow="title" :formatter="tabFormatter" title="装机功率(kWp)" field="totalCapcity" min-width="110" align="right">
            </vxe-table-column>
            <vxe-table-column show-overflow="title" :formatter="tabFormatter" title="电站地址" field="psLocation" min-width="160">
            </vxe-table-column>
            <vxe-table-column show-overflow="title" :formatter="tabFormatter" title="建站时间" min-width="160" field="installDate">
            </vxe-table-column>
            <vxe-table-column show-overflow="title" :formatter="tabFormatter" title="通讯模块SN" field="sn" min-width="120">
            </vxe-table-column>
            <vxe-table-column title="操作" :width="260" fixed="right" :resizable="false">
              <template slot-scope="scope">
                <div class="flex-action-button">
                  <a
                    v-if="scope.row.isDeleted === 0 && scope.row.source != '0' && show('station:sync')"
                    type="link"
                    size="small"
                    @click.stop.prevent="oneClickSync(index, scope.row)"
                    >一键同步</a
                  >
                  <a
                    v-if="scope.row.isDeleted === 0  && show('station:config')"
                    type="link"
                    size="small"
                    @click.stop.prevent="equipmentConfig(scope.row)"
                    >装机信息配置</a
                  >
                  <!-- <a
                    v-if="scope.row.algorithmFlag === '1' && scope.row.isDeleted === 0"
                    type="link"
                    size="small"
                    @click.stop.prevent="stopAlgorithmClick(index, scope.row)"
                    >停止算法</a
                  > -->
                  <a
                    v-if="show('station:delete') && ((scope.row.algorithmFlag === '2' && scope.row.isDeleted === 1 && scope.row.source != '0') || (scope.row.source == '0' && scope.row.isOverMonth == '1'))"
                    size="small"
                    type="link"
                    @click="handleDelete(index, scope.row)"
                    >删除</a
                  >
                  <!-- <a
                    v-if="scope.row.isDeleted === 0 && scope.row.algorithmFlag !== '1'"
                    type="link"
                    size="small"
                    class="el-bg-btn"
                    @click.stop.prevent="openAlgorithm(index, scope.row)"
                    >开启算法</a
                  > -->
                  <!-- <a
                    v-if="scope.row.algorithmFlag === '2' && scope.row.isDeleted === 0"
                    size="small"
                    type="link"
                    @click.stop.prevent="handleClear(index, scope.row)"
                    >清空</a
                  > -->
                </div>
              </template>
            </vxe-table-column>

            <template v-slot:empty>
              <span>查询无数据</span>
            </template>
          </vxe-table>
          <page-pagination
            :pageSize="queryParams.size"
            :current="queryParams.curPage"
            :total="total"
            @size-change="sizeChange"
          />
      </a-col>
    </a-spin>
    <station-add ref="stationAdd" @refresh="clickSearch"/>
    <station-mage-drawer ref="drawerForm" :psId="psId" :psName="psName" v-if="psId" @click="clearPsId"></station-mage-drawer>
    <option-form ref="optionForm" :open="modelOpen" :psId="psId" @onClose="closeModel"/>
    <!-- 导入智能采集硬件设备 -->
    <upload v-model="upload.open" :upload="upload" @fileUpload="fileUpload" listType="text" >
      <template slot='formwork'>
        <a class="blue operation-btn-hover" @click='exportTemplate'>模板下载</a>
      </template>
    </upload>
  </div>
</template>
<script>
import {
  searchPowerStation,
  getPowerStationSystemCodeList,
  oneSync,
  deletePowerStation,
  deleteSelfPowerStation,
  clearPowerStation,
  stopAlgorithm,
  getExportData,
  exportDetail
} from '@/api/health/healthapi.js';
import { downloadTemplate } from '@/api/isolarErp/config/templateupload';
import StationMageDrawer from './modules/StationMageDrawer';
import download from '@/utils/download';
import upload from '@/components/erp/upload/upload';
import optionForm from './modules/optionForm';
import tableHeight from '@/mixins/tableHeightAndSearchModel';
import StationAdd from './modules/station/StationAdd';
const baseUrl = process.env.VUE_APP_Health_BASE_URL;
const urlData = process.env.VUE_APP_Health_BASE_DATA_URL;
// const baseUrl = 'http://*********:8081';
export default {
  components: {
    StationMageDrawer,
    optionForm,
    upload,
    StationAdd
  },
  mixins: [tableHeight],
  name: 'StationMage',
  data () {
    return {
      isPro: process.env.NODE_ENV == 'production',
      algorithmFlagOptions: [],
      inverterTypeOptions: [],
      psTypeOptions: [],
      queryParams: {
        projectName: '',
        treePsId: '',
        treeOrgId: '',
        treePsaId: '',
        psName: '',
        algorithmFlag: undefined,
        psId: this.psId,
        inverterType: this.inverterType,
        psType: this.psType,
        psIdLike: '',
        sn: '',
        validFlag: '',
        curPage: 1,
        pageSize: 10,
        isPsManage: '1',
        depCodes: '',
        source: '',
        treePsaIds: []
      },
      psId: '',
      moreShow: false,
      loadTable: true,
      dataSource: [],
      total: '',
      deviceConfig: {
        open: false
      },
      url: {
        // list: 'healthPowerStation/listPowerStation',
        list: '/healthPowerStation/list',
        export: '/healthPowerStation/export'
      },
      exportLoading: false,
      exportDetailLoading: false,
      selectedRowKeys: [],
      selectionRows: [],
      modelOpen: false,
      loading: true,
      importType: '1',
      // 导入参数
      upload: {
        title: '',
        url: '',
        open: false
      },
      isFirst: true
    };
  },
  created () {
    this.getOptionsData();
  },
  beforeDestroy () {},
  mounted () {},
  methods: {
    refresh (deptCode, psaIds) {
      this.queryParams.depCodes = this.isFirst ? '' : deptCode || '';
      this.queryParams.treePsaIds = psaIds;
      // this.queryParams.treePsId = '';
      // this.queryParams.treeOrgId = '';
      // this.queryParams.treePsaId = '';
      // this.queryParams.psId = '';
      // if (node.isPsa == '0' && !node.isPs) {
      //   this.queryParams.treeOrgId = node.id;
      // } else if (node.isPsa == '1') {
      //   this.queryParams.treePsaId = node.id;
      // } else if (node.isPs == 1) {
      //   this.queryParams.treePsId = node.id;
      // }
      this.queryParams.curPage = 1;
      this.selectedRowKeys = [];
      this.selectionRows = [];
      this.getData();
      this.isFirst = false;
    },
    // 获取下拉项数据
    getOptionsData () {
      getPowerStationSystemCodeList()
        .then((res) => {
          if (res.result_code == '1') {
            let data = res.result_data;
            let obj = {
              label: '请选择',
              value: ''
            };
            // 算法状态---algorithmFlagList
            data.algorithmFlagList.forEach((item) => {
              item.label = item.secondName;
              item.value = item.secondTypeCode;
              this.algorithmFlagOptions.push(item);
            });
            this.algorithmFlagOptions.unshift(obj); // 算法状态--options
            // 系统类型---inverterTypeList
            data.inverterTypeList.forEach((item) => {
              item.label = item.secondName;
              item.value = item.secondTypeCode;
              this.inverterTypeOptions.push(item);
            });
            this.inverterTypeOptions.unshift(obj); // 系统类型---inverterTypeOptions

            // 电站类型---psTypeList
            data.psTypeList.forEach((item) => {
              item.label = item.secondName;
              item.value = item.secondTypeCode;
              this.psTypeOptions.push(item);
            });
            this.psTypeOptions.unshift(obj); // 电站类型---psTypeOptions
          } else {
            this.$message.error(res.result_msg);
          }
        })
        .catch(function () {});
    },
    // 获取列表数据
    getData () {
      this.loadTable = true;
      this.$refs.multipleTable && this.$refs.multipleTable.clearScroll();
      this.loading = true;
      this.selectedRowKeys = [];
      this.selectionRows = [];
      searchPowerStation(this.queryParams)
        .then((res) => {
          if (res.result_code === '1') {
            this.loadTable = false;
            this.dataSource = res.result_data.pageList;
            this.total = res.result_data.rowCount;
          }
          this.loading = false;
        })
        .catch((err) => {
          this.loadTable = false;
          this.loading = false;
          console.log(err);
        });
    },
    // 点击搜索按钮
    clickSearch () {
      this.queryParams.curPage = 1;
      this.getData();
    },
    // 重置查询条件
    resetSearch() {
      this.queryParams = {
        projectName: '',
        treePsId: '',
        treeOrgId: '',
        treePsaId: '',
        psName: '',
        algorithmFlag: undefined,
        psId: '',
        inverterType: undefined,
        psType: undefined,
        psIdLike: '',
        sn: '',
        validFlag: '',
        curPage: 1,
        pageSize: 10,
        isPsManage: '1',
        depCodes: '',
        source: '',
        treePsaIds: []
      },
      this.$refs.roleTree.reset();
    },
    sizeChange (current, size) {
      this.queryParams.size = size;
      this.queryParams.curPage = current;
      this.getData();
    },
    // 设备配置
    equipmentConfig (row) {
      this.psId = row.psId;
      this.psName = row.psName;
      this.$nextTick(() => {
        this.$refs.drawerForm.initDrawer(row, { title: '编辑', visible: true, type: 'edit' });
      });
    },
    // 关闭抽屉
    closeEquipmentConfig () {
      this.deviceConfig.open = false;
      this.queryParams.curPage = 1;
      this.getData();
    },
    // 一键同步
    oneClickSync (index, row) {
      let psId = row.psId;
      let msg = '是否一键同步电站名称为"' + row.psName + '"的电站及设备参数?';
      let tips = '一键同步';
      this.$confirm({
        title: tips,
        content: msg,
        okText: '确定',
        cancelText: '取消',
        onOk: () => {
          return new Promise((resolve, reject) => {
            oneSync({
              psId
            })
              .then((res) => {
                if (res.result_code == '1') {
                  this.$message.success('一键同步成功');
                  this.getData();
                  resolve();
                } else {
                  this.$message.error(res.result_msg);
                  resolve();
                }
              })
              .catch((err) => {
                reject(err);
              });
          });
        },
        onCancel () {}
      });
    },
    // 删除
    handleDelete (index, row) {
      let msg = '是否确认删除电站名称为"' + row.psName + '"的所有历史数据?';
      let tips = '删除';
      let psId = row.psId;
      this.$confirm({
        title: tips,
        content: msg,
        okText: '确定',
        cancelText: '取消',
        onOk: () => {
          if (row.source == '1') {
            return new Promise((resolve, reject) => {
              deletePowerStation({
                psId
              })
                .then((res) => {
                  if (res.result_code == '1') {
                    this.$message.success('删除成功');
                    this.getData();
                    resolve();
                  } else {
                    this.$message.error(res.result_msg);
                    resolve();
                  }
                })
                .catch(function (err) {
                  reject(err);
                });
            });
          } else {
            this.loading = true;
            deleteSelfPowerStation({ psId: row.psId, createTime: row.createTime }).then(res => {
              this.$message.success('删除成功');
              this.getData();
            }).catch(() => { this.loading = false; });
          }
        },
        onCancel () {}
      });
    },
    // 清空
    handleClear (index, row) {
      let msg = '是否确认清空电站名称为"' + row.psName + '"的所有历史数据?';
      let tips = '清空';
      let psId = row.psId;
      this.$confirm({
        title: tips,
        content: msg,
        okText: '确定',
        cancelText: '取消',
        onOk: () => {
          return new Promise((resolve, reject) => {
            clearPowerStation({
              psId
            })
              .then((res) => {
                if (res.result_code == '1') {
                  this.$message.success('清空成功');
                  this.getData();
                  resolve();
                } else {
                  this.$message.error(res.result_msg);
                  resolve();
                }
              })
              .catch(function (err) {
                reject(err);
              });
          });
        },
        onCancel () {}
      });
    },

    closeModel () {
      this.modelOpen = false;
      this.getData();
    },
    // 开启算法
    openAlgorithm (index, row) {
      // this.$refs.optionForm.openModel({psId:row.psId},'/dataCenter/modules/optionForm')
      this.modelOpen = true;
      this.psId = row.psId;
    },
    // 停止算法
    stopAlgorithmClick (index, row) {
      let msg = '是否停止电站名称为"' + row.psName + '"的算法?';
      let tips = '停止算法';
      let psId = row.psId;
      this.$confirm({
        title: tips,
        content: msg,
        okText: '确定',
        cancelText: '取消',
        onOk: () => {
          return new Promise((resolve, reject) => {
            stopAlgorithm({
              psId
            })
              .then((res) => {
                if (res.result_code == '1') {
                  this.$message.success('停止算法成功');
                  this.getData();
                  resolve();
                } else {
                  this.$message.error(res.result_msg);
                  resolve();
                }
              })
              .catch(function (err) {
                reject(err);
              });
          });
        },
        onCancel () {}
      });
    },
    clearPsId (flag, refreshFlag) {
      if (!flag) {
        this.psId = '';
      }
      if (refreshFlag) {
        this.getData();
      }
    },
    // 表格复选框变化事件
    onSelectChange (val) {
      let arr = [];
      val.records.forEach((item) => {
        arr.push(item.psId);
      });
      this.selectedRowKeys = arr;
      this.selectionRows = val.records;
    },
    handleExport (type) {
      if (type == 'list') {
        this.exportLoading = true;
        let psId = this.queryParams.psId;
        let id = psId ? JSON.stringify(psId) : '';
        if (this.selectedRowKeys.length > 0) {
          this.queryParams.psId = this.selectedRowKeys.join(',');
        }
        getExportData(this.queryParams).then(res => {
          this.exportLoading = false;
          this.queryParams.psId = id ? JSON.parse(id) : '';
          if (res.result_code === '1') {
            download(res.result_data, 'strBase64');
            this.$message.success('导出成功');
          } else {
            this.$message.warning(res.result_msg);
          }
        }).catch(() => {
          this.exportLoading = false;
          this.queryParams.psId = id ? JSON.parse(id) : '';
        });
      } else {
        this.exportDetailLoading = true;
        let list = this.selectionRows.map(item => {
          return {
            psId: item.psId,
            psName: item.psName,
            sn: item.sn,
            installDate: item.installDate,
            psaName: item.psaName,
            validFlagName: item.validFlagName,
            totalCapcity: item.totalCapcity,
            psLocation: item.psLocation,
            source: item.source
          };
        });
        exportDetail({ list: list }).then(res => {
          this.exportDetailLoading = false;
          if (res.result_code === '1') {
            download(res.result_data, 'strBase64');
            this.$message.success('导出成功');
          } else {
            this.$message.warning(res.result_msg);
          }
        }).catch(() => {
          this.exportDetailLoading = false;
        });
      }
    },
    // 导入电站/导入智能采集硬件设备
    handleImport (title, url, type) {
      let urlSer = type == '1' ? urlData : baseUrl;
      this.upload = {
        title: title,
        url: urlSer + url,
        open: true
      };
      this.importType = type;
    },
    // 导入电站、导入智能采集设备回调函数
    fileUpload (res) {
      if (this.importType == '1') {
        if (res == '操作成功') {
          this.$message.success('导入成功');
          this.clickSearch();
        } else {
          if (res.msg) {
            this.$message.warning(res.msg);
          }
          if (res.status == '0' && res.fileName && res.strBase64) {
            setTimeout(() => {
              this.$downloadFile({ fileName: res.fileName, fileBase64Code: res.strBase64 });
            }, 1500);
          }
        }
      } else {
        if (res.status == '1') {
          this.$message.success('导入成功');
          this.clickSearch();
        } else {
          this.$message.warning(res.msg);
          if (res.status == '0' && res.fileName && res.strBase64) {
            setTimeout(() => {
              this.$downloadFile({ fileName: res.fileName, fileBase64Code: res.strBase64 });
            }, 1500);
          }
        }
      }
    },
    // 根据权限标识和外部指示状态进行权限判断
    show (perms) {
      return this.showHandle(perms);
    },
    addStation () {
      this.$refs.stationAdd.init();
    },
    // 下载导入模板
    exportTemplate () {
      let templateName = this.importType == '1' ? '设备映射表.xlsx' : '智能采集硬件装置导入表.xlsx';
      this.loading = true;
      downloadTemplate({ templateName: templateName }).then(res => {
        this.$downloadFile(res.result_data);
        this.loading = false;
      }).catch(() => {
        this.loading = false;
      });
    }
  }
};
</script>
