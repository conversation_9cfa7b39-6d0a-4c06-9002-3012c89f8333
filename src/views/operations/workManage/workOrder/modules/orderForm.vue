<template>
  <!-- 缺陷工单表单 -->
  <div class="drawer-form-com">
    <div class="drawer-form-content">
      <a-spin :spinning="loading">
        <!-- 基本信息 -->
        <template v-if="['1','2','9'].includes(model.type)">
          <!-- 提交表单-缺陷登记 -->
          <register-form ref="editRegister" @change="changeEvent" :type="model.type" :dict="{...dict, ...dictMap}" />
        </template>
        <template v-else>
          <order-base :id="order.id" ref='orderBase' :isFarm="isFarm" :type="model.type" @updateOrder="updateOrder" />
          <!-- 指派 -->
          <template v-if="model.type == '5'">
            <a-form-model ref="sendForm" :model="sendform" :rules="rules" :labelCol="{ style:'width: 115px' }"
              :wrapperCol="{ style:'width: calc(100% - 115px)' }">
              <a-row :gutter="24">
                <a-col :xl='8' :sm='12' :xs='24'>
                  <a-form-model-item label="消除人" prop="eliminateId">
                    <a-select v-model="sendform.eliminateId" placeholder="请选择" style="width: 100%;">
                      <a-select-option v-for="item in users" :key="item.id" :value="item.id">{{item.realName}}
                      </a-select-option>
                    </a-select>
                  </a-form-model-item>
                </a-col>
              </a-row>
            </a-form-model>
          </template>
        </template>
        <div v-show="!['1','2','9'].includes(model.type)">
          <!-- 消缺方案 -->
          <div
            v-show="((order.orderStatus == '3' && model.type == '7') || order.active && (['4','5','6','7','8','9'].includes(order.orderStatus))) && order.isFarm!='1'">
            <order-eliminate ref="orderEliminate" :disabled="order.orderStatus != '3' || model.type == '3'" />
          </div>
          <!-- 处理-执行信息 -->
          <div
            v-show="(order.orderStatus == '6' && model.type == '7') || (order.resolveResult && ['6','7','8','9'].includes(order.orderStatus))">
            <order-dispose ref="orderDispose" :disabled="order.orderStatus != '6' || model.type =='3'" :isDiagnosis="order.orderSource" :isFarm="order.isFarm" />
          </div>
        </div>
      </a-spin>
    </div>
    <div @click="openFlowChart" class="flow-chart-btn">
      <svg-icon iconClass="flow"></svg-icon>
      流程图</div>
    <flow-chart-drawer v-if="showDiagram" ref="flowChartDrawer" :parentId="parentId" :processInstanceId="order.processInstanceId ?order.processInstanceId:processInstanceId" :flowUser="order.flowUser" :processDefinitionKey="order.isFarm=='1'?'户用_工单':'工单'" type="1"/>

    <!-- 查看详情 -->
    <div v-if="model.type == '3'" class="drawer-form-foot">
      <throttle-button label="返回" type="info" @click="cancel" class="solar-eye-btn-primary-line" />
    </div>
    <div v-else class="drawer-form-foot">
      <!-- 新增、编辑 -->
      <template v-if="['1','2','9'].includes(model.type)">
        <throttle-button v-show="model.type != '9'" label="保存" :loading="loading" @click="confirmFault('0')" />
        <throttle-button label="提交" :loading="loading" @click="confirmFault('1')" />
      </template>
      <!-- 指派、作废 -->
      <template v-else-if="model.type == '5'">
        <throttle-button label="指派" :loading="loading" @click="orderSend" />
      </template>
      <!-- 领取 -->
      <template v-else-if="model.type == '6'">
        <throttle-button label="领取" :loading="loading" @click="receiveOrderEven" />
        <throttle-button label="取消" :disabled="loading" type="info" @click="cancel" />
      </template>
      <!-- 工单处理/执行 -->
      <template v-else-if="['3','6'].includes(order.orderStatus)">
        <throttle-button label="退回" :loading="loading" @click="backNullifyEven('1')" />
        <throttle-button v-show="order.orderStatus == '6'" label="保存" :loading="loading"
          @click="confirmOrderResolve('0')" />
        <throttle-button label="提交" :loading="loading" @click="confirmOrderResolve('1')" />
      </template>
      <!-- 站内审核、区域审核、站内验收、区域验收 -->
      <template v-else-if="['4','5','7','8'].includes(order.orderStatus)">
        <throttle-button label="通过" :loading="loading" @click="checkOrder('1')" />
        <throttle-button label="驳回" :loading="loading" @click="checkOrder('0')" />
        <throttle-button label="取消" :disabled="loading" type="info" @click="cancel" />
      </template>
       <!-- 工单领取 -->
      <template v-else-if="model.type == '21'">
        <throttle-button label="领取" :loading="loading" @click="receiveOrderEven" />
      </template>
    </div>

    <!-- 派发 -->
    <order-send ref="orderSend" @refresh="cancel" />

    <!-- 站内审核、区域审核、站内验收、区域验收 -->
    <flow-review v-model="showAll" :visible="showAll" :type="examineType" @reviewFlow="reviewFlow" />

    <!-- 退回、作废 弹窗 -->
    <backNullify v-model="backNullifyShow" :loading="loading" :visible="backNullifyShow" :type="backNullifyType" @backNullify="backConfirm" />
  </div>
</template>

<script>
import moment from 'moment';
import orderBase from './orderBasic';
import registerForm from './register';
import initDict from '@/mixins/initDict';
import orderDispose from './orderDispose';
import orderEliminate from './orderEliminate';
import { getPsaId } from '@/api/monitor/runMonitor';
import flowReview from '@/components/erp/activiti/review';
import {
  insertOrUpdate,
  distributeUserListByPSId
} from '@/api/common_gy/faultManage.js';
import {
  handleOrder,
  faultDetail,
  cancelOrder
} from '@/api/common_gy/workOrder.js';
export default {
  name: 'orderForm',
  mixins: [initDict],
  components: {
    orderBase,
    flowReview,
    registerForm,
    orderDispose,
    orderEliminate
  },
  props: {
    dict: {
      type: Object,
      default: () => { return {}; }

    },
    /* 抽屉挂在的DOM节点ID，必要参数 */
    parentId: {
      type: String,
      required: true,
      default: ''
    }
  },
  data () {
    return {
      loading: false,
      kind: '1', // tabs页默认表单
      order: {
        id: undefined
      }, // 工单详情信息
      model: {
        show_model: false, // 是否显示模态框
        title: '缺陷管理新增',
        type: 1 // type 新增：1  编辑：2  详情: 3  审批: 4  指派: 5  领取: 6  处理: 7  验收: 8
      },
      users: [],
      sendform: {
        eliminateId: undefined // 消除人
      },
      rules: {
        eliminateId: [{
          required: true,
          message: '请选择消除人'
        }]
      },
      backNullifyShow: false,
      backNullifyType: null,
      // 审批、验收
      showAll: false,
      examineType: null,
      showDiagram: false,
      processInstanceId: '',
      isFarm: ''
    };
  },
  methods: {
    moment,
    // 初始化 type 新增：1  编辑：2  详情: 3  审批: 4  指派: 5  领取: 6  处理: 7  验收: 8  9:告警转工单
    init (type, row) {
      this.loading = true;
      if (!this.dict.hasOwnProperty('two_fault_classify') && !this.dictMap.hasOwnProperty('two_fault_classify')) {
        this.getDictMap('taskType,two_sts_task,has_elec_status,check_content,two_fault_classify,two_device_run_sts');
      }
      if (['3', '4', '5', '6', '7', '8', '21'].includes(type)) {
        Object.assign(this.order, row);
      }
      this.isFarm = row && row.isFarm ? row.isFarm : '';
      this.processInstanceId = this.order.processInstanceId;
      // this.model.show_model = true;
      this.model.type = type;
      this.row = row;
      switch (this.model.type) {
        case '1':
          this.setRegister(type);
          return '设备缺陷';
        case '2':
          this.getRegisterDetail(type, this.row);
          return '缺陷编辑';
        case '3':
          return '工单详情';
        case '4':
          return '工单审批';
        case '5':
          return '工单指派';
        case '6':
          return '工单领取';
        case '7':
          return '工单处理';
        case '8':
          return '工单验收';
        case '9':
          this.physicsPsId = row.physicsPsId;
          this.order.faultContentOptions = this.faultContentOptions = row.faultContentOptions;
          this.warningTurnDataReady(type, this.physicsPsId);
          return '告警转工单';
        case '21':
          return '工单领取';
      }
    },
    changeEvent (isFarm) {
      if (this.order.isFarm != isFarm) {
        this.showDiagram = false;
        this.order.isFarm = isFarm;
      }
    },
    // 打开流程图
    openFlowChart () {
      this.showDiagram = true;
      if (this.$refs.editRegister) {
        this.order.isFarm = this.$refs.editRegister.register.isFarm;
      }
      this.$nextTick(() => {
        this.$refs.flowChartDrawer.openView();
      });
    },
    // 缺陷登记设置
    setRegister (type, register) {
      let self = this;
      if (type == '1') {
        window.setTimeout(() => {
          self.$refs.editRegister.initRegister(null);
          self.loading = false;
        }, 50);
      } else {
        // self.$refs.editRegister.register.psId = register.psId
        self.$refs.editRegister.initRegister(register);
      }
    },
    // 获取工单详情信息
    getRegisterDetail (type, row) {
      let params = {
        id: row.id
      };
      faultDetail(params).then(res => {
        this.setRegister(type, res.result);
        this.order.isFarm = res.result.isFarm;
        this.loading = false;
      }).catch(() => {
        this.loading = false;
        this.cancel();
      });
    },
    // 告警转工单 获取发现人和电站档案匹配
    warningTurnDataReady (type, physicsPsId) {
      getPsaId({
        psId: physicsPsId
      }).then(res => {
        this.order.psId = (res.result_data.psaId).toString();
        this.setRegister(type, this.order);
        this.loading = false;
      }).catch(() => {
        this.setRegister(type, this.order);
        this.loading = false;
      });
    },
    // 更新order信息
    updateOrder (order) {
      if (order) {
        Object.assign(this.order, order);
        if (this.model.type == '5') {
          this.getUserList(order.psId);
        }
        // 消缺方案
        if (this.$refs.orderEliminate) {
          this.$refs.orderEliminate.updataEliminate(order);
        }
        // 执行信息
        if (this.$refs.orderDispose) {
          this.$refs.orderDispose.updataDispose(order);
        }
      }
      this.loading = false;
    },
    // 缺陷登记-新增、编辑  保存/提交
    confirmFault (type) {
      this.loading = true;
      this.$refs.editRegister.getValidate().then(params => {
        params.buttonFlag = type;
        insertOrUpdate(params).then((res) => {
          this.$message.success(res.message ? res.message : '操作成功');
          this.cancel(); // 监控中心告警转缺陷回调
        }).catch(() => {
          this.loading = false;
        });
      }).catch(() => {
        this.loading = false;
      });
    },
    // 指派
    orderSend () {
      this.$refs.sendForm.validate(valid => {
        if (valid) {
          let map = {
            id: this.order.id,
            orderStatus: this.order.orderStatus,
            eliminateId: this.sendform.eliminateId,
            taskId: this.order.taskId,
            processInstanceId: this.order.processInstanceId
          };
          if (this.order.orderSource == '4') {
            if (!this.$refs.orderBase.getValidate()) {
              return;
            }
            let obj = this.$refs.orderBase.form;
            map = Object.assign(map, {
              docs: obj.files,
              faultRemark: obj.faultRemark,
              deviceTypeName: obj.deviceTypeName,
              defectId: obj.defectId,
              orderSource: obj.orderSource,
              faultClassify: obj.faultClassify.toString(),
              deviceType: obj.deviceTypeList[obj.deviceTypeList.length - 1],
              orderStatus: obj.orderStatus,
              predictPlanTimeLimit: obj.predictPlanTimeLimit
            });
          }
          this.loading = true;
          handleOrder(map).then(res => {
            this.loading = false;
            this.$message.success(res.message ? res.message : '操作成功');
            this.cancel();
          }).catch(() => {
            this.loading = false;
          });
        } else {
          this.loading = false;
          return false;
        }
      });
    },
    // 获取指派人列表
    getUserList (psId) {
      distributeUserListByPSId({
        'psId': psId
      }).then(res => {
        this.users = res.result;
        this.loading = false;
      }).catch(() => {
        this.users = [];
        this.loading = false;
      });
    },
    // 显示退回、作废弹窗
    backNullifyEven (type) {
      this.backNullifyType = type;
      this.backNullifyShow = true;
    },
    // 退回、作废回调
    backConfirm (message) {
      if (this.backNullifyType == '1') {
        this.returnOrderEven(message);
      } else if (this.backNullifyType == '2') {
        this.cancelOrderEven(message);
      }
    },
    // 作废
    cancelOrderEven (message) {
      let that = this;
      this.$confirm({
        content: '确认作废此工单任务吗？',
        okText: '确定',
        cancelText: '取消',
        onOk () {
          that.loading = true;
          let map = {
            'id': that.order.id,
            'invalidationReason': message,
            'isFarm': that.order.isFarm
          };
          cancelOrder(map).then(res => {
            that.backNullifyType = null;
            that.backNullifyShow = false;
            that.$nextTick(() => {
              that.$message.success(res.message ? res.message : '操作成功');
              that.cancel();
            });
          }).catch(() => {
            that.loading = false;
          });
        },
        onCancel () {
          that.$destroyAll();
        }
      });
    },
    // 退回
    returnOrderEven (message) {
      this.loading = true;
      let map = {
        'id': this.order.id,
        'returnReason': message,
        'auditStatusProcess': '0',
        'taskId': this.order.taskId,
        'auditOpinionProcess': message,
        'orderStatus': this.order.orderStatus,
        'processInstanceId': this.order.processInstanceId
      };
      handleOrder(map).then(res => {
        this.backNullifyType = null;
        this.backNullifyShow = false;
        this.$nextTick(() => {
          this.$message.success(res.message ? res.message : '操作成功');
          this.cancel();
        });
      }).catch(() => {
        this.loading = false;
      });
    },
    // 领取
    receiveOrderEven () {
      let that = this;
      let map = {
        id: that.order.id,
        orderStatus: that.order.orderStatus,
        taskId: that.order.taskId,
        processInstanceId: that.order.processInstanceId
      };
      if (this.model.type == '6') {
        this.$confirm({
          content: '确认要领取此工单任务吗？',
          okText: '确定',
          cancelText: '取消',
          onOk () {
            that.loading = true;

            handleOrder(map).then(res => {
              that.$message.success(res.message ? res.message : '操作成功');
              that.cancel();
            }).catch(() => {
              that.loading = false;
            });
          },
          onCancel () {
            that.$destroyAll();
          }
        });
      } else {
        let obj = this.$refs.orderBase.form;
        if (!this.$refs.orderBase.getValidate()) {
          return;
        }
        that.loading = true;
        map = Object.assign(map, {
          docs: obj.files,
          faultRemark: obj.faultRemark,
          deviceTypeName: obj.deviceTypeName,
          defectId: obj.defectId,
          orderSource: obj.orderSource,
          faultClassify: obj.faultClassify.toString(),
          deviceType: obj.deviceTypeList[obj.deviceTypeList.length - 1],
          predictPlanTimeLimit: obj.predictPlanTimeLimit,
          orderStatus: obj.orderStatus
        });
        handleOrder(map).then(res => {
          that.$message.success('领取成功');
          this.cancel();
        }).catch(() => {
          that.loading = false;
        });
      }
    },
    // 方案上传或工单处理 status 3:方案上传  6:工单处理  type 0:保存  1:提交
    confirmOrderResolve (type) {
      let status = this.order.orderStatus;
      // 保存时不校验附件，反之
      if (type == 0) {
        Object.assign(this.$refs.orderDispose.rules, {
          resolveDocs: [{ required: false }],
          workContent: [{ required: false }],
          ticketText: [{ required: false }]
        });
      } else {
        Object.assign(this.$refs.orderDispose.rules, {
          resolveDocs: [{
            required: true,
            message: '请选择需要上传的文件',
            trigger: 'change'
          }],
          workContent: [{
            required: true,
            message: '请选择工作内容'
          }], // 工作内容
          ticketText: [{
            required: true,
            message: '当前工作内容需开具工作票'
          }] // 关联两票
        });
      }

      if (status == '3') {
        this.$refs.orderEliminate.saveEliminate().then(params => {
          let map = {
            'id': this.order.id,
            'orderStatus': this.order.orderStatus,
            'taskId': this.order.taskId,
            'processInstanceId': this.order.processInstanceId,
            ...params
          };
          this.loading = true;
          handleOrder(map).then(res => {
            this.loading = false;
            this.$message.success(res.message ? res.message : '操作成功');
            this.cancel();
          }).catch(() => {
            this.loading = false;
          });
        }).catch(() => {
          this.loading = false;
        });
      } else if (status == '6') {
        this.$refs.orderDispose.saveOrderDispose(type).then(params => {
          let map = {
            'id': this.order.id,
            'orderStatus': this.order.orderStatus,
            'taskId': this.order.taskId,
            'processInstanceId': this.order.processInstanceId,
            'type': type,
            'faultClassify': (['1', '2'].includes(this.order.faultClassify) ? '1' : '2'),
            ...params
          };
          this.loading = true;
          handleOrder(map).then(res => {
            this.loading = false;
            this.$message.success(res.message ? res.message : '操作成功');
            this.cancel();
          }).catch(() => {
            this.loading = false;
          });
        }).catch(() => {
          this.loading = false;
        });
      }
    },
    // 显示审批弹窗
    checkOrder (type) {
      this.examineType = type; // 1、通过 0、退回
      this.showAll = true;
    },
    // 站内审核、区域审核、站内验收、区域验收
    reviewFlow (message, type) {
      this.loading = true;
      let status = this.order.orderStatus;
      let sceneCondition = this.order.sceneCondition ? this.order.sceneCondition : '';
      if (['4', '5'].includes(status)) { // 审核
        let map = {
          'id': this.order.id,
          'back': (status == '5' ? '1' : '0'),
          'receiveFlag': type,
          'auditOpinion': message,
          'orderStatus': this.order.orderStatus,
          'taskId': this.order.taskId,
          'auditStatusProcess': type,
          'auditOpinionProcess': message,
          'processInstanceId': this.order.processInstanceId,
          sceneCondition: sceneCondition
        };
        this.auditOrder(map);
        return;
      }
      // 验收
      let map = {
        'id': this.order.id,
        'orderStatus': this.order.orderStatus,
        'taskId': this.order.taskId,
        'auditStatusProcess': type,
        'auditOpinionProcess': message,
        'processInstanceId': this.order.processInstanceId,
        'acceptanceStatus': (type == '1' ? '7' : '8'),
        'acceptanceSituation': message,
        sceneCondition: sceneCondition
      };
      if (status == '8') { // 区域验收
        map.acceptanceStatus = (type == '1' ? '9' : '10');
        map.areaAcceptRemark = message;
      }
      this.auditOrder(map);
    },
    // 审核、验收
    auditOrder (map) {
      handleOrder(map).then(res => {
        this.examineType = null;
        this.showAll = false;
        this.$nextTick(() => {
          this.$message.success(res.message ? res.message : '操作成功');
          this.cancel();
        });
      }).catch(() => {
        this.loading = false;
      });
    },
    // 取消按钮事件
    cancel () {
      this.$emit('cancel');
      this.reset();
    },
    /*
        数据重置
      */
    reset () {
      let dict = { ...this.dict, ...this.dictMap };
      if (this.$refs.editRegister) {
        this.$refs.editRegister.clearValidate();
      }
      if (this.$refs.orderDispose) {
        this.$refs.orderDispose.clearValidate();
      }
      Object.assign(this.$data, this.$options.data());
      this.dictMap = dict;
    }
  }
};
</script>

<style lang="less" scoped>
  :deep(.ant-form-item) {
    width: 100%;
    display: inline-flex;
  }
</style>
