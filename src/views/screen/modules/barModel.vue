<template>
  <div class="solar-eye-screen-bar">
    <div class="solar-eye-screen-title">{{title}}</div>
    <div class="bar-model" ref="barGap"></div>
  </div>
</template>
<script>
import { powerTrend } from '@/api/screen/screen';
export default {
  props: {
    legendName: {
      type: String,
      default: '已完成',
      required: true
    },
    title: {
      type: String,
      defalut: '',
      required: true
    }
  },
  methods: {
    initPlanFinishRate (echarts) {
      let self = this;
      powerTrend({}).then(res => {
        let x_data = [];
        let rate = [];
        if (Array.isArray(res.result_data)) {
          res.result_data.forEach(item => {
            x_data.push(item.reportMonth);
            rate.push(item.completionRate);
          });
        }
        self.initLineBar(echarts, x_data, rate, '%');
      }, () => {
        self.initLineBar(echarts, [], []);
      });
    },
    initLineBar (echarts, x_data, data, unit) {
      if (!this.$refs.barGap) { // dom 不存在时不画
        return;
      }
      // 不重复初始化
      let barGap = echarts.getInstanceByDom(this.$refs.barGap);
      if (!barGap) {
        barGap = echarts.init(this.$refs.barGap);
        this.$once('hook:beforeDestroy', function () {
          echarts.dispose(barGap);
          barGap = null;
        });
      }
      let option = {
        tooltip: {
          show: true,
          trigger: 'axis',
          axisPointer: {
            type: 'shadow',
            shadowStyle: {
              color: 'rgba(150,150,150,0.1)'
            }
          },
          backgroundColor: 'rgba(55, 71, 89, 0.9)',
          borderColor: '#69C7EB',
          borderWidth: '1',
          textStyle: {
            color: '#ffffff'
          },
          formatter: function (params) {
            var str = '';
            for (var i = 0; i < params.length; i++) {
              if (params[i].seriesName !== '') {
                str +=
                    params[i].name +
                    '<br/>' + params[i].marker + "<span style='margin-right:24px'>" +
                    params[i].seriesName + '</span>' +
                    params[i].value +
                    '<br/>';
              }
            }
            return str;
          }
        },
        legend: {
          icon: 'roundRect',
          top: '6%',
          // right: '10',
          itemWidth: 10,
          itemHeight: 10,
          textStyle: {
            textAlign: 'center',
            fontSize: 14,
            color: 'rgba(255,255,255,0.7)'
          },
          data: this.legendName.split(',')
        },
        grid: {
          x: 10,
          y: 60,
          x2: 10,
          y2: 10,
          containLabel: true
        },
        xAxis: [{
          type: 'category',
          data: x_data,
          axisPointer: {
            type: 'shadow'
          },
          axisLine: {
            show: false,
            lineStyle: {
              color: 'rgba(255, 255, 255, 0.7)',
              fontSize: 12
            }
          },
          axisTick: false
        }],
        yAxis: [{
          name: unit,
          splitLine: {
            show: true,
            lineStyle: {
              color: 'rgba(96, 130, 163, 0.19)'
            }
          },
          axisLine: {
            show: false,
            lineStyle: {
              color: 'rgba(255, 255, 255, 0.7)',
              fontSize: 12
            }
          },
          minInterval: 1,
          min: function (value) {
            if (isNaN(value.min) || value.min === Infinity) {
              return 0;
            }
          },
          max: function (value) {
            if (isNaN(value.max) || value.max === -Infinity) {
              return 100;
            }
          },
          axisTick: false
        }],
        dataZoom: [{
          type: 'inside',
          show: false
        }],
        series: [{
          name: this.legendName,
          data: data,
          type: 'bar',
          // stack:'已完成',
          barMaxWidth: 20,
          itemStyle: {
            color: {
              x: 0,
              y: 0,
              x2: 0,
              y2: 1,
              type: 'linear',
              global: false,
              colorStops: [{
                offset: 0,
                color: '#27C6C8'
              }, {
                offset: 1,
                color: '#158ac6'
              } ]
            }
          },
          label: {
            show: false,
            align: 'center',
            position: 'outside',
            distance: 8,
            verticalAlign: 'bottom',
            color: '#fff'
          }
        }]
      };
      barGap.setOption(option, true);
    },
    getStackList (arr) {
      let filterArr = arr.filter(item => {
        return item != '--';
      });
      let maxNum = Math.max(...filterArr);
      let stackData = new Array(arr.length);
      arr.forEach((element, index) => {
        if (element === '--' || element == '0.0') {
          stackData[index] = '--';
        } else {
          stackData[index] = maxNum * 0.04;
        }
      });
      return stackData;
    }
  }
};
</script>

<style lang="less" scoped>
  .bar-model {
    width: 100%;
    height: 85%;
  }
</style>
