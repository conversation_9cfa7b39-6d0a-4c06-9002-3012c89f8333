<template>
  <div class="configuration">
    <configuration-menu :routePath="routePath"></configuration-menu>
  </div>
</template>

<script>
import configurationMenu from '@/components/com/configurationMenu';
export default {
  name: 'serviceConfiguration',
  components: {
    configurationMenu
  },
  data () {
    return {
      routePath: '/serviceConfiguration'
    };
  },
  created () {},
  methods: {}
};
</script>

<style lang="less" scoped>
  .configuration {
    width: 100%;
    height: 100%;
    overflow-x: hidden;
    overflow-y: auto;
  }
</style>
