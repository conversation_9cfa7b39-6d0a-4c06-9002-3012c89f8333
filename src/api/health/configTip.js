const tip = {
  obj: '',
  valitate: (name) => {
    return function (rule, value, callback) {
      if (name === '启停异常值约束阈值') {
        if (/^[0-9]+([.]{1}[0-9]{1})?$/.test(value) && Number(value) <= 2 && Number(value) >= 0.5) {
          callback();
        } else {
          callback(new Error('启停异常值约束阈值只允许输入0.5~2的数值'));
        }
      }
      if (name === '死值约束阈值') {
        if (/^\d*$/.test(value) && Number(value) <= 25 && Number(value) >= 7) {
          callback();
        } else {
          callback(new Error('死值约束阈值只允许输入7~25的数值'));
        }
      }
      if (name === '虚假值约束阈值') {
        if (/^\d*$/.test(value) && Number(value) <= 25 && Number(value) >= 7) {
          callback();
        } else {
          callback(new Error('虚假值约束阈值只允许输入7~25的数值'));
        }
      }
      if (name === '问题组串占比阈值') {
        if (/^[0-9]+([.]{1}[0-9]{1,3})?$/.test(value) && Number(value) <= 1 && Number(value) >= 0) {
          callback();
        } else {
          callback(new Error('问题组串占比阈值只允许输入0~1的数值'));
        }
      }
      if (name === '典型日二阶差分值阈值') {
        if (/^[0-9]+([.]{1}[0-9]{1,3})?$/.test(value) && Number(value) <= 0.3 && Number(value) >= 0) {
          callback();
        } else {
          callback(new Error('典型日二阶差分值阈值只允许输入0~0.3的数值'));
        }
      }
      if (name === '等效小时数阈值') {
        if (/^[0-9]+([.]{1}[0-9]{1})?$/.test(value) && Number(value) <= 5 && Number(value) >= 0.5) {
          callback();
        } else {
          callback(new Error('等效小时数阈值只允许输入0.5~5的数值'));
        }
      }
      if (name === '逆变器有功功率波动率阈值') {
        if (/^[0-9]+([.]{1}[0-9]{1,3})?$/.test(value) && Number(value) <= 0.1 && Number(value) >= 0) {
          callback();
        } else {
          callback(new Error('逆变器有功功率波动率阈值只允许输入0~0.1的数值'));
        }
      }
      if (name === '逆变器有功功率波动率时长阈值') {
        if (/^[1-9]*$/.test(value) && Number(value) <= 25 && Number(value) >= 7) {
          callback();
        } else {
          callback(new Error('逆变器有功功率波动率时长阈值只允许输入7~25的数值'));
        }
      }
      if (name === '频繁启停约束阈值') {
        if (/^[1-9]*$/.test(value) && Number(value) <= 10 && Number(value) >= 1) {
          callback();
        } else {
          callback(new Error('频繁启停约束阈值只允许输入1~10的数值'));
        }
      }
      if (name === '电流最大值时刻跌幅') {
        if (/^[0-9]+([.]{1}[0-9]{1,2})?$/.test(value) && Number(value) <= 0.5 && Number(value) >= 0) {
          callback();
        } else {
          callback(new Error('电流最大值时刻跌幅只允许输入0~0.5的数值'));
        }
      }
      if (name === '遮挡损失率') {
        if (/^[0-9]+([.]{1}[0-9]{1,2})?$/.test(value) && Number(value) <= 0.5 && Number(value) >= 0) {
          callback();
        } else {
          callback(new Error('遮挡损失率只允许输入0~0.5的数值'));
        }
      }
      if (name === '遮挡时长') {
        if (/^[0-9]*$/.test(value) && Number(value) <= 25 && Number(value) >= 0) {
          callback();
        } else {
          callback(new Error('遮挡时长只允许输入0~25的数值'));
        }
      }
      if (name === '低效损失率') {
        if (/^[0-9]+([.]{1}[0-9]{1,2})?$/.test(value) && Number(value) <= 0.5 && Number(value) >= 0) {
          callback();
        } else {
          callback(new Error('低效损失率只允许输入0~0.5的数值'));
        }
      }
      if (name === '逆变器电流离散率阈值') {
        if (/^[0-9]+([.]{1}[0-9]{1,2})?$/.test(value) && Number(value) <= 1 && Number(value) >= 0) {
          callback();
        } else {
          callback(new Error('逆变器电流离散率阈值只允许输入0~1的数值'));
        }
      }
      if (name === '距离和差异值阈值') {
        if (/^[0-9]+([.]{1}[0-9]{1})?$/.test(value) && Number(value) <= 1000 && Number(value) >= 0) {
          callback();
        } else {
          callback(new Error('距离和差异值阈值只允许输入0~1000的数值'));
        }
      }
      if (name === '逆变器三相电流不平衡度') {
        if (/^(0.[\d]{0,3}|0|1)$/.test(value)) {
          callback();
        } else {
          callback(new Error('逆变器三相电流不平衡度只允许输入0~1的数值'));
        }
      }
      callback();
    };
  }
};
export default tip;
