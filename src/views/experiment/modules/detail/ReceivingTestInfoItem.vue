<template>
  <div>
    <a-row  class="device-box" style="padding: 0 0 0 16px">
      <a-col :span="7" class="detail_layout_content">
        <span class="left" style="width: 160px">设备类型{{testInfoIndex+1}}-{{deviceIndex+1}}</span>
        <span class="right">{{deviceItem.deviceTypeName || '--'}}</span>
      </a-col>
      <a-col :span="7" class="detail_layout_content" >
        <span class="left" style="width: 160px">数量</span>
        <span class="right">{{deviceItem.deviceAmount || '--'}}</span>
      </a-col>
      <a-col :span="7" class="detail_layout_content" >
        <span class="left" style="width: 160px">试验项目</span>
        <span class="right">{{deviceItem.projectName || '--'}}</span>
      </a-col>
      <a-col :span='3' class='flex-end'>
        <span @click='toggle' style='cursor: pointer'>
           {{show?'收起':'展开'}}
          <svg-icon icon-class='up' :style='`transform: rotate(${show?0:180}deg)`'/>
        </span>
      </a-col>
      <a-col v-show='show' :span='24' v-for='(testProjectItem,index) in  deviceItem.testProjectList' :key='index'  class='table-config-box'>
        <div class='project-title'>
          <span class="left" style="width: 160px">{{index+1}}.试验项目</span>
          <span class="right">{{testProjectItem.projectNameRealName}}</span>
        </div>
        <TableItem
          style='width: 60%'
          v-for='(rowTableItem, rowTableItemIndex) in testProjectItem.tableConfigDtoList'
          :key='rowTableItemIndex'
          :row-list='rowTableItem.projectTitleList.filter(j=>j.titleType===1)'
          :col-list='rowTableItem.projectTitleList.filter(j=>j.titleType===2)'
          :project-table-remark='rowTableItem.projectTableRemark'
          :is-have-sub-title='rowTableItem.isHaveSubTitle'
        />
      </a-col>
    </a-row>
  </div>
</template>

<script>
import TableItem from './TableItem.vue';

export default {
  name: 'ReceivingTestInfoItem.vue',
  components: { TableItem },
  props: {
    deviceItem: {
      type: Object
    },
    testInfoIndex: {
      type: Number
    },
    deviceIndex: {
      type: Number
    }

  },
  data () {
    return {
      show: true
    };
  },
  methods: {
    toggle () {
      this.show = !this.show;
    }
  }
};
</script>

<style scoped lang='less'>
:deep(.table-config-box){
  margin-left: 24px;
  background: #F7F7F7;
  padding: 12px 12px 12px 36px;
  border-radius: 3px;
  .project-title{
    margin-bottom: 12px;
    .left{
      color: #666;
      font-weight: 550;
      -webkit-box-flex: 0;
      -ms-flex: none;
      flex: none;
    }
    .left::after{
      content: "\FF1A";
    }
  }

}
:deep(.detail_layout_content){
  line-height:36px;
}

:root[data-theme='dark'] {
  .device-box{
    .table-config-box{
      background: #162133;
    }
  }
}
</style>
