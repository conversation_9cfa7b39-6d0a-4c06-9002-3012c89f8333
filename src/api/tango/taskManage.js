import { axios } from '@/utils/erp-request';
const url = process.env.VUE_APP_TANGO_BASE_URL;
const taskUrl = process.env.VUE_APP_API_ERP_URL;
// 获取检测工程师
export function getThirdAssgin (data) {
  return axios({
    url: url + '/task/getThirdAssgin',
    method: 'post',
    data
  });
}
// 获取无人机型号
export function getUavModels (data) {
  return axios({
    url: url + '/task/getUavModels',
    method: 'post',
    data
  });
}
// 无人机-获取飞手
export function getAssignor (data) {
  return axios({
    url: url + '/task/getAssignor',
    method: 'post',
    data
  });
}
// 查询电站是否建模
export function checkHasMode (data) {
  return axios({
    url: taskUrl + '/pdca/care/workOrder/checkHasMode',
    method: 'post',
    data
  });
}
