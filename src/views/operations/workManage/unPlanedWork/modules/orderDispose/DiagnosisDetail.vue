<template>
  <!-- 提交表单 -->
  <detail-layout :labelList="diagnosis" :form="order()" title="执行信息">
    <template v-slot:stepList="baseform" v-if="order().stepList.length">
      <a-col :span="24" class="step-content">
        <div class="title-box">
          <span class="before"></span>
          <span>作业步骤</span>
        </div>
        <a-steps v-model="current" direction="vertical">
          <a-step :key="step.title" v-for="step in order().stepList">
            <template slot="title">
              {{step.stepName}}
              <throttle-button v-if="step.uploadExampleFileList && step.uploadExampleFileList.length > 0" @click="handlePreview(step.uploadExampleFileList)"
                label="示例图片" class="example-btn"></throttle-button>
            </template>
            <template slot="description">
              <a-col :span="24" v-if="step.stepContent && step.stepContent.indexOf('1') > -1">
                <div class="line_height">
                  <div class="left">情况描述：</div>
                  <div class="right">{{ step.workDesc }}</div>
                </div>
              </a-col>
              <a-col :span="24" v-if="step.stepContent && step.stepContent.indexOf('2') > -1">
                <div class="line_height">
                  <div class="left">上传图片：</div>
                  <div class="right">
                    <uploadFileView v-model="step.uploadPictureList" listType="picture-card" :zoom="0.8"
                      disabled accept=".jpg,.png,.jpeg,.bmp,.JPG,.PNG,.JPEG,.BMP" tip="最多上传5张图片，单张图片不超10MB，支持格式jpg/jpeg/png/bmp！" />
                  </div>
                </div>
              </a-col>
              <a-col :span="24" v-if="step.stepContent && step.stepContent.indexOf('3') > -1">
                <div class="line_height">
                  <div class="left">上传附件：</div>
                  <div div="right">
                    <uploadFileView v-model="step.uploadFileList" tip="最多上传5个文件,且上传的附件最大不超过10MB!"
                      disabled />
                  </div>
                </div>
              </a-col>
            </template>
          </a-step>
        </a-steps>
      </a-col>
    </template>
  </detail-layout>
</template>

<script>
import initDict from '@/mixins/initDict';
import { orderDispose } from './mixins';
import { Diagnosis } from './Dispose';
export default {
  name: 'base',
  inject: ['order'],
  mixins: [initDict, orderDispose],
  data () {
    return {
      diagnosis: Diagnosis
    };
  },
  create () {
    this.getDictMap('farm_scene_condition');
    let order = this.order();
    if (!this.getStatus() && order.stepList.length == 0) {
      this.getStepList();
    }
  },
  methods: {}
};
</script>

<style lang="less" scoped>
  :deep(.ant-form-item) {
    width: 100%;
    display: inline-flex;
  }

  :deep(.ant-form-item-label) {
    width: 74px;
  }

  :deep(.ant-form-item-control) {
    max-width: 800px;
  }

  .example-btn {
    font-size: 12px !important;
    padding: 0 8px !important;
    height: 22px !important;
    margin-left: 16px;
  }

  .line_height {
    display: flex;
    padding-bottom: 16px;

    .left {
      width: 124px;
      text-align: right;
      margin-right: 8px;
      font-weight: 600;
      color: #666;
    }

    .right {
      color: rgba(0, 0, 0, 0.65)
    }
  }

  .file-upload {
    :deep(.ant-upload-list-item) {
      margin-top: 0 !important;
      margin-bottom: 8px !important;
    }
  }

  .step-content {
    margin-top: 12px;
    margin-left: -4px;
    padding: 0 !important;
  }

  :deep(.ant-steps-vertical .ant-steps-item-description) {
    padding: 16px 0 0 !important;
  }

  :root[data-theme='dark'] {
    .line_height .left {
      color: #fff;
    }

    .line_height .right {
      color: #fff;
    }
  }
</style>
