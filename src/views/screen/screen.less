.screen-page {
    -webkit-touch-callout: none;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
    position: absolute;
    z-index: 99;
    // width: 100%;
    // height: 100%;
    width: 100vw;
    height: 100vh;
    left: 0;
    top: 0;
    background: #09152a;
    overflow: hidden;
    z-index: 200;

    :deep(.ant-spin-nested-loading>div>.ant-spin) {
      max-height: 100vh;
    }

    :deep(.ant-spin-nested-loading) {
      width: 100%;
      height: 100%;
    }

    :deep(.ant-spin-container) {
      width: 100%;
      height: 100%;
    }

    .all-elec-power {
      position: absolute;
      left: 2.5vw;
      top: 15.185vh;
      z-index: 200;
      font-family: Arial-<PERSON>, Arial;
      display: flex;
      align-items: center;

      .data {
        font-size: 2.5vw;
        font-weight: 900;
        line-height: 3.4375vw;
        font-family: <PERSON><PERSON>-<PERSON>, <PERSON><PERSON>;
      }

      .all-elec {
        color: rgba(248, 143, 57, 1);
      }

      .all-power {
        color: rgba(89, 206, 255, 1);
        margin-left: 24px;
      }

      .unit {
        color: rgba(255, 255, 255, 1);
        font-size: 0.9375vw;
        font-family: HYQiHei-FES, HYQiHei;
        font-weight: normal;
        color: #FFFFFF;
        line-height: 0.9375vw;
      }
    }

    .solar_eye_logo {
      width: 21.5vw;
      height: 9.6vh;
      position: absolute;
      left: 0;
      padding-left: 2.5vw;
      top: 3.33vh;
      z-index: 200;
      background: linear-gradient(270deg, rgba(255, 148, 61, 0) 0%, #FF943D 100%);
      display: flex;
      align-items: center;
      &:hover {
        cursor: pointer;
      }

      .left-icon-group {
        display: flex;
        align-items: center;
        height: 50px;
        overflow: hidden;
        padding-left: 15px;
      }

      @keyframes fdsseq {
        0% {
          opacity: 1;
        }

        100% {
          opacity: 0;
        }
      }

      .left-icon {
        width: 50px;
        height: 50px;
        border: 0;
        border-bottom: 8px solid;
        border-left: 8px solid;
        margin-right: -20px;
        transform: rotate(45deg);
      }

      .left-icon {
        animation-name: fdsseq;
        animation-duration: 0.8s;
        animation-iteration-count: infinite;
      }

      .left-icon-group .left-icon:nth-child(1) {
        animation-delay: 0.4s;
      }

      .left-icon-group .left-icon:nth-child(2) {
        animation-delay: 0.2s;
      }

      .left-icon-group .left-icon:nth-child(3) {
        animation-delay: 0s;
      }

      .screen-logo {
        margin: auto;
        width: 11.67vw;
        height: 4.44vh;
        cursor: pointer;
        background: url(../../assets/images/screen/logo.png) no-repeat;
        background-size: 100% 100%;
      }
    }

    .right-time-model {
      height: 9.6vh;
      position: absolute;
      right: 1.6vw;
      top: 3.33vh;
      z-index: 200;
      text-align: center;
      padding-top: 1.5vh;

      .now-time {
        font-size: 1.46vw;
        font-family: HYQiHei-FES, HYQiHei;
        font-weight: normal;
        color: #D8D8D8;
        line-height: 1.46vw;
        -webkit-background-clip: text;
      }

      .now-day {
        font-size: 1vw;
        font-family: HYQiHei-FES, HYQiHei;
        font-weight: normal;
        color: #D8D8D8;
        line-height: 1vw;
        -webkit-background-clip: text;
        opacity: 0.5;
      }
    }

    .amap-model {
      width: 100%;
      height: 100%;
      position: relative;
      background: #09152a;
    }

    .amap-div {
      width: 100%;
      height: 100%;
      background: #09152a;
      // visibility: hidden;
    }

    .search-model {
      position: absolute;
      bottom: 1.5vh;
      left: 2.5vw;
      z-index: 200;
      background: unset;

      :deep(.screen-select .ant-select-arrow-icon) {
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        color: rgba(255, 255, 255, 0.8);
        font-size: 0.83vw;
      }

      :deep(.screen-select) {
        background: transparent;
        border-radius: 40px;
        border: 1px solid #59CEFF;
        color: #FFFFFF;
        background: #202A35;
        opacity: 0.4;

        .ant-select-selection__clear {
          background: transparent;
          color: #fff;
        }
      }

      :deep(.ant-select-selection) {
        border: 0 !important;
        background: unset;
        box-shadow: unset;
      }
    }

    .refresh-list {
      position: flex;
      left: 0;
      top: 0;
      z-index: 299;
      width: 200px;
      min-height: 100px;
      background: antiquewhite;
    }

    :deep(.amap-markers) {
      color:rgba(0, 0, 0, .6)
    }

    .all-preview {
      position: absolute;
      z-index: 200;
      right: 1.67vw;
      bottom: 2.96vh;

      :deep(.ant-row) {
        margin-bottom: 12px;
      }

      :deep(.ant-col) {
        padding-right: 0 !important;
      }

      .all-preview-com {
        width: 20vw;
        height: 27vh;
        border-radius: 4px;
        padding: 0px 10px 0 8px;
        background: linear-gradient(180deg, #0E1820 0%, #1B242D 100%);
        border: 1px solid #51637C;
      }
    }
  }

  .bg-filter {
    width: 20vw;
    height: 27vh;
    background: #212D3A;
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    filter: blur(10px);
  }

  .psa-detail-info {
    position: absolute;
    width: 48vw;
    max-height: 86.5vh;
    right: 1.67vw;
    bottom: 2.5vh;
    z-index: 200;
    visibility: hidden;
  }
  .amap-marker-label {
    background: rgba(0, 0, 0, 0.1);
    padding: 12px;
    color: #FFFFFF;
    font-size: 14px;
    border: 0;
  }

  .info-window-content {
    max-height: 30vh;
    overflow: auto;
    background: linear-gradient(180deg, #0E1820 0%, #1B242D 100%);
    border: 1px solid #51637C;
    padding: 12px;
    border-radius: 4px;
    color: #fff;
  }

  .info-window-content .psa-name {
    cursor: pointer;
  }

  /* 更改高德地图聚合图标样式 */
  .render-cluster-marker {
    background: radial-gradient(rgba(0, 0, 0, 0) 0%, rgba(0, 0, 0, 0.1) 10%, rgba(8, 173, 235, 0.3) 100%);
    text-align: center;
    font-size: 14px;
    color: #40C7FF;
    animation: shadow 3s linear infinite;
  }

  @keyframes shadow {
    0% {
      box-shadow: 0px 0px 0px rgba(8, 173, 235, 0);
    }

    50% {
      box-shadow: 0px 0px 20px rgba(8, 173, 235, 0.3);
    }

    100% {
      box-shadow: 0px 0px 0px rgba(8, 173, 235, 0);
    }
  }

  .effect-scatter {
    background: #212D3A;
    opacity: 0.79;
    border: 1px solid #3C96BC;
    border-radius: 4px;
    padding: 7px 15px 8px 15px;
  }

  .effect-scatter-top {
    width: 100%;
    text-align: center;
    position: relative;
  }

  .effect-scatter-name {
    display: initial;
    margin: auto;
    min-width: 30px;
    font-size: 16px;
    font-family: PingFangSC-Regular, PingFang SC;
    font-weight: 400;
    color: #59CEFF;
    line-height: 22px;
    padding: 0 6px;
    background: #212D3A;
  }

  .effect-scatter-left {
    position: absolute;
    left: 0;
    top: 10.5px;
    width: calc(50% - 15px);
    height: 2px;
    background: linear-gradient(270deg, #59CEFF 0%, rgba(89, 206, 255, 0) 100%);
    opacity: 0.6;
    z-index: -1;
  }

  .effect-scatter-right {
    position: absolute;
    right: 0;
    top: 10.5px;
    width: calc(50% - 15px);
    height: 2px;
    background: linear-gradient(270deg, rgba(89, 206, 255, 0) 0%, #59CEFF 100%);
    opacity: 0.6;
    z-index: -1;
  }

  .effect-scatter-center {
    display: flex;
    vertical-align: initial;
    align-items: center;
  }

  .type-wind,
  .type-pv,
  .type-storage {
    width: 18px;
    height: 18px;
  }

  .type-wind {
    background: url(../../assets/images/screen/wind.png) no-repeat;
    background-size: 100% 100%;
  }

  .type-pv {
    background: url(../../assets/images/screen/pv.png) no-repeat;
    background-size: 100% 100%;
  }

  .type-storage {
    background: url(../../assets/images/screen/storage.png) no-repeat;
    background-size: 100% 100%;
  }

  .psa-num {
    margin: 0 24px 0 8px;
    font-size: 16px;
    font-family: PingFangSC-Medium, PingFang SC;
    font-weight: 500;
    color: #FFFFFF;
    line-height: 33px;
  }

  .last-data {
    margin-right: 0;
  }

  .hide {
    display: none !important;
  }

  .show {
    display: block !important;
  }

  .min-z-index {
    z-index: 0;
  }

  .max-z-index {
    z-index: 120;
  }

  @keyframes wind {
    0% {
      transform: rotate(0deg);
    }

    100% {
      transform: rotate(360deg);
    }
  }

  .station-com {
    min-height: 28px;
    min-width: 28px;
  }

  .station-no {
    filter: brightness(0.5);
    cursor: default;
  }

  .wind {
    height: 32px;
    width: 32px;
    background: url(../../assets/images/screen/wind.png);
    animation: wind 8s linear infinite;
    background-size: 100% 100%;
  }

  .storage {
    height: 28px;
    width: 36px;
    position: relative;
    display: flex;
    background: url(../../assets/images/screen/storage.png) no-repeat;
    background-size: 100% 100%;
  }

  .storage-bolt {
    margin: auto;
    transform: skewX(-10deg);
    animation: lightning-anim 4s linear infinite;
  }

  .charge-pv-gif {
    width: 30px;
    height: 30px;
  }

  .storage-bolt::before {
    content: "";
    position: absolute;
    margin-top: -9px;
    margin-left: -6.5px;
    border-top: 6px solid transparent;
    border-right: 3px solid yellow;
    border-bottom: 6px solid yellow;
    border-left: 3px solid transparent;
  }

  .storage-bolt::after {
    content: "";
    position: absolute;
    margin-left: -0.5px;
    margin-top: -1px;
    border-top: 6px solid yellow;
    border-right: 3px solid transparent;
    border-bottom: 6px solid transparent;
    border-left: 3px solid yellow;
  }

  @keyframes lightning-anim {
    0% {
      opacity: 0;
    }

    15% {
      opacity: 1;
    }

    25% {
      opacity: 0;
    }

    35% {
      opacity: 1;
    }

    100% {
      opacity: 0;
    }
  }

  .pv {
    height: 24px;
    width: 24px;
    position: relative;
    background: url(../../assets/images/screen/pv.png) no-repeat;
    background-size: 100% 100%;
  }

  .pv-img {
    opacity: 0.9;
    position: absolute;
    right: -2px;
    top: -5px;
    height: 18px;
    width: 18px;
    background: url(../../assets/images/weather/W0.png) no-repeat;
    background-size: 100% 100%;
    animation: wind 8s linear infinite;
  }

  @-webkit-keyframes breath {
    0% {
      opacity: 0.2;
    }

    50% {
      opacity: 1;
    }

    100% {
      opacity: 0.2;
    }
  }

  @keyframes breath {
    0% {
      opacity: 0.2;
    }

    50% {
      opacity: 1;
    }

    100% {
      opacity: 0.2;
    }
  }

  .marker-active {
    background-color: #CF1322;
  }

  .amap-logo {
    display: none !important;
  }