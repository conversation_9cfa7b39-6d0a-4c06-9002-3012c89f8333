export default {
  data () {
    return {
      lastCardHeight: '80vh'
    };
  },
  computed: {
    scroll: function () {
      var width = window.innerWidth;
      let $antTable = window.document.getElementsByClassName('ant-row');
      if ($antTable[0]) {
        width = $antTable[0].clientWidth;
      }
      return {
        // x:'max-content',
        x: width,
        y: this.lastCardHeight - 160
      };
    },
    innerHeight: function () {
      var innerHeight = window.innerHeight;
      return innerHeight;
    }
  },
  mounted () {
    this.getHeight();
  },
  methods: {
    getFixHeight () {
      let height = window.innerHeight - 60 - 40 - 24;
      return height > 700 ? (window.innerHeight - 60 - 40 - 24) : 800;
    },
    getHeight (height = 184) {
      if (this.moreShow) {
        this.lastCardHeight = this.getFixHeight() - height - 16;
      } else {
        this.lastCardHeight = this.getFixHeight() - 128 - 16;
      }
    }
  },
  watch: {
    'moreShow' () {
      this.getHeight();
    }
  }
};
