// 工单列表column
export const orderColumn = [
  { title: '区域', key: 'areaName', width: 160 },
  { title: '电站名称', key: 'psaName', width: 180 },
  { title: '工单编号', key: 'workCode', width: 160 },
  { title: '工单来源', key: 'orderSourceName', width: 160 },
  { title: '任务类型', key: 'taskTypeName' },
  { title: '任务子类', key: 'workSubclassName', width: 160 },
  { title: '任务名称', key: 'taskName', width: 180 },
  { title: '任务描述', key: 'taskDescription', width: 180 },
  { title: '责任人', key: 'liablePersonName' },
  { title: '工作状态', key: 'workStatusName' },
  { title: '计划开始时间', key: 'planStartTime' },
  { title: '计划结束时间', key: 'planEndTime' },
  { title: '发现时间', key: 'findTime' },
  { title: '预计消除时间', key: 'predictRecoverTime' },
  { title: '派发/领取时间', key: 'realStartTime' },
  { title: '执行提交时间', key: 'realEndTime' },
  { title: '关联计划/非计划', key: 'planCode' },
  { title: '流程状态', key: 'flowStsName' },
  { title: '当前环节', key: 'flowUser' },
  { title: '流程开始时间', key: 'flowStartTime' },
  { title: '流程结束时间', key: 'flowEndTime' },
  { title: '作废原因', key: 'cancelReason' },
  { title: '创建人', key: 'createUserName' },
  { title: '创建时间', key: 'createTime' }
];

export const orderCheck = ['areaName', 'psaName', 'workCode', 'orderSource', 'taskType', 'taskTypeName', 'taskDescription',
  'liablePersonName', 'workStatusName', 'planStartTime', 'planEndTime', 'taskName', 'flowStsName',
  'flowUser', 'planCode', 'realStartTime', 'realEndTime', 'flowStartTime', 'flowEndTime',
  'createUserName', 'createTime', 'orderSourceName', 'workSubclass', 'workSubclassName',
  'isOutLiable', 'findTime', 'predictRecoverTime', 'cancelReason'
];

export const unPlanedColumn = [
  { title: '区域', key: 'areaName', width: 160 },
  { title: '电站名称', key: 'psaName', width: 180 },
  { title: '工作编号', key: 'workCode', width: 160 },
  { title: '任务类型', key: 'taskTypeName' },
  { title: '任务子类', key: 'workSubclassName', width: 180 },
  { title: '任务名称', key: 'taskName', width: 180 },
  { title: '任务描述', key: 'taskDescription', width: 180 },
  { title: '责任人', key: 'liablePersonName' },
  { title: '工作状态', key: 'workStatusName' },
  { title: '计划开始时间', key: 'planStartTime' },
  { title: '计划结束时间', key: 'planEndTime' },
  { title: '发现时间', key: 'findTime' },
  { title: '预计消除时间', key: 'predictRecoverTime' },
  { title: '流程状态', key: 'flowStsName' },
  { title: '当前环节', key: 'flowUser' },
  { title: '流程开始时间', key: 'flowStartTime' },
  { title: '流程结束时间', key: 'flowEndTime' },
  { title: '创建人', key: 'createUserName' },
  { title: '创建时间', key: 'createTime' }
];

export const unPlanedCheck = ['areaName', 'psaName', 'workCode', 'taskTypeName', 'taskDescription',
  'liablePersonName', 'workStatusName', 'planStartTime', 'planEndTime', 'taskName', 'flowStsName',
  'flowUser', 'flowStartTime', 'flowEndTime', 'createUserName', 'createTime', 'workSubclassName',
  'isOutLiable', 'findTime', 'predictRecoverTime'
];

export function getSubDict (value, obj) {
  let subDict = [];
  let dictMap = obj;
  switch (value) {
    case '1': // 清洗
      subDict = dictMap.pdca_sub_task_type_clean;
      break;
    case '2': // 除草
      subDict = dictMap.pdca_sub_task_type_grass;
      break;
    case '3': // 实验
      subDict = dictMap.pdca_sub_task_type;
      break;
    case '5': // 检修
      subDict = dictMap.pdca_overhaul_category;
      break;
    case '7': // 故障 // 缺陷
    case '8':
      subDict = dictMap.two_fault_classify;
      break;
    case '10': // 巡检
      subDict = dictMap.pdca_sub_task_type_inspection;
      break;
    default:
      subDict = [];
  }
  return subDict;
}
