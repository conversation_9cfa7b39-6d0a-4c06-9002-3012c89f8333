<template>
    <a-tooltip overlayClassName="custom-tooltip-title-style">
      <template #title v-if="toggleShow">
        <div v-html='computedList'></div>
      </template>
      <div class="content" v-html='computedList'/>
   </a-tooltip>
</template>

<script>
import PropTypes from 'ant-design-vue/es/_util/vue-types';
export default {
  name: 'CheckContent',
  props: {
    list: PropTypes.array,
    index: PropTypes.bool
  },
  data () {
    return {
      toggleStatus: false,
      toggleShow: false
    };
  },
  mounted () {
    setTimeout(() => {
      const el = document.querySelectorAll('.content')[this.index];
      this.toggleShow = el.scrollHeight > el.clientHeight;
    });
  },
  computed: {

    computedList () {
      return this.list.map((v, k) => {
        return `${k + 1}. ${v}`;
      }).join('<br/>');
    }
  }
};
</script>

<style lang="less" scoped>
.content {
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp:5;
  overflow: hidden;
  width: 100%;
  line-height: 22px;
  letter-spacing: 0em;
  height: 107px;
}
</style>
