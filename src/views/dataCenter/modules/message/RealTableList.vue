<template>
<a-spin :spinning="loading">

 <vxe-table
    :data="dataSource"
    ref="multipleTable"
    class="my-table"
    resizable
    show-overflow
    highlight-hover-row
    size="small"
    :height="tableHeight - 24"
  >
    <vxe-table-column type="seq" :width="80" title="序号" align="center"></vxe-table-column>
    <vxe-table-column show-overflow="title" title="转发设备名称" :min-width="160" field="deviceName" :formatter="tabFormatter">
    </vxe-table-column>
    <vxe-table-column show-overflow="title" title="设备类型" :formatter="tabFormatter" field="deviceTypeCN" :min-width="160">
    </vxe-table-column>
    <vxe-table-column show-overflow="title" title="测点类型" :formatter="tabFormatter" field="pointType" :min-width="160">
    <template slot-scope="scope">
        <span>{{ scope.row.pointType =='1'?'遥测':'遥信' }}</span>
      </template>
    </vxe-table-column>
    <vxe-table-column show-overflow="title" title="测点名称" :formatter="tabFormatter" field="pointName" :min-width="160">
    </vxe-table-column>
    <vxe-table-column show-overflow="title" title="测点编码" field="point" :min-width="160">
      <template slot-scope="scope">
        <a @click="gotoDeepAnalysis(scope.row)" :class="isShowMenu ? 'blue' : 'normal-text'" v-if="scope.row.pointStatus=='0'">{{ scope.row.point || '--' }}</a>
        <span :style="{color:scope.row.pointStatus=='1'? 'red':'#999'}" v-else>{{ scope.row.point || '--' }}</span>
      </template>
    </vxe-table-column>
    <vxe-table-column show-overflow="title" title="转发序号" :formatter="tabFormatter" field="pointId" :min-width="160">
    </vxe-table-column>
    <vxe-table-column show-overflow="title" title="实时数据" :formatter="tabFormatter" field="pointValue" :min-width="160">
    </vxe-table-column>
    <vxe-table-column show-overflow="title" title="标准单位" :formatter="tabFormatter" field="pointUnit" :min-width="160">
    </vxe-table-column>
    <vxe-table-column show-overflow="title" title="更新时间" :formatter="tabFormatter" field="time" :min-width="160">
     <template slot-scope="scope">
        <span :style="{color: scope.row.timeDisplayStatus =='0'?'':'red'}">{{ moment(scope.row.time).format('YYYY-MM-DD HH:mm:ss') || '--' }}</span>
      </template>
    </vxe-table-column>
    <template #empty>
      <span>查询无数据</span>
    </template>
  </vxe-table>
  </a-spin>
</template>
<script>
import moment from 'moment';
export default {
  props: {
    dataSource: {
      type: Array,
      default: () => {
        return [];
      }
    },
    baseInfo: {
      require: true,
      default: () => {
        return {
          psId: '',
          psName: '',
          psKey: ''
        };
      }
    },
    tableHeight: {
      type: Number,
      default: 400
    },
    loading: {
      type: Boolean,
      default: true
    }
  },
  computed: {
    isShowMenu () {
      return this.$store.state.user.isShowMenu;
    }
  },
  methods: {
    moment,
    async clearScroll () {
      let $table = this.$refs.multipleTable;
      $table && await $table.clearScroll();
    },
    // 跳转到洞察工具
    gotoDeepAnalysis (row) {
      if (!this.isShowMenu) {
        return;
      }
      let points = [{
        deviceType: row.deviceType,
        key: row.point,
        point: row.point,
        pointName: row.pointName,
        unit: row.pointUnit
      }];
      let devices = [{
        deviceType: row.deviceType,
        psKey: row.psKey
      }];
      let list = {
        points: points,
        devices: devices,
        types: [row.deviceType]
      };
      let time = [moment(row.time), moment(row.time)]; // 发生时间
      this.$router.push({
        name: 'dataCenter-insightTool',
        params: { psId: this.baseInfo.psId, psName: this.baseInfo.psName, list: list, time: time, source: 1 }
      });
      window.comefromAlarmCenter = true;
    }
  }
};
</script>
<style lang="less" scoped>
.normal-text {
  color: #606266;
}
:root[data-theme='dark'] .normal-text {
  color: #fff;
}
</style>
