<template>
  <a-modal
    v-model="departForm.open"
    :maskClosable="false"
    @cancel="cancel"
    :title="departForm.title"
    width="85%"
    centered
  >
    <a-spin :spinning="loading">
          <depart-search @search="searchEvent" ref='search' v-if="departForm.open"></depart-search>
      <!--表格渲染-->
      <div class="station_num">已选择<span class="num"> {{checkedData.length}}</span> 个电站</div>
      <vxe-table
        :height="tableHeight"
        ref="multipleTable"
        align="center"
        border
        show-header-overflow
        show-overflow
        highlight-hover-row
        size="small"
        @checkbox-all="handleSelectionChange"
        @checkbox-change="handleSelectionChange"
        :seq-config="{ startIndex: (page - 1) * size }"
      >
        <vxe-table-column type="checkbox" width="50" fixed="left"></vxe-table-column>
        <vxe-table-column type="seq" width="80" title="序号"></vxe-table-column>
        <vxe-table-column
          show-overflow="title"
          :formatter="tabFormatter"
          v-for="item in columnList"
          :key="item.name"
          min-width="150"
          :field="item.name"
          :title="item.comment"
        >
          <template v-slot="{ row }">
            <template v-if="item.name == 'psModel'">
              <a-tooltip placement="topLeft" :title="getPsaModelLabel(row)" arrow-point-at-center>
                <span>{{ getPsaModelLabel(row) }}</span>
              </a-tooltip>
            </template>
            <template v-else>
              <span>{{ getLabel(row[item.name], null) }}</span>
            </template>
          </template>
        </vxe-table-column>
        <template v-slot:empty>
          <span>查询无数据</span>
        </template>
      </vxe-table>
      <!--分页组件-->
      <page-pagination :pageSize="size" :current="page" :total="total" :pageSizeOptions="[5, 10, 15, 20, 50, 100, 500,1000,2000,5000]" @size-change="sizeChange" />
    </a-spin>
    <div slot="footer" class="modal-footer">
      <a-button size="default" type="primary" @click="doSubmit">保存</a-button>
      <a-button size="default" :disabled="loading" @click="cancel">取消</a-button>
    </div>
  </a-modal>
</template>

<script>
import { assignablePsaList, saveDepartRelPowerStationArchives } from '@/api/isolarErp/depart/departRelationPsa';
import initDict from '@/mixins/initDict';
import DepartSearch from './depart/DepartSearch';
export default {
  name: 'DepartPowerStationForm',
  mixins: [initDict],
  components: { DepartSearch },
  props: {
    departForm: {
      type: Object,
      required: {
        open: false,
        title: '',
        departId: ''
      }
    }
  },

  watch: {
    'departForm.open' (val, old) {
      if (val) {
        this.getDictMap('psa_model');
        this.$nextTick(() => {
          this.query();
        });
      } else {
        Object.assign(this.$data, this.$options.data());
      }
    }
  },

  data () {
    return {
      tableHeight: 300,
      // 查询条件
      psaName: '',
      columnList: [
        { name: 'psaName', comment: '电站名称' },
        { name: 'gridConnectedScale', comment: '并网容量（MW）' },
        { name: 'psModel', comment: '电站类型' },
        { name: 'placeProvinceCity', comment: '所属省市' },
        { name: 'placeAddress', comment: '详细地址' }
      ],
      data: [],
      // 选中的数据
      checkedData: [],
      saveParams: {},
      loading: false,
      size: 10,
      page: 1,
      total: 0
    };
  },

  created () {
    // 加载数据字典
    this.getDictMap('psa_model');
  },

  methods: {
    getPsaModelLabel (row) {
      let label1 = this.getLabel(row.psModelOne, this.dictMap.psa_model);
      let label2 = this.getLabel(row.psModelTwo, this.dictMap.psa_model);
      let label3 = this.getLabel(row.psModelThree, this.dictMap.psa_model);
      let label = label1 + '，' + label2 + '，' + label3;
      label = label.replace('，--', '').replace('，--', '');
      if (label == '') {
        return '--';
      }
      return label;
    },
    // 查询已经关联的电站
    async query (params) {
      let self = this;
      self.loading = true;
      let $table = this.$refs.multipleTable;
      $table && (await $table.clearScroll());
      self.checkedData = [];
      let map = {
        pageSize: self.size,
        pageNo: self.page,
        psaName: self.psaName,
        depId: self.departForm.departId,
        referer: self.departForm.referer,
        ...this.params
      };
      if (params) map = Object.assign({}, map, params);
      const XTable = self.$refs.multipleTable;
      assignablePsaList(map)
        .then((res) => {
          if (res.success) {
            XTable.reloadData(res.result.records).then(() => {
              self.loading = false;
            });
            // self.data = res.result.records;
            self.total = res.result.total;
          } else {
            self.$message.error(res.message);
            XTable.reloadData([]).then(() => {
              self.loading = false;
            });
            self.total = 0;
            self.loading = false;
          }
        })
        .catch(() => {
          XTable.reloadData([]).then(() => {
            self.loading = false;
          });
          self.total = 0;
          self.loading = false;
        });
    },

    // 行选中事件
    handleSelectionChange ({ records }) {
      this.checkedData = records;
    },

    // 保存
    doSubmit () {
      const self = this;
      self.loading = true;
      if (self.checkedData.length < 1) {
        self.$message.warning('请选择要关联的电站');
        self.loading = false;
      } else {
        let psaIds = [];
        self.checkedData.forEach((item) => {
          psaIds.push(parseInt(item.psaId));
        });
        let map = {
          depId: self.departForm.departId,
          psaIds: psaIds
        };
        saveDepartRelPowerStationArchives(map)
          .then((res) => {
            if (res.success) {
              self.$message.success(res.message);
              self.loading = false;
              self.cancel();
            } else {
              self.$message.error(res.message);
              self.loading = false;
            }
          })
          .catch(() => {
            self.loading = false;
          });
      }
    },
    searchEvent (obj) {
      this.page = 1;
      this.query(obj);
    },
    // 分页事件
    sizeChange (p, e) {
      this.page = p;
      this.size = e;
      this.query(this.$refs.search ? this.$refs.search.params : {});
    },

    // 弹窗关闭事件
    cancel () {
      this.page = 1;
      this.psaName = '';
      this.checkedData = [];
      this.$emit('closeDepartForm');
    }
  }
};
</script>

<style lang='less' scoped>
  :deep(.solar-eye-search-model .search-item-s) {
    margin-top: 4px;
  }
  .station_num {
    display: flex;
    justify-content: flex-end;
    margin-bottom: 8px;
    align-items: center;
    .num {
      font-weight: bold;
      font-size:16px;
      padding: 0 8px;
    }
  }
</style>
