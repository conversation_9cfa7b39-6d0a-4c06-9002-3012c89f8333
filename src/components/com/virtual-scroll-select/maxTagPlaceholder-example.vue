<template>
  <div class="max-tag-placeholder-example">
    <h2>maxTagPlaceholder 使用示例</h2>
    
    <!-- 1. 字符串形式的 maxTagPlaceholder -->
    <div class="example-section">
      <h3>1. 字符串形式</h3>
      <a-select
        v-model="stringValue"
        mode="multiple"
        style="width: 100%"
        placeholder="请选择选项"
        :maxTagCount="2"
        maxTagPlaceholder="更多..."
      >
        <a-select-option v-for="item in options" :key="item.value" :value="item.value">
          {{ item.label }}
        </a-select-option>
      </a-select>
      <p>选中值: {{ stringValue }}</p>
    </div>

    <!-- 2. 函数形式的 maxTagPlaceholder -->
    <div class="example-section">
      <h3>2. 函数形式 - 显示剩余数量</h3>
      <a-select
        v-model="functionValue"
        mode="multiple"
        style="width: 100%"
        placeholder="请选择选项"
        :maxTagCount="2"
        :maxTagPlaceholder="getMaxTagPlaceholder"
      >
        <a-select-option v-for="item in options" :key="item.value" :value="item.value">
          {{ item.label }}
        </a-select-option>
      </a-select>
      <p>选中值: {{ functionValue }}</p>
    </div>

    <!-- 3. 带 Tooltip 的 maxTagPlaceholder -->
    <div class="example-section">
      <h3>3. 带 Tooltip 显示完整内容</h3>
      <a-select
        v-model="tooltipValue"
        mode="multiple"
        style="width: 100%"
        placeholder="请选择选项"
        :maxTagCount="2"
        :maxTagPlaceholder="getTooltipPlaceholder"
      >
        <a-select-option v-for="item in options" :key="item.value" :value="item.value">
          {{ item.label }}
        </a-select-option>
      </a-select>
      <p>选中值: {{ tooltipValue }}</p>
    </div>

    <!-- 4. 自定义样式的 maxTagPlaceholder -->
    <div class="example-section">
      <h3>4. 自定义样式</h3>
      <a-select
        v-model="customValue"
        mode="multiple"
        style="width: 100%"
        placeholder="请选择选项"
        :maxTagCount="2"
        :maxTagPlaceholder="getCustomPlaceholder"
      >
        <a-select-option v-for="item in options" :key="item.value" :value="item.value">
          {{ item.label }}
        </a-select-option>
      </a-select>
      <p>选中值: {{ customValue }}</p>
    </div>

    <!-- 5. Virtual Scroll Select 中的使用 -->
    <div class="example-section">
      <h3>5. Virtual Scroll Select 中的使用</h3>
      <virtual-scroll-select
        v-model="virtualValue"
        :select-data="virtualData"
        :is-multiple="true"
        :maxTagCount="2"
        :maxTagPlaceholder="getVirtualPlaceholder"
      />
      <p>选中值: {{ virtualValue }}</p>
    </div>

    <!-- 6. 复杂的 maxTagPlaceholder 示例 -->
    <div class="example-section">
      <h3>6. 复杂示例 - 显示具体的隐藏项</h3>
      <a-select
        v-model="complexValue"
        mode="multiple"
        style="width: 100%"
        placeholder="请选择用户"
        :maxTagCount="2"
        :maxTagPlaceholder="getComplexPlaceholder"
      >
        <a-select-option v-for="user in users" :key="user.id" :value="user.id">
          {{ user.name }} ({{ user.role }})
        </a-select-option>
      </a-select>
      <p>选中值: {{ complexValue }}</p>
    </div>
  </div>
</template>

<script>
import VirtualScrollSelect from './index.vue'

export default {
  name: 'MaxTagPlaceholderExample',
  components: {
    VirtualScrollSelect
  },
  data() {
    return {
      stringValue: ['option1', 'option2', 'option3'],
      functionValue: ['option1', 'option2', 'option3', 'option4'],
      tooltipValue: ['option1', 'option2', 'option3'],
      customValue: ['option1', 'option2', 'option3'],
      virtualValue: ['user1', 'user2', 'user3'],
      complexValue: ['user1', 'user2', 'user3', 'user4'],
      
      options: [
        { value: 'option1', label: '选项一' },
        { value: 'option2', label: '选项二' },
        { value: 'option3', label: '选项三' },
        { value: 'option4', label: '选项四' },
        { value: 'option5', label: '选项五' },
        { value: 'option6', label: '选项六' }
      ],

      users: [
        { id: 'user1', name: '张三', role: '前端开发' },
        { id: 'user2', name: '李四', role: '后端开发' },
        { id: 'user3', name: '王五', role: 'UI设计师' },
        { id: 'user4', name: '赵六', role: '产品经理' },
        { id: 'user5', name: '钱七', role: '测试工程师' }
      ],

      virtualData: {
        data: [
          { name: '张三', code: 'user1' },
          { name: '李四', code: 'user2' },
          { name: '王五', code: 'user3' },
          { name: '赵六', code: 'user4' },
          { name: '钱七', code: 'user5' }
        ],
        label: 'name',
        value: 'code'
      }
    }
  },
  methods: {
    // 函数形式 - 显示剩余数量
    getMaxTagPlaceholder(omittedValues) {
      return `+${omittedValues.length} 更多`
    },

    // 带 Tooltip 的占位符
    getTooltipPlaceholder(omittedValues) {
      const omittedLabels = omittedValues.map(value => {
        const option = this.options.find(opt => opt.value === value)
        return option ? option.label : value
      })
      
      return this.$createElement('a-tooltip', {
        props: {
          title: omittedLabels.join(', '),
          placement: 'top'
        }
      }, [
        this.$createElement('span', {
          style: {
            color: '#1890ff',
            cursor: 'pointer'
          }
        }, `+${omittedValues.length} 项`)
      ])
    },

    // 自定义样式的占位符
    getCustomPlaceholder(omittedValues) {
      return this.$createElement('span', {
        style: {
          background: 'linear-gradient(45deg, #1890ff, #722ed1)',
          color: 'white',
          padding: '2px 6px',
          borderRadius: '10px',
          fontSize: '12px',
          fontWeight: 'bold'
        }
      }, `+${omittedValues.length}`)
    },

    // Virtual Select 的占位符
    getVirtualPlaceholder(omittedValues) {
      return `还有 ${omittedValues.length} 个用户`
    },

    // 复杂示例 - 显示具体的隐藏项
    getComplexPlaceholder(omittedValues) {
      const omittedUsers = omittedValues.map(id => {
        const user = this.users.find(u => u.id === id)
        return user ? user.name : id
      })
      
      const title = omittedUsers.join('、')
      
      return this.$createElement('a-tooltip', {
        props: {
          title: `隐藏的用户：${title}`,
          placement: 'topLeft',
          overlayStyle: { maxWidth: '300px' }
        }
      }, [
        this.$createElement('a-tag', {
          props: {
            color: 'processing'
          },
          style: {
            margin: '2px',
            cursor: 'pointer'
          }
        }, `+${omittedValues.length} 人`)
      ])
    }
  }
}
</script>

<style lang="less" scoped>
.max-tag-placeholder-example {
  padding: 20px;
  max-width: 800px;
  margin: 0 auto;

  h2 {
    color: var(--zw-text-1-color--default);
    margin-bottom: 30px;
  }

  .example-section {
    margin-bottom: 30px;
    
    h3 {
      color: var(--zw-text-1-color--default);
      margin-bottom: 15px;
      font-size: 16px;
    }

    p {
      margin-top: 10px;
      color: var(--zw-text-2-color--default);
      font-size: 14px;
      word-break: break-all;
    }
  }
}

// 全局样式，用于自定义 maxTagPlaceholder
:global(.ant-select-selection__choice__content) {
  display: inline-flex;
  align-items: center;
}
</style>
