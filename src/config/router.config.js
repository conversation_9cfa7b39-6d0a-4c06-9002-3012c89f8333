import { UserLayout, TabLayout } from '@/components/layouts';

/**
 * 走菜单，走权限控制
 * @type {[null,null]}
 */
export const asyncRouterMap = [

  {
    path: '/',
    name: 'dashboard',
    component: TabLayout,
    meta: { title: '首页' },
    redirect: '/dashboard/analysis',
    children: []
  },
  {
    path: '*',
    redirect: '/404',
    hidden: true
  }
];

/**
 * 基础路由
 * @type { *[] }
 */
export const constantRouterMap = [{
  path: '/user',
  component: UserLayout,
  redirect: '/user/login',
  hidden: true,
  children: [{
    path: 'login',
    name: 'login',
    component: () =>
                    import(/* webpackChunkName: "user" */ '@/views/user/Login')
  },
  {
    path: 'alteration',
    name: 'alteration',
    component: () =>
                    import(/* webpackChunkName: "user" */ '@/views/user/alteration/Alteration')
  }
  ]
},
// 大屏
{
  path: '/screen/page',
  component: () =>
            import(/* webpackChunkName: "user" */ '@/views/screen/index.vue')
            // path: '/',
            // component: BlankLayout,
            // redirect: '/screen/page',
            // hidden: true,
            // children: [{
            //     path: '/screen/page',
            //     name: 'screen',
            //     hidden: true,
            //     component: () =>
            //         import ('@/views/screen/index.vue')
            //     }
            // ]
}, {
  path: '/404',
  component: () =>
            import('@/views/exception/404')
},
{
  path: '/houseHold/dashboard',
  component: () => import('@/views/dashboard/runMonitor/index')
},
{
  path: '/houseHold/AlarmCenter',
  component: () => import('@/views/health/realtimeDiagnosis/AlarmCenter')
},
{
  path: '/houseHold/QualityAnalyse',
  component: () => import('@/views/dataCenter/QualityAnalyse')
},
{
  path: '/houseHold/DeepAnalysisTool',
  component: () => import('@/views/dataCenter/DeepAnalysisTool')
},
{
  path: '/houseHold/dataCenter/insightTool',
  component: () => import('@/views/dataCenter/insightTool/InsightTool')
},
{
  path: '/houseHold/cleanAnalysis',
  component: () => import('@/views/health/healthAnalysis/CleanAnalysis')
},
{
  path: '/houseHold/Message',
  component: () => import('@/views/dataCenter/Message')
},
{
  path: '/houseHold/preTest',
  component: () => import('@/views/experiment/index')
}
];
