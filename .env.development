NODE_ENV=development
VUE_APP_ENV=dev
VUE_APP_QIANKUN=true
VUE_APP_API_BASE_URL=https://api-fat.isolareye.com/solareye-system
VUE_APP_API_ERP_URL=https://api-fat.isolareye.com/isolar-erp
VUE_APP_API_MSG_URL=https://api-fat.isolareye.com/solareye-msg
VUE_APP_API_CODE_URL=https://app.isolareye.com/fat/app.png
VUE_APP_API_SEC_URL=https://api-fat.isolareye.com/solareyetwo
VUE_APP_API_BI_URL=https://api-fat.isolareye.com/isolarerpbi
VUE_APP_AIGC_URL=https://aigc-fat.isolareye.com/#/index
VUE_APP_AIGC_API_URL=https://api-fat.isolareye.com/solareye-aigc
VUE_APP_TANGO_BASE_URL=https://api-fat.isolareye.com/solareyecare
VUE_APP_Health_BASE_URL=https://api-fat.isolareye.com/isolar-health
VUE_APP_Health_BASE_DATA_URL=https://api-fat.isolareye.com/solareye-basic-data
VUE_APP_BI_BASE_URL=https://api-fat.isolareye.com/isolarerpbi
VUE_APP_DING_BASE_URL=https://api-fat.isolareye.com/dingding
VUE_APP_API_SHOP_URL = https://api-fat.isolareye.com/solareye-shop
VUE_APP_OSS_FILE_URL=https://api-fat.isolareye.com/solareyemc/oss/v1
VUE_APP_DM_BASE_URL=https://api-fat.isolareye.com/solareyedm
VUE_APP_IOT_BASE_URL=https://api-fat.isolareye.com/solareyeiot
# 微应用列表必须VUE_APP_SUB_开头,solarCare为子应用的项目名称,也是子应用的路由父路径
VUE_APP_SUB_solarCare = '//localhost:3001'