import { axios } from '@/utils/erp-request';

// 根据选择的项目树节点的项目编号获取项目信息
export function getErpProInfoById (data) {
  return axios({
    url: '/system/v1/erpProjectManager/getErpProjectByProjectId',
    method: 'post',
    data
  });
}

// 添加/编辑项目/子项目
export function saveProject (data) {
  return axios({
    url: '/system/v1/erpProjectManager/saveProject',
    method: 'post',
    data
  });
}

// 查询项目员工
export function getErpEmployeeInfoByProjectId (data) {
  return axios({
    url: '/system/v1/erpProjectManager/getErpEmployeeInfoByProjectId',
    method: 'post',
    data
  });
}

// 删除项目
export function delErpProjectManagerByProjectId (data) {
  return axios({
    url: '/system/v1/erpProjectManager/delErpProjectManagerByProjectId',
    method: 'post',
    data
  });
}

// 查询项目电站
export function getErpPowerStationArchivesByProjectId (data) {
  return axios({
    url: '/system/v1/erpProjectManager/getErpPowerStationArchivesByProjectId',
    method: 'post',
    data
  });
}

// 项目check
export function checkIsProject (data) {
  return axios({
    url: '/system/v1/erpProjectManager/checkIsProject',
    method: 'post',
    data
  });
}
// 判断是否已有根节点
export function getProjectRootInfo (data) {
  return axios({
    url: '/system/v1/erpProjectManager/getProjectRootInfo',
    method: 'post',
    data
  });
}
