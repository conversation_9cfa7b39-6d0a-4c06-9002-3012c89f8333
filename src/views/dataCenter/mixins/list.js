/**
 * 新增修改完成调用 drawerFormOk方法 编辑弹框组件ref定义为drawerForm
 * 高级查询按钮调用 superQuery方法  高级查询组件ref定义为superQueryModal
 * data中url定义 list为查询列表  delete为删除单条记录  deleteBatch为批量删除
 */
import { postAction } from '@/api/manage';
import { getDeviceTypeList, baseUrl } from '@/api/dataCenter';
import download from '@/utils/download';

const innerHeight = window.innerHeight - 60 - 40 - 24;
export const dataCenterListMixin = {
  data () {
    return {
      dataSource: [],
      pageSizeOptions: [5, 10, 15, 20],
      loading: false,
      total: '',
      height: '',
      /* table加载状态 */
      /* table选中keys */
      selectedRowKeys: [],
      /* table选中records */
      selectionRows: [],
      exportLoading: false
    };
  },
  created () {

  },
  mounted () {
    this.getTableHeight();
  },
  computed: {
    scroll: function () {
      var width = window.innerWidth;
      let $antTable = window.document.getElementsByClassName('ant-row');
      if ($antTable[0]) {
        width = $antTable[0].clientWidth;
      }
      console.log('$antTable', $antTable);
      return {
        // x:'max-content',
        x: width,
        y: window.innerHeight / 2
      };
    },
    innerHeight () {
      return innerHeight;
    }
  },
  methods: {
    // 设置表格高度
    getTableHeight () {
      let $searchHeight = window.document.getElementsByClassName('search-card');
      let height = 0;
      if ($searchHeight[0]) {
        height = $searchHeight[0].clientHeight + 16;
      }
      this.height = innerHeight - height;
    },
    sizeChange (current, size) {
      this.queryParams.pageSize = size;
      this.queryParams.currentPage = current;
      this.loadData();
    },
    loadData (arg) {
      this.$refs.multipleTable && this.$refs.multipleTable.clearScroll();
      if (!this.url.list) {
        this.$message.error('请设置url.list属性!');
        return;
      }
      // 加载数据 若传入参数1则加载第一页的内容
      if (arg === 1) {
        this.queryParams.currentPage = 1;
      }
      this.loading = true;
      postAction(baseUrl + this.url.list, this.queryParams).then((res) => {
        if (res.result_code == '1') {
          this.dataSource = res.result_data.pageList;
          if (res.result_data) {
            this.total = res.result_data.rowCount;
          }
        }
        this.loading = false;
      }).catch(() => {
        this.loading = false;
      });
    },
    // 表格复选框变化事件
    onSelectChange (val) {
      let arr = [];
      val.records.forEach((item) => {
        arr.push(item.id);
      });
      this.selectedRowKeys = arr;
      this.selectionRows = val.selection;
    },
    onClearSelected () {
      this.selectedRowKeys = [];
      this.selectionRows = [];
    },
    searchQuery () {
      this.loadData(1);
    },
    handleDelete: function (id) {
      if (!this.url.delete) {
        this.$message.error('请设置url.delete属性!');
        return;
      }
      var that = this;
      postAction(baseUrl + that.url.delete, { id: id }).then((res) => {
        if (res.result_code === '1') {
          that.$message.success(res.message);
          that.loadData();
        } else {
          that.$message.warning(res.message);
        }
      });
    },
    handleAction (record, obj) {
      this.$refs.drawerForm.initDrawer(record, obj);
    },
    // 排序
    handleTableSortChange (val) {
      console.log(val);
      if (val.order == null) {
        this.queryParams.order = '';
      } else {
        this.queryParams.order = val.property + ' ' + val.order;
      }
      this.loadData(1);
    },
    // 获取设备类型
    getDeviceTypeList (pointManagementFlag) {
      let obj = {};
      if (pointManagementFlag) {
        obj.pointManagementFlag = '1';
      }
      return new Promise((resolve, reject) => {
        getDeviceTypeList(obj).then(res => {
          this.deviceTypeList = res.result_data;
          resolve(res.result_data);
        }).catch(() => {

        });
      });
    },
    /* 导出 */
    handleExport (list) {
      let params = list || this.getQueryParams();
      this.exportLoading = true;
      postAction(baseUrl + this.url.export, params).then((res) => {
        this.exportLoading = false;
        if (res.result_code === '1') {
          download(res.result_data);
        } else {
          this.$message.warning(res.result_msg);
        }
      }).catch(() => {
        this.exportLoading = false;
      });
    }
  }

};
