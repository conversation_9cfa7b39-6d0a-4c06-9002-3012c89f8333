import { axios } from '@/utils/erp-request';

// 获取公告列表
export function getErpSysPsAppraiseQuotaSet (data) {
  return axios({
    url: '/system/v1/erpSysPsAppraiseQuotaSet/getErpSysPsAppraiseQuotaSet',
    method: 'post',
    data
  });
}
export function upErpSysPsAppraiseQuotaSet (data) {
  return axios({
    url: '/system/v1/erpSysPsAppraiseQuotaSet/upErpSysPsAppraiseQuotaSet',
    method: 'post',
    data
  });
}
export function recoveryDefaultValue (data) {
  return axios({
    url: '/system/v1/erpSysPsAppraiseQuotaSet/recoveryDefaultValue',
    method: 'post',
    data
  });
}
