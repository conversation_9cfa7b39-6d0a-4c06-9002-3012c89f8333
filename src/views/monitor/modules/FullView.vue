<!--全景图-->
<template>
  <div :style="{ flex: params().psId == '107353' && !fLoad() ? 0.99 : 1 }">
    <a-spin size="large" v-if="fLoad()" class="full_load"></a-spin>
    <template v-else>
      <Scene :list="faultDeviceList" @select="changeDevice" v-if="params().psId == '107353'"></Scene>
      <div v-else class="common_bg">
        <img src="@/assets/images/monitor/device/static.png" />
      </div>
    </template>
  </div>
</template>
<script>
import { getFaultDeviceCount } from '@/api/monitor/device';
import Scene from './fullView/Scene';
export default {
  inject: ['params', 'fLoad'],
  components: { Scene },
  data () {
    return {
      faultDeviceList: []
    };
  },
  created () {},
  mounted () {
    let that = this;
    let timer = setInterval(() => {
      that.initData();
      that.$emit('select', true);
    }, 1000 * 5 * 60);
    this.$once('hook:beforeDestroy', () => {
      clearInterval(timer);
      timer = null;
    });
    this.$once('hook:deactivated', () => {
      clearInterval(timer);
      timer = null;
    });
  },
  methods: {
    async initData (data) {
      let res = data || (await this.getData());
      this.faultDeviceList = res.result_data.alarmList;
    },
    getData () {
      return new Promise((resolve, reject) => {
        getFaultDeviceCount({ psId: this.params().psId })
          .then((res) => {
            resolve(res);
          })
          .catch((res) => {
            reject(res);
          });
      });
    },
    changeDevice (obj) {
      this.$emit('select', obj);
    }
  }
};
</script>
<style lang="less" scoped>
.common_bg {
    display: flex;
    align-items: center;
    justify-content: center;
    height: calc(100%);
}
.full_load {
  margin: 15% auto;
  width: 100%;
}
</style>
