<template>
  <div class="custom-select">
    <div :class="['custom-ant-select', isDisabled ? 'custom-select-disabled' : '']" @click="handleOpenTicketModal">
      <div class="inner-box">
        <a-tag v-for="item in ticketDtoList" :key="item.ticketId" :closable="!isDisabled" @close="(e) => handleTagClose(e, item)">
          {{ item.ticketNo }}
        </a-tag>
      </div>
      <a-icon type="right"></a-icon>
    </div>
    <ChooseTicketModal
      ref="chooseTicket"
      :dictMap="dictMap"
      :psaId="psaId"
      :ticketDtoList="ticketDtoList"
      @ok="handleOk"
    />
  </div>
</template>

<script>
import ChooseTicketModal from './ChooseTicketModal';
export default {
  model: {
    prop: 'value',
    event: 'change'
  },
  components: { ChooseTicketModal },
  props: {
    value: {
      type: Array,
      default: () => []
    },
    dictMap: {
      type: Object,
      default: () => ({})
    },
    psaId: {
      type: String
    },
    isDisabled: {
      type: Boolean
    }
  },
  data () {
    return {
      ticketDtoList: []
    };
  },
  watch: {
    value: {
      immediate: true,
      handler (val) {
        this.ticketDtoList = val;
      }
    }
  },
  methods: {
    handleOpenTicketModal () {
      if (this.isDisabled) return;
      const chooseTicketRef = this.$refs.chooseTicket;
      chooseTicketRef.init();
    },
    handleTagClose (e, item) {
      // 阻止冒泡事件
      e.stopPropagation();
      const { ticketId } = item;
      const idx = this.ticketDtoList.findIndex(item => item.ticketId === ticketId);
      this.ticketDtoList.splice(idx, 1);
      this.$emit('change', this.ticketDtoList);
    },

    handleOk (rows) {
      this.ticketDtoList = rows;
      this.$emit('change', rows);
    }
  }
};
</script>
<style scoped lang="less">
.custom-select {
  .ant-tag{
    background: #E8E8E8;
    border-width: 0;
  }
  .custom-ant-select {
    display: flex;
    justify-content: space-between;
    align-items: center;
    border: 1px solid #d9d9d9;
    border-radius: 4px;
    min-height: 32px;
    margin-top: 4px;
    width: 100%;
    padding: 5px 8px;
    cursor: pointer;
    &:active,
    &:focus,
    &:hover {
      border-color: #ffab5c;
      outline: 0;
      -webkit-box-shadow: 0 0 0 2px rgba(255, 143, 51, 0.2);
      box-shadow: 0 0 0 2px rgba(255, 143, 51, 0.2);
    }

    .inner-box{
        display: flex;
        gap: 8px;
        flex-wrap: wrap;
    }
    .anticon {
      color: rgba(0, 0, 0, 0.25);
    }
  }
  .custom-select-disabled{
      cursor: not-allowed;
      background-color: #f5f5f5;
    }

}
</style>
