module.exports = {
  root: true,
  env: {
    node: true
  },
  extends: ['plugin:vue/essential', '@vue/standard'],
  parserOptions: {
    parser: 'babel-eslint'
  },
  rules: {
    //0--不启用； 1--出现问题会有警告； 2--出现问题会报错
    'no-console': process.env.NODE_ENV === 'production' ? 'off' : 'off',
    'no-debugger': process.env.NODE_ENV === 'production' ? 'warn' : 'off',
    indent: [2, 2, { SwitchCase: 1 }], // 缩进风格
    eqeqeq: [0, 'always'], // 关闭要求使用 === 和 !==
    semi: [2, 'always'], // 语句强制分号结尾
    'no-undef': 0, //不能有未定义的变量
    'no-useless-escape': 0, // 禁止不必要的转义字符
    camelcase: 0, // 变量名使用驼峰,
    'vue/no-parsing-error': 0,
    'space-before-function-paren': [0, 'always'] // 函数定义时括号前面要有空格
  }
};
