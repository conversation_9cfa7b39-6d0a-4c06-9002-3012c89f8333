<template>
  <div :style="{'--zoom': 0.9}" class='img-box' @click='handleClick'>
      <img class='first-img' :src='list && list[0] && list[0].fileUrlThumbnail' alt=''/>
    <span class='title'>点击查看全部</span>
  </div>
</template>

<script>
import PropTypes from 'ant-design-vue/es/_util/vue-types';
import VueViewer from '@/mixins/VueViewer';
export default {
  name: 'PsaAppearanceExample',
  mixins: [VueViewer],
  props: {
    list: PropTypes.array.def([])
  },
  data () {
    return {

    };
  },
  methods: {
    handleClick () {
      this.viewerImage({ 'images': this.list });
    }
  }

};
</script>

<style lang='less' scoped>
.img-box{
  width: 94px;
  height: 94px;
  border-radius: 3px;
  opacity: 1;
  display: flex;
  flex-direction: column;
  padding: 4px;
  background: #F0F0F0;
  cursor: pointer;
  justify-content: center;
  align-items: center;
  .first-img{
    width: 74px;
    height: 56px;
    border-radius: 2px;
  }
  .title{
    font-size: 12px;
    line-height: 14px;
    color: #666666;
    padding: 4px 1px 0;
  }
}
</style>
