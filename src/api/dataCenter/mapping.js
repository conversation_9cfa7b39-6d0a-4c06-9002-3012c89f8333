import { postAction } from '@/api/manage';
const url = process.env.VUE_APP_Health_BASE_URL
// const url = 'http://10.5.5.253:8088'
export const getModelMappingParam = (params) => postAction(url + '/standardAccess/getModelMappingParam', params); // 型号映射下拉框
export const getIotModelList = (params) => postAction(url + '/standardAccess/getIotModelList', params); // 型号列表-三方
export const getStandardModelList = (params) => postAction(url + '/standardAccess/getStandardModelList', params); // 型号列表-标准
export const modelMapping = (params) => postAction(url + '/standardAccess/modelMapping', params); // 型号绑定