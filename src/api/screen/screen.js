import { axios } from '@/utils/erp-request';
const url = process.env.VUE_APP_Health_BASE_URL;
const dmUrl = process.env.VUE_APP_DM_BASE_URL;
/**
 * 获取地图配置的中心点和路径
 * @param {Object} data
 */
export function getAllRouteInfo (data) {
  return axios({
    url: '/system/v1/portal/getAllRouteInfo',
    method: 'post',
    data
  });
}
/**
 * 获取地图上所有实体电站的点信息
 * @param {Object} data
 */
export function getAllStationInfo (data) {
  return axios({
    url: '/system/v1/portal/getAllStationInfo',
    method: 'post',
    data
  });
}
/**
 * 综合页面-日发电量
 * @param {Object} data
 */
export function getDaySumElec (data) {
  return axios({
    url: url + '/portal/getDaySumElec',
    method: 'post',
    data
  });
}

/**
 * 单站-故障告警
 * @param {Object} data
 */
export function dataStatistical (data) {
  return axios({
    url: url + '/diagnosis/dataStatistical',
    method: 'post',
    data
  });
}
/**
 * 综合页面-环境指标
 * @param {Object} data
 */
export function getEnvironmentalData (data) {
  return axios({
    url: '/system/v1/portal/getEnvironmentalData',
    method: 'post',
    data
  });
}
/**
 * 综合页面-等效小时数排行
 * @param {Object} data
 */
export function getHoursRank (data) {
  return axios({
    url: '/system/v1/portal/getHoursRank',
    method: 'post',
    data
  });
}
/**
 * 综合页面-月发电量
 * @param {Object} data
 */
export function getMonthSumElec (data) {
  return axios({
    url: '/system/v1/portal/getMonthSumElec',
    method: 'post',
    data
  });
}
/**
 * 综合页面-计划完成率
 * @param {Object} data
 */
export function getPlanFinishRate (data) {
  return axios({
    url: '/system/v1/portal/getPlanFinishRate',
    method: 'post',
    data
  });
}
export function powerTrend (data) {
  return axios({
    url: '/portal/v1/block/powerAnalysis/powerTrend',
    method: 'post',
    data
  });
}
/**
 * 综合页面-电站运行情况
 * @param {Object} data
 */
export function getStationStatus (data) {
  return axios({
    url: '/system/v1/portal/getStationStatus',
    method: 'post',
    data
  });
}
/**
 * 综合页面-获取总装机容量
 * @param {Object} data
 */
export function getSumScale (data) {
  return axios({
    url: '/system/v1/portal/getSumScale',
    method: 'post',
    data
  });
}
/**
 * 综合页面-年发电量
 * @param {Object} data
 */
export function getYearSumElec (data) {
  return axios({
    url: '/system/v1/portal/getYearSumElec',
    method: 'post',
    data
  });
}
/**
 * 单站页面-上月PR
 * @param {Object} data
 */
export function getStationPr (data) {
  return axios({
    url: '/system/v1/portal/getStationPr',
    method: 'post',
    data
  });
}
/**
 * 单站页面-电站运行图
 * @param {Object} data
 */
export function getStationRunningData (data) {
  return axios({
    url: url + '/portal/getStationRunningData',
    method: 'post',
    data
  });
}
/**
 * 单站页面-发电量/功率
 * @param {Object} data
 */
export function getStationTodayElec (data) {
  return axios({
    url: url + '/portal/getStationTodayElec',
    method: 'post',
    data
  });
}
/**
 * 单站页面-天气数据
 * @param {Object} data
 */
export function getStationWeatherInfo (data) {
  return axios({
    url: '/system/v1/portal/getStationWeatherInfo',
    method: 'post',
    data
  });
}
/**
 * 单站页面-告警/故障
 * @param {Object} data
 */
export function getStationWarnInfo (data) {
  return axios({
    url: '/system/v1/portal/getStationWarnInfo',
    method: 'post',
    data
  });
}
/**
 * 单站页面-计划/消缺完成率
 * @param {Object} data
 */
export function getStationPlanDefectInfo (data) {
  return axios({
    url: '/system/v1/portal/getStationPlanDefectInfo',
    method: 'post',
    data
  });
}
/**
 * 获取所有电站的数量
 * @param {Object} data
 */
export function countAllStationInfo (data) {
  return axios({
    url: '/system/v1/portal/countAllStationInfo',
    method: 'post',
    data
  });
}

// Teant22Data
export function getTenant22Data (data) {
  return axios({
    url: '/system/v1/portal/getYearMonthSumElecForTenant22',
    method: 'post',
    data
  });
}
export function getDMData (data) {
  return axios({
    url: dmUrl + '/screen/elecFinishRate',
    method: 'post',
    data
  });
}
