
<template>
  <div>
    <a-drawer
    width="100%"
    :visible="show"
    :body-style="{ padding: '0', }" :drawerStyle="{ background: 'tr', padding:0,overflow:'hidden'}"
    @close="onClose"
    :get-container="dom"
    :wrap-style="{ position: 'absolute' }"
  >
      <a-row :gutter="navTheme == 'dark' ? 16 : 0">
        <a-col :span="4">
          <a-card :body-style="{ height: height  +'px',padding:'24px 0',overflow:'scroll' }">
            <a-tree checkable checkStrictly :treeData="treeData" :expandedKeys.sync="expandedKeys" v-model="bindChecked"
                    @check="getChecked">
              <template slot="custom" slot-scope="item">
                <span class="tree-row flex-between">
                  <span>
                    {{item.title}}
                  </span>
                  <a-tag color="#F56C6C">{{item.problemName}}</a-tag>
                </span>
              </template>
            </a-tree>
          </a-card>
        </a-col>
        <a-col :span="20">
          <a-spin size="big" :spinning="chartLoading">
            <a-card :body-style="{ height: height  +'px' ,overflow:'scroll'}">
              <div>
                <a-range-picker
                  class="search-time-range"
                  format="YYYY-MM-DD"
                  id="forAntDropDown"
                  :placeholder="['起始时间', '结束时间']"
                  :getCalendarContainer="(node) => node.parentNode"
                  @change="onChange"
                  :allowClear="false"
                  v-model="timeRange"
                  :disabled-date="disabledDate"
                />
              </div>

              <div class="quality-chart" :style="{ height:chartHeight + 'px',width:'100%'}" id="device-quality-analyse-chart"></div>

            </a-card>

          </a-spin>

        </a-col>
      </a-row>
    </a-drawer>

  </div>
</template>

<script>
import { modelDataDetail, stationModelPointTree } from '@/api/dataCenter/index';
import echarts from '@/utils/enquireEchart';
import moment from 'moment';
import { mixin } from '@/utils/mixin.js';
import store from '@/store';
let innerHeight = window.innerHeight - 60 - 40 - 24 - 21 + (store.state.user.isShowMenu ? 0 : 124);

export default {
  name: 'DeviceAnalyseChartDrawer',
  props: {
    pageParams: {
      type: Object
    }
  },
  mixins: [mixin],
  data () {
    return {
      show: false, // 弹窗是否弹出
      timeDateBegin: '', // 查询时间始
      timeDateEnd: '', // 查询时间结束
      psId: '', // 电站id
      psKey: '', // 设备key
      deviceType: '', // 设备类型
      chartHeight: 300, // 图表的容器高度值
      selectedPoints: [], // 选中的测点
      bindChecked: {}, // 选中测点双向绑定
      treeData: [
        {
          title: '',
          key: '1',
          checkable: false,
          selectable: false,
          children: [
            {
              title: '',
              key: '1-1',
              checkable: false,
              selectable: false,
              children: []
            }
          ]
        }
      ], // 左侧树
      option: {
        grid: [],
        tooltip: {
          trigger: 'axis',
          alwaysShowContent: false
        },
        xAxis: [],
        axisPointer: {
          link: [{ xAxisIndex: 'all' }],
          snap: true,
          type: 'line',
          show: true
        },
        dataZoom: [{
          type: 'slider', // slider表示有滑动块的，
          show: true,
          showDetail: false,
          bottom: 0,
          xAxisIndex: [], // 表示x轴折叠
          start: 0, // 数据窗口范围的起始百分比
          end: 100// 数据窗口范围的结束百分比
        }, {
          type: 'inside',
          xAxisIndex: []
        }],
        yAxis: [
        ],
        series: [

        ]
      }, // 空图表基础配置
      timeRange: [], // 时间范围
      limitData: [], // 左侧树的极值列表
      expandedKeys: ['1', '1-1'], // 默认展开节点
      gridHeight: 180, // 图表实例中一个y轴高度
      baseHeight: 60, // 图表实例中距离底部的高度
      myChart: null, // 图表实例
      chartLoading: false, // 图表实例
      nowChangeData: {}, // 当前图表源数据
      dom: document.getElementsByClassName('quality-device-list')[0], // 辅助drawer绑定dom
      height: innerHeight > 700 ? innerHeight : 700 // 主题内容高度
    };
  },
  created () {
    console.log(this.height);
  },
  mounted () {
    this.dom = document.getElementsByClassName('quality-device-list')[0];
  },
  watch: {
    'navTheme' () {
      if (this.myChart) {
        this.randerChart(this.nowChangeData);
      }
    }
  },
  methods: {
    open (obj) {
      this.show = true;
      this.timeDateBegin = this.pageParams.defaultDate;
      this.timeDateEnd = this.pageParams.defaultDate;
      this.timeRange = [moment(this.timeDateBegin), moment(this.timeDateEnd)];
      this.treeData[0].title = obj.psName;
      this.treeData[0].children[0].title = obj.deviceName;
      this.psId = obj.psId;
      this.psKey = obj.psKey;
      this.deviceType = obj.deviceType;
      this.getTreeData(obj.psId, obj.psKey);
    },
    disabledDate (current) {
      // 不能选择今天及今天以后的
      return current && current > moment().subtract(1, 'days').endOf('day');
    },
    onChange (val) {
      let dayIntervel = val[1].diff(val[0], 'day');
      if (dayIntervel <= 31) {

      } else {
        this.$message.info('日期范围最多一个月');
        this.timeRange = [moment(val[1]).subtract(1, 'month').calendar(), moment(val[1])];
        this.timeDateBegin = moment(moment(val[1]).subtract(1, 'month').calendar()).format('YYYY-MM-DD');
        this.timeDateEnd = moment(val[1]).format('YYYY-MM-DD');
        return;
      }

      this.timeDateBegin = moment(val[0]).format('YYYY-MM-DD');
      this.timeDateEnd = moment(val[1]).format('YYYY-MM-DD');
      this.refreshData();
    },
    async getTreeData (psId, psKey) {
      let res = await stationModelPointTree({
        // psId: "168881",
        // psKey: "168881_4_5414_1",
        // timeDateBegin: "2021-09-02 00:00:00",
        // timeDateEnd: "2021-09-02 23:59:59"
        psId,
        psKey,
        timeDateBegin: this.timeDateBegin + ' 00:00:00',
        timeDateEnd: this.timeDateEnd + ' 23:59:59'
      });
      this.treeData[0].children[0].children = res.result_data.map((item, index) => {
        let problems = item.name.split(',');
        return {
          title: item.pointName,
          key: item.point,
          checkable: true,
          selectable: false,
          point: item.point,
          problemName: problems.length > 2 ? problems[0] + '...' : problems[0],
          scopedSlots: {
            title: 'custom'
          }
        };
      });

      this.limitData = res.result_data.map((item, index) => {
        return {
          title: item.pointName,
          point: item.point,
          lowerLimitValue: item.lowerLimitValue,
          upperLimitValue: item.upperLimitValue
        };
      });

      if (res.result_data.length == 0) {
        return;
      }
      if (res.result_data.length == 1) {
        this.selectedPoints = [res.result_data[0].point];
      } else {
        this.selectedPoints = [res.result_data[0].point, res.result_data[1].point];
      }
      this.bindChecked = {
        checked: this.selectedPoints,
        halfCheckedKeys: []
      };
      this.initChart();
    },
    getChecked (val, e) {
      if (val.checked.length > 9) {
        this.$message.info('最多可选择9条');
        this.bindChecked = {
          checked: val.checked.filter(item => item != e.node.eventKey),
          halfChecked: []
        };
        return;
      }
      this.selectedPoints = val.checked;
      this.refreshData();
    },
    makeXAxis (gridIndex, opt, length) {
      return echarts.util.merge({
        type: 'category',
        axisLine: { onZero: false, lineStyle: { color: 'rgba(51, 51, 51, .14)' } },
        axisTick: { show: false },
        axisLabel: { show: gridIndex == length - 1, color: '#9B9B9B' },
        splitLine: { show: false, lineStyle: { color: 'rgba(51, 51, 51, .14)' } },
        gridIndex: gridIndex
      }, opt || {}, true);
    },
    makeYAxis (gridIndex, opt) {
      return echarts.util.merge({
        type: 'value',
        gridIndex: gridIndex,
        nameLocation: 'middle',
        nameGap: 80,
        splitLine: { lineStyle: {
          color: this.navTheme == 'dark' ? '#474345' : 'rgba(51, 51, 51, .14)' }
        },
        axisPointer: { show: false },
        axisLine: { onZero: false, show: false },
        axisLabel: { show: true, color: '#9B9B9B', showMaxLabel: gridIndex == 0 },
        nameTextStyle: {
          color: '#9B9B9B'
        }
      }, opt || {}, true);
    },
    makeGrid (top, index, length) {
      return echarts.util.merge({
        top: top,
        bottom: length == index ? 60 : undefined,
        show: true,
        right: 100,
        backgroundColor: index % 2 == 1
          ? (this.navTheme === 'dark' ? 'rgba(255,255,255,.06)' : 'rgba(0,0,0,.02)')
          : 'rgba(255,255,255,0)',
        height: this.gridHeight
      }, {}, true);
    },
    makeSeries (index, opt) {
      return echarts.util.merge({
        type: 'line',
        symbol: 'none',
        // color:'#6e6e6e',
        xAxisIndex: index,
        yAxisIndex: index,
        smooth: true
      }, opt || {}, true);
    },
    makeMarkPoint (opt) {
      return echarts.util.merge({
        symbol: 'pin',
        symbolSize: 50,
        itemStyle: {
          color: '#F56C6C'
        },
        label: {
          show: true,
          color: '#fff'
        },
        data: []
      }, opt || {}, true);
    },
    makeMarkLine (name) {
      let item = this.limitData.find(item => item.title == name);
      let data = [];
      if (item.lowerLimitValue || item.upperLimitValue) {
        if (item.upperLimitValue) {
          data.push({
            label: {
              formatter: '上限值: ' + item.upperLimitValue,
              color: '#9B9B9B'
            },
            position: 'start',
            lineStyle: {
              color: '#F56C6C',
              width: 2
            },
            yAxis: item.upperLimitValue
          });
        }

        if (item.lowerLimitValue) {
          data.push({
            label: {
              formatter: '下限值: ' + item.lowerLimitValue,
              color: '#9B9B9B'
            },
            position: 'end',
            lineStyle: {
              color: '#81b6f5',
              width: 2
            },
            yAxis: item.lowerLimitValue
          });
        }
        return {
          symbol: 'none',
          label: {
            show: true
          },
          data
        };
      } else {
        return {};
      }
    },
    initChart () {
      if (this.myChart == null) {
        let chartDom = document.getElementById('device-quality-analyse-chart');
        this.myChart = echarts.init(chartDom);
        const options = this.option;
        options && this.myChart.setOption(options);
        this.$once('hook:beforeDestroy', function () {
          echarts.dispose(this.myChart);
        });
        this.myChart.on('datazoom', () => {
          this.myChart.dispatchAction({
            type: 'hideTip'
          });
        });
      }
      this.refreshData();
    },
    async refreshData () {
      this.chartLoading = true;
      let res = await modelDataDetail({
        points: [...this.selectedPoints],
        deviceType: this.deviceType,
        psId: this.psId,
        psKey: this.psKey,
        // timeDateBegin: "2021-09-02 00:00:00",
        // timeDateEnd: "2021-09-02 23:59:59",
        // psId: "168881",
        // psKey: "168881_4_5414_1",
        // deviceType: "4",
        timeDateBegin: this.timeDateBegin + ' 00:00:00',
        timeDateEnd: this.timeDateEnd + ' 23:59:59'
      }).catch(() => { this.chartLoading = false; });
      this.chartLoading = false;
      let xData = [];
      let yData = [];
      let yAxis = [];
      let problemData = [];
      let lowerLimitValue = [];
      let upperLimitValue = [];
      res.result_data.map((item, index) => {
        yData.push(item.data.map(el => el.dataY));
        yAxis.push({
          name: item.name,
          unit: item.unit
        });
        lowerLimitValue.push(item.lowerLimitValue);
        upperLimitValue.push(item.upperLimitValue);
        problemData.push(item.problem.map(el => {
          return {
            // coord:[el.index,el.dataY],
            xAxis: el.index,
            yAxis: el.dataY == '' ? '0' : el.dataY,
            name: el.problemName,
            value: el.problemName == '越上限' || el.problemName == '越下限' ? '越限' : el.problemName
          };
        }));
        if (index == 0) {
          xData = item.data.map(el => el.timeX);
        }
      });

      let changeData = {
        xData,
        yData,
        yAxis,
        problemData,
        lowerLimitValue,
        upperLimitValue
      };
      this.randerChart(changeData);
      this.nowChangeData = JSON.parse(JSON.stringify(changeData));
    },
    randerChart (changeData) {
      let options = this.option;
      options.xAxis = changeData.yAxis.map((item, index) => {
        return this.makeXAxis(index, { data: changeData.xData }, changeData.yAxis.length);
      });
      options.yAxis = changeData.yAxis.map((item, index) => this.makeYAxis(index, { name: item.name + '\n' + '(' + item.unit + ')' }));
      options.series = changeData.yData.map((item, index) => {
        return this.makeSeries(index, {
          name: changeData.yAxis[index].name,
          data: item,
          markPoint: this.makeMarkPoint({ data: changeData.problemData[index] }),
          markLine: this.makeMarkLine(changeData.yAxis[index].name)

        });
      });

      options.dataZoom[0].xAxisIndex = changeData.yAxis.map((item, index) => index);
      options.dataZoom[1].xAxisIndex = changeData.yAxis.map((item, index) => index);
      options.grid = changeData.yAxis.map((item, index) => {
        return this.makeGrid(this.baseHeight + this.gridHeight * index, index, changeData.yAxis.length);
      });

      options.tooltip = {
        trigger: 'axis',
        alwaysShowContent: false,
        className: 'solar-eye-tooptip',
        formatter: params => {
          let str = params.reduce((prev, cur) => {
            return prev + cur.marker + cur.seriesName + ' : ' + (cur.value === '' ? '--' : cur.value) + '</br>';
          }, params[0].axisValue + '</br>');
          return str;
        }
      };
      this.myChart.clear();
      this.myChart.resize({ height: this.baseHeight * 2 + this.gridHeight * (changeData.yAxis.length) });
      this.myChart.setOption(options, true);
    },
    goPage () {
      this.$emit('changePage', 1);
    },
    onClose () {
      this.myChart.clear();
      this.selectedPoints = [];
      this.show = false;
    }
  }
};
</script>

<style lang="less" scoped>
  .tree-row {
    width: 100% !important;
  }
  :deep(.ant-tree-title){
    width: 100% !important;
  }
  :deep(.ant-tree-node-content-wrapper) {
    width: 80% !important;
  }

</style>
