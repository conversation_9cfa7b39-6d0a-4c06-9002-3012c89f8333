import { axios } from '@/utils/erp-request';
import { postAction, getAction } from '@/api/manage';
const url = process.env.VUE_APP_Health_BASE_URL;
// const url = 'http://10.5.7.50:8085';
// 获取告警事件列表
export function getAlarmEvents (data) {
  return axios({
    url: url + '/diagnosis/list',
    method: 'post',
    data
  });
}
// 导出告警事件
export const exportAlarmEvents = (params) => postAction(url + '/diagnosis/export', params);
// 复核告警事件
export function reviewAlarmEvent (data) {
  return axios({
    url: url + '/diagnosis/review',
    method: 'post',
    data
  });
}
// 删除告警事件
export function deleteAlarmEvent (data) {
  return axios({
    url: url + '/diagnosis/delete',
    method: 'post',
    data
  });
}
// 关闭告警事件
export function closeAlarmEvent (data) {
  return axios({
    url: url + '/diagnosis/close',
    method: 'post',
    data
  });
}
// 隔离告警事件
export function seprarateEvents (data) {
  return axios({
    url: url + '/diagnosis/isolate',
    method: 'post',
    data
  });
}
// 派发查看历史选择项目接口
export function distributeHistory (data) {
  return axios({
    url: url + '/diagnosis/distributeHistory',
    method: 'post',
    data
  });
}
// 获取时序分析测点曲线
export function faultCurve (data) {
  return axios({
    url: url + '/diagnosis/faultCurve',
    method: 'post',
    data
  });
}
// 联动分析接口
export function linkageAnalysis (data) {
  return axios({
    url: url + '/diagnosis/linkageAnalysis',
    method: 'post',
    data
  });
}
// 查询电站主环境检测仪
export function getMeteo (data) {
  return axios({
    url: url + '/healthPowerStation/getMeteo',
    method: 'post',
    data
  });
}
// 获取待处理、已隔离、已派发总条数
export function getTotal (data) {
  return axios({
    url: url + '/diagnosis/dataStatistical',
    method: 'post',
    data
  });
}
// 获取设备故障日历
export function getDeviceAlarmCalendar (data) {
  return axios({
    url: url + '/diagnosis/getDeviceAlarmCalendar',
    method: 'post',
    data
  });
}
// 时序分析曲线图取得设备运行状态接口
export function getDeviceStatus (data) {
  return axios({
    url: url + '/diagnosis/getDeviceStatus',
    method: 'post',
    data
  });
}
// 时序分析曲线图故障时间段接口
export function getFaultTimeRange (data) {
  return axios({
    url: url + '/diagnosis/getFaultTimeRange',
    method: 'post',
    data
  });
}
// 时序分析曲线图故障时间段接口
export function getDefaultDeviceByPsId (data) {
  return axios({
    url: url + '/diagnosis/getDefaultDeviceByPsId',
    method: 'post',
    data
  });
}
// 取得设备待处理故障信息
export function getFaultByPsKey (data) {
  return axios({
    url: url + '/diagnosis/getFaultByPsKey',
    method: 'post',
    data
  });
}
// 获取health字典
export function getSystemCodeListWithChild (data) {
  return axios({
    url: url + '/systemCode/getSystemCodeListWithChild',
    method: 'post',
    data
  });
}
// 查询指定时间段内的天气接口
export function getWeatherByTimeRange (data) {
  return axios({
    url: url + '/healthPsWeather/getWeatherByTimeRange',
    method: 'post',
    data
  });
}

// 查询隔离详情
export function getIsolateDetail (data) {
  return axios({
    url: url + '/diagnosis/isolateDetail',
    method: 'post',
    data
  });
}

// 取得电站下对应设备类型的测点集接口
export function queryDeviceTypePointByPsId (data) {
  return axios({
    url: url + '/healthPowerStation/queryDeviceTypePointByPsId',
    method: 'post',
    data
  });
}

// 取得电站下对应设备类型的测点集接口 (新)
export function queryDeviceTypePointByMultiPsId (data) {
  return axios({
    url: url + '/healthPowerStation/queryDeviceTypePointByMultiPsId',
    method: 'post',
    data
  });
}
// 获取故障原因标签字典
export function getDict (data) {
  return axios({
    url: url + '/dict/getDictForStatus',
    method: 'post',
    data
  });
}
// 打单个标签
export function tagging (data) {
  return axios({
    url: url + '/diagnosis/tagging',
    method: 'post',
    data
  });
}
// 批量打标签
export function taggingList (data) {
  return axios({
    url: url + '/diagnosis/taggingList',
    method: 'post',
    data
  });
}
// 洞察工具添加常用测点
export function createViewTemplate (data) {
  return axios({
    url: url + '/insightTools/viewTemplate/create',
    method: 'post',
    data
  });
}
// 洞察工具常用测点列表
export function viewTemplateList (data) {
  return axios({
    url: url + '/insightTools/viewTemplate/list',
    method: 'post',
    data
  });
}
// 洞察工具删除常用测点
export function deleteViewTemplate (data) {
  return axios({
    url: url + '/insightTools/viewTemplate/delete',
    method: 'post',
    data
  });
}
const twoUrl = process.env.VUE_APP_API_SEC_URL;
// 报警类型数据字典
export const getAlarmTypeList = (params) => postAction(url + '/dict/getDictListForGrade', params);
// 批量派发
export const batchDistributeEvent = (params) => postAction(twoUrl + '/default/management/faultDaily/distributeWorkOrderForList', params);
// 诊断中心tab页故障数量
export const dataStatisticalSumForRemarkTab = (params) => postAction(url + '/diagnosis/dataStatisticalSumForRemarkTab', params);
// 诊断中心tab页新增故障数量、环比数据
export const dataStatisticalRateForRemarkTab = (params) => postAction(url + '/diagnosis/dataStatisticalRateForRemarkTab', params);
// 诊断中心获取故障原因下拉选项
export const getReasonByRemarkAndDeviceType = (params) => postAction(url + '/diagnosis/getReasonByRemarkAndDeviceType', params);
// 动态获取alarmRemark
export const getRemarkDynamic = (params) => postAction(url + '/diagnosis/getRemarkAndReasonForGrade', params);

// #故障诊断#
// 获取隐患运行新详情
export const getAlarm = (params) => postAction(url + '/diagnosis/getAlarm', params);

// 导出行为分析/环境诊断
export const exportForSensorApi = (params) => postAction(url + '/diagnosis/exportForSensor', params);

// 设备健康-诊断详情
export const diagnosisDetail = (params) => getAction(url + '/diagnosis/detail', params);

// 设备健康-工单详情
export const workOrderDetail = (params) => getAction(url + '/diagnosis/workOrderDetail', params);

export const alarmRemarkAndReasonApi = (params) => getAction(url + '/diagnosis/alarmRemarkAndReason', params);
// 设备健康-统计
export const diagnosisStatistics = (params) => getAction(url + '/diagnosis/statistics', params);
