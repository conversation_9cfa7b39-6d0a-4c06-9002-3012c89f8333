<template>
  <div class="alarmAnalysis">
    <a-drawer
      width="100%"
      :visible="alarmAnalysisVisable"
      @close="onClose(false)"
      :get-container="getDom"
      :wrap-style="{ position: 'absolute' }"
      :closable="false"
      :afterVisibleChange="afterVisibleChange"
      wrapClassName="solar-eye-drawer-bg">
    <a-spin :spinning="pageloading" >

        <a-icon class="health-clean-icon" title="清除日期" type="close" />
        <div class="analysis-title">
          <span class="back-icon" @click="onClose(false)"><a-icon type="left" />返回</span>
          <div class="split-line"></div>
          <span class="title">AI⁺诊断</span><span class="split">/</span>
          <span class="alarm-type">{{tabName}}</span>
        </div>
        <div class="analysis-body">
          <div class="analysis-head">
            <span class="device-name">{{defaultInfo.deviceNameShow}}</span>
            <span>电站：{{defaultInfo.psName}}</span>
          </div>
          <div class="solar-eye-deep-tool analysis-content">
            <!-- 右侧分析内容页面 -->
            <div class="right">
              <a-row>
                <!-- 查询条件Tab区域 -->
                <div class="seaech-content">
                  <div class="search-item">
                    <span class="search-label">日期</span>
                      <a-range-picker class="search-date" :disabledDate="disabledDate" @openChange="openChange" @change="timeDateChange"
                        @calendarChange="calendarChange" v-model="sequenceTime" allowClear format="YYYY-MM-DD">
                        <template slot="dateRender" slot-scope="current">
                          <div class="my-date">
                            <div :class="getTopicalClassName(current)">
                            </div>
                            <div :class="`ant-calendar-date ${getCurrentClassName(current)}`">
                              {{ current.date() }}
                            </div>
                          </div>
                        </template>
                      </a-range-picker>
                  </div>
                  <div class="search-item">
                    <span class="search-label">时间间隔</span>
                      <a-select class="search-time" v-model="timeQueryData.timeInterval" show-search @change="getTimeAnalysisData(timeQueryData)"
                        :options="timeIntervalOptions"  placeholder="请选择" >
                      </a-select>
                  </div>
                  <!-- 天气信息 -->
                  <div class="search-item">
                   <div class="weather-info" v-if="Object.keys(weatherInfo).length>0 && weatherData.length > 0">
                      <a-popover placement="rightTop" trigger="click " >
                        <a-space>
                          <a-col>
                            <img v-if="weatherInfo.iconDir" :src="weatherInfo.iconDir" class="weather-icon"/>
                          </a-col>
                          <span v-if="(weatherInfo.tempNight+'') || (weatherInfo.tempDay+'')">{{weatherInfo.tempNight}}℃~{{weatherInfo.tempDay}}℃</span>
                          <span>|</span><span>{{weatherInfo.city.name}}</span>
                        </a-space>
                        <template slot="content" >
                          <div class="weather-popper " >
                            <vxe-table :data="weatherData" :maxHeight="260" ref="weatherTable" align="center"
                              resizable border show-overflow highlight-hover-row size="small" >
                              <vxe-table-column show-overflow="title" field="predictDate" title="日期" width="150">
                              </vxe-table-column>
                              <vxe-table-column show-overflow="title" field="conditionDay" title="天气" width="150">
                                <template v-slot="{ row }">
                                  <div class="weather-row">
                                    <img :src="mapWeatherIconById(row.conditionIdDay)" class="weather-icon"/>
                                    <span>{{ row.conditionDay }}</span>
                                  </div>
                                </template>
                              </vxe-table-column>
                              <vxe-table-column show-overflow="title" field="tempDay" title="气温[℃]" width="150">
                                <template v-slot="{ row }">
                                  <div class="weather-row">
                                    <span v-if="(row.tempNight+'') || (row.tempDay+'')">{{row.tempNight}}℃~{{row.tempDay}}℃</span>
                                  </div>
                                </template>
                              </vxe-table-column>
                            </vxe-table>
                          </div>
                        </template>
                      </a-popover>
                    </div>
                  </div>

                </div>
                <div class="go-page" @click="gotoDeepAnalysis" v-if="isShowMenu">
                  <span class="link">自定义分析</span><a-icon type="right" />
                </div>
                <!-- 操作按钮 -->
                <div class="operation-icon">
                  <span title="派发" v-if="show(910108101102)" :class="{disabled: defaultInfo.psCategory && defaultInfo.psCategory.includes('Storage') }">
                    <svg-icon v-show="alarmStatus == '01' && showOperationButton" @click="batchDistributeEvent(defaultInfo, '2', '4')"
                      iconClass="health-flaw" class="health-icon"></svg-icon>
                  </span>
                  <span title="详情" v-if="show(910108101110)">
                    <svg-icon v-show="alarmStatus == '04'"  class="health-icon" @click="workOrderDetail(defaultInfo, 'alarm')"
                      iconClass="health-detail"></svg-icon>
                    <!-- <erp-button v-show="alarmStatus == '04' || alarmStatus == '02'"  perms="910108101110" title="详情" icon="file-text"
                      size="default" @click="workOrderDetail(defaultInfo, 'alarm')" class="detail-btn ">
                    </erp-button> -->
                  </span>
                  <!-- <span title="关闭" v-if="show(910108101105)">
                    <svg-icon v-show="alarmStatus == '02' && !defaultInfo.finishedTime && showOperationButton" @click="doClose(defaultInfo, '2')"
                      iconClass="health-close"></svg-icon>
                  </span> -->
                  <span title="删除" v-if="show(910108101106)">
                    <svg-icon v-show="alarmStatus == '01' || alarmStatus == '02' && showOperationButton" @click="doDelete(defaultInfo, '2')"
                      iconClass="health-delete" class="health-icon"></svg-icon>
                  </span>
                </div>
                <!-- 分析内容区域 -->
                <a-col class="analysis-box solar-eye-pure-bg">
                  <!-- 时序分析页面 -->
                  <div class="chart-content" id="linkAnalysis" :style="{height: isShowMenu ? 'calc(100vh - 356px)' : 'calc(100vh - 232px)'}">
                    <!-- 时序分析图表 -->
                    <time-analysis-chart ref="timeAnalysisChart" :getLegend="getLegend" ></time-analysis-chart>
                  </div>
                </a-col>
              </a-row>
            </div>
          </div>
        </div>
      </a-spin>
    </a-drawer>
    <drawer-view ref="orderForm" parentId="AlarmCenter" />
  </div>
</template>
<script>
import { faultCurve, getDeviceAlarmCalendar, getFaultTimeRange, getWeatherByTimeRange } from '@/api/health/AlarmEvents.js';
import { getPowerStationTopicalDay } from '@/api/health/healthapi.js';
import initDict from '@/mixins/initDict';
import { LeftMixin } from '@/mixins/LeftMixin';
import { AlarmEventMixins } from '../mixins/alarmEventMixins';
import TimeAnalysisChart from './TimeAnalysisChart';
import moment from 'moment';
export default {
  name: 'AlarmAnalysis',
  mixins: [initDict, LeftMixin, AlarmEventMixins],
  components: {
    TimeAnalysisChart
  },
  props: {
    tabName: {
      type: String,
      default: ''
    },
    type: {
      type: String,
      default: '1'
    },
    deviceType: {
      type: String,
      default: ''
    }
  },
  data () {
    return {
      alarmAnalysisVisable: false,
      pageloading: false,
      weatherInfo: {},
      weatherData: [],
      showOperationButton: true,
      // 工作台列表数据进入的默认信息
      defaultInfo: '',
      alarmStatus: '',
      // 时序分析 - 查询条件变量
      timeOptionRange: [],
      alarmDays: [],
      topicalDays: [],
      timeIntervalOptions: [],
      timeIntervalOptionsAll: {
        day: [
          {
            label: '5min',
            value: '5'
          }, {
            label: '15min',
            value: '15'
          }, {
            label: '30min',
            value: '30'
          }, {
            label: '60min',
            value: '60'
          }
        ],
        week: [
          {
            label: '15min',
            value: '15'
          }, {
            label: '30min',
            value: '30'
          }, {
            label: '60min',
            value: '60'
          }
        ]
      },
      sequenceTime: [],
      timeQueryData: {},
      // 时序分析-分析图表变量
      xAxisData: [],
      legend: [],
      series: [],
      faultArr: [],
      irradiateCount: 0,
      faultPointCount: 0,
      enableCount: 0, // 组串未接
      remoteCount: 0,
      unitArr: [],
      stopPoint: undefined,
      legendData: []
    };
  },
  computed: {
    // 监听换肤颜色变化
    navTheme () {
      return this.$store.state.app.theme;
    },
    isShowMenu () {
      return this.$store.state.user.isShowMenu;
    },
    // 判断是否低效缺陷的电站低效
    isdeviceType11inTab3 () {
      let { defaultInfo, type } = this;
      return type == '3' && defaultInfo.deviceType == '11';
    }
  },

  methods: {
    moment,
    // 将抽屉组件挂载到对应的父组件上
    getDom () {
      return document.getElementById('AlarmCenter');
    },
    // 页面初始化变量， alarmStatus: 1 待处理  2 已隔离  3 已派发  4 已闭环
    init (row, alarmStatus) {
      this.alarmAnalysisVisable = true;
      this.defaultInfo = row;
      this.alarmStatus = alarmStatus;
      // 时序分析-初始化查询条件
      this.timeIntervalOptions = this.timeIntervalOptionsAll.day;
      this.timeQueryData = {
        id: this.defaultInfo.id,
        psKey: this.defaultInfo.psKey,
        timeInterval: '5',
        startTime: '',
        endTime: '',
        pointList: [],
        isInitialization: true,
        psId: this.defaultInfo.psId
      };
      this.sequenceTime = [moment(row.lastHappenTime || row.happenTime), moment(new Date())];
      this.timeOptionRange = [];
      this.setSequenceTime(row);
      this.timeQueryData.startTime = this.sequenceTime[0].format('YYYY-MM-DD');
      this.timeQueryData.endTime = this.sequenceTime[1].format('YYYY-MM-DD');
    },
    // 时序分析，设置查询条件日期值
    setSequenceTime (row) {
      let dayIntervel = this.sequenceTime[1].diff(this.sequenceTime[0], 'day');
      if (dayIntervel > 7) {
        let temp = this.sequenceTime[0].format('YYYY-MM-DD');
        this.sequenceTime = [moment(temp), moment(temp).add(7, 'days')];
      }
      // 低效，电站下日期区间是前7天 (有更新时间用更新时间往前推7天，没有更新时间用发生时间往前推7天)
      if (this.type == '3' && row.deviceType == '11') {
        let time = row.lastHappenTime || row.happenTime;
        this.sequenceTime = [moment(time).subtract(6, 'days'), moment(time)];
      }
      if (dayIntervel > 0) {
        this.timeIntervalOptions = this.timeIntervalOptionsAll.day;
        this.timeQueryData.timeInterval = '5';
      }
    },
    // 抽屉组件切换动画结束后事件，异步请求事件须放在这里执行，否则可能会导致页面滑动卡死
    async afterVisibleChange () {
      if (this.alarmAnalysisVisable) {
        this.pageloading = true;
        // 时序分析-获取故障日期
        this.getAlarmDay(this.defaultInfo);
        // 时序分析-获取典型日
        this.getTopicalDay(this.defaultInfo);
        // 时序分析-获取天气信息
        this.getWeatherInfo(this.defaultInfo.psId);
        this.getTimeAnalysisData(this.timeQueryData);
        let setEventFlag = this.type == '1' && this.defaultInfo.deviceTypeShow == '10';
        // 时序分析、联动分析图表初始化
        this.$refs.timeAnalysisChart.drawLine(setEventFlag);
      }
    },
    // 关闭取消功能、重置数据
    onClose (flag) {
      this.alarmAnalysisVisable = false;
      this.showOperationButton = true;
      // 测点树、设备树、天气信息-重置变量
      this.pointArr = [];
      this.weatherInfo = {};
      this.weatherData = [];
      // 时序分析-重置变量
      this.xAxisData = [];
      this.legend = [];
      this.series = [];
      this.faultArr = [];
      this.irradiateCount = 0;
      this.faultPointCount = 0;
      this.enableCount = 0;
      this.remoteCount = 0;
      this.remotePoint = {};
      // 销毁时序分析图表实例
      if (this.$refs.timeAnalysisChart) {
        this.$refs.timeAnalysisChart.destroyChart();
      }
      this.$emit('alarmAnalysisClose', flag);
    },
    // 时序分析-获取故障日期
    getAlarmDay (row) {
      // 请求查询接口
      this.alarmDays = [];
      getDeviceAlarmCalendar({ psKey: row.psKey })
        .then((res) => {
          if (res.result_code === '1') {
            if (res.result_data) {
              res.result_data.forEach((item) => {
                this.alarmDays.push(moment(item).format('YYYY/MM/DD'));
              });
            }
          }
        });
    },
    // 时序分析-获取典型日
    getTopicalDay (row) {
      // 请求查询接口
      this.topicalDays = [];
      let paramsObj = {
        psId: row.psId, // 电站id
        startTopicalDay: '', // 典型日开始时间
        endTopicalDay: '' // 典型日结束时间
      };
      getPowerStationTopicalDay(paramsObj)
        .then((res) => {
          if (res.result_code === '1') {
            if (res.result_data) {
              res.result_data.forEach((item) => {
                this.topicalDays.push(moment(item).format('YYYY/MM/DD'));
              });
            }
          }
        });
    },
    // 时序分析-获取天气信息
    getWeatherInfo (psId) {
      let obj = {
        psId: psId,
        startDate: this.timeQueryData.startTime || this.defaultInfo.happenTime.substring(0, 10),
        endDate: this.timeQueryData.endTime || this.defaultInfo.happenTime.substring(0, 10)
      };
      getWeatherByTimeRange(obj).then(res => {
        if (Object.keys(res.result_data).length > 0) {
          this.weatherInfo.city = res.result_data.city;
          this.weatherData = res.result_data.weather.map(item => {
            if ((this.timeQueryData.endTime || this.defaultInfo.happenTime.substring(0, 10)) == item.timeDate.substring(0, 10)) {
              this.weatherInfo.tempDay = item.tempDay;
              this.weatherInfo.tempNight = item.tempNight;
              this.weatherInfo.iconDir = this.mapWeatherIconById(item.conditionIdDay);
            }
            return {
              predictDate: item.timeDate.substring(0, 10),
              conditionDay: item.conditionDay,
              conditionIdDay: item.conditionIdDay,
              tempDay: item.tempDay,
              tempNight: item.tempNight
            };
          });
        } else {
          this.weatherData = [];
        }
        if (this.$refs.weatherTable) {
          this.$refs.weatherTable.refreshColumn();
        }
      });
    },
    // 时序分析-获取天气图标
    mapWeatherIconById (conditionId) {
      if (conditionId >= 0) {
        return require(`@/assets/images/weather/W${conditionId}.png`);
      }
      return null;
    },
    // 时序分析-获取分析测点数据
    async getTimeAnalysisData (pramas) {
      this.irradiateCount = 0;
      this.faultPointCount = 0;
      this.enableCount = 0;
      if (!this.pageloading) {
        this.pageloading = true;
      }
      pramas.psId = this.timeQueryData.psId;
      pramas.source = this.defaultInfo.source;
      // 分析数据
      let timeDataResult = await faultCurve(pramas).catch(() => { this.pageloading = false; });
      // 故障区间数据
      let faultTimeRangeResult = await getFaultTimeRange({
        ...pramas,
        alarmReason: this.defaultInfo.alarmReason
      }).catch(() => { this.pageloading = false; });
      if (!timeDataResult) {
        this.pageloading = false;
        return;
      }
      this.xAxisData = timeDataResult.result_data.dataX.map(item => {
        return item.split(' ')[0] + '\n' + item.split(' ')[1];
      });
      let dataY = timeDataResult.result_data.dataY;
      // 初始进入界面时，取后台返回的默认测点
      if (this.timeQueryData.isInitialization) {
        this.timeQueryData.pointList = timeDataResult.result_data.pointList;
        // 遥信数据需单独记录返回的遥信测点信息
        if (this.defaultInfo.pointType == '1') {
          this.timeQueryData.pointList.forEach(item => {
            if (item.pointType == '1') {
              this.remoteCount = 1;
              this.remotePoint = item;
            }
          });

          timeDataResult.result_data.dataY.forEach(item => {
            if (item.point == this.remotePoint.point) {
              this.remotePoint.pointName = item.pointName;
              this.remotePoint.unit = item.unit;
              this.remotePoint.deviceName = item.deviceName;
            }
          });
        }
        this.timeQueryData.pointList.forEach(item => {
          if (item.pointType == '3') {
            this.stopPoint = item;
          }
        });
        if (this.stopPoint) {
          timeDataResult.result_data.dataY.forEach(item => {
            if (item.point == this.stopPoint.point) {
              this.stopPoint.pointName = item.pointName;
              this.stopPoint.unit = item.unit;
              this.stopPoint.deviceName = item.deviceName;
            }
          });
        }
        this.timeQueryData.isInitialization = false;
      }
      let legendData = this.getLegendData(dataY);
      this.legend = this.getLegend(legendData);
      this.legendData = legendData;
      this.faultArr = [];
      // 清除上个图表内容，重新绘制图表
      if (this.$refs.timeAnalysisChart) {
        this.$refs.timeAnalysisChart.destroyChart();
      }
      let setEventFlag = this.type == '1' && this.defaultInfo.deviceTypeShow == '10';
      let isExistChart = this.$refs.timeAnalysisChart;
      isExistChart && isExistChart.drawLine(setEventFlag);
      if (legendData.length > 0 && dataY.length > 0) {
        this.series = this.getSeries(legendData, dataY, this.xAxisData, faultTimeRangeResult);
        let chartData = {
          xAxisData: this.xAxisData,
          legend: this.legend,
          series: this.series,
          faultArr: this.faultArr,
          alarmType: this.defaultInfo.alarmType,
          irradiateCount: this.irradiateCount,
          faultPointCount: this.faultPointCount,
          enableCount: this.enableCount,
          unitArr: this.unitArr,
          remoteCount: this.remoteCount,
          legendData: legendData[0].data,
          deviceType: this.defaultInfo.deviceTypeShow,
          type: this.type
        };
        isExistChart && isExistChart.refreshChart(chartData);
      }
      this.pageloading = false;
    },
    dealData () {
      let isExistChart = this.$refs.timeAnalysisChart;
      let chartData = {
        xAxisData: this.xAxisData,
        legend: this.legend,
        series: this.series,
        faultArr: this.faultArr,
        alarmType: this.defaultInfo.alarmType,
        irradiateCount: this.irradiateCount,
        faultPointCount: this.faultPointCount,
        enableCount: this.enableCount,
        unitArr: this.unitArr,
        remoteCount: this.remoteCount,
        legendData: this.legendData[0].data,
        deviceType: this.defaultInfo.deviceTypeShow,
        type: this.type
      };
      isExistChart && isExistChart.refreshChart(chartData);
    },
    // 时序分析-图表-获取图标legend对象数组
    getLegendData (dataY) {
      let data = [];
      // 斜面瞬时辐照测点信息
      let irradiateInfo = {};
      let irradiateFlag = false;
      // Y轴单位数组
      this.unitArr = [];
      let defaultDevicePointArr = []; // 默认设备测点数据
      let faultPointArr = []; // 故障测点数据
      let enableArr = []; // 组串未接测点数据
      let otherDevicePointArr = []; // 其他设备测点数据
      let { alarmRemark, psKey, deviceType } = this.defaultInfo;
      dataY.forEach(item => {
        if ((item.psKey.split('_')[1] == '1' || ['36', '37', '39', '42', '43'].includes(alarmRemark)) && psKey != item.psKey) {
          otherDevicePointArr.push({
            psKey: item.psKey,
            points: []
          });
        }
      });
      otherDevicePointArr = this.arrUnique(otherDevicePointArr, 'psKey') || [];
      this.timeQueryData.pointList.forEach(ele => {
        dataY.forEach(v => {
          if (v.point == ele.point && v.psKey == ele.psKey) {
            ele.pointName = v.pointName;
            ele.unit = v.unit;
            if (ele.point == 'p2007') {
              irradiateFlag = true;
              irradiateInfo = {
                deviceName: v.deviceName,
                pointName: v.pointName,
                psKey: v.psKey,
                unit: v.unit
              };
              this.irradiateCount = 1;
            // 组串故障时，故障测点排在前面，并记录个数
            } else if (((alarmRemark == '2' || alarmRemark == '3' || alarmRemark == '4') && v.isFaultPoint)) {
              faultPointArr.push({ deviceName: v.deviceName, pointName: v.pointName, psKey: v.psKey, unit: v.unit });
              this.faultPointCount = this.faultPointCount + 1;
            } else if (ele.enable === '0') { // 组串未接
              enableArr.push({ deviceName: v.deviceName, pointName: v.pointName, psKey: v.psKey, unit: v.unit });
              this.enableCount += 1;
            } else if (v.psKey == psKey && ['1', '4', '7', '301', '5', '26', '23', '24', '37'].includes(deviceType) && v.point != 'stop') {
              defaultDevicePointArr.push({ deviceName: v.deviceName, pointName: v.pointName, psKey: v.psKey, unit: v.unit });
            } else {
              otherDevicePointArr.forEach(item => {
                if (item.psKey == v.psKey) {
                  item.points.push({ deviceName: v.deviceName, pointName: v.pointName, psKey: v.psKey, unit: v.unit });
                }
              });
            }
            if (this.unitArr.indexOf(v.unit) == -1) {
              this.unitArr.push(v.unit);
            }
          }
        });
      });
      let arr = [];

      otherDevicePointArr.forEach(item => {
        arr = [...arr, ...item.points];
      });
      // 按故障测点数据、默认设备测点数据、其他设备测点数据排序、未接组串测点数据
      data = [...faultPointArr, ...defaultDevicePointArr, ...arr, ...enableArr];
      // 遥信测点排在第二位
      if (this.defaultInfo.pointType == '1') {
        data.unshift(this.remotePoint);
        if (this.unitArr.indexOf(this.remotePoint.unit) == -1) {
          this.unitArr.push(this.remotePoint.unit);
        }
      }

      // 斜面瞬时辐照排在第一位
      if (irradiateFlag) {
        data.unshift(irradiateInfo);
      }
      if (this.stopPoint && data.length == 0) {
        data.push(this.stopPoint);
        if (this.unitArr.indexOf(this.stopPoint.unit) == -1) {
          this.unitArr.push(this.stopPoint.unit);
        }
      }
      return [{ data: data }];
    },
    // 时序分析-图表-获取图标legend数组
    getLegend (legendData) {
      let data = [];
      legendData.forEach((item, index) => {
        item.data.forEach(ele => {
          if (ele.point != 'stop') {
            data.push(ele.deviceName + ele.pointName);
          }
        });
      });
      let obj = { data: data, type: 'scroll', y: 10 }; let isDark = this.navTheme == 'dark'; let isDarkColor = isDark ? '#ffffff' : '#9b9b9b'; let isNeedObj = {
        textStyle: {
          color: isDarkColor
        },
        pageIconColor: isDarkColor,
        pageTextStyle: {
          color: isDarkColor
        }
      };
      let realObj = Object.assign({}, obj, isNeedObj);

      return [realObj];
    },
    // 时序分析-图表-获取series数组、故障区间组
    getSeries (legend, dataY, dataX, faultTimeRangeResult) {
      let series = [];
      this.faultArr = [];
      // 根据legend中data个数，设置series数组
      let isdeviceType11inTab3 = this.isdeviceType11inTab3;
      legend.length > 0 && legend[0].data.forEach((ele, key) => {
        let data = [];
        let statusSignArr = [];
        // 处理接口返回的y轴数据
        dataY.forEach(val => {
          if (ele.pointName == val.pointName && ele.deviceName == val.deviceName) {
            let list = [];
            val.pointValue.forEach((element, index) => {
              // 获取故障持续时间、时间段，低效缺陷-电站 不展示故障
              let alarmArr = isdeviceType11inTab3 ? [] : this.getDurationOrMarkAreaArr(faultTimeRangeResult, index, dataX, true);
              list.push({
                value: element,
                unit: val.unit,
                point: val.point,
                alarmArr: alarmArr,
                pointName: val.pointName,
                deviceName: val.deviceName,
                isFaultPoint: val.isFaultPoint,
                pointType: val.pointType
              });
            });
            data = list;
          }
          // 电站状态信息数组
          if (val.point == 'statusSign') {
            statusSignArr = val.pointValue;
          }
        });
        data.forEach((v, i) => {
          v.statusSign = statusSignArr[i];
        });
        // 获取故障区间遮罩层区域
        // 故障停机-组串，低效缺陷-电站 不展示区间
        let markAreaArr = [];
        if (!((this.type == '1' && this.defaultInfo.deviceTypeShow == '10') || isdeviceType11inTab3)) {
          markAreaArr = this.getDurationOrMarkAreaArr(faultTimeRangeResult, null, dataX, false);
        }
        series.push({
          type: 'line',
          name: ele.deviceName + ele.pointName,
          data: data,
          smooth: true,
          showSymbol: false,
          lineStyle: {
            width: 1
          },
          emphasis: {
            focus: 'series'
          },
          z: legend[0].data.length - key,
          markArea: {
            itemStyle: {
              color: {
                type: 'linear',
                x: 0,
                y: 0,
                x2: 0,
                y2: 1,
                colorStops: [{
                  offset: 0, color: 'rgba(245, 108, 108, 0)' // 0% 处的颜色
                }, {
                  offset: 1, color: '#F56C6C' // 100% 处的颜色
                }],
                global: false // 缺省为 false
              },
              opacity: key == 0 ? 0.2 : 0
            },
            data: markAreaArr,
            emphasis: {
              itemStyle: {
                color: {
                  type: 'linear',
                  x: 0,
                  y: 0,
                  x2: 0,
                  y2: 1,
                  colorStops: [{
                    offset: 0, color: 'rgba(245, 108, 108, 0)' // 0% 处的颜色
                  }, {
                    offset: 1, color: '#F56C6C' // 100% 处的颜色
                  }],
                  global: false // 缺省为 false
                },
                opacity: key == 0 ? 0.2 : 0
              }
            }
          },
          yAxisIndex: this.unitArr.indexOf(ele.unit)
        });
      });
      return series;
    },
    // 时序分析-图表-获取故障区间段信息或者故障区间遮罩层区域，flag为ture时为获取故障时间段信息，false时为获取遮罩区域
    getDurationOrMarkAreaArr (res, index, dataX, flag) {
      let alarmArr = [];
      let markAreaArr = [];
      res.result_data.forEach(ele => {
        let xTime = flag ? new Date(dataX[index]) : '';
        let happenTime = new Date(ele.happenTime);
        let endTime = ele.endTime ? new Date(ele.endTime) : new Date(); ;
        let arr = [];
        if (flag) {
          let duration = '';
          if (xTime >= happenTime && xTime <= endTime) {
            if (ele.endTime) {
              duration = ele.duration + 'h';
            } else {
              duration = ((endTime - happenTime) / 3600000).toFixed(2) + 'h';
            }
            // obj.powerLoss = ele.hasOwnProperty('powerLoss') ?ele.powerLoss : '--'
            this.faultArr.push(dataX[index]);
            alarmArr.push(
              {
                alarmType: ele.alarmType,
                alarmGrade: ele.alarmGrade || '--',
                alarmRemark: ele.alarmRemark,
                alarmReason: ele.alarmReason,
                duration: duration
              }
            );
          }
        } else {
          this.faultArr.forEach(val => {
            xTime = new Date(val);
            if (xTime >= happenTime && xTime <= endTime) {
              arr.push(val);
            }
          });
        }
        if (arr.length > 0) {
          markAreaArr.push([ { xAxis: arr[0] }, { xAxis: arr[arr.length - 1] } ]);
        }
      });
      if (flag) {
        return alarmArr;
      } else {
        return markAreaArr;
      }
    },
    cleanDate () {
      this.sequenceTime = [];
      this.timeDateChange([], false);
    },
    // 阻止冒泡方法
    stopEvenet (e) {
      e = e || window.event;
      if (e.stopPropagation) { // W3C阻止冒泡方法
        e.stopPropagation();
      } else {
        e.cancelBubble = true; // IE阻止冒泡方法
      }
    },
    // 时序分析-日期面板打开、关闭
    openChange (status) {
      if (status) {
        this.timeOptionRange = [];
      }
      setTimeout(() => {
        let isExist = document.getElementsByClassName('health-clean-icon').length == 0;
        if (status && !isExist) {
          if (!document.getElementsByClassName('health-clean-icon')[0].style.top) {
            document.body.appendChild(document.getElementsByClassName('health-clean-icon')[0]);
            document.getElementsByClassName('health-clean-icon')[0].addEventListener('click', this.cleanDate);
          }
          document.getElementsByClassName('health-clean-icon')[0].style.display = 'block';
          let top = document.getElementsByClassName('ant-calendar-picker-container')[0].style.top;
          let left = document.getElementsByClassName('ant-calendar-picker-container')[0].style.left;
          document.getElementsByClassName('health-clean-icon')[0].style.top = top.substring(0, top.length - 2) * 1 + 10 + 'px';
          document.getElementsByClassName('health-clean-icon')[0].style.left = left.substring(0, left.length - 2) * 1 + 510 + 'px';
        } else {
          if (!isExist) document.getElementsByClassName('health-clean-icon')[0].style.display = 'none';
        }
      }, 200);
    },

    // 时序分析-查询条件-日期变化事件
    timeDateChange (val, flag) {
      if (this.sequenceTime.length > 0) {
        this.timeQueryData.startTime = this.sequenceTime[0].format('YYYY-MM-DD');
        this.timeQueryData.endTime = this.sequenceTime[1].format('YYYY-MM-DD');
        let dayIntervel = val[1].diff(val[0], 'day');
        if (dayIntervel > 0) {
          // this.timeIntervalOptions = this.timeIntervalOptionsAll.week;
          this.timeIntervalOptions = this.timeIntervalOptionsAll.day;
        } else {
          this.timeIntervalOptions = this.timeIntervalOptionsAll.day;
        }
      } else {
        this.timeOptionRange = [];
        this.timeQueryData.startTime = '';
        this.timeQueryData.endTime = '';
        this.timeIntervalOptions = this.timeIntervalOptionsAll.day;
      }
      this.timeQueryData.timeInterval = this.timeIntervalOptions[0].value;
      if (flag) {
        this.getWeatherInfo(this.defaultInfo.psId);
        this.getTimeAnalysisData(this.timeQueryData);
      }
    },
    // 时序分析-查询条件-不可选择日期限制
    disabledDate (current) {
      let timeOptionRange = this.timeOptionRange;
      if (timeOptionRange && timeOptionRange.length > 0) {
        return (
          current.diff(timeOptionRange[0], 'days') > 29 ||
          current.diff(timeOptionRange[0], 'days') < -29 ||
          current > moment().endOf('day')
        );
      } else {
        return current > moment().endOf('day');
      }
    },
    // 时序分析-查询条件-日期面板变化
    calendarChange (time) {
      // 当第一时间选中才设置禁用
      this.timeOptionRange = time;
    },
    // 时序分析-查询条件-故障日期样式
    getCurrentClassName (current) {
      if (this.alarmDays.includes(current.format('YYYY/MM/DD'))) {
        return 'topic-date';
      }
      return '';
    },
    // 时序分析-查询条件-典型日期样式
    getTopicalClassName (current) {
      if (this.topicalDays.includes(current.format('YYYY/MM/DD'))) {
        return 'topical-day';
      }
      return '';
    },
    gotoDeepAnalysis () {
      let pointList = this.timeQueryData.pointList.filter(item => {
        return ['1', '4', '5', '7', '26', '23', '24', '37'].includes(item.deviceType) && item.point != 'stop' && item.pointType == '2' && item.pointName;
      });
      let points = pointList.map(item => {
        return {
          deviceType: item.deviceType,
          key: item.point,
          point: item.point,
          pointName: item.pointName,
          unit: item.unit
        };
      });
      points = this.arrUnique(points, 'key') || [];
      let devices = pointList.map(item => {
        return {
          deviceType: item.deviceType,
          psKey: item.psKey
        };
      });
      devices = this.arrUnique(devices, 'psKey');
      if (!devices) {
        devices = [{
          deviceType: this.defaultInfo.deviceType,
          psKey: this.defaultInfo.psKey
        }];
      }
      let types = pointList.map(item => {
        return item.deviceType;
      });
      types = Array.from(new Set(types));
      let list = {
        points: points,
        devices: devices,
        types: types
      };
      let time = this.sequenceTime.length > 0 ? this.sequenceTime : [moment(this.defaultInfo.happenTime), moment(this.defaultInfo.happenTime)];
      console.log('params', { psId: this.defaultInfo.psId, psName: this.defaultInfo.psName, list: list, time: time, source: 2 });
      this.$router.push({
        // name: 'DeepAnalysisTool',
        name: 'dataCenter-insightTool',
        params: { psId: this.defaultInfo.psId, psName: this.defaultInfo.psName, list: list, time: time, source: 2 }
      });
      window.comefromAlarmCenter = true;
    }
  }
};
</script>
<style lang="less" scoped>
:deep(.ant-drawer-body) {
  padding: 0;
}
:deep(.ant-drawer-wrapper-body) {
  overflow: hidden;
}
:deep(.ant-calendar-picker-container) {
  top: 200px !important;
}
.right {
  margin-left: 0;
  width: 100%;
}
.health-clean-icon {
  display: none;
  position: absolute;
  cursor: pointer;
  z-index: 9999;
}
.analysis-title {
  font-weight: 400;
  color: var(--zw-text-2-color--default);
  font-size: 14px;
  padding: 12px 0 16px;
  display: flex;
  align-items: center;

  i {
    margin: 0 5px 0 7px;
  }
  .back-icon {
    cursor: pointer;
  }
  .split-line {
    width: 1px;
    height: 12px;
    background: var(--zw-divider-color--default);
    margin: 0 20px;
    position: relative;
    top: 1px;
  }
  .title {
    color: var(--zw-text-1-color--default);
  }
  .split {
    margin: 0 8px;
    color: var(--zw-text-1-color--default);
  }
  .alarm-type {
    font-weight: 600;
    color: var(--zw-text-2-color--default);
    line-height: 24px;
    font-size: 16px;
  }

}
.analysis-head {
  padding: 16px 24px;
  background: var(--zw-card-bg-color--default);
  box-shadow: 0px 4px 6px 0px rgba(21, 60, 104, 0.09);
  border-radius: 4px 4px 0px 0px;
  .device-name {
    font-weight: 500;
    color: var(--zw-text-1-color--default);
    line-height: 22px;
    font-size: 16px;
    margin-right: 40px;
  }
}
.analysis-body {
  background: var(--zw-card-bg-color--default);
  box-shadow: 0px 4px 6px 0px rgba(21, 60, 104, 0.09);
  border-radius: 4px;
  .seaech-content {
    padding: 24px;
    .search-item {
      margin-right: 40px;
      .search-label {
        margin-right: 10px;

      }
      .search-date {
        width: 240px;
      }
      .search-time {
        width: 166px;
      }

    }
    .weather-info {
      padding: 1px 20px;
      border-radius: 16px;
      background: var(--zw-table-header-bg-color--default);
      width: auto;
      .ant-space {
        cursor: pointer;
      }
    }

  }
  .go-page {
      position: absolute;
      top:24px;
      right: 32px;
      font-weight: 600;
      color: var(--zw-primary-color--default);
      cursor: pointer;
      line-height: 18px;
      padding: 8px 20px ;
      background: var(--zw-primary-common-light-color--default);
      border-radius: 4px;
      border: 1px solid var(--zw-primary-color--default);
      .link {
        margin-right: 8px;
      }

    }
}
.analysis-content {
  margin-top:0;
  width:'calc(100% - 24px)';
  .left {
    width: 300px;
  }
  .analysis-box {
    background: transparent;
  }
}
.operation-icon {
    // position: absolute;
    // right: 24px;
    // top: 14px;
    height: 81px;
    font-size: 22px;
    text-align: right;
    padding: 44px 32px 4px;
    svg {
      margin-left: 6px;
      cursor: pointer;
    }
  }
.chart-content {
  border-radius: 0 0 4px 4px;
  overflow: auto;
  width: 100%;

}
.topical-day {
  position:absolute;
  width:4px;
  height:4px;
  top:-5px;
  left:16px;
  background: var(--zw-primary-color--default);
  border-radius:100%;
}
.weather-popper {
  max-height: 300px;
  .weather-row {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 30px;
    img {
      margin-right: 5px;
    }
  }
}
.weather-icon {
  height: 30px;
  object-fit: contain;
}
.my-date {
  position: relative;
}
.detail-btn {
  position: relative;
  top: -2px;
  border: none;
  color: var(--zw-conduct-color--normal);
  font-size: 18px;
  background: none;
}
.health-icon {
  color: var(--zw-conduct-color--normal);
  font-size: 28px;
}
.health-icon:hover {
  color: var(--zw-primary-color--default) !important;
}
.disabled {
  cursor: not-allowed;
    pointer-events: none;
    opacity: 0.5;
}
</style>
