<template>
  <div class="drawer-form-com" id="orderHallBox">
    <div :class="['drawer-form-content', footShow ? '' : 'drawer-form-content-all']">
      <a-spin size="small" :spinning="loading" style="height: 100%">
        <a-form-model :model="formData" ref="orderHallForm" :labelCol="{ style: 'width: 150px' }"
          :wrapperCol="{ style: 'width: calc(100% - 150px)' }" style='display: flex;flex-direction: column;gap: 24px'>
          <!-- 基本信息 -->
          <BasicInfo :formData.sync="formData" ref='basic' :type="type" :dictMap="dictMap"/>
          <!-- 发电信息 -->
          <GenerationInfo :businessData="formData.businessData" v-if="formData.taskType === '10'" />
          <!-- 设备信息  -->
          <DeviceInfo :formData.sync="formData" :type="type" ref="device" :dictMap="dictMap"
            :isDroneOrder='isDroneOrder'/>
          <!-- 两票信息 -->
          <template v-if="formData.ticketDtoList && formData.ticketDtoList.length">
            <TicketInfo :formData.sync="formData" :type="type" :dictMap="dictMap" :ticketRequired="ticketRequired"
                        :parentId="parentId" :isDisabled="formData.isHangUp == '1'" />
          </template>
          <!-- 执行信息 -->
          <ExecuteInfo ref='executeInfo' :formData.sync="formData" :groupCleanExecuteVo='formData.groupCleanExecuteVo'
            :type="type" :isDisabled="formData.isHangUp == '1'" :dictMap="dictMap"
            :rowInfo="rowInfo" :isDroneOrder='isDroneOrder' :businessData="formData.businessData" />
          <!-- 作业步骤 -->
          <template v-if="formData.stepVoList && formData.stepVoList.length">
            <WorkSteps :formData="formData" :type="type" :isDisabled="formData.isHangUp == '1'"
              :rowInfo="rowInfo" :safeWorkShow="safeWorkShow" />
          </template>
          <DelayDetail v-if="type == '3' && formData.extensionList && formData.extensionList.length > 0" :extensionList="formData.extensionList" :parentId="parentId"></DelayDetail>
          <!-- 终止信息 -->
          <TerminateInfo v-if="formData.flowSts === '5'" :formData="formData" style="margin-top: -24px;" />
          <HangUpReason ref="hangupReason" v-if="step == 1" :formData="formData" />
        </a-form-model>
      </a-spin>
    </div>
    <div class="drawer-form-foot" v-if='footShow'>
      <throttle-button v-if="['3'].includes(type)" :loading="loading" label="返回" @click="$emit('cancel')" />
    </div>
    <div @click="openFlowChart" class="flow-chart-btn">
      <svg-icon iconClass="flow"></svg-icon>
      流程图
    </div>
    <flow-chart-drawer v-if="showDiagram" ref="flowChartDrawer" :parentId="parentId"
      :processInstanceId="formData.processInstanceId" :flowUser="formData.flowUser" />
  </div>
</template>

<script>
import BasicInfo from './basic/BasicInfo';
import DeviceInfo from './device/DeviceInfo';
import ExecuteInfo from './execute/ExecuteInfo';
import TicketInfo from './ticket/TicketInfo';
import initDict from '@/mixins/initDict';
import WorkSteps from './steps/WorkStepsDetail.vue';
import HangUpReason from './HangUpReason';
import orderHallMixin from '../js/mixin';
import { isEmpty, isNumber, clone } from 'xe-utils';
import { gatOrderDetail } from '@/api/distributed/orderHall';
import flowReview from '@/components/erp/activiti/review';

import moment from 'moment';
import TerminateInfo from './terminateInfo/TerminateInfo';
import GenerationInfo from './generation/GenerationInfo';
import { resolvePrecion } from '../js/config';
import TaskAndOrder from '@/mixins/TaskAndOrder';
import DelayDetail from './delay/DelayDetail';
export default {
  name: 'OrderHallForm',
  provide () {
    return {
      trigggleFootShow: this.trigggleFootShow,
      updateOpsWorkOrderCleanVo: this.updateOpsWorkOrderCleanVo,
      validateFields: this.validateFields,
      type: () => this.type,
      assignList: () => this.assignList,
      actualFaultLossDetail: () => this.actualFaultLossDetail
    };
  },
  mixins: [initDict, orderHallMixin, TaskAndOrder],
  components: {
    DelayDetail,
    BasicInfo,
    ExecuteInfo,
    DeviceInfo,
    WorkSteps,
    HangUpReason,
    flowReview,
    TerminateInfo,
    TicketInfo,
    GenerationInfo
  },
  props: {
    parentId: {
      type: String,
      required: true,
      default: ''
    }
  },
  data () {
    return {
      type: '', // 类型
      rowInfo: {}, // 当前行相关信息
      formData: {
        deviceId: undefined, // 设备名称
        deviceNo: undefined, // 设备编号
        defectName: undefined, // 缺陷（故障）名称
        taskType: undefined, // 任务名称
        powerCutRange: undefined,
        defectHandleCondition: undefined,
        executeCycleRange: undefined,
        reason: undefined,
        groupCleanExecuteVo: {
          uploadFileParamsSafe: []
        },
        documentPicFileList: undefined,
        invoicePicFileList: undefined,
        appearanceVoList: [],
        ticketDtoList: [],
        businessData: {
          onElectric: null,
          lossElectricDetail: {}
        }
      },
      loading: false,
      showDiagram: false,
      step: 0,
      showExecuteOrSteps: true,
      // 审批、验收
      footShow: true,
      assignList: [], // 负责人
      ticketRequired: false, // 两票信息是否为必填项标识
      safeWorkShow: true, // 安全措施是否展示
      actualFaultLossDetail: null
    };
  },
  mounted () {
    this.getDictMap(this.initialMaps);
  },
  methods: {
    reset () {
      this.$emit('cancel');
      Object.assign(this.rowInfo, { taskNo: null });
    },
    async init (type, row) {
      this.type = type;
      this.rowInfo = row;
      this.loading = true;
      const data = await this.getDetail(type, row);
      this.afterVisibleChange();
      return `工单${this.enumTypes[type]}-${data.workCode}【${data.flowStsName}】`;
    },
    afterVisibleChange () {
      this.$nextTick(() => {
        const { taskType, workSubclass, defectType } = this.formData;
        // this.getIsNeedTicket({ taskType, workSubclass, defectType }, this.type !== '7');
      });
    },
    async getDetail (type, row) {
      let res;
      try {
        res = await gatOrderDetail({ businessesId: row.id, taskNo: row.taskNo, workCode: row.workCode });
        // this.formData = Object.assign(res.result_data, { ticketDtoList: [] });
        const deviceTypeId = res.result_data.deviceTypeId;
        const newDeviceTypeId = deviceTypeId && deviceTypeId.split(',');
        const stationEnvironment = res.result_data.stationEnvironment || '';
        const riskJobDate = res.result_data.riskJobDate || null;
        const cleanCap = res.result_data.cleanCap || null;
        const actualFaultLossDetail = (res.result_data.businessData && res.result_data.businessData.actualFaultLossDetail) || null;
        this.actualFaultLossDetail = actualFaultLossDetail;

        this.formData = Object.assign({}, res.result_data, {
          deviceTypeId: newDeviceTypeId,
          stationEnvironment: stationEnvironment.split(',').filter(o => !!o),
          riskJobDate: type == '3' ? riskJobDate : (riskJobDate || moment().format('YYYY-MM-DD')),
          // 工单关联两票信息ticketDtoList
          ticketDtoList: res.result_data.ticketVoList || []
        });
        const { planStartTime, planEndTime, findTime, predictRecoverTime, taskType } = res.result_data;

        if (taskType === '10') {
          // 处理应发电量执行时的初始字段
          const clonedBusinessData = clone((this.formData.businessData || {}), true);
          const { actualOnElectric, actualFaultLossElectric, insideFaultLossElectric, actualInsideFaultLossCutElectric, faultLossReason,
            actualRationLossElectric, rationLossElectric, actualRationLossCutElectric, rationLossReason, actualShadowLossElectric, shadowLossReason,
            actualOtherLossElectric, otherLossReason
          } = clonedBusinessData;
          const newBusinessData = Object.assign({}, this.formData.businessData, {
            actualOnElectric: !this.$isEmpty(actualOnElectric) ? actualOnElectric : undefined,
            actualFaultLossElectric: !this.$isEmpty(actualFaultLossElectric) ? actualFaultLossElectric : undefined,
            insideFaultLossElectric: !this.$isEmpty(insideFaultLossElectric) ? insideFaultLossElectric : undefined,
            actualInsideFaultLossCutElectric: !this.$isEmpty(actualInsideFaultLossCutElectric) ? actualInsideFaultLossCutElectric : undefined,
            faultLossReason: !this.$isEmpty(faultLossReason) ? faultLossReason : undefined,
            actualRationLossElectric: !this.$isEmpty(actualRationLossElectric) ? actualRationLossElectric : undefined,
            rationLossElectric: !this.$isEmpty(rationLossElectric) ? rationLossElectric : undefined,
            actualRationLossCutElectric: !this.$isEmpty(actualRationLossCutElectric) ? actualRationLossCutElectric : undefined,
            rationLossReason: !this.$isEmpty(rationLossReason) ? rationLossReason : undefined,
            actualShadowLossElectric: !this.$isEmpty(actualShadowLossElectric) ? actualShadowLossElectric : undefined,
            shadowLossReason: !this.$isEmpty(shadowLossReason) ? shadowLossReason : undefined,
            actualOtherLossElectric: !this.$isEmpty(actualOtherLossElectric) ? actualOtherLossElectric : undefined,
            otherLossReason: !this.$isEmpty(otherLossReason) ? otherLossReason : undefined
          });
          // 处理kwh--->万kwh
          const newData = resolvePrecion(newBusinessData, 'divide');
          this.$set(this.formData, 'businessData', newData);
        }
        // 非故障、缺陷 计划日期
        if (taskType != '10' && planStartTime && planEndTime) {
          this.$set(this.formData, 'planTime', [planStartTime, planEndTime]);
        } else if (taskType == '10' && findTime && predictRecoverTime) {
          this.$set(this.formData, 'planTime', [findTime, predictRecoverTime]);
        } else {
          this.$set(this.formData, 'planTime', null);
        }
        // 非 清洗专项(purchasesWay：采购方式（清洗专项）)、组串清洗、下网电费、系统维护、抢修、物资代采、保险购买、站荣站貌、应发电量 获取sop作业步骤
        const notGetSOPTypes = ['13', '14', '4', '15', '16', '17', '18', '10', ...this.renewalTaskType];
        if (!this.getStatus() && isEmpty(res.result_data.stepVoList) && type == 7 &&
          !res.result_data.purchasesWay && !notGetSOPTypes.includes(res.result_data.taskType)) {
          this.getStepList();
        }
        // 0426 清洗专项执行周期
        if (this.formData.executeCycleStartDate) {
          const { executeCycleStartDate, executeCycleEndDate } = this.formData;
          this.$set(this.formData, 'executeCycleRange', [executeCycleStartDate, executeCycleEndDate]);
        } else {
          this.$set(this.formData, 'executeCycleRange', null);
        }
        // 服务周期
        if (this.formData.serverCycleEndDate) {
          const { serverCycleStartDate, serverCycleEndDate } = this.formData;
          this.$set(this.formData, 'serverCycleRange', [serverCycleStartDate, serverCycleEndDate]);
        } else {
          this.$set(this.formData, 'serverCycleRange', null);
        }
        // 组串清洗初始化
        if (!this.formData.groupCleanExecuteVo) Object.assign(this.formData, { groupCleanExecuteVo: {} });
        // 缺陷/故障工程遗留默认为否，防止后端未返回的兜底策略
        if (['7', '8'].includes(this.formData.taskType)) {
          const { isEngLegacy } = this.formData;
          Object.assign(this.formData, {
            isEngLegacy: isNumber(isEngLegacy) ? isEngLegacy : 0
          });
        }
        this.loading = false;
      } catch (err) {
        this.loading = false;
      }
      return new Promise((resolve) => {
        resolve(res.result_data);
      });
    },
    openFlowChart () {
      this.showDiagram = true;
      const _self = this;
      this.$nextTick(() => {
        _self.$refs.flowChartDrawer.openView();
      });
    },
    // 清洗专项 去除底部非现场执行步骤按钮
    trigggleFootShow (isShow) {
      this.footShow = isShow;
    },
    //  执行完现场执行替换当前表单
    updateOpsWorkOrderCleanVo (orderExecuteRate) {
      // this.formData.cleanDetailVo.opsWorkOrderCleanVo = opsWorkOrderCleanVo;
      this.formData.orderExecuteRate = orderExecuteRate || 0;
    },
    validateFields (fields) {
      const newFields = [].concat(fields);
      this.$refs.orderHallForm.validateField(newFields);
    },
  },
  computed: {
    // 是否无人机派单
    isDroneOrder () {
      const { opsSourceCategory } = this.formData;
      return opsSourceCategory === '3';
    }
  }
};
</script>
<style lang="less" scoped>
#orderHallBox {
  padding: 12px 0 0;

  .drawer-form-content {

    //padding-right: 0;
    .ant-alert.ant-alert-warning {
      background-color: #F9E0C7;
      margin-right: -28px;

      .ant-alert-message {
        font-size: 14px;
        font-weight: 400;
        color: rgba(0, 0, 0, 0.9);
      }

      :deep(.ant-alert-icon) {
        color: #ED7B2F;
      }

      :deep(.ant-alert-close-icon) {
        right: 22px;
      }
    }
  }

  .drawer-form-content-all {
    height: 100%;
  }

  :deep(.ant-form-item) {
    // width: 100%;
    display: flex;
  }
}
</style>

<style lang="less" scoped>
.view_detail_box {
  padding: 8px 0 0;

  .title_box_top {
    font-size: 18px;
    font-weight: 550;
    line-height: 25px;
    padding-bottom: 20px;
    display: flex;
    flex-flow: row nowrap;
    justify-content: space-between;
  }
}

:deep(.ant-form-item) {
  width: 100%;
  display: inline-flex;
}

.title-box {
  padding-bottom: 8px;
}

.ant-dropdown-link {
  margin-right: 16px;
}

.ant-dropdown {
  .ant-dropdown-menu {
    margin-bottom: 10px;
    text-align: center;

    &::after,
    &::before {
      display: none !important;
    }
  }

  &::before,
  &::after {
    display: none;
  }
}

:root[data-theme='dark'] {
  #orderHallBox {
    :deep(.ant-alert.ant-alert-warning) {
      .ant-alert-message {
        color: rgba(0, 0, 0, 0.9) !important;
      }

      .ant-alert-close-icon {
        .anticon.anticon-close {
          color: rgba(0, 0, 0, 0.6) !important;
        }
      }
    }
  }

  .line_height .left {
    color: #fff !important;
  }

}
</style>
