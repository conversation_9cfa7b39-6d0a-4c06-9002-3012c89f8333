<template>
  <a-modal :title="title" :width="800" :visible="visible" :confirmLoading="confirmLoading" @ok="handleOk"
    @cancel="handleCancel"  okText= "确定" cancelText="关闭" wrapClassName="ant-modal-cust-warp"
    style="top:5%;height: 85%;overflow-y: hidden">
    <a-spin :spinning="confirmLoading">
      <a-form :form="form">
        <a-form-item :labelCol="labelCol" :wrapperCol="wrapperCol" label="角色名称">
          <a-input placeholder="请输入角色名称" v-decorator.trim="[ 'roleName', validatorRules.roleName]" autocomplete="off"/>
        </a-form-item>
        <a-form-item v-if="roleDisabled" :labelCol="labelCol" :wrapperCol="wrapperCol" label="角色编码">
          <a-input placeholder="请输入角色编码" :disabled="roleDisabled"
           v-decorator.trim="[ 'roleCode']" />
        </a-form-item>
        <a-form-item :labelCol="labelCol" :wrapperCol="wrapperCol" label="描述">
          <a-textarea :auto-size="{ minRows: 5, maxRows: 5}" v-decorator="[ 'description', validatorRules.description ]" />
        </a-form-item>
      </a-form>
    </a-spin>
  </a-modal>
</template>

<script>
import pick from 'lodash.pick';
import {
  addRole,
  editRole,
  duplicateCheck
} from '@/api/api';

export default {
  name: 'RoleModal',
  data () {
    return {
      title: '操作',
      visible: false,
      roleDisabled: false,
      model: {},
      labelCol: {
        xs: {
          span: 24
        },
        sm: {
          span: 5
        }
      },
      wrapperCol: {
        xs: {
          span: 24
        },
        sm: {
          span: 16
        }
      },
      confirmLoading: false,
      form: this.$form.createForm(this),
      validatorRules: {
        roleName: {
          rules: [{
            required: true,
            message: '请输入角色名称!'
          },
          {
            min: 2,
            max: 30,
            message: '长度在 2 到 30 个字符',
            trigger: 'blur'
          }
          ]
        },
        description: {
          rules: [{
            min: 0,
            max: 126,
            message: '长度不超过 126 个字符',
            trigger: 'blur'
          }]
        }
      }
    };
  },
  created () {},
  methods: {
    add () {
      this.edit({});
    },
    edit (record) {
      this.form.resetFields();
      this.model = Object.assign({}, record);
      this.visible = true;

      // 编辑页面禁止修改角色编码
      if (this.model.id) {
        this.roleDisabled = true;
      } else {
        this.roleDisabled = false;
      }
      this.$nextTick(() => {
        this.form.setFieldsValue(pick(this.model, 'roleName', 'description', 'roleCode'));
      });
    },
    close () {
      this.$emit('close');
      this.visible = false;
    },
    handleOk () {
      const that = this;
      // 触发表单验证
      this.form.validateFields((err, values) => {
        if (!err) {
          that.confirmLoading = true;
          let formData = Object.assign(this.model, values);
          let obj;
          console.log(formData);
          if (!this.model.id) {
            obj = addRole(formData);
          } else {
            obj = editRole(formData);
          }
          obj.then((res) => {
            if (res.success) {
              that.$message.success(res.message);
              that.$emit('ok');
            } else {
              that.$message.warning(res.message);
            }
          }).finally(() => {
            that.confirmLoading = false;
            that.close();
          });
        }
      });
    },
    handleCancel () {
      this.close();
    },
    validateRoleCode (rule, value, callback) {
      if (/[\u4E00-\u9FA5]/g.test(value)) {
        callback(new Error('角色编码不可输入汉字!'));
      } else {
        var params = {
          tableName: 'sys_role',
          fieldName: 'role_code',
          fieldVal: value,
          dataId: this.model.id
        };
        duplicateCheck(params).then((res) => {
          if (res.success) {
            callback();
          } else {
            callback(res.message);
          }
        });
      }
    }

  }
};
</script>

<style scoped>
</style>
