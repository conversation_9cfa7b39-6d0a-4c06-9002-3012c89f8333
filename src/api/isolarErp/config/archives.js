import { axios } from '@/utils/erp-request';
import { postAction } from '@/api/manage';
const baseUrl = process.env.VUE_APP_API_BASE_URL;

// 获取电站档案列表
export const getPowerStationArchivesList = (params) => postAction(baseUrl + '/system/v1/erpPowerStationArchives/getErpPowerStationArchivesList', params);
// 通过电站档案Id获取电站档案信息
export const getPowerStationArchivesByPsaId = (params) => postAction(baseUrl + '/system/v1/erpPowerStationArchives/getErpPowerStationArchivesByPsaId', params);
// 逐级加载区域树

export const selectAreaLazily = (params) => postAction(baseUrl + '/system/v1/erpPowerStationArchives/selectAreaLazily', params);
// 获取可关联的实体电站
export const selectEntityStation = (params) => postAction(baseUrl + '/system/v1/erpPowerStationArchives/selectEntityStation', params);
// 获取solargis电站id
export const selectDeviceSn = (params) => postAction(baseUrl + '/system/v1/erpPowerStationArchives/selectDeviceSn', params);
// 获取已选电站信息
export const selectCheckedEntityStation = (params) => postAction(baseUrl + '/system/v1/erpPowerStationArchives/selectCheckedEntityStation', params);
// 新增信息
export const insert = (params) => postAction(baseUrl + '/system/v1/erpPowerStationArchives/insert', params);
// 编辑基本信息
export const updateBasic = (params) => postAction(baseUrl + '/system/v1/erpPowerStationArchives/updateBasic', params);
// 编辑基本信息
export const updateDetail = (params) => postAction(baseUrl + '/system/v1/erpPowerStationArchives/updateDetail', params);

// 删除
export const deleteData = (params) => postAction(baseUrl + '/system/v1/erpPowerStationArchives/delete', params);

// 删除前检查
export const checkDelete = (params) => postAction(baseUrl + '/system/v1/erpPowerStationArchives/checkDelete', params);

// 导出
export const exportData = (params) => postAction(baseUrl + '/system/v1/erpPowerStationArchives/exportData', params);
/* 业主项目 - 配置api */

// 业主-项目公司配置保存
export const ownerProjectSave = (params) => postAction(baseUrl + '/system/v2/ownerProject/save', params);
// 业主-项目公司配置修改
export const ownerProjectEdit = (params) => postAction(baseUrl + '/system/v2/ownerProject/edit', params);
// 业主-项目公司配置删除
export const ownerProjectDelete = (params) => postAction(baseUrl + '/system/v2/ownerProject/delete', params);
// 业主-分页查询
export const getOwnerByPage = (params) => postAction(baseUrl + '/system/v2/ownerProject/ownerPage', params);
// 项目公司分页查询
export const getProjectByPage = (params) => postAction(baseUrl + '/system/v2/ownerProject/projectPage', params);
// 业主-下拉
export const getOwner = (params) => postAction(baseUrl + '/system/v2/ownerProject/owner', params);
// 项目公司-下拉
export function getProjectByOwner (data) {
  return axios({
    url: '/system/v2/ownerProject/project',
    method: 'post',
    data
  });
}
