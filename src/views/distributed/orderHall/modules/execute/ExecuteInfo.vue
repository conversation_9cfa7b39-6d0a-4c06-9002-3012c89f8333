<template>
  <div v-if="executeInfoShow">
    <ExecuteInfoDetail :baseForm="formData" :dict='dictMap' :type='type'/>
  </div>
</template>

<script>

import ExecuteInfoDetail from './ExecuteInfoDetail.vue';
import TaskAndOrder from '@/mixins/TaskAndOrder';
export default {
  name: 'execute-info',
  mixins: [TaskAndOrder],
  provide () {
    return {
      coverFirstFrame: this.coverFirstFrame,
      providePlayFn: this.providePlayFn,
      getFileExtension: this.getFileExtension
    };
  },
  components: {
    ExecuteInfoDetail
  },
  props: {
    type: {
      type: String,
      default: () => {
        return null;
      }
    },
    // 整个工单详情
    formData: {
      type: Object,
      default: () => {
        return {
          serverCycleRange: undefined
        };
      }
    },
    // 组串清洗详情
    groupCleanExecuteVo: {
      type: Object,
      default: () => ({
        groupCleanState: null,
        groupCleanReason: undefined,
        groupCleanRemark: undefined,
        groupCleanDate: undefined,
        uploadFileParams: [],
        uploadFileParamsSafe: [],
        picBeforeClean: [],
        picAfterClean: []
      })
    },
    isDisabled: {
      type: Boolean,
      default: false
    },
    dictMap: {
      type: Object,
      default: () => {
        return {};
      }
    },
    rowInfo: {
      type: Object,
      default: () => {
        return {};
      }
    },
    businessData: {
      type: Object,
      default: () => ({
        onElectric: undefined
      })
    }
  },
  data () {
    return {
    };
  },
  mounted () {},
  methods: {
  },
  computed: {
    executeInfoShow () {
      const type = this.type;
      const formData = this.formData;
      if (['6', '5'].includes(type)) {
        return false;
      }
      // otherSts:0标识迁移数据，代表流程已走完
      if (type == '3' && ['0', '3', '4', '5', '6', '9', '10'].includes(formData.otherSts)) {
        if (['1', ...this.cleanType].includes(formData.taskType) && formData.purchasesWay) {
          return false;
        }
        return true;
      }
      if ((type == 7 || type == 8) && ['3', '4', '5', '6', '9'].includes(formData.otherSts)) {
        // 清洗专项兼容
        if (['1', ...this.cleanType].includes(formData.taskType) && formData.purchasesWay) {
          return false;
        }
        return true;
      }
      if (type === '4' && ['9', '10'].includes(formData.otherSts)) {
        return true;
      }
      return false;
    }
  }
};
</script>

<style lang="less" scoped>
  :deep(.video-box){
    width: 102px;
    height: 102px;
    box-sizing: border-box;
    padding: 9px;
    border: 1px dashed #d9d9d9;
    border-radius: 4px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;

    .video-close{
      position: absolute;
      width: 20px;
      height: 20px;
      top: 0;
      right: 0;
    }
    .video-poster{
      border-radius: 2px;
      width: 100%;
      height: 100%;
      object-fit: cover;
      filter:brightness(0.7);
      z-index: -1;
    }
    .video-play{
      width: 20px;
      height: 20px;
      position: absolute;

    }
  }
  .multiple-upload-flex{
    display: flex;
    .ant-form-item{
      width: unset;
      :deep(.ant-upload-text){
        font-size: 12px;
      }
    }
  }
</style>
