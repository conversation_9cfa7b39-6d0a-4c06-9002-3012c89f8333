// 用于监听主题颜色变化
import { mapState } from 'vuex';
import { bus, tokenLose, microFromName } from './index';
import { permissionRouterGo } from '@/utils';
import store from '@/store';
const mixin = {
  computed: {
    ...mapState({
      navTheme: state => state.app.theme,
      primaryColor: state => state.app.color
    })
  },
  watch: {
    primaryColor (color) {
      bus.$emit('change-color', {
        color,
        theme: this.navTheme
      });
    }
  },
  created () {
    Object.assign(this.propsData, {
      tokenLose,
      theme: this.navTheme,
      microFrom: microFromName,
      color: this.primaryColor
    });
    let _this = this;
    bus.$on('sub-emit', function (name, type, params) {
      console.log('子应用回调', name, type, params);
      _this.busOn(type, params);
    });
  },
  beforedestory () {
    let _this = this;
    bus.$off('sub-emit', function (name, type, params) {
      console.log('子应用回调', name, type, params);
      _this.busOn(type, params);
    });
  },
  methods: {
    busOn (model, params) {
      switch (model) {
        case '1':
          // this.$router.push({ path: params });
          permissionRouterGo(params, this);
          break;
        case '2':
          let { type, data, path } = params;
          let $el = this.$refs.wujieRef;
          if (!$el) {
            setTimeout(() => {
              this.busOn(model, params);
            }, 50);
            return;
          }
          $el.init(type, data, path);
          break;
        case '3':
          store.commit('SET_WUJIE', params);
          break;
      }
    }
  }
};

export { mixin };
