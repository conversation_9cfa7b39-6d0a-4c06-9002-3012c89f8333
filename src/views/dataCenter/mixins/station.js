import location from '../modules/station/location.vue';
import { areaTree } from '@/api/isolarErp/safetyquality/safetyRisk';
import { getSystemCodeList } from '@/api/health/healthapi.js';
import { RecycleScroller } from 'vue-virtual-scroller';
import 'vue-virtual-scroller/dist/vue-virtual-scroller.css';
export const StationMixins = {
  components: {
    location,
    RecycleScroller
  },
  data () {
    const validSn = (rule, value, callback) => {
      let regex = /^[0-9A-Za-z]+$/;
      if (!value) {
        callback(new Error('请输入通讯模块SN'));
      } else {
        if (regex.test(value)) {
          callback();
        } else {
          callback(new Error('只能输入数字和字母'));
        }
      }
    };
    return {
      options: [],
      baseForm: {},
      baseFormRules: {
        validFlag: [{
          required: true,
          message: '请选择电站状态'
        }]
      },
      collectorFormRules: {
        collectorName: [{
          required: true,
          message: '请输入采集器名称'
        }],
        factory: [{
          required: true,
          message: '请输入厂家名称'
        }],
        purpose: [{
          required: true,
          message: '请选择用途'
        }],
        sn: [{
          required: true,
          validator: validSn
        }]
      },
      saveFlag: false,
      list: [],
      purposeOptions: []
    };
  },
  created () {
    this.queryArea();
    this.getPurposeOptions();
  },
  methods: {
    // 查询范围 下拉宽内容 国省市区
    queryArea () {
      areaTree({}).then((res) => {
        if (res.result_code == '1') {
          this.options = res.result_data;
          // if (this.areaCode) {
          //   this.place = this.familyTree(res.result_data, this.areaCode);
          // }
        }
      });
    },
    getPurposeOptions () {
      getSystemCodeList({ firstTypeCode: '0213' }).then(res => {
        this.purposeOptions = res.result_data['0213'];
      });
    },
    setLoaction () {
      this.$refs.location.createMap();
    },
    setLngLat (lng, lat) {
      this.baseForm.longitude = lng;
      this.baseForm.latitude = lat;
      if (this.baseInfoChange) {
        this.baseInfoChange('经度', lng);
        this.baseInfoChange('纬度', lat);
      }
      this.$refs.baseForm.validateField(['longitude', 'latitude']);
    },
    getLabel (val, options) {
      if (options) {
        if (options.length > 1) {
          let arr = options.filter((item, index) => { return index != 0; });
          arr = arr.map(item => { return item.label; });
          this.baseForm.psLocation = arr.join('');
        } else {
          this.baseForm.psLocation = options[0].label;
        }
      } else {
        this.baseForm.psLocation = val.toString();
      }
      if (this.baseInfoChange) {
        this.$nextTick(() => {
          this.baseForm.psLocation && this.baseInfoChange('所属国家(省/市/区)', this.baseForm.psLocation);
          this.baseForm.psLocationList && this.baseInfoChange('所属国家(省/市/区)id', this.baseForm.psLocationList.toString());
        });
      }
    },
    addForm () {
      this.list.push({
        factory: '',
        purpose: undefined,
        sn: '',
        description: '',
        state: '0',
        overTime: '1',
        isDelete: 0,
        collectorName: '',
        key: this.list.length + new Date().getTime()
      });
    },
    deleteForm (index) {
      this.list.splice(index, 1);
    },
    scrollEvent () {
      this.clearSelect();
      if (this.saveFlag) {
        this.list.forEach((item, index) => {
          if (this.$refs['collectorForm' + index]) {
            this.$refs['collectorForm' + index].validateField(['factory', 'purpose', 'sn']);
            this.$forceUpdate();
          }
        });
      }
    },
    clearSelect () {
      this.list.forEach((item, index) => {
        if (this.$refs['select' + index]) {
          this.$refs['select' + index].blur();
        }
      });
    }
  }
};
