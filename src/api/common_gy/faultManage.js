import {
  axios
} from '@/utils/gy-request';

export function getFaultList (parameter) {
  return axios({
    url: '/default/management/faultDaily/list',
    method: 'post',
    data: parameter
  });
}

export function faultsDelete (parameter) {
  return axios({
    url: '/default/management/faultDaily/delete',
    method: 'post',
    data: parameter
  });
}

export function insertOrUpdate (parameter) {
  return axios({
    url: '/default/management/faultDaily/insertOrUpdate',
    method: 'post',
    data: parameter
  });
}

export function transferOrder (parameter) {
  return axios({
    url: '/default/management/faultDaily/transferOrder',
    method: 'post',
    data: parameter
  });
}

export function getDetail (parameter) {
  return axios({
    url: '/default/management/faultDaily/detail',
    method: 'post',
    data: parameter
  });
}

export function getFaultName (parameter) {
  return axios({
    url: '/common/management/getFaultNameList',
    method: 'post',
    data: parameter
  });
}

export function getFaultGradingList (parameter) {
  return axios({
    url: '/default/management/faultGrading/List',
    method: 'post',
    data: parameter
  });
}

export function getDeviceTypeTree (parameter) {
  return axios({
    url: '/common/management/getDeviecTypeTree',
    method: 'post',
    data: parameter
  });
}

export function getGradingDetail (parameter) {
  return axios({
    url: '/default/management/faultGrading/detail',
    method: 'post',
    data: parameter
  });
}

export function deleteGradingDetail (parameter) {
  return axios({
    url: '/default/management/faultGrading/delete',
    method: 'post',
    data: parameter
  });
}

export function insertGrading (parameter) {
  return axios({
    url: '/default/management/faultGrading/insertOrUpdate',
    method: 'post',
    data: parameter
  });
}

export function getPSByUserAccount (parameter) {
  return axios({
    url: '/common/management/getPSByUserAccount',
    method: 'post',
    data: parameter
  });
}

export function getDeviceNameList (parameter) {
  return axios({
    url: '/common/management/getDeviceNameList',
    method: 'post',
    data: parameter
  });
}

export function getDeviceIdList (parameter) {
  return axios({
    url: '/common/management/getDeviceIdList',
    method: 'post',
    data: parameter
  });
}
export function getDeviecClassByDeviceId (parameter) {
  return axios({
    url: '/common/management/getDeviecClassByDeviceId',
    method: 'post',
    data: parameter
  });
}
export function userListByPSId (parameter) {
  return axios({
    url: '/common/management/userListByPSId',
    method: 'post',
    data: parameter
  });
}
export function distributeUserListByPSId (parameter) {
  return axios({
    url: '/common/management/distributeUserListByPSId',
    method: 'post',
    data: parameter
  });
}

export function getDailyWork (parameter) {
  return axios({
    url: '/theWorkbench/getDailyWork',
    method: 'post',
    data: parameter
  });
}

export function acceptanceDailyWork (parameter) {
  return axios({
    url: '/theWorkbench/acceptanceDailyWork',
    method: 'post',
    data: parameter
  });
}

export function getUserInfoByUserName (parameter) {
  return axios({
    url: '/common/management/getUserInfoByUserName',
    method: 'post',
    data: parameter
  });
}

export function distributedDailyWork (parameter) {
  return axios({
    url: '/theWorkbench/distributedDailyWork',
    method: 'post',
    data: parameter
  });
}

export function getUserInfo (parameter) {
  return axios({
    url: '/common/management/getUserInfoByName',
    method: 'post',
    data: parameter
  });
}
export function adjustment (parameter) {
  return axios({
    url: '/default/management/faultGrading/adjustment',
    method: 'post',
    data: parameter
  });
}
export function adjustmentBox (parameter) {
  return axios({
    url: '/default/management/faultGrading/adjustmentBox',
    method: 'post',
    data: parameter
  });
}
