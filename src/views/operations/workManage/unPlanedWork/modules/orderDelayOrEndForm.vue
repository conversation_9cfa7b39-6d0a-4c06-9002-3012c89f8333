<template>
  <!-- 延期 终止 工单表单 -->
  <div class="drawer-form-com" :class="{'unique-padding': model.type!=1 && model.type!=2}">
    <div class="drawer-form-content">
      <a-spin :spinning="loading">
        <!-- 基本信息 -->
        <baseDetail ref="editRegister" :type="model.type" />

        <!-- 延期或终止 -->
        <div v-if="order.taskType">
          <delayOrEnd ref="orderDelayOrEnd" :delayOrEndType="delayOrEndType" :disabled="['3','4'].includes(model.type)" />
        </div>
      </a-spin>
    </div>

    <div @click="openFlowChart" class="flow-chart-btn">
      <svg-icon iconClass="flow"></svg-icon>
      流程图
    </div>

    <flow-chart-drawer
      v-if="showDiagram"
      ref="flowChartDrawer"
      :parentId="parentId"
      :processInstanceId="order.procInstId"
      :processDefinitionKey="order.processDefinitionKey"
      :flowUser="order.flowUser" />

    <!-- 编辑 -->
    <div v-if="model.type == '2'" class="drawer-form-foot">
      <throttle-button label="提交" :loading="loading" @click="delayOrEndSubmit()" />
    </div>
    <!--  详情 -->
    <div v-else-if="model.type == '3'" class="drawer-form-foot">
      <throttle-button label="返回" type="info" @click="cancel" />
    </div>
    <!--  审批 -->
    <div v-else-if="model.type == '4'" class="drawer-form-foot">
      <throttle-button label="通过" :loading="loading" @click="checkOrder('1')" />
      <throttle-button label="退回" :loading="loading" @click="checkOrder('0')" />
      <throttle-button label="取消" :disabled="loading" type="info" @click="cancel" />
    </div>

    <!--  审批 -->
    <flow-review v-model="showAll" :visible="showAll" :type="examineType" @reviewFlow="reviewFlow" />
  </div>
</template>

<script>
import moment from 'moment';
import flowReview from '@/components/erp/activiti/review';
import baseDetail from './baseDetail';
import initDict from '@/mixins/initDict';
import delayOrEnd from './orderDelayOrEnd.vue';
import { getWorkDetail, delayOrTerminateDetail, delayOrTerminateApprove } from '@/api/operations/digitalOrder';

export default {
  name: 'orderForm',
  mixins: [initDict],
  components: {
    flowReview,
    baseDetail,
    delayOrEnd
  },
  props: {
    dict: {
      type: Object,
      default: () => {
        return {};
      }
    },
    /* 抽屉挂在的DOM节点ID，必要参数 */
    parentId: {
      type: String,
      required: true,
      default: ''
    }
  },
  provide () {
    return {
      type: () => this.model.type,
      order: () => this.order,
      orderSource: () => this.order.orderSource
    };
  },
  data () {
    return {
      loading: false,
      order: {
        id: undefined,
        processDefinitionKey: ''
      }, // 工单详情信息
      model: {
        show_model: false, // 是否显示模态框
        title: '缺陷管理新增',
        type: '' // type 新增：1  编辑：2  详情: 3  审批: 4  指派: 5  领取: 6  处理: 7  验收: 8 计划转工单的处理：10
      },
      delayOrEndShow: false,
      delayOrEndType: null,
      // 审批、验收
      showAll: false,
      examineType: null,
      showDiagram: false,
      processInstanceId: '',
      disabled: true
    };
  },
  computed: {},
  methods: {
    moment,
    // 初始化 type 编辑：2  详情: 3  审批: 4
    async init (type, row) {
      this.model.type = type;
      this.row = row;
      this.loading = true;
      let res = await delayOrTerminateDetail({ businessesId: row.id });
      let resData = res.result_data;
      this.delayOrEndType = resData.flowType;
      this.order = Object.assign({}, this.order, resData);
      res = await getWorkDetail({ id: resData.erpPdcaWorkOrderId });
      let resultData = res.result_data;
      delete resultData.procInstId;
      delete resultData.flowUser;
      this.loading = false;
      this.order = Object.assign({}, this.order, resultData);
      this.order.stationEnvironment = this.order.stationEnvironment ? Array.from(new Set(this.order.stationEnvironment.split(','))) : [];
      this.order.uniqueType = type;
      this.order.step = resultData.flowDetail && resultData.flowDetail.taskDefKey
        ? Number(resultData.flowDetail.taskDefKey.split('act')[1]) : 0;
      this.$nextTick(() => {
        this.setRegister(type, this.order);
      });
      return this.getTitleName(type, row);
    },

    getTitleName (type, row) {
      let title = '';
      let delayOrEnd = this.delayOrEndType == '0' ? '工单延期' : '工单终止';
      switch (type) {
        case '2':
          title = '编辑';
          break;
        case '3':
          title = '详情';
          break;
        case '4':
          title = '审批';
          break;
      }
      title = delayOrEnd + title + '-' + this.order.workCode;
      return title;
    },
    // 打开流程图
    openFlowChart () {
      this.showDiagram = true;
      this.$nextTick(() => {
        this.$refs.flowChartDrawer.openView();
      });
    },
    /**
       * 设置form 表单
       * params {type:String} 类型
       * params {register:Object} 表单详情
       */
    setRegister (type, register) {
      let self = this;
      this.$nextTick(() => {
        if (type != 1) {
          self.$refs.editRegister.initData(register, type);
        }
        setTimeout(() => {
          self.$refs.editRegister.initRegister(type == '1' ? null : register);
        }, 50);

        self.loading = false;
      });
    },

    getCommonParams () {
      let row = this.row;
      return {
        businessesId: row.id,
        taskId: row.taskId,
        taskDefKey: row.taskDefKey,
        processInstanceId: row.processInstanceId,
        processDefKey: row.processDefKey,
        otherSts: row.otherSts,
        flowSts: row.flowSts
      };
    },

    // 退回后编辑 提交
    delayOrEndSubmit () {
      this.loading = true;
      this.$refs.orderDelayOrEnd.saveDelayOrEnd().then((params) => {
        let map = {
          ...this.getCommonParams(),
          ...params
        };
        delayOrTerminateApprove(map).then((res) => {
          this.loading = false;
          this.$message.success(res.message ? res.message : '操作成功');
          this.cancel();
        }).catch(() => {
          this.loading = false;
        });
      }).catch(() => {
        this.loading = false;
      });
    },
    // 显示审批弹窗
    checkOrder (type) {
      this.examineType = type; // 1、通过 0、退回
      this.showAll = true;
    },
    // 审批
    reviewFlow (message, type) {
      this.loading = true;
      let map = {
        auditStatus: type,
        auditOpinion: message,
        auditStatusType: type,
        ...this.getCommonParams()
      };
      delayOrTerminateApprove(map).then((res) => {
        this.examineType = null;
        this.showAll = false;
        this.$nextTick(() => {
          this.$message.success(res.message ? res.message : '操作成功');
          this.cancel();
        });
      }).catch(() => {
        this.loading = false;
      });
    },
    // 取消按钮事件
    cancel (istrue) {
      this.$emit('cancel', istrue);
      if (!istrue) {
        this.reset();
      }
    },
    // 数据重置
    reset () {
      let dict = { ...this.dict, ...this.dictMap };
      Object.assign(this.$data, this.$options.data());
      this.dictMap = dict;
    }
  }
};
</script>

<style lang="less" scoped>
  :deep(.ant-form-item) {
    width: 100%;
    display: inline-flex;
  }

  .unique-padding {
    padding: 12px 0 0;

    .drawer-form-content {
      padding-right: 12px;
    }
  }

  :deep(.ant-spin-nested-loading) {
    height: 100%;
  }
</style>
