<template>
  <div :title="dicveLabel">
    <a-cascader :allowClear="allowClear" :change-on-select="changeOnSelect" :style="{width:'100%'}" v-model="dicveType" :options="options"
      :disabled="disabled" :placeholder="placeholder" :getPopupContainer="(node) => node.parentNode"
      @change="deviceTypeChange">
    </a-cascader>
  </div>
</template>

<script>
export default {
  model: {
    prop: 'value',
    event: 'change'

  },
  props: {
    value: [Array, Number, Object],
    options: {
      type: [Array, Object],
      default: () => {
        return [];
      }
    },
    placeholder: {
      default () {
        return '请选择设备类型';
      }
    },
    disabled: {
      default () {
        return false;
      }
    },
    allowClear: {
      type: Boolean,
      default: true
    },
    changeOnSelect: {
      type: Boolean,
      default: true
    }
  },
  data () {
    return {
      dicveType: undefined,
      dicveLabel: ''
    };
  },
  watch: {
    value: {
      handler (val) {
        this.dicveType = val;
        this.getLabel(val);
      },
      deep: true,
      immediate: true
    }
  },
  methods: {
    getLabel (val) {
      const self = this;
      // 延时-为了防止options数据加载过慢
      window.setTimeout(function () {
        if ((val instanceof Array) && (self.options instanceof Array)) {
          let label = [];
          let options = JSON.parse(JSON.stringify(self.options));
          val.forEach(item => {
            for (let option of options) {
              if (option.value == item) {
                label.push(option.label);
                options = option.children;
                break;
              }
            }
          });
          self.dicveLabel = label.join(' / ');
        } else {
          self.dicveLabel = '';
        }
      }, 1000);
    },
    deviceTypeChange (val, options) {
      this.$emit('change', val, options);
      this.getLabel(val);
    },
    popupVisibleChange (val) {
      try {
        if (!val && this.dicveType.length <= 1) {
          // this.$message.warning('请选择二级或二级以上的设备类型')
          this.dicveType = undefined;
          this.dicveLabel = '';
        } else if (!val) {
          this.$emit('change', this.dicveType);
          this.getLabel(val);
        }
      } catch (error) {

      }
    }
  }
};
</script>
