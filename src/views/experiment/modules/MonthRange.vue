<template>
  <!-- 月度区间 -->
  <a-range-picker
    v-model="months"
    :disabled="disabled"
    :mode="mode"
    format="YYYY-MM"
    :open="open"
    @openChange="openChange"
    valueFormat="YYYY-MM"
    :allowClear="allowClear"
    @panelChange="panelChange"
    dropdownClassName="plan_date"
  >
    <template slot="renderExtraFooter">
      <span @click="confirmFn" class="picker-close">确定</span>
    </template>
  </a-range-picker>
</template>

<script>
import moment from 'moment';
export default {
  name: 'monthRange',
  model: {
    prop: 'value',
    event: 'change'
  },
  props: {
    value: {
      type: Object,
      default: null
    },
    allowClear: {
      type: Boolean,
      default: true
    },
    disabled: {
      type: Boolean,
      default: false
    }
  },
  watch: {
    value: {
      immediate: true,
      handler: function (val) {
        if (Array.isArray(val)) {
          this.months = val;
        } else {
          this.months = [];
        }
      }
    }
  },
  data () {
    return {
      months: [],
      mode: ['month', 'month'],
      open: false
    };
  },
  methods: {
    moment,
    panelChange (value, mode) {
      this.mode = ['month', 'month'];
      if (Array.isArray(value) && value.length) {
        value = [
          moment(value[0].startOf('month')).format('YYYY-MM-DD'),
          moment(value[1].endOf('month')).format('YYYY-MM-DD')
        ];
      }
      this.months = value;
      this.$emit('change', value);
    },
    openChange (status) {
      this.open = status;
    },
    confirmFn () {
      this.open = false;
    }
  }
};
</script>
