<template>
  <div class="chart" :id="chartId">
  </div>
</template>

<script>

import echarts from '@/utils/enquireEchart';
import { simpleDebounce } from '@/utils/util';
import {
  lineChart, lineChartCn
} from '@/api/monitor/runMonitor';

export default {
  name: '',
  props: {
    psId: {
      type: [String, Number],
      default: ''
    },
    refreshFlag: {
      type: Number,
      default: 0
    },
    chartIndex: {
      type: Number,
      default: 0
    },
    unit: {
      type: String,
      default: 'kW'
    },
    isCn: {
      type: Boolean,
      default: false
    }
  },
  data () {
    this.initChart = simpleDebounce(this.initChart, 300);
    this.reRanderChart = simpleDebounce(this.reRander<PERSON>hart, 500);
    return {
      series: [],
      chart: null,
      chartId: '0',
      option: {

      }
    };
  },
  created () {
    let { isCn, chartIndex } = this;
    this.chartId = isCn ? `chart_StoredEnergy_${chartIndex}` : `chart_RunMonitor_${chartIndex}`;
  },
  mounted () {
    this.initChart();
  },
  methods: {

    /**
       * [getTimeList description] 生成时间列表
       * @param  {[type]} hours [description] 小时
       * @param  {[type]} step  [description] 分段
       * @return {[type]}       [description] 时间段列表
       */
    getTimeList (hours, step) {
      var minutes = 60;
      var timeArr = [];
      for (var i = 0; i < hours; i++) {
        var str = '';
        if (i < 10) {
          str = 0 + '' + i;
        } else {
          str = '' + i;
        }

        for (var j = 0; j < minutes; j++) {
          if (j % step == 0) {
            var s = j < 10 ? ':' + 0 + '' + j : ':' + j;
            s = str + s;
            timeArr.push(s);
          }
        }
      }

      return timeArr;
    },
    // 窗口变化时 重新渲染
    reRanderChart () {
      let id = this.chartId;
      if (document.getElementById(id) && this.chart && this.option && Object.keys(this.option)) {
        this.chart.resize();
      }
    },

    // 初始化图表
    async initChart () {
      let action = this.isCn ? lineChartCn : lineChart;
      let res = await action({
        psId: this.psId
      });
      let result = res.result_data;
      if (!result) return;
      const data = {
        alarm: result.alarm,
        radiation: result.radiation
      };
      this.$emit('getCardData', data, this.chartIndex);
      let currentPower = []; let radiationP2003 = [];
      // 转换为Number 方便后面转化数据
      if (this.isCn) {
        currentPower = result.power.map((item, index) => {
          if (item.dataY === '') return null;
          return Number(item.dataY);
        });
        radiationP2003 = result.soc.map((item, index) => {
          if (item.dataY === '') return null;
          return Number(item.dataY);
        });
      } else {
        currentPower = result.currentPower.map((item, index) => {
          if (item.dataY === '') return null;
          return Number(item.dataY);
        });
        radiationP2003 = result.radiationP2003.map((item, index) => {
          if (item.dataY === '') return null;
          return Number(item.dataY);
        });
      }

      let maxY1;
      let maxY2;
      // y轴最大值转换为数组最大值同数量级并且首位＋1 的 后续全为0的格式 例如 58=>60   2839=>3000
      const arr = [1e1, 1e2, 1e3, 1e4, 1e5, 1e6];
      for (let item of arr) {
        if (Math.max(...currentPower) <= item) {
          maxY1 = (Math.floor((Math.max(...currentPower) + (item / 10)) / (item / 10))) * (item / 10);
          break;
        }
      }

      maxY2 = Math.floor((Math.max(...radiationP2003) + 100) / 100) * 100;

      // 全无数据则置空数组
      if (currentPower.every(item => item === null)) {
        currentPower = [];
        maxY1 = 0;
      }
      if (radiationP2003.every(item => item === null)) {
        radiationP2003 = [];
        maxY2 = 0;
      }

      currentPower = currentPower.map(item => item === null ? '--' : item);
      radiationP2003 = radiationP2003.map(item => item === null ? '--' : item);

      // let chart = echarts.init(document.getElementById('chart' + this.chartIndex))
      let id = this.chartId;
      if (!document.getElementById(id)) { // dom 不存在时不画
        return;
      }
      // 不重复初始化
      this.chart = echarts.getInstanceByDom(document.getElementById(id));
      if (!this.chart) {
        this.chart = echarts.init(document.getElementById(id));
        this.$once('hook:beforeDestroy', function () {
          echarts.dispose(this.chart);
        });
      }
      const option = {
        legend: {
          itemHeight: 1,
          itemWidth: 16,
          align: 'left',
          textStyle: {
            // color: this.$store.state.app.theme === 'dark' ? '#ffffff' : '#696969',
            color: '#fff',
            number: 14
          },
          selected: {
            [this.isCn ? '直流功率' : '实时功率']: !!(currentPower.length),
            [this.isCn ? 'soc' : '辐照度']: !!(radiationP2003.length)
          },
          selectedMode: !!(currentPower.length && radiationP2003.length),

          // selectedMode: false,
          data: this.isCn ? [{ name: '直流功率' }, { name: 'soc' }] : [{ name: '实时功率' }, { name: '辐照度' }],
          top: '0%'
        },
        tooltip: {
          trigger: 'axis',
          className: 'solar-eye-tooptip'
        },
        grid: {
          left: '4%',
          right: '4%',
          bottom: this.isCn ? '1%' : '6%',
          top: '14%',
          containLabel: true
        },
        color: this.isCn ? ['#2BC8C3', '#D1D100'] : ['#7BB9FE', '#FF9974'],
        xAxis: {
          axisLine: {
            show: false,
            lineStyle: {
              // color: this.$store.state.app.theme === 'dark' ? '#ffffff' : '#ADADAD'
              color: '#fff'
            }
          },
          axisLabel: {
            interval: 35
          },
          axisTick: {
            show: false
          },
          type: 'category',
          boundaryGap: false,
          data: [...this.getTimeList(24, 5), '00:00'],
          nameTextStyle: {
            // color: this.$store.state.app.theme === 'dark' ? '#ffffff' : ''
            color: '#fff'
          }
        },
        yAxis: [
          {
            name: this.isCn ? result.powerUnit : (currentPower.length ? this.unit : ''),
            nameTextStyle: {
              align: currentPower.length ? 'right' : 'left',
              // color: this.$store.state.app.theme === 'dark' ? '#ffffff' : ''
              color: '#fff'
            },
            splitLine: {// 分割线配置
              show: true,
              lineStyle: {
                color: ['rgba(151, 151, 151, 0.11)']
              }
            },
            type: 'value',
            axisLine: {
              show: false,
              lineStyle: {
                // color: this.$store.state.app.theme === 'dark' ? '#ffffff' : '#ADADAD'
                color: '#fff'
              }
            },
            min: this.isCn ? undefined : 0,
            max: maxY1, // 左侧y轴最大值
            // 两个y轴的刻度必须整除一个相同的数才能重合
            interval: maxY1 / 5, // 间距等分为5等分
            axisTick: {
              show: false
            }
          }, {
            name: this.isCn ? result.socUnit : (radiationP2003.length ? 'W/m²' : ''),
            nameTextStyle: {
              align: radiationP2003.length ? 'left' : 'right',
              // color: this.$store.state.app.theme === 'dark' ? '#ffffff' : ''
              color: '#fff'
            },
            min: this.isCn ? undefined : 0,
            max: maxY2, // 右侧y轴最大值
            // 两个y轴的刻度必须整除一个相同的数才能重合
            interval: maxY2 / 5, // 间距等分为5等分
            type: 'value',
            axisLine: {
              show: false,
              lineStyle: {
                // color: this.$store.state.app.theme === 'dark' ? '#ffffff' : '#ADADAD'
                color: '#fff'
              }
            },
            axisTick: {
              show: false
            },
            splitLine: {// 分割线配置
              show: true,
              lineStyle: {
                color: ['rgba(151, 151, 151, 0.11)']
              }
            }
          }],
        series: [
          {
            name: this.isCn ? '直流功率' : '实时功率',
            smooth: true,
            type: 'line',
            symbol: 'none',
            yAxisIndex: 0,
            emphasis: {
              focus: 'series'
            },
            areaStyle: {
              opacity: 0.2,
              color: {
                type: 'linear',
                x: 0,
                y: 0,
                x2: 0,
                y2: 1,
                colorStops: !this.isCn ? [{
                  offset: 1, color: 'rgba(214, 214, 214, 0)' // 100% 处的颜色
                }, {
                  offset: 0, color: '#8DA8E1' // 0% 处的颜色
                }, {
                  offset: 0.3, color: '#C1D3F2' // 30% 处的颜色
                }] : [{
                  offset: 1, color: 'rgba(81, 176, 228, 1)' // 100% 处的颜色
                }, {
                  offset: 0.3, color: 'rgba(81, 176, 228, 0.5)' // 30% 处的颜色
                }, {
                  offset: 0, color: 'rgba(21, 177, 231, 0.2)' // 0% 处的颜色
                }]
              }
            },
            data: currentPower
          },
          {
            name: this.isCn ? 'soc' : '辐照度',
            smooth: true,
            symbol: 'none',
            type: 'line',
            yAxisIndex: 1,
            emphasis: {
              focus: 'series'
            },
            areaStyle: {
              opacity: 0.2,
              color: {
                type: 'linear',
                x: 0,
                y: 0,
                x2: 0,
                y2: 1,
                colorStops: !this.isCn ? [
                  {
                    offset: 1, color: 'rgba(214, 214, 214, 0)' // 100% 处的颜色
                  }, {
                    offset: 0.3, color: '#F08B59' // 30% 处的颜色
                  }, {
                    offset: 0, color: '#FAC192' // 0% 处的颜色
                  }] : [
                  {
                    offset: 1, color: '#D1D100' // 100% 处的颜色
                  }, {
                    offset: 0.3, color: '#3c4432' // 30% 处的颜色
                  }, {
                    offset: 0, color: '#373c34' // 0% 处的颜色
                  }]
              }
            },
            data: radiationP2003
          }
        ]
      };

      this.option = Object.assign(option);
      option && this.chart.setOption(option);

      window.addEventListener('resize', this.reRanderChart, false);
    }
  },
  computed: {
    navTheme () {
      return this.$store.state.app.theme;
    }
  },
  activated () {
    if (!this.chart) {
      this.initChart();
    }
  },
  watch: {
    psId: {
      handler () {
        this.initChart();
      }
    },
    refreshFlag: {
      handler () {
        this.initChart();
      }
    },
    navTheme () {
      // let isDark = this.navTheme == 'dark' || dock;
      let isDark = true; // 目前改版只做了深色版本，现阶段只取深色
      let option = this.option; let lineStyle = isDark ? '#ffffff' : '#ADADAD'; let nameStyle = isDark ? '#ffffff' : '';
      option.legend.textStyle.color = isDark ? '#ffffff' : '#696969';
      option.xAxis.axisLine.lineStyle.color = lineStyle;
      option.xAxis.nameTextStyle.color = nameStyle;
      option.yAxis[0].axisLine.lineStyle.color = lineStyle;
      option.yAxis[1].axisLine.lineStyle.color = lineStyle;
      option.yAxis[0].nameTextStyle.color = nameStyle;
      option.yAxis[1].nameTextStyle.color = nameStyle;
      this.chart.setOption(this.option, true);
    }
  }
};
</script>

<style scoped>
  .chart {
    height: 100%;
    width: 100%;
  }
</style>
