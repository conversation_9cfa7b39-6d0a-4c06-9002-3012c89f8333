<!-- 对接配置 -->
<template>
  <div class="abutment-config">
    <a-spin :spinning="loading">
      <div class="solar-eye-search-model" style="padding: 0">
        <div class="solar-eye-search-content">
          <a-row a-row :gutter="24" style="margin: 0">
            <a-col :span="6">
              <div class="search-item">
                <span class="search-label">接入来源</span>
                  <a-input v-model="searchData.sourceName" placeholder="请输入关键字搜索" allow-clear/>
              </div>
            </a-col>
            <a-col :span="6">
              <div class="search-item">
                <span class="search-label">首次对接时间</span>
                <a-range-picker v-model="firstTime" @change="timeChange" format="YYYY-MM-DD" allow-clear>
                  <a-icon slot="suffixIcon" type="calendar" style="height: 32px"/>
                </a-range-picker>
              </div>
            </a-col>
            <a-col :span="4">
              <div class="search-item">
                <a-button class="solar-eye-btn-primary" @click="pageChange()">查询</a-button>
                <a-button class="solar-eye-btn-primary-cancel" @click="resetSearch()">重置</a-button>
              </div>
            </a-col>
          </a-row>
        </div>
      </div>
      <div class="solar-eye-gap"></div>
      <a-col class="solar-eye-main-content">
        <vxe-table
          :data="data"
          ref="multipleTable"
          resizable
          show-overflow
          highlight-hover-row
          size="small"
          :height="tableHeight + 12"
          :seq-config="{ startIndex: (searchData.currentPage - 1) * searchData.pageSize }"
          class="my-table"
        >
          <vxe-table-column type="seq" :width="80" title="序号"></vxe-table-column>
          <vxe-table-column v-for="item in columns" show-overflow="title" :formatter="tabFormatter"
            :title="item.title" min-width="160" :field="item.field">
          </vxe-table-column>
          <vxe-table-column title="操作" :width="120" fixed="right" :resizable="false">
            <template v-slot="{ row }">
              <span v-if="showHandle('abutmentConfig:config')" class="table-icon" @click="openConfig(row, '1')" title="配置">
                <svg-icon class="operation-btn-hover" iconClass="config" style='font-size: 16px'/>
              </span>
              <span v-if="showHandle('abutmentConfig:detail')" class="table-icon" @click="openConfig(row, '2')" title="详情">
                <svg-icon class="operation-btn-hover" iconClass="detail" style='font-size: 16px'/>
              </span>
            </template>
          </vxe-table-column>
          <template v-slot:empty>
            <span>查询无数据</span>
          </template>
        </vxe-table>
        <div class="bottom-page" >
          <page-pagination v-if="total > 10" :pageSize="searchData.pageSize" :current="searchData.currentPage"
             :total="total" @size-change="sizeChange" :pageSizeOptions="pageSizeOptions"/>
        </div>
      </a-col>
    </a-spin>
    <config ref="config"/>
  </div>
</template>
<script>
import tableHeight from '@/mixins/tableHeightAndSearchModel';
import config from './modules/config.vue'
import { getAccessList, getTaskList } from '@/api/model/abutmentConfig.js';
import moment from 'moment';
export default {
  name: 'AbutmentConfig',
  mixins: [tableHeight],
  components: { config },
  data () {
    return {
      loading: false,
      searchData: {},
      firstTime: [],
      data: [],
      total: 0,
      columns: [
        { title: '接入来源', field: 'secondName'},
        { title: '接入协议', field: 'agreement'},
        { title: '协议描述', field: 'agreementDesc'},
        { title: '采集间隔', field: 'collectInterval'},
        { title: '首次对接时间', field: 'firstTime'},
        { title: '最新接收时间', field: 'lastTime'}
      ],
      pageSizeOptions: [10, 15, 20, 50, 100, 500]
    }
  },
  created () {
    this.resetSearch();
  },
  methods: {
    // 查询列表
    pageChange () {
      this.searchData.currentPage = 1;
      this.getList();
    },
    // 获取列表数据
    getList() {
      this.loading = true;
      getAccessList(this.searchData).then(res => {
        if(res.result_data && res.result_data.pageList) {
          res.result_data.pageList.forEach(item => {
            item.collectInterval += 'min';
          })
          this.data = res.result_data.pageList;
          this.total = res.result_data.rowCount;
        }
        this.loading = false
      }).catch(() => { this.loading = false });
    },
    // 首次对接时间变化时间
    timeChange(val) {
      this.searchData.firstTimeStart = val.length ? moment(val[0]).format('YYYY-MM-DD') + ' 00:00:00' : undefined;
      this.searchData.firstTimeEnd = val.length ? moment(val[1]).format('YYYY-MM-DD') + ' 23:59:59' : undefined;
    },
    // 重置列表
    resetSearch () {
      this.searchData = {
        currentPage: 1,
        pageSize: 10,
        sourceName: undefined,
        firstTimeStart: undefined,
        firstTimeEnd: undefined
      }
      this.firstTime = [];
      this.getList();
    },
    // 列表分页、页数变化
    sizeChange (current, size) {
      this.searchData.pageSize = size;
      this.searchData.currentPage = current;
      this.getList();
    },
    // 打开配置、详情页面
    openConfig(row, type) {
      this.loading = true;
      getTaskList({sourceId: row.id}).then(res => {
        if(res.result_data && res.result_data.length > 0) {
          this.$refs.config.init(row, type, res.result_data);
        } else {
          this.$message.warning('协议任务获取失败！');
        }
        this.loading = false;
      }).catch(() => {this.loading = false});
    }
  } 
}
</script>
<style lang="less" scoped>
.table-icon {
  cursor: pointer;
  color: var(--zw-conduct-color--normal);
}
.table-icon + .table-icon {
  margin-left: 24px;
}
.bottom-page {
  height: 42.5px;
}
</style>
