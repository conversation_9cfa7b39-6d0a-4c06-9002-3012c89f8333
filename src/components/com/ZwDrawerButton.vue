<template>
  <div class='flex-center'>
    <!-- 更多操作下拉菜单 -->
    <a-dropdown v-if='dropList.length>0' :trigger="['hover']" placement='topCenter' :overlayStyle='{ textAlign: "center" }'>
      <a class='ant-dropdown-link' @click='e => e.preventDefault()'>
        <a-button> 更多 <a-icon type="up" class="dark-black-color" /></a-button>
      </a>
      <a-menu slot='overlay'>
        <!-- 遍历下拉菜单中的按钮 -->
        <template v-for='(v,k) in dropList'>
          <a-menu-item :key='k'>
            <a-button :title='v.name' :loading="loading" size='default' @click='$emit(v.emit)'>
              {{ v.name }}
            </a-button>
          </a-menu-item>
        </template>
      </a-menu>
    </a-dropdown>

    <!-- 默认显示的按钮 -->
    <template v-for='(v,k) in buttonList'>
      <a-button :key='k' :class='className(k)' :title='v.name' :loading="loading" size='default' @click='$emit(v.emit)'>
        {{ v.name }}
      </a-button>
    </template>
  </div>
</template>

<script>
export default {
  name: 'drawerButton',
  props: {
    // 按钮配置列表
    list: {
      type: Array,
      default: () => {
        return [];
      }
    },
    // 按钮加载状态
    loading: {
      type: Boolean,
      default: false
    }
  },
  data () {
    return {
      buttonList: [], // 默认显示按钮
      dropList: [] // 更多内按钮
    };
  },
  watch: {
    list: {
      handler (newVal) {
        let _btnList = [];
        // 循环处理按钮
        Array.from(newVal, (item, index) => {
          // 将类数组对象或可迭代对象转化为数组。
          let show = this.isShow(item.has) && item.show;
          if (show) {
            _btnList.push({
              emit: item.emit,
              name: item.name
            });
          }
        });
        let len = _btnList.length;
        if (len > 0) {
          if (len <= 4) {
            // 如果按钮数量小于等于4，全部显示
            this.buttonList = _btnList;
            this.dropList = [];
          } else {
            // 如果按钮数量大于4，显示第一个和最后两个按钮，其余放入下拉菜单
            const segment1 = _btnList.slice(0, 1);
            const segment2 = _btnList.slice(len - 2, len);
            this.buttonList = [...segment1, ...segment2];
            this.dropList = _btnList.slice(1, len - 2);
          }
        } else {
          this.buttonList = [];
          this.dropList = [];
        }
      },
      deep: true,
      immediate: true
    }
  },
  methods: {
    // 判断按钮是否显示
    isShow: function (perms) {
      if (perms === true) {
        return true;
      } else {
        // 根据权限标识和外部指示状态进行权限判断
        let userAuth = sessionStorage.getItem('LOGIN_USER_BUTTON_AUTH');
        userAuth = (userAuth ? JSON.parse(userAuth) : []);
        let arr = userAuth.filter(item => (item.type == '1' && item.action == perms));
        return arr.length == 1;
      }
    },
    // 获取对应按钮样式
    className (k) {
      let len = this.buttonList.length;
      switch (k) {
        case 0:
          return 'zw-btn-cancel';
        case 1:
          if (len === 2) {
            return 'zw-btn-primary';
          } else if (len === 3) {
            return 'zw-btn-save';
          } else if (len === 4) {
            return 'zw-btn-cancel';
          }
        // eslint-disable-next-line no-fallthrough
        case 2:
          if (len === 3) {
            return 'zw-btn-primary';
          } else if (len === 4) {
            return 'zw-btn-save';
          }
        // eslint-disable-next-line no-fallthrough
        case 3:
          return 'zw-btn-primary';
        default:
          return '';
      }
    }
  }
};
</script>

<style lang='less' scoped>
.ant-dropdown-trigger {
  margin-right: 16px !important;
}

:deep(.ant-dropdown-menu-item .ant-btn) {
  margin: auto 8px;
  border: 0;
  background-color: unset;
  min-width: 28px;
  height: 28px;
  box-shadow: 0 0 black;
}

// 返回关闭按钮及hover
.zw-btn-cancel {
  background: var(--zw-card-bg-color--default) !important;
  border: 1px solid var(--zw-border-color--default) !important;
  color: var(--zw-text-1-color--default) !important;

  &:hover {
    border: 1px solid var(--zw-primary-color--default) !important;
    color: var(--zw-primary-color--default) !important;
  }
}

// 暂存保存按钮及hover
.zw-btn-save {
  background: var(--zw-card-bg-color--default) !important;
  border: 1px solid var(--zw-primary-color--default) !important;
  color: var(--zw-primary-color--default) !important;

  &:hover {
    background: var(--zw-primary-partial-areas-color--hover) !important;
  }
}

// 默认按钮及hover
.zw-btn-primary {
  background: var(--zw-primary-color--default) !important;
  border: 1px solid var(--zw-primary-color--default) !important;
  color: var(--zw-common-white-color--default) !important;

  &:hover {
    background: var(--zw-primary-color--hover) !important;
    border: 1px solid var(--zw-primary-color--hover) !important;
  }
}
</style>
