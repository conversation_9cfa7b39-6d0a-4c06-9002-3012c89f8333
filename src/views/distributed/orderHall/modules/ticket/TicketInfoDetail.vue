<template>
  <detail-layout :labelList="labelList" :form="baseForm" title="两票信息">
    <template v-slot:ticketDtoList>
      <a-col :span="24" class="detail_layout_content">
        <span class="left">关联两票</span>
        <span class="right" >
          <template v-if="baseForm.ticketDtoList && baseForm.ticketDtoList.length">
            <template v-for="(item, index) in baseForm.ticketDtoList">
              <span>{{ item.ticketNo }}</span>
              <!-- <span :key='index' @click="toTicketEven(item)" class="blue operation-btn-hover">
                <a>{{ item.ticketNo }}</a>
              </span> -->
              <span :key='index' v-if='index !== baseForm.ticketDtoList.length - 1'>{{ '、'}}</span>
            </template>
          </template>
          <template v-else>
            {{ '--' }}
          </template>
        </span>
      </a-col>
    </template>
  </detail-layout>
</template>

<script>

export default {
  name: 'TicketInfoDetail',
  props: {
    baseForm: {
      type: Object,
      default: () => ({})
    }
  },
  data () {
    return {
      labelList: [
        { slot: 'ticketDtoList' }
      ]
    };
  },
  methods: {
    // 两票跳转
    toTicketEven (item) {
      this.$emit('toTicketEven', item);
    }
  }
};
</script>

<style lang='less' scoped>
</style>
