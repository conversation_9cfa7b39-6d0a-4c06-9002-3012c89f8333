<!-- 调动轨迹 -->
<template>
  <div class="drawer-form-com">
    <div class="drawer-form-content">
      <a-spin :spinning="loading">
        <div class="solar-eye-search-model">
          <a-row :gutter="24" class="solar-eye-search-content">
            <a-col :xxl="6" :xl="8" :md="12">
              <div class="search-item">
                <span class="search-label">姓名</span>
                <a-input v-model="dataForm.empName" size="default" :disabled="true" />
              </div>
            </a-col>
            <a-col :xxl="6" :xl="8" :md="12">
              <div class="search-item">
                <span class="search-label">工号</span>
                <a-input v-model="dataForm.empId" size="default" :disabled="true" />
              </div>
            </a-col>
            <a-col :xxl="6" :xl="8" :md="12">
              <div class="search-item">
                <span class="search-label">当前部门</span>
                <a-input v-model="dataForm.departName" size="default" :disabled="true" />
              </div>
            </a-col>
            <a-col :xxl="6" :xl="8" :md="12">
              <div class="search-item">
                <span class="search-label">当前职位</span>
                <a-select v-model="dataForm.position" :disabled="true">
                  <a-select-option v-for="item in dictMap.position" :key="item.dataValue" :value="item.dataValue">{{item.dataLable}}</a-select-option>
                </a-select>
              </div>
            </a-col>
            <a-col :xxl="6" :xl="8" :md="12">
              <div class="search-item">
                <span class="search-label">用户状态</span>
                <a-select v-model="dataForm.workSts" :disabled="true">
                  <a-select-option v-for="item in dictMap.work_sts_emp" :key="item.dataValue" :value="item.dataValue">{{item.dataLable}}</a-select-option>
                </a-select>
              </div>
            </a-col>
          </a-row>
        </div>

        <div class="solar-eye-main-content">
          <div class="operation-btn">
            <throttle-button perms="920110102203" label="导出" :loading="excel_load" @click="excelDownLoad()" />
          </div>
          <vxe-table ref="trajectoryTable" :data="trajectoryData" resizable max-height="457" align="center" border
            show-overflow highlight-hover-row size="small" :seq-config="{startIndex: (page - 1) * size}">
            <vxe-table-column type="seq" width="80px" title="序号"></vxe-table-column>
            <vxe-table-column show-overflow="title" v-for="item in columnList" :key="item.name"
              :min-width="(item.width ? item.width : 150)" :title="item.comment">
              <template v-slot="{row}">
                <span>{{getLabel(row[item.name], dictMap[item.dict])}}</span>
              </template>
            </vxe-table-column>
          </vxe-table>
          <!--分页组件-->
          <page-pagination :pageSize="size" :current="page" :total="total" @size-change="sizeChange" />
        </div>
      </a-spin>
    </div>
  </div>
</template>

<script>
import initDict from '@/mixins/initDict';
import { getWidth } from '@/utils/util.js';
import { getTransferTrajectoryInfo, exportTrajectoryList } from '@/api/isolarErp/employee/employee';

export default {
  mixins: [initDict],
  components: {},
  data () {
    return {

      dataForm: {
        id: undefined, // id
        empName: undefined, // 姓名
        empId: undefined, // 工号
        departName: undefined, // 当前部门
        position: undefined, // 当前职位
        workSts: undefined // 用户状态
      },

      // 分页参数
      total: 0,
      page: 1,
      size: 10,

      trajectoryData: [],
      excel_load: false,
      loading: false,
      columnList: [
        { name: 'depNew', comment: '所属部门', width: 200 },
        { name: 'positionNew', comment: '职位', width: 200 },
        { name: 'arrivalDate', comment: '到岗日期', width: 200 },
        { name: 'transferReasonType', comment: '调动原因', width: 200 },
        { name: 'depOldName', comment: '原部门', width: 200 },
        { name: 'positionOld', comment: '原职位', width: 200 },
        { name: 'startDate', comment: '入职日期', width: 200 },
        { name: 'desertDate', comment: '离职日期', width: 200 },
        { name: 'onDutyTime', comment: '在岗天数' }
      ]
    };
  },
  mounted () {
    this.columnList.forEach(item => {
      if (!item.width) {
        item.width = getWidth(item.comment);
      }
    });
    // 加载数据字典
    this.getDictMap('position,work_sts_emp');
  },
  methods: {
    //   表单初始化 调动轨迹
    init (type, data) {
      Object.assign(this.dataForm, data);
      this.queryData();
      return '调动轨迹';
    },
    // 查询
    async queryData () {
      let self = this;
      self.loading = true;
      let $table = this.$refs.trajectoryTable;
      $table && await $table.clearScroll();
      let map = {
        'size': this.size,
        'curPage': this.page,
        'account': this.dataForm.userAccount
      };
      getTransferTrajectoryInfo(map).then(res => {
        self.total = res.result_data.total;
        self.trajectoryData = res.result_data.rows;
        self.loading = false;
      }).catch(() => {
        self.trajectoryData = [];
        self.total = 0;
        self.loading = false;
      });
    },
    // 导出列表
    excelDownLoad () {
      this.excel_load = true;
      let map = {
        'account': this.dataForm.userAccount
      };
      exportTrajectoryList(map).then(res => {
        this.$downloadFile(res.result_data);
        this.excel_load = false;
      }).catch(() => {
        this.excel_load = false;
      });
    },
    // 关闭回调
    cancel () {
      this.$emit('cancel');
      this.reset();
    },
    // 数据重置
    reset () {
      let dictMap = Object.assign({}, this.dictMap);
      Object.assign(this.$data, this.$options.data());
      this.dictMap = dictMap;
    },
    // 分页事件
    sizeChange (p, e) {
      this.page = p;
      this.size = e;
      this.queryData();
    }

  }
};
</script>

<style lang="less" scoped>

</style>
