<template>
  <div class='card-box' @click='goDetail(cardItemData)'>
      <div class='card-top'>
        <div class='title-box'>
          <!-- <img :src="getPng(cardItemData)" alt=''> -->
          <span class='title' :title='cardItemData.deviceTypeName'>{{cardItemData.deviceTypeName}}</span>
        </div>
        <span class='extra-num'>{{cardItemData.count}}个</span>
      </div>
      <div class='card-content'>
        <span :class="['experiment-name',item.isEnable?'':'experiment-item-disabled']"
         v-for='(item,index) in computedProjectNames'
         :title='item.projectNameLabel' :key='index'>{{getItemLabel(item)}}</span>
        <span class='experiment-more' v-if='cardItemData.count>5'>...</span>
      </div>
  </div>
</template>

<script>
import { mixin } from '@/utils/mixin.js';
export default {
  mixins: [mixin],
  props: {
    cardItemData: {
      type: Object,
      default: () => ({})
    }
  },
  inject: ['goDetail'],
  methods: {
    // getPng (cardItemData) {
    //   const proImagesType = ['发电设备', '继保设备', '输电设备', '配电设备', '附属设备'];
    //   if (proImagesType.includes(cardItemData.firstDeviceTypeName)) {
    //     return require('@/assets/images/preExperiment/' + cardItemData.firstDeviceTypeName + '_' + this.navTheme + '.png');
    //   }
    //   return require('@/assets/images/preExperiment/' + '发电设备' + '_' + this.navTheme + '.png');
    // },
    // 增加禁用标识
    getItemLabel (item) {
      if (item.isEnable) {
        return item.projectNameLabel;
      }
      return item.projectNameLabel + '【禁用】';
    }
  },
  computed: {
    computedProjectNames () {
      let project;
      if (this.cardItemData.count > 5) {
        project = this.cardItemData.projectName.slice(0, 4);
      } else {
        project = this.cardItemData.projectName.slice(0);
      }
      return project;
    }
  }
};
</script>
<style lang='less' scoped>
  .card-box{
    width: 100%;
    border: 1px solid var(--zw-border-color--default);
    border-radius: 3px;
    .card-top{
      display: flex;
      justify-content: space-between;
      padding: 0 16px;
      height: 50px;
      line-height: 50px;
      background: var(--zw-small-area-bg-color--default);
      border-bottom: 1px solid var(--zw-border-color--default);
      .title-box{
        font-size: 16px;
        flex: 1;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
        img{
          width:40px;
          height:40px;
          object-fit: cover;
        }
       .title{
         font-weight: 600;
         color: var(--zw-text-2-color--default);
         line-height: 22px;
       }
      }
      .extra-num{
        font-weight: 600;
        color: var(--zw-text-2-color--default);
      }
    }
    .card-content{
      height: 190px;
      padding: 24px 16px;
      display: flex;
      flex-direction: column;
      gap: 8px;
      .experiment-name{
        display: inline-block;
        white-space: nowrap;
        text-overflow: ellipsis;
        overflow: hidden;
      }
      .experiment-item-disabled{
        color: var(--zw-text-color--disable);
      }
    }
  }
</style>
