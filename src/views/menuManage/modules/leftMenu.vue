<template>
  <div class="manage-left-menu">
    <div class="menu-list" v-bind:class="{'hidden': icon_type}">
      <a-menu v-model="current" @click="handleClick" mode="inline" class="menu-manage-menus">
        <a-menu-item v-for="item in menus" :key="item.path">
          <span>{{item.meta.title}}</span>
        </a-menu-item>
      </a-menu>
    </div>
    <div class="middle" :class="{'middle-btn-expand': icon_type}"  :style="{left:!icon_type ?'180px':'0' }" v-show="isHasOpenDrawer == 0">
      <svg-icon iconClass="expand" class="middle-btn" @click="showMenu"></svg-icon>
    </div>
  </div>
</template>

<script>
import { USER_AUTH } from '@/store/mutation-types';
export default {
  name: 'leftMenu',
  model: {
    prop: 'value',
    event: 'show'
  },
  props: {
    value: {
      type: <PERSON>ole<PERSON>,
      required: true,
      default: false
    },
    isNeedClose: {
      type: Boolean,
      default: false
    }
  },
  data () {
    return {
      current: [],
      menus: [],
      icon_type: false,
      isHasOpenDrawer: document.getElementsByClassName('ant-drawer-open').length
    };
  },
  computed: {
    permissionMenuList () {
      return this.$store.state.user.permissionList;
    }
  },
  watch: {
    $route: {
      handler (val, oldval) {
        this.getMenus();
      },
      // 深度观察监听
      deep: true
    },
    isNeedClose () {
      if (this.isNeedClose) {
        this.current = this.menus[0].path;
      }
    }
  },
  mounted () {
    this.getMenus();
    window.addEventListener('click', this.hasOpenDrawer, true);
  },
  beforeDestroy () {
    window.removeEventListener('click', this.hasOpenDrawer, true);
  },
  methods: {
    hasOpenDrawer (e) {
      // 屏蔽左侧菜单点击影响
      if (e.target.id == 'logo-icon') {
        return;
      }
      let doc = document;
      this.isHasOpenDrawer = doc.getElementsByClassName('ant-drawer-open').length;
    },
    /*
        显示隐藏左侧
      */
    showMenu () {
      this.icon_type = !this.icon_type;
      this.$emit('show', this.icon_type);
    },
    /*
        菜单切换
      */
    handleClick ({ item, key, keyPath }, e) {
      if (JSON.stringify(this.current) != JSON.stringify(keyPath)) {
        let menu = this.menus.find(o => o.path === key);
        if (menu) {
          this.$emit('menu-change', menu);
        }
      }
    },
    /*
        根据当前路由获取子菜单
      */
    getMenus () {
      let path = this.$route.path;
      let all_menu = this.permissionMenuList;
      const menus = [];
      const find = function (list) {
        list.forEach((item) => {
          if (item.alwaysShow && item.path === path) {
            let childrens = (item.children).filter((child) => {
              return child.path != path;
            });
            menus.push(...childrens);
          } else if (Array.isArray(item.children) && item.children.length > 0) {
            find(item.children);
          }
        });
      };
      find(all_menu);
      this.setAdd(menus);
      this.menus = menus;
      if (menus.length) {
        this.current = [menus[0].path];
        this.$emit('menu-change', menus[0]);
      }
    },
    /*
        判断是否有填报权限
      */
    setAdd (menus) {
      const all = JSON.parse(sessionStorage.getItem(USER_AUTH) || '[]');
      menus.forEach(item => {
        let add_btn = all.find(btn => btn.action === item.path);
        item.has_add = (add_btn && add_btn.path);
        item.form_component = (add_btn && add_btn.path ? add_btn.path : null);
      });
    }
  }
};
</script>

<style lang="less" scoped>
  .manage-left-menu{
    height: 100%;
    overflow: hidden auto;
    display: flex;
    .menu-list{
      width: 200px;
      height: 100%;
      visibility: visible;
    }
    .hidden{
      width: 0;
      visibility: hidden;
    }
    .menu-manage-menus{
      height: 100%;
      width: 200px;
      overflow: hidden auto;
      padding-top: 38px;
      //background: rgba(0, 0, 0, .1);
    }
    .middle{
      width: 32px;
      height: 38px;
      position: fixed;
      top: 112px;
      text-align: center;
      line-height: 38px;
      z-index: 2;
      border-radius: 0px 4px 4px 0px;
      .middle-btn {
        border: none;
        font-size: 16px;
      }
    }
    .right-icon:hover{
      background-color: rgba(0, 0, 0, .1);
    }
  }
</style>
