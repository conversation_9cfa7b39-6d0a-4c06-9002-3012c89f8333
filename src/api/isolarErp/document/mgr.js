import { axios } from '@/utils/erp-request';

// 获取左侧目录结构
export function getDocumentMenuList (data) {
  return axios({
    url: '/document/v1/document/getDocumentMenuList',
    method: 'post',
    data
  });
}

// 文档管理列表
export function getFileList (data) {
  return axios({
    url: '/document/v1/document/mgr',
    method: 'post',
    data
  });
}

// 文档权限取得
export function getDocumentAuthority (data) {
  return axios({
    url: '/document/v1/document/getDocumentAuthority',
    method: 'post',
    data
  });
}

// 员工信息取得
export function getErpEmployeeInfoList (data) {
  return axios({
    url: '/document/v1/document/getErpEmployeeInfoList',
    method: 'post',
    data
  });
}

// 创建目录
export function createFolder (data) {
  return axios({
    url: '/document/v1/document/createFolder',
    method: 'post',
    data
  });
}

// 修改文档
export function updateDocument (data) {
  return axios({
    url: '/document/v1/document/updateDocument',
    method: 'post',
    data
  });
}

// 上传文件
export function uploadFile (data) {
  return axios({
    url: '/document/v1/document/uploadFile',
    method: 'post',
    data
  });
}

// 上传创建权限检查
export function checkOperationAuthority (data) {
  return axios({
    url: '/document/v1/document/checkOperationAuthority',
    method: 'post',
    data
  });
}

export function deleteDocument (data) {
  return axios({
    url: '/document/v1/document/deleteDocument',
    method: 'post',
    data
  });
}
