<template>
  <div class="alarmDetail">
    <a-drawer
    width="100%"
    :visible="alarmDetailVisable"
    @close="onClose(false)"
    :get-container="getDom"
    :wrap-style="{ position: 'absolute' }"
     :body-style="{ padding: 0 }"
    :closable="false"
    :afterVisibleChange="afterVisibleChange"
  >
    <a-spin :spinning="pageloading" style="min-width:1200px">
      <a-row style="margin: 0">
        <a-col class="radiusBox" :style="{ }">
          <div >
            <a-icon class="back-icon" @click="onClose(false)" title="返回" type="close" />
            <a-tabs size="default" v-model="tabKind" @change="tabHandleClick" id="timeAnalysis" class="tab-layout-tabs" type="card">
              <a-tab-pane key="1" tab="事件回放"></a-tab-pane>
              <a-tab-pane key="2" tab="实时预览"></a-tab-pane>
              <a-tab-pane key="3" tab="健康轨迹"></a-tab-pane>
            </a-tabs>
          </div>
          <!-- 事件回放 -->
          <div class="alarm-play"  v-show="tabKind == '1'||tabKind=='2'" >
            <a-row style="margin: 0" v-show="(tabKind == '1'||tabKind == '2')">
              <a-col :xs="32" :sm="16" :md="16" :lg="16" :xl="20">
                <span class="station-title">{{ defaultInfo.psName }}</span>
              </a-col>
              <a-col :xs="16" :sm="8" :md="8" :lg="8" :xl="4"  v-show="tabKind == '1'" >
                <div class="operation-icon">
                  <span title="派发" v-if="show('safetyEvents:dispatch')" >
                    <svg-icon v-show="parentType == '01' && showOperationButton" class="margin-left-6 health-icon" @click="batchDistributeEvent(defaultInfo, '2', '5')"
                      iconClass="health-flaw" ></svg-icon>
                  </span>
                </div>
              </a-col>
            </a-row>
            <div  v-show="tabKind == '1'" class="data-info">
              <span  style="font-weight: bold">事件等级:</span>
              <div :class="priority=='3'?'high':priority=='2'?'middle':'low'"><span>{{eventLevelValue}}</span></div>
              <span  style="margin-left: 18px; font-weight: bold">所在区域/位置:</span
              ><span  style="margin-left: 8px">{{defaultInfo.psName}}</span
              ><span  style="margin-left: 18px; font-weight: bold">设备编号:</span
              ><span  style="margin-left: 8px">{{defaultInfo.deviceId}}</span>
              <span  style="margin-left: 18px; font-weight: bold">发生时间:</span
              ><span  style="margin-left: 8px">{{startTime}}</span>
              <span  style="margin-left: 18px; font-weight: bold">结束时间:</span
              ><span  style="margin-left: 8px">{{endTime}}</span>
              <span  style="margin-left: 18px; font-weight: bold">告警类型:</span
              ><span  style="margin-left: 8px">{{defaultInfo.alarmRemarkName}}</span>
            </div>
            <a-row  v-show="tabKind == '2'" :gutter="24" style="margin: 0" class="solar-eye-pure-bg">
              <a-col :xxl="5" :xl="8" :md="10">
                <div class="item_box">
                  <span class="text">设备名称</span>
                  <div>
                    <a-select allowClear showSearch v-model="deviceName" :getPopupContainer="(node) => node.parentNode"
                       style="width: 100%;height:32px" placeholder="请选择" @change='handleAsyncChange'>
                      <a-select-option v-for="item in regions" :key="item.deviceName" :value='item.regionIndexCode'>
                        {{ item.deviceName }}
                      </a-select-option>
                    </a-select>
                  </div>
                </div>
              </a-col>
            </a-row>

            <a-row class='alarm-replay-video' >
              <a-col :span="15" class='video-wrapper' :style="{height: tabKind !='1'? '480px':'' }">
                <div class="video" id="player" :style="{width: tabKind !='1'? '1800px':'',height: tabKind !='1'? '480px':'' }">
                </div>
                <div @mouseover="showBtn = true" @mouseout="showBtn = false" v-if="tabKind == '1'">
                  <a-form-item v-show="showBtn"  class="btn" :style="{left: windowIndex == '1' ? '840px' : '240px' }">
                    <img title="重播" @click="wndPlaybackStartOri(windowIndex);isPlay1=true;isPlay2=true" src="@/assets/images/health/safetyEvents/restart.svg" />
                    <img title="暂停" @click="playbackPause(windowIndex)" v-show="windowIndex == '0' ? isPlay1 : isPlay2" src="@/assets/images/health/safetyEvents/pause.png" />
                    <img title="恢复" @click="playbackResume(windowIndex)" v-show="windowIndex == '0' ? !isPlay1 : !isPlay2" src="@/assets/images/health/safetyEvents/renew.png" />
                    <!-- <img title="停止"  @click="stopPlay(windowIndex);isPlay1=true;isPlay2=true" src="@/assets/images/health/safetyEvents/end.png" /> -->
                  </a-form-item>
                </div>
              </a-col>
              <a-col :span="6" v-if="tabKind == '1'">
                <a-carousel v-show="tabKind == '1'" arrows>
                  <div slot="prevArrow"  class="custom-slick-arrow" style="left: 10px; z-index: 1">
                    <a-icon type="left-circle" theme="twoTone"  />
                  </div>
                  <div slot="nextArrow"  class="custom-slick-arrow" style="right: 10px">
                    <a-icon type="right-circle" theme="twoTone" />
                  </div>
                  <div class="alarm-pic" v-for="item in imgUrl" :key="item"><img referrerpolicy="no-referrer" :src="item" class="alarm-img"/>
                  </div>
                </a-carousel>
              </a-col>
            </a-row>

            <div v-show="tabKind == '1'" class="page-end">
              <a-pagination simple v-model="currentSlide" :default-current="1" :default-page-size="1" :total="totalSlide" @change="onChangeTimePeriod" />
            </div>
          </div>

          <!-- 健康轨迹 -->
          <div class="health-history solar-eye-search-model" v-show="tabKind == '3'">
            <a-row :gutter="24" class="solar-eye-search-content">
              <a-col :xxl="6" :xl="8" :md="10">
                <div class="search-item">
                  <span class="search-label">告警类型</span>
                  <a-cascader :options="alarmTypes" @change="pageChange(1)" placeholder="请选择" change-on-select :allowClear="false" v-model="alarmRemarks" style="width: 100%;"/>
                </div>
              </a-col>
              <a-col :xxl="6" :xl="8" :md="10" >
                <div class="search-item">
                        <span class="search-label">时间</span>
                    <a-range-picker v-model="happenTime" @change="pageChange(1)" />
                </div>
              </a-col>
            </a-row>
            <a-row>
              <a-col :xs="32" :sm="24" :md="24" :lg="24" :xl="24">
                 <div class="button-page">
                <div class="data-info">
                  <span style="font-weight: bold">电站名称:</span
                  ><span style="margin-left: 8px">{{ defaultInfo.psName }}</span>
                  <span style="margin-left: 48px; font-weight: bold">设备名称:</span
                  ><span style="margin-left: 8px">{{ defaultInfo.deviceName }}</span>
                </div>
                <a-pagination
                  style="display: inline-block; flex:1;text-align:right"
                  size="small"
                  default-current="1"
                  show-size-changer
                  :page-size-options="pageSizeOptions"
                  @change="currentChange"
                  @showSizeChange="onChange"
                  v-model="page"
                  show-quick-jumper
                  :total="total"
                  :show-total="(total) => `共 ${total} 条`"
                />
               </div>
              </a-col>

            </a-row>

            <vxe-table
              :data="data"
              :height="tableHeight - 274"
              ref="multipleTable"
              align="center"
              resizable
              border
              show-overflow
              highlight-hover-row
              size="small"
            >
              <vxe-table-column show-overflow="title" field="alarmTypeName" title="诊断类型" min-width="120">
                <template #header="{ column }">
                  <table-sort
                    :column="column"
                    filed="alarmTypeName"
                    @sortChange="mySortChange"
                    :downSort="downSort"
                    :upSort="upSort"
                    :sortFiled="sortFiled"
                  />
                </template>
                <template v-slot="{ row }">
                  <span @click="gotoPlayback(row)" class="blue">{{ row.alarmTypeName }}</span>
                </template>
              </vxe-table-column>
              <vxe-table-column show-overflow="title" field="alarmRemarkName" title="故障类型" min-width="120">

              </vxe-table-column>
              <vxe-table-column show-overflow="title" field="alarmReason" title="故障原因" min-width="120">
                <template v-slot="{ row }">
                <span> {{row.alarmReason ? row.alarmReason : "--" }}</span>
              </template>
              </vxe-table-column>
              <vxe-table-column show-overflow="title" field="eventStartTime" title="发生时间" min-width="120">
                <template #header="{ column }">
                  <table-sort
                    :column="column"
                    filed="eventStartTime"
                    @sortChange="mySortChange"
                    :downSort="downSort"
                    :upSort="upSort"
                    :sortFiled="sortFiled"
                  />
                </template>
              </vxe-table-column>
              <vxe-table-column show-overflow="title" field="lastHappenTime" title="更新时间" min-width="120">
                <template #header="{ column }">
                  <table-sort
                    :column="column"
                    filed="lastHappenTime"
                    @sortChange="mySortChange"
                    :downSort="downSort"
                    :upSort="upSort"
                    :sortFiled="sortFiled"
                  />
                </template>
                <template v-slot="{ row }">
                  <span >{{ row.lastHappenTime ? row.lastHappenTime : '--'}}</span>
                </template>
              </vxe-table-column>
              <vxe-table-column show-overflow="title" field="finishedTime" title="结束时间" min-width="120">
                <template #header="{ column }">
                  <table-sort
                    :column="column"
                    filed="finishedTime"
                    @sortChange="mySortChange"
                    :downSort="downSort"
                    :upSort="upSort"
                    :sortFiled="sortFiled"
                  />
                </template>
              </vxe-table-column>
              <vxe-table-column show-overflow="title" field="powerLoss" title="电量损失值" min-width="120">
                <template #header="{ column }">
                  <table-sort
                    :column="column"
                    filed="powerLoss"
                    @sortChange="mySortChange"
                    :downSort="downSort"
                    :upSort="upSort"
                    :sortFiled="sortFiled"
                  />
                </template>
              </vxe-table-column>
              <vxe-table-column show-overflow="title" field="handleTypeName" title="处理方式" min-width="120">
                <template  v-slot="{ row }">
                  <template v-if="row.handleType == '2'">
                    <span  @click="workOrderDetail(row, 'safety')" class="blue">{{ row.handleTypeName}}</span>
                  </template>
                  <template v-else> <span>{{ row.handleTypeName}}</span></template>
                </template>
              </vxe-table-column>
              <vxe-table-column show-overflow="title" field="declareUser" title="负责人" min-width="120">
                <template #header="{ column }">
                  <table-sort
                    :column="column"
                    filed="declareUser"
                    @sortChange="mySortChange"
                    :downSort="downSort"
                    :upSort="upSort"
                    :sortFiled="sortFiled"
                  />
                </template>
              </vxe-table-column>
              <template v-slot:empty>
                <span>查询无数据</span>
              </template>
            </vxe-table>
          </div>
        </a-col>
      </a-row>
    </a-spin>
    </a-drawer>
    <drawer-view ref="orderForm" parentId="AlarmCenter" />
  </div>
</template>
<script>
import {} from '@/api/health/healthapi.js';
import { getEventPlayBack, getPreviewURLsByRegionCode, healthTrajectoryList, getRegionByPsId } from '@/api/health/safetyEvents.js';
import videoMixin from '@/mixins/health/haikangH5Video.js';
import { AlarmEventMixins } from './../mixins/alarmEventMixins';
import { LeftMixin } from '@/mixins/LeftMixin';
import moment from 'moment';
export default {
  props: {},
  name: 'AlarmDetail',
  mixins: [videoMixin, AlarmEventMixins, LeftMixin],
  data () {
    return {
      currentSlide: 1, // 事件回放第一页
      totalSlide: 0,
      size: '10',
      page: 0, // 健康轨迹列表当前页数
      eventAlarmResult: [],
      alarmType: '',
      deviceName: '',
      imgUrl: [], // 当前页告警图片
      // alarmRemarkName:"", //告警类型
      alarmRemarks: [],
      deviceNo: '', // 设备编号
      startTime: '', // 事故发生时间
      endTime: '', // 事故结束时间
      priority: 1, // 事件等级 1高；2中；3低
      eventLevelValue: '', // 事件等级高中低
      regions: [], // 出事电站下的所有监控点
      deviceList: [],
      defaultInfo: {},
      pageloading: false,
      tabKind: '1',
      alarmDetailVisable: false,
      showOperationButton: true,
      happenTime: [],
      healthSearchData: {},
      playbackParams: {
        startTime: '',
        endTime: '',
        url0: '',
        url1: ''
      },
      realPlayParams: {
        url0: '',
        url1: ''
      },
      showBtn: false,
      windowIndex: 0
    };
  },
  created () {
    this.getAlarmTypes();
  },
  mounted () {
    this.pageLoading = false;
    // 健康轨迹-表格自适应高度
    let that = this;
    window.onresize = function () {
      that.tableHeight = document.body.offsetHeight;
    };
  },
  methods: {
    onClose (flag) {
      this.alarmDetailVisable = false;
      this.showOperationButton = true;
      this.tabKind = '1';
      this.imgUrl = [];
      this.priority = '';
      this.eventLevelValue = '';
      this.startTime = '';
      this.endTime = '';
      this.deviceName = '';
      // 健康轨迹-重置变量
      this.alarmType = '';
      this.happenTime = [];
      this.alarmRemarks = [];
      this.getHealthList();
      this.$emit('alarmDetailClose', flag);
    },
    // 将抽屉组件挂载
    getDom () {
      return document.getElementById('AlarmCenter');
    },
    // 抽屉组件切换动画结束后事件，异步请求事件须放在这里执行，否则可能会导致页面滑动卡死
    async afterVisibleChange () {
      if (this.alarmDetailVisable) {
        // 图表数据处理有可能提前 给出延时防止异步bug 后续可优化接口同步顺序
        this.getEventPlayBack(this.defaultInfo);
        getRegionByPsId({
          psId: this.defaultInfo.psId
        }).then(res => {
          if (res.result_code == '1') {
            this.regions = res.result_data;
            this.deviceName = this.regions[0].deviceName;
          } else {
            this.$message.error(res.result_msg);
          }
        }).catch(() => {
          this.pageloading = false;
        });
      }
    },
    getEventPlayBack (row) {
      let requestData = {
        regionIndexCode: row.regionIndexCode,
        cameraIndexCode: row.cameraIndexCode,
        eventStartTime: row.eventStartTime,
        lastHappenTime: row.lastHappenTime == '--' ? '' : row.lastHappenTime
      };
      this.stopAllPlay();
      // 获取事件回放、实时预览视频链接
      getEventPlayBack(requestData).then(res => {
        if (res.result_code == '1') {
          if (this.defaultInfo.cameraIndexCode == requestData.cameraIndexCode && this.defaultInfo.regionIndexCode == requestData.regionIndexCode) {
            this.eventAlarmResult = res.result_data;
            this.totalSlide = this.eventAlarmResult.length < 4 ? this.eventAlarmResult.length : 4;
            this.eventAlarmResult.forEach(item => {
              item.url = item.url.filter(child => {
                return child != null;
              });
            });
            this.onChangeTimePeriod(1);
          }
        } else {
          this.$message.error(res.result_msg);
        }
      }).catch(() => {
        this.pageloading = false;
      });
    },
    // 派发弹框关闭
    drawerClose (flag) {
      if (flag) {
        this.onClose(true);
      }
    },

    // 页面初始化变量
    init (row, kind) {
      this.alarmDetailVisable = true;
      this.defaultInfo = Object.assign({}, row);
      this.parentType = kind;
      this.$el.style.setProperty('display', 'block');
      this.initVideo();
      this.$nextTick(() => {
        this.getDom();
        if (document.getElementById('player')) {
          document.getElementById('player').addEventListener('contextmenu', e => {
            e.preventDefault();
          });
          this.player = this.createPlayer('player', 2);
          this.windowCallback(this.player);
        }
      });
    },
    reset () {},
    // 点击告警事件，到事件回放页面
    gotoPlayback (row) {
      this.player.JS_Resize();
      this.imgUrl = [];
      this.priority = '';
      this.eventLevelValue = '';
      this.startTime = '';
      this.endTime = '';
      this.alarmRemarkName = '';
      this.defaultInfo = Object.assign({}, row);
      this.tabKind = '1';
      this.getEventPlayBack(row);
      this.defaultInfo.alarmType = row.alarmType;
      this.showOperationButton = false;
    },
    // 健康轨迹-获取健康轨迹列表数据
    getHealthList () {
      this.fomartHealthSearchData();
      this.pageloading = true;
      healthTrajectoryList(this.healthSearchData)
        .then((res) => {
          if (res.result_code == '1') {
            this.data = res.result_data.pageList;
            this.dealData();
            this.total = res.result_data.rowCount;
          }
          this.pageloading = false;
        })
        .catch(() => {
          this.pageloading = false;
        });
    },
    // 处理列表电量损失、更新时间数据
    dealData () {
      this.data.forEach(item => {
        if (!item.powerLoss && typeof (item.powerLoss) != 'number') {
          item.powerLoss = '--';
        }
        if (!item.disappearTime && typeof (item.disappearTime) != 'number') {
          item.disappearTime = '--';
        }
        if (!item.lastHappenTime && typeof (item.lastHappenTime) != 'number') {
          item.lastHappenTime = '--';
        }
      });
    },
    // 健康轨迹-格式化查询、导出条件数据
    fomartHealthSearchData () {
      let obj = {};
      obj.psId = this.defaultInfo.psId;
      obj.psKey = this.defaultInfo.psKey;
      if (this.alarmRemarks.length > 0) {
        obj.alarmType = this.alarmRemarks[0];
        obj.alarmRemark = this.alarmRemarks.length == 2 ? this.alarmRemarks[1] : '';
      }
      if (this.happenTime.length > 0) {
        obj.startTime = this.happenTime[0].format('YYYY-MM-DD');
        obj.endTime = this.happenTime[1].format('YYYY-MM-DD');
      } else {
        obj.startTime = '';
        obj.endTime = '';
      }
      obj.sortFiled = this.sortFiled;
      obj.sortKind = this.sortKind;
      obj.size = this.size;
      obj.curPage = this.page;
      this.healthSearchData = obj;
    },
    // 健康轨迹-表格页签变化事件
    currentChange () {
      this.getHealthList();
    },
    // 健康轨迹-表格分页大小变化事件
    onChange (current, pageSize) {
      this.page = 1;
      this.size = pageSize;
      this.getHealthList();
    },
    // 健康轨迹-表格数据变化事件
    pageChange (val) {
      this.page = val;
      this.getHealthList();
    },
    // 根据权限标识和外部指示状态进行权限判断
    show (perms) {
      return this.showHandle(perms);
    },
    // tab页签点击事件
    async tabHandleClick (kind) {
      this.tabKind = kind;
      if (this.tabKind != '3') {
        this.player.JS_Resize();
      }
      if (this.tabKind == '1') {
        await this.stopAllPlay();
        this.onChangeTimePeriod(1);
      } else if (this.tabKind == '2') {
        // 调用接口前先停止播放
        await this.stopAllPlay();
        if (this.regions && this.regions.length > 0) {
          getPreviewURLsByRegionCode({ regionIndexCode: this.regions[0].regionIndexCode }).then(res => {
            let realPlayParams = {
              url0: res.result_data[0].url,
              url1: res.result_data[1].url
            };
            this.wndsRealPlay(realPlayParams);
          });
        }
      } else {
        this.getHealthList();
        this.tableHeight = document.body.offsetHeight;
      }
    },
    // 处理选择设备，获取实时播放视频
    handleAsyncChange (regionIndexCode) {
      if (regionIndexCode) {
        getPreviewURLsByRegionCode({ regionIndexCode: regionIndexCode }).then(res => {
          let realPlayParams = {
            url0: res.result_data ? res.result_data[0].url : '',
            url1: res.result_data ? res.result_data[1].url : ''
          };
          this.wndsRealPlay(realPlayParams);
        });
      }
    },
    // 获取事件回放时间段视频链接
    onChangeTimePeriod (currentSlide) {
      this.isPlay1 = true;
      this.isPlay2 = true;
      let resCur = this.eventAlarmResult[currentSlide - 1];
      this.imgUrl = [];
      this.priority = '';
      this.eventLevelValue = '';
      this.startTime = '';
      this.endTime = '';
      // this.alarmRemarkName = '';
      this.$nextTick(() => {
        this.imgUrl = resCur ? resCur.imgUrl : '';
        this.priority = resCur ? resCur.eventLevel : '';
        this.eventLevelValue = resCur ? resCur.eventLevelValue : '';
        this.startTime = resCur ? moment(resCur.startTime).format('YYYY-MM-DD HH:mm:ss') : '';
        this.endTime = resCur ? moment(resCur.endTime).format('YYYY-MM-DD HH:mm:ss') : '';
        // this.alarmRemarkName =   resCur?resCur.alarmRemark:''
      });
      this.playbackParams = {
        startTime: resCur.startTime,
        endTime: resCur.endTime,
        url0: resCur.url[0].split('?beginTime=')[0],
        url1: resCur.url[1].split('?beginTime=')[0]
      };
      this.wndsPlaybackStart(this.playbackParams);
    },
    wndPlaybackStartOri (index) {
      this.wndPlaybackStart(index, this.playbackParams);
    },
    windowEventOver (index) {
      this.windowIndex = index;
      this.showBtn = true;
    },
    windowEventOut (index) {
      this.windowIndex = index;
      this.showBtn = false;
    },
    downloadImage (url) {
      var a = document.createElement('a'); // 创建一个a节点插入的document
      var event = new MouseEvent('click'); // 模拟鼠标click点击事件
      a.download = '图片名字';
      a.target = '_blank'; // 设置a节点的download属性值
      a.href = url; // 将图片的src赋值给a节点的href
      a.dispatchEvent(event);
    }
  }
};
</script>
<style lang="less" scoped>
:deep(.ant-drawer-content .btn){
  border-top:0 !important
}
.btn {
  position: absolute;
  top: 274px;
  img {
    margin-left: 8px;
    cursor: pointer;
  }
}
.back-icon {
  position: absolute;
  right: 24px;
  top: 10px;
  font-size: 22px;
  z-index: 999;
  }
.item_box{
  height:32px;
  margin: 16px 0;
  .text{
    padding-right: 10px;
    width: 96px;
    height: 24px;
    text-align: right;
  }
}
.health-history{
    .button-page{
      display:flex;
      padding:10px 28px;
    }
}
.alarm-play{
    padding: 12px;
    margin-top: 12px;
  .page-end{
    text-align: center;
  }
  .alarm-pic{
    position: relative;
    width: 565px;
    height: 320px;
     min-height:320px;
    .alarm-img {
       width: 100%;
       height: 100%;
    }
    .download-img{
      object-fit: contain;
      object-position: 50% 50%;
      aspect-ratio: 1.77777778;
      width: 565px;
    }
    .download {
      position: absolute;
      width: 28px;
      top: 10px;
      right: 16px;
      cursor: pointer;
    }
  }
  .station-title{
    padding-left: 16px;
    font-weight: bold;
    margin: 12px 0;
    font-size:20px;
  }
  .operation-icon {
    position: absolute;
    right: 24px;
    font-size: 22px;
    .margin-left-6 {
      margin-left: 6px;
      cursor: pointer;
    }
  }
  .video-wrapper{
      display: inline-block;
      height:400px;
      min-width:1200px;
      .video{
          top: 0;
          right: 0;
          bottom: 0;
          left: 0;
          width: 1200px;
          height:320px ;
      }
    }
  .alarm-replay-video{
   :deep(.ant-carousel) {
      width:575px;
      padding-left:10px;
      display: inline-block;
      float:left;
      height: 340px;
      min-height:320px;
      .slick-slide {
        text-align: center;
        overflow: hidden;
      }
      .custom-slick-arrow {
        width: 25px;
        height: 25px;
        font-size: 25px;
        color: #fff;
        background-color: rgba(31, 45, 61, 0.11);
        opacity: 0.3;
      }
      .custom-slick-arrow:before {
        display: none;
      }
      .custom-slick-arrow:hover {
        opacity: 0.5;
      }
    }
    padding:10px 28px;
  }
  .data-info{
    padding:10px 28px;
    display: flex;
    .high{
         width: 50px;
          height: 19px;
          background-color: red;
          color: white;
          text-align: center;
          border-radius: 5px;
          line-height: 19px;
          margin-left: 8px

    }
    .middle{
       width: 43px;
          height: 19px;
      background-color:yellow;
       color: white;
          text-align: center;
          border-radius: 5px;
          line-height: 19px;
          margin-left: 8px
    }
  }
}
.blue{
  cursor: pointer;
}
.health-icon {
  color: var(--zw-conduct-color--normal);
  font-size: 28px;
}
.health-icon:hover {
  color: var(--zw-primary-color--default) !important;
}
</style>
