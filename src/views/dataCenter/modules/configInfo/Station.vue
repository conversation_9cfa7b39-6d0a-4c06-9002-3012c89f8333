<template>
  <div class="station-info">
    <a-card :body-style="{ padding: '16px' }" v-if="show('station:edit')">
      <div class="table-operator" style="margin: 32px 0 0">
        <a-button  @click="changeEditStatus" size="small" :class="{'solar-eye-btn-primary': isEdit ? false : true}">{{ isEdit ? '取消' : '编辑' }}</a-button>
        <a-button :class="{'solar-eye-btn-primary': isEdit ? true : false}" @click="saveData()" :disabled="!isEdit" size="small"> 保存 </a-button>
      </div>
    </a-card>
    <div :class="show('station:edit') ? 'health-base-param' : 'health-base-param unique'">
      <a-card title="基本参数">
        <!-- 阳光云电站 -->
        <a-form-model
           class="unable-edit-from"
          v-if="baseInfo.sungrowObtain && !isSelf"
          v-bind="{
            labelCol: {
              xs: { span: 24 },
              sm: { span: 6 },
            },
            wrapperCol: {
              xs: { span: 24 },
              sm: { offset: 1, span: 17 },
            },
          }"
        >
          <a-row>
            <a-col :xs="23" :sm="12" v-for="(item, index) in baseInfo.sungrowObtain" :key="index">
              <a-form-model-item class="item form-item-inject" :label="item.key">
                <span>{{ item.value ?item.value :'--' }}</span>
              </a-form-model-item>
            </a-col>
          </a-row>
        </a-form-model>
        <!-- 自主接入电站 -->
        <a-form-model
          v-if="isSelf"
          ref="baseForm"
          :rules="isEdit ? baseFormRules : {}"
          :model="baseForm"
          v-bind="{
            labelCol: {
              xs: { span: 24 },
              sm: { span: 6 },
            },
            wrapperCol: {
              xs: { span: 24 },
              sm: { offset: 1, span: 17 },
            },
          }"
        >
          <a-row>
            <a-col :xs="23" :sm="12">
              <a-form-model-item label="电站名称" prop="psName">
                <span>{{ getBaseInfoValue('电站名称') || '--' }} </span>
              </a-form-model-item>
            </a-col>
             <!-- <a-col :xs="23" :sm="12">
              <a-form-model-item label="电站编码" prop="stationCode">
                <a-input v-if="isEdit" v-model="baseForm.stationCode" @change="baseInfoChange('电站编码', baseForm.stationCode)" :maxLength="50"
                  @blur="baseForm.stationCode = $trim($event)" placeholder="输入电站编码"/>
                <span v-else>{{ getBaseInfoValue('电站编码') || '--' }} </span>
              </a-form-model-item>
            </a-col> -->
                <a-col :xs="23" :sm="12">
              <a-form-model-item label="电站类型" prop="stationType">
                <span>{{ getBaseInfoValue('电站类型') || '--' }} </span>
              </a-form-model-item>
            </a-col>
                <a-col :xs="23" :sm="12">
              <a-form-model-item label="接入状态" prop="validFlag">
                <a-select v-if="isEdit" v-model="baseForm.validFlag" @change="(val, option) => baseInfoChange('电站状态id', val, '电站状态', option)"
                          :getPopupContainer="(node) => node.parentNode" placeholder="请选择">
                  <a-select-option name="已接入" value="1">已接入</a-select-option>
                  <a-select-option name="已关闭" value="2">已关闭</a-select-option>
                  <a-select-option name="未接入" value="3">未接入</a-select-option>
                </a-select>
                <span v-else>{{ getBaseInfoValue('电站状态') || '--' }} </span>
              </a-form-model-item>
            </a-col>
             <a-col :xs="23" :sm="12">
              <a-form-model-item label="光伏装机容量(kWp)" prop="totalCapcity">
                <span>{{ getBaseInfoValue('电站容量') || '--' }} </span>
              </a-form-model-item>
            </a-col>
               <a-col :xs="23" :sm="12">
              <a-form-model-item label="储能装机容量(kWh)" prop="storageCapacity">
                <span>{{ getBaseInfoValue('储能装机容量(kWh)') || '--' }} </span>
              </a-form-model-item>
            </a-col>
              <a-col :xs="23" :sm="12">
              <a-form-model-item label="储能额定功率(kW)" prop="storageRating">
                <span>{{ getBaseInfoValue('储能额定功率(kW)') || '--' }} </span>
              </a-form-model-item>
            </a-col>
               <a-col :xs="23" :sm="12">
              <a-form-model-item label="所属项目" prop="psaId">
                <span>{{ getBaseInfoValue('所属项目') || '--' }} </span>
              </a-form-model-item>
            </a-col>
            <a-col :xs="23" :sm="12">
              <a-form-model-item label="所属国家(省/市/区)" prop="psLocationList">
                <span>{{ getBaseInfoValue('所属国家(省/市/区)') || '--' }} </span>
              </a-form-model-item>
            </a-col>
            <a-col :xs="23" :sm="12">
              <a-form-model-item label="详细地址" prop="addressDetail">
                <span>{{ getBaseInfoValue('详细地址') || '--' }}</span>
              </a-form-model-item>
            </a-col>
            <a-col :xs="23" :sm="12">
              <a-form-model-item label="经度" prop="longitude">
                <span>{{ getBaseInfoValue('经度') || '--' }}</span>
              </a-form-model-item>
            </a-col>
            <a-col :xs="23" :sm="12">
              <a-form-model-item label="纬度" prop="latitude">
                <span>{{ getBaseInfoValue('纬度') || '--' }} </span>
              </a-form-model-item>
            </a-col>
            <a-col :xs="23" :sm="12">
              <a-form-model-item label="建站时间" prop="installDate">
                <span>{{ getBaseInfoValue('建站时间') || '--'}} </span>
              </a-form-model-item>
            </a-col>
          </a-row>
        </a-form-model>
      </a-card>
      <template >
        <div class="solar-eye-gap"></div>
        <a-card title="采集器及智能设备">
          <div class="operation-btn">
            <a-button class="solar-eye-btn-primary" @click="doExport(provideData.psId)">导出IEC测点</a-button>
            <a-button v-show="list.length < 1000" class="solar-eye-btn-primary" :disabled="!isEdit" @click="addCollector">添加</a-button>
          </div>
          <div class="form-list" id="formList" :style="{height: list.length > 5 ? '350px' : list.length * 70 + 'px'}">
            <RecycleScroller
              class="scroller"
              :items="list"
              :item-size="70"
              v-if="list.length"
              key-field="key"
            >
              <template v-slot="{ item, index }">
                <a-form-model :ref="'collectorForm' + index" :model="item" :rules="isEdit ? collectorFormRules : {}" :labelCol="{ style:'width: 95px' }" :wrapperCol="{ style:'width: calc(100% - 95px)' }">
                  <a-row>
                    <a-col :span="4">
                      <a-form-model-item label="采集器名称" prop="collectorName">
                        <a-input @change="collectInfoChange(index, 'collectorName', item.collectorName)" v-if="isEdit" v-model="item.collectorName"
                          @blur="item.collectorName = $trim($event)" :maxLength="30" placeholder="采集器名称"/>
                        <span class="form-text" v-else :title="getCollectInfoValue('collectorName', index) || '--'">
                          {{ formatString(getCollectInfoValue('collectorName', index)) }}
                        </span>
                      </a-form-model-item>
                    </a-col>
                     <a-col :span="4">
                      <a-form-model-item label="用途" prop="purpose" :wrapperCol="{ style:'width: calc(100% - 90px)' }" :labelCol="{ style:'width: 70px' }">
                        <a-select  v-if="isEdit" v-model="item.purpose" @change="(val, option) => collectInfoChange(index, '用途ID', val, '用途', option)"
                          placeholder="请选择" :ref="'select' + index" :disabled="getCollectInfoValue('主键ID', index)">
                          <a-select-option v-for="item in useList" :value="item.secondTypeCode" :key="item.secondName" :data-deviceType="item.deviceType">
                            {{ item.secondName }}
                          </a-select-option>
                        </a-select>
                        <span class="form-text" v-else :title="getCollectInfoValue('用途', index)">
                          {{ formatString(getCollectInfoValue('用途', index)) }}
                        </span>
                      </a-form-model-item>
                    </a-col>
                    <a-col :span="4">
                      <a-form-model-item label="厂家" prop="factory">
                       <a-select
                   @change="(val, option) => collectInfoChange(index, '厂家ID',val, '厂家', option)" v-if="isEdit" v-model="item.factory"
                    placeholder="请选择"
                  >
                    <a-select-option v-for="item in item.factoryList" :key="item.maker" :value="item.makerId.toString()">
                      {{ item.maker }}
                    </a-select-option>
                  </a-select>
                        <!-- <a-input
                          @blur="item.factory = $trim($event)" :maxLength="50" placeholder="输入厂家"/> -->
                        <span class="form-text" v-else :title="getCollectInfoValue('厂家', index) || '--'">
                          {{ formatString(getCollectInfoValue('厂家', index)) }}
                        </span>
                      </a-form-model-item>
                    </a-col>

                    <a-col :span="4">
                      <a-form-model-item label="通讯模块SN" prop="sn">
                        <a-input v-if="isEdit" v-model="item.sn" @change="collectInfoChange(index, '通讯模块SN', item.sn)"
                          :maxLength="20" placeholder="输入SN号"/>
                        <span v-else class="form-text sn-text" :title="getCollectInfoValue('通讯模块SN', index)"  @click="getIecPoints(getCollectInfoValue('通讯模块SN', index))">
                          {{ formatString(getCollectInfoValue('通讯模块SN', index) || '--') }}
                        </span>
                      </a-form-model-item>
                    </a-col>
                    <a-col :span="4">
                      <a-form-model-item label="描述" prop="description" :wrapperCol="{ style:'width: calc(100% - 80px)' }" :labelCol="{ style:'width: 60px' }">
                        <a-input v-if="isEdit" v-model="item.description" @change="collectInfoChange(index, '描述', item.description)"
                        :maxLength="100" placeholder="输入描述" @blur="item.description = $trim($event)" />
                        <span class="form-text" v-else :title="getCollectInfoValue('描述', index) || '--'">
                          {{ formatString(getCollectInfoValue('描述', index) || '--') }}
                        </span>
                      </a-form-model-item>
                    </a-col>
                    <a-col :span="4">
                      <div class="form-btn">
                        <span class="status-text">状态</span> <span class="state" :class="getStatusClassOrText(item.state, 'class')">{{getStatusClassOrText(item.state, 'text')}}</span>
                        <a-button v-if="((item.overTime == '1' || item.state== '0') && !isEdit) || (isEdit && !getCollectInfoValue('主键ID', index))"
                           @click="deleteSn(index)" class="solar-eye-btn-primary-cancel">删除</a-button>
                        <a-switch v-if="item.state== '2' && !isEdit" :checked="item.isDelete == '1'" @change="val => changeStatus(val, index)"
                          checked-children="拆除" un-checked-children="未拆除" class="remove-btn"  />
                      </div>
                    </a-col>
                  </a-row>
                </a-form-model>
              </template>
            </RecycleScroller>
          </div>
        </a-card>
      </template>
      <div class="solar-eye-gap"></div>
      <a-card title="电站设备基本信息" class="unable-edit-from">
        <a-form-model
          v-if="baseInfo.systemObtain"
          v-bind="{
            labelCol: {
              xs: { span: 24 },
              sm: { span: 6 },
            },
            wrapperCol: {
              xs: { span: 24 },
              sm: { offset: 1, span: 17 },
            },
          }"
        >
          <a-row>
            <a-col :xs="23" :sm="12" v-for="(item, index) in baseInfo.systemObtain" :key="index">
              <a-form-model-item class="item form-item-inject" :label="item.key">
                <span>{{ item.value ?item.value:'--' }}</span>
              </a-form-model-item>
            </a-col>
          </a-row>
        </a-form-model>
      </a-card>
      <div class="solar-eye-gap"></div>
      <a-card title="清洁度评估参数配置">
        <a-form-model
          v-if="baseInfo.attributeConfig"
          ref="form"
          :rules="rules"
          :model="baseInfo.attributeConfig"
          v-bind="{
            labelCol: {
              xs: { span: 24 },
              sm: { span: 6 },
            },
            wrapperCol: {
              xs: { span: 24 },
              sm: { offset: 1, span: 17 },
            },
          }"
        >
          <a-row>
            <a-col :xs="23" :sm="12">
              <a-form-model-item class="item form-item-inject" label="引入环境监测仪">
                <a-select v-if="isEdit" v-model="baseInfo.attributeConfig.p120036" style="width: 100%" showSearch
                  :getPopupContainer="(node) => node.parentNode" allowClear :filter-option="filterOption">
                  <a-select-option
                    v-for="item in meteoPsList"
                    :key="item.psId"
                    :label="item.psName"
                    :value="item.psId"
                    >{{ item.psName }}</a-select-option
                  >
                </a-select>
                <span v-else>{{metoPsName() ? metoPsName():'--'}} </span>
              </a-form-model-item>
            </a-col>
            <a-col :xs="23" :sm="12">
              <a-form-model-item class="item form-item-inject" label="状态标牌：" >
                <a-select v-if="isEdit" v-model="baseInfo.attributeConfig.statusSign" :getPopupContainer="(node) => node.parentNode" style="width: 100%" allowClear>
                  <a-select-option
                    v-for="item in stationSelectType['0059']"
                    :key="item.secondTypeCode"
                    :label="item.secondName"
                    :value="item.secondTypeCode"
                    >{{ item.secondName }}</a-select-option
                  >
                </a-select>
                <span v-else>{{ formatterAttributeConfig('statusSign', '0059') }}</span>
              </a-form-model-item>
            </a-col>
            <a-col :xs="23" :sm="12">
              <a-form-model-item class="item form-item-inject" label="电站类型：" prop="p120013">
                <a-select v-if="isEdit" v-model="baseInfo.attributeConfig.p120013" :getPopupContainer="(node) => node.parentNode" style="width: 100%">
                  <a-select-option
                    v-for="item in stationSelectType['0025']"
                    :key="item.secondTypeCode"
                    :label="item.secondName"
                    :value="item.secondTypeCode"
                    >{{ item.secondName }}</a-select-option
                  >
                </a-select>
                <span v-else>{{ formatterAttributeConfig('p120013', '0025') }}</span>
              </a-form-model-item>
            </a-col>
            <a-col :xs="23" :sm="12">
              <a-form-model-item class="item form-item-inject" label="支架类型：" prop="p120014">
                <a-select v-if="isEdit" v-model="baseInfo.attributeConfig.p120014" :getPopupContainer="(node) => node.parentNode" style="width: 100%">
                  <a-select-option
                    v-for="item in stationSelectType['0026']"
                    :key="item.secondTypeCode"
                    :label="item.secondName"
                    :value="item.secondTypeCode"
                    >{{ item.secondName }}</a-select-option
                  >
                </a-select>
                <span v-else>{{ formatterAttributeConfig('p120014', '0026') }}</span>
              </a-form-model-item>
            </a-col>
            <a-col :xs="23" :sm="12">
              <a-form-model-item class="item form-item-inject" label="系统类型：" prop="p120015">
                <a-select v-if="isEdit" v-model="baseInfo.attributeConfig.p120015" :getPopupContainer="(node) => node.parentNode" style="width: 100%">
                  <a-select-option
                    v-for="item in stationSelectType['0002']"
                    :key="item.secondTypeCode"
                    :label="item.secondName"
                    :value="item.secondTypeCode"
                    >{{ item.secondName }}</a-select-option
                  >
                </a-select>
                <span v-else>{{ formatterAttributeConfig('p120015', '0002') }}</span>
              </a-form-model-item>
            </a-col>
            <a-col :xs="23" :sm="12">
              <a-form-model-item class="item form-item-inject" label="全容量并网日期：" prop="p120016">
                <a-date-picker
                  v-if="isEdit"
                  v-model="baseInfo.attributeConfig.p120016"
                  type="date"
                  format="YYYY-MM-DD"
                  value-format="YYYY-MM-DD"
                  placeholder="选择日期"
                  style="width: 100%"
                />
                <span v-else>{{ formatterAttributeConfig('p120016') }}</span>
              </a-form-model-item>
            </a-col>
            <a-col :xs="23" :sm="12">
              <a-form-model-item class="item form-item-inject" label="售电电价：" prop="p120017">
                <a-input
                  v-if="isEdit"
                  style="width: 100%"
                  v-model="baseInfo.attributeConfig.p120017"
                  placeholder="请输入售电电价"
                />
                <span v-else>{{ formatterAttributeConfig('p120017') }}</span>
              </a-form-model-item>
            </a-col>
            <!-- <a-col :xs="23" :sm="12">
              <a-form-model-item class="item form-item-inject" label="采集器设备号：" prop="p120018">
                <a-input
                  v-if="isEdit"
                  style="width: 100%"
                  v-model="baseInfo.attributeConfig.p120018"
                  placeholder="请输入采集器设备号"
                />
                <span v-else>{{ formatterAttributeConfig('p120018') }}</span>
              </a-form-model-item>
            </a-col>
            <a-col :xs="23" :sm="12">
              <a-form-model-item class="item form-item-inject" label="通道1安装位置：" prop="p120019">
                <a-input
                  v-if="isEdit"
                  style="width: 100%"
                  v-model="baseInfo.attributeConfig.p120019"
                  placeholder="请输入通道1安装位置"
                />
                <span v-else>{{ formatterAttributeConfig('p120019') }}</span>
              </a-form-model-item>
            </a-col>
            <a-col :xs="23" :sm="12">
              <a-form-model-item class="item form-item-inject" label="通道2安装位置：" prop="p120020">
                <a-input
                  v-if="isEdit"
                  style="width: 100%"
                  v-model="baseInfo.attributeConfig.p120020"
                  placeholder="请输入通道2安装位置"
                />
                <span v-else>{{ formatterAttributeConfig('p120020') }}</span>
              </a-form-model-item>
            </a-col>
            <a-col :xs="23" :sm="12">
              <a-form-model-item class="item form-item-inject" label="通道3安装位置：" prop="p120021">
                <a-input
                  v-if="isEdit"
                  style="width: 100%"
                  v-model="baseInfo.attributeConfig.p120021"
                  placeholder="请输入通道3安装位置"
                />
                <span v-else>{{ formatterAttributeConfig('p120021') }}</span>
              </a-form-model-item>
            </a-col>
            <a-col :xs="23" :sm="12">
              <a-form-model-item class="item form-item-inject" label="通道4安装位置：" prop="p120022">
                <a-input
                  v-if="isEdit"
                  style="width: 100%"
                  v-model="baseInfo.attributeConfig.p120022"
                  placeholder="请输入通道4安装位置"
                />
                <span v-else>{{ formatterAttributeConfig('p120022') }}</span>
              </a-form-model-item>
            </a-col> -->
            <a-col :xs="23" :sm="12">
              <a-form-model-item class="item form-item-inject" label="清洗单价：" prop="p120023">
                <a-input
                  v-if="isEdit"
                  style="width: 100%"
                  v-model="baseInfo.attributeConfig.p120023"
                  placeholder="请输入清洗单价"
                />
                <span v-else>{{ formatterAttributeConfig('p120023') }}</span>
              </a-form-model-item>
            </a-col>
            <a-col :xs="23" :sm="12">
              <a-form-model-item class="item form-item-inject" label="电站地址：" prop="p120024">
                <a-input
                  v-if="isEdit"
                  style="width: 100%"
                  v-model="baseInfo.attributeConfig.p120024"
                  placeholder="请输入电站地址"
                />
                <span v-else>{{ formatterAttributeConfig('p120024') }}</span>
              </a-form-model-item>
            </a-col>
            <!--<a-col :xs="23" :sm="12">
              <a-form-model-item class="item form-item-inject" label="项目id：" prop="p120025">
                <a-input
                  v-if="isEdit"
                  style="width: 100%"
                  v-model="baseInfo.attributeConfig.p120025"
                  placeholder="请输入项目id"
                />
                <span v-else>{{ formatterAttributeConfig('p120025') }}</span>
              </a-form-model-item>
            </a-col> -->
            <a-col :xs="23" :sm="12">
              <a-form-model-item class="item form-item-inject" label="是否开启清洗度诊断：" prop="p120026">
                <a-select v-if="isEdit" v-model="baseInfo.attributeConfig.p120026" :getPopupContainer="(node) => node.parentNode" style="width: 100%">
                  <a-select-option
                    v-for="item in stationSelectType['0042']"
                    :key="item.secondTypeCode"
                    :label="item.secondName"
                    :value="item.secondTypeCode"
                    >{{ item.secondName }}</a-select-option
                  >
                </a-select>
                <span v-else>{{ formatterAttributeConfig('p120026', '0042') }}</span>
              </a-form-model-item>
            </a-col>
            <a-col :xs="23" :sm="12">
              <a-form-model-item class="item form-item-inject" label="污染类型：" prop="p120027">
                <a-select v-if="isEdit" v-model="baseInfo.attributeConfig.p120027" :getPopupContainer="(node) => node.parentNode" style="width: 100%">
                  <a-select-option
                    v-for="item in stationSelectType['0046']"
                    :key="item.secondTypeCode"
                    :label="item.secondName"
                    :value="item.secondTypeCode"
                    >{{ item.secondName }}</a-select-option
                  >
                </a-select>
                <span v-else>{{ formatterAttributeConfig('p120027', '0046') }}</span>
              </a-form-model-item>
            </a-col>
            <!-- <a-col :xs="23" :sm="12">
              <a-form-model-item class="item form-item-inject" label="电站所属区域：" prop="p120028">
                <a-cascader
                  v-if="isEdit"
                  v-model="selectedNames"
                  @change="areaChange"
                  :field-names="{
                    label: 'nameCn',
                    value: 'nameCn',
                    children: 'children',
                  }"
                  expand-trigger="hover"
                  :options="areaOptions"
                  placeholder="请选择电站所属区域"
                />
                <span v-else>{{ selectedNames.length ? selectedNames.join('/') : '--' }}</span>
              </a-form-model-item>
            </a-col> -->

            <a-col :xs="23" :sm="12">
              <a-form-model-item class="item" label="标准组串容量(kWp)：" prop="p120029">
                <a-input
                  style="width: 100%"
                  v-model="baseInfo.attributeConfig.p120029"
                  placeholder="请输入标准组串容量"
                  v-if="isEdit"
                ></a-input
                ><span v-else>{{ formatterAttributeConfig('p120029') }}</span>
              </a-form-model-item>
            </a-col>
            <a-col :xs="23" :sm="12">
              <a-form-model-item class="item" label="清洗系统效率下限(0-1)：" prop="p120030">
                <a-input
                  style="width: 100%"
                  v-model="baseInfo.attributeConfig.p120030"
                  placeholder="请输入清洗系统效率下限"
                  v-if="isEdit"
                ></a-input
                ><span v-else>{{ formatterAttributeConfig('p120030') }}</span>
              </a-form-model-item>
            </a-col>
            <a-col :xs="23" :sm="12">
              <a-form-model-item class="item" label="清洗系统效率上限(0-1)：" prop="p120031">
                <a-input
                  style="width: 100%"
                  v-model="baseInfo.attributeConfig.p120031"
                  placeholder="请输入清洗系统效率上限"
                  v-if="isEdit"
                ></a-input
                ><span v-else>{{ formatterAttributeConfig('p120031') }}</span>
              </a-form-model-item>
            </a-col>
            <a-col :xs="23" :sm="12">
              <a-form-model-item class="item" label="预设投资收益率(0-1)：" prop="p120032">
                <a-input
                  style="width: 100%"
                  v-model="baseInfo.attributeConfig.p120032"
                  placeholder="请输入预设投资收益率"
                  v-if="isEdit"
                ></a-input
                ><span v-else>{{ formatterAttributeConfig('p120032') }}</span>
              </a-form-model-item>
            </a-col>
          </a-row>
        </a-form-model>
      </a-card>
    </div>
    <location ref="location" @setLngLat="setLngLat" />
    <points ref="points" />
  </div>
</template>
<script>
import points from '../station/points';
import baseInfo from '../../mixins/baseInfo';
import { StationMixins } from '../../mixins/station.js';
import { getAllRegion, getMeteoPsList, deleteCollectorBySN, exportSnAndPoint, removeCollectorBySN, getDeviceModelList } from '@/api/health/healthapi.js';
import moment from 'moment';
import 'vue-virtual-scroller/dist/vue-virtual-scroller.css';
export default {
  name: 'PowerInfo',
  mixins: [baseInfo, StationMixins],
  components: {
    points
  },
  props: {
    isSelf: {
      type: Boolean,
      default: true
    },
    deviceType: {
      type: String,
      default: ''
    }
  },
  data () {
    const checkOneToZero = (rule, value, callback) => {
      let reg = /^(0.\d+|0|1)$/;
      if (value && !reg.test(value)) {
        callback(new Error('请输入0-1的数字'));
      } else {
        callback();
      }
    };
    return {
      areaOptions: [],
      selectedNames: [],
      isEdit: false,
      meteoPsList: [],
      psName: '',
      rules: {
        p120013: [
          {
            required: true,
            trigger: 'change',
            message: '请选择电站类型'
          }
        ],
        p120014: [
          {
            required: true,
            trigger: 'change',
            message: '请选择支架类型'
          }
        ],
        p120030: [
          {
            validator: checkOneToZero,
            trigger: 'blur'
          }
        ],
        p120031: [
          {
            validator: checkOneToZero,
            trigger: 'blur'
          }
        ],
        p120032: [
          {
            validator: checkOneToZero,
            trigger: 'blur'
          }
        ]
      }
    };
  },
  created () {
    // this.getDeviceInfo()
    this.getSystemCodeList();
    this.getAreaOptions();
    this.getMeteoPsList();
    this.useList = this.purposeOptions;
  },
  computed: {
    filteredOptions () {
      return this.selectedOptions['0025'].filter((o) => !this.baseInfo.attributeConfig.p120033.includes(o.secondName));
    },
    useList () {
      return this.isSelf ? this.purposeOptions : this.purposeOptions.filter(item => {
        return item.secondTypeCode != '4';
      });
    }

  },
  methods: {
    moment,
    metoPsName () {
      let psName = ''; let psId = this.baseInfo.attributeConfig.p120036;
      this.meteoPsList.forEach(item => {
        if (item.psId == psId) {
          psName = item.psName;
        }
      });
      return psName;
    },
    getMeteoPsList () {
      getMeteoPsList({
        psId: this.provideData.psId
      }).then(res => {
        res.result_data.map(item => {
          item.psId = item.psId.toString();
        });
        this.meteoPsList = res.result_data;
      });
    },
    getAreaOptions () {
      getAllRegion().then((res) => {
        this.areaOptions = res.result_data;
        this.setAreaName(this.selectedNames);
        if (this.isSelf) {
          this.$nextTick(() => {
            document.getElementById('formList').addEventListener('scroll', this.scrollEvent, true);
            document.getElementsByClassName('health-base-param')[0].addEventListener('scroll', this.clearSelect, true);
          });
        }
      });
    },
    areaChange (value, selectedOptions) {
      if (selectedOptions.length) {
        this.baseInfo.attributeConfig.p120028 = selectedOptions[selectedOptions.length - 1].areaId;
      } else {
        this.baseInfo.attributeConfig.p120028 = null;
      }
      this.setAreaName(this.selectedNames);
    },
    setAreaName (selectedNames) {
      selectedNames.length = 0;
      let areaId = this.baseInfo.attributeConfig.p120028;
      if (!areaId) {
        return;
      }
      let target = JSON.parse(JSON.stringify(this.areaOptions));
      if (!target || !target.length) {
        return;
      }
      let selectedNode = null;
      const loopTree = (targetData, parent) => {
        if (targetData.push && targetData.length) {
          for (let ele of targetData) {
            if (parent) {
              ele.parent = parent;
            }
            if (ele.areaId == areaId) {
              selectedNode = ele;
              return;
            }
            if (ele.children) {
              loopTree(ele.children, ele);
            }
          }
        }
      };
      loopTree(target, null);
      let node = selectedNode;
      selectedNames.push(node.nameCn);
      while (node.parent) {
        node = node.parent;
        selectedNames.unshift(node.nameCn);
      }
    },
    formatterAttributeConfig (key, dictCode) {
      let val = this.baseInfo.attributeConfig[key];
      if (!dictCode) {
        return val || '--';
      }
      val = this.getCodeName(dictCode, val);
      return val || '--';
    },
    async saveData () {
      this.saveFlag = true;
      let valid1 = this.isSelf ? await this.$refs.baseForm.validate().catch(() => {}) : true;
      let valid2 = await this.$refs.form.validate().catch(() => {});
      let valid3 = true;
      let arr = [];
      this.list.forEach(async (item, index) => {
        if (!item.factory || !item.purpose || !item.sn || !item.collectorName) {
          valid3 = false;
        }
        if (this.$refs['collectorForm' + index]) {
          await this.$refs['collectorForm' + index].validateField(['factory', 'purpose', 'sn', 'collectorName'], error => {
            if (error) {
              arr.push(error);
            }
          });
        }
      });
      if (arr.length > 0) valid3 = false;
      if (valid1 && valid2 && valid3) {
        this.$confirm({
          title: '提示',
          content: '是否确认修改？',
          okText: '确定',
          cancelText: '取消',
          onOk: () => {
            let psConfig = this.baseInfo.attributeConfig;
            if (this.isSelf) {
              this.baseInfo.sungrowObtain.forEach(item => {
                psConfig[item.key] = item.value;
              });
            }
            let data = {
              psConfig: psConfig,
              collectorConfig: this.baseInfo.collectorConfig,
              psId: this.provideData.psId,
              flag: 0,
              isStation: true
            };
            console.log(data.collectorConfig);
            if (this.isSelf) {
              this.$emit('saveData', data, this.getBaseInfoValue('电站名称'));
            } else {
              this.$emit('saveData', data);
            }
          }
        });
      }
    },
    // 获取用途获取生产厂
    getDeviceMaker (deviceType) {
      return new Promise((resolve, reject) => {
        getDeviceModelList({ manageDeviceTypeList: [deviceType].join(','), groupBy: 'makerId' }).then(res => {
          if (res) {
            resolve(res);
          } else {
            reject(res);
          }
        }).catch((error) => {
          reject(error);
        });
      });
    },
    changeEditStatus () {
      let storageForm = JSON.parse(this.storageForm);
      this.baseInfo.attributeConfig = storageForm.attributeConfig;
      this.baseInfo.sungrowObtain = storageForm.sungrowObtain;
      if (this.$refs.form) {
        this.$refs.form.clearValidate();
      }
      if (this.$refs.baseForm) {
        this.$refs.baseForm.clearValidate();
      }
      this.list.forEach((item, index) => {
        if (this.$refs['collectorForm' + index]) {
          this.$refs['collectorForm' + index].clearValidate();
        }
      });
      this.isEdit = !this.isEdit;
      this.saveFlag = false;
      // if (this.isSelf) {
      if (storageForm.collectorConfig) {
        this.baseInfo.collectorConfig = storageForm.collectorConfig;
        this.setCollectForm();
      } else {
        this.list = [];
      }
      if (this.isEdit) {
        this.setBaseForm();
      }
      // }
    },
    filterOption (input, option) {
      return (
        option.componentOptions.children[0].text.toLowerCase().indexOf(input.toLowerCase()) >= 0
      );
    },
    setBaseForm () {
      this.psName = this.getBaseInfoValue('所属项目');
      this.baseForm = {
        psName: this.getBaseInfoValue('电站名称'),
        psaId: this.getBaseInfoValue('所属项目'),
        validFlag: this.getBaseInfoValue('电站状态id'),
        totalCapcity: this.getBaseInfoValue('电站容量'),
        longitude: this.getBaseInfoValue('经度'),
        latitude: this.getBaseInfoValue('纬度'),
        psLocationList: this.getBaseInfoValue('所属国家(省/市/区)id') ? this.getBaseInfoValue('所属国家(省/市/区)id').split(',') : [],
        psLocation: this.getBaseInfoValue('所属国家(省/市/区)'),
        addressDetail: this.getBaseInfoValue('详细地址'),
        installDate: this.getBaseInfoValue('建站时间'),
        storageRating: this.getBaseInfoValue('储能额定功率(kW)'),
        storageCapacity: this.getBaseInfoValue('储能装机容量(kWh)'),
        stationType: this.getBaseInfoValue('电站类型id') ? this.getBaseInfoValue('电站类型id').split(',') : [],
        stationCode: this.getBaseInfoValue('电站编码')
      };
    },
    setCollectForm () {
      this.list = [];
      this.baseInfo.collectorConfig.forEach((item, index) => {
        let form = {
          collectorName: this.getCollectInfoValue('collectorName', index),
          factory: this.getCollectInfoValue('厂家ID', index),
          purpose: this.getCollectInfoValue('用途ID', index),
          sn: this.getCollectInfoValue('通讯模块SN', index),
          description: this.getCollectInfoValue('描述', index),
          state: this.getCollectInfoValue('状态', index),
          overTime: this.getCollectInfoValue('overTime', index),
          isDelete: this.getCollectInfoValue('是否可拆除', index),
          key: index + new Date().getTime(),
          deviceType: item.deviceType,
          factoryList: [],
          psKey: item.psKey
        };
        this.list.push(form);
      });
      this.list.forEach(async item => {
        let data = await this.getDeviceMaker(item.deviceType);
        item.factoryList = data;
      });
    },
    getStationChange (val, node) {
      this.baseForm.psaId = node.id;
      this.baseInfoChange('所属项目', node.name);
      this.baseInfoChange('所属项目id', node.id);
    },
    baseInfoChange (name1, val1, name2, option, toString) {
      this.baseInfo.sungrowObtain.map(item => {
        if (item.key == name1) {
          if (name1 == '建站时间') {
            item.value = val1 ? moment(val1).format('YYYY-MM-DD HH:mm:ss') : null;
          } else {
            item.value = val1;
          }
        };
        let isArray = option && Array.isArray(option);
        if (option && !isArray && item.key == name2) {
          item.value = option.data.attrs.name;
        }
        if (isArray && item.key == name2) {
          item.value = [];
          option.forEach(child => {
            item.value.push(child.data.attrs.name);
          });
        }
        if (toString && Array.isArray(item.value)) {
          item.value = item.value.join(',');
        }
        console.log(item.value);
      });
    },
    async collectInfoChange (index, name1, val1, name2, option) {
      this.baseInfo.collectorConfig[index][name1] = val1;

      if (option) {
        this.baseInfo.collectorConfig[index][name2] = option.data.key;
      }
      if (name1 == '用途ID') {
        this.baseInfo.collectorConfig[index]['厂家'] = undefined;
        this.baseInfo.collectorConfig[index]['厂家ID'] = undefined;
        this.list[index].factory = undefined;
        let result = await this.getDeviceMaker(option.data.attrs['data-deviceType']);
        this.list[index].factoryList = result;
      }
    },
    deleteSn (index) {
      if (this.baseInfo.collectorConfig[index]['主键ID']) {
        this.$confirm({
          title: '提示',
          content: '是否删除此采集器？',
          okText: '确定',
          cancelText: '取消',
          onOk: () => {
            let obj = {
              sn: this.baseInfo.collectorConfig[index]['通讯模块SN'],
              id: this.baseInfo.collectorConfig[index]['主键ID']
            };
            deleteCollectorBySN(obj).then(res => {
              if (res.result_code == '1') {
                this.$message.success('删除成功！');
                this.deleteForm(index);
                this.baseInfo.collectorConfig.splice(index, 1);
                this.getDeviceInfo(true);
                this.$emit('setChangeData', true);
              }
            });
          }
        });
      } else {
        this.deleteForm(index);
        this.baseInfo.collectorConfig.splice(index, 1);
      }
    },
    getStatusClassOrText (state, type) {
      let className = '';
      let text = '';
      if (state == '0') {
        className = 'unjoin-state';
        text = '未接入';
      } else if (state == '1') {
        className = 'join-state';
        text = '已接入';
      } else {
        className = 'off-state';
        text = '离线';
      }
      if (type == 'class') {
        return className;
      } else {
        return text;
      }
    },
    changeStatus (val, index) {
      this.$confirm({
        title: '提示',
        content: '是否' + (val ? '拆除' : '恢复') + '此采集器？',
        okText: '确定',
        cancelText: '取消',
        onOk: () => {
          removeCollectorBySN({ psId: this.provideData.psId, sn: this.list[index].sn, isDelete: val ? '1' : '0', psKey: this.list[index].psKey }).then(res => {
            if (res.result_code == '1') {
              this.$message.success((val ? '拆除' : '恢复') + '成功！');
              this.list[index].isDelete = val;
              this.baseInfo.collectorConfig[index]['是否可拆除'] = val ? '1' : '0';
              this.getDeviceInfo(true);
              this.$emit('setChangeData');
            }
          });
        }
      });
    },
    addCollector () {
      this.addForm();
      this.baseInfo.collectorConfig.push({
        '主键ID': null,
        '厂家': '',
        '厂家ID': '',
        '描述': '',
        '是否可拆除': 0,
        '状态': '0',
        '用途': '',
        '用途ID': '',
        '通讯模块SN': '',
        collectorName: ''
      });
    },
    getIecPoints (sn) {
      this.$refs.points.init(sn);
    },
    // 导出IEC测点
    doExport (psId) {
      exportSnAndPoint({ psId: psId }).then(res => {
        this.$downloadFile({ fileBase64Code: res.result_data.strBase64, fileName: res.result_data.fileName });
      });
    },
    formatString (str) {
      if (str) {
        if (str.length > 9) {
          str = str.substring(0, 9) + '...';
        }
        return str;
      } else {
        return '--';
      }
    }
  },
  beforeDestroy () {
    this.$refs.form.resetFields();
  }
};
</script>
<style lang="less" scoped>
.power-info {
  height: 100%;
}
.unable-edit-from {
  :deep(.form-item-inject) {
    margin-bottom: 0;
  }
}
:deep(.ant-form-item) {
  width: 100%;
  display: inline-flex;
}
.form-btn {
  padding-top: 6px;
  .state {
    margin: 0 12px 0 12px;
    width: 42px;
    display: inline-block;
  }
  .remove-btn {
    margin-left: 16px;
  }
}
.operation-btn {
  position: absolute;
  right: 24px;
  top: 9px;
  button + button {
    margin-left: 16px;
  }
}
.unjoin-state {
  color: #9D9997;
}
.join-state {
  color: #00B769;
}
.off-state {
  color: #f5222d;
}
.scroller {
  height: 100%;
}
.form-list {
  max-height: 320px;
  overflow: auto;
}
.form-text {
  margin-left: 8px;
}
.sn-text {
  color: #1890ff;
  cursor: pointer;
}
.status-text {
  color: rgba(0, 0, 0, 0.85);
}
:root[data-theme='dark'] {
  .status-text {
    color: #fff;
  }
}
</style>
