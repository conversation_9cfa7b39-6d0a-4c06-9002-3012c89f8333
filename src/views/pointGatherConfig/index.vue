<!-- 测点管理 -->
<template>
  <div id="point-config">
    <a-spin :spinning="loading" >
      <div class='titles'>
        <div>
        </div>
      </div>
      <div class="solar-eye-search-model">
        <a-form labelAlign="left"  class="solar-eye-search-content">
          <a-row :gutter="24" align="middle">
            <a-col  :md="8" :sm="24" :xxl="5" :xl="8" :lg="8">
              <a-form-item label="接入SN" :colon="false">
                <a-input v-model="queryParamsHome.sn" placeholder="请输入SN号"></a-input>
              </a-form-item>
            </a-col>
            <a-col  :md="8" :sm="24" :xxl="5" :xl="8" :lg="8">
              <a-form-item label="接入来源" :colon="false">
                <a-select v-model="queryParamsHome.source">
                  <a-select-option value="">全部</a-select-option>
                  <a-select-option v-for="item in sourceData" :value="item.secondTypeCode" :key="item.secondTypeCode">
                    {{ item.secondName }}
                  </a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
            <a-col  :md="8" :sm="24" :xxl="5" :xl="8" :lg="8">
              <a-form-item label="接入状态" :colon="false">
                <a-select v-model="queryParamsHome.accessStatus">
                  <a-select-option value="">全部</a-select-option>
                    <a-select-option v-for="item in accessStatusData" :value="item.secondTypeCode" :key="item.secondTypeCode">
                    {{ item.secondName }}
                  </a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
            <a-col  :md="8" :sm="24" :xxl="5" :xl="8" :lg="8">
              <a-form-item label="接入时间" :colon="false">
                <a-select v-model="queryParamsHome.accessTimeType">
                  <a-select-option value="">全部</a-select-option>
                  <a-select-option value="1">近7天</a-select-option>
                  <a-select-option value="2">近30天</a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
            <a-col  :md="8" :sm="24" :xxl="4" :xl="8" :lg="8">
              <div class="search-item">
                <throttle-button title="查询" label='查询' @click="searchData(1)"/>
                <throttle-button title="重置" label='重置' @click="resetData"/>
              </div>
            </a-col>
          </a-row>
        </a-form>
      </div>
      <div class="solar-eye-gap"></div>
      <a-col class="solar-eye-main-content">
        <!-- 操作按钮 -->
        <div class="operation" >
          <div class="operation-btn">
            <throttle-button  label='绑定设备' :disabled="isShowBoundDeviceHome" @click='showBoundDevice(1,selectedRowKeys)' />
            <!-- <throttle-button  label='导出' /> -->
            <erp-button label="导出" @click="exportSn"  size="default"></erp-button>
          </div>
        </div>
          <vxe-table
            :data="dataList"
            ref="multipleTable"
            class="my-table"
            @sort-change="(event)=>handleTableSortChange(event, queryParamsHome, 'snList')"
            :sort-config="{ remote: true }"
            resizable
            align="center"
            show-overflow
            highlight-hover-row
            size="small"
            @checkbox-all="onSelectChange"
            @checkbox-change="onSelectChange"
            :seq-config="{startIndex: (queryParamsHome.currentPage - 1) * queryParamsHome.pageSize}"
            :height="tableHeight - 24"
          >
            <vxe-table-column type="checkbox" :width="60"></vxe-table-column>
            <vxe-table-column type="seq" :width="80" title="序号" align="center"></vxe-table-column>
            <vxe-table-column show-overflow="title" v-for="item in colunms" :key="item.key" :field="item.key"
                              :title="item.title" :min-width="item.width || 140"
                              :fixed="item.fixed ? item.fixed : ''" sortable>
              <template v-slot:default="{row}">
                <span>{{ getLabel(row[item.key], null) }}</span>
              </template>
            </vxe-table-column>
            <vxe-table-column title="操作" fixed="right" width="160" :resizable="false" class-name="fixed-right-column-160">
              <template v-slot="{ row }">
                <div class="flex-action-button">
                   <a @click="goPointConfig(row)" v-if="row.source==4" type="link" size="small">直采测点配置</a>
                </div>
                <!-- <span @click="goPointConfig(row)" class="operation-btn-hover" style="color: #3662EC">直采测点配置</span> -->
              </template>
            </vxe-table-column>
            <template v-slot:empty>
              <span>查询无数据</span>
            </template>
          </vxe-table>
        <page-pagination
          :pageSize="queryParamsHome.pageSize"
          :current="queryParamsHome.currentPage"
          :total="homeTotal"
          @size-change="sizeChange"
        />
      </a-col>
    </a-spin>
    <!-- <drawer-view ref="boundDevice" @cancel="hideBoundDevice" parentId="point-config" /> -->
    <point-config ref='pointConfigList' @cancel='getList'   parentId="point-config"/>
  </div>
</template>
<script>
import tableHeight from '@/mixins/tableHeightAndSearchModel';
import { listMixin } from './mixins';
import { snListColumns } from './columns';
import PointConfig from './modules/PointConfig';
import { exportAccessSnList, updateRedisMappingByDeviceAndPointMapping } from '@/api/health/getPointConfig.js';

export default {
  name: 'SnList',
  mixins: [tableHeight, listMixin],
  components: {
    PointConfig
  },
  data () {
    return {
      loading: false,
      selectedRowKeys: [],
      colunms: snListColumns,
      url: {
        list: '/access/getAccessSnList'
      },
      sourceList: []
    };
  },
  created () {
    // this.loadData(1);
    this.getList();
    this.getDictList();
  },
  computed: {},
  methods: {
    // 导出
    exportSn () {
      for (let key in this.exportParams) {
        this.exportParams[key] = this.queryParamsHome[key];
      }
      exportAccessSnList(this.exportParams).then(res => {
        this.$downloadFile({ fileBase64Code: res.result_data.strBase64, fileName: res.result_data.fileName });
      }).catch(() => {
      });
    },
    // 查询
    searchData (val) {
      this.queryParamsHome.currentPage = val;
      this.getList();
    },
    // 重置
    resetData () {
      this.queryParamsHome.sn = '';
      this.queryParamsHome.source = '';
      this.queryParamsHome.accessStatus = '';
      this.queryParamsHome.accessTimeType = '';
      this.queryParamsHome.sortFiled = '';
      this.queryParamsHome.sortKind = '';
      this.searchData(1);
    },
    // 表格分页选项变化事件
    sizeChange (current, pageSize) {
      this.queryParamsHome.currentPage = current;
      this.queryParamsHome.pageSize = pageSize;
      this.getList();
    },
    onSelectChange (val) {
      let arr = [];
      this.sourceList = [];
      val.records.forEach((item) => {
        arr.push(item.sn);
        this.sourceList.push(item.source);
      });
      this.selectedRowKeys = arr;
      this.isShowBoundDeviceHome = this.selectedRowKeys.length === 0;
    },
    // 点击绑定设备
    showBoundDevice (type, dataSource) {
      let arr = Array.from(new Set(this.sourceList));
      if (arr.length > 1 || this.sourceList.includes(4)) {
        this.$message.warning('所选SN中，接入来源含"电站接入"或"不一致"');
        return;
      }
      this.$refs.pointConfigList.boundBtn(type, dataSource);
    },
    goPointConfig (row) {
      updateRedisMappingByDeviceAndPointMapping({
        sn: row.sn
      }).then(res => {

      });
      this.$refs.pointConfigList.init(1, row, '/pointGatherConfig/modules/PointConfig');
    }
  }
};
</script>

<style lang='less' scoped >
.test{
  display: flex;
  justify-content: space-between;
}

.droparea:-moz-drag-over {
  border: 1px solid black;
}

</style>
