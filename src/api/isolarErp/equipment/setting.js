import request from '@/utils/request';
// 获取设备型号参数列表数据
export function queryList (data) {
  return request({
    url: '/equipment/v1/detail/list',
    method: 'post',
    data
  });
}
// 获取设备型号参数配置详情信息
export function getErpDeviceSetInfoList (data) {
  return request({
    url: '/equipment/v1/detail/getErpDeviceSetInfoList',
    method: 'post',
    data
  });
}
// 获取设备类型下拉框数据
export function getParentDeviceTypeDetailInfoList (data) {
  return request({
    url: '/equipment/v1/type/getParentDeviceTypeDetailInfoList',
    method: 'post',
    data
  });
}
// 删除设备详情信息
export function deleteErpDeviceDetail (data) {
  return request({
    url: '/equipment/v1/detail/deleteErpDeviceDetail',
    method: 'post',
    data
  });
}
// 设备配置保存
export function saveErpDeviceSet (data) {
  return request({
    url: '/equipment/v1/detail/saveErpDeviceSet',
    method: 'post',
    data
  });
}
// 检查当前登录的用户是否可以编辑该条设备型号参数信息
export function checkEditErpDeviceSetResultByPsId (data) {
  return request({
    url: '/equipment/v1/detail/checkEditErpDeviceSetResultByPsId',
    method: 'post',
    data
  });
}
