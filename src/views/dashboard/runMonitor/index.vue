<!--运行监测主页-->
<template>
<a-spin :spinning="load">
<div style="width:100%; overflow:hidden;position: relative">
  <a-tabs :activeKey="selectTab" @change="callback" :tabBarStyle="{'border-bottom':0,'margin-bottom':0}" v-show="pageLevel==1 && !load">
      <a-tab-pane v-for="item in tabList" :key="item.value"  >
      <template slot="tab">
        <div class="test">{{item.name}}</div>
      </template>
      </a-tab-pane>
    </a-tabs>
  <div class="run-monitor" id="runMonitor" :class="{isview: nowPage == 'Summary',isThird: !isShowMenu }" >
     <div class="title" v-if="pageLevel > 1">
      <span class="back-icon"
      @click="changePage(1)"><a-icon type="left" />返回</span>
      <div class="split-line"></div>
      <span>{{psName}}</span><span class="split">/</span>
      <span >
      <template  v-if='breadList.length >0'>
      <span v-for="item in breadList" :key="item.ps_key" @click="changeDevice(item)" style="cursor: pointer">{{item.device_name || item.deviceName}}<span style="padding: 0 4px">/</span></span>
      </template>
      <span class="alarm-type">{{tabName}}</span></span>
    </div>
    <keep-alive exclude="InverterList,Summary">
      <component
      ref='monitor'
      @changePage="changePage"
      @changePageParams="changePageParams"
      @open="openStatic"
      :is="nowPage"
      :categoryList="categoryList"
      :stationType='stationType'
      :remarkList="remarkList"
      :commonParams="pageParams" />
    </keep-alive>

  </div>
 <static ref='staticModal' :commonParams="pageParams"></static>
</div>
</a-spin>
</template>
<script>
import RunMonitor from './RunMonitor';
import Summary from '@/views/monitor/Summary';
import InverterList from './inverter/InverterList';
import Static from '@/views/monitor/modules/Static';
import StoredHeadData from './StoredEnergy';
import { getPsCategory } from '@/api/monitor/device';
import { getRemarkDynamic } from '@/api/health/AlarmEvents.js';
import { updateTheme } from '@comp/tools/setting';
const tabList = [ {
  name: '光伏总览',
  value: 'RunMonitor',
  key: 'CentralizedPV'
}, {
  name: '光伏总览',
  value: 'RunMonitor',
  key: 'DistributedPV'
}, {
  name: '光伏总览',
  value: 'RunMonitor',
  key: 'WindPower'
}, {
  name: '光伏总览',
  value: 'RunMonitor',
  key: 'Hydrogen'
}, {
  name: '光伏总览',
  value: 'RunMonitor',
  key: 'CIPV'
}, {
  name: '储能总览',
  value: 'StoredHeadData',
  key: 'Storage'
}];
export default {
  components: { RunMonitor, InverterList, Summary, StoredHeadData, Static },
  name: 'index',
  data () {
    return {
      nowPage: '',
      pageLevel: 1,
      pageParams: {},
      psName: '',
      psId: '',
      tabName: '',
      breadList: [],
      selectTab: '',
      stationType: 4, // 电站类型 4光伏，5 储能
      tabList: [],
      load: false,
      categoryList: [],
      remarkList: []
    };
  },
  created () {
    this.setBg('dark');
    this.initTab();
    this.getRemarkDynamic();
  },
  deactivated () {
    this.setBg(this.navTheme);
  },
  activated () {
    this.setBg('dark');
  },
  computed: {
    getRealStationTree () {
      let arr = this.$store.state.tree.realStationTree;
      let realStationTreeList = typeof arr === 'string' ? JSON.parse(arr) : arr;
      return realStationTreeList;
    },
    navTheme () {
      this.setBg('dark');
      return this.$store.state.app.theme;
    },
    isShowMenu () {
      return this.$store.state.user.isShowMenu;
    }
  },
  watch: {
    'navTheme' (value, old) {
      if (this.$route.path == '/dashboard/analysis' || this.$route.path == '/houseHold/dashboard') {
        this.setBg('dark');
      } else {
        this.setBg(value);
      }
    }

  },
  methods: {
    setBg (theme) {
      document.documentElement.setAttribute('data-theme', theme);
      updateTheme();
      // document.getElementsByTagName('body')[0].classList = 'solar-eye-' + theme + ' ' + webpbg;
      let that = this;
      this.$once('hook:beforeDestroy', () => {
        that.setBg(that.navTheme);
      });
    },
    getRemarkDynamic () {
      getRemarkDynamic().then(res => {
        this.remarkList = res.result_data;
      });
    },
    initTab () {
      this.load = true;
      getPsCategory().then(res => {
        this.load = false;
        let list = [];
        let data = res.result_data;
        this.categoryList = data;
        tabList.forEach(item => {
          if (data.indexOf(item.key) > -1 && list.indexOf(item.value) == -1 && ['CentralizedPV', 'DistributedPV', 'Storage', 'CIPV'].indexOf(item.key) > -1) {
            this.tabList.push(item);
            list.push(item.value);
          }
          // return res.result_data.indexOf(item.key) > -1;
        });
        // this.tabList = list;
        this.nowPage = this.selectTab = this.tabList[0].value;
        this.stationType = this.tabList[0].key == 'Storage' ? 'Storage' : 4;
      }).catch(() => {
        this.load = false;
      });
    },
    changePage (level, name, list) {
      if (name) {
        this.breadList = list || [];
        this.tabName = name;
        return;
      }
      this.pageLevel = level;
      if (level == 1) {
      // this.nowPage = 'RunMonitor';
        this.nowPage = this.selectTab;
      }

      if (level == 2) {
        this.nowPage = 'Summary';
      }
    },
    changeDevice (item) { // 需要修复
      let deviceType = Number(item.device_type || item.deviceType);
      let value = '';
      switch (deviceType) {
        case 1:
          value = 'Inverter';
          break;
        case 23:
          value = 'BMS';
          break;
        case 24:
          value = 'RackBMS';
          break;
        case 47:
          value = 'EnergyStorage';
          break;
        default:
          value = 'BoxChange';
          break;
      }
      this.$refs.monitor.changeDevice({ value: value, name: item.device_name || item.deviceName, type: deviceType, deviceSubType: value == 99 ? 4 : null }, item);
    },
    changePageParams (obj) {
      this.pageParams = {
        ...obj
      };
      this.psName = obj.psName;
    },

    getPsName (psId) {
      // 根据psId，回显电站名称
      this.getRealStationTree.length > 0 &&
        this.getRealStationTree.forEach((item, index) => {
          if (psId == item.projectId) {
            this.psName = item.name;
          }
        });
    },
    callback (activeKey) {
      this.nowPage = activeKey;
      this.selectTab = activeKey;
      this.stationType = activeKey == 'RunMonitor' ? '4' : 'Storage';
    },
    openStatic (e) {
      this.$refs.staticModal.initData();
    }
  }
};
</script>

<style lang="less" scoped>
  :deep(.ant-tabs-nav ){
    .ant-tabs-tab{
      padding: 8px 16px;
      .test {
        padding: 4px 12px;
      }
      &:hover {
        .test {
          border-radius: 3px;
          background: var(--zw-table-bg-color--hover);
          color: var(--zw-text-1-color--default)
        }
      }
    }
  }

.title {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
  color: var(--zw-text-1-color--default);
  .back-icon:hover {
    .anticon {
      color:var(--zw-primary-color--default);
    }
    cursor: pointer;
    color:var(--zw-primary-color--default);
  transition:.3s;
  }
  .split {
    padding: 0 8px;
  }
  .split-line {
        width: 1px;
    height: 12px;
    background: var(--zw-divider-color--default);
    margin: 0 20px;
    position: relative;
    top: 1px;
  }
  .alarm-type {
    font-size: 16px;
    font-weight: 600;
    color: var(--zw-text-1-color--default)
  }
}
</style>
