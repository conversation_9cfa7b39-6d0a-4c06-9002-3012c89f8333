<!-- 数据角色管理 -->
<template>
  <a-row :gutter="24">
    <a-spin :spinning="loading">
      <a-col :md="12" :sm="24"  v-bind:style="{'height': boxHeight+'px'}" :class="navTheme == 'dark'?'dark-bg':'white-bg'">
        <a-card :bordered="false">
          <!-- 按钮操作区域 -->
          <a-row class="btn-group">
            <throttle-button label="批量删除" @click="batchDel"/>
          </a-row>
          <div class="tree-box" v-bind:class="navTheme == 'dark'?'dark-bg':'white-bg'">
            <a-alert type="info" :showIcon="true">
              <div slot="message">
                当前选择：
                <template v-if="currSelected.title">
                  <span>{{ getCurrSelectedTitle() }}</span>
                  <a style="margin-left: 10px" @click="onClearSelected">取消选择</a>
                </template>
              </div>
            </a-alert>
            <a-input-search @search="onSearch" style="width:100%;margin-top: 10px" placeholder="请输入"/>
            <!-- 树-->
            <a-col :md="10" :sm="24" v-bind:style="{'height':(boxHeight - 220) + 'px','overflow': 'auto', 'width': '100%'}">
              <template>
                <a-dropdown :trigger="[this.dropTrigger]" @visibleChange="dropStatus">
                  <span style="user-select: none">
                  <a-tree
                    checkable
                    multiple
                    @select="onSelect"
                    @check="onCheck"
                    @rightClick="rightHandle"
                    :selectedKeys="selectedKeys"
                    :checkedKeys="checkedKeys"
                    :treeData="nodes"
                    :checkStrictly="checkStrictly"
                    :expandedKeys="iExpandedKeys"
                    :autoExpandParent="autoExpandParent"
                    @expand="onExpand"
                    :replaceFields="{title:'name', key:'id'}"/>
                    </span>
                  <!--新增右键点击事件,和增加添加和删除功能-->
                  <a-menu slot="overlay">
                    <!-- 只有默认数据角色才能添加下级 -->
                    <a-menu-item @click="handleAdd(3)" key="1">添加</a-menu-item>
                    <!-- <template v-if="rightClickSelectedNode.type == '0'">
                    </template> -->
                    <!-- 只能删除自定义角色 -->
                    <template v-if="rightClickSelectedNode.type != '0'">
                      <a-menu-item @click="handleDelete" key="2">删除</a-menu-item>
                    </template>
                    <a-menu-item @click="closeDrop" key="3">取消</a-menu-item>
                  </a-menu>
                </a-dropdown>
              </template>
            </a-col>
          </div>
        </a-card>
        <!---- author:os_chengtgen -- date:20190827 --  for:切换父子勾选模式 =======------>
        <div class="drawer-bootom-button">
          <a-dropdown :trigger="['click']" placement="topCenter">
            <a-menu slot="overlay">
              <a-menu-item key="1" @click="switchCheckStrictly(1)">父子关联</a-menu-item>
              <a-menu-item key="2" @click="switchCheckStrictly(2)">取消关联</a-menu-item>
              <a-menu-item key="3" @click="checkALL">全部勾选</a-menu-item>
              <a-menu-item key="4" @click="cancelCheckALL">取消全选</a-menu-item>
              <a-menu-item key="5" @click="expandAll">展开所有</a-menu-item>
              <a-menu-item key="6" @click="closeAll">合并所有</a-menu-item>
            </a-menu>
            <a-button>
              树操作 <a-icon type="up" />
            </a-button>
          </a-dropdown>
        </div>
        <!---- author:os_chengtgen -- date:20190827 --  for:切换父子勾选模式 =======------>
      </a-col>
      <a-col :md="12" :sm="24" v-bind:style="{'height': boxHeight+'px'}" :class="navTheme == 'dark'?'dark-bg':'white-bg'">
        <a-tabs v-model="activeKey">
          <a-tab-pane tab="基本信息" key="1" forceRender>
            <a-card :bordered="false" v-show="currSelected.id">
              <a-spin :spinning="submitLoading">
                <base-form ref="baseForm" :disabled="currSelected.type == '0'"></base-form>
              </a-spin>
              <div class="ant-form-btn" v-show="currSelected.type != '0'">
                <throttle-button :disabled="submitLoading" @click="resetForm" type="default" label="重置" icon="sync" style="margin-right: 12px;"></throttle-button>
                <throttle-button :disabled="submitLoading" :loading="submitLoading" @click="submitForm" label="保存" icon="form"></throttle-button>
              </div>
            </a-card>
            <a-card v-show="!currSelected.id">
              <a-empty>
                <span slot="description"> 请先选择一个部门! </span>
              </a-empty>
            </a-card>
          </a-tab-pane>
          <a-tab-pane tab="电站权限" key="2" forceRender>
            <psa-table :selectedKey="currSelected.id" :dictMap="dictMap" ref="psaTable"></psa-table>
          </a-tab-pane>
          <a-tab-pane tab="人员" key="3" forceRender>
            <emp-table :selectedKey="currSelected.id" :dictMap="dictMap" ref="empTable"></emp-table>
          </a-tab-pane>
        </a-tabs>
      </a-col>
    </a-spin>
    <form-modal ref="formModal" @success="loadTree"></form-modal>
  </a-row>
</template>

<script>
import { deleteAction } from '@/api/manage';
import initDict from '@/mixins/initDict';
import baseForm from './dataRoleModules/form';
import psaTable from './dataRoleModules/psaTable';
import empTable from './dataRoleModules/empTable';
import formModal from './dataRoleModules/formModal';
import { dataRoleEdit, deleteByDepartId, getDataRoleTree, getDataRoleTreeBy } from '@/api/api';
export default {
  name: 'DataRoleList',
  mixins: [ initDict ],
  components: {
    baseForm,
    psaTable,
    empTable,
    formModal
  },
  data () {
    return {
      loading: false,
      boxHeight: 400,
      model: {},
      nodes: [],
      allNode: [],
      currSelected: {},
      allTreeKeys: [],
      selectedKeys: [],
      checkStrictly: true,
      iExpandedKeys: [],
      checkedKeys: [],
      dropTrigger: '',
      rightClickSelectedNode: {},
      autoExpandParent: false,
      add_btn: false,
      activeKey: '1',
      submitLoading: false
    };
  },
  computed: {
    navTheme () {
      return this.$store.state.app.theme;
    }
  },
  created () {
    this.getDictMap('psa_model,psa_status,position,property,sex,yes_no_emp,work_sts_emp'); // 加载数据字典
    this.loadTree();
  },
  mounted () {
    window.addEventListener('resize', this.getBoxHeight);
  },
  updated () {
    this.boxHeight = window.innerHeight - 124;
  },
  beforeDestroy () {
    window.removeEventListener('resize', this.getBoxHeight);
  },
  methods: {
    /*
        批量删除
      */
    batchDel () {
      if (this.checkedKeys.length <= 0) {
        this.$message.warning('请选择一条记录！');
        return;
      }
      let that = this;
      let ids = this.checkedKeys.join(',');
      this.$confirm({
        title: '确认删除',
        content: '确定要删除所选中的 ' + this.checkedKeys.length + ' 条数据，以及子节点数据吗?',
        okText: '确定',
        cancelText: '取消',
        onOk () {
          deleteAction('/sys/sysDepart/deleteBatch', { ids: ids }).then((res) => {
            that.$message.success(res.message);
            that.loadTree();
            that.onClearSelected();
            that.checkedKeys = [];
          });
        }
      });
    },
    /*
        添加
      */
    handleAdd (type) {
      this.add_btn = true;
      switch (type) {
        case 1:
          // 暂时不要
          break;
        case 2:
          // 暂时不要
          break;
        case 3:
          let rightClickSelectedNode = Object.assign({}, this.rightClickSelectedNode);
          this.$refs.formModal.init(rightClickSelectedNode);
          break;
      }
    },
    /*
        删除节点
      */
    handleDelete () {
      let that = this;
      this.$confirm({
        title: '确认删除',
        content: '确定要删除此节点以及子节点数据吗?',
        okText: '确定',
        cancelText: '取消',
        onOk () {
          let nodeId = that.rightClickSelectedNode.id;
          deleteByDepartId({ id: nodeId }).then((resp) => {
            that.dropTrigger = '';
            that.checkedKeys.splice(that.checkedKeys.findIndex(key => key === nodeId), 1);
            that.$message.success('删除成功!');
            that.loadTree();
            that.onClearSelected();
          });
        }
      });
    },
    /*
        树查询
      */
    loadTree () {
      let _this = this;
      _this.nodes = [];
      _this.allNode = [];
      _this.allTreeKeys = [];
      getDataRoleTree().then(res => {
        let result = Array.isArray(res.result) ? res.result : [res.result];
        result.forEach(node => {
          _this.setThisExpandedKeys(node);
          _this.getAllKeys(node);
        });
        _this.nodes = result;
      });
    },
    setThisExpandedKeys (node) {
      if (node && node.children && node.children.length > 0) {
        this.iExpandedKeys.push(node.id);
        node.children.forEach(o => {
          this.setThisExpandedKeys(o);
        });
      }
    },
    getAllKeys (node) {
      if (!node) return;
      node.disableCheckbox = (node.type == '0'); // 默认数据角色不可删除
      if (node.type != '0') {
        this.allTreeKeys.push(node.id);
      }
      this.allNode.push(Object.assign({}, node, { children: undefined }));
      if (node.children && node.children.length > 0) {
        node.children.forEach(o => {
          this.getAllKeys(o);
        });
      }
    },
    /*
        树查询事件-按条件
      */
    onSearch (value) {
      let _this = this;
      if (value) {
        _this.nodes = [];
        getDataRoleTreeBy({ keyWord: value }).then((res) => {
          res.result.forEach(node => {
            node.disableCheckbox = (node.type == '0'); // 默认数据角色不可删除
          });
          _this.nodes = res.result;
        });
      } else {
        _this.loadTree();
      }
    },
    /*
        树节点右击shijian
      */
    rightHandle (node) {
      this.dropTrigger = 'contextmenu';
      this.rightClickSelectedNode = node.node.dataRef;
    },
    /*
        树节点多选事件
      */
    onCheck (checkedKeys, info) {
      if (this.checkStrictly) {
        this.checkedKeys = checkedKeys.checked;
      } else {
        this.checkedKeys = checkedKeys;
      }
    },
    /*
        树节点选中事件
      */
    onSelect (selectedKeys, e) {
      let record = e.node.dataRef;
      this.currSelected = Object.assign({}, record);
      this.selectedKeys = [record.id];
      this.$nextTick(() => {
        this.initTab();
      });
    },
    initTab () {
      let selectedNode = JSON.parse(JSON.stringify(this.currSelected));
      if (selectedNode.parentId) {
        let parent = this.allNode.find(item => item.id == selectedNode.parentId);
        selectedNode.parentName = parent && parent.name ? parent.name : '';
      }
      this.$refs.baseForm.init(selectedNode, 'edit');
      this.$refs.psaTable.init(selectedNode);
      this.$refs.empTable.init(selectedNode);
    },
    /*
        重置
      */
    resetForm () {
      this.$refs.baseForm.reset();
    },
    /*
        保存
      */
    submitForm () {
      this.submitLoading = true;
      this.$refs.baseForm.validate().then(form => {
        dataRoleEdit(form).then(res => {
          this.loadTree();
          this.$message.success('操作成功');
          this.submitLoading = false;
        }).catch(() => {
          this.submitLoading = false;
        });
      }).catch(() => {
        this.submitLoading = false;
      });
    },
    /*
        右键点击下拉框改变事件
      */
    dropStatus (visible) {
      if (visible == false) {
        this.dropTrigger = '';
      }
    },
    /*
        右键取消-关闭下拉框
      */
    closeDrop () {
      this.dropTrigger = '';
    },
    /*
        树节点展开事件
      */
    onExpand (expandedKeys) {
      this.iExpandedKeys = expandedKeys;
      this.autoExpandParent = false;
    },
    getBoxHeight () {
      this.boxHeight = window.innerHeight - 124;
    },
    /*
        取消选中节点
      */
    onClearSelected () {
      this.checkedKeys = this.selectedKeys = [];
      this.currSelected = this.rightClickSelectedNode = {};
      this.$forceUpdate();
    },
    getCurrSelectedTitle () {
      return !this.currSelected.title ? '' : this.currSelected.title;
    },
    /*
        树操作-父子关联、取消关联
      */
    switchCheckStrictly (v) {
      this.checkStrictly = (v == 2);
    },
    /*
        树操作-全选
      */
    checkALL () {
      this.checkStriccheckStrictlytly = false;
      this.checkedKeys = this.allTreeKeys;
    },
    /*
        树操作-取消全选
      */
    cancelCheckALL () {
      this.checkedKeys = [];
    },
    /*
        树操作-展开
      */
    expandAll () {
      this.iExpandedKeys = this.allNode.map(node => {
        return node.id;
      });
    },
    /*
        树操作-收起
      */
    closeAll () {
      this.iExpandedKeys = [];
    }
  }
};
</script>

<style lang="less" scoped>
  .white-bg{
    background: #FFFFFF;
  }
  .btn-group{
    margin-left: 12px;
    text-align: left;
    :deep(.ant-btn) {
      margin-right: 12px !important;
      margin-left: 0;
    }
  }
  .ant-form-btn{
    text-align: center;
  }
  .tree-box{
    margin-left: 12px;
  }
</style>
