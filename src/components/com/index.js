import funnel from './funnel.vue';
import FunnelSelect from './funnelSelect.vue';
import erpButton from './erpButton.vue';
import gButton from './gButton.vue';
import gCascader from './gCascader.vue';
import pagePagination from './pagePagination.vue';
import throttleButton from './throttle-button.vue';
import treeSelect from './proTreeSelect';
import depTreeSelect from './depTreeSelect';
import flowchart from './flowchart';
import backNullify from './backNullify';
import yearPicker from './yearPicker';
import tableSort from './tableSort';
import drawerView from './drawerView';
import FlowChartDrawer from './FlowChartDrawer';
import PsTreeSelect from './PsTreeSelect';
// import fileUpload from "./fileUpload"
import roleTreeSelect from './roleTreeSelect';
import psaSelect from './data-role/psaSelect';
import DetailLayout from './DetailLayout';
import FileUploadView from './fileUploadView';
import ZwTextarea from './TextArea';
import ZwFileUpload from './ZwFileUpload';
import ZwDrawerButton from './ZwDrawerButton';
export default {
  install (Vue) {
    Vue.component('funnel', funnel);
    Vue.component('FunnelSelect', FunnelSelect);
    Vue.component('erpButton', erpButton);
    Vue.component('gButton', gButton);
    Vue.component('pagePagination', pagePagination);
    Vue.component('gCascader', gCascader);
    Vue.component('throttleButton', throttleButton);
    Vue.component('treeSelect', treeSelect);
    Vue.component('depTreeSelect', depTreeSelect);
    Vue.component('flowchart', flowchart);
    Vue.component('backNullify', backNullify);
    Vue.component('yearPicker', yearPicker);
    Vue.component('tableSort', tableSort);
    Vue.component('drawerView', drawerView);
    Vue.component('FlowChartDrawer', FlowChartDrawer);
    Vue.component('PsTreeSelect', PsTreeSelect);
    Vue.component('roleTreeSelect', roleTreeSelect);
    Vue.component('psaSelect', psaSelect);
    Vue.component('DetailLayout', DetailLayout);
    Vue.component('FileUploadView', FileUploadView);
    Vue.component('ZwTextarea', ZwTextarea);
    Vue.component('ZwFileUpload', ZwFileUpload);
    Vue.component('ZwDrawerButton', ZwDrawerButton);
  }
};
