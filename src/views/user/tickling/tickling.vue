<template>
  <div class="tickling-page">
    <a-spin :spinning="pageloading">
          <div class="solar-eye-search-model">
        <a-row :gutter="24" class="solar-eye-search-content">
              <a-col :xxl="6" :xl="8" :md="12">
                 <div class="search-item">
              <span class="search-label">问题描述</span>
                    <a-input v-model="questionDesc" allowClear @blur="questionDesc = $trim($event)" size="default" placeholder="请输入问题描述" style="width: 100%;"></a-input>
                </div>
              </a-col>
              <a-col :xxl="6" :xl="8" :md="12">
                 <div class="search-item">
              <span class="search-label">问题状态</span>

                    <a-select size="default" v-model="questionSts" placeholder="请选择" allowClear style="width: 100%;">
                      <a-select-option v-for="item in dictMap.question_sts" :key="item.dataValue" :value="item.dataValue">{{item.dataLable}}</a-select-option>
                    </a-select>
                </div>
              </a-col>
              <a-col :xxl="6" :xl="8" :md="12">
                <div class="search-item">
              <span class="search-label">反馈时间</span>
                    <a-range-picker v-model="ticklingDate" :placeholder="['选择日期', '选择日期']" format="YYYY-MM-DD" value-format="YYYY-MM-DD" style="width: 100%;" />
                </div>
              </a-col>
              <a-col :xxl="3" :xl="6" :md="8">
                 <div class="search-item">
                  <throttle-button label="查询" @click="pageChange()" />
                </div>
              </a-col>
            </a-row>
          </div>
          <div class="solar-eye-gap"></div>
      <a-col class="solar-eye-main-content">
        <div class="operation">
          <div class="operation-btn" >
            <span>筛选常见问题&nbsp;&nbsp;</span>
            <a-switch checked-children="Yes" un-checked-children="No" @change="onChange" />
            <throttle-button label="反馈问题" perms="910101101102150" @click="showTicklingForm('1', null)" />
            <throttle-button label="新增常见问题" perms="910101101102151" @click="showTicklingForm('2', null)" />
            <throttle-button label="导出" :loading="exp_loading" perms="910101101102152" @click="exportBtn()" />
          </div>
          </div>
          <!--表格渲染-->
          <vxe-table :data="data" :height="tableHeight - 36" ref="multipleTable" align="center" border show-overflow highlight-hover-row size="small"
            :seq-config="{startIndex: (page - 1) * size}" resizable>
            <vxe-table-column type="seq" width="80" title="序号" align="center"></vxe-table-column>
            <vxe-table-column show-overflow="title" :formatter="tabFormatter" field="questionNum" title="问题编号" min-width="150">
              <template #header="{ column }">
                <table-sort :column="column" filed="questionNum" @sortChange="mySortChange" :downSort="downSort" :upSort="upSort" :sortFiled="sortFiled" />
              </template>
            </vxe-table-column>
            <vxe-table-column show-overflow="title" field="questionDesc" align="left" title="问题描述" min-width="400">
              <template v-slot="{ row }">
                <span v-if="row.questionDesc" @click="showTicklingForm('3', row)" class="blue">{{ row.questionDesc}}</span>
                <span v-else>--</span>
              </template>
            </vxe-table-column>

            <vxe-table-column v-for="item in columnList" :key="item.name" show-overflow="title" :formatter="tabFormatter"
              :min-width="item.width || 150" :field="item.name" :title="item.comment">
            </vxe-table-column>

            <vxe-table-column show-overflow="title" :formatter="tabFormatter" field="createTime" title="反馈时间" min-width="130">
              <template #header="{ column }">
                <table-sort :column="column" filed="createTime" @sortChange="mySortChange" :downSort="downSort" :upSort="upSort" :sortFiled="sortFiled" />
              </template>
            </vxe-table-column>
            <vxe-table-column show-overflow="title" :formatter="tabFormatter" field="questionStsName" title="状态" min-width="100"></vxe-table-column>
            <vxe-table-column show-overflow="title" :formatter="tabFormatter" field="updateTime" title="更新时间" min-width="130">
              <template #header="{ column }">
                <table-sort :column="column" filed="updateTime" @sortChange="mySortChange" :downSort="downSort" :upSort="upSort" :sortFiled="sortFiled" />
              </template>
            </vxe-table-column>
            <vxe-table-column :visible="showHandle(perms)" title="操作" fixed="right" width="120" :resizable="false" class-name="fixed-right-column-120">
              <template v-slot="{ row }">
                <g-button @detail="showTicklingForm('3', row)" @edit="showTicklingForm('4', row)"
                  @reply="showTicklingForm('5', row)" @close="closeClick(row)" @delete="deleteClick(row)"
                  :list="[
                      {
                        icon: 'file-text',
                        emit: 'detail',
                        name: '查看',
                        has: '910101101102153', // 未分配权限 则添加 true,  否则传入权限标识即可
                        show: true // 显示条件
                      },{
                        icon: 'edit',
                        emit: 'edit',
                        name: '编辑',
                        has: '910101101102154', // 未分配权限 则添加 true,  否则传入权限标识即可
                        show: row.isComQuestion == '0' // 显示条件
                      },{
                        icon: 'form',
                        emit: 'reply',
                        name: '回复',
                        has: '910101101102155', // 未分配权限 则添加 true,  否则传入权限标识即可
                        show: row.questionSts != '3' // 显示条件
                      },{
                        icon: 'close',
                        emit: 'close',
                        name: '关闭',
                        has: '910101101102156', // 未分配权限 则添加 true,  否则传入权限标识即可
                        show: row.questionSts != '3'// 显示条件
                      },{
                        icon: 'delete',
                        emit: 'delete',
                        name: '删除',
                        has: '910101101102157', // 未分配权限 则添加 true,  否则传入权限标识即可
                        show: true // 显示条件
                      },
                    ]" />
              </template>
            </vxe-table-column>
            <template v-slot:empty>
              <span>查询无数据</span>
            </template>
          </vxe-table>
          <!--分页组件-->
          <page-pagination :pageSize="size" :current="page" :total="total" @size-change="sizeChange" />
            </a-col>
    </a-spin>
    <drawer-view ref="ticklingForm" @cancel="queryData" parentId="drawerViewDetail" />
  </div>
</template>

<script>
import { selectListFeedBack, exportFeedBack, closeFeedBack, deleteFeedBack } from '@/api/isolarErp/tickling';
import initDict from '@/mixins/initDict';
import { LeftMixin } from '@/mixins/LeftMixin';
import moment from 'moment';
import { getWidth } from '@/utils/util';
import { USER_INFO } from '@/store/mutation-types';
import tableHeight from '@/mixins/tableHeightAndSearchModel';
export default {
  mixins: [initDict, LeftMixin, tableHeight],
  components: {},
  name: 'tickling',
  data () {
    return {
      perms: '910101101102153,910101101102154,910101101102155,910101101102156,910101101102157',
      // 查询参数
      questionDesc: '',
      questionSts: undefined,
      ticklingDate: [],
      startTime: '',
      endTime: '',
      isComQuestion: '',
      exp_loading: false,
      userInfo: null,
      // 表格相关参数
      data: [],
      // 分页参数
      total: 0,
      page: 1,
      size: 10,
      columnList: [
        { name: 'areaName', comment: '反馈区域' },
        { name: 'departName', comment: '反馈部门' },
        { name: 'questionType', comment: '问题类型' },
        { name: 'questionSource', comment: '来源' },
        { name: 'createUserName', comment: '反馈人' }
      ] // table展示字段
    };
  },
  created () {
    this.columnList.forEach(item => {
      if (!item.width) {
        item.width = getWidth(item.comment);
      }
    });
    this.userInfo = Vue.ls.get(USER_INFO);
    // 加载数据字典
    this.getDictMap('question_sts');
    this.queryData();
  },
  methods: {
    moment,
    // 表-列刷新
    refreshColumn () {
      if (this.$refs.multipleTable) {
        this.$refs.multipleTable.refreshColumn();
      }
    },
    // 查询
    async queryData () {
      let self = this;
      self.pageloading = true;
      let $table = this.$refs.multipleTable;
      $table && await $table.clearScroll();
      let param = {
        'curPage': self.page,
        'size': self.size,
        'questionDesc': self.questionDesc,
        'questionSts': self.questionSts,
        'startTime': self.ticklingDate ? self.ticklingDate[0] : '',
        'endTime': self.ticklingDate ? self.ticklingDate[1] : '',
        'isComQuestion': self.isComQuestion,
        'sortFiled': self.sortFiled, // 列表字段值
        'sortKind': self.sortKind // 排序 其中desc是降序，asc升序
      };
      selectListFeedBack(param).then(res => {
        if (res.result_code == '1') {
          self.data = res.result_data.rows;
          self.total = res.result_data.total;
        } else {
          self.data = [];
          self.total = 0;
        }
        self.pageloading = false;
      }).catch(() => {
        self.data = [];
        self.total = 0;
        self.pageloading = false;
      });
    },
    // 导出
    exportBtn () {
      let self = this;
      self.exp_loading = true;
      let param = {
        'curPage': self.page,
        'size': self.size,
        'questionDesc': self.questionDesc,
        'questionSts': self.questionSts,
        'startTime': self.ticklingDate ? self.ticklingDate[0] : '',
        'endTime': self.ticklingDate ? self.ticklingDate[1] : '',
        'isComQuestion': self.isComQuestion,
        'sortFiled': self.sortFiled, // 列表字段值
        'sortKind': self.sortKind // 排序 其中desc是降序，asc升序
      };
      exportFeedBack(param).then(res => {
        self.$downloadFile(res.result_data);
        self.exp_loading = false;
      }).catch(() => {
        self.exp_loading = false;
      });
    },
    // 开关
    onChange (checked) {
      if (checked) {
        this.isComQuestion = '0';
      } else {
        this.isComQuestion = '';
      }
      this.pageChange();
    },
    // 反馈问题 1  新增常见问题 2  查看 3  编辑  4  回复 5  查看(需要刷新主页面) 6
    showTicklingForm (type, row) {
      if (type == '3' && this.userInfo.username == row.createUser && row.questionSts != '3' && row.isComQuestion == '1') {
        type = '6';
      }
      this.$refs.ticklingForm.init(type, row, '/user/tickling/form');
    },
    // 关闭
    closeClick (row) {
      let self = this;
      self.$confirm({
        title: '关闭后此问题将不能再回复，确定要关闭吗?',
        confirmText: '确定',
        okText: '确定',
        cancelText: '取消',
        onOk () {
          self.pageloading = true;
          let map = {
            'id': row.id
          };
          closeFeedBack(map).then(res => {
            self.pageloading = false;
            if (res.result_code == '1') {
              self.$message.success('关闭成功');
              self.pageChange(1);
            } else {
              self.$message.warning(res.result_msg);
            }
          });
        }
      });
    },
    // 删除
    deleteClick (row) {
      let self = this;
      self.$confirm({
        title: '确定要删除吗?',
        confirmText: '确定',
        okText: '确定',
        cancelText: '取消',
        onOk () {
          self.pageloading = true;
          let map = {
            'id': row.id
          };
          deleteFeedBack(map).then(res => {
            self.pageloading = false;
            if (res.result_code == '1') {
              self.$message.success('删除成功');
              self.pageChange(1);
            } else {
              self.$message.warning(res.result_msg);
            }
          });
        }
      });
    },
    // 查询点击事件
    pageChange () {
      this.page = 1;
      this.queryData();
    },
    // 分页事件
    sizeChange (p, e) {
      this.page = p;
      this.size = e;
      this.queryData();
    }
  }
};
</script>
<style lang="less" scoped>
  .tickling-page {
    width: 100%;
    height: 100%;
  }
</style>
