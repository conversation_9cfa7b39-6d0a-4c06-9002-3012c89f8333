<template>
  <a-modal v-model="typeDialog" :maskClosable="false" :footer="null" centered @cancel="close" :title="'详情'" width="55%">
    <a-row class="type-detail">
      <a-col :sm="24" :md="12" class="com-color">
        <label for="设备类型">设备类型：</label>
        <span :title="typeDetail.deviceName">{{ typeDetail.deviceName }}</span>
      </a-col>
      <a-col class="com-color" :sm="24" :md="12" v-if="typeDialog && typeDetail.parentId != 0">
        <label for="设备类型父类">设备类型父类：</label>
        <span :title="typeDetail.parentDeviceName"> {{ typeDetail.parentDeviceName }}</span>
      </a-col>
      <a-col class="com-color" :sm="24" :md="12">
        <label for="单位">单位：</label>
        <span>{{unitName}}</span>
      </a-col>
      <a-col  class="com-color" :sm="24" :md="12">
        <label  for="显示状态">显示状态：</label>
        <span>{{statusName}}</span>
      </a-col>
      <a-col class="com-color" :sm="24" :md="24">
        <label  for="设备固有项目">设备固有项目：</label>
        <vxe-table ref="multipleTable" :data="typeDetail.setItemList" size="small" max-height="300"
          resizable border align="center" style="width: calc(100% - 125px)">
          <vxe-table-column type="seq" title="序号" width="80"></vxe-table-column>
          <vxe-table-column field="setItemDataName" title="项目名称" show-header-overflow></vxe-table-column>
          <vxe-table-column field="isRequired" title="是否必填">
            <template v-slot="{ row }">
            <span>{{ row.isRequired == 1 ? "是" : "否" }}</span>
            </template>
          </vxe-table-column>
          <template v-slot:empty>
            <span class="com-color">查询无数据</span>
          </template>
        </vxe-table>
      </a-col>
    </a-row>
  </a-modal>
</template>

<script>
export default {
  name: 'typeDetail',
  model: {
    prop: 'value',
    event: 'change'
  },
  props: {
    value: {
      type: Boolean,
      default: false
    },
    typeDetail: {
      type: Object
    },
    dictMap: {
      type: Object
    }
  },
  data () {
    return {
      typeDialog: false
    };
  },
  watch: {
    value (val, old) {
      this.typeDialog = val;
      if (!val) {
        this.typeDetail = {};
        this.dictMap = {};
      }
    }
  },
  mounted () {
    this.typeDialog = this.value;
  },
  computed: {
    'unitName' () {
      let unit = '';
      if (this.dictMap && this.dictMap.unit) {
        this.dictMap.unit.forEach(element => {
          if (this.typeDetail.deviceUnit == element.codeValue) {
            unit = element.dispName;
          }
        });
      }
      return unit || '--';
    },
    'statusName' () {
      let name = '';
      if (this.dictMap && this.dictMap.device_status) {
        this.dictMap.device_status.forEach(element => {
          if (this.typeDetail.deviceStatus == element.codeValue) {
            name = element.dispName;
          }
        });
      }
      return name || '--';
    }
  },
  methods: {
    close () {
      this.$emit('change', false);
      this.typeDetail = {};
      this.dictMap = {};
      this.typeDialog = false;
      this.$parent.detailDialog = false;
    }
  }
};
</script>

<style lang="less" scoped>
.type-detail {
  :deep(.ant-col) {
  line-height: 30px;
  display: flex;
  }

  label {
    width: 125px;
    text-align: right;
    display: inline-block;
    margin-right: 5px;
  }

  span {
    flex: 1;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
  }
}
</style>
