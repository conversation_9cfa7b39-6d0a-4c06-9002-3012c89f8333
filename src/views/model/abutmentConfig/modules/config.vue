<template>
  <a-drawer
    width="100%"
    :visible="visible"
    :title="title"
    @close="close(false)"
    :get-container="getDom"
    :wrap-style="{ position: 'absolute' }"
  >
    <a-spin :spinning="loading" style="height: 100%;">
      <div class="config-content">
        <div class="left-tab">
          <div class="tab-box">
            <div class="tab-title">协议任务</div>
            <div class="tab-content" v-for="item in taskList" :key="item.taskId" @click="leftTabClick('1', item)" :class="{'active': item.active}">
              {{ item.taskName }}
            </div>
          </div>
          <div class="tab-box">
            <div class="tab-title">接收信息</div>
            <div class="tab-content" v-for="item in receiveList" :key="item.type" @click="leftTabClick('2', item)" :class="{'active': item.active}">
              {{ item.name }}
            </div>
          </div>
        </div>
        <div class="right-content">
          <a-tabs v-model="activeTab" v-if="activeContent == '1'" @change="tabchange">
            <a-tab-pane key="1" tab="规则参数">
              <rules-set ref="rulesSet" :type="type" :sourceId="sourceId" :taskId="taskId" :row="row"
               @setLoading="setLoading" @lisChange="firstInit = true"/>
            </a-tab-pane>
            <a-tab-pane key="2" tab="参数列表">
              <rules-list ref="rulesList" :type="type" :sourceId="sourceId" :taskId="taskId"
               @setLoading="setLoading" :source="row.source"/>
            </a-tab-pane>
          </a-tabs>
          <recieve v-else ref="recieve" :sourceId="sourceId" @setLoading="setLoading" :type="type"/>
        </div>
      </div>
    </a-spin>
  </a-drawer>
</template>
<script>
import recieve from './recieve.vue'
import RulesSet from './RulesSet.vue'
import RulesList from './RulesList.vue'
export default {
  components: { recieve, RulesList, RulesSet},
  data() {
    return {
      title: '',
      type: '',
      visible: false,
      loading: false,
      taskList: [],
      receiveList: [ { type: '1', name: '电站列表信息', active: false} ],
      activeContent: '1',
      activeTab: '1',
      firstInit: true,
      sourceId: '',
      taskId: '',
      row: {}
    }
  },
  methods: {
    getDom () {
      return document.getElementsByClassName('abutment-config')[0];
    },
    init (row, type, taskList) {
      this.title = type == '1' ? '配置' : '详情';
      this.taskList = taskList;
      this.taskList[0].active = true;
      this.row = row;
      this.type = type;
      this.visible = true;
      this.firstInit = true;
      this.sourceId = row.id;
      this.taskId = taskList[0].id;
      this.$nextTick(() => {
        this.$refs.rulesSet.getList();
      })
    },
    close () {
      this.visible = false;
      this.activeTab = '1';
      this.activeContent = '1';
      this.receiveList[0].active = false;
    },
    leftTabClick(activeContent, data) {
      this.activeContent = activeContent;
      if(!data.active) {
        this.taskList.forEach(item => { item.active = false; })
        this.receiveList.forEach(item => { item.active = false; })
        data.active = true;
      }
      if(activeContent == '1') {
        this.activeTab = '1';
        this.firstInit = true;
        this.taskId = data.id;
        this.$nextTick(() => {
          this.$refs.rulesSet.getList();
        })
      } else {
        this.$nextTick(() => {
          this.$refs.recieve.getPsList(1);
        });
      }
    },
    tabchange(val) {
      if(val == '2' && this.firstInit) {
        this.firstInit = false;
        this.$nextTick(() => {
          this.$refs.rulesList.resetSearch(true);
        });
      }
    },
    setLoading (loading) {
      this.loading = loading;
    }
  }
}
</script>
<style lang="less" scoped>
:deep(.ant-drawer-body) {
  height: calc(100% - 55px);
  padding: 0 24px;
}
:deep(.ant-spin-container) {
  height: 100%;
}
.config-content {
  display: flex;
  height: 100%;
  .left-tab {
    height: calc(100% - 34px);
    width: 275px;
    border-right: 1px solid #E8E8E8;
    overflow: auto;
    padding: 14px 0 24px;
    margin-top: 10px;
    .tab-box {
      .tab-title {
        font-weight: bold;
        line-height: 22px;
      }
      .tab-content {
        padding-left: 8px;
        color: #3D3D3D;
        margin-top: 16px;
        line-height: 22px;
        cursor: pointer;
      }
      .tab-content:hover, .active {
        color: var(--zw-primary-color--default);
      }
      // .active {
      //   font-weight: bold;
      // }
    }
    .tab-box + .tab-box {
      margin-top: 32px;
    }
  }
  .right-content {
    width: calc(100% - 275px);
    padding: 12px 0 24px 24px;
    height: 100%;
  }
}
:root[data-theme='dark'] {
  .left-tab {
    border-right: 1px solid rgba(53, 70, 97, 0.4);
    .tab-box {
      .tab-content {
        color: #fff;
      }
      .tab-content:hover, .active {
        color: #24CCFF;
      }
    }
  }
}
</style>