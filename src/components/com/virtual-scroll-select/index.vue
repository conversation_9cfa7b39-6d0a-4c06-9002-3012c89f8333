<template>
  <a-select
    v-model="inputValue"
    allowClear
    @deselect="changeSelect"
    :mode="isMultiple ? 'multiple' : ''"
    ref="optionRef"
    @change="selectChange"
    show-search
    @search="selectSearch"
    style="width: 100%"
    :maxTagCount="maxTagCount"
    :maxTagTextLength="maxTagTextLength"
    placeholder="请输入关键字搜索"
    showArrow
  >
    <div slot="dropdownRender">
      <VirtualList
        ref="virtualList"
        class="virtualselect-list"
        :data-key="selectData.value"
        :data-sources="selectArr"
        :isMultiple="isMultiple"
        :data-component="itemComponent"
        :keeps="20"
        :extra-props="{
          label: selectData.label,
          value: selectData.value,
          isMultiple: isMultiple,
          onItemClick: disposeSelected,
        }"
      />
      <div class="no-data" v-if="selectData.data.length <= 0 || selectArr.length <= 0">暂无数据</div>
    </div>
  </a-select>
</template>

<script>
import VirtualList from 'vue-virtual-scroll-list';
import OptionNode from './OptionNode';

export default {
  components: {
    VirtualList
  },
  model: {
    prop: 'modelValue',
    event: 'change'
  },
  props: {
    modelValue: {
      type: [String, Array],
      default: () => []
    },
    // 是否多选
    isMultiple: {
      type: Boolean,
      default: true
    },
    // 默认项
    defaultValue: {
      type: Array,
      default: () => {
        // return ['China0', 'China1'];
        return [];
      }
    },
    // 默认传入值
    selectData: {
      type: Object,
      default: () => ({
        data: [],
        label: 'name',
        value: 'code'
      })
    },
    maxTagCount: {
      type: Number,
      default: 1
    },
    maxTagTextLength: {
      type: Number,
      default: 12
    }
  },
  data () {
    return {
      selectDataTemplt: [], // 存储原始数据
      inputValue: [], // 双向绑定值
      itemComponent: OptionNode,
      selectArr: [], // 下拉选项
      selectedArr: [], // 选中的数组
      isFirst: true // 是否第一次 默认回显使用
    };
  },

  watch: {
    'selectData.data' (val) {
      this.selectedArr = this.modelValue;
      const i = this.selectedArr ? (Array.isArray(this.selectedArr) ? this.selectedArr : [this.selectedArr]) : [];
      this.inputValue = val.filter((item) => i.includes(item[this.selectData.value])).map((item) => item[this.selectData.label]);
      val.forEach(item => {
        if (i.includes(item[this.selectData.value])) {
          item.checked = true;
        }
      });
      this.init();
    }
    // 'selectArr'(val){
    //   console.log('===> selectArr', val)
    // },
    // 'selectedArr'(val){
    //   console.log('===> selectedArr', val)
    // },
    // 'inputValue'(val) {
    //   console.log('===> inputValue', val)
    // },
  },
  methods: {
    // 清空操作
    selectChange (value) {
      /// 多选时候清空value是空数组  单选时候是undefined
      if ((value && value.length == 0) || !value) {
        this.selectDataTemplt.forEach((item) => {
          item.checked = false;
        });
        this.selectArr = [...this.selectDataTemplt];
        this.selectedArr = [];
        this.$emit('change', this.isMultiple ? [] : undefined);
        this.init();
      }
    },
    // 搜索框有值时候调用
    selectSearch (value) {
      if (value) {
        // 搜索时候滚动到顶部
        this.$refs.virtualList.scrollToIndex(0);
        this.selectArr = this.selectDataTemplt.filter((item) => item[this.selectData.label].includes(value));
      } else {
        // 搜索框没有值时候，显示全部
        this.selectArr = [...this.selectDataTemplt];
      }
    },
    // input框删除选中
    changeSelect (value) {
      let delObj = this.selectArr.filter((item) => item[this.selectData.label] === value);
      if (delObj[0] && delObj[0][this.selectData.value]) {
        this.disposeSelected(delObj[0]);
      }
    },
    // 处理选中项 没有选中，就选中
    disposeSelected (item) {
      if (this.isMultiple) {
        // 多选 选中就取消
        if (this.selectedArr.includes(item[this.selectData.value])) {
          this.selectedArr = this.selectedArr.filter((list) => list !== item[this.selectData.value]);
          item.checked = false;
        } else {
          this.selectedArr.push(item[this.selectData.value]);
          item.checked = true;
        }
        this.$emit('change', this.selectedArr);
      } else {
        // 单选
        this.selectedArr = [item[this.selectData.value]];
        this.selectArr = this.selectArr.map((list) => {
          if (list[this.selectData.value] !== item[this.selectData.value]) {
            return { ...list, checked: false };
          }
          return { ...list, checked: true };
        });
        this.$emit('change', item[this.selectData.value]);
      }
      this.showInput();
    },
    // 显示选中项
    showInput () {
      // 获取选中的数组，用初始数据比较全面 搜索时候this.selectArr不全
      const selectedLabel = [];
      for (let i = 0; i < this.selectedArr.length; i++) {
        for (let j = 0; j < this.selectDataTemplt.length; j++) {
          if (this.selectedArr[i] === this.selectDataTemplt[j][this.selectData.value]) {
            selectedLabel.push(this.selectDataTemplt[j][this.selectData.label]);
            break;
          }
        }
      }
      this.inputValue = selectedLabel;
      // 由于单击选项会使input失去焦点，导致下拉框收起，所以自动获取焦点
      this.$refs.optionRef.focus();
    },
    init () {
      // 后面的数组会覆盖前面的数组,存储已选值
      const map = new Map()
      // 单次遍历合并两个数组
      ;[...this.selectData.data, ...this.selectArr].forEach((item) => {
        map.set(item[this.selectData.value], item); // 如果 key 已存在，会被 array2 的值替换
      });
      this.selectDataTemplt = Object.freeze(Array.from(map.values()));
      this.selectArr = Array.from(map.values());
      // 第一次如果有默认值默认回显
      if (this.isFirst && this.defaultValue.length) {
        // 处理回显
        this.inputValue = [...this.defaultValue];
        this.disposeEcho();
        this.isFirst = false;
      }
    },
    disposeEcho () {
      // 寻找回显对象
      const result = this.selectDataTemplt.filter((item) => this.inputValue.includes(item[this.selectData.value]));
      result.forEach((item) => {
        this.disposeSelected(item);
      });
    },
    visibleChange (bool) {
      // 下拉框显示时候初始化
      if (!bool) {
        setTimeout(() => {
          this.$refs.virtualList.reset();
          this.init();
        });
      }
    },
    reset () {
      Object.assign(this.$data, this.$options.data());
      this.selectData = { ...this.selectData, data: [] };
      this.selectChange(this.isMultiple ? [] : undefined);
    }
  }
};
</script>

<style lang="less" scoped>
.virtualselect {
  &-list {
    max-height: 245px;
    overflow-y: auto;
  }
}

.no-data {
  text-align: center;
  height: 100px;
  line-height: 100px;
}
</style>
