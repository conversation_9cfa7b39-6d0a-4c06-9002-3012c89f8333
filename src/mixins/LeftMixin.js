import { USER_INFO } from '@/store/mutation-types';
export const LeftMixin = {
  data () {
    return {
      deptCode: '',
      psaIds: '',
      selectId: '', // 左侧交互，部门or项目
      selectName: '', // 左侧交互，部门or项目名称
      pageloading: false, // 页面loading
      doubleRight: false, // 左侧树显示参数
      // 排序参数
      sortFiled: '',
      sortKind: '',
      // 自定义表格排序参数
      downSort: false,
      upSort: false,
      // 状态统计
      tabKind: '', // 1、全部 2、待办
      checkedAll: true, // 全部tab里的全选
      countAll: 0, // 全部tab里的全选统计数量
      checkedAllToDo: true, // 待办tab里的全选
      countAllToDo: 0, // 代办tab里的全选统计数量
      allStatusList: [], // 全部tab里所选参数
      todoStatusList: [], // 待办tab里所选参数
      defAll: [],
      defTodo: [],
      taskStatus0: 0, // 状态-未提交
      taskStatus1: 0, // 状态-处理中
      taskStatus2: 0, // 状态-已完成
      taskStatus3: 0, // 状态-已退回
      userInfo: {},
      maxHeight: 457 // 表格高度
    };
  },
  created () {
    this.userInfo = Vue.ls.get(USER_INFO);
  },
  computed: {
    excludeHy () {
      return Vue.ls.get(USER_INFO).companyId === '1';
    }
  },
  methods: {
    // 设置表格高度
    getTableHeight (arr, type) {
      /**
             * 59: 顶部一级菜单高度
             * 52：顶部菜单tab高度
             * 12：内容区域margin-top高度
             * 42：表格区域padding、margin高度
             * 42：分页高度
             * 12：距离底部高度
             */
      const clientHeight = document.body.clientHeight;
      if (!clientHeight) {
        return 457;
      }
      if (arr instanceof Array) {
        let sum = 0;
        arr.forEach(item => {
          const box = document.getElementById(item);
          let h = box.offsetHeight || box.clientHeight;
          if (h) {
            sum += Number(h);
          }
        });
        let tableH = Number(clientHeight - sum - 56 - 40 - 12 - 42 - 42 - 12);
        if (type) { // 分析页面没有分页组件
          tableH = tableH + 42;
          return (tableH < 177 ? 177 : tableH);
        }
        if (tableH < (clientHeight / 3)) {
          return 457;
        } else {
          return tableH;
        }
      } else {
        return 457;
      }
    },
    roleTreeChange (deptCode, psaIds) {
      this.deptCode = deptCode;
      this.psaIds = psaIds;
      if (this.queryParams) {
        Object.assign(this.queryParams, { 'deptCode': deptCode, 'psaIds': psaIds });
      }

      this.pageChange(1);
    },
    // 获取漏斗子组件传递过来值的方法
    getChild (val, name) {
      this.selectId = val;
      this.selectName = name;
      this.pageChange(1);
    },
    // 获取树组件传递过来值的方法 val key  node  valule
    listenChange (val, node) {
      this.selectId = node.id;
      this.selectName = node.name;
      this.pageChange(1);
    },
    // 全选change事件
    checkedAllClick (e) {
      let self = this;
      self.checkAll = e.target.checked;
      self.allStatusList = (self.checkedAll ? self.defAll : []);
      self.pageChange(1);
    },
    // 待办全选change事件
    checkedAllToDoClick (e) {
      let self = this;
      self.checkedAllToDo = e.target.checked;
      self.todoStatusList = (self.checkedAllToDo ? self.defTodo : []);
      self.pageChange(1);
    },
    // 多选框选中事件,区分待办和全部
    statusClick () {
      if (this.tabKind == '1') {
        this.checkedAll = (this.allStatusList.length == this.defAll.length);
      } else {
        this.checkedAllToDo = (this.todoStatusList.length == this.defTodo.length);
      }
      this.pageChange(1);
    },
    tabHandleClick () {
      this.pageChange(1);
    },
    /**
         * assingnData 方法名 数据赋值
         * params {*Object} data 传的状态数值
         */
    assingnData (data) {
      if (data) {
        this.taskStatus0 = data.uncommitted || data.taskStatus0 || 0;
        this.taskStatus1 = data.inprocess || data.taskStatus1 || data.processing || 0;
        this.taskStatus2 = data.audited || data.taskStatus2 || data.finished || 0;
        this.taskStatus3 = data.backed || data.taskStatus3 || data.returned || 0;
        this.countAll = parseInt(this.taskStatus0) + parseInt(this.taskStatus1) +
                    parseInt(this.taskStatus2) + parseInt(this.taskStatus3);
        this.countAllToDo = parseInt(this.taskStatus1) + parseInt(this.taskStatus3);
      } else {
        this.taskStatus0 = 0;
        this.taskStatus1 = 0;
        this.taskStatus2 = 0;
        this.taskStatus3 = 0;
        this.countAll = 0;
        this.countAllToDo = 0;
      }
    },
    // 左侧树交互
    showOrHide (val) {
      this.doubleRight = val;
    },
    // 表格排序事件
    sortChange ({ column, property, order }) {
      if (order) {
        this.sortFiled = property;
        this.sortKind = order;
      } else {
        this.sortFiled = '';
        this.sortKind = '';
      }
      this.pageChange(1);
    },
    // 自定义表格排序事件
    mySortChange (sortKind, sortFiled, downSort, upSort) {
      this.sortKind = sortKind;
      this.sortFiled = sortFiled;
      this.downSort = downSort;
      this.upSort = upSort;
      this.pageChange(1);
    }
  }
};
