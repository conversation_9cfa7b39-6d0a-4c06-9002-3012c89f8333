import {
  axios
} from '@/utils/erp-request';

// 获取左侧目录结构
export function getDocumentMenuList (data) {
  return axios({
    url: '/document/v1/document/getDocumentMenuList',
    method: 'post',
    data
  });
}
// 获取文档列表
export function getDocumentFileList (data) {
  return axios({
    url: '/document/v1/document/list',
    method: 'post',
    data
  });
}
export function fileDownload (data) {
  return axios({
    url: 'system/v1/annex/downloadBizAnnex',
    method: 'post',
    data
  });
}
// 文档在线预览
export function filePreview (data) {
  return axios({
    url: '/document/v1/document/filePreview',
    method: 'post',
    data
  });
}
