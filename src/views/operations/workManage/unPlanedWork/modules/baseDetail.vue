<!-- 任务基础表单 -->
<template>
  <div>
    <detail-layout :labelList="baseInfo" :form="order()">
      <template v-slot:isEngLegacy="baseform">
        <a-col :span="8" v-if="order().taskType == '8'" class="detail_layout_content">
          <span class="left">工程遗留</span>
          <span class="right">{{ order().isEngLegacy == '1' ?'是':'否' }}</span>
        </a-col>
      </template>
    </detail-layout>

    <BaseDetail ref="fillForm" title="" v-if="!isFault && order().taskType != '7'"></BaseDetail>

    <FaultDetail v-if="(isFault && order().isEngLegacy == '0') || order().taskType == '7'" ref="fillForm" title=""></FaultDetail>

    <LegacyDetail v-if="isFault && order().isEngLegacy == '1'" ref="fillForm"></LegacyDetail>
  </div>
</template>

<script>
import { baseInfo } from './formDetail/detailList';
import BaseDetail from './formDetail/BaseDetail';
import FaultDetail from './formDetail/FaultDetail';
import LegacyDetail from './formDetail/LegacyDetail';
export default {
  name: 'taskInfo',
  inject: ['type', 'order'],
  components: {
    FaultDetail,
    BaseDetail,
    LegacyDetail
  },
  data () {
    return {
      // 任务表单基础信息
      baseInfo: baseInfo
    };
  },
  mounted () {},
  computed: {
    isFault () {
      return ['8'].includes(this.order().taskType);
    }
  },
  created () {},
  methods: {
    initData () {},
    initRegister () {},
    clearValidate () {}
  }
};
</script>
<style lang="less" scoped>
  .line_height {
    line-height: 36px;

    .left {
      padding-right: 8px;
      width: 120px;
    }
  }
</style>
