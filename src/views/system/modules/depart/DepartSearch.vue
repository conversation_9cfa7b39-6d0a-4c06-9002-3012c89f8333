<template>
<div class="solar-eye-search-model">
            <a-row :gutter="24" class="solar-eye-search-content">
              <a-col :xxl="6">
        <div class="search-item search-item-s">
          <span class="search-label">所属区域</span>
          <a-cascader
            v-model="place"
            popupClassName="solar-cascade-h360"
            :options="options"
            :allow-clear="false"
            :load-data="loadOptions"
            placeholder="请选择所属国家/省/市/区"
            @change="onChange"
            change-on-select
            style="width: 100%"
          />
        </div>
        </a-col>
           <a-col :xxl="6">
        <div class="search-item search-item-s">
          <span class="search-label">所属业主</span>
          <a-select
            v-model="params.ownerId"
            @change="ownerChange"
            :showSearch="true"
            option-filter-prop="children"
            :filter-option="filterOption"
            allow-clear
            size="default"
            placeholder="请选择业主"
            style="width: 100%"
          >
            <a-select-option v-for="o in owners" :key="o.id" :value="o.id">
              {{ o.ownerName }}
            </a-select-option>
          </a-select>
        </div>
        </a-col>
           <a-col :xxl="6">
        <div class="search-item search-item-s">
          <span class="search-label">所属项目公司</span>
          <a-select
            v-model="params.ownerProjectId"
            :showSearch="true"
            option-filter-prop="children"
            :filter-option="filterOption"
            size="default"
            allow-clear
            placeholder="请选择项目公司"
            style="width: 100%"
          >
            <a-select-option v-for="o in ownerProjects" :key="o.id" :value="o.id">
              {{ o.projectCompany }}
            </a-select-option>
          </a-select>
        </div>
        </a-col>
        <template v-if="toggleSearchStatus">
         <!-- <a-col :xxl="6">
        <div class="search-item search-item-s">
          <span class="search-label">运维商</span>
          <a-input
            v-model="params.psaName"
            @blur="params.psaName = $trim($event)"
            size="default"
            placeholder="请输入电站名称"
            allowClear
          ></a-input>
        </div>
        </a-col> -->
           <a-col :xxl="6">
        <div class="search-item search-item-s">
          <span class="search-label">电站名称</span>
          <a-input
            v-model="params.psaName"
            @blur="params.psaName = $trim($event)"
            size="default"
            placeholder="请输入电站名称"
            allowClear
          ></a-input>
        </div>
        </a-col>
        </template>
         <a-col :xxl="6">
        <div class="search-item" :class="{'search-item-s': !toggleSearchStatus}">
        <a-button size="default" @click="search(true)">重置</a-button>
          <a-button size="default" class="solar-eye-btn-primary" @click="search()">查询</a-button>
          <span class="com-color" @click="handleToggleSearch">
          {{ toggleSearchStatus ? '收起' : '展开' }}
          <a-icon :type="toggleSearchStatus ? 'up' : 'down'" />
        </span>
        </div>
        </a-col>
      </a-row>
      </div>
</template>
<script>
import Depart from '../..//mixins/Depart';
export default {
  data () {
    return {
      toggleSearchStatus: false
    };
  },
  mixins: [Depart],
  created () {
    this.getAreaLazily();
    this.getOwners();
  },
  methods: {
    search (isReset) { // true 是重置, false是查询
      if (isReset) { this.initForm(); }
      this.$emit('search', this.params);
    },
    initForm () {
      // for (let item in this.params) {
      //   this.params[item] = '';
      // }
      Object.assign(this.$data.params, this.$options.data().params);
      this.place = [];
    },
    handleToggleSearch () {
      this.toggleSearchStatus = !this.toggleSearchStatus;
      this.$emit('toggle', this.toggleSearchStatus);
    }
  }
};
</script>
