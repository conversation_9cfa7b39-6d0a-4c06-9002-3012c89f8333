<!-- 任务基础表单 -->
<template>
  <detail-layout :labelList="baseInfo" :form="order()" title="">
    <template v-slot:uavModelId="baseform">
      <a-col :span="8" class="line_height" v-if="order().taskType == '10' && order().workSubclass == '2'">
        <span class="left">无人机型号:</span>
        <span class="right">{{ baseform.uavModelId }}</span>
      </a-col>
    </template>
    <template v-slot:cleanType="baseform">
      <template v-if="['1', '2'].includes(order().taskType)">
        <a-col class="line_height" :span="8">
          <span class="left">{{baseform.taskType == '1' ? '清洗' : '除草'}}容量(MW):</span>
          <span class="right">{{ baseform.cleanCap }}</span>
        </a-col>
      </template>
    </template>
  </detail-layout>
</template>

<script>
import { faultDetail } from './detailList';
export default {
  name: 'baseForm',
  inject: ['type', 'order'],
  data () {
    return {
      baseInfo: faultDetail
    };
  },
  watch: {},
  mounted () {},
  created () {},
  computed: {},
  methods: {}
};
</script>
