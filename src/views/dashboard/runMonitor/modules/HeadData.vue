<!--监控中心头部数据-->
<template>
    <div v-if="showType != 'combinerBox'" class="data-header">
      <div class="info-data flex-between">
        <svg-icon iconClass="real_power" style="font-size: 62px"></svg-icon>
        <div class="info-data-item">
          <countTo class="info-num" :startVal='0'
                   :decimals="dynamicDecimals(dataExchange(headData.realTimePower,'unit'))"
                   :endVal="dataExchange(headData.realTimePower,'data')"
                   :duration='1000'></countTo>
          <p class="info-text">实时功率<span>({{dataExchange(headData.realTimePower,'unit')}})</span></p>
        </div>
        <div class="info-data-item">
          <countTo class="info-num"
                   :startVal='0'
                   :decimals="4"
                   :endVal="dataExchange(showType==='powerStation' ? headData.accessCapacity:headData.radiation,'data')"
                   :duration='1000'></countTo>
          <p class="info-text">{{showType==='powerStation'? '接入容量' : '倾斜面辐射'}}<span>({{dataExchange(showType==='powerStation' ? headData.accessCapacity:headData.radiation,'unit')}})</span></p>
        </div>
      </div>
      <div v-if="showType!='inverterDetail'" class="divider"></div>

      <div v-if="showType!='inverterDetail'" class="info-data flex-between">
        <svg-icon iconClass="station_num" style="font-size: 62px"></svg-icon>
        <div class="info-data-item">
          <countTo class="info-num" :startVal='0' :decimals="showType==='powerStation' ? 0: dynamicDecimals(dataExchange(headData.accessCapacity,'data'))"
                   :endVal="dataExchange(showType==='powerStation' ? headData.stationNum: headData.accessCapacity,'data')"
                   :duration='1000'></countTo>
          <p class="info-text">{{showType==='powerStation' ? '电站数量': '装机容量'}}
            <span>({{dataExchange(showType==='powerStation' ? headData.stationNum: headData.accessCapacity,'unit')}})</span>
          </p>
        </div>

        <div class="info-data-item">
          <countTo class="info-num" :startVal='0'
                   :endVal="dataExchange(showType==='powerStation' ? headData.inverterNum: headData.deviceType == '1'? headData.inverterNum : headData.stringBoxNum,'data')"
                   :duration='1000'></countTo>
          <p class="info-text">{{showType==='powerStation' ? '逆变器数量': '装机台数'}}
            <span>({{dataExchange(showType==='powerStation' ? headData.inverterNum: headData.inverterNum,'unit')}})</span>
          </p>
        </div>
      </div>

      <div class="divider"></div>
      <div class="info-data flex-between">
        <svg-icon iconClass="daily_capacity" style="font-size: 62px"></svg-icon>
        <div class="info-data-item">
          <countTo class="info-num"
                   :decimals="dynamicDecimals(dataExchange(headData.dailyPowerGeneration,'unit'))"
                   :startVal='0'
                   :endVal="dataExchange(headData.dailyPowerGeneration,'data')" :duration='1000'></countTo>
          <p class="info-text">日发电量<span>({{dataExchange(headData.dailyPowerGeneration,'unit')}})</span></p>
        </div>
        <div class="info-data-item">
          <countTo class="info-num" :decimals="2" :startVal='0' :endVal="dataExchange(headData.usingHours,'data')"
                   :duration='1000'></countTo>
          <p class="info-text">日等效利用小时<span>({{dataExchange(headData.usingHours,'unit')}})</span></p>
        </div>
      </div>

      <div v-if="showType!='inverterDetail'" class="divider"></div>
      <div v-if="showType!='inverterDetail'" class="info-data flex-between">
      <svg-icon iconClass="monthly_capacity" style="font-size: 62px"></svg-icon>
        <div class="info-data-item">
          <countTo class="info-num"
                   :decimals="dynamicDecimals(dataExchange(headData.yearPower,'unit'))"
                   :startVal='0' :endVal="dataExchange(headData.monthPower,'data')"
                   :duration='1000'></countTo>
          <p class="info-text">月发电量<span>({{dataExchange(headData.monthPower,'unit')}})</span></p>
        </div>
        <div class="info-data-item">
          <countTo
            class="info-num"
            :decimals="dynamicDecimals(dataExchange(headData.yearPower,'unit'))"
            :startVal='0'
            :endVal="dataExchange(headData.yearPower,'data')"
            :duration='1000'></countTo>
          <p class="info-text">年发电量<span>({{dataExchange(headData.yearPower,'unit')}})</span></p>
        </div>
      </div>

      <div v-if="showType=='inverterDetail'" class="divider"></div>
      <div v-if="showType=='inverterDetail'" class="info-data flex-between">
        <div class="info-data-item">
          <countTo
            class="info-num"
            :decimals="dynamicDecimals(dataExchange(headData.monthPower,'unit'))"
            :startVal='0'
            :endVal="dataExchange(headData.monthPower,'data')"
            :duration='1000'></countTo>
          <p class="info-text">月发电量<span>({{dataExchange(headData.monthPower,'unit')}})</span></p>
        </div>
        <div class="info-data-item">
          <countTo
            class="info-num"
            :decimals="4"
            :startVal='0'
            :endVal="dataExchange(headData.yearPower,'data')"
            :duration='1000'></countTo>
          <p class="info-text">年发电量<span>({{dataExchange(headData.yearPower,'unit')}})</span></p>
        </div>
      </div>
    </div>
    <div class="data-header" v-else>
      <div class="info-data flex-between">
        <div class="info-data-item">
          <countTo class="info-num" :startVal='0'
                   :decimals="3"
                   :endVal="Number(headData.power)"
                   :duration='1000'></countTo>
          <p class="info-text">实时功率<span>(kW)</span></p>
      </div>
        <div class="info-data-item">
          <countTo class="info-num"
                   :startVal='0'
                   :decimals="3"
                   :endVal="Number(headData.maxPower)"
                   :duration='1000'></countTo>
          <p class="info-text">峰值功率<span>(kW)</span></p>
        </div>
        <div class="info-progress">
          <a-progress :percent="headData.percent" :showInfo="false"/>
          <div :style="'left:' + headData.percent + '%'" class="triangle-top"></div>
        </div>
      </div>
      <div class="divider"></div>
      <div class="info-data flex-between">
        <div class="info-data-item">
          <countTo class="info-num" :startVal='0' :decimals="2"
                   :endVal="Number(headData.voltage)"
                   :duration='1000'></countTo>
          <p class="info-text">电压
            <span>(V)</span>
          </p>
        </div>

        <div class="info-data-item">
          <countTo class="info-num" :startVal='0'
                   :decimals="2"
                   :endVal="Number(headData.electricCurrent)"
                   :duration='1000'></countTo>
          <p class="info-text">总电流
            <span>(A)</span>
          </p>
        </div>
      </div>
      <div class="divider"></div>
      <div class="info-data flex-start">
        <div class="info-data-item">
          <countTo class="info-num" :startVal='0' :decimals="2"
                   :endVal="Number(headData.internalTemperature)"
                   :duration='1000'></countTo>
          <p class="info-text">温度(℃)
          </p>
        </div>
      </div>
    </div>
</template>

<script>
import countTo from 'vue-count-to'; // 数值滚动插件

export default {
  props: {
    headData: {
      type: Object,
      default: () => {
        return {
          deviceType: '1',
          accessCapacity: '0,kW', // 接入容量
          dailyPowerGeneration: '0,kW', // 日发电量
          inverterNum: '0,台', // 逆变器数量
          stringBoxNum: '0,台', // 汇流箱数量
          // monthCompletionRate: '--', // 月完成率
          monthPower: '0,万kWh', // 月发电量
          realTimePower: '0,kW', // 实时功率
          radiation: '0,MJ/m²', // 倾斜面辐射
          stationNum: '0,座', // 电站数量
          usingHours: '0,h', // 日等效小时
          // yearCompletionRate: '--', // 年完成率
          yearPower: '0,万kWh', // 年发电量
          percent: 0, // 百分比
          power: 0, // 实时功率
          maxPower: 0, // 峰值功率
          voltage: 0, // 电压
          electricCurrent: 0, // 总电流
          internalTemperature: 0 // 温度
        };
      }
    },
    showType: {
      type: String,
      default: 'powerStation'
    }
  },
  components: { countTo },
  data () {
    return {};
  },
  created () {
  },
  computed: {
    pngSuffix () {
      return this.isSupportWebp ? '.webp' : '.png';
    }
  },
  methods: {
    dataExchange (data, type) {
      if (type === 'unit') {
        return data.split(',')[1];
      } else {
        return Number(data.split(',')[0]);
      }
    },
    dynamicDecimals (unit) {
      if (unit == 'kW' || unit == 'kWh') {
        return 3;
      } else {
        return 4;
      }
    }
  }
};
</script>

<style lang="less" scoped>
  .data-header {
    height: 97px;
    box-sizing: border-box;
    margin: 12px 0 16px;
    display: flex;
    display: -webkit-box;
    justify-content: flex-start;
    align-items: center;
    color: var(--zw-text-1-color--default);
    .info-data {
      background: var(--zw-card-bg-color--default);
      width: calc((100% - 3 * 16px) / 4);
     height: 96px;
      border-radius:6px;
     background-size: contain;
      display: flex;
      position: relative;
       padding: 0 24px;
      margin-right: 16px;
      .info-img {
        margin-bottom: 10px;
      }
      .info-data-item {
        text-align: left;
         text-align: left;
      width: calc((100% - 120px) / 2);
        .info-num {
          font-size: 26px;
          font-weight: 600;
          margin: 0;
          color: var(--zw-text-1-color--default);
        }

        .info-text {
          font-size: 14px;
          font-weight: 400;
          margin: 0;
          color: var(--zw-text-1-color--default);
        }
      }

      .info-progress {
        width: 80%;
        position: absolute;
        bottom: -20px;

        .ant-progress-inner {
          background: var(--zw-conduct-color--normal);
          border-radius: 100px;

          .ant-progress-bg {
            background: var(--zw-primary-color--default);
          }
        }

        .triangle-top {
          position: absolute;
          right: 50%;
          bottom: -60%;
          transform: translate(-50%, -50%);
          animation: move-right 1s linear;
          width: 0;
          height: 0;
          border-left: 5px solid transparent;
          border-right: 5px solid transparent;
          border-bottom: 10px solid var(--zw-warning-color--normal);
        }
      }
    }
  }

</style>
