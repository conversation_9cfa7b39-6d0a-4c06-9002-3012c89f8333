import { axios } from '@/utils/erp-request';
import { postAction } from '@/api/manage';
const baseUrl = process.env.VUE_APP_API_ERP_URL;
const url = process.env.VUE_APP_API_BASE_URL;
const querySafetyRiskList = (params) => postAction(baseUrl + '/v1/safetyRisk/list', params);// 安全检查列表
export { querySafetyRiskList };

export function addSafetyRiskList (data) {
  return axios({
    url: 'v1/safetyRisk/save',
    method: 'post',
    data
  });
}

export function deleteSafetyRisk (data) {
  return axios({
    url: 'v1/safetyRisk/delete',
    method: 'post',
    data
  });
}

export function exportSafetyRisk (data) {
  return axios({
    url: 'v1/safetyRisk/exportExcel',
    method: 'post',
    data
  });
}

export function queryFillingStatusList (data) {
  return axios({
    url: 'v1/safetyRisk/fillingStatus/list',
    method: 'post',
    data
  });
}

export function exportFillingStatus (data) {
  return axios({
    url: 'v1/safetyRisk/fillingStatus/exportExcel',
    method: 'post',
    data
  });
}

export function riskAllocation (data) {
  return axios({
    url: 'v1/safetyRisk/riskAllocation',
    method: 'post',
    data
  });
}

export function updateRisk (data) {
  return axios({
    url: 'v1/safetyRisk/edit',
    method: 'post',
    data
  });
}

export function riskDetail (data) {
  return axios({
    url: 'v1/safetyRisk/detail',
    method: 'post',
    data
  });
}
export function areaTree (data) {
  return axios({
    url: 'v1/safetyRisk/areaTree',
    method: 'post',
    data
  });
}
// 业主-下拉
export const ownerOfUserForIac = (params) => postAction(baseUrl + '/system/v2/ownerProject/ownerOfUser', params);
// 项目公司-下拉
export const projectOfUserForIac = (params) => postAction(baseUrl + '/system/v2/ownerProject/projectOfUser', params);
// 查询用户数据权限下的电站列表
export const getPowerStations = (params) => postAction(url + '/sys/user/power-stations', params);
