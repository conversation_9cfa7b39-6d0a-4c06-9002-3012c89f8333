<template>
  <detail-layout :labelList="labelList" :form="baseForm" title="设备信息" :dictMap='dictMap'>
    <template v-slot:deviceNo>
      <a-col :span="8" class="detail_layout_content" v-if="baseForm.deviceTypeId != 0">
        <span class="left">设备编号</span>
        <span :class="['right',isDroneOrder ? 'device-no' : '']">{{ getLabel(baseForm.deviceNo, null) }}</span>
        <span class='device-ps' v-if='isDroneOrder' @click="$emit('openCareMenu')">查看定位</span>
      </a-col>
    </template>
    <template v-slot:powerCutRange>
      <a-col :span="8" class="detail_layout_content" v-if="showPowerCutRange">
        <span class="left">停电范围</span>
        <span class="right">{{ powerCutRangeLabel }}</span>
      </a-col>
    </template>
  </detail-layout>
</template>

<script>

export default {
  name: 'DeviceInfoDetail',
  props: {
    baseForm: {
      type: Object,
      default: () => {
        return {};
      }
    },
    dictMap: {
      type: Object,
      default: () => {
        return {};
      }
    },
    isDroneOrder: {
      type: Boolean,
      default: false
    }
  },
  data () {
    return {
      labelList: [
        {
          label: '设备类型',
          key: 'deviceTypeIdName'
        },
        {
          label: '设备名称',
          key: 'deviceName',
          func: (params) => {
            return params.deviceTypeId != 0;
          }
        },
        // {
        //   label: '设备编号',
        //   key: 'deviceNo',
        //   func: (params) => {
        //     return params.deviceTypeId != 0;
        //   }
        // },
        { slot: 'deviceNo' },
        {
          label: '生产厂家',
          key: 'maker',
          func: (params) => {
            return params.deviceTypeId != 0;
          }
        },
        {
          label: '设备型号',
          key: 'deviceModel',
          func: (params) => {
            return params.deviceTypeId != 0;
          }
        },
        {
          label: '设备状态',
          key: 'deviceTypeName'
        },
        {
          label: (params) => {
            return params.taskType === '7' ? '故障名称' : '缺陷名称';
          },
          labelFunc: true,
          key: 'defectName',
          func: (params) => {
            return params.deviceTypeId != 0;
          }
        },
        {
          label: '处理条件',
          key: 'defectHandleCondition',
          func: (formData) => {
            if (formData.taskType === '8' && formData.deviceTypeId != '0') {
              return true;
            }
            return false;
          },
          dict: 'defect_handle_condit'
        },
        { slot: 'powerCutRange' },
        {
          label: (params) => {
            return params.taskType === '7' ? '故障类别' : '缺陷类别';
          },
          labelFunc: true,
          key: 'defectTypeName'
        }
      ]
    };
  },
  computed: {
    powerCutRangeLabel () {
      const { psaName } = this.baseForm;
      if (/扶贫|分布式/g.test(psaName)) {
        return this.getLabel(this.baseForm.powerCutRange, this.dictMap.power_cut_dis);
      }
      return this.getLabel(this.baseForm.powerCutRange, this.dictMap.power_cut_centre);
    },
    showPowerCutRange () {
      const deviceTypeId = this.baseForm.deviceTypeId;
      return this.baseForm.taskType === '7' && deviceTypeId && deviceTypeId[0] != '0';
    }

  },
  watch: {
    baseForm: {
      handler: function (val) {
        console.log(val);
      },
      immediate: true
    }
  }
};
</script>

<style lang='less' scoped>
.device-no{
  max-width: 200px;
  display: inline;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  flex: unset !important;
}
.device-ps{
  color: #267DCF;
  margin-left: 12px;
  cursor: pointer;
  &:hover{
    color: var(--zw-primary-color--default);
  }
}
</style>
