<!--系统BMS  -->
<template>
<a-spin size="large" :spinning="wholePageLoading" class="card-spin-center">
  <div class="device">
      <!-- <section>
      <a-radio-group v-model="alarmGrade" @change="alarmGradeChange" size="default">
        <a-radio-button value="">全部({{ activeTotal }})</a-radio-button>
        <a-radio-button v-for="item in alarmNumList" :key="item.grade" :value="item.grade"
          >{{ item.gradeName }}({{ item.count }})</a-radio-button
        >
      </a-radio-group>
    </section> -->
    <div class="content  width_100">
      <template v-for="item in list">
        <div class="b-box" :key="item.ps_key">
          <header class="device_name">
            <div :title="item.deviceName" class="device_name_lable over-flow">
              {{item.deviceName}}
            </div>
            <div class="next_device"  @click="changeDevice( { value: 'RackBMS', name: 'Rack BMS', type: 24 },Object.assign(item,{'device_type': 23}))"></div>
          </header>
          <section>
            <div class="left"><img src="@/assets/images/monitor/device/bms.png" /></div>
            <div class="right">
              <div class="desc">
                <div class="desc-left">系统电压({{dealData(item.p230002, 'unit')}})：</div>
                <div class="desc-right" :title="dealData(item.p230002,'title')">{{dealData(item.p230002)}}</div>
              </div>
              <div class="desc">
                <div class="desc-left">系统电流({{dealData(item.p230003, 'unit')}})：</div>
                <div class="desc-right" :title="dealData(item.p230003,'title')">{{dealData(item.p230003)}}</div>
              </div>
              <div class="desc">
                <div class="desc-left">系统SOC({{dealData(item.p230004, 'unit')}})：</div>
                <div class="desc-right" :title="dealData(item.p230004,'title')">{{dealData(item.p230004)}}</div>
              </div>
              <div class="desc">
                <div class="desc-left">系统SOH(%)：</div>
                <div class="desc-right" :title="dealData(item.p230005)">{{dealData(item.p230005)}}</div>
              </div>
            </div>
          </section>
        </div>
      </template>
         <infinite-loading :identifier="infiniteId" v-if="list.length >= pageSize" @infinite="getList">
          <div slot="spinner">
            <a-spin size="large" v-if="!wholePageLoading" class="card-spin"></a-spin>
          </div>
          <div slot="no-more">
            <p class="no-more-data">无更多数据</p>
          </div>
        </infinite-loading>
         <div class="infinite-loading-container" v-if="list.length == 0 && !wholePageLoading">
            <img class="no-data-img" :src="noDataImg" />
            <p class="no-data-text">暂无数据</p>
          </div>
    </div>
  </div>
  </a-spin>
</template>
<script>
import deviceType from '../mixins/deviceType';
export default {
  mixins: [deviceType],
  data () {
    return {
      url: {
        count: '',
        alarmList: '/monitorCn/getSystemBMSListInfo'
      }
    };
  },
  methods: {
    dealData (data, isUnit) {
      if (!data) {
        return;
      }
      let isInUnit = data.indexOf(',') > -1;
      let dataNum = isInUnit ? data.split(',') : data;
      let isArr = Array.isArray(dataNum);
      let dealData = (isArr && dataNum[0]) ? dataNum[0] : data;
      let isTrue = dealData || dealData == 0;
      let str = String(dealData).length > 9 && !isUnit ? (String(dealData).slice(0, 7) + '...') : dealData;
      if (isUnit == 'unit') {
        return isArr && dataNum.length > 1 ? dataNum[1] : 'x';
      } else {
        return isTrue ? str : '--';
      }
    },
    changeDevice (obj, item) {
      this.$emit('select', obj, item);
    }
  }
};
</script>
<style lang="less" scoped>
@import url('../less/base.less');
.device {
 align-items: flex-start;
  .b-box {
    width: 382px;
    height: 210px;
    margin: 0 16px 16px 0;
  }
  .content {
    flex-wrap: wrap;
    height: calc(100vh - 410px);
    overflow: auto;
    align-content: flex-start;
    margin-top: 16px;
  }
  .b-box {
    section {
      padding: 8px 16px 8px 0;
      .left {
        width: 140px;
        margin-right: 16px;
      }
      .right {
        flex-direction: column;
        padding: 16px;
        justify-content: space-between;
        width: 178px;
        height: 132px;
        background: rgba(00, 00, 00, 0.19);
        border-radius: 8px;
        .desc {
          display: flex;
          color: white;
        }
        .desc-left {
          white-space: nowrap;
          flex: 1;
        }
        .desc-right {
          width: 80px;
        }
      }
    }
  }
}
</style>
