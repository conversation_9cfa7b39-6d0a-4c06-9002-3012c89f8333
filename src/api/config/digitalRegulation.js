import { postAction } from '@/api/manage';
const baseUrl = process.env.VUE_APP_API_ERP_URL;
// const baseUrl = "http://192.168.156.54:8080"
// 作业指导书配置

const addInstruction = (params) => postAction(baseUrl + '/pdca/sopConfig/addSopConfig', params);
const editInstruction = (params) => postAction(baseUrl + '/pdca/sopConfig/sopConfigEdit', params);
const deleteInstruction = (params) => postAction(baseUrl + '/pdca/sopConfig/sopConfigDel', params);
const listInstruction = (params) => postAction(baseUrl + '/pdca/sopConfig/list', params);
const getInstructionDetail = (params) => postAction(baseUrl + '/pdca/sopConfig/sopConfigDetail', params);
const getSopDetailForBus = (params) => postAction(baseUrl + '/pdca/sopConfig/getSopDetailForBus', params);
// ordertimeout 工单超时配置
const addOrderOut = (params) => postAction(baseUrl + '/pdca/wotConfig/wotAdd', params);
const editOrderOut = (params) => postAction(baseUrl + '/pdca/wotConfig/wotEdit', params);
const getOrderOutDetail = (params) => postAction(baseUrl + '/pdca/wotConfig/wotDetail', params);
const deleteOrderOut = (params) => postAction(baseUrl + '/pdca/wotConfig/wotDel', params);
const listOrderOut = (params) => postAction(baseUrl + '/pdca/wotConfig/list', params);
const getDate = (params) => postAction(baseUrl + '/pdca/wotConfig/getDateByTaskTypeAndDate', params);
// 填报超时配置
const addFillOut = (params) => postAction(baseUrl + '/pdca/rotConfig/rotAdd', params);
const editFillOut = (params) => postAction(baseUrl + '/pdca/rotConfig/rotEdit', params);
const getFillOutDetail = (params) => postAction(baseUrl + '/pdca/rotConfig/rotDetail', params);
const deleteFillOut = (params) => postAction(baseUrl + '/pdca/rotConfig/rotDel', params);
const listFillOut = (params) => postAction(baseUrl + '/pdca/rotConfig/list', params);

export {
  addInstruction,
  editInstruction,
  deleteInstruction,
  listInstruction,
  getInstructionDetail,
  addOrderOut,
  editOrderOut,
  getOrderOutDetail,
  deleteOrderOut,
  listOrderOut,
  getDate,
  addFillOut,
  editFillOut,
  getFillOutDetail,
  deleteFillOut,
  listFillOut,
  getSopDetailForBus
};
