// import defaultSettings from '../defaultSettings';
import { formatThemeConfig } from '@comp/tools/antdVariables';
import { getRootColor } from '@/utils/erpcommon';

let lessNodesAppended;

const updateTheme = primaryColor => {
  function buildIt () {
    // 正确的判定less是否已经加载less.modifyVars可用
    if (!window.less || !window.less.modifyVars) {
      return;
    }
    // less.modifyVars可用
    window.less.modifyVars(formatThemeConfig({
      '@primary-color': getRootColor('--zw-primary-color--default'), // 主色
      '@error-color': getRootColor('--zw-warning-color--normal'), // 错误色
      '@text-color': getRootColor('--zw-text-1-color--default'), // 主文色
      '@input-placeholder-color': getRootColor('--zw-text-3-color--default'), // 输入框提示语字体颜色
      '@border-color-base': getRootColor('--zw-border-color--default'), // 边框色
      '@background-color-light': getRootColor('--zw-card-bg-color--default'), // 页面卡片背景色
      '@background-color-base': getRootColor('--zw-input-bg-color--disable'), // 输入框禁用时的背景色
      '@layout-body-background': getRootColor('--zw-primary-bg-color--default'), // 整体页面背景
      '@component-background': getRootColor('--zw-card-bg-color--default'), // 页面卡片背景色
      '@border-color-split': getRootColor('--zw-divider-color--default'), // tab的分割线颜色
      '@primary-1': getRootColor('--zw-primary-partial-areas-color--hover'), // 主色-部分区域hover状态时的背景
      '@heading-color': getRootColor('--zw-text-1-color--default'), // drawer的title颜色
      '@text-color-secondary': getRootColor('--zw-text-1-color--default'), // drawer右侧x的颜色
      '@icon-color-hover': getRootColor('--zw-primary-color--hover'), // icon hover颜色
      '@disabled-color': getRootColor('--zw-text-3-color--default'), // 禁用颜色
      '@item-active-bg': getRootColor('--zw-primary-common-light-color--default') // menu菜单选中背景色
    }));
  }
  if (!lessNodesAppended) {
    // insert less.js and color.less
    const lessStyleNode = document.createElement('link');
    const lessConfigNode = document.createElement('script');
    const lessScriptNode = document.createElement('script');
    lessStyleNode.setAttribute('rel', 'stylesheet/less');
    lessStyleNode.setAttribute('href', __webpack_public_path__ + 'color.less');
    lessConfigNode.innerHTML = `
      window.less = {
        async: true,
        env: 'production',
        javascriptEnabled: true
      };
    `;
    lessScriptNode.src = 'https://gw.alipayobjects.com/os/lib/less.js/3.8.1/less.min.js';
    lessScriptNode.async = true;
    lessScriptNode.onload = () => {
      buildIt();
      lessScriptNode.onload = null;
    };
    document.body.appendChild(lessStyleNode);
    document.body.appendChild(lessConfigNode);
    document.body.appendChild(lessScriptNode);
    lessNodesAppended = true;
  } else {
    buildIt();
  }
};

const updateColorWeak = colorWeak => {
  colorWeak ? document.body.classList.add('colorWeak') : document.body.classList.remove('colorWeak');
};

export { updateTheme, updateColorWeak };
