// import Vue from 'vue'
import { ACCESS_TOKEN, TENANT_ID } from '@/store/mutation-types';
import store from '@/store';
/**
 * 单点登录
 */
const init = (callback) => {
  let token = Vue.ls.get(ACCESS_TOKEN);
  let st = getUrlParam('token');
  let sevice = 'http://' + window.location.host + '/';
  if (process.env.VUE_APP_SSO == 'true') {
    console.log('-------单点登录开始-------');
    if (token) {
      loginSuccess(callback);
    } else {
      if (st) {
        localStorage.setItem('deviceId', getUrlParam('deviceId'));
        validateSt(st, sevice, callback);
      } else {
        window.location.href = sevice + '#/user/login';
      }
    }
    console.log('-------单点登录结束-------');
  } else {
    if (token && !getUrlParam('token')) {
      loginSuccess(callback);
    } else {
      if (st) {
        localStorage.setItem('deviceId', getUrlParam('deviceId'));
        Vue.ls.set(ACCESS_TOKEN, getUrlParam('token'));
        Vue.ls.set(TENANT_ID, getUrlParam('tenant_id'));
        store.commit('SET_TENANT', getUrlParam('tenant_id'));
        store.commit('SET_TOKEN', getUrlParam('token'));
        validateSt(st, '', callback);
      } else {
        callback && callback();
      }
    }
  }
};
const SSO = {
  init: init
};

function getUrlParam (paraName) {
  let url = document.location.toString();
  let arrObj = url.split('?');

  if (arrObj.length > 1) {
    let arrPara = arrObj[1].split('&');
    let arr;

    for (let i = 0; i < arrPara.length; i++) {
      arr = arrPara[i].split('=');

      if (arr != null && arr[0] == paraName) {
        return arr[1];
      }
    }
    return '';
  } else {
    return '';
  }
}

function validateSt (ticket, service, callback) {
  let params = {
  };
  if (service) {
    params = {
      token: ticket,
      service: service
    };
  } else {
    params = {
      token: ticket
    };
  }
  store.dispatch('ValidateLogin', params).then(res => {
    // this.departConfirm(res)
    if (res.success) {
      loginSuccess(callback);
    } else {
      let sevice = 'http://' + window.location.host + '/';
      window.location.href = sevice + '/#/user/login';
    }
  }).catch((err) => {
    console.log(err);
    // that.requestFailed(err);
  });
}

function loginSuccess (callback) {
  callback();
}
export default SSO;
