<!--监控中心逆变器列表页-->

<template>
  <div>
    <head-data :headData="headData" showType="inverter"></head-data>
    <div class="inverter-list-search-header">
      <div class="search-header-left">
        <div class="search-select">
           <ps-tree-select @change="getChild" :isPsName="psName" v-model="psId" :isQueryPs="1" :hasMaintenanceStatusParams="true" style="width: 100%;" />
        </div>
        <a-select class="type-select" v-model="queryParams.deviceType"
                  :getPopupContainer="(node) => node.parentNode"  @change='handleAsyncChange'>
          <a-select-option key='1' :value="'1'">逆变器</a-select-option>
          <a-select-option v-if="hasCombinerbox" key='4' :value="'4'">汇流箱</a-select-option>
        </a-select>

        <a-date-picker class="search-time-picker" placeholder="请选择时间"
                       @ok="timeChange"
                       @change="timeChange"
                       :allowClear="false"
                       v-model="queryParams.chosenTime"
                       :getCalendarContainer="(node) => node.parentNode"
                       :disabled-date="disabledDate"
                       :show-time="{ format: 'HH:mm' }"
                       format="YYYY-MM-DD HH:mm">
          <a-icon slot="suffixIcon" type="history"/>
        </a-date-picker>
        <!-- <a-select class="type-select"
                  :getPopupContainer="(node) => node.parentNode"
                  v-model="queryParams.alarmGrade"
                  :options="warningTypeOptions">
        </a-select> -->
        <span v-if="queryParams.deviceType == '1'" class="search-item-label">显示归一化数据</span>
        <a-switch v-if="queryParams.deviceType == '1'" @change="isUnificationChange" v-model="isUnification" />
        <a-button class="solar-eye-btn-primary margin-left-24" :loading="pageloading" @click="search()">
          查询
        </a-button>
      </div>

    </div>
    <a-spin :spinning="pageloading">
      <div style="display:flex">
        <div class="inverter-list-chart-area" style="margin-right:16px">
        <div ref="chartLeft" class="monitor-inverter-list-chart-area-chart" id="chartLeft">
        </div>
      </div>
      <div class="inverter-list-chart-area">
        <div  ref="chartRight"  class="monitor-inverter-list-chart-area-chart" id="chartRight">
        </div>
      </div>
      </div>

      <!-- 4、如果该站有汇流箱则在下拉框可选择汇流箱，同时界面展示汇流箱信息，曲线展示总电流、实时功率数据，列表展示当前时刻汇流箱名称、总电流、母线电压、实时功率； -->
      <div class="monitor-list-area" >
        <div class="button-page">
          <a-button @click="doExport"  title="导出" class="exportBtn" size="large" icon="download"></a-button>
        </div>
        <vxe-table  v-show="deviceType=='1'" v-loading="pageloading" :data="listData" ref="inverterTable"
                   class="my-table"
                   @sort-change="sortChange"
                   :sort-config="{remote:true}"
                   resizable show-overflow highlight-hover-row size="small">
          <vxe-table-column show-overflow="title" :formatter="tabFormatter" field="name" title="逆变器"
                            min-width="200">
            <template slot-scope="scope">
              <span class="cursor-pointer" :class="readedPsKeys.includes(scope.row.psKey) ? 'solareye-color-off-line' : ''" >{{scope.row.name}}</span>
            </template>
          </vxe-table-column>
          <vxe-table-column show-overflow="title" :formatter="tabFormatter" field="accessCapacity" title="装机容量（kW）"
                            sortable
                            min-width="200">
          </vxe-table-column>
          <vxe-table-column show-overflow="title" :formatter="tabFormatter" field="dcPower" title="直流功率（kW）"
                            sortable
                            min-width="200"></vxe-table-column>
          <vxe-table-column show-overflow="title" :formatter="tabFormatter" field="acPower" title="交流功率（kW）"
                            sortable
                            min-width="200"></vxe-table-column>
          <vxe-table-column show-overflow="title" :formatter="tabFormatter" field="dailyPowerGeneration" title="日发电量（kWh）"
                            sortable
                            min-width="200"></vxe-table-column>
          <vxe-table-column show-overflow="title" :formatter="tabFormatter" field="usingHours" title="日等效小时（h）"
                            sortable
                            min-width="200"></vxe-table-column>

          <!--          暂时拿不到此数据-->
<!--          <vxe-table-column show-overflow="title" :formatter="tabFormatter" field="status" title="状态"-->
<!--                            min-width="140">-->
            <!--          <template slot-scope="scope">-->
            <!--            <div class="point-out">-->
            <!--              <div class="point-in">-->
            <!--                {{scope.row.status}}-->
            <!--              </div>-->
            <!--            </div>-->
            <!--          </template>-->
<!--          </vxe-table-column>-->
          <template v-slot:empty>
            <span>查询无数据</span>
          </template>
        </vxe-table>
        <vxe-table v-show="deviceType=='4'" v-loading="pageloading" :data="comboxListData" ref="comboxTable"
                   class="my-table"
                   @sort-change="sortComChange"
                   :sort-config="{remote:true}"
                   resizable show-overflow highlight-hover-row size="small">
          <vxe-table-column show-overflow="title" :formatter="tabFormatter" field="name" title="汇流箱"
                            min-width="200">
            <template slot-scope="scope">
              <span class="cursor-pointer" :class="readedPsKeys.includes(scope.row.psKey) ? 'solareye-color-off-line' : ''" >{{scope.row.name}}</span>
            </template>
          </vxe-table-column>
          <vxe-table-column show-overflow="title" :formatter="tabFormatter" field="electricCurrent" title="总电流（A）"
                            sortable
                            min-width="200">
          </vxe-table-column>
          <vxe-table-column show-overflow="title" :formatter="tabFormatter" field="voltage" title="母线电压（V）"
                            sortable
                            min-width="200"></vxe-table-column>
          <vxe-table-column show-overflow="title" :formatter="tabFormatter" field="power" title="实时功率（kW）"
                            sortable
                            min-width="200"></vxe-table-column>
          <template v-slot:empty>
            <span>查询无数据</span>
          </template>
        </vxe-table>
        <page-pagination
          :pageSize="queryParams.pageSize"
          :current="queryParams.currentPage"
          :total="total"
          @size-change="sizeChange"/>
      </div>
    </a-spin>

  </div>
</template>

<script>
import HeadData from '../modules/HeadData';
import echarts from '@/utils/enquireEchart';
import moment from 'moment';
import {
  getStationStatisticsByPsId,
  getInverterList,
  getInverterPowerChart,
  getCombinerBoxList,
  getCombinerBoxChart,
  exportInverterList,
  exportCombinerBoxList
} from '@/api/monitor/inverter/inverterList.js';
import { mixin } from '@/utils/mixin.js';
import {
  getSystemCodeList,
  psDeviceTypeList
} from '@/api/health/healthapi.js';
export default {
  components: { HeadData },
  name: 'InverterList',
  mixins: [mixin],
  props: {
    commonParams: {
      type: Object,
      default: () => {
      }
    }
  },
  data () {
    return {
      pageloading: false,
      psNameRander: true,
      firstLoad: false,
      hasCombinerbox: false,
      headData: {
        deviceType: '1', // 当前设备类型
        accessCapacity: '0,kW', // 接入容量
        dailyPowerGeneration: '0,kW', // 日发电量
        inverterNum: '0,台', // 逆变器数量
        stringBoxNum: '0,台',
        // monthCompletionRate: '--', // 月完成率
        monthPower: '0,万kWh', // 月发电量
        realTimePower: '0,kW', // 实时功率
        radiation: '0,MJ/m²', // 倾斜面辐射
        stationNum: '0,座', // 电站数量
        usingHours: '0,h', // 日等效小时
        // yearCompletionRate: '--', // 年完成率
        yearPower: '0,万kWh', // 年发电量
        percent: 0 // 百分比
      },
      isUnification: true, // 是否归一化
      queryParams: {
        deviceType: '1', // 设备类型从前端区分
        // alarmGrade:'', // 告警等级
        time: '', // 转换后的时间
        order: '', // 排序
        chosenTime: '', // 当前选中时间
        pageSize: 10,
        currentPage: 1
      },
      deviceType: '1',
      total: 0,
      myChartPower: null,
      myChartUsingHour: null,
      myChartRealPower: null, // 实时功率
      myChartCurrent: null, // 总电流
      chartOptionsPower: {},
      chartOptionsPowerUnification: {},
      readedPsKeys: [],
      scrollTop: 0,
      chartLegend: [],
      comboxChartLegend: [],
      xData: [],
      warningTypeOptions: [],
      listData: [],
      comboxListData: [],
      options: {
        tooltip: {
          trigger: 'axis',
          confine: true,
          formatter: params => {
            let str = params.reduce((prev, cur, index) => {
              return prev + cur.marker +
                  cur.seriesName + ' : ' +
                  (cur.value === '' ? '--' : cur.value) +
                  (index % 2 == 1 ? '</br>' : ' ');
            }, params[0].axisValue + '</br>');
            return str;
          }

        },
        legend: {
          type: 'scroll',
          data: [],
          right: 10,
          textStyle: {
            number: 14
          }

        },
        grid: {
          left: '3%',
          right: '4%',
          bottom: '3%',
          containLabel: true
        },
        toolbox: {},
        xAxis: {
          type: 'category',
          boundaryGap: false,
          data: []

        },
        yAxis: {
          type: 'value',
          // name:'kW',
          nameTextStyle: {
            align: 'left'
          }

        },
        series: []
      },
      psId: '',
      leftOption: null,
      rightOption: null,
      psName: ''
    };
  },
  created () {
    this.firstLoad = true;
    setTimeout(() => {
      this.firstLoad = false;
    }, 2000);
    this.psId = this.commonParams.psId;
    this.psName = this.commonParams.psName;
    psDeviceTypeList({ psId: this.psId }).then(res => {
      res.result_data.forEach(element => {
        if (element.deviceType == '4') {
          this.hasCombinerbox = true;
        }
      });
    });

    this.queryParams.chosenTime = moment().format('YYYY-MM-DD HH:mm');
    this.queryParams.time = this.timeExchange(this.queryParams.chosenTime);
    // this.getWarningTypes()
    if (this.queryParams.deviceType == '1') {
      this.queryParams.order = '';
      this.queryParams.currentPage = 1;
      this.refreshList();
    } else if (this.queryParams.deviceType == '4') {
      this.queryParams.order = '';
      this.queryParams.currentPage = 1;
      this.refreshComboxList();
    }
    this.refreshHeadData();
  },
  mounted () {
    this.initChart();
    this.xData = this.getTimeList(24, 5);
    if (this.psId != this.commonParams.psId && !this.firstLoad) {
      this.scrollTop = 0;
      this.readedPsKeys = [];
      this.psNameRander = false;
      this.$nextTick(() => {
        this.psNameRander = true;
      });
    }
    let dom = document.getElementById('runMonitor');
    dom.scrollTop = this.scrollTop;
    this.psId = this.commonParams.psId;
    if (this.queryParams.deviceType == '1') {
      this.queryParams.order = '';
      this.queryParams.currentPage = 1;
      this.refreshList();
    } else if (this.queryParams.deviceType == '4') {
      this.queryParams.order = '';
      this.queryParams.currentPage = 1;
      this.refreshComboxList();
    }
    this.refreshHeadData();
  },
  watch: {
    'navTheme' (color) {
      if (this.chartLeft) {
        this.changeTheme(this.leftOption, color, 0);
        this.chartLeft.setOption(this.leftOption, true);
        this.changeTheme(this.rightOption, color, 1);
        this.chartRight.setOption(this.rightOption, true);
      }
    }
  },
  methods: {
    changeTheme (obj, color, index) {
      let isDarkColor = color == 'dark' ? '#ffffff' : '#333';
      let tooltipBg = {
        backgroundColor: '#1f334e',
        borderColor: '#51637C',
        textStyle: {
          color: '#fff'
        },
        boxShadow: '0px 2px 8px 0px rgba(0, 0, 0, 0.15), 5px 4px 9px 0px rgba(3, 18, 38, 0.5)'
      }; let tooltipBg1 = {
        backgroundColor: '#fff',
        borderColor: '#f0f0f0',
        textStyle: {
          color: '#333'
        }
      };
      obj.legend[0].textStyle.color = isDarkColor;
      obj.yAxis[0].nameTextStyle.color = isDarkColor;
      obj.yAxis[0].axisLine.lineStyle.color = isDarkColor;
      obj.xAxis[0].axisLabel.color = isDarkColor;
      obj.yAxis[0].splitLine.lineStyle.color = color == 'dark' ? ['#354661'] : ['#d6d6d6'];
      obj.legend[0].pageIconColor = isDarkColor;
      obj.legend[0].pageTextStyle.color = isDarkColor;
      if (color == 'dark') {
        obj.tooltip[0] = Object.assign({}, obj.tooltip[0], tooltipBg);
      } else {
        obj.tooltip[0] = Object.assign({}, obj.tooltip[0], tooltipBg1);
      }
      //
    },
    doExport () {
      this.pageloading = true;
      if (this.queryParams.deviceType == '1') {
        exportInverterList({
          ...this.queryParams,
          psId: this.psId
        }).then(res => {
          this.$downloadFile({ fileBase64Code: res.result_data.strBase64, fileName: res.result_data.fileName });
          this.pageloading = false;
        }).catch(() => {
          this.pageloading = false;
        });
      } else if (this.queryParams.deviceType == '4') {
        exportCombinerBoxList({
          ...this.queryParams,
          psId: this.psId
        }).then(res => {
          this.$downloadFile({ fileBase64Code: res.result_data.strBase64, fileName: res.result_data.fileName });
          this.pageloading = false;
        }).catch(() => {
          this.pageloading = false;
        });
      }
    },
    resizeHandler (type) {
      this[type].resize();
    },
    search () {
      this.deviceType = this.queryParams.deviceType;
      this.$nextTick(() => {
        if (this.queryParams.deviceType == '1') {
          this.refreshList();
          this.$refs.inverterTable.refreshColumn();
          this.$refs.inverterTable.updateData();
        } else if (this.queryParams.deviceType == '4') {
          this.refreshComboxList();
          this.$refs.comboxTable.refreshColumn();
          this.$refs.comboxTable.updateData();
        }
      });
      this.refreshHeadData();
    },
    handleAsyncChange (deviceType) {
      this.queryParams.order = '';
      this.queryParams.currentPage = 1;
    },
    // 时间转换为向前取整五分钟格式
    timeExchange (time) {
      let minitues = Number(time.substr(-2, 2));
      let temp = minitues - minitues % 5;
      return time.substr(0, 14) + (temp < 10 ? '0' + temp : temp) + '';
    },
    async getWarningTypes () {
      let res = await getSystemCodeList({ firstTypeCode: '0070' });
      let arr = res.result_data['0070'].filter(item => item.secondTypeCode != '4').map(item => {
        return {
          value: item.secondTypeCode,
          label: item.secondName
        };
      });
      this.warningTypeOptions = [
        {
          label: '全部',
          value: ''
        },
        ...arr
      ];
      // this.alarmGradeList = this.warningTypeOptions.map(item => item.value)
    },

    disabledDate (current) {
      // 不能选择今天以后的
      return current && current > moment().endOf('day');
    },
    getChild (value, obj) {
      this.$emit('click', obj);
      this.psId = obj.id;
      this.hasCombinerbox = false;
      this.queryParams.currentPage = 1;
      this.$emit('changePageParams', {
        psId: obj.id,
        psName: obj.name
      });
      psDeviceTypeList({ psId: obj.id }).then(res => {
        res.result_data.forEach(element => {
          if (element.deviceType == '4') {
            this.hasCombinerbox = true;
          } else {
            this.queryParams.deviceType = '1';
          }
        });
      });
      this.readedPsKeys = [];
    },
    // goInverterDetail(row){
    //   if(!this.readedPsKeys.includes(row.psKey)){
    //     this.readedPsKeys.push(row.psKey)
    //   }
    //   let dom = document.getElementById('runMonitor')
    //   this.scrollTop = dom.scrollTop

    //   this.$emit('changePageParams', {
    //     psKey:row.psKey,
    //     psId:this.psId,
    //     interverName: row.name,
    //     deviceType:'1'
    //   })
    //   setTimeout(() => {
    //     this.$emit('changePage', 3)
    //   }, 10)
    // },
    timeChange (val, data) {
      this.queryParams.chosenTime = moment(val).format('YYYY-MM-DD HH:mm');
      this.queryParams.time = this.timeExchange(this.queryParams.chosenTime);
      this.xData = this.getTimeList(24, 5);
    },
    /**
       * [getTimeList description] 生成时间列表
       * @param  {[type]} hours [description] 小时
       * @param  {[type]} step  [description] 分段
       * @return {[type]}       [description] 时间段列表
       */
    getTimeList (hours, step) {
      var minutes = 60;
      var timeArr = [];
      for (var i = 0; i < hours; i++) {
        var str = '';
        if (i < 10) {
          str = 0 + '' + i;
        } else {
          str = '' + i;
        }

        for (var j = 0; j < minutes; j++) {
          if (j % step == 0) {
            var s = j < 10 ? ':' + 0 + '' + j : ':' + j;
            s = str + s;
            timeArr.push(s);
          }
        }
      }

      return timeArr;
    },
    sortChange (val) {
      console.log('排序', val.property, val.order);
      if (val.order == null) {
        this.queryParams.order = '';
      } else {
        this.queryParams.order = val.property + ' ' + val.order;
      }
      this.refreshList();
    },
    sortComChange (val) {
      console.log('排序', val.property, val.order);
      if (val.order == null) {
        this.queryParams.order = '';
      } else {
        this.queryParams.order = val.property + ' ' + val.order;
      }
      this.refreshComboxList();
    },
    isUnificationChange (val) {
      this.changeChart('chartLeft', this.isUnification ? this.chartOptionsPowerUnification : this.chartOptionsPower);
    },
    sizeChange (current, size) {
      this.queryParams.pageSize = size;
      this.queryParams.currentPage = current;
      if (this.queryParams.deviceType == '1') {
        this.refreshList();
      } else if (this.queryParams.deviceType == '4') {
        this.refreshComboxList();
      }
    },
    async refreshHeadData () {
      let res = await getStationStatisticsByPsId({
        psId: this.psId,
        time: this.queryParams.time,
        deviceType: this.queryParams.deviceType
      });
      if (res.result_code !== '1') {
        this.$message.error(res.result_msg);
        return;
      }
      if (Object.keys(res.result_data).length) {
        this.headData = Object.assign(this.headData, res.result_data);
      }
      this.headData.deviceType = this.queryParams.deviceType;
      this.headData.percent = this.headData.percent > 100 ? 100 : this.headData.percent;
    },

    initChart () {
      this.chartLeft = echarts.init(this.$refs.chartLeft);
      this.chartRight = echarts.init(this.$refs.chartRight);

      this.chartLeft.setOption(this.options);
      this.chartRight.setOption(this.options);
    },

    async refreshChart () {
      let res = await getInverterPowerChart({
        deviceType: this.queryParams.deviceType,
        time: this.queryParams.time + ':00',
        ids: this.listData.map(item => item.psKey),
        psId: this.psId
      });
      this.pageloading = false;

      if (res.result_code !== '1') {
        this.$message.error(res.result_msg);
        return;
      }

      this.chartOptionsPower = res.result_data.map(item => {
        return {
          data: item.acPower,
          name: item.deviceName,
          type: 'line',
          symbol: 'circle'
        };
      });
      this.chartOptionsPowerUnification = res.result_data.map(item => {
        return {
          data: item.unification,
          name: item.deviceName,
          type: 'line',
          symbol: 'circle'
        };
      });
      let usingHours = res.result_data.map(item => {
        return {
          data: item.usingHours,
          name: item.deviceName,
          type: 'line',
          symbol: 'circle'
        };
      });
      this.changeChart('chartLeft', this.isUnification ? this.chartOptionsPowerUnification : this.chartOptionsPower);
      this.changeChart('chartRight', usingHours);
    },
    async refreshComboxChart () {
      await getCombinerBoxChart({
        // deviceType:this.queryParams.deviceType,
        time: this.queryParams.time + ':00',
        ids: this.comboxListData.map(item => item.psKey),
        psId: this.psId
      }).then(res => {
        if (res.result_code == '1') {
          let realPower = res.result_data.map(item => {
            return {
              data: item.powers,
              name: item.deviceName,
              type: 'line',
              symbol: 'circle'
            };
          });
          let realCurrent = res.result_data.map(item => {
            return {
              data: item.electricCurrent,
              name: item.deviceName,
              type: 'line',
              symbol: 'circle'
            };
          });
          this.changeComboxChart('chartLeft', realPower);
          this.changeComboxChart('chartRight', realCurrent);
        } else {
          this.$message.error(res.result_msg);
        }
      }).catch(error => {
        this.$message.error(error);
      });
      this.pageloading = false;
    },
    async changeChart (type, yData) {
      let options = this[type].getOption();
      options.series = yData;
      options.legend[0].data = this.chartLegend;
      options.xAxis[0].data = this.xData;
      options.yAxis[0].name = type == 'chartLeft' ? (this.isUnification ? '功率归一化（kW/kWp）' : '功率（kW）') : '等效小时（h）';
      this.changeTheme(options, this.navTheme);
      if (type == 'chartLeft') {
        this.leftOption = options;
      } else {
        this.rightOption = options;
      }
      this.setOptions(type, options);
    },
    async changeComboxChart (type, yData) {
      let options = this[type].getOption();
      options.series = yData;
      options.legend[0].data = this.comboxChartLegend;
      options.xAxis[0].data = this.xData;
      options.yAxis[0].name = type == 'chartLeft' ? '实时功率（kW）' : '总电流（A）';
      this.changeTheme(options, this.navTheme);
      if (type == 'chartLeft') {
        this.leftOption = options;
      } else {
        this.rightOption = options;
      }

      this.setOptions(type, options);
    },
    async refreshList () {
      this.pageloading = true;
      let res = await getInverterList({
        ...this.queryParams,
        psId: this.psId
      });
      if (res.result_code !== '1' || !res) {
        this.$message.error(res.result_msg);
        return;
      }
      this.listData = res.result_data.pageList;
      this.chartLegend = this.listData.map(item => item.name);
      this.total = res.result_data.rowCount;
      this.refreshChart();
    },

    async refreshComboxList () {
      this.pageloading = true;
      let res = await getCombinerBoxList({
        ...this.queryParams,
        psId: this.psId
      });
      if (res.result_code !== '1') {
        this.$message.error(res.result_msg);
        return;
      }
      this.comboxListData = res.result_data.pageList;
      this.comboxChartLegend = this.comboxListData.map(item => item.name);
      this.total = res.result_data.rowCount;
      this.refreshComboxChart();
    },
    setOptions (type, options) {
      this[type] && this[type].clear();
      this.resizeHandler(type);
      if (this[type]) {
        this[type].setOption(options, true);
      }
    }
  }
};
</script>

<style lang="less">
  .inverter-list-search-header {
    height: 68px;
    box-shadow: 0 2px 4px 0 rgba(0, 0, 0, 0.06);
    margin: 16px 0 0;
    padding: 0 24px;
    box-sizing: border-box;
    border-radius: 6px;
    display: flex;
    justify-content: flex-start;
    align-items: center;

    .search-header-left {
      position: relative;
      display: flex;
      justify-content: flex-start;
      align-items: center;
      .search-item-label{
        margin-left: 24px;
        margin-right: 16px;
      }
      .search-select {
        position: relative;
        width: 260px;
        height: 32px;
      }

      .type-select, .search-time-picker {
        width: 166px;
        margin-left: 24px;
      }
      .search-input {
        width: 200px;
        height: 36px;
      }
    }
  }

  .inverter-list-chart-area {

    margin-top: 16px;
    padding: 24px;
    background: #FFFFFF;
    box-shadow: 0 2px 4px 0 rgba(0, 0, 0, 0.06);
    box-sizing: border-box;
    border-radius: 6px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 49.8%;
    .monitor-inverter-list-chart-area-chart {
      height: 250px;
      width: 100%;
    }
  }
  .button-page {
    text-align: right;
    width: 100%;
    .exportBtn {
      display: inline;
      position: relative;
      border: none ;
      color:#1890FF;
      background: none !important;
      top: -6px;
      right: 16px;
    }
  }
</style>
