<template>
  <div ref="pdf-clean" id="pdf-clean" class="report-clean" :class="{'report-clean-hidden':hidden}">
    <div class="page-one-part">
      <div class="page-one">
        <div class="page-one-left">
          <div class="page-one-left-top">
            <img fit="cover" :src="require('@/assets/images/health/cleanAnalysisReport/image-left-001.png')" />
          </div>
          <div class="page-one-left-bottom">
            <img fit="cover" :src="require('@/assets/images/health/cleanAnalysisReport/image-left-002.png')" />
          </div>
        </div>
        <div class="page-one-right">
          <div class="sub-image">
            <img :src="require('@/assets/images/health/cleanAnalysisReport/image001.png')" style="width: 218px;object-fit: contain;" />
            <img :src="require('@/assets/images/health/cleanAnalysisReport/image002.png')" style="width: 314px;object-fit: contain;" />
          </div>
          <div class="main-image">
            <img :src="require('@/assets/images/health/cleanAnalysisReport/image003.png')" style="width: 576px;object-fit: contain;" />
          </div>
          <div>
            <div class="page-one-title">光伏电站清洁度评估报告</div>
            <a-divider class="main-divider"></a-divider>
            <div class="page-one-name">{{psName}}</div>
          </div>
        </div>
      </div>
    </div>
    <div class="part-conclusion">
      <h2>1 结论</h2>
      <div class="report-date">
        <div>&nbsp;报告日期&nbsp;</div>
        <div class="data-value-blue">{{conclusion.reportDay}}</div>
      </div>
      <div class="conclusion-main">
        <div class="conclusion-data">
          <a-descriptions bordered :column="1">
            <a-descriptions-item v-for="data in conclusion.data" :key="data.key">
              <template slot="label">
                <span>{{data.label}}</span>
              </template>
              <span class="data-value-blue" :class="{'font-bold':data.key == 'cleanSuggestion'}">{{formatter(data.value, '--')}}</span>
              <span v-if="(data.value || data.value ==0) && data.unit">[{{data.unit}}]</span>
            </a-descriptions-item>
          </a-descriptions>
        </div>
      </div>
    </div>
    <div class="part-detail">
      <h2>2 评估详情</h2>
      <div class="report-date"><span>&nbsp;报告日期&nbsp;</span><span class="data-value-blue">{{conclusion.reportDay}}</span></div>
      <div class="part-detail-main">
        <div class="part-detail-data">
          <a-descriptions layout="vertical" bordered>
            <a-descriptions-item v-for="(data,index) in detailData" :key="index">
              <template slot="label">
                <span class="table-header-font">{{data.title}}</span>
                <span v-if="data.unit" class="table-header-font header-no-wrap">
                  [{{data.unit}}]</span>
              </template>

              <span class="data-value-blue">{{data.value}}</span>
            </a-descriptions-item>
          </a-descriptions>
        </div>
      </div>
      <!-- <div class="part-detail-note">
        <h3 class="text-line-height calculate-note">计算说明：</h3>
        <div>
          <a-row class="text-line-height sub-title">【1】当日灰尘损失度</a-row>
          <a-row class="text-line-height">对比标准组串和对比组串的发电数据，结合辐照值和天气情况综合计算。</a-row>
        </div>
        <div>
          <a-row class="text-line-height sub-title">【2】累计损失电量</a-row>
          <a-row class="text-line-height">累计损失电量=∑ (当日实发电量*当日灰尘损失度)/(1-当日灰尘损失度)</a-row>
          <a-row class="text-line-height">（计算近30日）</a-row>
        </div>
        <div>
          <a-row class="text-line-height sub-title">【3】平均系统效率</a-row>
          <a-row class="text-line-height">近30日平均系统效率</a-row>
        </div>
        <div>
          <a-row class="text-line-height sub-title">【4】平均灰尘损失度</a-row>
          <a-row class="text-line-height">近15日平均灰尘损失度</a-row>
        </div>
        <div>
          <a-row class="text-line-height sub-title">【5】典型年辐照值</a-row>
          <a-row class="text-line-height">取未来30日累计每日倾斜面辐照值数据</a-row>
          <a-row class="text-line-height">（气象数据信息来源：Meteonorm7.1.3版本）</a-row>
        </div>
        <div>
          <a-row class="text-line-height sub-title">【6】清洗经济性分析</a-row>
          <a-row class="text-line-height"><span class="font-bold">电站清洗总成本</span>=清洗单价*电站容量</a-row>
          <a-row class="text-line-height"><span class="font-bold">电站清洗总收益</span>=平均灰尘损失度*典型年辐照值*平均系统效率*电站容量*售电电价/3.6</a-row>
          <a-row class="text-line-height"><span class="font-bold">电站清洗净收益</span>=电站清洗总收益-电站清洗总成本</a-row>
        </div>
      </div> -->
    </div>
    <div class="part-annex">
      <h2>3 附件</h2>
      <div v-for="(annex,index) of annexData" :key="index">
        <div class="text-line-height sub-title">{{annex.title}}</div>
        <div class="part-annex-data">
          <a-table :data-source="annex.data" :pagination="false" bordered :row-key="(r,i)=>(i.toString())" size="small"
            class="annex-table" :header-cell-class-name="function({columnIndex}){return annexHeaderColClass(columnIndex,annex.columnList.length)}"
            :cell-class-name="function({rowIndex,columnIndex}){return annexCellClass(rowIndex,annex.data.length,columnIndex,annex.columnList.length)}">
            <a-table-column v-for="(col, index) in annex.columnList" :key="index" :prop="col.key" :title="col.title"
              align="center" scoped-slot>
              <template slot="title">
                <span class="table-header-font">{{col.title.split('[')[0]}}</span>
                <span v-if="col.title.split('[').length > 1" class="table-header-font header-no-wrap">
                  {{'[' + col.title.split('[')[1]}}</span>
              </template>
              <template slot-scope="text,record">
                <span>{{formatter(record[col.key], '--')}}</span>
              </template>
            </a-table-column>
          </a-table>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import moment from 'moment';
import { getPdf } from '@/utils/htmlToPdf.js';
import {
  getFuture15DaysWeatherByPsId,
  getCleanReportInfo
} from '@/api/health/healthapi.js';
console.log(getPdf);
export default {
  props: {
    loadingReport: {
      type: Boolean,
      default: false
    },
    psId: {
      type: [String, Number]
      // default: '107353'
    }
  },
  watch: {
    exportBtn (val, oldVal) {
      if (oldVal && !val) {
        this.resetAllData();
      }
      this.$emit('update:loadingReport', val);
    },
    'dataLoading.length': {
      handler (val, oldVal) {
        this.$emit('update:loadingReport', val > 0);
      },
      immediate: true
    }
  },

  data () {
    return {
      hidden: true,
      exportBtn: false,
      dataLoading: [],
      psName: '',
      conclusion: {
        reportDay: '',
        data: []
      },
      detailData: [],
      annexData: []
    };
  },
  mounted () {
    // this.hidden = false
    // this.getData()
  },
  methods: {
    exportPdf () {
      if (!this.psId) {
        this.$message.error('请选择一个电站');
        return;
      }
      // 强制滚动到顶部 防止打印出来的pdf的上边距多出来一部分
      document.scrollingElement.scrollTop = 0;
      this.hidden = false;
      this.getData(() => {
        this.$nextTick(() => {
          getPdf(this, '清洁度评估报告', 'pdf-clean').then(() => {
            this.hidden = true;
          });
        });
      }, true);
    },
    getData (completion, isButton = false) {
      this.enLoading(3);
      this.requstData().then(({
        mainData,
        weatherData
      }) => {
        // 编辑详情数据
        this.editConclustion(mainData);
        this.editDetailData(mainData);
        this.editAnnexData(mainData, weatherData);
        completion && completion();
      })
        .catch(({
          res1,
          res2
        }) => {
          if (isButton) {
            res1 && res1.result_code != 1 && res1.result_msg && this.$message.error(res1.result_msg);
            setTimeout(() => {
              res2 && res2.result_code != 1 && res2.result_msg && this.$message.error(res2.result_msg);
            }, 500);
          }
          this.disLoading(3);
        });
    },
    formatter (val, placeholder = '/') {
      if (!val && val !== 0) {
        return placeholder;
      }
      return val;
    },
    requstData () {
      let psId = this.psId;
      return new Promise((resolve, reject) => {
        Promise.all([getFuture15DaysWeatherByPsId({
          psId
        }),
        getCleanReportInfo({
          psId
        })
        ]).then(([res1, res2]) => {
          let mainData = null;
          let weatherData = null;
          if (res1.result_code == 1) {
            weatherData = { ...res1.result_data
            };
          }
          if (res2.result_code == 1) {
            mainData = { ...res2.result_data
            };
            this.psName = mainData.psName;
          }
          resolve({
            mainData,
            weatherData
          });
        }).catch(err => {
          reject(err);
        });
      });
    },
    editConclustion (data) {
      this.conclusion.data = [{
        label: '灰尘损失度',
        key: 'averageDustLossStr',
        unit: '',
        value: ''
      }, {
        label: '污染类型',
        key: 'contaminatedType',
        unit: '',
        value: ''
      }, {
        label: '电站清洗总成本',
        key: 'cleanCost',
        unit: '元，预估',
        value: ''
      }, {
        label: '电站清洗总收益',
        key: 'cleanIncome',
        unit: '元，预估',
        value: ''
      }, {
        label: '电站清洗净收益',
        key: 'cleanNetIncome',
        unit: '元，预估',
        value: ''
      }, {
        label: '清洗建议',
        key: 'cleanSuggestion',
        unit: '',
        value: ''
      }];
      if (data) {
        this.conclusion.reportDay = data.reportDay;
        // 映射数据
        for (let con of this.conclusion.data) {
          let val = data[con.key];
          if (con.key == 'dustLoss' && val && !isNaN(val) && String(val).indexOf('%') < 0) {
            val = `${(val * 100).toFixed(2)}%`;
          }
          con.value = val;
        }
      }
      this.disLoading();
    },
    // 评估详情
    editDetailData (data) {
      this.detailData = [{
        key: 'address',
        dataIndex: 'address',
        title: '电站地址',
        unit: '',
        value: ''
      },
      {
        key: 'capacity',
        dataIndex: 'capacity',
        title: '电站容量',
        unit: 'MW',
        value: ''
      },
      {
        key: 'psType',
        dataIndex: 'psType',
        title: '电站类型',
        unit: '',
        value: ''
      },
      {
        key: 'electricityPrice',
        dataIndex: 'electricityPrice',
        title: '售电电价',
        unit: '元/kWh',
        value: '',
        slots: {
          title: 'title'
        }
      },
      {
        key: 'unitPrice',
        dataIndex: 'unitPrice',
        title: '清洗单价',
        unit: '元/MW',
        value: '',
        slots: {
          title: 'title'
        }
      },
      {
        key: 'averageSystemRate',
        dataIndex: 'averageSystemRate',
        title: '平均系统效率',
        unit: '近30日',
        value: '',
        slots: {
          title: 'title'
        }
      },
      {
        key: 'averageDustLossStr',
        dataIndex: 'averageDustLossStr',
        title: '灰尘损失度',
        unit: '',
        value: '',
        slots: {
          title: 'title'
        }
      },
      {
        key: 'typicalYearIrradiation',
        dataIndex: 'typicalYearIrradiation',
        title: '典型年辐照值',
        unit: 'MJ/㎡，未来30日',
        value: '',
        slots: {
          title: 'title'
        }
      },
      {
        key: 'placeholder0',
        dataIndex: 'placeholder0',
        title: '/',
        unit: '',
        value: '/',
        slots: {
          title: 'title'
        }
      }
      ];
      // 映射数据
      for (let detail of this.detailData) {
        if (detail.key != 'placeholder0' && data) {
          detail.value = data[detail.key];
        }
      }
      this.disLoading();
    },
    editAnnexData (mainData, weatherData) {
      this.annexData = [{
        title: '【1】近15天灰尘损失度数据',
        data: [],
        columnList: [{
          key: 'timeDateStr',
          dataIndex: 'timeDateStr',
          title: '日期'
        }, {
          key: 'contrastSeries',
          dataIndex: 'contrastSeries',
          title: '对比组串发电量[kWh]'
        }, {
          key: 'standardSeries',
          dataIndex: 'standardSeries',
          title: '标准组串发电量[kWh]'
        }, {
          key: 'dustLossStr',
          dataIndex: 'dustLossStr',
          title: '当日灰尘损失度'
        }, {
          key: 'weather',
          dataIndex: 'weather',
          title: '天气'
        }]
      },
      // {
      //   title: '【2】未来30日典型年日倾斜面辐照值',
      //   data: [],
      //   columnList: [{
      //     key: 'timeDateStr',
      //     dataIndex: 'timeDateStr',
      //     title: '日期',
      //   }, {
      //     key: 'typicalIrradiation',
      //     dataIndex: 'typicalIrradiation',
      //     title: '每日辐照值[kWh/㎡]',
      //   }, {
      //     key: 'timeDateStr1',
      //     dataIndex: 'timeDateStr1',
      //     title: '日期',
      //   }, {
      //     key: 'typicalIrradiation1',
      //     dataIndex: 'typicalIrradiation1',
      //     title: '每日辐照值[kWh/㎡]',
      //   }]
      // },
      {
        title: '【2】未来15日天气预报',
        data: [],
        columnList: [{
          key: 'predictDate',
          dataIndex: 'predictDate',
          title: '日期'
        }, {
          key: 'tempDay',
          dataIndex: 'tempDay',
          title: '气温[℃]'
        }, {
          key: 'conditionDay',
          dataIndex: 'conditionDay',
          title: '天气'
        }]
      }
      ];
      if (mainData) {
        this.annexData[0].data = mainData.dustLossInfo;
        // eslint-disable-next-line no-unused-vars
        let tiInfo = mainData.typicalIrradiationInfo;
        // eslint-disable-next-line no-unused-vars
        const editTiInfo = (tiInfo) => {
          let index = false;
          let res = [];
          let newItm = null;
          for (let itm of tiInfo) {
            if (!index) {
              newItm = {
                timeDateStr: itm.timeDateStr,
                typicalIrradiation: itm.typicalIrradiation
              };
              res.push(newItm);
              index = true;
            } else {
              newItm.timeDateStr1 = itm.timeDateStr;
              newItm.typicalIrradiation1 = itm.typicalIrradiation;
              index = false;
            }
          }
          return res;
        };
        // this.annexData[1].data = this.annexData[1].data.concat(editTiInfo(tiInfo))
      }
      if (weatherData) {
        this.annexData[1].data = this.annexData[1].data.concat(weatherData.forecast.filter((item) => {
          return moment(item.predictDate).isSameOrAfter(new Date(), 'day');
        }));
      }
      this.disLoading();
    },
    enLoading (count = 1) {
      for (let i = 0; i < count; i++) {
        setTimeout(() => {
          this.dataLoading.push(1);
        }, 1);
      }
    },
    disLoading (count = 1) {
      for (let i = 0; i < count; i++) {
        setTimeout(() => {
          if (this.dataLoading.length) {
            this.dataLoading.pop();
          }
        }, 1);
      }
    },
    resetAllData () {
      Object.assign(this, {
        dataLoading: [],
        psName: '',
        conclusion: {
          reportDay: '',
          data: []
        },
        detailData: [],
        annexData: []
      });
    },
    detailHeaderColClass (columnIndex, columnCount) {
      if (columnIndex < columnCount - 1) {
        return 'annex-table-header';
      } else {
        return 'annex-table-header-last';
      }
    },
    detailCellClass (rowIndex, rowCount, columnIndex, columnCount) {
      let classArr = ['annex-table-cell'];
      if (columnIndex < columnCount - 1) {
        classArr.push('annex-table-right-border');
      }
      if (rowIndex < rowCount - 1) {
        classArr.push('annex-table-bottom-border');
      }
      return classArr.join(' ');
    },
    annexHeaderColClass (columnIndex, columnCount) {
      if (columnIndex < columnCount - 1) {
        return 'annex-table-header';
      } else {
        return 'annex-table-header-last';
      }
    },
    annexCellClass (rowIndex, rowCount, columnIndex, columnCount) {
      let classArr = ['annex-table-cell'];
      if (columnIndex < columnCount - 1) {
        classArr.push('annex-table-right-border');
      }
      if (rowIndex < rowCount - 1) {
        classArr.push('annex-table-bottom-border');
      }
      return classArr.join(' ');
    }
  }
};
</script>
<style scoped lang="less">
  @standardWidth: 595.28*1.4px;
  @standardHeight: 841.89*1.4px;
  @standardFont: 18px;
  @labelColor: rgba(208, 206, 206, 1.0);
  .report-clean-hidden {
    display: none;
  }
  .report-clean {
    // overflow-y: auto;
    width: @standardWidth;
    font-size: @standardFont;
    line-height: 100%;

    .form-label {
      padding: 10px 0;
      border-right: solid 1px;
      background-color: @labelColor;
    }
    .form-label-row {
      border-bottom: solid 1px;
    }

    .form-data {
      padding: 10px 0;
    }

    .form-data-row {
      border-right: solid 1px;
    }

    .font-bold {
      font-weight: bold;
    }

    .data-value-blue {
      color: #00B0F0;
    }

    .detail-data-cell {
      border-bottom: solid 1px #000;
    }

    .text-line-height {
      line-height: 200%;
    }

    .report-date {
      margin-bottom: 2%;
      font-weight: 550;
      display: flex;
      width: 25%;
      align-items: center;
    }

    :deep(.table-header-font) {
      font-size: 120%;
    }

   :deep(.header-no-wrap) {
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }

    .page-one-part {
      height: @standardHeight;
    }

    .page-one {
      padding: 0 5%;
      padding-top: 8%;
      display: flex;

      .page-one-left {
        height: 100%;
        padding-bottom: 20%;
        display: flex;
        flex-direction: column;

        .page-one-left-top {
          height: 80%;
          margin-bottom: 40%;
        }

        .page-one-left-bottom {
          height: 10%;
        }
      }

      .page-one-right {
        padding: 3% 10%;
        padding-bottom: 20%;
        display: flex;
        flex-direction: column;
        justify-content: space-between;

        .page-one-title {
          font-size: 28px;
          font-weight: bolder;
          text-align: right;
        }

        .main-divider {
          background-color: rgba(0, 0, 0, 0.5);
          margin: 12px 0;
        }

        .page-one-name {
          font-size: 22px;
          text-align: right;
          color: #00B0F0;
          font-weight: bold
        }

        .main-image {}

        .sub-image {
          width: 85%;
          display: flex;
          justify-content: space-evenly;
          flex-wrap: nowrap;
        }
      }
    }

    .part-conclusion {
      padding: 0 5%;
      padding-top: 5%;
      text-align: left;

      .conclusion-main {
        display: flex;
        justify-content: center;
      }

      .conclusion-data {
        text-align: center;
        width: 70%;
      }
    }

    .part-detail {
      padding: 3% 5%;
      // padding-top: 5%;
      text-align: left;

      .part-detail-main {
        display: flex;
        justify-content: center;
      }

      .part-detail-data {
        text-align: center;
        width: 70%;
        margin-bottom: 5%;
        // border: solid 1px;
      }

      .detail-table {
        border-right: solid 1px #000;
        border-bottom: solid 1px #000;
      }

      .part-detail-note {
        .calculate-note {
          margin-top: 0;
          margin-bottom: 0;
        }

        .sub-title {
          font-size: 108%;
          font-weight: 600;
        }
      }
    }

    .part-annex {
      padding: 0 5%;
      text-align: left;

      .part-annex-data {
        width: 100%;
      }

      .annex-table {
        width: 81%;
        margin: 2% 9%;
        // border: solid 1px #000 !important;
      }
    }

    :deep(.annex-table-header) {
      background: @labelColor  !important;
      border-right: solid 1px #000 !important;
      border-bottom: solid 1px #000 !important;
    }

    :deep(.annex-table-header-last) {
      background: @labelColor  !important;
      border-bottom: solid 1px #000 !important;
    }

    :deep(.annex-table-cell) {
      font-size: 130%;
      border: none !important;
    }

    :deep(.annex-table-right-border) {
      border-right: solid 1px #000 !important;
    }

    :deep(.annex-table-bottom-border) {
      border-bottom: solid 1px #000 !important;
    }
  }
</style>
