<template>
  <div class="double-lines" ref="doubleLines"></div>
</template>
<script>
// 引入基本模板
const echarts = require('echarts');
export default {
  props: {
    // 区分
    type: {
      type: Number,
      default: null
    }
  },
  data () {
    return {
      doubleLines: null
    };
  },
  methods: {
    initLines (data) {
      data = this.formatData(data);
      let x_data = (data.hasOwnProperty('x') ? data.x : []);
      let legend = []; let lines = [];
      if (this.type == '0') {
        legend = ['任务完成率', '消缺完成率'];
        let plan = (data.hasOwnProperty('plan') ? data.plan : '');
        let defect = (data.hasOwnProperty('defect') ? data.defect : '');
        lines.push(plan);
        lines.push(defect);
      } else {
        legend = ['功率', '辐照度'];
        let pow = (data.hasOwnProperty('pow') ? data.pow : '');
        let irr = (data.hasOwnProperty('irr') ? data.irr : '');
        lines.push(pow);
        lines.push(irr);
      }
      if (!this.$refs.doubleLines) { // dom 不存在时不画
        return;
      }
      // 不重复初始化
      let doubleLines = echarts.getInstanceByDom(this.$refs.doubleLines);
      if (!doubleLines) {
        doubleLines = echarts.init(this.$refs.doubleLines);
        this.$once('hook:beforeDestroy', function () {
          echarts.dispose(doubleLines);
        });
      }
      let option = {
        color: ['rgba(10, 162, 231, 1)', 'rgba(225, 131, 54, 1)'],
        title: {
          text: (this.type == '0' ? '任务/消缺完成率' : '电站运行概览'),
          top: '0',
          left: '0',
          textStyle: {
            fontSize: 16,
            color: 'rgba(255, 255, 255, 0.8)'
          }
        },
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow',
            shadowStyle: {
              color: 'rgba(150,150,150,0.1)'
            }
          },
          backgroundColor: 'rgba(55, 71, 89, 0.9)',
          borderColor: '#69C7EB',
          borderWidth: '1',
          textStyle: {
            color: '#ffffff'
          }
        },
        grid: {
          x: 10,
          y: 60,
          x2: (this.type == '0' ? 0 : 15),
          y2: 10,
          containLabel: true
        },
        legend: {
          // icon: 'roundRect',
          top: '0',
          right: '0',
          textStyle: {
            textAlign: 'center',
            fontSize: 14,
            color: 'rgba(255,255,255,0.7)'
          },
          data: legend
        },
        xAxis: [{
          type: 'category',
          data: x_data,
          axisPointer: {
            type: 'shadow'
          },
          axisLine: {
            show: false,
            lineStyle: {
              color: 'rgba(255, 255, 255, 0.7)',
              fontSize: 12
            }
          },
          axisTick: false
        }],
        yAxis: this.getYAxis(lines),
        dataZoom: [{
          type: 'inside',
          show: false
        }],
        series: this.getSeries(lines)
      };
      doubleLines.setOption(option, true);
    },
    formatData (data) {
      if (data) {
        let obj = {
          irr: {
            unit: 'W/㎡',
            value: data.radiationP2003.map(item => { return item.dataY; })
          },
          pow: {
            unit: data.currentPowerUnit,
            value: data.currentPower.map(item => { return item.dataY; })
          },
          x: [...this.getTimeList(24, 5), '00:00']
        };
        data = obj;
      }
      return data;
    },
    getTimeList (hours, step) {
      var minutes = 60;
      var timeArr = [];
      for (var i = 0; i < hours; i++) {
        var str = '';
        if (i < 10) {
          str = 0 + '' + i;
        } else {
          str = '' + i;
        }

        for (var j = 0; j < minutes; j++) {
          if (j % step == 0) {
            var s = j < 10 ? ':' + 0 + '' + j : ':' + j;
            s = str + s;
            timeArr.push(s);
          }
        }
      }
      return timeArr;
    },
    // 柱状图和折线
    getSeries (lines) {
      let series = [{
        name: (this.type == '0' ? '任务完成率' : '功率'),
        type: 'line',
        smooth: true,
        symbol: 'circle',
        symbolSize: 9,
        z: 3,
        areaStyle: {
          normal: {
            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [{
              offset: 1,
              color: 'rgba(89, 206, 255, 0)' // 0% 处的颜色
            }, {
              offset: 0.3,
              color: 'rgba(82, 187, 233, 0)' // 0% 处的颜色
            }, {
              offset: 0,
              color: 'rgba(86, 178, 186, 0.6)' // 100% 处的颜色
            }], false)
          }
        },
        showSymbol: false,
        connectNulls: true,
        data: (this.type == '0' ? lines[0] : (lines[0].value ? lines[0].value : []))
      }];
      // 日统计图时-会有趋势
      if (this.type == '0') {
        series.push({
          name: '消缺完成率',
          type: 'line',
          smooth: true,
          symbol: 'circle',
          symbolSize: 9,
          z: 9,
          areaStyle: {
            normal: {
              color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [{
                offset: 1,
                color: 'rgba(250, 206, 21, 0)'
              },
              {
                offset: 0,
                color: 'rgba(250, 206, 21, 0.2)'
              }
              ], false)
            }
          },
          showSymbol: false,
          connectNulls: true,
          data: lines[1]
        });
      } else {
        series.push({
          name: '辐照度',
          type: 'line',
          yAxisIndex: 1,
          connectNulls: true,
          smooth: true,
          symbolSize: 9,
          symbol: 'circle',
          z: 9,
          areaStyle: {
            normal: {
              color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [{
                offset: 1,
                color: 'rgba(250, 206, 21, 0)'
              },
              {
                offset: 0,
                color: 'rgba(250, 206, 21, 0.2)'
              }
              ], false)
            }
          },
          showSymbol: false,
          data: (lines[1].value ? lines[1].value : [])
        });
      }
      return series;
    },
    // 是否双坐标
    getYAxis (lines) {
      let yAxis = [{
        type: 'value',
        name: (this.type == '0' ? '%' : (lines[0].unit ? lines[0].unit : 'MW')),
        splitLine: {
          show: true,
          lineStyle: {
            color: 'rgba(96, 130, 163, 0.19)'
          }
        },
        axisLine: {
          show: false,
          lineStyle: {
            color: 'rgba(255, 255, 255, 0.7)',
            fontSize: 12
          }
        },
        minInterval: 1,
        min: function (value) {
          if (!isFinite(value.min)) {
            return 0;
          }
        },
        max: function (value) {
          if (!isFinite(value.max)) {
            return 100;
          }
          if (value.max < 1) {
            return 1;
          }
        },
        axisTick: false
      }];
      if (this.type != '0') {
        yAxis.push({
          type: 'value',
          name: (lines[1].unit ? lines[1].unit : 'W/m2'),
          yAxisIndex: 1,
          splitLine: {
            show: false
          },
          axisLine: {
            show: false,
            lineStyle: {
              color: 'rgba(255, 255, 255, 0.7)',
              fontSize: 12
            }
          },
          minInterval: 1,
          min: function (value) {
            if (!isFinite(value.min)) {
              return 0;
            }
          },
          max: function (value) {
            if (!isFinite(value.max)) {
              return 100;
            }
            if (value.max < 1) {
              return 1;
            }
          },
          axisTick: false
        });
      }
      return yAxis;
    }
  }
};
</script>

<style lang="less" scoped>
  .double-lines {
    width: 100%;
    height: 100%;
  }
</style>
