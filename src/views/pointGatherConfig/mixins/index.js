/**
 * 新增修改完成调用 drawerFormOk方法 编辑弹框组件ref定义为drawerForm
 * 高级查询按钮调用 superQuery方法  高级查询组件ref定义为superQueryModal
 * data中url定义 list为查询列表  delete为删除单条记录  deleteBatch为批量删除
 */
import { postAction } from '@/api/manage';
import { baseUrl } from '@/api/dataCenter';
import download from '@/utils/download';
import {
  getAccessPointList,
  getAccessSnList,
  deleteAccessPoint,
  getAccessDeviceList,
  getConfigPointList,
  getAccessSelfCheckList,
  updatePointAccessStatus,
  getConfigPointListForCollect,
  getAccessHeaderStatistics,
  // submitConfigPoint,
  // relocationConfigPoint,
  getDictByListForR,
  configPointListByStrategy,
  configPointParam
} from '@/api/health/getPointConfig.js';

const innerHeight = window.innerHeight - 60 - 40 - 24;
export const listMixin = {
  data () {
    return {
      pageSizeOptions: [5, 10, 15, 20],
      // loading: false,
      total: '',
      loadingConfig: true,
      // 字典接口请求参数
      queryDictParams: {
        firstTypeCodeList: [
          '0213',
          '0240',
          '0241',
          '0243'
        ]
      },
      sourceData: [], // 接入来源数据源
      accessStatusData: [], // 接入状态数据源
      pointConfigData: [], // 接入状态数据源
      deviceTypeData: [], // 设备类型数据源
      // tab表头统计请求参数
      queryHeaderParams: {
        sn: ''
      },
      tabHeaderData: {},
      isShowBoundDeviceHome: true,
      // 导出参数
      exportParams: {
        source: '', // 接入来源
        sn: '', // 接入SN
        accessStatus: '', // 接入状态
        accessTimeType: '' // 接入时间
      },
      // sn列表查询参数
      queryParamsHome: {
        currentPage: 1,
        pageSize: 10,
        source: '', // 接入来源
        sn: '', // 接入SN
        accessStatus: '', // 接入状态
        accessTimeType: '', // 接入时间
        sortFiled: '',
        sortKind: ''
      },
      homeTotal: '',
      dataList: [],
      // tab1  列表查询请求参数
      queryPointParams: {
        currentPage: 1,
        pageSize: 10,
        pointType: '', // 测点类型
        configurationStatus: '', // 配置状态
        deviceCode: '', // 接入设备编号
        receiveStationType: '', // 接入子站类型
        snList: [],
        sortFiled: '',
        sortKind: ''
      },
      dataPointList: [], // tab1表格数据
      totalPoint: '',
      originalPointKeyList: [],
      height: '',
      isShowDelBtn: true, // 是否禁用批量删除按钮
      isShowDisableBtn: true, // 是否禁用批量禁用按钮
      isShowEnableBtn: true, // 是否禁用批量启用按钮
      // tab2  列表查询请求参数
      queryDeviceParams: {
        snList: [],
        deviceCode: '',
        receiveStationName: '',
        receiveStationType: '',
        bindingStatus: '',
        ids: [],
        currentPage: 1,
        pageSize: 10,
        sortFiled: '',
        sortKind: ''
      },
      dataDeviceList: [], // tab2表格数据
      totalDevice: '',
      isShowBoundDeviceBtn: true, // 是否禁用tab2绑定设备按钮
      // tab2-测点绑定设备-电站设备列表查询参数
      boundDeviceParams: {
        currentPage: 1,
        pageSize: 5000,
        psId: '',
        deviceType: '',
        deviceName: ''
      },
      dataBoundDeviceList: [],
      totalBoundDeviceList: '',
      // tab3  列表查询请求参数
      queryConfigParams: {
        snList: [],
        psId: '',
        currentPage: 1,
        pageSize: 10,
        sortFiled: '',
        sortKind: '',
        psName: '',
        deviceType: ''
      },
      dataConfigList: [], // tab3表格数据
      totalConfig: '',
      isShowNumberCodeBtn: true, // 是否禁用编号匹配编码按钮
      isShownNameCodeBtn: true, // 是否禁用名称匹配编码按钮
      isShownConfigPointBtn: true, // 是否禁用匹配测点编码按钮
      // tab3编码绑定请求参数
      queryCodeBoundParams: {
        // pointType: 2,
        originalPsKeyList: [],
        bindingStrategy: ''
      },
      // 直采测点表查询参数
      collectParams: {
        originalPsKeyList: [],
        pointType: '2', // 测点类型 遥信1 遥测2(默认)
        pointName: '', // 测点名称
        currentPage: 1,
        pageSize: 10
      },
      dataCollectList: [], // 直采测点表表格数据
      totalCollect: '',
      // 标准测点表查询参数
      standardParams: {
        deviceType: 1, // 设备类型
        pointName: '',
        currentPage: 1,
        pageSize: 5000,
        pointType: '2'
      },
      standardLoading: false,
      dataStandardList: [], // 标准测点表表格数据
      totalStandard: '',
      // tab4  列表查询请求参数
      queryCheckParams: {
        snList: [],
        psId: '',
        currentPage: 1,
        pageSize: 10,
        pointType: '',
        sortFiled: '',
        sortKind: '',
        psName: '',
        deviceType: ''
      },
      dataCheckList: [], // tab4表格数据
      totalCheck: '',
      // tab4 重新配置/编辑请求参数
      configParamsTab4: {
        pointMappingIdList: [],
        originalPsKeyList: [],
        coefficient: '',
        reverse: '',
        excursion: ''
      },
      isShowEditMore: true, // 是否禁用tab4批量编辑按钮
      isShowSubmit: true, // 是否禁用tab4提交按钮
      isShowReconfigure: true, // 是否禁用tab4重新配置按钮
      // tab4提交按钮请求参数
      // queryCheckSubmitParmas: {
      //   originalPointKeyList: []
      // },
      queryCheckSubmitParmas: {
        snList: []
      },
      /* table加载状态 */
      /* table选中keys */
      selectedRowKeys: [], // tab1选中数据
      selectedRowKeysTab2: [], // tab2选中数据
      selectedRowKeysTab3: [], // tab3选中数据
      selectedRowKeysTab4: [], // tab4选中数据
      selectedRowKeysTab4Id: [], // tab4选中数据
      /* table选中records */
      selectionRows: [],
      exportLoading: false,
      // tab1编辑传参
      editParamsTab1: {
        originalPointKeyList: [],
        accessStatus: '',
        snList: []
      },
      editPointRow: null,
      editCheckRow: null,
      headerLoading: true,
      selectedRowsTab3: []
    };
  },
  created () {

  },
  mounted () {
  },
  computed: {
    scroll: function () {
      var width = window.innerWidth;
      let $antTable = window.document.getElementsByClassName('ant-row');
      if ($antTable[0]) {
        width = $antTable[0].clientWidth;
      }
      return {
        // x:'max-content',
        x: width,
        y: window.innerHeight / 2
      };
    },
    innerHeight () {
      return innerHeight;
    }
  },
  methods: {
    // 获取字典
    getDictList () {
      getDictByListForR(this.queryDictParams).then(res => {
        if (res.result_code == '1') {
        // sn接入来源: 0213
        // sn接入状态：0240
        // 测点配置状态：0241
        // 设备类型: 0243
          let arr = Object.keys(res.result_data).map(key => res.result_data[key]);
          this.sourceData = arr[0];
          this.deviceTypeData = arr[1];
          this.accessStatusData = arr[2];
          this.pointConfigData = arr[3];
        }
      }).catch(() => {
      });
    },
    //  tab表头统计
    getHeaderList () {
      this.headerLoading = true;
      getAccessHeaderStatistics(this.queryHeaderParams).then(res => {
        if (res.result_code == '1') {
          this.tabHeaderData = res.result_data;
        }
        this.headerLoading = false;
      }).catch(() => {
        this.headerLoading = false;
      });
    },
    // 获取SN列表数据
    getList (page) {
      this.loading = true;
      if (page) this.queryParamsHome.currentPage = page;
      this.selectedRowKeys = [];
      this.isShowBoundDeviceHome = true;
      getAccessSnList(this.queryParamsHome).then(res => {
        if (res.result_code == '1') {
          this.dataList = res.result_data.pageList;
          if (res.result_data) {
            this.homeTotal = res.result_data.rowCount;
          }
          this.loading = false;
        }
      }).catch(() => {
        this.loading = false;
      });
    },
    //  获取原始测点接收表列表数据--tab1
    getPointList () {
      this.loadingConfig = true;
      getAccessPointList(this.queryPointParams).then(res => {
        if (res.result_code == '1') {
          this.dataPointList = res.result_data.pageList;
          this.dataPointList.map(
            (item, index) => {
              item.accessStatus = String(item.accessStatus);
              this.$set(this.dataPointList[index], 'edit', 0);
            }
          );
          if (res.result_data) {
            this.totalPoint = res.result_data.rowCount;
          }
          this.loadingConfig = false;
        }
      }).catch(() => {
        this.loadingConfig = false;
      });
    },
    // tab1 页面切换
    sizeChangePoint (current, size) {
      this.queryPointParams.pageSize = size;
      this.queryPointParams.currentPage = current;
      this.getPointList();
    },
    // tab1 表格内编辑事件
    // 编辑按钮
    editRow (index) {
      this.editPointRow = Object.assign({}, this.dataPointList[index]);
      this.dataPointList[index].isEdit = 1;
      this.$nextTick(() => {
        this.$refs.xTable.reloadData(this.dataPointList);
      });
    },
    // 编辑 -- 取消
    cancelRow (index) {
      if (this.editPointRow) {
        this.dataPointList[index] = this.editPointRow;
      }
      this.$nextTick(() => {
        this.$refs.xTable.reloadData(this.dataPointList);
        this.$forceUpdate();
      });
    },
    // 编辑 -- 保存
    saveRow (row) {
      this.editParamsTab1.originalPointKeyList[0] = row.originalPointKey;
      this.editParamsTab1.accessStatus = row.accessStatus;
      updatePointAccessStatus(this.editParamsTab1).then(res => {
        if (res.result_code == '1') {
          this.getPointList();
        }
        this.getHeaderList();
      }).catch(() => {
      });
    },

    // tab4 表格内编辑事件
    // 编辑按钮
    editRowEventTab4 (index) {
      this.editCheckRow = Object.assign({}, this.dataCheckList[index]);
      this.dataCheckList[index].isEdit = 1;
      this.$nextTick(() => {
        this.$refs.xTableTab4.reloadData(this.dataCheckList);
      });
    },
    // 编辑 -- 取消
    cancelRowEventTab4 (index) {
      if (this.editCheckRow) {
        this.dataCheckList[index] = this.editCheckRow;
      }
      this.$nextTick(() => {
        this.$refs.xTableTab4.reloadData(this.dataCheckList);
        this.$forceUpdate();
      });
    },
    // 编辑 -- 保存
    saveRowEventTab4 (row) {
      this.configParamsTab4.coefficient = row.coefficient;
      this.configParamsTab4.reverse = row.reverse;
      this.configParamsTab4.excursion = row.excursion;
      this.configParamsTab4.pointMappingIdList[0] = row.id;
      this.configParamsTab4.originalPsKeyList[0] = row.originalPsKey;
      configPointParam(this.configParamsTab4).then(res => {
        if (res.result_code == '1') {
          this.getCheckList();
        };
        that.getHeaderList();
      }).catch(() => {
      });
    },
    // 原始测点接收表--删除 type为1是单列删除,type为2时是批量删除
    delTab1 (row, type) {
      if (type === '1') {
        this.originalPointKeyList[0] = row.originalPointKey;
      } else if (type === '2') {
        this.originalPointKeyList = this.selectedRowKeys;
      }
      let that = this;
      this.$confirm({
        title: '提示',
        content: '删除不影响再次根据报文自动添加，确认删除？',
        okText: '确定',
        cancelText: '取消',
        onOk: function () {
          deleteAccessPoint({ originalPointKeyList: that.originalPointKeyList }).then(res => {
            if (res.result_code == '1') {
              that.$message.success('删除成功!');
            }
            that.getPointList();
            that.isShowDelBtn = true;
            that.getHeaderList();
          }).catch(() => {
          });
        },
        onCancel: function () {
          that.getPointList();
          that.isShowDelBtn = true;
          that.isShowDisableBtn = true;
          that.isShowEnableBtn = true;
        }
      });
    },
    // 原始测点接收表--批量禁用
    disableBtn (row, type) {
      this.editParamsTab1.accessStatus = 0;
      this.editParamsTab1.originalPointKeyList = this.selectedRowKeys;
      let that = this;
      this.$confirm({
        title: '提示',
        content: '禁用后测点数据不可用，是否对所选设备测点全部禁用？',
        okText: '确定',
        cancelText: '取消',
        onOk: function () {
          updatePointAccessStatus(that.editParamsTab1).then(res => {
            if (res.result_code == '1') {
              that.getPointList();
              that.isShowDelBtn = true;
              that.isShowDisableBtn = true;
              that.isShowEnableBtn = true;
            }
            that.getHeaderList();
          }).catch(() => {
          });
        },
        onCancel: function () {
          that.getPointList();
          that.isShowDelBtn = true;
          that.isShowDisableBtn = true;
          that.isShowEnableBtn = true;
        }
      });
    },
    // 原始测点接收表--批量启用
    enableBtn (row, type) {
      this.editParamsTab1.accessStatus = 1;
      this.editParamsTab1.originalPointKeyList = this.selectedRowKeys;
      let that = this;
      this.$confirm({
        title: '提示',
        content: '启用后测点数据会自动上传，是否对所选设备测点全部启用？',
        okText: '确定',
        cancelText: '取消',
        onOk: function () {
          updatePointAccessStatus(that.editParamsTab1).then(res => {
            if (res.result_code == '1') {
              that.getPointList();
              that.isShowDelBtn = true;
              that.isShowDisableBtn = true;
              that.isShowEnableBtn = true;
            }
            that.getHeaderList();
          }).catch(() => {
          });
        },
        onCancel: function () {
          that.getPointList();
          that.isShowDelBtn = true;
          that.isShowDisableBtn = true;
          that.isShowEnableBtn = true;
        }
      });
    },
    // 原始测点接收表--表格复选框变化事件tab1
    onSelectChangePoint (val) {
      let arr = [];
      val.records.forEach((item) => {
        arr.push(item.originalPointKey);
      });
      this.selectedRowKeys = arr;
      this.isShowDelBtn = this.selectedRowKeys.length === 0;
      this.isShowDisableBtn = this.selectedRowKeys.length === 0;
      this.isShowEnableBtn = this.selectedRowKeys.length === 0;
    },
    // tab2 复选框勾选
    onSelectChangPointBound (val) {
      let arr = [];
      val.records.forEach((item) => {
        arr.push(item);
      });
      this.selectedRowKeysTab2 = arr;
      this.isShowBoundDeviceBtn = this.selectedRowKeysTab2.length === 0;
    },
    // tab3 复选框勾选
    onSelectChangeCode (val) {
      let arr = [];
      let arrPsKey = [];
      val.records.forEach((item) => {
        arr.push(item.originalPsKey);
        arrPsKey.push(item.psKey);
      });
      this.selectedRowsTab3 = val.records;
      this.selectedRowKeysTab3 = arr;
      this.checkConfigPointParams.psKeyList = arrPsKey;
      this.isShowNumberCodeBtn = this.selectedRowKeysTab3.length === 0;
      this.isShownNameCodeBtn = this.selectedRowKeysTab3.length === 0;
      this.isShownConfigPointBtn = this.selectedRowKeysTab3.length === 0;
    },
    // tab3 -- 按接收测点编号匹配编码
    numberCode () {
      this.queryCodeBoundParams.originalPsKeyList = this.selectedRowKeysTab3;
      this.queryCodeBoundParams.bindingStrategy = 'code';
      let that = this;
      this.$confirm({
        title: '提示',
        content: '“接收测点编号”需按照标准测点编码上传，是否继续操作？',
        okText: '确定',
        cancelText: '取消',
        onOk () {
          return configPointListByStrategy(that.queryCodeBoundParams).then(res => {
            if (res.result_code == '1') {
              that.getConfigList();
              // that.isShowDelBtn = true;
              that.isShowNumberCodeBtn = true;
              that.isShownNameCodeBtn = true;
              that.isShownConfigPointBtn = true;
              that.$message.success('按照标准测点编码上传操作成功');
              that.getHeaderList();
            }
          }).catch(() => {
          });
        },
        onCancel: function () {
          // that.getConfigList();
          // that.isShowDelBtn = true;
          // that.isShowNumberCodeBtn = true;
          // that.isShownNameCodeBtn = true;
          // that.isShownConfigPointBtn = true;
        }
      });
    },
    // tab3 -- 按原测点名称匹配编码
    nameCode () {
      this.queryCodeBoundParams.originalPsKeyList = this.selectedRowKeysTab3;
      this.queryCodeBoundParams.bindingStrategy = 'name';
      let that = this;
      this.$confirm({
        title: '提示',
        content: '将自动查找标准测点中相同测点名称进行配置，是否继续？',
        okText: '确定',
        cancelText: '取消',
        onOk: function () {
          return configPointListByStrategy(that.queryCodeBoundParams).then(res => {
            if (res.result_code == '1') {
              that.getConfigList();
              // that.isShowDelBtn = true;
              that.isShowNumberCodeBtn = true;
              that.isShownNameCodeBtn = true;
              that.isShownConfigPointBtn = true;
              that.$message.success('按原测点名称匹配编码操作成功');
              that.getHeaderList();
            }
          }).catch(() => {
          });
        },
        onCancel: function () {
          // that.getConfigList();
          // that.isShowDelBtn = true;
          // that.isShowNumberCodeBtn = true;
          // that.isShownNameCodeBtn = true;
          // that.isShownConfigPointBtn = true;
        }
      });
    },
    // 表格复选框变化事件--tab4
    onSelectChangeTab4 (val) {
      let arr = [];
      let arrList = [];
      let arrTab4 = [];
      val.records.forEach((item) => {
        arr.push(item.originalPointKey);
        arrList.push(item.id);
        arrTab4.push(item);
      });
      this.selectedRowKeysTab4 = arr;
      this.selectedRowKeysTab4Id = arrList;
      this.isShowEditMore = this.selectedRowKeysTab4.length === 0;
      this.isShowSubmit = this.selectedRowKeysTab4.length === 0;
      // 根据条件判断,选中数据中不能存在配置完成,否则无法点击重新配置按钮
      this.isShowReconfigure = this.selectedRowKeysTab4.length === 0;
    },
    //  获取测点绑定设备列表数据--tab2
    getDeviceList () {
      this.loadingConfig = true;
      getAccessDeviceList(this.queryDeviceParams).then(res => {
        if (res.result_code == '1') {
          this.dataDeviceList = res.result_data.pageList;
          if (res.result_data) {
            this.totalDevice = res.result_data.rowCount;
          }
          this.loadingConfig = false;
        }
      }).catch(() => {
        this.loadingConfig = false;
      });
    },
    // tab2 页面切换
    sizeChangeDevice (current, size) {
      this.queryDeviceParams.pageSize = size;
      this.queryDeviceParams.currentPage = current;
      this.getDeviceList();
    },
    //  获取测点编码配置列表数据--tab3
    getConfigList (val) {
      this.loadingConfig = true;
      if (val) this.queryConfigParams.currentPage = 1;
      getConfigPointList(this.queryConfigParams).then(res => {
        if (res.result_code == '1') {
          this.dataConfigList = res.result_data.pageList;
          if (res.result_data) {
            this.totalConfig = res.result_data.rowCount;
          }
          this.loadingConfig = false;
        }
      }).catch(() => {
        this.loadingConfig = false;
      });
    },
    // tab3 页面切换
    sizeChangeConfig (current, size) {
      this.queryConfigParams.pageSize = size;
      this.queryConfigParams.currentPage = current;
      this.getConfigList();
    },
    // 直采测点列表数据
    getCollectList () {
      getConfigPointListForCollect(this.collectParams).then(res => {
        if (res.result_code == '1') {
          this.dataCollectList = res.result_data.pageList;
          if (res.result_data) {
            this.totalCollect = res.result_data.rowCount;
          }
        }
      }).catch(() => {
      });
    },
    // 直采测点表页面切换
    sizeChangeCollect (current, size) {
      let that = this;
      this.$confirm({
        title: '提示',
        content: '已配置的测点编码还未保存，切换分页会导致数据丢失，您确认切换吗？',
        okText: '确定',
        cancelText: '取消',
        onOk: function () {
          that.collectParams.pageSize = size;
          that.collectParams.currentPage = current;
          that.getCollectList();
        },
        onCancel: function () {
        }
      });
    },
    //  获取接入数据自检列表数据--tab4
    getCheckList (val) {
      this.loadingConfig = true;
      if (val) this.queryCheckParams.currentPage = 1;
      getAccessSelfCheckList(this.queryCheckParams).then(res => {
        if (res.result_code == '1') {
          res.result_data.pageList.map((item, index) => {
            item.edit = 2;
          });
          this.dataCheckList = res.result_data.pageList;
          if (res.result_data) {
            this.totalCheck = res.result_data.rowCount;
          }
          this.loadingConfig = false;
        }
      }).catch(() => {
        this.loadingConfig = false;
      });
    },
    // tab4 页面切换
    sizeChangeCheck (current, size) {
      this.queryCheckParams.pageSize = size;
      this.queryCheckParams.currentPage = current;
      this.getCheckList();
    },
    // tab4 编辑按钮接口
    configtab4 () {
      // this.queryCheckSubmitParmas.originalPointKeyList = this.selectedRowKeysTab4;
      configPointParam(this.configParamsTab4).then(res => {
        if (res.result_code == '1') {
          this.isShowEditMore = true;
          this.isShowSubmit = true;
          this.isShowReconfigure = true;
          this.getCheckList();
          this.$message.success('重新配置成功!');
        }
      }).catch(() => {
      });
    },
    loadData (arg) {
      this.$refs.multipleTable && this.$refs.multipleTable.clearScroll();
      if (!this.url.list) {
        this.$message.error('请设置url.list属性!');
        return;
      }
      // 加载数据 若传入参数1则加载第一页的内容
      if (arg === 1) {
        this.queryParams.currentPage = 1;
      }
      this.loading = true;
      postAction(baseUrl + this.url.list, this.queryParams).then((res) => {
        if (res.result_code == '1') {
          this.dataSource = res.result_data.pageList;
          if (res.result_data) {
            this.total = res.result_data.rowCount;
          }
        }
        this.loading = false;
      }).catch(() => {
        this.loading = false;
      });
    },
    onClearSelected () {
      this.selectedRowKeys = [];
      this.selectionRows = [];
    },
    searchQuery () {
      this.loadData(1);
    },
    handleDelete: function (id) {
      if (!this.url.delete) {
        this.$message.error('请设置url.delete属性!');
        return;
      }
      var that = this;
      postAction(baseUrl + that.url.delete, { id: id }).then((res) => {
        if (res.result_code === '1') {
          that.$message.success(res.message);
          that.loadData();
        } else {
          that.$message.warning(res.message);
        }
      });
    },
    handleAction (record, obj) {
      this.$refs.drawerForm.initDrawer(record, obj);
    },
    // 排序
    handleTableSortChange (val, params, type) {
      if (val.order == null) {
        params.sortFiled = '';
        params.sortKind = '';
      } else {
        params.sortFiled = val.property;
        params.sortKind = val.order;
      }
      switch (type) {
        case 'snList':
          this.getList(1);
          break;
        case 'pointList':
          this.searchPointData(1);
          break;
        case 'deviceList':
          this.searchDeviceData(1);
          break;
        case 'queryConfig':
          this.getConfigList(1);
          break;
        case 'queryCheck':
          this.getCheckList(1);
          break;
      }
    },
    /* 导出 */
    handleExport (list) {
      let params = list || this.getQueryParams();
      this.exportLoading = true;
      postAction(baseUrl + this.url.export, params).then((res) => {
        this.exportLoading = false;
        if (res.result_code === '1') {
          download(res.result_data);
        } else {
          this.$message.warning(res.result_msg);
        }
      }).catch(() => {
        this.exportLoading = false;
      });
    }
  }

};
