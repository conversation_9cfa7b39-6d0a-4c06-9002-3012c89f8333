<template>
  <div class="drawer-form-com unique-padding">
    <div class="drawer-form-content">
      <a-spin :spinning="loading">
        <template v-if="basics.reportType=='0'">
          <detail-layout :labelList="basicsDetail" :form="basics" title="基本信息" ></detail-layout>
        </template>
        <template v-else>
          <detail-layout :labelList="baseDetail" :form="basics" title="基本信息" >
            <template slot="top-title">
              <span class="is-do"> {{basics.flowStsName}}</span>
            </template>
            <template v-slot:psUuid>
              <a-col :span="8" class="detail_layout_content">
                <span class="left">票号</span>
                <span class="right">
                    {{basics.psUuid}}
                  <span style="color: #FF8900;">【{{basics.flowUser}}】</span>
                </span>
              </a-col>
            </template>
          </detail-layout>

          <template v-if="!['noReport', 'toReport', 'canceled'].includes(basics.handleType)">
            <detail-layout :labelList="signDetail" :form="basics" title="签发信息" ></detail-layout>
          </template>
          <template v-if="(basics.handleType == 'allowWork' && basics.hasOperateTicket== '1') || ['toHandle', 'toEnd', 'finished'].includes(basics.handleType)">
            <detail-layout :labelList="measuresDetail" :form="basics" title="安全措施" >
              <template v-slot:operationList>
                <a-col :span="24">
                  <vxe-table :data="basics.operationWebList" max-height="400" align="center" border show-overflow
                             highlight-hover-row size="small" resizable>
                    <vxe-table-column type="seq" title="序号" show-overflow="title" width="60"></vxe-table-column>
                    <vxe-table-column field="operationName" title="安全措施" min-width="180"> </vxe-table-column>
                    <vxe-table-column field="executeFlag" title="执行结果" min-width="180">
                        <template v-slot="{ row }">
                          <span>{{getLabel(row.executeFlag,dict.ticket_execute_flag)}}</span>
                        </template>
                    </vxe-table-column>
                  </vxe-table>
                </a-col>
              </template>
            </detail-layout>
          </template>
          <template v-if="['toHandle', 'toEnd', 'finished'].includes(basics.handleType)">
            <detail-layout :labelList="allowWorkDetail" :form="basics" title="许可信息" ></detail-layout>
          </template>
          <template v-if="['toEnd', 'finished'].includes(basics.handleType)">
            <detail-layout :labelList="acceptDetail" :form="basics" title="工作执行" >
              <template v-slot:accept>
                <a-col :span="24" class="detail_layout_content">
                  <span class="left">安全交底</span>
                  <span class="right">
                    确认工作负责人布置的工作任务和安全措施，进行安全技术交底。
                </span>
                </a-col>
              </template>
              <template v-slot:workEndTime>
                <a-col :span="24" class="detail_layout_content">
                  <span class="left">工作终结</span>
                  <span class="right">
                    全部工作于{{basics.workEndTime}}结束, 工作人员已全部撤离，材料工具已清理完毕
                </span>
                </a-col>
              </template>
            </detail-layout>
          </template>
          <template v-if="['finished'].includes(basics.handleType)">
            <detail-layout :labelList="endDetail" :form="basics" title="设备投运信息" >
              <template v-slot:deviceMeasure>
                <a-col :span="24" class="detail_layout_content">
                  <span class="left">恢复设备运行</span>
                </a-col>
                <a-col :span="24">
                  <vxe-table :data="basics.deviceMeasureWeb" max-height="400" align="center" border show-overflow
                             highlight-hover-row size="small" resizable>
                    <vxe-table-column type="seq" title="序号" show-overflow="title" width="60"></vxe-table-column>
                    <vxe-table-column field="operationName" title="安全措施" min-width="180"> </vxe-table-column>
                    <vxe-table-column field="executeFlag" title="执行结果" min-width="180">
                      <template v-slot="{ row }">
                        <span>{{getLabel(row.executeFlag,dict.ticket_execute_flag)}}</span>
                      </template>
                    </vxe-table-column>
                  </vxe-table>
                </a-col>
              </template>
            </detail-layout>
          </template>
        </template>
      </a-spin>
    </div>
    <div class="drawer-form-foot">
      <throttle-button label="返回" type="info" @click="cancel" class="solar-eye-btn-primary-line" />
    </div>
  </div>
</template>

<script>
import { basicsDetail, baseDetail, signDetail, measuresDetail, allowWorkDetail, acceptDetail, endDetail } from './detailList';
export default {
  name: 'ticketDetail',
  props: {
    /* 抽屉挂在的DOM节点ID，必要参数 */
    parentId: {
      type: String,
      required: true,
      default: ''
    },
    basics: {
      type: String,
      required: true,
      default: ''
    },
    loading: {
      type: Boolean,
      required: true,
      default: false
    },
    dict: {
      type: Array,
      default: () => {
        return [];
      }
    }
  },
  data () {
    return {
      basicsDetail: Object.freeze(basicsDetail),
      baseDetail: Object.freeze(baseDetail),
      signDetail: Object.freeze(signDetail),
      measuresDetail: Object.freeze(measuresDetail),
      allowWorkDetail: Object.freeze(allowWorkDetail),
      acceptDetail: Object.freeze(acceptDetail),
      endDetail: Object.freeze(endDetail)
    };
  },
  methods: {
    // 关闭回调
    cancel () {
      this.$emit('cancel');
      this.reset();
    },
    // 数据重置
    reset () {
      Object.assign(this.$data, this.$options.data());
    }
  }
};
</script>

<style lang="less" scoped>
.is-do{
  font-size: 12px;
  font-weight: 400;
  line-height: 20px;
  color: #F3A023;
  height: 22px;
  background: rgba(243,160,35,0.2);
  border-radius: 2px;
  border: 1px solid #F3A023;
  margin-left: 10px;
}
.unique-padding {
  padding: 12px 0 0;

  .drawer-form-content {
    padding-right: 12px;
  }
}
</style>
