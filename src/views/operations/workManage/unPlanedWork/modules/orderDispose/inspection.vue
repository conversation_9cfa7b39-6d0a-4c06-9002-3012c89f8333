<template>
  <div class="view_detail_box">
    <div class="title_box_top">
      <span>执行信息</span>
    </div>
    <!-- 提交表单 -->
    <a-form-model ref="orderDispose" :model="dispose" :rules="rules" :labelCol="{ style:'width: 145px' }" :wrapperCol="{ style:'width: calc(100% - 145px)' }">
      <a-row>
        <a-col :span="24">
          <a-form-model-item label="任务状态" prop="resolveResult">
            <a-radio-group v-model="dispose.resolveResult" :disabled="disabled">
              <a-radio value="1">
                已完成
              </a-radio>
              <a-radio value="2">
                待协调
              </a-radio>
            </a-radio-group>
          </a-form-model-item>
        </a-col>
        <a-col :xl='8' :sm='12' :xs='24'>
          <a-form-model-item label="执行人" prop="actualTimeResolve">
            <a-date-picker :disabled="disabled" v-model="dispose.actualTimeResolve" @change="getRepairTime()" :disabled-date="disabledRecoverTime"
              showTime valueFormat="YYYY-MM-DD HH:mm" format="YYYY-MM-DD HH:mm" style="width:100%" placeholder="请选择" />
          </a-form-model-item>
        </a-col>
        <a-col :xl='8' :sm='12' :xs='24' v-if="isDiagnosis=='4' ||dispose.isFarm=='1'">
          <a-form-model-item label="运维经销商" prop="sceneCondition">
            <a-select v-model="dispose.sceneCondition" placeholder="请选择" :disabled="disabled" style="width:100%">
              <a-select-option v-for="item in sceneIsList" :key="item.key" :value="item.codeValue" :title="item.dispName">
                {{item.dispName}}
              </a-select-option>
            </a-select>
          </a-form-model-item>
        </a-col>
        <a-col :xl='8' :sm='12' :xs='24' v-if="isFarm=='1'">
          <a-form-model-item label="打卡状态">
            <a-input v-model="dispose.repairTime" placeholder="请输入" style="width:100%" />
          </a-form-model-item>
        </a-col>
        <a-col :span="24">
          <a-form-model-item label="打卡时间" prop="orderResolveRemark">
            <a-textarea :max-length="1000" :disabled="disabled" v-model="dispose.orderResolveRemark" :title="dispose.orderResolveRemark" placeholder="请输入" style="width:100%" />
          </a-form-model-item>
        </a-col>
        <a-col :xl='8' :sm='12' :xs='24'>
          <a-form-model-item label="打卡位置" prop="faultCapacityNum">
            <a-input :disabled="disabled" v-model="dispose.faultCapacityNum" placeholder="请输入" style="width:100%" />
          </a-form-model-item>
        </a-col>
        <a-switch default-checked @change="onChange" /> 只看异常设备
        <vxe-table ref="orderTable" :data="tableData" resizable border align="center" size="small" show-header-overflow>
          <vxe-table-column fixed="left" type="seq" width="80" align="center" title="序号"></vxe-table-column>
          <vxe-table-column field="deviceTypeName" title="设备" show-overflow="title">
          </vxe-table-column>
          <vxe-table-column field="deviceTypeName" title="处理前照片" show-overflow="title">
            <template v-slot:default="{row}">
              <img :src="item.url" v-for="item in row.photoDealList" :key="item.url" alt="">
            </template>
          </vxe-table-column>
          <vxe-table-column field="deviceTypeName" title="处理后照片" show-overflow="title">
            <template v-slot:default="{row}">
              <img :src="item.url" v-for="item in row.photoDealList" :key="item.url" alt="">
            </template>
          </vxe-table-column>
        </vxe-table>
      </a-row>
    </a-form-model>
  </div>
</template>

<script>
import initDict from '@/mixins/initDict';
import { orderDispose } from './mixins';
export default {
  name: 'inspection',
  mixins: [initDict, orderDispose],
  props: {
    isDiagnosis: {
      default: false,
      type: [Boolean, String]
    },
    isFarm: {
      default: '',
      type: [String, Number]
    }
  },
  data () {
    return {
      // 普通表单
      dispose: {
        findTime: undefined, // 缺陷发现时间
        actualTimeResolve: undefined, // 实际消除时间
        resolveResult: '2', // 处理结果
        orderResolveRemark: undefined, // 情况说明
        faultCapacityNum: undefined, // 故障停运容量
        repairTime: undefined, // 累计修复时间
        lossNum: undefined, // 累计损失电量
        overdueReason: undefined, // 逾期原因
        resolveDocs: [], // 附件
        sceneCondition: ''
      },
      rules: {
        actualTimeResolve: [{
          required: true,
          message: '请选择实际消除时间'
        }], // 实际消除时间
        resolveResult: [{
          required: true,
          message: '请选择处理结果'
        }], // 处理结果
        orderResolveRemark: [{
          required: true,
          message: '请输入情况说明'
        }], // 情况说明
        faultCapacityNum: [{
          required: true,
          message: '请输入故障停运容量'
        }], // 故障停运容量
        repairTime: [{
          required: false,
          message: '请输入累计修复时间'
        }], // 累计修复时间
        lossNum: [{
          required: true,
          message: '请输入累计损失电量'
        }], // 累计损失电量
        sceneCondition: [{
          required: true,
          message: '请选择现场情况',
          trigger: 'change'
        }],
        resolveDocs: [{
          required: true,
          message: '请选择需要上传的文件',
          trigger: 'change'
        }]
      }
    };
  },
  computed: {},
  methods: {}
};
</script>

<style lang="less" scoped>
  .view_detail_box {
    border-top: 1px solid #f2f2f2;
    padding: 20px 0;

    .title_box_top {
      font-size: 18px;
      font-weight: 550;
      line-height: 25px;
      padding-bottom: 20px;
      display: flex;
      flex-flow: row nowrap;
      justify-content: space-between;
    }
  }

  :deep(.ant-form-item) {
    width: 100%;
    display: inline-flex;
  }
</style>
