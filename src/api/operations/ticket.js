import { axios } from '@/utils/erp-request';

// 查询列表
export function getTicketInfo (data) {
  return axios({
    url: '/ticket/manager/page',
    method: 'post',
    data
  });
}

// 统计状态数量
export function countTicketSts (data) {
  return axios({
    url: '/ticket/manager/count',
    method: 'post',
    data
  });
}

// 删除
export function deleteTicket (data) {
  return axios({
    url: '/ticket/deleteErpPowerTicketRec',
    method: 'post',
    data
  });
}

// 获取附件信息
export function getTicketAnnex (data) {
  return axios({
    url: '/ticket/annexErpPowerTicketRec',
    method: 'post',
    data
  });
}

// 导出列表
export function exportExcel (data) {
  return axios({
    url: '/ticket/manager/exportExcel',
    method: 'post',
    data
  });
}

// 附件打包下载、下载
export function exportZip (data) {
  return axios({
    url: '/ticket/exportZip',
    method: 'post',
    data
  });
}

// 样票下载
export function downloadSpecimen (data) {
  return axios({
    url: '/ticket/downloadSpecimen',
    method: 'post',
    data
  });
}

// 新增
export function insertOrUpdateTicket (data) {
  return axios({
    url: '/ticket/manager/insertOrUpdate',
    method: 'post',
    data
  });
}

// 详情
export function getTicketDetail (data) {
  return axios({
    url: '/ticket/manager/detail',
    method: 'post',
    data
  });
}

// 获取各种人  发令人 受令人
export function getAllUserList (data) {
  return axios({
    url: '/ticket/manager/userList',
    method: 'post',
    data
  });
}

// 查询危险点及控制措施
export function getDangerousPointList (data) {
  return axios({
    url: '/ticket/config/peril/list',
    method: 'post',
    data
  });
}

// 打印
export function print (data) {
  return axios({
    url: '/ticket/manager/print',
    method: 'post',
    data
  });
}

// 作废
export function nullifyTicket (data) {
  return axios({
    url: '/ticket/manager/cancel',
    method: 'post',
    data
  });
}

// 两票工作班成员变更详情
export function memberChangeDetail (data) {
  return axios({
    url: '/ticket/manager/detail/work',
    method: 'post',
    data
  });
}

// 工作班成员变更
export function updateMemberChange (data) {
  return axios({
    url: '/ticket/manager/change/staff',
    method: 'post',
    data
  });
}

// 通过 退回
export function approveTicket (data) {
  return axios({
    url: '/ticket/manager/approve',
    method: 'post',
    data
  });
}

// 获取安全措施
export function getSafetyMeasuresList (data) {
  return axios({
    url: '/ticket/config/safe/list',
    method: 'post',
    data
  });
}

// 模板下载
export function downloadTemplate (data) {
  return axios({
    url: '/ticket/manager/template',
    method: 'post',
    data
  });
}

// 票面预览
export function specimenPreview (data) {
  return axios({
    url: '/ticket/manager/specimen',
    method: 'post',
    data
  });
}

// 根据作业类别查询作业内容
export function getJobContentDict (data) {
  return axios({
    url: '/ticket/v2/config/peril/getJobContentDict',
    method: 'post',
    data
  });
}
