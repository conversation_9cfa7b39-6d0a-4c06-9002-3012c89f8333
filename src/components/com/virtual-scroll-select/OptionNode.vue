<template>
  <div class="select-option" @click="selectItem" @mouseover="isShow = true" @mouseleave="isShow = false">
    <!-- 使用自定义插槽渲染 -->
    <div v-if="useSlot && slotScope"
         :class="{'selected': source['checked'], 'isDisabled': source['isDisabled']}"
         class="custom-slot-content">
      <div class="slot-wrapper">
        <!-- 渲染自定义插槽内容 -->
        <component
          :is="{
            functional: true,
            render: (h, ctx) => slotScope({
              item: source,
              index: index,
              selected: source['checked'],
              disabled: source['isDisabled']
            })
          }"
        />
      </div>
      <!-- 多选时显示选中图标 -->
      <svg-icon v-if="isMultiple && (source['checked'] || isShow)"
                :class="{ 'isShow': isShow }"
                iconClass="selected"
                class="selection-icon">
      </svg-icon>
    </div>

    <!-- 默认渲染方式 -->
    <template v-else>
      <div v-if="isMultiple" class="is-multiple" :class="{'selected': source['checked'], 'isDisabled': source['isDisabled']}">
        <span :title="source['name']" class="option-name">{{source[label]}}</span>
        <svg-icon  :class="{ 'isShow': isShow }" v-if="source['checked'] || isShow" iconClass="selected"></svg-icon>
      </div>
      <div v-else :class="{'selected': source['checked'], 'isDisabled': source['isDisabled']}">
        <span :title="source[label]" class="option-name">{{source[label]}}</span>
      </div>
    </template>
  </div>
</template>
<script>
export default {
  name: 'OptionNode',
  props: {
    // 每一行的索引
    index: {
      type: Number
    },
    // 每一行的内容
    source: {
      type: Object,
      default () {
        return {};
      }
    },
    // 需要显示的名称
    label: {
      type: String
    },
    // 绑定的值
    value: {
      type: String
    },
    // 是否多选
    isMultiple: {
      type: Boolean,
      default () {
        return false;
      }
    },
    onItemClick: {
      type: Function,
      default () {
        return () => {};
      }
    },
    // 是否使用自定义插槽
    useSlot: {
      type: Boolean,
      default: false
    },
    // 插槽作用域函数
    slotScope: {
      type: Function,
      default: null
    }
  },
  data () {
    return {
      isShow: false // 多选hover后面√是否显示
    };
  },
  methods: {
    selectItem () {
      if (!this.source.isDisabled) {
        this.onItemClick(this.source);
      }
    }
  }
};
</script>
<style lang="less" scoped>
.select-option {
  width: 100%;
  cursor: pointer;
  color: var(--zw-text-2-color--default);
  font-size: 14px;
  & > div {
    padding: 5px 12px;
  }
  &:hover {
    background-color: var(--zw-primary-bg-color--hover);
    border-radius: 4px;
  }
  .selected {
    background: var(--zw-primary-color--default);
    color: var(--zw-common-white-color--default);
    .checked {
      color: var(--zw-common-white-color--default) !important;
    }
  }
  & > div {
    display: flex;
    align-items: center;
    .option-name {
      padding-right: 32px;
      display: block;
      position: relative;
      flex: 1;
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
    }

  }
  .isDisabled {
    color: var(--zw-text-reset-grey-color--default);
    cursor: not-allowed;
    .isShow {
      color: var(--zw-text-reset-grey-color--default) !important;
    }
  }

  // 自定义插槽样式
  .custom-slot-content {
    display: flex;
    align-items: center;
    position: relative;

    .slot-wrapper {
      flex: 1;
      overflow: hidden;
    }

    .selection-icon {
      margin-left: 8px;
      flex-shrink: 0;
    }
  }
}
</style>
