import { getDeviceModelList } from '@api/health/healthapi';
export const DeviceMixins = {
  data () {
    return {
      manufacturersList: [],
      deviceModelList: [],
      versionList: []
    };
  },
  methods: {
    // 下拉框选项搜索
    filterOption (input, option) {
      return option.componentOptions.children[0].text.toLowerCase().indexOf(input.toLowerCase()) >= 0;
    },
    // 获取生产厂商下拉选项值
    getManufacturersList (deviceType) {
      getDeviceModelList({ manageDeviceTypeList: [deviceType].join(','), groupBy: 'makerId' }).then((res) => {
        if (Array.isArray(res) && res.length) {
          this.manufacturersList = res;
        } else {
          this.manufacturersList = [];
        }
      });
    },
    // 获取设备型号下拉选项值
    getDeviceModelList (deviceType, id, factoryId) {
      getDeviceModelList({ manageDeviceTypeList: [deviceType].join(','), id: id == '--' ? '' : id, makerIdList: factoryId == '--' ? '' : [factoryId].join(','), groupBy: 'modelId' }).then((res) => {
        if (Array.isArray(res) && res.length) {
          this.deviceModelList = res;
        } else {
          this.deviceModelList = [];
        }
      });
    }
  }

};
