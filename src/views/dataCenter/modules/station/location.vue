<template>
  <a-modal v-model="visible" title="经纬度设置" width="80%" footer @cancel="onClose(false)">
    <div class="solar-eye-search-model">
      <div class="solar-eye-search-content">
        <a-row a-row :gutter="24" style="margin: 0">
          <a-col :span="6">
            <div class="search-item">
              <span class="search-label">查询地址</span>
                <a-input-search placeholder="输入地址" v-model="searchValue"  @search="onSearch" />
            </div>
          </a-col>
          <a-col :span="6">
            <div class="search-item">
              <span class="search-label">经度</span>
              <div class="text-div">{{lng}}</div>
            </div>
          </a-col>
          <a-col :span="6">
            <div class="search-item">
              <span class="search-label">纬度</span>
              <div class="text-div">{{lat}}</div>
            </div>
          </a-col>
        </a-row>
      </div>
    </div>
    <div id="container"></div>
    <div class="drawer-form-foot">
      <a-button  @click="onClose(false)" class="solar-eye-btn-primary-cancel">取消</a-button>
      <a-button class="solar-eye-btn-primary" @click="saveData">保存</a-button>
    </div>
  </a-modal>
</template>
<script>
export default {
  name: 'loaction',
  data () {
    return {
      visible: false,
      map: null,
      geocoder: null,
      lng: '',
      lat: '',
      searchValue: ''
    };
  },
  mounted () {
    // this.createMap();
  },
  methods: {
    createMap () {
      this.visible = true;
      this.$nextTick(() => {
        this.map = new AMap.Map('container', {
          resizeEnable: true,
          zoom: 4.8, // 初始化地图层级
          center: [116.397428, 39.909230], // 初始化地图中心点
          rotateEnable: true,
          pitchEnable: true
        });
        this.geocoder = new AMap.Geocoder();
        this.marker = new AMap.Marker();
        this.map.on('click', this.showInfoClick);
      });
    },
    showInfoClick (e) {
      this.lng = e.lnglat.getLng(); // 获取经度
      this.lat = e.lnglat.getLat(); // 获取纬度
      this.marker.setPosition({ lng: this.lng, lat: this.lat, Q: this.lat, R: this.lng });
      this.map.add(this.marker);
    },
    onSearch () {
      this.geocoder.getLocation(this.searchValue, (status, result) => {
        if (status === 'complete' && result.geocodes.length) {
          let lnglat = result.geocodes[0].location;
          this.lng = lnglat.lng;
          this.lat = lnglat.lat;
          this.marker.setPosition(lnglat);
          this.map.add(this.marker);
          this.map.setFitView(this.marker);
        } else {
          log.error('根据地址查询位置失败');
        }
      });
    },
    onClose (flag) {
      this.visible = false;
      this.lng = '';
      this.searchValue = '';
      this.lat = '';
    },
    saveData () {
      if (this.lng && this.lat) {
        this.$emit('setLngLat', this.lng, this.lat);
        this.onClose();
      } else {
        this.$message.destroy();
        this.$message.warning('请设置经纬度！');
      }
    }

  }
};
</script>
<style lang="less" scoped>
:deep(.ant-modal-content) {
  height: 90vh;
  .ant-modal-body {
    height: 100%;
    max-height: calc(100vh - 124px);
  }
}
.solar-eye-search-model .search-item {
  margin: 0;
}
#container {
  height: calc(100% - 102px);
  width: 100%;
}
.text-div {
  height:32px;
  padding:4px 11px;
  border: 1px solid #d9d9d9;
  border-radius: 4px;
  width:100%;
}
:root[data-theme='dark'] {
  .solar-eye-search-model .solar-eye-search-content {
    background: transparent;
    .text-div {
      border: 1px solid #51637C;
    }
  }
}
</style>
