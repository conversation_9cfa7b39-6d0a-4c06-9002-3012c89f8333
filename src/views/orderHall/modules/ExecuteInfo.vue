<template>
  <div v-if="executeInfoShow">
    <div v-if="['3', '8'].includes(type)">
      <ExecuteInfoDetail :baseForm="formData" />
    </div>
    <div v-else>
      <a-row :gutter="24">
        <a-col :span="24">
          <div class="order-dispose">
            <div class="title-box">
              <span class="before"></span>
              <span>执行信息</span>
            </div>
          </div>
        </a-col>
      </a-row>
      <a-row :gutter="24" v-if="conditionShow">
        <a-col :xl="8" :sm="12" :xs="24">
          <a-form-model-item
            label="现场情况"
            prop="sceneCondition"
            :rules="{ required: true, message: '此项为必填项' }"
          >
            <a-select
              :disabled="isDisabled"
              v-model="formData.sceneCondition"
              :getPopupContainer="(node) => node.parentNode"
              placeholder="请选择现场情况"
            >
              <a-select-option v-for="item in sceneConditionList" :key="item.dataValue">
                {{ item.dataLable }}
              </a-select-option>
            </a-select>
          </a-form-model-item>
        </a-col>
      </a-row>
      <a-row :gutter="24" v-if="realConditionShow">
        <a-col :xl="8" :sm="12" :xs="24">
          <a-form-model-item
            label="故障停运容量(MWp)"
            prop="faultCap"
            :rules="{ required: true, message: '此项为必填项' }"
          >
            <a-input-number
              :disabled="isDisabled"
              :max="999999.9999"
              :min="0"
              :precision="4"
              v-model="formData.faultCap"
              placeholder="请输入"
              style="width: 100%"
            />
          </a-form-model-item>
        </a-col>
        <a-col :xl="8" :sm="12" :xs="24">
          <a-form-model-item
            label="累计损失电量(万KWh)"
            prop="totalLossPower"
            :rules="{ required: true, message: '此项为必填项' }"
          >
            <a-input-number
              :disabled="isDisabled"
              :precision="4"
              :min="0"
              :max="9999999.999"
              v-model="formData.totalLossPower"
              placeholder="请输入"
              style="width: 100%"
            />
          </a-form-model-item>
        </a-col>
        <a-col :span="12">
          <a-form-model-item label="逾期原因" prop="overdueReason" :rules="{required:overdueReasonRequired,message: '此项为必填项' }">
            <a-textarea
              :disabled="isDisabled"
              :max-length="1000"
              v-model="formData.overdueReason"
              :title="formData.overdueReason"
              placeholder="请输入"
              style="width: 100%"
            />
          </a-form-model-item>
        </a-col>
        <a-col :span="12">
          <a-form-model-item label="备注" prop="conditionRemark">
            <a-textarea
              :disabled="isDisabled"
              :max-length="1000"
              v-model="formData.conditionRemark"
              :title="formData.conditionRemark"
              placeholder="请输入"
              style="width: 100%"
            />
          </a-form-model-item>
        </a-col>
      </a-row>
      <a-row :gutter="24" v-else-if="otherConditionShow">
        <a-col :span="24">
          <a-form-model-item label="现场图片" prop="sceneFileList" :rules="{ required: true, message: '此项为必填项' }">
            <uploadFileView
              :disabled="isDisabled"
              v-model="formData.sceneFileList"
              :maxNum="5"
              :maxSize="10"
              tip="最多上传5张图片，单张图片不超10MB，支持格式jpg/jpeg/png/bmp！"
              accept=".jpg,.png,.jpeg,.bmp"
              :multiple="true"
              @set="$emit('validateField')"
              >上传</uploadFileView
            >
          </a-form-model-item>
        </a-col>
      </a-row>
      <!-- 气象服务 -->
      <a-row :gutter="24" v-if="formData.workSubclass === '20'">
        <a-col :span="24">
          <a-form-model-item label="厂家名称" prop="meteoMaker">
            <a-input :disabled="isDisabled" placeholder="请输入" v-model="formData.meteoMaker" :maxLength="20" />
          </a-form-model-item>
        </a-col>
      </a-row>
      <a-row :gutter="24" v-if="!['7', '8'].includes(formData.taskType)">
        <a-col :xl="8" :sm="12" :xs="24">
          <a-form-model-item label="备注" prop="conditionRemark">
            <a-textarea
              :disabled="isDisabled"
              :max-length="1000"
              v-model="formData.conditionRemark"
              :title="formData.conditionRemark"
              placeholder="请输入"
              style="width: 100%"
            />
          </a-form-model-item>
        </a-col>
      </a-row>
    </div>
  </div>
</template>

<script>

import ExecuteInfoDetail from './ExecuteInfoDetail';
import uploadFileView from '@/components/com/fileUploadView';

export default {
  name: 'execute-info',
  components: { ExecuteInfoDetail, uploadFileView },
  props: {
    type: {
      type: String,
      default: () => {
        return null;
      }
    },
    formData: {
      type: String,
      default: () => {
        return {};
      }
    },
    isDisabled: {
      type: Boolean,
      default: false
    },
    dictMap: {
      type: Object,
      default: () => {
        return {};
      }
    },
    rowInfo: {
      type: Object,
      default: () => {
        return {};
      }
    }
  },
  data () {
    return {};
  },
  mounted () {},
  methods: {},
  computed: {
    executeInfoShow () {
      const type = this.type;
      const formData = this.formData;
      if (['6', '5'].includes(type)) {
        return false;
      }
      // otherSts:0标识迁移数据，代表流程已走完
      if (type == '3' && ['0', '3', '4', '5', '6'].includes(formData.otherSts)) {
        return true;
      }
      if ((type == 7 || type == 8) && ['3', '4', '5', '6'].includes(formData.otherSts)) {
        return true;
      }
      return false;
    },
    overdueReasonRequired () {
      const formData = this.formData;
      if (formData.predictRecoverTime) {
        return new Date().getTime() > new Date(formData.predictRecoverTime).getTime();
      }
      return false;
    },
    conditionShow () {
      const { orderSource, opsSourceCategory, taskType } = this.formData;
      return (['4', '5'].includes(orderSource) || opsSourceCategory === '3') && ['7', '8'].includes(taskType);
    },
    realConditionShow () {
      const formData = this.formData;
      if (this.conditionShow) {
        return formData.sceneCondition === '1' && ['7', '8'].includes(formData.taskType);
      }
      return ['7', '8'].includes(formData.taskType);
    },
    otherConditionShow () {
      const formData = this.formData;
      if (this.conditionShow) {
        return formData.sceneCondition && formData.sceneCondition !== '1';
      }
      return false;
    },
    sceneConditionList () {
      const opsSceneCondition = this.dictMap.ops_scene_condition || [];
      if (this.formData.orderSource === '4') {
        return opsSceneCondition;
      }
      // 非ai诊断的过滤真实问题和误报
      return opsSceneCondition.filter(item => ['1', '2'].includes(item.dataValue));
    }
  }
};
</script>
