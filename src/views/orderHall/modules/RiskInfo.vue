<template>
  <div v-if="riskInfoShow">
    <div v-if="['3', '7', '8'].includes(type) && formData.taskType !== '4'">
      <RiskInfoDetail :baseForm="formData" />
    </div>
    <a-row :gutter="24" v-else>
      <a-col :span="24">
        <div class="order-dispose">
          <div class="title-box">
            <span class="before"></span>
            <span>风险信息</span>
          </div>
        </div>
      </a-col>

      <template>
        <a-col :xl="8" :sm="12" :xs="24">
          <a-form-model-item label="作业类别" prop="jobCategory" :rules="{ required: true, message: '此项为必填项' }">
            <a-select v-model="formData.jobCategory" allowClear size="default" placeholder="请选择" style="width: 100%">
              <a-select-option v-for="item in dictMap.job_category" :key="item.dataValue" :value="item.dataValue">
                {{ item.dispName }}
              </a-select-option>
            </a-select>
          </a-form-model-item>
        </a-col>
        <a-col :xl="8" :sm="12" :xs="24">
          <a-form-model-item
            label="作业内容"
            prop="erpSqJobRiskMappingId"
            :rules="{ required: true, message: '此项为必填项' }"
          >
            <a-select
              v-model="formData.erpSqJobRiskMappingId"
              @change="erpSqJobRiskMappingIdChange"
              allowClear
              size="default"
              placeholder="请选择"
              style="width: 100%"
            >
              <a-select-option v-for="item in jobContentOptions" :key="item.id" :value="item.id" :code="item.riskLevel">
                {{ item.jobContentName }}
              </a-select-option>
            </a-select>
          </a-form-model-item>
        </a-col>
        <a-col :xl="8" :sm="12" :xs="24">
          <a-form-model-item label="风险等级" prop="riskLevel" :rules="{ required: true, message: '此项为必填项' }">
            <a-select v-model="formData.riskLevel" size="default" disabled placeholder="请选择" style="width: 100%">
              <a-select-option v-for="item in dictMap.risk_level" :key="item.dataValue" :value="item.dataValue">
                {{ item.dispName }}
              </a-select-option>
            </a-select>
          </a-form-model-item>
        </a-col>
        <a-col :xl="8" :sm="12" :xs="24">
          <a-form-model-item
            label="作业开始日期"
            prop="riskJobDate"
            :rules="{ required: true, message: '此项为必填项' }"
          >
            <a-date-picker
              disabled
              style="width: 90%"
              size="default"
              v-model="formData.riskJobDate"
              format="YYYY-MM-DD"
              valueFormat="YYYY-MM-DD"
              placeholder="请输入作业开始日期"
              :disabled-date="disabledRiskJobDate"
            />
            <a-popover placement="right">
              <template slot="content">
                <div style="max-width: 268px">
                该日期为安全风险数据生成开始时间
                </div>
              </template>
              <span>
                <svg-icon  style="margin-left: 1em" iconClass="health-info"></svg-icon>
              </span>
            </a-popover>

          </a-form-model-item>
        </a-col>
      </template>
    </a-row>
  </div>
</template>

<script>
import { riskAllocation } from '@/api/isolarErp/safetyquality/safetyRisk';
import initDict from '@/mixins/initDict';
import RiskInfoDetail from './RiskInfoDetail';
import moment from 'moment';

export default {
  name: 'risk-info',
  mixins: [initDict],
  components: { RiskInfoDetail },
  props: {
    type: {
      type: String,
      default: () => {
        return null;
      }
    },
    formData: {
      type: Object,
      default: () => {
        return {};
      }
    },
    dictMap: {
      type: Object,
      default: () => {
        return {};
      }
    }
  },
  data () {
    return {
      jobContentOptions: []
    };
  },
  mounted () {
    // this.getDictMap('risk_level,job_category')
  },
  methods: {
    // 作业类别change事件
    jobCategoryChange () {
      const jobCategory = this.formData.jobCategory;
      const param = { jobCategory };
      if (!jobCategory) {
        this.jobContentOptions = [];
        this.formData.erpSqJobRiskMappingId = undefined;
        this.formData.riskLevel = undefined;
        return;
      }
      riskAllocation(param).then((res) => {
        this.jobContentOptions = res.result_data;
      });
    },
    // 作业内容change事件
    erpSqJobRiskMappingIdChange (val, option) {
      if (val) {
        this.formData.riskLevel = option.data.attrs.code;
      } else {
        this.formData.riskLevel = undefined;
      }
    },
    // 作业开始日期
    disabledRiskJobDate (current) {
      // 不能选择今天过去3个月之前的时间 或 今天3个月之后的时间
      return (current && current > moment().add(3, 'month')) || current < moment().subtract(3, 'month');
    }
  },
  computed: {
    riskInfoShow () {
      const type = this.type;
      const formData = this.formData;
      if (['3'].includes(type)) {
        if (['1', '2', '3', '12', '9', '11', '7', '8'].includes(formData.taskType) && formData.workSubclass != '17') {
          return true;
        }
        return false;
      }
      if (['6', '5', '8', '7'].includes(type)) {
        // workSubclass：17无人机 无风险信息
        const flag =
          ['7', '8', '11', '9', '12', '1', '2'].includes(formData.taskType) ||
          (formData.taskType === '3' && formData.workSubclass != '17');
        return flag;
      }
      return false;
    }
  },
  watch: {
    'formData.jobCategory' (val) {
      this.jobCategoryChange();
    }
  }
};
</script>
