// 任务大厅页面
export const taskHall = [
  { name: 'taskCode', comment: '任务编号', width: 140 },
  { name: 'areaName', comment: '区域', width: 160 },
  { name: 'psaName', comment: '电站', width: 180 },
  { name: 'taskType', comment: '任务类型', width: 120 },
  { name: 'taskSubType', comment: '任务子类', width: 120 },
  { name: 'flowStsName', comment: '任务状态', width: 100 },
  { name: 'liablePerson', comment: '责任人', width: 120 },
  { name: 'planStartTime', comment: '计划开始', width: 150 },
  { name: 'planEndTime', comment: '计划结束', width: 170 },
  { name: 'taskSource', comment: '任务来源', width: 120 },
  { name: 'actualStartTime', comment: '实际开始', width: 120 },
  { name: 'actualEndTime', comment: '实际结束', width: 120 },
  { name: 'acceptanceTime', comment: '验收日期', width: 120 }
];

export const labelList1 = [
  {
    label: '任务类型',
    key: 'taskType'
  },
  {
    label: '任务子类',
    key: 'taskSubType',
    span: 16
  },
  {
    label: '电站名称',
    key: 'psaName'
  },
  {
    slot: 'planTime'
  }
];

export const labelList2 = [
  {
    label: '任务类型',
    key: 'taskType'
  },
  {
    label: '任务子类',
    key: 'taskSubType'
  },
  {
    label: '任务来源',
    key: 'taskSourceName'
  },
  {
    label: '电站名称',
    key: 'psaName'
  },
  {
    slot: 'planTime'
  },
  // {
  //   slot: 'stationEnvironment'
  // },
  {
    label: '并网容量（MW）',
    key: 'gridConnectedScale'
  },
  {
    label: '并网时间',
    key: 'mergeTime'
  },
  {
    key: 'placeName',
    label: '所属国家/省/市/区'
  },
  {
    label: '详细地址',
    key: 'placeAddress'
  },
  {
    label: '试验负责人',
    key: 'liablePersonName'
  },
  {
    label: '试验成员',
    key: 'teamMembersName',
    ellipsis: true
  },
  {
    label: '试验仪器',
    key: 'instrumentName',
    ellipsis: true
  },
  {
    label: '电压等级',
    key: 'voltageLevel',
    ellipsis: true
  },
  {
    label: '报告名称',
    key: 'reportTitle',
    ellipsis: true
  }
];

export const labelList3 = [
  { label: '终止时间', key: 'terminateTime' },
  {
    label: '终止原因',
    key: 'terminateReason',
    span: 24
  }
];
