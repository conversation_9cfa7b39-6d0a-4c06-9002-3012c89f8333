<template>
   <a-drawer class="optionModal" :visible="open"
    title="开启算法"
    :get-container="false"
    :wrap-style="{ position: 'absolute' }"
    width="100%"
    @close="cancel">
    <div class="form-model" ref="optionFormData" :labelCol="{style:'width:130px'}" :wrapperCol="{ style: 'width: calc(50% - 130px)' }">
    <a-row :gutter="24">
        <a-col :xxl='24' :xl='24' :lg='24' v-for="item in algorithmArr" :key="item.index">
          <div>
            <span class="margin-right-24">{{item.label}}</span>
            <a-radio-group :options="options" v-model="item.value" />
          </div>
        </a-col>
    </a-row>
    </div>
    <div style="width:100%;text-align:center;margin-top:48px">
      <a-button  :style="{ marginRight: '8px' }"  size="default" :disabled="loading" @click="cancel">取消</a-button>
      <a-button size="default" :loading="loading" type="primary" @click="saveForm()">确认</a-button>
    </div>
   </a-drawer>
</template>

<script>
import {
  addAlgorithmPowerStation,
  getSystemCodeList
} from '@/api/health/healthapi.js';
export default {
  props: {
    open: {
      type: Boolean,
      required: true,
      default: false
    },
    psId: {
      type: [String, Number],
      required: true
    }
  },
  data () {
    return {
      loading: false,
      algorithmArr: [],
      options: [
        { label: '开启', value: true },
        { label: '关闭', value: false }
      ]
    };
  },
  created () {
    this.getAlgorithmArr();
  },
  methods: {
    async getAlgorithmArr () {
      let res = await getSystemCodeList({ firstTypeCode: '0076' });
      this.algorithmArr = res.result_data['0076'].map(item => {
        return {
          label: item.secondName,
          index: item.secondTypeCode,
          value: true
        };
      });
    },

    // 数据保存
    async saveForm () {
      let res;
      this.algorithmIds = [];
      this.algorithmArr.forEach(element => {
        if (element.value) {
          this.algorithmIds.push(element.index);
        }
      });
      this.loading = true;
      res = await addAlgorithmPowerStation({
        psId: this.psId,
        algorithmIds: this.algorithmIds
      });
      this.loading = false;
      if (res.result_code !== '1') {
        this.$message.error(res.result_msg);
        return;
      }
      this.$message.success(`操作成功`);
      this.cancel();
    },
    // 关闭弹窗回调方法
    cancel () {
      this.$emit('onClose');
      this.loading = false;
    }
  }
};
</script>

<style lang="less">
  .optionModal {
    .ant-row {
      .ant-col {
          width: 100% !important;
          display: inline-flex !important;
          justify-content: center;
          line-height: 45px;
          >div{
            width:340px;
             .ant-radio-group{
                  line-height: 45px;
                  float:right;
              }
          }
      }

    }
  }
  .margin-right-24{
    margin-right:24px;
  }

</style>
