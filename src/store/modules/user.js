// import Vue from 'vue'
import { login, logout, thirdLogin } from '@/api/login';
import { DEFAULT_TOP_DEPART, ACCESS_TOKEN, USER_NAME, USER_INFO, USER_LCA, USER_DEPT_LCA, USER_AUTH, SYS_BUTTON_AUTH, ALL_PERMISSION_MENU, UI_CACHE_DB_DICT_DATA, TENANT_ID, CACHE_INCLUDED_ROUTES, HAS_ALL_DATA_HEALTH, UPLOAD_TYPE,
  UPLOAD_END_POINT,
  UPLOAD_ASSCESS_KEY,
  UPLOAD_SECRET_KEY,
  UPLOAD_MERGE_BUCKET,
  UPLOAD_ENVIRONMENT_PATH,
  UPLOAD_FRAGMENTATION_PATH,
  UPLOAD_REGION_NAME,
  HAS_INDERPENDENT,
  SUNGROW_DEP,
  OTHER_DEP,
  ALL_DEP,
  SYS_USER_AUTH
 } from '@/store/mutation-types';
import { welcome } from '@/utils/util';
import { queryPermissionsByUser } from '@/api/api';
import { postAction } from '@/api/manage';
import { updateTheme } from '@/components/tools/setting';
import { message } from 'ant-design-vue';
import { myDeptTree } from '@/api/system/accountManage.js';
import { showHandle } from '@/utils/erpcommon.js';
import { getStorageMethod } from '@/api/tango/fileManage';
import { microFromName } from '@/wujie';
const env = process.env.VUE_APP_ENV;
let work = env ? (env === 'pro' ? 'www' : env) : 'fat';
const systemName = window._CONFIG['system'];
const isWork = systemName == '/work';
const platformList = {
  '/houseHold': '.isolareye.com/houseHold',
  '/iSolarHealth': '.isolarhealth.com',
  '/smartAnalyze': '.isolareye.com/zx',
  '/integratedPlatform': '.isolareye.com/allInOne',
  '/solarCare': '.isolarhealth.com/newCare'
};
const otherPlatform = ['/iSolarHealth', '/smartAnalyze', '/solarCare', '/iot', '/houseHold', '/integratedPlatform', '/screen/page', '/screen/jd', '/aigc'];
const user = {
  state: {
    token: '',
    username: '',
    realname: '',
    tenantid: '',
    welcome: '',
    avatar: '',
    permissionList: [],
    info: {},
    direction: '',
    hasAllDataHealth: '',
    isNotReadNum: 0,
    firstMenu: '',
    isShowMenu: true,
    firstTimeLogin: ''
  },

  mutations: {
    SET_NOTREADNUM: (state, o) => {
      if (o.isUpdate) {
        state.isNotReadNum -= 1;
      } else {
        state.isNotReadNum = o.num;
      }
    },
    SET_FirstMenu: (state, item) => {
      state.firstMenu = item;
      Vue.ls.set('FirstMenu', item);
    },
    SET_TOKEN: (state, token) => {
      state.token = token;
    },
    SET_NAME: (state, { username, realname, welcome }) => {
      state.username = username;
      state.realname = realname;
      state.welcome = welcome;
    },
    SET_AVATAR: (state, avatar) => {
      state.avatar = avatar;
    },
    SET_PERMISSIONLIST: (state, permissionList) => {
      state.permissionList = permissionList;
    },
    SET_INFO: (state, info) => {
      state.info = info;
    },
    SET_TENANT: (state, id) => {
      state.tenantid = id;
    },
    VIEW_DIRECTION: (state, direction) => {
      state.direction = direction;
    },
    SET_HAS_ALL_DATA_HEALTH: (state, allData) => {
      state.hasAllDataHealth = allData;
    },
    SET_ISSHOWMENU: (state, isShowMenu) => {
      state.isShowMenu = isShowMenu;
    },
    SET_FirstTimeLogin: (state, firstTimeLogin) => {
      state.firstTimeLogin = firstTimeLogin;
    }
  },

  actions: {
    // 跨平台登录
    ValidateLogin ({ commit }, userInfo) {
      return new Promise((resolve, reject) => {
        // getAction('/sys/cas/client/validateLogin', userInfo).then(response => {
        postAction('/sys/jwt', userInfo).then(async response => {
          if (response.success) {
            const result = response.result;
            dealResult({ commit }, result);
            if (systemName == '/solarCare') {
              let storageRes = await getStorageMethod({ systemType: '2' });
              setStorageData(storageRes.payload);
            }
            resolve(response);
          } else {
            resolve(response);
          }
        }).catch(error => {
          reject(error);
        });
      });
    },
    // 手机号码登陆
    TelePhoneLogin ({ commit }, userInfo) {
      return new Promise((resolve, reject) => {
        postAction('/sys/phoneLogin', userInfo).then(async response => {
          if (response.success) {
            const result = response.result;
            dealResult({ commit }, result);
            if (systemName == '/solarCare') {
              let storageRes = await getStorageMethod({ systemType: '2' });
              setStorageData(storageRes.payload);
            }
            resolve(response);
          } else {
            resolve(response);
          }
        }).catch(error => {
          reject(error);
        });
      });
    },
    // 登录
    Login ({ commit }, userInfo) {
      return new Promise((resolve, reject) => {
        login(userInfo).then(async response => {
          if (response.code == '200') {
            const result = response.result;
            dealResult({ commit }, result);
            if (systemName == '/solarCare') {
              let storageRes = await getStorageMethod({ systemType: '2' });
              setStorageData(storageRes.payload);
            }
            resolve(response);
          } else {
            if (response.message == '验证码错误') {
              resolve(response);
            } else {
              reject(response);
            }
          }
        }).catch(error => {
          reject(error);
        });
      });
    },
    // 获取用户信息
    GetPermissionList ({ commit }) {
      return new Promise((resolve, reject) => {
        queryPermissionsByUser().then(response => {
          const menuData = response.result.menu;

          let hasScreen = (menuData.filter(item => (item.path == '/screen/page'))).length == 1;
          let JDScreen = (menuData.filter(item => (item.path == '/screen/jd'))).length == 1;
          let hasAigc = (menuData.filter(item => (item.path == '/aigc'))).length == 1;
          // 大屏权限
          localStorage.setItem('hasScreen', hasScreen);
          localStorage.setItem('JDScreen', JDScreen);
          localStorage.setItem('AIGC', hasAigc);
          let isHasErp = menuData.filter(item => otherPlatform.indexOf(item.path) == -1).length == 0;
          sessionStorage.setItem('hasErp', isHasErp);
          let hasErp = menuData.filter(item => { return systemName == '/work' ? otherPlatform.indexOf(item.path) == -1 : item.path === systemName; });
          const treeToArr = arr => {
            return arr.reduce((prev, cur) => {
              if (cur.hasOwnProperty('children')) {
                return [...prev, cur.path, ...treeToArr(cur.children)];
              } else {
                return [...prev, cur.path];
              }
            }, []);
          };
          sessionStorage.setItem(ALL_PERMISSION_MENU, JSON.stringify(treeToArr(menuData)));
          let treeMenu = treeToArr(menuData);
          let isFirst = false;
          let has = [];
          if (isWork) {
            has = treeMenu.filter(item => item != '/screen/page' && item != '/screen/jd' && item != '/aigc');
          } else {
            has = menuData.filter(item => (item.path == systemName));
          }
          if (has.length == 0) {
            message.error({
              content: (h) =>
                h('span', {}, [
                  h('span', '暂无权限，请联系管理员！'),
                  h('span', {
                    style: {
                      paddingLeft: '40px',
                      cursor: 'pointer'
                    },
                    on: {
                      click: () => {
                        message.destroy();
                      }
                    }
                  }, 'X')
                ]),
              duration: 5,
              class: 'no-platform',
              style: {
                marginTop: '5vh'
              }
            });
            reject(new Error('暂无权限，请联系管理员！'));
          } else if (hasErp.length === 0 && isWork) {
            for (let item in platformList) {
              if (treeMenu.indexOf(item) > -1 && !isFirst) {
                isFirst = true;
                window.location.href = `https://${work}${platformList[item]}/#/user/login?tenant_id=${Vue.ls.get(TENANT_ID)}&token=${Vue.ls.get(ACCESS_TOKEN)}`;
              }
            }
          }
          const authData = response.result.auth;
          const userAuths = sysButtonAuth(response.result.auth);
          sessionStorage.setItem(SYS_USER_AUTH, JSON.stringify(userAuths));

          const allAuthData = response.result.allAuth;
          sessionStorage.setItem(USER_AUTH, JSON.stringify(authData));
          sessionStorage.setItem(SYS_BUTTON_AUTH, JSON.stringify(allAuthData));
          if (systemName == '/solarCare') {
            getDeptInfo();
          }
          let menuList = [];
          let dataList = isWork ? menuData && menuData.length > 0 : hasErp[0].children && hasErp[0].children.length > 0;
          if (dataList > 0) {
            let list = isWork ? hasErp : hasErp[0].children;
            // 一级菜单的子菜单全部是隐藏路由，则一级菜单不显示
            list.forEach((item, index) => {
              if (item['children']) {
                let hasChildrenMenu = item['children'].filter((i) => {
                  return !i.hidden || i.hidden == false;
                });
                if (hasChildrenMenu == null || hasChildrenMenu.length == 0) {
                  item['hidden'] = true;
                }
              }
              if (item.children) {
                item.children.map(child => {
                  if (child.children) {
                    child.children.map(childrenlist => {
                      if (childrenlist.children) {
                        childrenlist.children.map(childrenlistss => {
                          menuList.push({
                            parent: childrenlist.path,
                            path: childrenlistss.path,
                            title: childrenlistss.meta.title
                          });
                        });
                      } else {
                        menuList.push({
                          parent: child.path,
                          path: childrenlist.path,
                          title: childrenlist.meta.title
                        });
                      }
                    });
                  } else {
                    menuList.push({
                      parent: item.path,
                      path: child.path,
                      title: child.meta.title
                    });
                  }
                });
                sessionStorage.setItem('menuList', JSON.stringify(menuList));
              }
            });
            // console.log(" menu show json ", menuData)
            // 一级菜单的子菜单全部是隐藏路由，则一级菜单不显示
            commit('SET_PERMISSIONLIST', list);
          } else {
            reject(new Error('getPermissionList: permissions must be a non-null array !'));
          }
          resolve(hasErp);
        }).catch(error => {
          reject(error);
        });
      });
    },

    // 登出
    Logout ({ commit, state }) {
      return new Promise((resolve) => {
        let logoutToken = state.token || Vue.ls.get(ACCESS_TOKEN);
        commit('SET_TOKEN', '');
        commit('SET_PERMISSIONLIST', []);
        commit('SET_PROJECT_TREE', []);
        commit('SET_REAL_STATION_TREE', []);
        commit('SET_FirstMenu', '');
        Vue.ls.remove(ACCESS_TOKEN);
        Vue.ls.remove(UI_CACHE_DB_DICT_DATA);
        Vue.ls.remove(CACHE_INCLUDED_ROUTES);

        logout(logoutToken).then(() => {
          if (process.env.VUE_APP_SSO == 'true') {
            let sevice = 'http://' + window.location.host + '/';
            let serviceUrl = encodeURIComponent(sevice);
            window.location.href = process.env.VUE_APP_CAS_BASE_URL + '/logout?service=' + serviceUrl;
          }
          window.qiankunStarted = false;
          resolve();
        }).catch(() => {
          resolve();
        });
      });
    },
    // 第三方登录
    ThirdLogin ({ commit }, param) {
      return new Promise((resolve, reject) => {
        thirdLogin(param.token, param.thirdType).then(response => {
          if (response.code == '200') {
            const result = response.result;
            const userInfo = result.userInfo;
            Vue.ls.set(ACCESS_TOKEN, result.token);
            Vue.ls.set(USER_NAME, userInfo.username);
            Vue.ls.set(USER_INFO, userInfo);
            commit('SET_TOKEN', result.token);
            commit('SET_INFO', userInfo);
            commit('SET_NAME', { username: userInfo.username, realname: userInfo.realname, welcome: welcome() });
            commit('SET_AVATAR', userInfo.avatar);
            resolve(response);
          } else {
            reject(response);
          }
        }).catch(error => {
          reject(error);
        });
      });
    },
    saveTenant ({ commit }, id) {
      Vue.ls.set(TENANT_ID, id);
      commit('SET_TENANT', id);
    },
    savehasAllData ({ commit }, id) {
      Vue.ls.set(HAS_ALL_DATA_HEALTH, id);
      commit('SET_HAS_ALL_DATA_HEALTH', id);
    },
    setNotReadNum ({ commit }, o) {
      commit('SET_NOTREADNUM', o);
    }
  }
};
// 根据主系统名称（microFromName）筛选权限按钮
function sysButtonAuth (arr) {
  let end = `_${microFromName}`;
  let auths = arr.filter(item => item.action && item.action.endsWith(end));
  return auths;
}

function getDeptInfo () {
  let userInfo = Vue.ls.get(USER_INFO);
  if (showHandle('sungrow') && showHandle('otherCompany')) {
    getDep(userInfo.id, '');
    getDep(userInfo.id, '1');
    getDep(userInfo.id, '2');
  } else if (!showHandle('sungrow') && !showHandle('otherCompany')) {
    getDep(userInfo.id, '');
  } else if (showHandle('sungrow')) {
    getDep(userInfo.id, '');
    getDep(userInfo.id, '1');
  } else if (showHandle('otherCompany')) {
    getDep(userInfo.id, '');
    getDep(userInfo.id, '2');
  }
}
function setStorageData (storageData) {
  Vue.ls.set(UPLOAD_TYPE, storageData.storageType);
  Vue.ls.set(UPLOAD_END_POINT, storageData.publicEndpoint);
  Vue.ls.set(UPLOAD_ASSCESS_KEY, storageData.accessId);
  Vue.ls.set(UPLOAD_SECRET_KEY, storageData.secretKey);
  Vue.ls.set(UPLOAD_MERGE_BUCKET, storageData.mergeBucket);
  Vue.ls.set(UPLOAD_ENVIRONMENT_PATH, storageData.environmentPath);
  Vue.ls.set(UPLOAD_FRAGMENTATION_PATH, storageData.minioFragmentationPath);
  Vue.ls.set(UPLOAD_REGION_NAME, storageData.regionname);
  Vue.ls.set(HAS_INDERPENDENT, storageData.hasIndependent);
}
function getDep (userId, companyType) {
  let map = {
    userId: userId,
    excludeHy: false,
    companyType: companyType
  };
  myDeptTree(map).then(res => {
    if (res.code == 0) {
      if (companyType == '1') {
        Vue.ls.set(SUNGROW_DEP, res.payload.result.code);
      } else if (companyType == '2') {
        Vue.ls.set(OTHER_DEP, res.payload.result.code);
      } else {
        Vue.ls.set(ALL_DEP, res.payload.result.code);
      }
    }
  });
}
function dealResult ({ commit }, result) {
  const dep = (result.hasOwnProperty('departs') && result.departs && result.departs.length > 0 ? result.departs[0] : '');
  result.userInfo.needHandover = (dep.hasOwnProperty('needHandover') ? dep.needHandover : '');
  result.userInfo.hasAllData = (dep.hasOwnProperty('hasAllData') ? dep.hasAllData : '');
  const userInfo = result.userInfo;
  userInfo.depId = dep.hasOwnProperty('id') ? dep.id : '';
  userInfo.dataRoles = result.dataRoles;
  // Vue.ls.set(ACCESS_TOKEN, result.token, 7 * 24 * 60 * 60 * 1000)
  Vue.ls.set(ACCESS_TOKEN, result.token);
  Vue.ls.set(USER_NAME, userInfo.username);
  Vue.ls.set(USER_INFO, userInfo);
  Vue.ls.set(UI_CACHE_DB_DICT_DATA, result.sysAllDictItems);
  Vue.ls.set(USER_LCA, result.lca);
  Vue.ls.set(USER_DEPT_LCA, result.lca4Dept);
  Vue.ls.set(DEFAULT_TOP_DEPART, result.root.orgCode);
  Vue.ls.set('firstTimeLogin', result.firstTimeLogin);
  // Vue.ls.set('user_data_roles', result.dataRoles)
  commit('SET_TOKEN', result.token);
  commit('SET_INFO', userInfo);
  commit('SET_NAME', { username: userInfo.username, realname: userInfo.realname, welcome: welcome() });
  commit('SET_AVATAR', userInfo.avatar);
  commit('SET_FirstTimeLogin', result.firstTimeLogin);
}
export default user;
