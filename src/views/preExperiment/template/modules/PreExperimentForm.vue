<template>
  <a-spin size="small" :spinning="loading" style="height: 100%">
    <div class="drawer-form-com">
      <div class="drawer-form-content">
          <a-form-model
            :model="formData"
            ref="proExperimentForm"
            :labelCol="{ style: 'width: 150px' }"
            :wrapperCol="{ style: 'width: calc(100% - 150px)' }"
          >
            <TitleItem title-lable='设备信息'/>
            <a-row>
              <a-col :xl="8" :sm="12" :xs="24">
                <a-form-model-item label="设备类型"  prop="deviceTypeId" :rules="{ required: true, message: '此项为必填项' }">
                  <a-cascader
                    :disabled="type === '2'"
                    v-model="formData.deviceTypeId"
                    :options="deviceTypeList"
                    style="width: 100%"
                    :fieldNames="{label: 'deviceName', value: 'id', children: 'childErpDeviceTypeInfo'}"
                    placeholder="请选择"
                    :show-search='true'
                    @popupVisibleChange="handlePopupVisibleChange"
                    change-on-select
                  ></a-cascader>
                </a-form-model-item>
              </a-col>
            </a-row>
           <div class='erperiment-operation'>
             <TitleItem title-lable='试验信息'/>
              <div class='right-operation-box'>
                <throttle-button size='small' label="预览" class="solar-eye-btn-primary-cancel" @click='handlePreview'/>
                <throttle-button size='small' label='新增项目' @click='addProExperiment'/>
              </div>
           </div>
            <ExperimentInfoItem
              v-for='(item,index) in formData.testProjectList'
              :experimentLen='formData.testProjectList.length'
              :experimentItemData='item'
              :projectIndex='index'
              :key='item.key'/>
          </a-form-model>
      </div>
      <div class="drawer-form-foot" >
        <a-button type="info" :loading="loading0" @click="onSubmit('0')">暂存</a-button>
        <a-button :loading="loading1" class="ant-btn-primary" @click="onSubmit('1')">提交</a-button>
      </div>
      <drawer-view ref="proExperimentDrawerView" parentId="pro-experiment-template" />
    </div>
  </a-spin>

</template>

<!--试验项目组件-->
<script>
import TitleItem from '@views/preExperiment/modules/TitleItem.vue';
import ExperimentInfoItem from '@views/preExperiment/template/modules/ExperimentInfoItem.vue';
import { initProjectItem, initRowTitleItem, transformRequestParam, transformResponseParam } from '../js/utils';
import { getParentDeviceTypeInfoList } from '@api/isolarErp/equipment/type';
import initDict from '@/mixins/initDict';
import { addPreTemplateConfigApi, checkDeviceTypeIdApi } from '@api/isolarErp/preExperiment';
import { clone } from 'xe-utils';
export default {
  mixins: [initDict],
  components: { TitleItem, ExperimentInfoItem },
  inject: ['tarnsformDeviceFn'],
  provide () {
    return {
      addTableFn: (index) => this.addRowTitleTable(index),
      removeTableFn: (proIndex, tableIndex) => this.removeRowTitleTable(proIndex, tableIndex),
      removePreExperimentFn: (index, projectId) => this.removePreExperiment(index, projectId),
      dictFn: () => this.dictMap
    };
  },
  data () {
    return {
      type: null,
      loading: false,
      formData: {
        deviceTypeId: undefined,
        testProjectList: [
          {
            projectName: undefined,
            isEnable: 1,
            projectTitleList: [
              {
                rowTitleList: [{
                  rowTitle: undefined,
                  childRowTitleList: []
                }],
                colTitleList: [
                  {
                    colTitle: undefined,
                    colType: '3',
                    content: undefined,
                    isRequired: true
                  }
                ],
                isCanEdit: false,
                projectTableRemark: undefined,
                childRowTitleChecked: false
              }
            ]
          }
        ],
        delProjectIds: []
      },
      deviceTypeList: [],
      tableValid: false,
      loading0: false, // 暂存loading
      loading1: false // 提交loading
    };
  },
  methods: {
    reset () {
      if (this.type !== '3') {
        this.$emit('cancel');
      }
    },
    init (type, row) {
      this.type = type;
      if (type === '2') { this.transformResponse(row); }
      this.afterVisibleChange();
      return this.type === '2' ? '编辑' : '新增';
    },
    afterVisibleChange () {
      this.loading = true;
      this.getDictMap('pre_test_project_type,pre_test_project_fill_type,pre_test_project_calculate');
      getParentDeviceTypeInfoList({ 'needAllChild': '1', 'levelLimitCheck': '2' }).then(res => {
        if (res.result_code === '1' && res.result_data) {
          this.loading = false;
          this.deviceTypeList = this.tarnsformDeviceFn(res.result_data);
        } else {
          this.loading = false;
          this.deviceTypeList = [];
        }
      });
    },
    onSubmit (type) {
      if (type === '1') {
        const custormItems = document.querySelectorAll('[data-required=true]');

        this.$refs.proExperimentForm.validate(valid => {
          this.$nextTick(async () => {
            if (valid && !(custormItems && custormItems.length)) {
              const check = await this.duplicationCheck();
              if (check) this.doCommit(type);
            } else {
              this.$errorScroll();
              this.validateCustomItems(custormItems);
            }
          });
        });
      } else {
        this.$refs.proExperimentForm.validateField('deviceTypeId', (errMsg) => {
          if (!errMsg) this.doCommit(type);
          else this.$errorScroll();
        });
      }
    },
    validateCustomItems (custormItems) {
      custormItems.forEach(item => {
        const attrs = item.getAttribute('class');
        if (!(attrs && attrs.split(' ') && attrs.split(' ').includes('has-error'))) {
          item.setAttribute('class', attrs + ' has-error');
        }
        const lastChild = item.lastChild;
        const lastClass = lastChild.getAttribute('class');
        if (lastClass && lastClass !== 'ant-form-explain' && item.getAttribute('data-msg')) {
          const div = document.createElement('div');
          div.setAttribute('class', 'ant-form-explain');
          div.innerHTML = '此项为必填项';
          item.appendChild(div);
        }
      });
    },
    // 1:提交 0:暂存
    doCommit (type) {
      this.loading = true;
      this['loading' + type] = true;
      // 编辑初始化倒叙显示，暂存/提交时按照升序排列
      const cloneData = clone(this.formData, true);
      cloneData.testProjectList.reverse();
      const param = transformRequestParam(cloneData);
      param.businessId = this.formData.businessId;
      addPreTemplateConfigApi({ ...param, isCommit: type }).then(res => {
        this.loading = false;
        this['loading' + type] = false;
        if (res.result_code === '1') {
          this.$message.success((type === '1' ? '提交' : '暂存') + '成功');
          if (type === '1') this.$emit('cancel');
          else if (type === '0') this.transformResponse(res.result_data);
        }
      }).catch(() => {
        this.loading = false;
        this['loading' + type] = false;
      });
    },
    // 当前试验项目下添加行列标题表格
    addRowTitleTable (index) {
      const item = initRowTitleItem();
      this.formData.testProjectList[index].projectTitleList.push(item);
    },
    // 当前试验项目下删除行列标题表格
    removeRowTitleTable (proExperimentIndex, rowAndColTableIndex) {
      this.$confirm({
        title: '删除',
        content: '确认删除当前行标题和列标题？',
        okText: '确定',
        cancelText: '取消',
        onOk: () => {
          this.formData.testProjectList[proExperimentIndex].projectTitleList.splice(rowAndColTableIndex, 1);
        },
        onCancel: () => {}
      });
    },
    // 当前试验项目
    removePreExperiment (index, projectId) {
      this.$confirm({
        title: '删除',
        content: '确认删除当前试验项目？',
        okText: '确定',
        cancelText: '取消',
        onOk: () => {
          this.formData.testProjectList.splice(index, 1);
          const { delProjectIds } = this.formData;
          projectId && delProjectIds.push(projectId);
        },
        onCancel: () => {}
      });
    },
    // 添加试验项目
    addProExperiment () {
      const item = initProjectItem();
      this.formData.testProjectList.unshift(item);
    },
    // 预览试验报告
    handlePreview () {
      const custormItems = document.querySelectorAll('[data-required=true]');
      this.$refs.proExperimentForm.validate(valid => {
        this.$nextTick(async () => {
          if (valid && !(custormItems && custormItems.length)) {
            const check = await this.duplicationCheck();
            if (check) this.$refs.proExperimentDrawerView.init('3', this.formData, '/preExperiment/template/modules/PreviewProExperiment');
          } else {
            this.$errorScroll();
            this.validateCustomItems(custormItems);
          }
        });
      });
    },
    // 转换响应数据结构
    transformResponse (data) {
      this.formData.deviceTypeId = data.deviceTypePath.split(',').map(item => Number(item));
      this.formData.businessId = data.businessId;
      const projectList = transformResponseParam(data.projectList);
      this.formData.testProjectList = projectList;
      console.log(this.formData);
    },
    // 重复性校验
    duplicationCheck () {
      let valid = true;
      const { testProjectList } = this.formData;
      const projectNames = testProjectList.map(o => o.projectName);
      for (let i = 0; i < testProjectList.length; i++) {
        const testProject = testProjectList[i];
        if (projectNames.filter(o => o === testProject.projectName).length > 1) {
          valid = false;
          this.showNotification('duplicationCheck', this.getLabel(testProject.projectName, this.dictMap.pre_test_project_type) + '项目重复配置，请修改后提交');
          break;
        }
        for (let j = 0; j < testProject.projectTitleList.length; j++) {
          const projectTitle = testProject.projectTitleList[j];
          const allRowTitleNames = projectTitle.rowTitleList.reduce((acc, cur) => {
            acc.push(cur.rowTitle);
            const childNames = cur.childRowTitleList.map(o => o.childRowTitle);
            for (let k = 0; k < childNames.length; k++) {
              if (childNames.filter(o => o === childNames[k]).length > 1) {
                valid = false;
                this.showNotification('duplicationCheck', this.getLabel(testProject.projectName, this.dictMap.pre_test_project_type) + '项目行标题有重复，请修改后提交');
                break;
              }
            }
            return acc;
          }, []);
          for (let k = 0; k < allRowTitleNames.length; k++) {
            if (allRowTitleNames.filter(o => o === allRowTitleNames[k]).length > 1) {
              valid = false;
              this.showNotification('duplicationCheck', this.getLabel(testProject.projectName, this.dictMap.pre_test_project_type) + '项目行标题有重复，请修改后提交');
              break;
            }
          }
          const allColTitleNames = projectTitle.colTitleList.map(o => o.colTitle);
          for (let k = 0; k < allColTitleNames.length; k++) {
            if (allColTitleNames.filter(o => o === allColTitleNames[k]).length > 1) {
              valid = false;
              this.showNotification('duplicationCheck', this.getLabel(testProject.projectName, this.dictMap.pre_test_project_type) + '项目列标题有重复，请修改后提交');
              break;
            }
          }
        }
      }
      return Promise.resolve(valid);
    },
    showNotification (key, description) {
      this.$notification.close(key);
      this.$notification.error({
        'key': key,
        'message': '系统提示',
        'description': description,
        'duration': 3
      });
    },
    // 失去焦点校验设备类型是否已经添加过
    async handlePopupVisibleChange (value) {
      if (!value) {
        const { deviceTypeId: deviceTypeIdArr } = this.formData;
        const len = deviceTypeIdArr.length;
        const deviceTypeId = deviceTypeIdArr[len - 1];
        const { projectList } = transformRequestParam(this.formData);
        await checkDeviceTypeIdApi({ deviceTypeId, projectList });
      }
    }
  }
};
</script>

<style lang='less' scoped>
.drawer-form-com {
  padding: 24px 0 0;
  height: calc(100vh - 179px);
  .drawer-form-content{
    padding-right: 24px;
    //margin-bottom: 12px;
    //height: calc(100% - 67px);
    :deep(.ant-form-item) {
      display: flex;
    }
    :deep(.ant-btn.ant-btn-link:not(.not-primary)){
      color: #FF8F33;
      background-color: transparent;
      border-color: transparent;
      -webkit-box-shadow: none;
      box-shadow: none;
    }
    :deep(.ant-btn.ant-btn-link.not-primary){
      border-color: transparent;
    }
    .erperiment-operation{
      display: flex;
      justify-content: space-between;
      .right-operation-box{
        .ant-btn:not(:last-child){
          margin-right: 8px;
        }
      }
    }
    :deep(.operation-btn-cursor){
      cursor: pointer;
      &:hover{
        color: var(--zw-primary-color--default);
      }
    }
  }
}
:root[data-theme='dark']{
  .drawer-form-com{
    :deep(.ant-btn.ant-btn-link:not(.not-primary)) {
      color: #267DCF;
      .anticon {
        color: #267DCF;
      }
    }
    :deep(.ant-btn.ant-btn-link.not-primary){
      color: #fff !important;
      background-color: transparent;
      border-color: transparent;
      -webkit-box-shadow: none;
      box-shadow: none;
      .anticon {
        color: #fff;
      }
    }
    :deep(.operation-btn-cursor:hover){
      color: #267DCF;
      .anticon {
        color: #267DCF;
      }
    }
  }
}
</style>
