<template>
  <div class="wujie-route-page" id="wujieRoutePage">
    <!--单例模式，name相同则复用一个无界实例，改变url则子应用重新渲染实例到对应路由 -->
    <WujieVue width="100%" :height="isWujie ? 'calc(100vh - 120px)': '100vh'" name="common" :props="propsData" :url="workUrl"></WujieVue>
    <drawer-view ref="wujieRef" @cancel="queryData" parentId="wujieRoutePage"/>
  </div>
</template>

<script>
import hostMap from '@/wujie/hostMap';
import { mixin } from '@/wujie/mixin.js';
export default {
  mixins: [mixin],
  data () {
    return {
      propsData: {}
    };
  },
  computed: {
    workUrl () {
      const path = this.$route.path;
      let url = hostMap('//localhost:3001/') + `#${path}`;
      console.log('url', url);
      return url;
    },
    isWujie () {
      return this.$store.state.user.isWujie;
    }
  }
};
</script>
<style lang="less" scoped>
.wujie-route-page{
  width: 100%;
  height: 100%;
  margin: 0;
  padding: 0;
  box-sizing: border-box;
  overflow: hidden;
  // :deep(.wujie_iframe){
  //   width: 100%;
  //   height: 100%;
  //   margin: 0;
  //   padding: 0;
  //   box-sizing: border-box;
  //   overflow: hidden;
  // }
}
</style>
