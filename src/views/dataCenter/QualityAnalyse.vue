<template>
  <div class="data-center-quality-analyes">
    <a-breadcrumb v-if="pageLevel > 1">
      <svg-icon iconClass="position" style="margin-right:8px"></svg-icon>
      <a-breadcrumb-item v-if="pageLevel > 1"><span class="cursor-pointer" :class="{'solar-eye-hover-primary':pageLevel > 1}"
                                                    @click="changePage(1)">质量分析</span></a-breadcrumb-item>
      <a-breadcrumb-item v-if="pageLevel > 1"><span class="cursor-pointer" :class="{'solar-eye-hover-primary':pageLevel > 2}"
                                                    @click="changePage(2)">数据概览</span></a-breadcrumb-item>
      <a-breadcrumb-item v-if="pageLevel > 2"><span class="cursor-pointer" :class="{'solar-eye-hover-primary':pageLevel > 3}"
                                                    @click="changePage(3)">数据详情</span></a-breadcrumb-item>
      <a-breadcrumb-item v-if="pageLevel > 3"><span class="cursor-pointer" @click="changePage(4)">{{ pageParams.deviceName }}</span>
      </a-breadcrumb-item>

    </a-breadcrumb>
    <keep-alive>
      <QualityAnalyseHomepage v-if="nowPage == 'QualityAnalyseHomepage'" @changePage="changePage" @changePageParams="changePageParams" :pageParams="pageParams" />
    </keep-alive>

    <component v-if="nowPage == 'DataView' || nowPage == 'DeviceList'" @changePage="changePage" @changePageParams="changePageParams" :pageParams="pageParams" :is="nowPage"/>

  </div>
</template>

<script>
import DataView from './modules/qualityAnalyse/DataView'; // 数据概览页
import DeviceList from './modules/qualityAnalyse/DeviceList'; // 设备列表
import QualityAnalyseHomepage from './modules/qualityAnalyse/QualityAnalyseHomepage'; // 质量分析首页

export default {
  name: 'QualityAnalyse',
  components: {
    DataView,
    DeviceList,
    QualityAnalyseHomepage
  },
  data () {
    return {
      nowPage: 'QualityAnalyseHomepage',
      pageLevel: 1,
      pageParams: {
        psId: '',
        deviceName: '',
        defaultDate: ''
      }
    };
  },
  created () {

  },
  methods: {

    changePage (level, inverterName) {
      this.pageLevel = level;
      if (level == 1) {
        this.nowPage = 'QualityAnalyseHomepage';
      }

      if (level == 2) {
        this.nowPage = 'DataView';
      }
      if (level == 3) {
        this.nowPage = 'DeviceList';
      }
      if (level == 4) {
        this.nowPage = 'DeviceList';
      }
    },
    changePageParams (obj) {
      this.pageParams.psId = obj.psId;
      this.pageParams.deviceName = obj.deviceName;
      this.pageParams.defaultDate = obj.defaultDate;
      this.pageParams.psName = obj.psName;
    }
  }

};
</script>

<style scoped lang="less">
  .data-center-quality-analyes {

  }

  .hover-primary:hover {
    color:var(--zw-primary-color--default)!important;
    transition:.3s;
  }
</style>
