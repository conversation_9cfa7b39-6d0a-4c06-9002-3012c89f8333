<!-- 职位管理 -->
<template>
  <div class="position">
    <a-spin :spinning="pageloading" >
        <div class="solar-eye-search-model">
            <a-row :gutter="24" class="solar-eye-search-content">
              <a-col :xxl="6" :xl="8" :md="12" >
                <div class="search-item">
                <span  class="search-label" style="width:60px">职位</span>
                <a-input-search :disabled="searchDisabled" allowClear placeholder="请输入" v-model="position" @search="onSearch" />
                </div>
              </a-col>
            </a-row>
        </div>
          <div class="solar-eye-gap"></div>
        <a-col class="solar-eye-main-content">
      <!-- 操作按钮 -->
        <div class="operation" style="height: 32px">
          <div class="operation-btn">
            <erp-button label="新增" perms="910105104101" size="default" @click="doAdd" style="margin-right:10px" ></erp-button>
            <erp-button label="导出" perms="910105104102" size="default" @click="doExport"></erp-button>
          </div>
        </div>
        <vxe-table  :data="data" :height="tableHeight" ref="positionTable" align="center"
          resizable border show-overflow highlight-hover-row size="small">
          <vxe-table-column type="seq" width="80" title="序号" align="center"></vxe-table-column>
          <vxe-table-column  show-overflow="title" title="职位" min-width="160">
            <template v-slot="{ row }">
              <template v-if="row.isAdd">
                <a-select v-model="row.position" show-search :filter-option="filterOption" placeholder="请选择">
                  <a-select-option v-for="data in dictMap.position" :key="data.dataValue"  :value="data.dataValue">
                    {{ data.dataLable }}
                  </a-select-option>
                </a-select>
              </template>
              <template v-else>
                <span> {{ row.positionLabel }} </span>
              </template>
            </template>
          </vxe-table-column>
          <vxe-table-column type="html" show-overflow="title" title="对应角色" min-width="160">
            <template v-slot="{ row }">
              <template v-if="row.showSave">
                <a-select v-model="row.roleId" show-search :filter-option="filterOption" placeholder="请选择">
                  <a-select-option v-for="(role,index) in roleList" :key="index" :value="role.id">
                    {{role.roleName}}
                  </a-select-option>
                </a-select>
              </template>
              <template v-else>
                <span> {{ row.roleName }} </span>
              </template>
            </template>
          </vxe-table-column>
          <vxe-table-column fixed="right" title="操作" width="180" :resizable="false">
            <template v-slot="{ row, rowIndex }">
              <erp-button v-show="!row.showSave" perms="910105104103" title="编辑" icon="edit" size="default" type="primary" @click="doEdit(rowIndex)"></erp-button>
              <erp-button v-show="row.showSave" perms="910105104104" title="保存" icon="save" size="default" type="primary" @click="doSave(row, rowIndex)"></erp-button>
              <erp-button v-show="row.showSave" perms="910105104105" title="取消" icon="arrow-right" size="default" type="primary" @click="doCancel"></erp-button>
            </template>
          </vxe-table-column>
          <template v-slot:empty>
              <span>查询无数据</span>
          </template>
        </vxe-table>
        </a-col>
    </a-spin>
  </div>
</template>
<script>
import initDict from '@/mixins/initDict';
import { queryall } from '@/api/api';
import { getPositionList, savePosition, exportPosition } from '@/api/isolarErp/foundation/position.js';
import tableHeight from '@/mixins/tableHeightAndSearchModel';
export default {
  name: 'position',
  mixins: [initDict, tableHeight],
  data () {
    return {
      pageloading: false,
      position: '',
      searchDisabled: false,
      data: [],
      roleList: [],
      tableHeight: 600
    };
  },
  created () {
    // 获取数字字典中各字段的选项值
    this.getDictMap('position');
    this.initialRoleList();
    this.getList();
  },
  methods: {
    // 职位模糊搜索功能
    onSearch () {
      this.getList();
    },
    // 获取职位配置列表
    getList () {
      this.pageloading = true;
      getPositionList({ positionLabel: this.position }).then(res => {
        if (res.result_code == '1') {
          this.data = res.result_data;
          this.dealData(this.data);
        }
        this.pageloading = false;
      }).catch(() => {
        this.pageloading = false;
      });
    },
    // 处理初始数据，给数据加编辑、添加标志位，以及记录老的角色信息
    dealData (arr) {
      arr.forEach(ele => {
        ele.showSave = false;
        ele.isAdd = false;
        ele.oldRoleId = ele.roleId;
      });
    },
    // 获取角色列表下拉选项
    initialRoleList () {
      queryall().then((res) => {
        this.roleList = (res.success ? res.result : []);
      });
    },
    // 保存校验
    checkShowSave () {
      let flag = false;
      this.data.forEach(element => {
        if (element.showSave) {
          flag = true;
        }
      });
      return flag;
    },
    // 新增功能
    doAdd () {
      if (this.checkShowSave()) {
        this.$message.warning('请先保存当前编辑的数据！');
      } else {
        const record = {
          roleId: undefined,
          position: undefined,
          isAdd: true,
          showSave: true
        };
        this.data.unshift(record);
        this.searchDisabled = true;
      }
    },
    // 导出功能
    doExport () {
      exportPosition({ positionLabel: this.position }).then(res => {
        if (res.result_code == '1') {
          this.$downloadFile({ fileBase64Code: res.result_data.fileBase64Code, fileName: res.result_data.fileName });
        }
      }).catch(() => {
        this.pageloading = false;
      });
    },
    // 编辑功能
    doEdit (index) {
      if (this.checkShowSave()) {
        this.$message.warning('请先保存当前编辑的数据！');
      } else {
        let isList = [];
        let row = this.data[index];
        isList = this.roleList.filter(item => item.id == row.roleId);
        if (!isList.length) {
          this.data[index].roleId = undefined;
        }
        this.data[index].showSave = true;
        this.$refs.positionTable.loadData(this.data);
        this.searchDisabled = true;
      }
    },
    // 保存功能
    doSave (row, index) {
      if (row.isAdd) {
        if (!row.position) {
          this.$message.warning('请选择职位！');
          return false;
        }
        if (!row.roleId) {
          this.$message.warning('请选择对应角色！');
          return false;
        }
      }
      this.pageloading = true;
      let data = {
        position: this.data[index].position,
        oldRoleId: this.data[index].oldRoleId,
        roleId: this.data[index].roleId
      };
      savePosition(data).then(res => {
        if (res.result_code == '1') {
          this.$message.success('保存成功！');
          this.searchDisabled = false;
          this.getList();
        }
      }).catch(() => {
        this.pageloading = false;
      });
    },
    // 取消功能
    doCancel () {
      this.searchDisabled = false;
      this.getList();
    },
    // 职位、角色模糊搜索
    filterOption (input, option) {
      return (
        option.componentOptions.children[0].text.toLowerCase().indexOf(input.toLowerCase()) >= 0
      );
    }
  }
};

</script>
<style lang="less" scoped>
.position {
  .radiusBox {
    padding: 24px;
    margin-bottom: 12px;
    .btn {
      text-align: right;
      margin-bottom: 24px
    }
  }
}
</style>
