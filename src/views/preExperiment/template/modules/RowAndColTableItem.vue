<template>
   <div class="row-col-table-inner">
    <a-row type='flex' justify='end' class='edit-row'>
      <a-checkbox v-model='tableItem.isCanEdit'>支持编辑</a-checkbox>
      <span v-if='rowAndColTableLen>1' class='operation-btn-cursor delete-row' @click='removeTableFn(projectIndex,tableItemIndex)'><a-icon style='margin-right: 8px' type='minus-circle'/>删除</span>
    </a-row>
     <div class='row-col-table-item'>
       <a-row :gutter='12'>
         <a-col :span="12" class='title-box-left'>
           <div class='title-box'>
             <span class='ant-form-item-required'>行标题</span>
             <a-checkbox @change='childRowTitleChange' v-model='tableItem.childRowTitleChecked'>子标题</a-checkbox>
           </div>
         </a-col>
         <a-col :span='12' class='title-box-right'>
           <div class='title-box'>
             <span class='ant-form-item-required'>列标题</span>
           </div>
         </a-col>
       </a-row>
       <a-row :gutter='12' type='flex'>
         <a-col :span='12' class='content-box-left'>
           <div class='operation-box operation-box-left'>
             <a-row style='margin-bottom: 16px' :gutter='[16,8]' type='flex' v-for='(rowItem,rowIndex) in tableItem.rowTitleList' :key='rowIndex'>
               <a-col
                 v-error='rowItem.rowTitle'
                 :data-required='rowItem.rowTitle ? false : true'
                 :data-msg="`${projectIndex+1}-${tableItemIndex+1}`"
                 :style="`width:${tableItem.childRowTitleChecked?'50%':'100%'}`"
               >
                 <a-input :max-length='50' style='width: calc(100% - 120px)' v-model='rowItem.rowTitle' @blur="rowItem.rowTitle = $trim($event)" allow-clear placeholder='请输入行标题' />
                 <span class='btn-box'  style='width: 120px'>
                  <a-icon type="arrow-up"  @click='upRowTitle(rowIndex)' v-if="rowIndex != 0" />
                  <a-icon type="arrow-down"  @click='downRowTitle(rowIndex)' v-if="rowIndex != tableItem.rowTitleList.length - 1 && tableItem.rowTitleList.length > 1" />
                   <a-icon type="plus-circle" @click='addRowTitle' />
                   <a-icon type="minus-circle" v-if='tableItem.rowTitleList.length > 1' @click='removeRowTitle(rowIndex)' />
                 </span>
               </a-col>
               <a-col
                 :span='12'
                 :offset='childIndex===0 ? 0 : 12'
                 v-for='(childItem,childIndex) in rowItem.childRowTitleList'
                 :key='childIndex'
                 v-error='childItem.childRowTitle'
                 :data-required='childItem.childRowTitle ? false : true'
                 :data-msg="`${projectIndex+1}-${tableItemIndex+1}`"
               >
                 <a-input :max-length='50' style='width: calc(100% - 60px)' v-model='childItem.childRowTitle' @blur="childItem.childRowTitle = $trim($event)" allow-clear placeholder='请输入子标题' />
                 <span class='btn-box' style='width: 60px'>
                   <a-icon type="plus-circle" @click='addChildTitle(rowIndex)' />
                   <a-icon type="minus-circle" v-if='rowItem.childRowTitleList.length>1'  @click='removeChildTitle(rowIndex,childIndex)' />
                 </span>
               </a-col>
             </a-row>

           </div>
         </a-col>
         <a-col :span='12' class='content-box-right'>
           <div class='operation-box operation-box-right' >
                <div class='table-header'>
                  <span class='column-item' :key='index' v-for='(item,index) in columns'>{{item.title}}</span>
                </div>
                <div  class='table-body'>
                  <div v-for='(item,index) in tableItem.colTitleList' :key='index' class='row-item'>
                    <span class='data-item' v-for='(columnItem,columnItemIndex) in columns' :key='columnItemIndex'>
                      <template v-if='columnItem.dataIndex === "name"'>
                          <span v-error='item.colTitle' :data-required='item.colTitle ? false : true' :data-msg="`${projectIndex+1}-${tableItemIndex+1}`">
                            <a-input :max-length='15' v-model='item.colTitle' @blur="item.colTitle = $trim($event)" allow-clear placeholder='请输入列标题'/>
                          </span>
                      </template>
                      <template v-if='columnItem.dataIndex === "type"'>
                          <a-select v-model='item.colType' @change='val=>handleChange(val,index)'>
                            <a-select-option v-for='itemType in dictFn().pre_test_project_fill_type' :key='itemType.dataValue' :value='itemType.dataValue'>
                              {{itemType.dataLable}}
                            </a-select-option>
                          </a-select>
                      </template>
                      <div v-if='columnItem.dataIndex === "content"'>
                          <!--数值项-->
                         <template v-if="item.colType === '3'">
                              <a-input-number placeholder='请输入' disabled style='width: 80%'  v-model='item.content' />
                         </template>
                          <!--填空项-->
                          <template v-if="item.colType === '2'">
                             <a-input placeholder='请输入' disabled style='width: 80%'  v-model='item.content' />
                         </template>
                          <!--单选、多选-->
                          <template v-else-if='["4","5"].includes(item.colType)' >
                            <SelectItem :list='item.content' :projectIndex='projectIndex' :tableItemIndex='tableItemIndex' />
                          </template>
                          <!--自动计算-->
                         <template v-else-if="item.colType === '6'">
                           <div v-error='item.content' :data-required='item.content ? false : true' :data-msg="`${projectIndex+1}-${tableItemIndex+1}`">
                              <a-select :dropdownMatchSelectWidth='false' style='width: 80%;max-width: 80%' v-model='item.content' placeholder='请选择'>
                                <a-select-option v-for='item in dictFn().pre_test_project_calculate' :title='item.dataLable' :key='item.dataValue' :value='item.dataValue'>
                                   {{item.dataLable}}
                                </a-select-option>
                            </a-select>
                           </div>
                          </template>
                        <!--固定值-->
                        <template v-else-if="item.colType==='1'">
                          <div  v-error='item.content' :data-required='item.content ? false : true' :data-msg="`${projectIndex+1}-${tableItemIndex+1}`">
                            <a-input placeholder='请输入' style='width: 80%'  v-model='item.content' />
                          </div>
                        </template>
                      </div>
                      <template v-if='columnItem.dataIndex === "isRequired"'>
                          <a-checkbox :disabled="item.colType==='6'" v-model='item.isRequired'/>
                      </template>
                      <template v-if='columnItem.dataIndex === "action"'>
                        <a-button style='padding: 0' type='link' size='small' v-if='tableItem.colTitleList.length > 1' @click='removeColTitle(index)'>删除</a-button>
                      </template>
                    </span>
                  </div>
                </div>
              <div class='table-footer-btn'>
                <a-button  style='padding: 0' type='link' size='small' icon="plus" @click='addColTitle'>添加选项</a-button>
              </div>
           </div>
         </a-col>
       </a-row>
     </div>
    <a-row>
      <span class='remark'>备注</span>
      <a-textarea :auto-size="{ minRows: 3, maxRows: 7 }" v-model='tableItem.projectTableRemark' placeholder='请输入规范要求和试验条件' :max-length='500'/>
    </a-row>

     <!-- <div class="split-line" v-if='rowAndColTableLen > 1 && rowAndColTableLen!==(tableItemIndex+1)'></div> -->
   </div>

</template>
<!--行标题&子标题组件-->
<script>
import { columns } from '@views/preExperiment/template/js/utils';
import SelectItem from '@views/preExperiment/template/modules/SelectItem.vue';
export default {
  components: { SelectItem },
  inject: ['removeTableFn', 'dictFn'],
  props: {
    tableItem: {
      type: Object
    },
    projectIndex: {
      type: Number
    },
    tableItemIndex: {
      type: Number
    },
    rowAndColTableLen: {
      type: Number
    }
  },
  data () {
    return {
      columns
    };
  },
  methods: {
    addChildTitle (rowIndex) {
      this.tableItem.rowTitleList[rowIndex].childRowTitleList.push({ childRowTitle: null });
    },
    removeChildTitle (rowIndex, childIndex) {
      this.tableItem.rowTitleList[rowIndex].childRowTitleList.splice(childIndex, 1);
    },
    // 内容设置change
    handleChange (val, index) {
      if (['4', '5'].includes(val)) {
        this.tableItem.colTitleList[index].content = [undefined];
      } else {
        this.tableItem.colTitleList[index].content = undefined;
      }
      // 自动计算时不必填
      if (val === '6') {
        this.tableItem.colTitleList[index].isRequired = false;
      }
    },
    // 添加列标题
    addColTitle () {
      this.tableItem.colTitleList.push({
        colTitle: undefined,
        colType: '3',
        content: undefined,
        isRequired: true
      });
    },
    //   删除列标题
    removeColTitle (index) {
      this.tableItem.colTitleList.splice(index, 1);
    },
    // 添加行标题
    addRowTitle () {
      const childRowTitleChecked = this.tableItem.childRowTitleChecked;
      if (childRowTitleChecked) {
        this.tableItem.rowTitleList.push({
          rowTitle: null,
          childRowTitleList: [{ childRowTitle: undefined }]
        });
      } else {
        this.tableItem.rowTitleList.push({
          rowTitle: undefined,
          childRowTitleList: []
        });
      }
    },
    upRowTitle (colIndex) { // 上移行标题
      let list = this.tableItem.rowTitleList;
      list.splice(colIndex, 1, ...list.splice(colIndex - 1, 1, list[colIndex]));
      this.tableItem.rowTitleList = [...list];
    },
    downRowTitle (colIndex) { // 下移行标题
      let list = this.tableItem.rowTitleList;
      list.splice(colIndex, 1, ...list.splice(colIndex + 1, 1, list[colIndex]));
      this.tableItem.rowTitleList = [...list];
    },
    //  删除行标题
    removeRowTitle (index) {
      this.tableItem.rowTitleList.splice(index, 1);
    },
    childRowTitleChange (e) {
      const checked = e.target.checked;
      if (checked) {
        this.tableItem.rowTitleList.forEach(item => {
          item.childRowTitleList.push({ childRowTitle: undefined });
        });
      } else {
        this.tableItem.rowTitleList.forEach(item => {
          item.childRowTitleList = [];
        });
      }
    }
  }
};
</script>

<style lang='less' scoped>
  .row-col-table-inner{
    margin-bottom: 16px;
    background: var(--zw-small-area-bg-color--default);
    padding: 16px 16px 24px;
    border-radius: 3px;
    .row-col-table-item{
      .title-box-left,.title-box-right{
        .title-box{
          height: 52px;
          line-height: 52px;
          background: var(--zw-card-light-bg-color--default);
          border-radius: 3px 3px 0px 0px;
          display: flex;
          justify-content: space-between;
          padding: 0 16px;
        }
      }
      .content-box-left,.content-box-right{
        margin-top: 16px;
        .operation-box{
          height: 100%;
          border-radius: 3px;
          border: 1px solid var(--zw-border-color--default);
          max-height: 346px;
          overflow: auto;
        }
        .operation-box-left{
          padding: 16px 16px 24px;
          .btn-box{
            display: inline-block;
            padding-left: 12px;
            .anticon{
              margin: 0 5px;
              cursor: pointer;
            }
          }
        }
        .operation-box-right{
          .table-header{
            border-radius: 3px 3px 0px 0px;
            background: var(--zw-card-light-bg-color--default);
            border-bottom: 1px solid var(--zw-border-color--default);
            height: 36px;
            line-height: 36px;
            padding: 0 16px;
            font-size: 14px;
            display: grid;
            gap: 16px;
            grid-template-columns: 25% 15% 36% 10% 8%;
            position: sticky;
            top: 0px;
            z-index: 5;
            .column-item{
              display: inline-block;
              font-weight: bold;
            }
          }
          .table-body{
            padding: 12px 16px;
            .row-item{
              display: grid;
              gap: 16px;
              grid-template-columns: 25% 15% 36% 10% 8%;
              &:not(:last-child){
                margin-bottom: 16px;
              }
              .data-item{
                display: inline-block;
                line-height: 34px;
              }
            }
          }
          .table-footer-btn{
            padding: 8px 16px 24px 16px;
          }
        }
      }
    }
    .remark{
      margin-top: 20px;
      margin-bottom: 8px;
      display: inline-block;
      font-weight: 400;
      font-size: 14px;
    }
    .edit-row{
      margin-bottom: 16px;
      .delete-row{
        margin-left: 24px;
      }
    }
    .split-line {
      height: 1px;
      margin: 24px 0;
      background: var(--zw-divider-color--default);
    }
  }
</style>
