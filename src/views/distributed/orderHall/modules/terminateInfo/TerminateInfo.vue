<template>
  <div class="terminate-info">
    <detail-layout :labelList="labelList" :form="formData" title="终止信息"></detail-layout>
  </div>
</template>

<script>
import PropTypes from 'ant-design-vue/es/_util/vue-types';

export default {
  name: 'TerminateInfo',
  props: {
    formData: PropTypes.object
  },
  data () {
    return {
      labelList: [
        {
          key: 'terminateTime',
          label: '终止时间',
          span: 24
        },
        {
          key: 'terminateReason',
          label: '终止原因',
          span: 24
        }
      ]
    };
  }
};
</script>
