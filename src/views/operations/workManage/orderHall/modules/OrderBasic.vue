<template>
  <a-form-model :model="form" ref="baseForm" :rules="isEdit ? rules:{}" :labelCol="{ style:'width: 160px' }" :wrapperCol="{ style:'width: calc(100% - 160px)' }">
    <a-row>
      <template v-if='form.orderType == 1'>
        <detail-layout  :labelList="baseInfoDetail" :form="form" title="基本信息">
          <template v-slot:findUserName>
            <a-col :span="8" class="detail_layout_content">
              <span class="left">发现人</span>
              <span class="right">
                  {{ form.faultManagementList ? (form.faultManagementList[0].findUser || '--') : '--' }}
              </span>
            </a-col>
          </template>
        </detail-layout>
        <template v-if="isEdit">
          <a-col :span='16' style="padding: 16px 12px 0 16px;">
            <a-form-model-item label="备注" class="has-colon">
              <a-textarea :disabled="false" :max-length="1000" v-model="form.faultRemark" :title="form.faultRemark" style="width: 100%;" />
            </a-form-model-item>
          </a-col>
          <a-col :span='24' style="padding: 0 16px 0 16px;">
            <a-form-model-item label="附件" class="has-colon">
              <uploadFile :disabled="false" v-model="form.files" :maxNum="5" :tipIsInline="true" tip="最多上传5个文件,且上传的附件最大不超过10MB!"></uploadFile>
            </a-form-model-item>
          </a-col>
        </template>
        <a-col :span='24'>
          <div class="order-dispose">
            <div class="title-box">
              <span class="before"></span>
              <span>故障信息</span>
            </div>
          </div>
        </a-col>
        <a-col :span='24' style="padding: 0 4px 24px 24px;">
          <fault-table :data="form.faultManagementList" :columns="columns"/>
        </a-col>
      </template>
      <detail-layout v-else :labelList="noFarmBaseInfoDetail" :form="form" title="基本信息"></detail-layout>
    </a-row>
  </a-form-model>
</template>

<script>
import { getPlanTimeLimit } from '@/api/common_gy/workOrder.js';
import { hyOrderDetail } from '@api/operations/orderHall';
import uploadFile from '@/components/common_gy/uploadFiles';
import { getDeviceTypeTree, getDeviecClassByDeviceId } from '@/api/common_gy/faultManage.js';
import { TENANT_ID } from '@/store/mutation-types';
import FaultTable from './FaultTable';
export default {
  components: {
    uploadFile,
    FaultTable
  },
  props: {
    id: {
      type: [String, Number],
      default: undefined
    },
    type: {
      type: String,
      default: undefined
    },
    isFarm: {
      type: String,
      default: undefined
    }
  },
  watch: {
    id: {
      immediate: true,
      handler: function () {
        this.getOrder();
      }
    }
  },
  computed: {
    getFaultClassify () {
      let faultClassify = this.form.faultClassify;
      if (!faultClassify) {
        return '--';
      }
      return this.faultClassify[faultClassify];
    },
    isEdit () {
      return (this.form.orderSource == '4') && (this.form.flowSts < 3) && this.type != 3;
    }
  },
  data () {
    return {
      faultClassify: { '1': '一类', '2': '二类', '3': '三类', '4': '四类' },
      form: {
        planId: undefined,
        faultClassify: undefined,
        psaName: undefined,
        deviceTypeName: undefined,
        deviceName: undefined,
        deviceUuid: undefined,
        eliminateName: undefined,
        findTime: undefined,
        twoDeviceRunStsName: undefined,
        actualStartTime: undefined,
        predictPlanTimeLimit: undefined,
        faultContent: undefined,
        faultRemark: undefined,
        files: [],
        orderFrom: '',
        realPsaName: '',
        sceneCondition: '',
        deviceTypeList: []
      },
      processDefKey: '',
      rules: {
        deviceTypeList: [{
          required: true,
          message: '设备类型不能为空',
          trigger: 'change'
        }],
        predictPlanTimeLimit: [{
          required: true,
          message: '预计消除时间不能为空',
          trigger: 'blur'
        }],
        faultClassify: [{
          required: true,
          message: '缺陷类别不能为空',
          trigger: 'blur'
        }]
      },
      noFarmBaseInfoDetail: [
        { label: '工单类型', key: 'orderTypeName' },
        { label: '工单编号', key: 'orderNo' },
        { label: '电站名称', key: 'psaName' },
        { label: '电站地址', key: 'psLocation' },
        { label: '反馈人姓名', key: 'feedbackUser' },
        { label: '反馈人联系方式', key: 'feedbackUserPhone' },
        { label: '工单生成时间', key: 'createTime' },
        { label: '处理内容', key: 'work', span: 24, lineClamp: 3 }
      ],
      baseInfoDetail: [
        { label: '电站名称', key: 'psaName' },
        {
          label: '实体电站',
          key: 'realPsaName',
          func: (params) => {
            return params.orderSource == '4';
          }
        },
        { label: '电站地址', key: 'psLocation' },
        { label: '户主电话', key: 'psTelephone' },
        { label: '缺陷来源', key: 'orderFrom' },
        { label: '发现人', slot: 'findUserName' },
        { label: '预计消除时间', key: 'predictRecoverTime' },
        {
          label: '派发人',
          key: 'dispatchSingle',
          func: (params) => {
            return (params.type == '7') || (params.resolveResult && ['6', '7', '8', '9'].includes(params.orderStatus)) || Number(params.flowSts) >= 3;
          }
        },
        {
          label: '工单派发时间',
          key: 'actualStartTime',
          func: (params) => {
            return (params.type == '7') || (params.resolveResult && ['6', '7', '8', '9'].includes(params.orderStatus)) || Number(params.flowSts) >= 3;
          }
        },
        {
          label: '处理人',
          key: 'eliminateName',
          func: (params) => {
            return (params.type == '7') || (params.resolveResult && ['6', '7', '8', '9'].includes(params.orderStatus)) || Number(params.flowSts) >= 3;
          }
        },
        {
          label: '运维商',
          key: 'operationalDealers',
          func: (params) => {
            return params.isFarm == '1' && Number(params.flowSts) >= 3;
          }
        },
        {
          label: '备注',
          key: 'faultRemark',
          span: 24,
          func: () => {
            return !this.isEdit;
          }
        },
        {
          label: '附件',
          key: 'files',
          span: 24,
          type: 'file:text',
          func: () => {
            return !this.isEdit;
          }
        }
      ],
      options: [],
      deviceType: [],
      columns: [
        { field: 'faultName', title: '故障名称' },
        { field: 'faultClassifyName', title: '缺陷类别' },
        { field: 'faultContent', title: '缺陷描述' },
        { field: 'deviceName', title: '设备名称' },
        { field: 'deviceUuid', title: '设备编号' },
        { field: 'deviceTypeName', title: '设备类型' },
        { field: 'twoDeviceRunStsName', title: '设备状态' },
        { field: 'findTime', title: '发现时间' }
      ]
    };
  },
  methods: {
    // 获取工单详情
    getOrder () {
      if (this.$refs.baseForm) {
        this.$refs.baseForm.clearValidate();
      }
      if (this.id) {
        hyOrderDetail({ 'id': this.id, isFarm: this.isFarm }).then(res => {
          let result = res.result_data;
          if ((result.orderSource == '4') && (Number(result.flowSts) < 3) && this.type != 3) {
            getDeviceTypeTree({ psId: result.psId }).then((res) => {
              this.options = res.result;
            }).catch(() => {
              this.options = [];
            });
          }
          this.form = Object.assign({}, this.form, result);
          this.form.type = this.type;
          this.form.flowSts = result.flowSts ? Number(result.flowSts) : '';
          if (this.form.deviceTypeList && this.form.deviceTypeList.length > 0) {
            this.form.deviceTypeList = this.form.deviceTypeList.map(item => (item = Number(item)));
          }
          this.$emit('updateOrder', result);
        }).catch(() => {
          this.$emit('updateOrder', null);
        });
      } else {
        this.form = Object.assign(this.form, this.$options.data().form);
      }
    },
    // 设备类型change事件
    deviceTypeChange (val) {
      this.form.faultClassify = '';
      if (!Array.isArray(val) || !val.length || !this.form.psId || val.length <= 1) {
        this.form.deviceTypeList = undefined;
        return;
      }
      // 设置缺陷类别
      getDeviecClassByDeviceId({
        typeId: val[val.length - 1],
        faultType: this.form.twoDeviceRunSts
      }).then((res) => {
        this.form.faultClassify = res.result;
      });
      this.getPlanTimeLimit();
    },
    // 获取预计消除时间
    getPlanTimeLimit () {
      getPlanTimeLimit({
        deviceTypeList: this.form.deviceTypeList,
        twoDeviceRunSts: this.form.twoDeviceRunSts,
        sysTenantId: Vue.ls.get(TENANT_ID),
        findTime: this.form.findTime
      }).then(res => {
        this.form.predictPlanTimeLimit = res.result;
      });
    },
    // 校验表单返回表单数据
    getValidate () {
      let isValid = true;
      this.$refs.baseForm.validate((valid) => {
        if (valid) {
          isValid = true;
          this.$emit('updateOrder', this.form);
        } else {
          isValid = false;
        }
      });
      return isValid;
    }
  }
};
</script>

<style lang="less" scoped>
:deep(.ant-form-item) {
  width: 100%;
  display: inline-flex;
}
.has-colon {
    :deep(.ant-form-item-label > label) {
      color: #666;
      font-weight: 550;
    }
}
:root[data-theme='dark'] {
  .has-colon {
    :deep(.ant-form-item-label > label) {
      color: #fff;
    }
  }
}
</style>
