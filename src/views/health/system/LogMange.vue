<template>
  <div>
    <div class="solar-eye-search-model">
      <a-form labelAlign="left" class="solar-eye-search-content">
        <a-row :gutter="24" align="middle">
          <a-col :xxl="6" :xl="8" :md="12">
            <a-form-item label="操作描述">
              <a-input v-model="queryParams.operationDescribe" />
            </a-form-item>
          </a-col>
          <a-col :xxl="6" :xl="8" :md="12">
            <a-form-item label="创建时间：">
              <a-range-picker
                v-model="timeRange"
                format="YYYY-MM-DD"
                :disabled-date="(current) => current.isAfter(Date(), 'day')"
              />
            </a-form-item>
          </a-col>
          <a-col :xxl="6" :xl="8" :md="12">
             <a-form-item>
            <a-button class="solar-eye-btn-primary" @click="getList(1)" icon="search">查询</a-button>
             </a-form-item>
          </a-col>
        </a-row>
      </a-form>
    </div>

    <div class="solar-eye-gap"></div>
     <a-col class="solar-eye-main-content">
         <a-spin :spinning="loading">
        <vxe-table ref="msgTable" :data="dataSource" :seq-config="{startIndex: (ipagination.current - 1) * ipagination.pageSize}" resizable border align="center" :height="tableHeight + 8" size="small">
          <vxe-table-column type="seq" :width="80" title="序号"></vxe-table-column>
         <vxe-table-column show-overflow="title" :formatter="tabFormatter" field="operationDescribe" title="操作描述">
          </vxe-table-column>
           <vxe-table-column show-overflow="title" :formatter="tabFormatter" field="operationType" title="操作类型">
            <template v-slot:default="{ row }">
            <div>
              {{ row.operationType ? (row.operationType === 1 ? '新增' : row.operationType === 2 ? '修改' : '--') : '--' }}
            </div>
          </template>
          </vxe-table-column>
           <vxe-table-column show-overflow="title" :formatter="tabFormatter" field="statusName" title="操作结果">
          </vxe-table-column>
          <vxe-table-column show-overflow="title" :formatter="tabFormatter" field="operationIp" title="操作IP">
          </vxe-table-column>
           <vxe-table-column show-overflow="title" :formatter="tabFormatter" field="createUser" title="操作人员">
          </vxe-table-column>
           <vxe-table-column show-overflow="title" :formatter="tabFormatter" field="createTime" title="创建时间">
             <template v-slot:default="{ row }">
            <div>
              {{ moment(row.createTime).format('YYYY-MM-DD') }}
            </div>
          </template>
          </vxe-table-column>
        </vxe-table>
        <!--分页组件-->
        <page-pagination :pageSize="ipagination.pageSize" :current="ipagination.current" :total="ipagination.total" @size-change="pageChangeEvent"/>
      </a-spin>
     </a-col>
    <!-- table区域-end -->
  </div>
</template>
<script>
import moment from 'moment';
import { postAction } from '@/api/manage';
import tableHeight from '@/mixins/tableHeightAndSearchModel';
const url = process.env.VUE_APP_Health_BASE_URL;
export default {
  name: 'LogMange',
  data () {
    return {
      queryParams: {
        curPage: 1,
        size: 10,
        operationDescribe: '',
        startCreateTime: undefined,
        endCreateTime: undefined
      },
      timeRange: [],
      url: {
        list: '/healthSysOperateLog/listSysOperateLog'
      },
      dataSource: [],
      /* 分页参数 */
      ipagination: {
        current: 1,
        pageSize: 10,
        total: 0
      }

    };
  },
  mixins: [tableHeight],
  created () {
    let nowDate = new Date();
    let startDiagnosisTime = new Date(new Date(this.moment(nowDate).subtract(1, 'years')).getTime()); // 一年前

    let endDiagnosisTime = new Date(nowDate.getTime() - 1 * 24 * 3600 * 1000);
    this.timeRange = [this.moment(startDiagnosisTime, 'YYYY-MM-DD'), moment(endDiagnosisTime, 'YYYY-MM-DD')];
    this.getList(1);
  },
  methods: {
    moment,
    getList (arg) {
      if (arg === 1) {
        this.ipagination.current = 1;
      }
      var params = this.getQueryParams(); // 查询条件
      this.loading = true;
      postAction(url + this.url.list)
        .then((res) => {
          this.loading = true;
          postAction(url + this.url.list, params).then((res) => {
            if (res.result_code == '1') {
              this.dataSource = res.result_data.pageList;
              this.ipagination.total = res.result_data.rowCount;
            } else {
              this.$message.warning(res.result_msg);
            }
            this.setBodyMinHeight(this.dataSource.length);
            this.loading = false;
          });
        })
        .catch(() => {
          this.loading = false;
        });
    },
    getQueryParams () {
      // 获取查询条件
      var param = Object.assign(this.queryParams);
      this.queryParams.curPage = this.ipagination.current;
      this.queryParams.size = this.ipagination.pageSize;
      this.queryParams.startCreateTime = this.moment(this.timeRange[0]).format('YYYY/MM/DD');
      this.queryParams.endCreateTime = this.moment(this.timeRange[1]).format('YYYY/MM/DD');
      return param;
    },
    pageChangeEvent (current, pageSize) {
      this.ipagination.current = current;
      this.ipagination.pageSize = pageSize;
      this.getList();
    }
  }
};
</script>
<style scoped lang="less">
</style>
