<template>
  <div class="device-info">
    <a-card :body-style="{ padding: '16px' }" v-if="show('station:edit')">
      <div class="table-operator" style="margin: 32px 0 0">
        <a-button :class="{'solar-eye-btn-primary': isEdit ? false : true }" @click="changeEditStatus" >
          {{ isEdit ? "取消" : "编辑" }}</a-button>
        <a-button :class="{'solar-eye-btn-primary': isEdit ? true : false }" @click="saveData()"
          :disabled="!isEdit">保存</a-button>
      </div>
    </a-card>
    <div :class="show('station:edit') ? 'health-base-param' : 'health-base-param'">
      <a-card title="设备基本信息">
        <a-form-model ref="baseForm" :model="baseForm" :rules="isEdit ? baseFormRules : {}"
                      v-bind="{
            labelCol: {
              xs: { span: 24 },
              sm: { span: 6 },
            },
            wrapperCol: {
              xs: { span: 24 },
              sm: { offset: 1, span: 17 },
            },
          }">
          <a-row>
            <a-col :xs="23" :sm="12">
              <a-form-model-item label="设备名称" prop='deviceName'>
                <a-input v-if="isEdit && (virtualDeviceType || collectionDeviceType)" v-model="baseForm.deviceName" @change="inputChange(baseForm.deviceName, 'deviceName')"
                         @blur="baseForm.deviceName = $trim($event)" :maxLength="30" placeholder="请输入设备名称"/>
                <span v-else>{{ getLabel(sungrowObtain.deviceName, null) }}</span>
              </a-form-model-item>
            </a-col>
            <a-col :xs="23" :sm="12">
              <a-form-model-item label="设备分类" prop='deviceType'>
                <a-select v-if="isEdit && virtualDeviceType" v-model="baseForm.deviceType"
                          @change="(value, option) => selectChange(value, option, 'deviceType','deviceTypeName')"
                          placeholder="请选择" style="width: 100%;">
                  <a-select-option :value="3">并网点</a-select-option>
                  <a-select-option :value="17">单元</a-select-option>
                  <a-select-option :value="47">储能单元</a-select-option>
                </a-select>
                <span v-else>{{ getLabel(sungrowObtain.deviceTypeName, null) }}</span>
              </a-form-model-item>
            </a-col>
            <a-col :xs="23" :sm="12" v-if="deviceType == '7'">
              <a-form-model-item class="item form-item-inject" label="设备子类" prop="meterType" >
                <a-select v-if="isEdit" v-model="baseForm.meterType"
                          @change="(value, option) => selectChange(value, option, 'meterType','meterType')"
                          placeholder="请选择" :getPopupContainer="(node) => node.parentNode">
                  <a-select-option v-for="item in meterTypeOptions" :key="item.label" :value="item.label">
                    {{ item.label }}
                  </a-select-option>
                </a-select>
                <span v-else>{{ getLabel(sungrowObtain.meterType, null) }}</span>
              </a-form-model-item>
            </a-col>
            <a-col :xs="23" :sm="12" v-else>
              <a-form-model-item label="设备子类" prop='deviceSubType'>
                <a-select v-if="isEdit && !virtualDeviceType" v-model="baseForm.deviceSubType"
                          @change="(value, option) => selectChange(value, option, 'deviceSubType','deviceSubTypeName')"
                          placeholder="请选择" :getPopupContainer="(node) => node.parentNode" style="width: 100%" >
                  <a-select-option v-for="item in deviceSubTypeOptionsFilter" :key="item.value" :value="item.value">{{ item.label }}</a-select-option>
                </a-select>
                <span v-else>{{ getLabel(sungrowObtain.deviceSubTypeName, null) }}</span>
              </a-form-model-item>
            </a-col>
            <a-col :xs="23" :sm="12" v-if="baseForm.deviceSubType == 8">
              <a-form-model-item label="对应集电线">
                <a-select v-if="isEdit" v-model="baseForm.mappingPsKey"
                          @change="(value, option) => selectChange(value, option, 'mappingPsKey','mappingDeviceName')"
                          placeholder="请选择" :getPopupContainer="(node) => node.parentNode" style="width: 100%">
                  <a-select-option v-for="item in collectorWire()" :key="item.psKey" :value="item.psKey" :name='item.deviceName'>{{item.deviceName}}</a-select-option>
                </a-select>
                <span v-else>{{ getLabel(sungrowObtain.mappingDeviceName, null) }}</span>
              </a-form-model-item>
            </a-col>
            <a-col :xs="23" :sm="12">
              <a-form-model-item label="生产厂家" prop='makerId'>
                <a-select v-if="isEdit && collectionDeviceType" v-model="baseForm.makerId" @change="(value, option) => manufacturersChange(value, option)"  :filter-option="filterOption"
                          show-search :getPopupContainer="(node) => node.parentNode">
                  <a-select-option v-for="item in manufacturersList" :name="item.maker" :key="item.makerId" :value="item.makerId">
                    {{ item.maker }}
                  </a-select-option>
                </a-select>
                <span v-else>{{ getLabel(sungrowObtain.makerName, null) }}</span>
              </a-form-model-item>
            </a-col>
            <a-col :xs="23" :sm="12">
              <a-form-model-item label="设备型号" prop='modelId'>
                <a-select v-if="isEdit && collectionDeviceType" v-model="baseForm.modelId" @change="(value, option) => modelChange(value, option)"
                          :filter-option="filterOption" show-search placeholder="请选择" :getPopupContainer="(node) => node.parentNode">
                  <a-select-option v-for="item in deviceModelList" :name="item.deviceModel" :key="item.modelId" :value="item.modelId">
                    {{ item.deviceModel }}
                  </a-select-option>
                </a-select>
                <span v-else>{{ getLabel(sungrowObtain.modelName, null) }}</span>
              </a-form-model-item>
            </a-col>
            <a-col :xs="23" :sm="12">
              <a-form-model-item label="装机功率(kWp)" prop='installedPower'>
                <span>{{ getLabel(sungrowObtain.installedPower, null) }}</span>
              </a-form-model-item>
            </a-col>
            <a-col :xs="23" :sm="12">
              <a-form-model-item label="自主接入设备编号" prop='importCode'>
                <a-input-number v-if="isEdit && (virtualDeviceType || !collectionDeviceType)" v-model="baseForm.importCode" @change="inputChange(baseForm.importCode, 'importCode')"
                                 :max="999999999999999" :min="0" placeholder="请输入自主接入设备编号" style="width: 100%;"/>
                <span v-else>{{ getLabel(sungrowObtain.importCode, null) }}</span>
              </a-form-model-item>
            </a-col>
            <a-col :xs="23" :sm="12">
              <a-form-model-item label="SN号" prop='sn'>
                <span>{{ getLabel(sungrowObtain.sn, null) }}</span>
              </a-form-model-item>
            </a-col>
            <a-col :xs="23" :sm="12">
              <a-form-model-item label="设备状态" prop='physicaDeviceStatusName'>
                <span>{{ getLabel(sungrowObtain.physicaDeviceStatusName, null) }}</span>
              </a-form-model-item>
            </a-col>
            <a-col :xs="23" :sm="12">
              <a-form-model-item label="是否开启诊断" prop="deviceStatus">
                <a-select v-if="isEdit" v-model="baseForm.deviceStatus" @change="(value, option) => selectChange(value, option, 'deviceStatus','deviceStatusName')"
                          placeholder="请选择" :getPopupContainer="(node) => node.parentNode" style="width: 100%;">
                  <a-select-option :value="0" name="是">是</a-select-option>
                  <a-select-option :value="1" name="否">否</a-select-option>
                </a-select>
                <span v-else>{{ getLabel(sungrowObtain.deviceStatusName, null) }}</span>
              </a-form-model-item>
            </a-col>
          </a-row>
        </a-form-model>
      </a-card>
      <div class="solar-eye-gap"></div>

      <!--环境监测仪-->
      <template v-if="this.sungrowObtain.deviceType == '5'">
        <a-card title="设备配置信息">
          <a-form-model
            :model="baseForm"
            :ref="!baseInfo.sungrowObtain ? 'form' : ''"
            v-bind="{
            labelCol: {
              xs: { span: 24 },
              sm: { span: 11 },
            },
            wrapperCol: {
              xs: { span: 24 },
              sm: { offset: 1, span: 12 },
            },
          }"
            :class="{ is_required: !isEdit }"
          >
            <a-row>
              <a-col :xs="23" :sm="12">
                <a-form-model-item class="item form-item-inject" label="共享情况：">
                  <a-button class="solar-eye-btn-primary-line" @click="handleAction({}, isEdit)">
                    {{ sungrowObtain.shareStatus === '0' ? '未共享' : '已共享' }}{{ !isEdit ? '，点击查看' : '，点击修改' }}
                  </a-button>
                </a-form-model-item>
              </a-col>
              <a-col :xs="23" :sm="12">
                <a-form-model-item class="item form-item-inject" label="是否为主设备:">
                  <a-select
                    v-if="isEdit"
                    v-model="baseForm.isMajor"
                    :getPopupContainer="(node) => node.parentNode"
                    @change="setToPrimary"
                  >
                    <a-select-option value="0">否</a-select-option>
                    <a-select-option value="1">是</a-select-option>
                  </a-select>
                  <span v-else>{{ sungrowObtain.isMajor == '0' ? '否' : '是' }}</span>
                </a-form-model-item>
              </a-col>
            </a-row>
          </a-form-model>
        </a-card>
        <div class="solar-eye-gap"></div>
      </template>

      <template v-else>
        <a-card title="设备技术参数" class="unable-edit-from">
          <a-form-model class="unable-edit-from" v-bind="{
          labelCol: {
            xs: { span: 24 },
            sm: { span: 9},
          },
          wrapperCol: {
            xs: { span: 24 },
            sm: {offset:1, span: 12 },
          }
        }">
            <a-row>
              <a-col :xs="23" :sm="12" v-for="(item) in baseInfo.deviceModelParam" :key="item.itemCode">
                <a-form-model-item class="item form-item-inject" :label="item.itemCode">
                  <span>{{ item.itemDataValue}}</span>
                </a-form-model-item>
              </a-col>
            </a-row>
          </a-form-model>
        </a-card>
        <div class="solar-eye-gap"></div>

        <a-card title="配置信息" class="unable-edit-from">
          <div style="position: absolute; top: 10px;right: 20px" v-if="baseInfo.sungrowObtain.deviceType && [1, 4].includes(baseInfo.sungrowObtain.deviceType)">
            配置方式 <a-switch checked-children="手动" un-checked-children="AI" v-model="baseInfo.isAdjust" :disabled="!isEdit" />
          </div>
          <a-form-model v-if="baseInfo.seriesEnableConfig" v-bind="{
              labelCol: {
                xs: { span: 24 },
                sm: { span: 9},
              },
              wrapperCol: {
                xs: { span: 24 },
                sm: {offset:1, span: 12 },
              }
            }">
            <a-row>
              <a-col :xs="23" :sm="4" v-for="item in baseInfo.seriesEnableConfig" :key="item.name">
                <a-form-model-item class="item form-item-inject" :label="item.name">
                  <a-switch checked-children="开" un-checked-children="关" :disabled="!isEdit || item.isEnable==0 || !baseInfo.isAdjust" v-model="item.value" />
                </a-form-model-item>
              </a-col>
            </a-row>
          </a-form-model>
        </a-card>
      </template>
    </div>
    <share-station-drawer
      ref="drawerForm"
      @listenChange="getShareStation"
      :deviceName="deviceTypeKey"
    ></share-station-drawer>
  </div>
</template>
<script>
import baseInfo from '../../mixins/baseInfo';
import { DeviceMixins } from '../../mixins/device.js';
import { selectDeviceConfig } from '@api/health/healthapi';
import ShareStationDrawer from './ShareStationDrawer';
export default {
  name: 'DeviceInfo',
  mixins: [ baseInfo, DeviceMixins ],
  components: { ShareStationDrawer },
  props: {
    isLocal: {
      type: Boolean,
      default: true
    },
    deviceType: {
      type: String,
      default: ''
    }
  },
  inject: ['collectorWire', 'selectNode'],
  data () {
    return {
      isEdit: false,
      baseForm: {},
      baseFormRules: {
        deviceName: [{
          required: false,
          message: '请输入设备名称'
        }],
        deviceType: [{
          required: false,
          message: '请选择设备分称'
        }],
        meterType: [{
          required: true,
          trigger: 'change',
          message: '请选择设备子类'
        }],
        deviceSubType: [{
          required: false,
          message: '请选择设备子类'
        }],
        makerId: [{
          required: false,
          message: '请选择生产厂商'
        }],
        modelId: [{
          required: false,
          message: '请选择设备型号'
        }],
        importCode: [{
          required: false,
          message: '请填写自主接入设备编号'
        }],
        sn: [{
          required: false,
          message: '请填写SN号'
        }],
        deviceStatus: [{
          required: false,
          message: '请选择是否开启诊断'
        }]
      },
      deviceSubTypeOptions: [
        { label: '主变间隔', value: '1', deviceType: '4' },
        { label: '接地变设备（含间隔）', value: '2', deviceType: '6' },
        { label: '接地变设备', value: '3', deviceType: '6' },
        { label: '箱变设备', value: '4', deviceType: '6' },
        { label: '出线PT', value: '5', deviceType: '30' },
        { label: '母线PT间隔', value: '6', deviceType: '30' },
        { label: '出线间隔', value: '7', deviceType: '12' },
        { label: '集电线间隔', value: '8', deviceType: '12' },
        { label: '低压并网柜', value: '9', deviceType: '12' },
        { label: '接地变间隔', value: '10', deviceType: '12' },
        { label: 'SVG间隔', value: '11', deviceType: '12' },
        { label: 'SVG设备（含间隔）', value: '12', deviceType: '29' },
        { label: 'SVG设备', value: '13', deviceType: '29' },
        { label: '集中逆变器', value: '14', deviceType: '1' },
        { label: '组串逆变器', value: '15', deviceType: '1' }
      ],
      meterTypeOptions: [
        { label: '关口', value: '1' },
        { label: '集电线', value: '2' },
        { label: '站用变', value: '3' },
        { label: 'SVG', value: '4' },
        { label: '备用变', value: '5' },
        { label: '母线', value: '6' }
      ]
    };
  },
  created () {
  },
  computed: {
    // 基本信息
    sungrowObtain () {
      return this.baseInfo.sungrowObtain;
    },
    // 虚拟设备
    virtualDeviceType () {
      return [3, 17, 47].includes(this.baseInfo.sungrowObtain.deviceType);
    },
    // 采集设备
    collectionDeviceType () {
      return this.isLocal == '1';
    },
    // 设备子类
    deviceSubTypeOptionsFilter () {
      return this.deviceSubTypeOptions.filter(item => item.deviceType == this.baseInfo.sungrowObtain.deviceType);
    },
    deviceTypeKey () {
      return this.baseInfo.sungrowObtain.deviceName || this.selectNode().deviceName;
    }
  },
  methods: {
    handleAction (obj, isEdit) {
      this.$refs.drawerForm.initDrawer(
        { sharePsList: this.baseInfo.sungrowObtain.sharePsList },
        { visible: true, type: isEdit ? 'edit' : 'view', title: isEdit ? '编辑' : '查看' }
      );
    },
    getShareStation (psList) {
      this.baseInfo.sungrowObtain.sharePsList = psList.join(',');
    },
    async saveData () {
      let valid = await this.$refs.baseForm.validate().catch(() => {});
      if (valid) {
        this.$confirm({
          title: '提示',
          content: '是否确认修改？',
          okText: '确定',
          cancelText: '取消',
          onOk: () => {
            let data = {
              ...this.baseInfo.sungrowObtain,
              isEdit: 0,
              isStation: false
            };
            if (this.baseInfo.seriesEnableConfig) {
              data.seriesEnableConfigAutoSource = JSON.parse(JSON.stringify(this.baseInfo.seriesEnableConfig));
              data.isAdjust = this.baseInfo.isAdjust ? 1 : 0;
              data.seriesEnableConfigAutoSource.map(item => {
                if (typeof item.value == 'boolean') {
                  item.value = item.value ? 1 : 0;
                }
              });
            }
            this.$emit('saveData', data, this.baseInfo.sungrowObtain.deviceName, this.baseInfo.sungrowObtain.deviceStatus);
            this.$refs.baseForm && this.$refs.baseForm.clearValidate();
          }
        });
      }
    },
    changeEditStatus () {
      let storeForm = JSON.parse(this.storageForm);
      this.baseInfo.sungrowObtain = storeForm.sungrowObtain;
      if (storeForm.seriesEnableConfig) {
        this.baseInfo.seriesEnableConfig = storeForm.seriesEnableConfig;
      }
      this.$refs.baseForm && this.$refs.baseForm.clearValidate();
      this.isEdit = !this.isEdit;
      if (this.isEdit) {
        this.setBaseForm();
        this.getManufacturersList(this.deviceType);
        this.getDeviceModelList(this.deviceType, storeForm.sungrowObtain.makerId, storeForm.sungrowObtain.makerId);
      }
    },
    // 生产厂商变化事件
    manufacturersChange (val, option) {
      this.baseForm.modelId = undefined;
      this.selectChange(val, option, 'makerId', 'makerName');
      this.baseInfo.sungrowObtain.modelId = undefined;
      this.baseInfo.sungrowObtain.modelName = undefined;
      this.getDeviceModelList(this.baseForm.deviceType, val, val);
    },
    // 设备型号变化事件
    modelChange (val, option) {
      this.selectChange(val, option, 'modelId', 'modelName');
      this.setConfigInfo();
    },
    selectChange (val, option, id, name) {
      this.baseInfo.sungrowObtain[name] = option ? option.data.attrs.name : undefined;
      this.baseInfo.sungrowObtain[id] = val;
      if (this.psInfo().deviceType == '12' && this.baseForm.deviceSubType != '8') {
        this.baseInfo.sungrowObtain.mappingPsKey = undefined;
        this.baseInfo.sungrowObtain.mappingDeviceName = undefined;
      }
    },
    inputChange (val, name) {
      this.baseInfo.sungrowObtain[name] = val;
    },
    // 版本ID变化，重新生成配置信息
    setConfigInfo () {
      if (this.baseForm.modelId) {
        let obj = {
          deviceType: this.deviceType,
          modelId: this.baseForm.modelId,
          psId: this.provideData.psId,
          psKey: this.provideData.psKey,
          source: '0'
        };
        selectDeviceConfig(obj).then(res => {
          if (res.result_code == '1') {
            this.baseInfo.seriesEnableConfig = res.result_data.seriesEnableConfig;
            this.baseInfo.seriesEnableConfig.map(item => {
              item.value = item.value == 1;
            });
          } else {
            this.baseInfo.seriesEnableConfig = [];
          }
        }).catch(() => {
          this.baseInfo.seriesEnableConfig = [];
        });
      } else {
        this.baseInfo.seriesEnableConfig = [];
      }
    },
    setBaseForm () {
      this.baseForm = {
        deviceName: this.baseInfo.sungrowObtain.deviceName,
        deviceType: this.baseInfo.sungrowObtain.deviceType,
        meterType: this.baseInfo.sungrowObtain.meterType,
        deviceSubType: this.baseInfo.sungrowObtain.deviceSubType,
        mappingPsKey: this.baseInfo.sungrowObtain.mappingPsKey,
        makerId: this.baseInfo.sungrowObtain.makerId,
        modelId: this.baseInfo.sungrowObtain.modelId,
        importCode: this.baseInfo.sungrowObtain.importCode,
        sn: this.baseInfo.sungrowObtain.sn,
        deviceStatus: this.baseInfo.sungrowObtain.deviceStatus,
        shareStatus: this.baseInfo.sungrowObtain.shareStatus,
        isMajor: this.baseInfo.sungrowObtain.isMajor
      };
      this.baseFormRules.deviceName[0].required = this.virtualDeviceType || this.collectionDeviceType;
      this.baseFormRules.deviceType[0].required = this.virtualDeviceType;
      this.baseFormRules.makerId[0].required = this.collectionDeviceType;
      this.baseFormRules.modelId[0].required = this.collectionDeviceType;
      // this.baseFormRules.importCode[0].required = this.virtualDeviceType;
      this.baseFormRules.deviceStatus[0].required = this.collectionDeviceType;
    },
    setToPrimary (value) {
      let that = this;

      this.$confirm({
        content: (h) => (
          <span>
            {' '}
            本站是否将<span style="font-weight:bold"> &nbsp;&nbsp;{that.deviceTypeKey} &nbsp;&nbsp;</span>
            {value === '1' ? '设为' : '取消'}主环境监测仪设备，请确认？
          </span>
        ),
        title: '',
        okText: '确定',
        cancelText: '取消',
        onOk () {
          that.baseInfo.sungrowObtain.isMajor = value;
        },
        onCancel () {
          that.baseForm.isMajor = value === '1' ? '0' : '1';
        }
      });
    }
  },
  beforeDestroy () {
    this.$refs.baseForm && this.$refs.baseForm.clearValidate();
  }
};
</script>
<style lang="less" scoped>
.device-info {
  height: 100%;
}
.unable-edit-from {
  :deep(.form-item-inject) {
    margin-bottom: 0;
  }
}
.health-base-param {
  max-height: calc(100vh - 238px);
  min-height: calc(100vh - 238px);
}
</style>
