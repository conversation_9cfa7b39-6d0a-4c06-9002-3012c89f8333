<template>
  <div>
    <a-row :gutter="16">
      <a-col :md="18" :sm="24">
        <div class="solar-eye-search-model">
          <SearchModule :toggleSearchStatus="toggleSearchStatus" @clickSearch="clickSearch" @handleSearch="handleToggleSearch" :loading="loadTable" />
        </div>
        <div class="solar-eye-gap"></div>
        <a-col class="solar-eye-main-content">
          <div class="operation" style="height: 32px">
            <div class="operation-btn">
              <!-- 操作按钮区域 -->
              已勾选{{ selectedRowKeys.length }}电站
              <throttle-button
                label="导出"
                @click="exportExcel"
                perms="diagnosticAlgorithm:export"
                :loading="exportLoading"
                class="m-l-16 solar-eye-btn-primary-cancel"
                :disabled="$isEmpty(dataSource)"
              />
            </div>
          </div>
          <!-- table区域-begin -->
          <a-spin :spinning="loadTable">
            <vxe-table
              :data="dataSource"
              ref="multipleTable"
              class="my-table"
              @sort-change="handleTableSortChange"
              :sort-config="{ remote: true }"
              resizable
              show-overflow
              highlight-hover-row
              size="small"
              @checkbox-all="onSelectChange"
              @checkbox-change="onSelectChange"
              :height="tableHeight - 24"
              @cell-click="cellClickEvent"
              :row-class-name="rowClassName"
            >
              <vxe-table-column type="checkbox" width="80" align="center"></vxe-table-column>
              <vxe-table-column
                show-overflow="title"
                :formatter="tabFormatter"
                title="电站名称"
                min-width="160"
                field="psName"
              >
                <template slot-scope="scope">
                  <div style="line-height: 40px" :class="{ isSinglePS: scope.row.psId == defautlPsId }">
                    {{ scope.row.psName }}
                  </div>
                </template>
              </vxe-table-column>

              <vxe-table-column
                show-overflow="title"
                :formatter="tabFormatter"
                title="算法状态"
                field="algorithmFlagName"
                min-width="160"
              >
                <template slot-scope="scope">
                  <div style="line-height: 40px">
                    <span
                      :class="[(scope.row.algorithmFlag == '1' ? 'open': scope.row.algorithmFlag == '2' ? 'warning' : 'close') + '-circle-color']"
                      class="open-circle"
                    ></span>
                    {{ scope.row.algorithmFlagName }}
                  </div>
                </template>
              </vxe-table-column>

              <template v-slot:empty>
                <span>查询无数据</span>
              </template>
            </vxe-table>
            <page-pagination
              :pageSize="queryParams.size"
              :current="queryParams.curPage"
              :total="total"
              @size-change="sizeChange"
            />
          </a-spin>
        </a-col>
        <!-- table区域-end -->
      </a-col>
      <a-col :md="6" :sm="24">
        <a-card
          class="solar-eye-card-title-40 bottom-auto-height"
          :bordered="false"
          title="算法"
          style="overflow: auto !important"
        >
          <span slot="extra" class="color-text-white pointer" @click="handleLeafsAllExpand">{{
            leafsAllExpand ? '收起' : '展开'
          }}<svg-icon icon-class="up" :style="`transform: rotate(${leafsAllExpand ? 0 : 180}deg)`" /></span>
          <div class="check-all top">
            <a-checkbox :indeterminate="indeterminate" :checked="checkAll" @change="onCheckAllChange">
              全部
            </a-checkbox>
          </div>
          <div class="algorithm bottom">
            <a-tree
              v-model="selectAlgorithmList"
              checkable
              :expandedKeys="expandedKeys"
              :tree-data="treeData"
              @check="onChange"
              @expand="handleExpandClick"
            >
            </a-tree>
            <!-- <a-checkbox-group v-model="selectAlgorithmList" @change="onChange"
              :style="{  width: '100%' }">
              <a-checkbox v-for="v in algorithmList" :key="v.secondTypeCode" :value="v.secondTypeCode"
                class="algorithm-100">{{ v.secondName }}
              </a-checkbox>
            </a-checkbox-group> -->
          </div>
          <div class="flex-center flex-gap-12 m-t-12">
            <throttle-button label="重置" class="solar-eye-btn-primary-cancel" @click="handleTreeReset" />
            <throttle-button label="保存" :loading="saveLoading" @click="saveConfig" />
          </div>
        </a-card>
      </a-col>
    </a-row>
  </div>
</template>
<script>
import {
  searchPowerStation,
  getOpenAlgorithm,
  addAlgorithmPowerStation,
  downloadOpenAlgorithmMsg,
  getHealthSystemCodeForGradeApi
} from '@/api/health/healthapi.js';
import initDict from '@/mixins/initDict';
import tableHeight from '@/mixins/tableHeightAndSearchModel';
import downBigExcel from '@/mixins/downBigExcel';
import SearchModule from './modules/SearchModule';
import { toArrayTree } from 'xe-utils';

let innerHeight = window.innerHeight - 60 - 40 - 24;
export default {
  name: 'diagnosticAlgorithm',
  components: { SearchModule },
  data () {
    return {
      psTypeOptions: [],
      queryParams: {
        psCategoryLabels: [],
        depCodes: undefined,
        ownerId: undefined,
        ownerProjectId: undefined,
        treePsaIds: [],
        province: undefined,
        city: undefined,
        counties: undefined,
        deptIds: [],
        startTimeOfStart: undefined,
        startTimeOfEnd: undefined,
        psIds: [],
        algorithmFlag: undefined,
        elecEnsureType: ['0'],
        curPage: 1,
        pageSize: 10,
        validFlag: '1'
      },
      psId: '',
      loadTable: false,
      dataSource: [],
      total: '',
      height: innerHeight > 700 ? innerHeight : 800,
      selectedRowKeys: [],
      selectionRows: [],
      defautlPsId: '',
      selectAlgorithmList: [],
      algorithmList: [],
      treeData: [],
      indeterminate: false,
      checkAll: false,
      expandedKeys: [],
      exportLoading: false, // 导出文件loading
      leafsAllExpand: false,
      saveLoading: false
    };
  },
  mixins: [initDict, tableHeight, downBigExcel],
  created () {
    this.loadTable = true;
    this.getAlgorithmArr();
  },
  beforeDestroy () {},
  mounted () {},
  methods: {
    getAlgorithmArr () {
      getHealthSystemCodeForGradeApi({ firstTypeCode: '0316', level: 3 }).then((res) => {
        this.algorithmList = res.map((item) => {
          return {
            key: item.secondTypeCode,
            title: item.secondName,
            pid: item.pid
          };
        });
        // this.expandedKeys = this.algorithmList.map(item => { return item.key; });
        this.treeData = toArrayTree(this.algorithmList, { parentKey: 'pid', key: 'key' });
      });
    },
    // 获取列表数据
    getData (flag) {
      this.loadTable = true;
      this.dataSource = [];
      if (flag) {
        this.queryParams.curPage = 1;
      }
      this.queryParams.order = 'ps_id desc';
      let params = Object.assign({}, this.queryParams);
      // 电站保证类型改为多选
      params.elecEnsureType = params.elecEnsureType.join();
      searchPowerStation(params)
        .then((res) => {
          if (res.result_code === '1') {
            let dataList = res.result_data.pageList;
            this.dataSource = dataList;
            this.total = res.result_data.rowCount;
          }
          this.loadTable = false;
        })
        .catch(() => {
          this.loadTable = false;
        });
    },
    // 点击搜索按钮
    clickSearch (params) {
      Object.assign(this.queryParams, params);
      this.queryParams.curPage = 1;
      this.getData();
    },
    sizeChange (current, size) {
      this.queryParams.size = size;
      this.queryParams.curPage = current;
      this.getData();
    },
    // 表格复选框变化事件
    onSelectChange (val) {
      let arr = [];
      val.records.forEach((item) => {
        arr.push(item.psId);
      });
      this.selectAlgorithmList = [];
      this.checkAll = false;
      this.indeterminate = false;
      if (arr.length == 1) {
        this.defautlPsId = arr[0];
      } else {
        this.defautlPsId = '';
      }
      if (this.defautlPsId) {
        this.getOpenAlgorithm();
      }
      this.selectedRowKeys = arr;
      this.selectionRows = val.selection;
    },
    cellClickEvent ({ row, column }) {
      if (column.type !== 'checkbox' && this.selectedRowKeys.length <= 1) {
        this.initSelectTable(row);
      }
    },
    getOpenAlgorithm () {
      // 获取单个电站的已配置算法
      getOpenAlgorithm({
        psId: this.defautlPsId,
        firstTypeCode: '0316'
      }).then((res) => {
        if (res.result_code == '1') {
          let data = res.result_data;
          this.selectAlgorithmList = data;
          this.updateExpandedKeys(this.selectAlgorithmList);
          this.onChange(data);
        } else {
          this.$message.error(res.result_msg);
        }
      });
    },
    // 递归查找一个 key 的路径
    findPath(tree, targetKey, path = []) {
      for (const node of tree) {
        const currentPath = [...path, node.key];
        if (node.key === targetKey) {
          return currentPath;
        }
        if (node.children) {
          const result = this.findPath(node.children, targetKey, currentPath);
          if (result) return result;
        }
      }
      return null;
    },

    // 根据选中的 key 列表更新 expandedKeys
    updateExpandedKeys(selectedKeys) {
      const expandedKeySet = new Set();
      for (const key of selectedKeys) {
        const path = this.findPath(this.treeData, key);
        if (path && path.length > 1) {
          // 除了最后一个 key（选中节点），其他都是父节点
          path.slice(0, -1).forEach(k => expandedKeySet.add(k));
        }
      }
      this.expandedKeys = Array.from(expandedKeySet);
    },
    initSelectTable (row) {
      if (this.$refs.multipleTable) {
        this.$refs.multipleTable.clearCheckboxRow();
      }
      this.selectionRows = [];
      this.selectedRowKeys = [];
      this.defautlPsId = row.psId;
      this.getOpenAlgorithm();
    },
    onChange (checkedList) {
      // 从checkedList中过滤掉父节点
      const onlyChildLeafs = checkedList.filter((item) => {
        let data = this.algorithmList.find((ele) => {
          return ele.key == item;
        });
        let childrenData = this.algorithmList.find((ele) => {
          return ele.pid == item;
        });
        return data && !childrenData;
      });
      // 从algorithmList中过滤掉父节点，只保留没有children的子节点
      const allChildLeafs = this.algorithmList.filter((item) => {
        let childrenData = this.algorithmList.find((ele) => {
          return ele.pid == item.key;
        });
        return item && !childrenData;
      });
      this.indeterminate = !!onlyChildLeafs.length && onlyChildLeafs.length < allChildLeafs.length;
      this.checkAll = onlyChildLeafs.length && onlyChildLeafs.length === allChildLeafs.length;
    },
    onCheckAllChange (e) {
      Object.assign(this, {
        selectAlgorithmList: e.target.checked ? this.algorithmList.map((o) => o.key) : [],
        indeterminate: false,
        checkAll: e.target.checked
      });
    },
    saveConfig () {
      this.saveLoading = true;
      let arr = this.selectedRowKeys.join(',');
      let params = {
        psId: this.defautlPsId ? this.defautlPsId : arr,
        algorithmIds: this.getLlgorithmIds()
      };
      addAlgorithmPowerStation(params).then((res) => {
        if (res.result_code == '1') {
          if (res.result_msg == '062') {
            downloadOpenAlgorithmMsg(params).then((res) => {
              if (res.result_code == '1') {
                this.$downloadFile({ fileBase64Code: res.result_data.strBase64, fileName: res.result_data.fileName });
              }
            });
            this.$notification.error({
              message: '系统提示',
              description: '存在未开启成功电站',
              duration: 4
            });
          } else {
            this.$message.success('配置成功');
            this.getData(true);
          }
          this.saveLoading = false;
        } else {
          this.$message.error(res.result_msg);
          this.saveLoading = false;
        }
      });
    },
    getLlgorithmIds () {
      let arr = [];
      this.selectAlgorithmList.forEach((item) => {
        let data = this.algorithmList.find((ele) => {
          return ele.key == item;
        });
        let childrenData = this.algorithmList.find((ele) => {
          return ele.pid == item;
        });
        if (data && !childrenData) {
          arr.push(item);
        }
      });
      return arr;
    },
    // 导出文件
    exportExcel () {
      let params = Object.assign({}, this.queryParams);
      // 电站保证类型改为多选
      params.elecEnsureType = params.elecEnsureType.join();
      this.exportExcelEvent('exportLoading', '600001', params);
    },
    // 算法树展开/收起
    handleExpandClick (expandedKeys, { expanded, node }) {
      this.expandedKeys = expandedKeys;
    },
    handleTreeReset () {
      this.selectAlgorithmList = [];
      this.indeterminate = false;
      this.checkAll = false;
    },
    handleLeafsAllExpand () {
      this.leafsAllExpand = !this.leafsAllExpand;
      if (this.leafsAllExpand) {
        const keys = this.algorithmList.map((o) => o.key);
        this.expandedKeys = keys;
      } else {
        this.expandedKeys = [];
      }
    },
    rowClassName({ row }) {
      return row.psId === this.defautlPsId ? 'vxe-table-row-selected' : '';
    }
  }
};
</script>
<style lang="less" scoped>
.check-all {
  padding-left: 2px;
}

.algorithm {
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
  overflow: auto;

  .algorithm-100 {
    width: 100%;
    line-height: 36px;
  }

  .algorithm-100 + .algorithm-100 {
    margin-left: 0 !important;
  }
}

.isSinglePS {
  color: var(--zw-primary-color--default);
}

.open-circle {
  width: 6px;
  height: 6px;
  border-radius: 6px;
  display: inline-block;
  margin-right: 8px;
}

.open-circle-color {
  background: #2ba471;
}

.close-circle-color {
  background: #ff8100;
}

.warning-circle-color {
  background: var(--zw-warning-color--normal);
}

.bottom-auto-height {
  :deep(.ant-card-body) {
    display: flex;
    flex-direction: column;
    height: calc(100vh - 176px);
  }
}

.search-title {
  margin-bottom: 0;
}
</style>
