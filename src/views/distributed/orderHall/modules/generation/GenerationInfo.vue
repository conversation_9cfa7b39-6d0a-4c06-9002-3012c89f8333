<!-- 应发电量/发电信息 -->
<template>
  <detail-layout :labelList="labelList" :form="businessData" title="发电信息" labelWidth="163px">
    <template #lossElectric>
      <a-col :span="16" class="detail_layout_content">
        <span class="left">日报损失电量(万kWh)</span>
        <span class="right">
          {{ businessData.lossElectric }}
          <a-popover v-model="visible" title="损失电量明细" trigger="click" placement="top"
            overlayClassName="generation-popover" :getPopupContainer="(node) => node.parentNode">
            <template slot="content">
              <div class="generation-popover-inner-content">
                <a-row v-if="businessData.lossElectricDetail">
                  <a-col :span="24" class="m-b-8">
                    <span>是否有站内故障：{{ businessData.lossElectricDetail.haveInsideFault ? '有' : '否' }}</span>
                  </a-col>
                  <template v-if="businessData.lossElectricDetail.haveInsideFault">
                    <a-col :span="12" class="m-b-8">
                      <span>站内故障损失电量(万kWh)：{{ businessData.lossElectricDetail.insideFaultLossElectric }}</span>
                    </a-col>
                    <a-col :span="12" class="m-b-8">
                      <span>站内故障可核减电量(万kWh)：{{ businessData.lossElectricDetail.insideFaultLossCutElectric }}</span>
                    </a-col>
                    <a-col :span="24" class="m-b-8">
                      站内故障损失原因：{{ businessData.lossElectricDetail.insideFaultLossReason }}
                    </a-col>
                  </template>
                  <a-col :span="24" class="m-b-8">
                    <span>是否有限电损失：{{ businessData.lossElectricDetail.haveRationLoss ? '有' : '否' }}</span>
                  </a-col>
                  <template v-if="businessData.lossElectricDetail.haveRationLoss">
                    <a-col :span="12" class="m-b-8">
                      <span>限电损失电量(万kWh)：{{ businessData.lossElectricDetail.rationLossElectric }}</span>
                    </a-col>
                    <a-col :span="12" class="m-b-8">
                      <span>限电损失可核减电量(万kWh)：{{ businessData.lossElectricDetail.rationLossCutElectric }}</span>
                    </a-col>
                    <a-col :span="24" class="m-b-8">
                      限电损失原因：{{ businessData.lossElectricDetail.rationLossReason }}
                    </a-col>
                  </template>
                  <a-col :span="24" class="m-b-8">
                    <span>是否有受累损失：{{ businessData.lossElectricDetail.haveTiredLoss ? '有' : '否' }}</span>
                  </a-col>
                  <template v-if="businessData.lossElectricDetail.haveTiredLoss">
                    <a-col :span="12" class="m-b-8">
                      <span>受累损失电量(万kWh)：{{ businessData.lossElectricDetail.tiredLossElectric }}</span>
                    </a-col>
                    <a-col :span="12" class="m-b-8">
                      <span>受累设备可核减电量(万kWh)：{{ businessData.lossElectricDetail.tiredLossCutElectric }}</span>
                    </a-col>
                    <a-col :span="24" class="m-b-8">
                      受累损失核减原因：{{ businessData.lossElectricDetail.tiredLossReason }}
                    </a-col>
                  </template>
                  <a-col :span="24" class="m-b-8">
                    <span>是否有计划停运：{{ businessData.lossElectricDetail.havePlanStopLoss ? '有' : '否' }}</span>
                  </a-col>
                  <template v-if="businessData.lossElectricDetail.havePlanStopLoss">
                    <a-col :span="12" class="m-b-8">
                      <span>计划停运损失电量(万kWh)：{{ businessData.lossElectricDetail.planStopLossElectric }}</span>
                    </a-col>
                    <a-col :span="12" class="m-b-8">
                      <span>计划停运可核减电量(万kWh)：{{ businessData.lossElectricDetail.planStopLossCutElectric }}</span>
                    </a-col>
                    <a-col :span="24" class="m-b-8">
                      计划停运损失核减原因：{{ businessData.lossElectricDetail.planStopLossReason }}
                    </a-col>
                  </template>
                </a-row>
              </div>
              <div class="flex-end m-t-8">
                <throttle-button label="关闭" size="small" class="solar-eye-btn-primary-cancel"
                  @click="visible = false" />
              </div>
            </template>
            <span v-if="businessData.lossElectricDetail" class="generation-check-detail">
              查看损失明细
            </span>
          </a-popover>
        </span>
      </a-col>
    </template>

    <template #powerLossDeviation>
      <a-col :span="8" class="detail_layout_content">
        <span class="left" style="width: 190px">参考损失电量偏差(万kWh)</span>
        <span class="right" :class="{ 'color-error': businessData.powerLossDeviation }">
          {{ businessData.powerLossDeviation }}
        </span>
      </a-col>
    </template>
  </detail-layout>
</template>

<script>
export default {
  name: 'GenerationInfo',
  props: {
    businessData: {
      type: Object,
      default: () => ({
        lossElectricDetail: {}
      })
    }
  },
  data () {
    return {
      visible: false,
      labelList: [
        {
          label: '发电时间',
          key: 'producePowerDateStr'
        }, {
          label: '天气情况',
          key: 'currWeather',
          span: 16
        }, {
          label: '上网电量(万kWh)',
          key: 'onElectric'
        }, {
          slot: 'lossElectric'
        }, {
          label: '应发电量(万kWh)',
          key: 'shouldElectric'
        }, {
          label: '参考损失电量(万kWh)',
          key: 'actualLossElectric'
        }, {
          slot: 'powerLossDeviation'
        }
      ]
    };
  }

};
</script>

<style lang="less" scoped>
.color-error {
  color: #EA0000;
}

.generation-check-detail {
  cursor: pointer;
  color: #267DCF;
  margin-left: 16px;
  text-decoration: underline;
  text-underline-offset: 5px;
}

.generation-popover {
  .ant-popover-title {
    border-bottom: none;
    font-weight: 600;
  }

  .generation-popover-inner-content {
    max-width: 520px;
    max-height: 180px;
    overflow: auto;
  }
}
</style>
