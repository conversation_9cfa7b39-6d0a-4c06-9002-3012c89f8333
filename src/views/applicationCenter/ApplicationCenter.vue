<template>
  <div class="bottom-auto-height">
    <a-card  class="bottom" style="overflow: auto" :bodyStyle="{ padding: '24px 0' }">
      <div v-if="menus && menus.length > 0">
        <div v-for="(item, index) in menus" :key="item.path" class="solaryeye-menu">
          <div class="menu-title">
            <div class="menu-title-before"></div>
            {{ item.meta.title }}
            <a-icon
              type="double-right"
              @click="handleClick(index)"
              :class="item.isShow ? 'solar-eye-arrow-down' : 'solar-eye-arrow-up'"
            />
          </div>
          <div class="menu-content">
            <div
              v-for="(child, childIndex) in item.children"
              :key="child.path"
              class="menu-child bg"
              :class="[isIncludeRouteName(child.meta.title, index), 'bg' + childIndex % 7]"
              v-show="item.isShow && !child.hidden"
              @click="openNewPath(item, child)"
            >
            {{item.hidden}}
              <a-icon
                :type="child.meta.icon"
                v-if="!isSolareyeCustom(child.meta.icon)"
                style="font-size: 48px; color: white;margin-top: -18px"
              ></a-icon>
              <svg-icon
                v-else
                :iconClass="isSolareyeCustom(child.meta.icon, true)"
                style="font-size: 48px; margin-top: -18px"
              ></svg-icon>
              <div class="child-div"
                >{{ child.meta.title }}</div
              >
            </div>
          </div>
        </div>
      </div>
    </a-card>
  </div>
</template>

<script>
const innerHeight = window.innerHeight - 60 - 40 - 24;
export default {
  name: 'ApplicationCenter',
  data () {
    return {
      menus: [],
      routeName: '',
      height: innerHeight - 96
    };
  },
  created () {
    this.secondMenu();
  },
  watch: {},
  computed: {
    permissionMenuList () {
      return this.$store.state.user.permissionList;
    }
  },

  methods: {
    secondMenu () {
      this.permissionMenuList.forEach((item) => {
        if (item.path === '/application') {
          this.menus = item.children;
          this.menus = this.menus.filter((child) => {
            return child.path != '/application' && child.path != '/application/common';
          });
          this.fillMenuList();
        }
      });
    },
    handleClick (index) {
      this.menus[index].isShow = !this.menus[index].isShow;
    },
    fillMenuList () {
      this.menus.map((item, index) => {
        this.$set(this.menus[index], 'isShow', true);
        // if (item.children && item.children.length > 0) {
        //   item.children.map((child, childIndex) => {
        //     child.imgSrc = require('@/assets/images/application/bg' + (childIndex % 7) + '.png')
        //   })
        // } else {
        //   item.imgSrc = require('@/assets/images/application/bg' + (index % 7) + '.png')
        // }
      });
    },
    /**
     *  判断是否是自定义的svg 图标
     *  parmas {string} icon 图标名称
     *  iconName {Boolean} true return svg 的name，false ，返回是否是自定义的svg 图标
     */
    isSolareyeCustom (icon, iconName) {
      if (!icon) {
        return null;
      }
      if (iconName) {
        return icon.split('solareye-custom-')[1];
      } else {
        return icon.indexOf('solareye-custom-') > -1;
      }
    },
    isIncludeRouteName (name, index) {
      let isIndexOf = this.routeName && name.indexOf(this.routeName) != -1;
      if (isIndexOf) {
        this.menus[index].isShow = true;
      }
      return isIndexOf ? 'menu-selected' : '';
    },
    /**
     *  打开新的页面
     * params {item}
     * params child
     */
    openNewPath (item, child) {
      let isIframe = child.meta.componentName === 'IframePageView' || child.meta.componentName === 'IframeView';
      let path = '';
      if (isIframe) {
        path = item.path + '/' + child.path;
      } else {
        path = child.path;
      }
      this.$router.push(path);
    }
  }
};
</script>

<style lang="less" scoped>

.search-input {
  width: 260px;
}
.colorize_bg(@color: @white, @alpha: 1) {
  background: hsla(hue(@color), saturation(@color), lightness(@color), @alpha);
}

.solaryeye-menu {
  // padding: 0 0 24px;
  &:last-child {
    .menu-content {
      border-bottom: transparent;
    }

  }

  .menu-title {
    padding: 0 0 16px;
    line-height: 22px;
    font-weight: 500;
    font-size: 16px;
    display: flex;
    align-items: center;
    i {
      margin: 0px 16px;
    }
    .solar-eye-arrow-down {
      transform: rotate(90deg);
    }
    .solar-eye-arrow-up {
      transform: rotate(-90deg);
    }
  }
  .menu-title-before {
    margin-right: 16px;
    height: 16px;
    border-radius:  0px 2px 2px 0px;
  }
  .menu-content {
    padding: 0 8px 4px;
    display: flex;
    flex-direction: row;
    flex-flow: wrap;
  }

  &:not(:first-child) {
    .menu-title {
      padding-top: 16px;
    }
  }

  .menu-child {
    display: inline-flex;
    white-space: pre-wrap;
    //border: 1px solid #ccc;
   // background: rgba(0, 0, 0, 0.03);
    height: 148px;
    width: 310px;
    padding: 0 24px;
    cursor: pointer;
    align-items: center;
    a, .child-div {
      color: white;
      padding-left: 16px;
      font-weight: 600;
      margin-top: -22px;
      font-size: 16px;
      flex: 1;
    }
  }
  .bg {
    background-image: url("../../assets/images/application/bg.png");
    &.bg0 {
      background-position: -10px -10px;
    }
    &.bg1 {
       background-position: -10px -178px;
    }
    &.bg2 {
       background-position: -340px -178px;
    }
    &.bg3 {
       background-position: -10px -346px;
    }
    &.bg4 {
       background-position: -340px -346px;
    }
    &.bg5 {
       background-position: -670px -10px;
    }
    &.bg6 {
       background-position: -340px -10px;
    }
  }

  .menu-child:hover {
    transform: translateY(-5px);
  }

  .menu-child.menu-selected {
    transform: translateY(-5px);
  }
}
</style>
