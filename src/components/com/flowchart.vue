<!-- 查看流程图  -->
<template>
  <a-spin :spinning="loading" class='width-height-100'>
    <a-row :gutter="24" class="flow-detail">
      <template v-if="processInstanceId">
        <a-col :sm="24" :md="6" style="height: 65vh;overflow: auto; padding:24px">
          <a-timeline style="margin: auto;">
            <a-timeline-item v-for="(activity, index) in activities" :key="index" :color="activity.color">
              <p>{{activity.content}}</p>
              <p>{{activity.timestamp || activity.message}}</p>
            </a-timeline-item>
          </a-timeline>
        </a-col>
        <a-col :sm="24" :md="18" style="height: 65vh;display: flex;">
          <img :src="workFlowImag64" style="margin: auto; max-width: 100%;" />
        </a-col>
      </template>
      <!-- 未开启流程的情况 -->
      <template v-else>
        <a-col :span="24" style="height: 65vh;display: flex;">
          <img :src="workFlowImag64" style="margin: auto; max-width: 100%;" />
        </a-col>
      </template>
    </a-row>
  </a-spin>
</template>
<script>
import { auditRecord } from '@/api/common_gy/plan.js';
import { showInitialProcessDiagram, listAuditInfo, showProcessDiagram } from '@/api/isolarErp/com/flowchart';
export default {
  name: 'flowChart',
  props: {
    // 流程相关id
    processInstanceId: {
      type: String,
      required: ''
    },
    // 当前流程人员
    flowUser: {
      type: String,
      required: ''
    },
    // 流程来源区分-ERP、TWO
    type: {
      type: [String, Number],
      default: '1'
    },
    // 流程定义主键名称 字典flowchart_type 的label值
    processDefinitionKey: {
      type: String,
      required: true
    }
  },
  watch: {
    processInstanceId: {
      immediate: true,
      handler: function () {
        this.showFlowChart();
      }
    }
  },
  data () {
    return {
      loading: false,
      // 流程信息
      activities: [],
      // 流程图
      workFlowImag64: ''
    };
  },
  methods: {
    // 获取流程图
    showFlowChart () {
      let self = this;
      self.loading = true;
      if (!self.processInstanceId) {
        let map = {
          processDefinitionKey: self.processDefinitionKey
        };
        showInitialProcessDiagram(map).then(res => {
          self.workFlowImag64 = res.result_data;
          self.loading = false;
        }).catch(() => {
          self.loading = false;
        });
      } else {
        let requestMap = {
          processInstanceId: self.processInstanceId,
          pProcessInstanceId: self.processInstanceId,
          flowUser: self.flowUser
        };
        const promise = new Promise((resolve, reject) => {
          showProcessDiagram(requestMap).then(res => {
            resolve(res.result_data);
          }).catch(err => {
            reject(err);
          });
        });
        promise.then(function (value) {
          self.workFlowImag64 = value;
          if (self.type == '1') {
            listAuditInfo(requestMap).then(res => {
              res.result_data.forEach(item => {
                if (item.color) {
                  if (item.color == 'limegreen') {
                    item.color = 'green';
                  }
                } else {
                  item.color = 'blue';
                }
              });
              self.activities = res.result_data;
              self.loading = false;
            });
          } else {
            auditRecord(requestMap).then((response) => {
              response.result.forEach(item => {
                if (!item.color) {
                  item.color = 'blue';
                }
              });
              self.activities = response.result;
              self.loading = false;
            });
          }
        }, function () {
          self.loading = false;
        });
      }
    }
  }
};
</script>

<style lang="less" scoped>
  :deep(.ant-timeline) {
    padding-top: 5px;
  }
</style>
