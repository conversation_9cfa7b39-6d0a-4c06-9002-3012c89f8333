<template>
  <a-drawer width="100%" :visible="visible" @close="cancel" :get-container="getContainer" :wrap-style="{ position: 'absolute' }"
            :afterVisibleChange="afterVisibleChange" :destroyOnClose="true" class="drawer-box">
    <a-tabs v-model="activeTab" @change="tabChange">
      <a-tab-pane key="1" tab="详情">
        <ticket-detail :basics="basics" @cancel="cancel" :dict="dictMap" :loading="loading"></ticket-detail>
      </a-tab-pane>
      <a-tab-pane key="2" tab="流程图" force-render>
        <flowchart :processInstanceId="basics.processInstanceId"
                    :flowUser="basics.flowUser" :processDefinitionKey="basics.processDefinitionKey" />
      </a-tab-pane >
    </a-tabs>
  </a-drawer>
</template>

<script>
import { getTicketDetail } from '@/api/operations/huTicket';
import ticketDetail from './ticketDetail';
import initDict from '@/mixins/initDict';
export default {
  name: 'ticketForm',
  mixins: [initDict],
  components: {
    ticketDetail
  },
  props: {
    /* 抽屉挂在的DOM节点ID，必要参数 */
    parentId: {
      type: String,
      required: true,
      default: ''
    },
    visible: {
      type: Boolean,
      default: null
    },
    row: {
      type: Array,
      default: () => []
    }
  },
  data () {
    return {
      activeTab: '1',
      loading: false,
      dictMap: {},
      // 两票基本信息
      basics: {}
    };
  },
  watch: {
    'parentId' (val, old) {
      this.getContainer();
    },
    'visible' (val, old) {
      if (val) {
        this.getTicketInfo();
      }
    }
  },
  created () {
    this.getDictMap('ticket_execute_flag');
  },
  methods: {
    // 获取抽屉组件挂在的DOM
    getContainer () {
      let mains = document.querySelectorAll('.main');
      return document.getElementById(this.parentId) || mains[mains.length - 1].querySelector('div');
    },
    // 切换抽屉时动画结束回调事件
    afterVisibleChange (visible) {

    },
    tabChange (key) {
      this.activeTab = key;
    },
    // 获取两票详情信息
    getTicketInfo () {
      this.loading = true;
      let self = this;
      let params = {
        'id': self.row.id
      };
      getTicketDetail(params).then(res => {
        // 两票基本信息设置
        let result = res.result_data;
        if (!Array.isArray(result.uploadFileList)) {
          result.uploadFileList = [];
        }
        if (result.allowWorkEndTime && result.allowWorkTime) {
          result.allowWorkTimeStr = result.allowWorkTime + '~' + result.allowWorkEndTime;
        }
        if (result.planTimeFrom && result.planTimeTo) {
          result.planTime = result.planTimeFrom + '~' + result.planTimeTo;
        }
        // if (result.deviceMeasureWeb) {
        //   result.deviceMeasureWeb = result.deviceMeasureWeb.filter(item => result.deviceMeasure.includes(item.safeMeasureId));
        // }
        // if (result.operationWebList) {
        //   result.operationWebList = result.operationWebList.filter(item => result.operationList.includes(item.safeMeasureId));
        // }
        self.basics = Object.assign({}, result);
        self.loading = false;
      }).catch(() => {
        self.loading = false;
      });
    },
    // 关闭回调
    cancel () {
      this.$emit('update:row', []);
      this.$emit('update:visible', false);
      this.reset();
    },
    // 数据重置
    reset () {
      let dictMap = { ...this.dict, ...this.dictMap };
      Object.assign(this.$data, this.$options.data());
      this.dictMap = dictMap;
    }
  }
};
</script>
<style lang="less" scoped>
.drawer-box{
  :deep(.ant-drawer-body) {
    padding-top: 5px;
    height: 100% !important;
  }
}
:deep(.ant-tabs-tabpane) {
  height: 80vh;
  overflow-y: auto;
  padding-right: 10px;
}
</style>
