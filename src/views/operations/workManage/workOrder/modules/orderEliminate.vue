<template>
  <div class="view_detail_box">
    <div class="title_box_top">
      <span>消除方案</span>
    </div>
    <!-- 提交表单 -->
    <a-form-model ref="eliminateForm" :model="eliminate" :labelCol="{ style:'width: 120px' }" :wrapperCol="{ style:'width: calc(100% - 120px)' }">
      <a-row :gutter="24">
        <a-col :span="24">
          <a-form-model-item label="方案上传">
            <a-switch :disabled="disabled" v-model="eliminate.active" @change="switchChange"/>
          </a-form-model-item>
        </a-col>
        <a-col :span="24" v-show="eliminate.active">
          <a-form-model-item label="消除计划方案">
            <uploadFile :disabled="disabled" v-model="eliminate.receiveDocs" :maxNum="5" :tipIsInline="true" tip="最多上传5个文件,且上传的附件最大不超过10MB!">上传</uploadFile>
          </a-form-model-item>
        </a-col>
      </a-row>
    </a-form-model>
  </div>
</template>

<script>
import uploadFile from '@/components/common_gy/uploadFiles';
export default {
  props: {
    disabled: {
      type: Boolean,
      default: false
    }
  },
  components: {
    uploadFile
  },
  data () {
    return {
      // 普通表单
      eliminate: {
        'receiveDocs': [], // 附件
        'active': true // 是否启用
      },
      active: true,
      rules: {
        receiveDocs: [{
          required: true,
          message: '请上传消缺方案'
        }]
      }
    };
  },
  methods: {
    switchChange (checked, event) {
      if (this.active) {
        this.eliminate.active = true;
        this.$message.warning('一、二类缺陷必选上传消缺方案');
        return;
      }
      this.rules.receiveDocs[0].required = checked;
    },
    // 更新数据
    updataEliminate (order) {
      this.eliminate = {
        'receiveDocs': order.receiveDocs, // 附件
        'active': order.active
      };
      this.active = order.active;
      this.rules.receiveDocs[0].required = order.active;
    },
    // 保存验证
    saveEliminate () {
      return new Promise((resolve, reject) => {
        if (this.eliminate.active) {
          if (Array.isArray(this.eliminate.receiveDocs) && this.eliminate.receiveDocs.length) {
            resolve(this.eliminate);
          } else {
            this.$message.error('请上传消缺方案');
            reject(new Error());
          }
        } else {
          resolve(this.eliminate);
        }
      });
    }
  }
};
</script>

<style lang="less" scoped>
  .view_detail_box {
    border-top: 10px solid #f2f2f2;
    padding: 20px 0;
    .title_box_top {
      font-size: 18px;
      font-weight: 550;
      line-height: 25px;
      padding-bottom: 20px;
      display: flex;
      flex-flow: row nowrap;
      justify-content: space-between;
    }
  }
  :deep(.ant-form-item) {
    width: 100%;
    display: inline-flex;
  }
</style>
