<template>
  <detail-layout :labelList="labelList" :form="baseForm" title="风险信息"> </detail-layout>
</template>

<script>
export default {
  name: 'RiskInfoDetail',
  props: {
    baseForm: {
      type: Object,
      default: () => {
        return {};
      }
    }
  },
  data () {
    return {
      labelList: [
        {
          label: '作业类别',
          key: 'jobCategoryName'
        },
        {
          label: '作业内容',
          key: 'jobContentName'
        },
        {
          label: '风险等级',
          key: 'riskLevelName'
        },
        {
          label: '作业开始日期',
          key: 'riskJobDate'
        }
      ]
    };
  }
};
</script>
