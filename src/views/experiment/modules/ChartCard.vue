<template>
  <div class="search_card">
    <template v-for="(item,index)  in itemList">
      <div class="search-tab" :key="index" @click="clickEvent(item.id,item.value,index)" :class="{'active': selectId == item.id}">
        <div class="image-box">
          <img :src="getPng(item.icon)"/>
        </div>
        <div>
          <div class="search-title">
            <span class="search-type">{{ item.name }}</span>
          </div>
          <div class="search-num">{{ item.num }}</div>
        </div>
      </div>
    </template>
    <div v-if="itemList.length > 0" class="high-bg" :style="{left: activeIndex * 16.7 + '%'}"></div>
    <div v-if="itemList.length > 0" class="high-border" :style="{left: (activeIndex * 16.7) + 2.6 + '%'}"></div>
  </div>
</template>

<script>
import { mixin } from '@/utils/mixin';

export default {
  mixins: [mixin],
  data () {
    return {
      selectId: null,
      activeIndex: 0
    };
  },
  props: {
    itemList: {
      type: Array,
      default: () => {
        return [];
      }
    }
  },
  created () {
    this.selectId = this.itemList[0].id;
  },
  methods: {
    clickEvent (id, value, index) {
      this.selectId = id;
      this.activeIndex = index;
      this.$emit('queryDataSource', value);
    },
    getPng (name) {
      return require('@/assets/images/taskHall/' + name + '_' + this.navTheme + '.png');
    }
  }
};
</script>

<style lang="less" scoped>
.search_card {
  display: flex;
  height: 120px;
  background: var(--zw-card-bg-color--default);
  box-shadow: 0px 2px 11px 0px rgba(0, 0, 0, 0.05);
  border-radius: 4px;

  .search-tab {
    width: 20%;
    padding: 20px 0 20px 20px;
    height: 100%;
    display: flex;
    cursor: pointer;
    position: relative;

    &:first-child {
      border-top-left-radius: 4px;
      border-bottom-left-radius: 4px;
    }

    .image-box {
      width: 80px;
      margin-right: 12px;
      display: flex;
      align-items: center;
      justify-content: center;

      img {
        width: 72px;
      }
    }

    .search-type {
      color: var(--zw-text-1-color--default);
      font-size: 14px;
      margin-right: 8px;
      font-weight: 600;
    }

    .search-num {
      font-weight: 600;
      color: var(--zw-text-1-color--default);
      line-height: 42px;
      font-size: 34px;
      margin: 4px 0;
    }
  }

  .search-tab:hover {
    background: var(--zw-card-light-bg-color--default);
  }

  .active:hover {
    background: transparent;
  }

  .high-bg {
    height: 90px;
    width: 16.7%;
    background: linear-gradient(180deg, var(--zw-card-bg-color--default) 0%, var(--zw-primary-color--default) 100%);
    opacity: 0.1;
    position: absolute;
    top: 30px;
    cursor: pointer;
  }

  .high-border {
    width: 11.4%;
    height: 3px;
    background: var(--zw-primary-color--default);
    border-radius: 2px;
    position: absolute;
    top: 117px;
    transition: left 0.2s;
    cursor: pointer;
  }

  .search-tab + .search-tab::after {
    height: 74px;
    width: 1px;
    margin: 28px 0px 28px -20px;
    border-left: 1px solid var(--zw-divider-color--default);
    content: '';
    position: absolute;
    top: 0;
  }
}
</style>
