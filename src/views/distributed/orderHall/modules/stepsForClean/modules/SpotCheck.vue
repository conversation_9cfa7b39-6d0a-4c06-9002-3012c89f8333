<template>
  <div>
    <span class='item-content-title'>清洗后完成现场验收</span>
    <a-form-model
      :model="form"
      ref="SafeCheck"
      :wrapperCol="{ style: 'width: calc(100% - 150px)' }"
      :rules='rules'
      labelAlign='left'
      class='mt-24'
    >
      <a-row :gutter="24">
        <a-col :span='24'>
          <a-form-model-item label="上传图片" prop="sceneCheckFourOneFileList">
            <DetailEmpty v-if='sceneCheckFourOneFileListShow' />
            <uploadFileView
              v-else
              v-model="form.sceneCheckFourOneFileList"
              listType="picture-card"
              :disabled="isDisabled"
              uploadText='上传现场照片'
              accept=".jpg,.png,.jpeg,.bmp,.JPG,.PNG,.JPEG,.BMP"
              :max-num='1'
              @change='checkPickure("sceneCheckFourOneFileList")'
            />
            <ExampleImg name='step4_scene.png' imgStyle='position: absolute;left: 112px;top:0'/>
          </a-form-model-item>
        </a-col >
        <a-col :span='24'>
          <a-form-model-item class='require-none' prop="sceneCheckFourTwoFileList" >
            <template slot='label'><span style='width: 63px;display: inline-block'></span></template>
            <template>
              <DetailEmpty v-if='sceneCheckFourTwoFileListShow' />
              <uploadFileView
                v-else
                v-model="form.sceneCheckFourTwoFileList"
                listType="picture-card"
                :disabled="isDisabled"
                uploadText='上传现场照片'
                accept=".jpg,.png,.jpeg,.bmp,.JPG,.PNG,.JPEG,.BMP"
                tip="单张图片不能超过10MB，仅支持上传jpg、jpeg、png、bmp格式"
                :max-num='1'
                @change='checkPickure("sceneCheckFourTwoFileList")'
              />
            </template>
            <ExampleImg name='step4_scene2.png' imgStyle='position: absolute;left: 112px;top:0'/>
          </a-form-model-item>
        </a-col>
      </a-row>
    </a-form-model>
  </div>
</template>

<script>
import uploadFileView from '@/components/com/fileUploadView';
import ExampleImg from '@views/distributed/orderHall/modules/stepsForClean/modules/ExampleImg.vue';
import DetailEmpty from '@views/distributed/orderHall/modules/stepsForClean/modules/DetailEmpty.vue';
export default {
  inject: ['type'],
  components: { uploadFileView, ExampleImg, DetailEmpty },
  props: {
    componentData: {
      type: Object,
      default: () => ({})
    },
    isDisabled: {
      type: Boolean,
      default: false
    }
  },
  data () {
    return {
      form: {
        sceneCheckFourOneFileList: undefined,
        sceneCheckFourTwoFileList: undefined
      },
      rules: {
        sceneCheckFourOneFileList: [
          { required: true, message: '请上传图片', trigger: 'change' }
        ],
        sceneCheckFourTwoFileList: [
          { required: true, message: '请上传图片', trigger: 'change' }
        ]
      }
    };
  },
  mounted () {
    const cleanDetailVo = this.componentData.cleanDetailVo || {};
    Object.assign(this.form, { sceneCheckFourOneFileList: cleanDetailVo.sceneCheckFourOneFileList, sceneCheckFourTwoFileList: cleanDetailVo.sceneCheckFourTwoFileList });
  },
  methods: {
    checkPickure (propertyName) {
      this.$nextTick(() => {
        this.$refs.SafeCheck.validateField(propertyName);
      });
    },
    validateForm () {
      let result;
      this.$refs.SafeCheck.validate(valid => {
        result = valid;
      });
      return result;
    },
    change (file) {
      console.log(this.form, file);
    }
  },
  computed: {
    sceneCheckFourOneFileListShow () {
      return this.isDisabled && ['3', '7', '8'].includes(this.type()) && !(this.form.sceneCheckFourOneFileList && this.form.sceneCheckFourOneFileList.length);
    },
    sceneCheckFourTwoFileListShow () {
      return this.isDisabled && ['3', '7', '8'].includes(this.type()) && !(this.form.sceneCheckFourTwoFileList && this.form.sceneCheckFourTwoFileList.length);
    }
  }
};
</script>

<style lang='less' scoped>

</style>
