import {
  selectAttributeConfig,
  getManageDeviceDetail,
  getSystemCodeList,
  getDeviceModelList,
  selectDeviceModel
} from '@/api/health/healthapi.js';
export default {
  inject: ['provideData', 'psInfo'],
  props: {
    isUpdateEdit: {
      type: Boolean,
      default: false
    },
    isSelf: {
      type: Boolean,
      default: true
    }
  },
  data () {
    return {
      stationSelectType: {},
      // psKey:'',
      baseInfo: {
        attributeConfig: {},
        sungrowObtain: [],
        systemObtain: [],
        seriesEnableConfig: [],
        seriesConfig: [],
        isAdjust: 0
      },
      storageForm: '',
      deviceMakerOptions: [],
      deviceModelOptions: []
    };
  },
  created () {},
  computed: {},
  watch: {
    'provideData.psId' () {
      this.getDeviceInfo(true);
    },
    'provideData.psKey': {
      immediate: true,
      handler (val) {
        this.isEdit = false;
        if (this.$refs.form) {
          this.$nextTick(() => {
            this.$refs.form.resetFields();
          });
        }
        this.$refs.baseForm && this.$refs.baseForm.clearValidate();
        this.getDeviceInfo(true);
      }
    },
    'provideData.isEdit' () {
      this.isEdit = false;
      if (this.$refs.form) {
        this.$refs.form.resetFields();
      }
      this.getDeviceInfo(true);
    }
  },
  methods: {
    getDeviceInfo (flag) {
      this.isEdit = false;
      if (this.provideData.deviceType == 11) {
        this.selectAttributeConfig(flag);
      } else {
        this.selectManageDeviceDetail();
      }
    },
    selectManageDeviceDetail () {
      getManageDeviceDetail({
        psId: this.provideData.psId,
        psKey: this.provideData.psKey
      }).then(res => {
        let data = res;
        this.baseInfo.sungrowObtain = data.manageDevice;
        this.baseInfo.isAdjust = data.manageDevice.isAdjust != '0';
        this.baseInfo.deviceModelParam = data.deviceModelParam;
        let seriesEnableConfig = data.string;
        this.baseInfo.seriesEnableConfig = seriesEnableConfig;
        if (seriesEnableConfig && seriesEnableConfig.length > 0) {
          this.baseInfo.seriesEnableConfig.map(item => {
            item.value = item.value == '1';
          });
        }
        this.storageForm = JSON.stringify(this.baseInfo);
      });
    },
    selectAttributeConfig (flag) {
      selectAttributeConfig({
        psId: this.provideData.psId,
        psKey: this.provideData.psKey,
        isLocal: this.psInfo().isLocal == 2 ? 2 : (this.isLocal ? 1 : 0),
        source: this.isSelf ? '0' : '1',
        communicateVersion: this.psInfo().communicateVersion
      }).then(res => {
        let data = res.result_data;
        this.baseInfo = data;
        if (!this.deviceType || this.deviceType == '11') {
          this.setCollectForm();
        }
        let seriesEnableConfig = this.baseInfo.seriesEnableConfig;
        if (seriesEnableConfig && seriesEnableConfig.length > 0) {
          this.baseInfo.seriesEnableConfig.map(item => {
            item.value = item.value == '1';
          });
        }
        if (flag) {
          this.form = data.attributeConfig;
          this.form.tableData = data.seriesConfig;
          this.storageForm = JSON.stringify(data);
          this.initCols(data.seriesConfig);
          if (['6', '12', '29', '30', '3', '17'].includes(this.deviceType) || (this.deviceType == '7' && !this.isLocal)) {
            this.form = data.sungrowObtain[0];
          }
        } else {
          this.storageForm = JSON.stringify(data.attributeConfig);
        }
        if (this.isLocal && ['5', '7'].includes(this.deviceType)) {
          this.getDeviceMakerOptions(data.attributeConfig.p110009, data.attributeConfig.p110010);
        }
      });
    },
    initCols (arr) {
      if (!arr || arr.length === 0) {
        return null;
      }
      let item = Object.keys(arr[0]);
      if (arr && arr.length > 0) {
        if (this.cols && this.cols.length > 0) {
          this.cols = this.cols.splice(0, 1);
          for (let i = 1; i < item.length - 1; i++) {
            this.cols.push({
              prop: `num${i}`,
              label: `第${i}路`
            });
          }
        }
      }
    },
    getSystemCodeList () {
      getSystemCodeList({ firstTypeCode: '0002,0025,0026,0027,0028,0029,0042,0046,0059' }).then(res => {
        this.stationSelectType = res.result_data;
      });
    },
    getCodeName (code, value) {
      let keys = Object.keys(this.stationSelectType);
      let name = null;
      if (code && keys.length > 0 && keys.indexOf(code) > -1) {
        this.stationSelectType[code].map(item => {
          if (value === item.secondTypeCode) {
            name = item.secondName;
          }
        });
      }
      return value ? name : '--';
    },
    // 获取生产厂商下拉选项
    getDeviceMakerOptions (maker, deviceModelId) {
      getDeviceModelList({ manageDeviceTypeList: [this.deviceType].join(','), groupBy: 'makerId' }).then(res => {
        if (Array.isArray(res) && res.length) {
          this.deviceMakerOptions = res;
          res.forEach(item => {
            if (maker == item.maker) {
              this.getDeviceModelOptions(item.makerId, deviceModelId);
              let storageForm = JSON.parse(this.storageForm);
              storageForm.attributeConfig.p120044 = item.makerId + '';
              this.storageForm = JSON.stringify(storageForm);
            }
          });
        } else {
          this.deviceMakerOptions = [];
        }
      });
    },
    // 获取设备型号下拉选项
    getDeviceModelOptions (makerId, deviceModelId) {
      selectDeviceModel({ deviceTypeName: this.baseInfo.sungrowObtain[0].value, makerId: makerId }).then(res => {
        if (res.result_data) {
          this.deviceModelOptions = res.result_data;
          if (deviceModelId) {
            res.result_data.forEach(item => {
              if (item.deviceModel == this.form.p110010) {
                let storageForm = JSON.parse(this.storageForm);
                storageForm.attributeConfig.p120033 = item.id + '';
                this.storageForm = JSON.stringify(storageForm);
              }
            });
          }
        } else {
          this.deviceModelOptions = [];
        }
      });
    },
    // 生产厂商变化事件
    deviceMakerChange (val, option) {
      this.form.p120044 = option.key + '';
      this.getDeviceModelOptions(option.key);
      this.form.p110010 = undefined;
      this.form.p120033 = undefined;
    },
    deviceModelChange (val, option) {
      this.form.p120033 = option.key + '';
    },
    getBaseInfoValue (key, isArray) {
      let data = this.baseInfo.sungrowObtain.find(item => { return item.key == key; });
      if (data && data.value) {
        return isArray ? data.value.split(',') : data.value;
      } else {
        return undefined;
      }
    },
    getCollectInfoValue (key, index) {
      if (index >= 0) {
        return this.baseInfo.collectorConfig[index][key];
      }
    },
    show (perms) {
      return this.showHandle(perms);
    }
  }
};
