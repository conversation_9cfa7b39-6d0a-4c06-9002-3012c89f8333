<!--电表-->
<template>
<a-spin size="large" :spinning="wholePageLoading" class="spin-content">
  <div class="device">
      <section>
      <a-radio-group v-model="alarmGrade" @change="alarmGradeChange" class="radio-div">
        <a-radio-button value="">全部({{ activeTotal }})</a-radio-button>
        <a-radio-button v-for="item in alarmNumList" :key="item.grade" :value="item.grade"
          >{{ item.gradeName }}({{ item.count }})</a-radio-button
        >
      </a-radio-group>
    </section>
    <div class="content  width_100" :style="{height: isShowMenu ? 'calc(100vh - 395px)' : 'calc(100vh - 271px)'}">
      <template v-for="item in list">
        <div class="b-box" :key="item.ps_key">
          <header class="device_name">
            <div :title="item.device_name" class="device_name_lable over-flow">
              {{item.device_name}}
            </div>
            <img
                :src="require(`@/assets/images/monitor/device/alarm_type_${item.grade ? item.grade : 5}.png`)"
                alt=""
                srcset=""
                class="alarm-type-img"
              />
              <div class="alarm-type" :class="['alarm_type_'+ item.grade]">{{item.gradeName}}</div>
          </header>
          <section>
            <div class="left"><img src="@/assets/images/monitor/device/electricMeter_s.png" /></div>
            <div class="right">
              <div class="desc">
                <div class="desc-left">正向有功(kWh)：</div>
                <div class="desc-right" :title="item.p8030">{{dealData(item.p8030)}}</div>
              </div>
              <div class="desc">
                <div class="desc-left">正向无功(kVarh)：</div>
                <div class="desc-right" :title="item.p8032">{{dealData(item.p8032)}}</div>
              </div>
              <div class="desc">
                <div class="desc-left">反向有功(kWh)：</div>
                <div class="desc-right" :title="item.p8031">{{dealData(item.p8031)}}</div>
              </div>
              <div class="desc">
                <div class="desc-left">反向无功(kVarh)：</div>
                <div class="desc-right" :title="item.p8033">{{dealData(item.p8033)}}</div>
              </div>
            </div>
          </section>
        </div>
      </template>
         <infinite-loading :identifier="infiniteId" v-if="list.length >= pageSize" @infinite="getList">
          <div slot="spinner">
            <a-spin size="large" v-if="!wholePageLoading" class="card-spin"></a-spin>
          </div>
          <div slot="no-more">
            <p class="no-more-data">无更多数据</p>
          </div>
        </infinite-loading>
         <div class="infinite-loading-container" v-if="list.length == 0 && !wholePageLoading">
            <img class="no-data-img" :src="noDataImg" />
            <p class="no-data-text">暂无数据</p>
          </div>
    </div>
  </div>
  </a-spin>
</template>
<script>
import deviceType from '../mixins/deviceType';
export default {
  mixins: [deviceType],
  data () {
    return {
      url: {
        count: '/monitorNew/getMeterAlarmCount',
        alarmList: '/monitorNew/getMeterAlarmList'
      }
    };
  },
  computed: {
    isShowMenu () {
      return this.$store.state.user.isShowMenu;
    }
  },
  methods: {
    dealData (data) {
      let isTrue = data || data == 0;
      let str = String(data).length > 9 ? (String(data).slice(0, 7) + '...') : data;
      return isTrue ? str : '--';
    }
  }
};
</script>
<style lang="less" scoped>
@import url('../less/base.less');
.device {
  align-items: flex-start;
  .content.width_100 {
    margin-top: 10px;
  }
  .b-box {
    width: 378px;
    height: 228px;
    margin: 6px 10px 10px 6px;
  }
  .content {
    flex-wrap: wrap;
    overflow: auto;
    align-content: flex-start;
    margin-top: 16px;
  }
  .b-box {
    header {
      .alarm-type-img {
        right: 12px;
      }
      .alarm-type {
        right: 28px;
      }
    }
    section {
      padding: 16px 16px 16px 0;
      .left {
        width: 150px;
        text-align: center;
      }
      .right {
        flex-direction: column;
        padding: 16px;
        justify-content: space-between;
        width: 203px;
        height: 132px;
        background: rgba(00, 00, 00, 0.19);
        border-radius: 8px;
        .desc {
          display: flex;
          color: white;
        }
        .desc-left {
          width: 124px;
        }
        .desc-right {
          width: 62px;
          text-align: right;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }
      }
    }
  }
}
</style>
