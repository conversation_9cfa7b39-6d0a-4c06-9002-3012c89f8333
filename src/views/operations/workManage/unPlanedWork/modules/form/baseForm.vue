<!-- 任务除故障缺陷基础表单 -->
<template>
  <a-form-model :model="baseForm" :rules="base_rules" ref="baseForm" :labelCol="{ style: 'width: 120px' }" :wrapperCol="{ style: 'width: calc(100% - 120px)' }">
    <a-row>
      <a-col :xl="8" :sm="12" :xs="24">
        <a-form-model-item label="计划日期" prop="planTime">
          <a-range-picker
            v-model="baseForm.planTime"
            :placeholder="['请选择', '请选择']"
            :disabled="isDisabled || type()=='10'"
            format="YYYY-MM-DD"
            valueFormat="YYYY-MM-DD"
            style="width: 100%"
            @calendarChange="changePlanTime"
            :open="openStatus"
            @openChange="openChangeEvent" />
        </a-form-model-item>
      </a-col>
      <!-- 任务名称 户用类电站时任务名称自动生成 -->
      <a-col :xl="8" :sm="12" :xs="24">
        <a-form-model-item label="任务名称" prop="taskName">
          <a-input
            :disabled=" isDisabled"
            :title="baseForm.taskName"
            v-model="baseForm.taskName"
            :max-length="50"
            @blur="baseForm.taskName = $trim($event)"
            placeholder="请输入"
            style="width: 100%" />
        </a-form-model-item>
      </a-col>
      <!--清洗及除草-->
      <a-col :xl="8" :sm="12" :xs="24" v-if="['1', '2'].includes(baseInfo.taskType)">
        <a-form-model-item label="电站环境" prop="stationEnvironment" key="stationEnvironment">
          <a-select
            v-model="baseForm.stationEnvironment"
            disabled
            placeholder="请选择"
            style="width: 100%"
            mode="multiple">
            <a-select-option
              v-for="item in dictMap.psa_model"
              :key="item.dataValue"
              :value="item.dataValue">{{ item.dataLable }}</a-select-option>
          </a-select>
        </a-form-model-item>
      </a-col>
      <div style="clear: both"></div>
      <a-col :span="24">
        <a-form-model-item label="任务内容" prop="taskDescription">
          <a-textarea
            size="default"
            :max-length="200"
            :auto-size="{ minRows: 2, maxRows: 2 }"
            :disabled="isDisabled"
            style="width: 100%"
            v-model="baseForm.taskDescription"
            @blur="baseForm.taskDescription = $trim($event)"></a-textarea>
        </a-form-model-item>
      </a-col>
      <!--清洗及除草-->
      <template v-if="['1', '2'].includes(baseInfo.taskType)">
        <a-col :xl="8" :sm="12" :xs="24" v-if="baseInfo.taskType == '1'">
          <a-form-model-item label="清洗方式" prop="cleanType" key="cleanType">
            <a-select v-model="baseForm.cleanType" :disabled="isDisabled">
              <a-select-option v-for="item in dictMap.pdca_clean_type" :key="item.dataValue" :value="item.dataValue">{{item.dataLable}}</a-select-option>
            </a-select>
          </a-form-model-item>
        </a-col>
        <a-col :xl="8" :sm="12" :xs="24" v-if="baseInfo.taskType == '2'">
          <a-form-model-item label="除草方式" prop="grassType" key="grassType">
            <a-select v-model="baseForm.grassType" :disabled="isDisabled">
              <a-select-option v-for="item in dictMap.pdca_grass_type" :key="item.dataValue" :value="item.dataValue">{{item.dataLable}}</a-select-option>
            </a-select>
          </a-form-model-item>
        </a-col>
        <a-col :xl="8" :sm="12" :xs="24">
          <a-form-model-item :label="(baseInfo.taskType == '1' ? '清洗' : '除草') + '容量(MW)'"
            prop="cleanCap" :rules="{ required: true, trigger: 'blur',  message: (baseInfo.taskType == '1' ? '清洗' : '除草') + '容量不能为空',}">
            <a-input-number
              v-model="baseForm.cleanCap"
              :min="0"
              :max="9999.9999"
              :precision="4"
              @blur='updatePlanEndTime'
              :disabled="isDisabled || baseInfo.isFarm == '1' || ( type() == 10 && baseInfo.taskType == '1' && baseInfo.workSubclass == '2' )"
              style="width: 100%" />
          </a-form-model-item>
        </a-col>
      </template>
      <!--试验-->
      <a-col :xl="8" :sm="12" :xs="24" v-if="baseInfo.taskType == '3'">
        <a-form-model-item label="上次试验时间" prop="beforeExperimentDate" key="beforeExperimentDate">
          <a-input v-model="baseForm.beforeExperimentDate" disabled></a-input>
        </a-form-model-item>
      </a-col>
      <!-- 非户用型电站-巡检、维护、检修、技改、抢修 -->
      <template v-if="baseInfo.isFarm != '1' && ['4', '5', '6', '9', '10'].includes(baseInfo.taskType) && baseInfo.taskType!='10'">
        <a-col :xl="8" :sm="12" :xs="24">
          <a-form-model-item label="设备类型" prop="deviceTypeId">
            <g-cascader
              v-model="baseForm.deviceTypeId"
              :disabled="isDisabled"
              :allowClear="false"
              :options="options"
              @change="deviceTypeChange"
              style="width: 100%" />
          </a-form-model-item>
        </a-col>
        <a-col :xl="8" :sm="12" :xs="24">
          <a-form-model-item label="设备名称" prop="deviceId">
            <a-select
              v-model="baseForm.deviceId"
              placeholder="请输入"
              @change="deviceNameChange"
              :disabled="isDisabled"
              v-if="!isDisabled"
              style="width: 100%"
              show-search>
              <a-select-option
                v-for="item in deviceNameList"
                :key="item.id"
                :data-name="item.name"
                :value="String(item.id)">{{ item.name }}</a-select-option>
            </a-select>
            <a-input v-model="baseForm.deviceName" disabled v-else></a-input>
          </a-form-model-item>
        </a-col>
        <a-col :xl="8" :sm="12" :xs="24">
          <a-form-model-item label="设备编号" prop="deviceNo" show-search>
            <a-select
              mode="multiple"
              :maxTagCount="1"
              v-model="baseForm.deviceNo"
              :placeholder="isDisabled ?'':'请选择'"
              @change="chooseDevice"
              :disabled="isDisabled"
              style="width: 100%">
              <a-select-opt-group>
                <span slot="label" class="cursor-pointer" @click="deviceNoSelectAll">全部</span>
                <a-select-option v-for="(item, k) in deviceNoList" :key="k" :value="item.deviceId">{{ item.deviceId }}
                </a-select-option>
              </a-select-opt-group>
            </a-select>
          </a-form-model-item>
        </a-col>
        <a-col :xl="8" :sm="12" :xs="24">
          <a-form-model-item label="设备数量" prop="deviceNumber">
            <a-input disabled v-model="baseForm.deviceNumber" style="width: 100%" />
          </a-form-model-item>
        </a-col>
      </template>
      <a-col :xl="8" :sm="12" :xs="24" v-if="type && type != '9' && order().processDefKey != 'activiti3273Workflow'">
        <a-form-model-item label="工作状态" prop="workStatus" key="workStatus">
          <a-radio-group v-model="baseForm.workStatus" :disabled="isDisabled">
            <a-radio v-for="item in dictMap.ticket_work_condition" :key="item.dataId" :value="item.dataValue">{{item.dataLable}}</a-radio>
          </a-radio-group>
        </a-form-model-item>
      </a-col>
      <a-col :xl="8" :sm="12" :xs="24"
        v-show="((baseInfo.isFarm != '1' && type != '6') || (baseInfo.isFarm == '1' && baseInfo.taskType != '9' && type == '5')) && order().processDefKey != 'activiti3273Workflow'">
        <a-form-model-item label="责任人" prop="liablePerson" key="liablePerson">
          <a-select v-model="baseForm.liablePerson" :placeholder="isDisabled ?'':'请选择'" :disabled="isDisabled" style="width: 100%" allowClear>
            <a-select-option v-for="(item, k) in assignList" :hidden="item.hidden" :key="k" :value="item.username">
              {{ item.realName }}
            </a-select-option>
          </a-select>
        </a-form-model-item>
      </a-col>
      <template v-if="!['10'].includes(baseInfo.taskType)">
        <a-col :xl="8" :sm="12" :xs="24">
          <a-form-model-item label="作业类别" prop="jobCategory">
            <a-select v-model="baseForm.jobCategory" :disabled="isDisabled" @change="jobCategoryChange" allowClear
              size="default" placeholder="请选择" style="width: 100%;">
              <a-select-option v-for="item in dictMap.job_category" :key="item.dataValue" :value="item.dataValue">
                {{item.dispName}}
              </a-select-option>
            </a-select>
          </a-form-model-item>
        </a-col>
        <a-col :xl="8" :sm="12" :xs="24">
          <a-form-model-item label="作业内容" prop="erpSqJobRiskMappingId">
            <a-select v-model="baseForm.erpSqJobRiskMappingId" :disabled="isDisabled" @change="erpSqJobRiskMappingIdChange" allowClear
              size="default" placeholder="请选择" style="width: 100%;">
              <a-select-option v-for=" item in jobContentOptions" :key="item.dataValue" :value="item.id" :code="item.riskLevel">
                {{item.jobContentName}}
              </a-select-option>
            </a-select>
          </a-form-model-item>
        </a-col>
        <a-col :xl="8" :sm="12" :xs="24">
          <a-form-model-item label="风险等级" prop="riskLevel">
            <a-select v-model="baseForm.riskLevel" :disabled="true" size="default" placeholder="请选择" style="width: 100%;">
              <a-select-option v-for="item in dictMap.risk_level" :key="item.dataValue" :value="item.dataValue">
                {{item.dispName}}
              </a-select-option>
            </a-select>
          </a-form-model-item>
        </a-col>
        <a-col :xl='8' :sm='12' :xs='24'>
          <a-form-model-item label="作业开始日期" prop="riskJobDate">
            <a-date-picker style="width: 100%;" size="default" v-model="baseForm.riskJobDate"
              format="YYYY-MM-DD" valueFormat="YYYY-MM-DD" placeholder="请输入作业开始日期"
              :disabled-date="disabledRiskJobDate"></a-date-picker>
          </a-form-model-item>
        </a-col>
      </template>
      <!-- 通用附件 -->
      <a-col :span="24" v-show="!(baseInfo.isFarm == '1' && baseInfo.taskType == '9')">
        <a-form-model-item label="附件" prop="uploadFileList">
          <uploadFileView
            v-model="baseForm.uploadFileList"
            :disabled="isDisabled"
            :maxNum="5"
            :maxSize="10"
            tip="最多上传5个文件,且上传的附件最大不超过10MB!"
            :multiple="true"
            @change="uploadFileListChange">上传</uploadFileView>
        </a-form-model-item>
      </a-col>
    </a-row>
  </a-form-model>
</template>

<script>
import moment from 'moment';
import initDict from '@/mixins/initDict';
import { getSpaceByPsId } from '@/api/common_gy/plan.js';
import uploadFileView from '@/components/com/fileUploadView';
import { getDate } from '@/api/config/digitalRegulation';
import { getErpPowerStationInfoByPsaId } from '@/api/isolarErp/equipment/archives';
import { formRule } from './formMixins';
import { getUavModels } from '@/api/tango/taskManage.js';

export default {
  name: 'baseForm',
  mixins: [initDict, formRule],
  inject: ['order'],
  components: {
    uploadFileView
  },
  data () {
    return {
      // 任务表单基础信息
      baseForm: {
        isFarm: null, // == '1' 时是户用电站
        taskName: '', // 任务名称
        taskDescription: '', // 任务描述
        cleanType: '', // 清洗方式
        grassType: '', // 除草方式
        deviceTypeId: [], // 设备类型
        deviceId: '', // 设备名称
        deviceNo: [], // 设备编号
        deviceNumber: 0, // 设备数量
        cleanCap: '', // 清洗容量(MW)
        cleanTimes: '', // 清洗次数
        workStatus: '', // 工作状态
        liablePerson: '', // 指派给
        jobCategory: undefined, // 作业类别
        erpSqJobRiskMappingId: undefined, // 作业内容
        riskLevel: undefined, // 风险等级
        riskJobDate: undefined, // 作业开始日期
        uploadFileList: [], // 附件列表
        planTime: [],
        planStartTime: '',
        planEndTime: '',
        deviceName: '',
        isBudget: '',
        beforeExperimentDate: '',
        uavModelId: '',
        stationEnvironment: []
      },
      uavModels: [], // 户用电站的任务类型
      psList: [],
      base_rules: {
        taskName: [{
          required: true,
          message: '请填写任务名称',
          trigger: 'change'
        }], // 任务名称
        planTime: [{
          required: true,
          message: '请选择计划日期',
          trigger: 'change'
        }], // 计划日期
        workStatus: [{
          required: true,
          message: '请选择工作状态'
        }], // 工作状态
        uploadFileList: [{
          required: this.order().processDefKey == 'activiti3273Workflow',
          message: '请上传文件'
        }],
        cleanType: [{
          required: true,
          trigger: 'change',
          message: '请选择清洗方式'
        }],
        grassType: [{
          required: true,
          trigger: 'change',
          message: '请选择除草方式'
        }],
        deviceTypeId: [{
          required: true,
          message: '请选择设备类型'
        }],
        deviceId: [{
          required: true,
          message: '请选择设备名称'
        }],
        cleanCapacity: [{
          required: true,
          message: '请选择清洗容量'
        }],
        cleanTimes: [{
          required: true,
          message: '请选择清洗次数'
        }],
        stationEnvironment: [{
          required: true,
          message: '请选择电站环境',
          trigger: 'change'
        }],
        jobCategory: [{ required: true, message: '请选择作业类别', trigger: 'change' }],
        erpSqJobRiskMappingId: [{ required: true, message: '请选择作业内容', trigger: 'change' }],
        riskLevel: [{ required: true, message: '请选择风险等级', trigger: 'change' }],
        riskJobDate: [{ required: true, message: '请选择作业开始日期', trigger: 'change' }]
      },
      deviceNameList: [], // 设备名称
      deviceNoList: [], // 设备编号

      openStatus: false
    };
  },
  watch: {
    'baseInfo.taskType' (val) {
      this.$refs.baseForm.resetFields();
      if (val == '10') {
        this.getPhysicsPsList(this.baseInfo.psaId);
      }
    },
    'baseInfo.workSubclass' (val) {
      this.baseForm.planTime = [];
      if (val == '2' && this.baseInfo.taskType == '10') {
        this.getUavModelsOptions();
      }
    },
    'baseInfo.psaId' (val) {
      if (val) {
        this.getAssignList(this.baseInfo.psaId, undefined);
      }
    },
    'baseInfo.jobCategory' (val) {
      if (val) {
        this.riskAllocation(this.baseInfo.jobCategory);
      }
    }
  },
  mounted () {},
  created () {
    if (this.baseInfo.taskType == '10') {
      this.getPhysicsPsList(this.baseInfo.psaId);
    }
    this.getDictMap('pdca_clean_type,pdca_grass_type,ticket_work_condition,psa_model,job_category,risk_level');
  },
  computed: {

  },
  methods: {
    moment,
    // 获取清洗容量
    getSpaceByPsId (psId) {
      if (this.baseForm.cleanCap) {
        return;
      }
      let params = {
        psaId: psId || this.baseForm.psId
      };
      getSpaceByPsId(params).then((res) => {
        let result = res.result ? res.result : {};
        this.baseForm.cleanCap = result.gridConnectedScale;
        this.cleanCap = result.gridConnectedScale;
      }).catch(() => {
        this.baseForm.cleanCap = undefined;
      });
    },
    openChangeEvent (status) {
      this.openStatus = status;
    },

    getPhysicsPsList (id) {
      // 获取实体电站
      getErpPowerStationInfoByPsaId({ psaId: id }).then((res) => {
        if (res.result_code == '1') {
          this.psList = res.result_data;
        } else {
          this.psList = [];
        }
      }).catch(() => {
        this.psList = [];
      });
    },
    initForm (id) {
      this.baseForm.deviceTypeId = [];
      this.baseForm.deviceId = undefined;
      this.baseForm.deviceNo = [];
      this.baseForm.deviceNumber = 0;
      this.deviceNameList = []; // 设备名称
      this.deviceNoList = []; // 设备编号
      this.baseForm.assign = undefined;
      this.baseForm.cleanCap = undefined;
      if (this.baseInfo.taskType == '1' || this.baseInfo.taskType == '2') {
        if (this.baseInfo.taskType == '1' && this.baseInfo.workSubclass == '2') {
          this.baseForm.cleanCap = this.baseInfo.cleanCap;
        }
        this.getSpaceByPsId(id);
      } else {
        this.baseForm.cleanCap = '';
        this.cleanCap = '';
      }
      if (this.baseInfo.taskType != '10') {
        this.baseForm.jobCategory = undefined; // 作业类别
        this.baseForm.erpSqJobRiskMappingId = undefined; // 作业内容
        this.baseForm.riskLevel = undefined; // 风险等级
        this.jobContentOptions = [];
      }
      this.getAssignList(id, undefined, true);
    },
    validateForm (type) {
      return new Promise((resolve, reject) => {
        if (this.baseForm.planTime.length > 0) {
          this.baseForm.planStartTime = this.baseForm.planTime[0];
          this.baseForm.planEndTime = this.baseForm.planTime[1];
        }
        this.baseForm.liablePerson = !this.baseForm.liablePerson ? '' : this.baseForm.liablePerson;
        if (type == 0 && this.order().processDefKey != 'activiti3273Workflow') {
          resolve(this.baseForm);
        } else {
          this.$refs.baseForm.validate((valid) => {
            if (valid) {
              resolve(this.baseForm);
            } else {
              resolve(false);
            }
          });
        }
      }).catch(() => {
        resolve(false);
      });
    },
    updatePlanEndTime () {
      let timer = setTimeout(() => {
        if (this.baseInfo.taskType == '1' && this.baseInfo.workSubclass == '2' && !!this.baseForm.cleanCap) {
          this.changePlanTime([moment(this.baseForm.planTime[0]).format('YYYY-MM-DD')]);
        }
      }, 100);
      this.$once('beforeDestory', () => {
        clearTimeOut(timer);
      });
    },
    async changePlanTime (val1, val2) {
      // 转工单 除草 遮挡除草时 计划结束时间的选择放开
      if (this.baseInfo.taskType == '2' && this.baseInfo.workSubclass == '2') {
        return;
      }
      if (this.order().processDefKey != 'activiti3273Workflow') {
        let time = '';
        await this.$parent.registerValidate().then(res => {
          time = true;
        }).catch(() => {
          this.openStatus = false;
        });
        if (val1.length > 1) {
          this.baseForm.planTime = [];
          return;
        }
        if (time) {
          let secondTime = '';
          await getDate({
            taskType: this.baseInfo.taskType,
            scale: (this.baseInfo.taskType == '1' && this.baseInfo.workSubclass == '2')
              ? (this.baseForm.cleanCap ? this.baseForm.cleanCap : this.cleanCap) : (this.cleanCap ? this.cleanCap : this.baseForm.cleanCap),
            subTaskType: this.baseInfo.workSubclass,
            endDate: val1.join(',')
          }).then((res) => {
            if (res.result_code == 1) {
              secondTime = res.result_data;
            }
          }).catch(() => {
            this.baseForm.planTime = [];
            this.$refs.baseForm.validateField('planTime');
          });
          if (secondTime) {
            this.baseForm.planTime = [moment(val1, 'YYYY-MM-DD'), moment(secondTime, 'YYYY-MM-DD')];
            this.$refs.baseForm.clearValidate('planTime');
          }
          this.openStatus = false;
        }
      }
    },

    // 无人机巡检-获取无人机型号
    getUavModelsOptions () {
      getUavModels({}).then(res => {
        this.uavModels = res.result_data;
      });
    },
    // 附件变化触发附件校验
    uploadFileListChange () {
      this.$nextTick(() => {
        this.$refs.baseForm.validateField('uploadFileList');
      });
    }
  }
};
</script>
