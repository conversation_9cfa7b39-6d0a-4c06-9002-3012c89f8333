// sn列表
export const snListColumns = [
  { title: '接入来源', key: 'sourceName', width: 160 },
  { title: '接入SN', key: 'sn', width: 180 },
  { title: '接入采集器名称', key: 'snName', width: 160 },
  { title: '接入状态', key: 'accessStatusName', width: 160 },
  { title: '最后接收时间', key: 'lastReceiveTime', width: 160 },
  { title: '最后通讯时间', key: 'lastMesTime', width: 160 },
  { title: '首次接收时间', key: 'firstReceiveTime', width: 160 },
  { title: '测点总数', key: 'pointCount', width: 160 }
];

// 原始测点接收表
export const pointConfigTab1 = [
  { title: '接收通道号', key: 'channel', width: 160 },
  { title: '接收设备编号', key: 'deviceCode', width: 180 },
  { title: '接收子站名称', key: 'receiveStationName', width: 160 },
  { title: '接收子站类型号', key: 'receiveStationType', width: 160 },
  { title: '测点类型', key: 'pointTypeName', width: 160 },
  { title: '接收测点编号', key: 'originalPoint', width: 160 },
  { title: '接收原始数据', key: 'lastOriginalPointValue', width: 160 },
  { title: '最后接收时间', key: 'lastReceiveTime', width: 160 },
  { title: '原测点名称', key: 'originalPointName', width: 160 },
  { title: '配置状态', key: 'configurationStatusName', width: 160 }
  // { title: '接入状态', key: 'accessStatusName', width: 160 }
];

// 测点绑定设备
export const pointConfigTab2 = [
  { title: '接入SN', key: 'sn', width: 160 },
  { title: '接收设备编号', key: 'deviceCode', width: 180 },
  { title: '接收子站名称', key: 'receiveStationName', width: 160 },
  { title: '接收子站类型号', key: 'receiveStationType', width: 160 },
  { title: '测点数', key: 'pointCount', width: 160 },
  { title: '设备绑定状态', key: 'bindingStatusName', width: 160 },
  { title: '设备编号 ', key: 'psKey', width: 160 },
  { title: '设备名称', key: 'deviceName', width: 160 },
  { title: '设备类型', key: 'deviceTypeName', width: 160 },
  { title: '电站PSID', key: 'psId', width: 160 },
  { title: '电站名称', key: 'psName', width: 160 }
];

// 测点编码配置
export const pointConfigTab3 = [
  { title: '电站PSID', key: 'psId', width: 160 },
  { title: '电站名称', key: 'psName', width: 180 },
  { title: '设备编号', key: 'psKey', width: 160 },
  { title: '设备名称', key: 'deviceName', width: 160 },
  { title: '设备类型', key: 'deviceTypeName', width: 160 },
  { title: '测点数', key: 'pointCount', width: 160 },
  { title: '通讯状态 ', key: 'communicatStatusName', width: 160 }
];

// 接入数据自检
export const pointConfigTab4 = [
  { title: '电站名称', key: 'psName', width: 160 },
  { title: '设备名称', key: 'deviceName', width: 180 },
  { title: '设备编号', key: 'psKey', width: 160 },
  { title: '设备类型', key: 'deviceTypeName', width: 160 },
  { title: '测点类型', key: 'pointTypeName', width: 160 },
  { title: '测点名称', key: 'pointName', width: 160 },
  { title: '测点编码 ', key: 'point', width: 160 },
  { title: '接收原始数据', key: 'lastOriginalPointValue', width: 160 },
  { title: '修正数据', key: 'correctionOriginalPointValue', width: 160 },
  { title: '存储单位', key: 'storageUnit', width: 160 },
  { title: '配置状态', key: 'configurationStatusName', width: 160 }
  // { title: '测点系数', key: 'coefficient', width: 160 },
  // { title: '测点取反', key: 'reverse', width: 160 },
  // { title: '偏移量', key: 'excursion', width: 160 }
];

// 绑定设备--tab1
export const boundDeviceTab1 = [
  { title: '接入SN', key: 'sn', width: 100 },
  { title: '接收设备编号', key: 'deviceCode', width: 120 },
  { title: '接收子站名称', key: 'receiveStationName', width: 120 },
  { title: '接收子站类型', key: 'receiveStationType', width: 120 },
  { title: '测点数', key: 'pointCount', width: 80 }
];

// 配置测点编码--tab1
export const PointCodeTab1 = [
  { title: '设备名称', key: 'deviceName', width: 80 },
  { title: '测点类型', key: 'pointTypeName', width: 80 },
  { title: '接收测点编号', key: 'originalPoint', width: 100 },
  { title: '接收原始数据', key: 'lastOriginalPointValue', width: 100 },
  { title: '原测点名称', key: 'originalPointName', width: 100 }
];
