<template>
  <!-- 联系人信息 -->
  <a-row>
    <a-spin :spinning="loading">
      <a-col v-show="show || type" :span="24" class="top-btn-group">
        <a-button size="default" class="solar-eye-btn-primary" @click="addContacts" style="margin-right:10px;">新增</a-button>
        <a-button size="default" @click="delContacts">删除</a-button>
      </a-col>
      <a-col :span="24">
        <vxe-table class="emp-table" ref="contactsTable" :data="contactsTable" :edit-rules="rules" resizable max-height="457" align="center" border show-overflow highlight-hover-row size="small">
          <vxe-table-column type="checkbox" fixed="left" width="60"></vxe-table-column>
          <vxe-table-column title="序号" type="seq" width="80"></vxe-table-column>
          <vxe-table-column field="contactPerson" title="联系人姓名" show-overflow="title" width="160">
            <template v-slot="{ row }">
              <a-input v-model="row.contactPerson" v-if="show || row.edit" :maxLength='50' size="default" @blur="row.contactPerson = $trim($event)" style="width: 95%;"></a-input>
              <span v-else>{{getLabel(row.contactPerson,null)}}</span>
            </template>
          </vxe-table-column>
          <vxe-table-column field="contactRelationship" title="与本人关系" show-overflow="title" width="160">
            <template v-slot="{ row }">
              <a-select v-model="row.contactRelationship" v-if="show || row.edit" :allowClear="false" size="default" style="width:100%;">
                <a-select-option v-for="(option,index) in dictMap['contact_relationship']" :key="index" :value="option.dataValue">{{option.dataLable}}</a-select-option>
              </a-select>
              <span v-else>{{getLabel(row.contactRelationship,dictMap['contact_relationship'])}}</span>
            </template>
          </vxe-table-column>
          <vxe-table-column field="contactTel" title="联系方式" show-overflow="title" width="200">
            <template v-slot="{ row }">
              <a-input v-model="row.contactTel" v-if="show || row.edit" :maxLength='11' size="default" @blur="row.contactTel = $trim($event)" style="width: 95%;"></a-input>
              <span v-else>{{getLabel(row.contactTel,null)}}</span>
            </template>
          </vxe-table-column>
          <vxe-table-column field="address" title="联系人地址/工作单位" show-overflow="title" min-width="240">
            <template v-slot="{ row }">
              <a-input v-model="row.address" v-if="show || row.edit" size="default" :maxLength='200' @blur="row.address = $trim($event)" style="width: 95%;"></a-input>
              <span v-else>{{getLabel(row.address,null)}}</span>
            </template>
          </vxe-table-column>
          <vxe-table-column :visible="type" title="操作" fixed="right" width="140" :resizable="false">
            <template slot-scope="scope">
              <template v-if="scope.row.edit">
                <a-button size="default" icon="save" :loading="loading" title="保存" @click="saveRow(scope.$rowIndex)"></a-button>
                <a-button size="default" icon="arrow-left" title="取消" @click="cacelRow(scope.$rowIndex)"></a-button>
              </template>
              <template v-else>
                <a-button size="default" type="primary" icon="form" title="编辑" @click="editRow(scope.$rowIndex)"></a-button>
              </template>
            </template>
          </vxe-table-column>
          <template v-slot:empty>
            <span>查询无数据</span>
          </template>
        </vxe-table>
      </a-col>
    </a-spin>
  </a-row>
</template>

<script>
import initDict from '@/mixins/initDict';
import { delErpEmpEmerContactById, saveEmpEmerContact } from '@/api/isolarErp/employee/employee';
import { TENANT_ID, USER_NAME } from '@/store/mutation-types';
export default {
  mixins: [initDict],
  props: {
    type: {
      type: Boolean,
      default: false
    },
    empId: {
      type: [String, Number],
      default: ''
    }
  },
  data () {
    return {
      contactsTable: [],
      show: false,
      rules: {
        contactPerson: [
          { required: true, message: '请填写联系人姓名', trigger: 'change' }
        ],
        contactRelationship: [
          { required: true, message: '请选择与本人关系', trigger: 'change' }
        ],
        contactTel: [
          { required: true, message: '联系方式', trigger: 'change' },
          { pattern: /^[1][3|4|5|6|7|8|9][0-9]{9}$/, message: '请输入正确的联系方式' }
        ]
      },
      loading: false,
      oldRow: null
    };
  },
  created () {
    this.getDictMap('contact_relationship');
  },
  methods: {
    // 初始化数据
    init (data, type) {
      this.show = (type && type != '3');
      data = (data || []);
      data.forEach(item => {
        item.edit = false;
      });
      this.contactsTable = data;
    },
    // 新增
    addContacts () {
      let row = {
        contactPerson: '',
        contactRelationship: '',
        contactTel: '',
        address: ''
      };
      if (this.type) {
        for (let item of this.contactsTable) {
          if (item.edit) {
            this.$message.warning('请先保存当前编辑的数据!');
            return;
          }
        }
        row.edit = true;
        this.oldRow = null;
      }
      this.contactsTable.unshift(row);
    },
    // 行编辑
    editRow (index) {
      for (let item of this.contactsTable) {
        if (item.edit) {
          this.$message.warning('请先保存当前编辑的数据!');
          return;
        }
      }
      this.oldRow = Object.assign({}, this.contactsTable[index]);
      this.contactsTable[index].edit = true;
    },
    // 行保存
    async saveRow (index) {
      const self = this;
      self.loading = true;
      const errMap = await self.$refs['contactsTable'].validate(self.contactsTable[index]).catch(errMap => errMap);
      if (errMap) {
        self.loading = false;
        return;
      }
      self.$confirm({
        title: '确定保存修改吗?',
        okText: '确定',
        cancelText: '取消',
        onOk () {
          let row = self.contactsTable[index];
          row.sysTenantId = Vue.ls.get(TENANT_ID);
          row.userAccount = Vue.ls.get(USER_NAME);
          row.empId = self.empId;
          saveEmpEmerContact({ 'erpEmpEmerContact': [row] }).then(res => {
            self.loading = false;
            if (res.result_code == '1') {
              self.$message.success('操作成功');
              self.contactsTable[index].edit = false;
              self.$emit('refresh');
            } else {
              self.$message.warning(res.result_msg);
            }
          }).catch(() => {
            self.loading = false;
          });
        },
        onCancel () {
          self.loading = false;
        }
      });
    },
    // 行-取消保存
    cacelRow (index) {
      const self = this;
      self.$confirm({
        title: '确定放弃修改吗?',
        okText: '确定',
        cancelText: '取消',
        onOk () {
          // 用于区分修改的是已有数据还是新加的数据
          if (self.oldRow) {
            self.contactsTable[index] = self.oldRow;
            const data = self.contactsTable;
            self.contactsTable = [];
            self.$nextTick(() => {
              self.contactsTable = data;
            });
          } else {
            self.contactsTable.splice(index, 1);
          }
        }
      });
    },
    // 删除
    delContacts () {
      const self = this;
      const select = self.$refs.contactsTable.getCheckboxRecords();
      if (select.length < 1) {
        self.$message.warning('请至少勾选一条数据');
        return;
      }
      self.$confirm({
        title: '确定要删除吗?',
        okText: '确定',
        cancelText: '取消',
        onOk () {
          if (self.type) {
            let ids = [];
            select.forEach(item => {
              if (item.id) {
                ids.push(item.id);
              }
            });
            if (ids.length == 0) {
              self.remove(select);
            } else {
              self.loading = true;
              delErpEmpEmerContactById({ 'ids': ids }).then(res => {
                self.loading = false;
                if (res.result_code == '1') {
                  self.$message.info('数据删除成功');
                  self.remove(select);
                } else {
                  self.$message.warning(res.result_msg);
                }
              }).catch(() => {
                self.loading = false;
              });
            }
          } else {
            self.remove(select);
          }
        }
      });
    },
    // 页面移除数据
    remove (data) {
      let _XIDS = [];
      data.forEach(item => {
        _XIDS.push(item._XID);
      });
      this.contactsTable = this.contactsTable.filter(item => !_XIDS.includes(item._XID));
    },
    // 主页-获取数据
    getData () {
      return this.contactsTable;
    },
    // 数据校验
    async validate () {
      const errMap = await this.$refs['contactsTable'].validate(true).catch(errMap => errMap);
      return errMap;
    }
  }
};
</script>

<style lang="less" scoped>
</style>
