<!--光功率预测柜/今日预测曲线-->
<template>
  <div class="seven-day-chart" :style="{ height }"></div>
</template>
<script>
import PropTypes from 'ant-design-vue/es/_util/vue-types';
import echarts from '@/utils/enquireEchart';
import ChartResizeMixins from '@/mixins/ChartResizeMixins';
import { mapGetters } from 'vuex';

const itemStyleColor = {
  type: 'linear',
  x: 0,
  y: 0,
  x2: 0,
  y2: 1,
  colorStops: [
    {
      offset: 0,
      color: 'rgba(64, 170, 255, 0)' // 0% 处的颜色
    },
    {
      offset: 1,
      color: 'rgba(64, 170, 255, 0.53)' // 100% 处的颜色
    }
  ],
  global: false // 缺省为 false
};
const itemStyleColorFn = (start = 'rgba(64, 170, 255, 0)', end = 'rgba(64, 170, 255, 0.4)') => ({
  type: 'linear',
  x: 0,
  y: 0,
  x2: 0,
  y2: 1,
  colorStops: [
    {
      offset: 0,
      color: start // 0% 处的颜色
    },
    {
      offset: 1,
      color: end // 100% 处的颜色
    }
  ],
  global: false // 缺省为 false
});
export default {
  name: 'SevenDayChart',
  props: {
    dataResult: PropTypes.array.def([]),
    height: PropTypes.string,
    tipsData: PropTypes.array.def([
      {
        key: 'shortSevenPrecision',
        title: '短期7点精度',
        multiple: 1,
        toFixed: 1,
        gradient: ['#F97475', '#7575A0']
      },
      {
        key: 'shortTwelvePrecision',
        title: '短期12点精度',
        multiple: 1,
        toFixed: 1,
        gradient: ['#F89D69', '#6F565C']
      },
      {
        key: 'superShortPrecision',
        title: '超短期精度',
        multiple: 1,
        toFixed: 1,
        gradient: ['#B6ADFC', '#598EE8']
      },
      {
        key: 'shortUploadRate',
        title: '短期上传率',
        multiple: 1,
        toFixed: 1,
        gradient: ['#40AAFF', '#266FB9']
      },
      {
        key: 'superShortUploadRate',
        title: '超短期上传率',
        multiple: 1,
        toFixed: 1,
        gradient: ['#A8BED1', '#375889']
      }
    ])
  },
  mixins: [ChartResizeMixins],
  data() {
    return {
      myChart: null
    };
  },
  mounted() {
    this.$nextTick(() => {
      const el = document.querySelector('.seven-day-chart');
      this.myChart = echarts.init(el);
      setTimeout(() => {
        this.myChart.resize();
      }, 50);
    });
    this.$once('hook:beforeDestroy', () => {
      echarts.dispose(this.myChart);
    });
  },
  computed: {
    ...mapGetters(['theme']),
    isDark() {
      return this.theme === 'dark';
    }
  },
  watch: {
    dataResult: {
      handler() {
        this.myChart && this.myChart.clear();
        const option = this.getOption() || {};
        this.myChart && this.myChart.setOption(option, true);
      },
      deep: true
    },
    theme: {
      handler() {
        const option = this.getOption() || {};
        this.myChart && this.myChart.setOption(option, true);
      }
    }
  },
  methods: {
    getOption() {
      return {
        grid: {
          left: '5%',
          right: '5%',
          top: '10%',
          bottom: '10%'
        },
        legend: {
          data: this.tipsData.map((item) => item.title),
          textStyle: {
            color: this.isDark ? 'rgba(255, 255, 255, 1)' : '#333'
          }
        },
        tooltip: {
          trigger: 'axis',
          triggerOn: 'mousemove|click',
          axisPointer: {
            type: 'shadow',
            shadowStyle: {
              color: itemStyleColor
            }
          },
          className: 'di-chart-tooltip',
          borderColor: this.isDark ? 'rgba(189, 226, 255, 0.4)' : '#DCDCDC',
          borderWidth: 1,
          formatter: (params) => {
            const newParams = params.filter((item) => item.axisIndex !== 1);
            let html = `<div class="title font-14">${newParams[0].axisValue}</div>`;
            html += '<div class="content">';
            newParams.forEach((item) => {
              html += `
                  <div  class="content-item">
                    <div class="flex-start">
                      ${item.marker}
                      <span class="tooltip-item-label">${item.seriesName}：</span>
                    </div>
                    <span>
                      <span class="text-2-color font-500">${
  (item.value && item.value.toFixed((item.data && item.data.toFixed) || 2)) || '--'
}</span>
                      <span class="content-unit">%</span>
                    </span>
                  </div>
              `;
            });
            html += '</div>';
            return html;
          }
        },
        xAxis: {
          type: 'category',
          axisLabel: {
            color: this.isDark ? '#8DCBFB' : '#333'
          },
          axisLine: {
            lineStyle: {
              color: this.isDark ? 'rgba(51, 98, 159, 0.4)' : '#DCDCDC'
            }
          },
          axisTick: {
            lineStyle: {
              color: this.isDark ? 'rgba(255, 255, 255, 0.7)' : '#E8E8E8',
              width: 2
            }
          },
          data: (this.dataResult || []).map((item) => item.time)
        },
        yAxis: [
          {
            type: 'value',
            axisLabel: { color: this.isDark ? '#8DCBFB' : '#666666' }, // y轴刻度线 最上面一个y轴才显示顶部刻度label
            splitLine: {
              lineStyle: {
                color: this.isDark ? 'rgba(97, 161, 213, 0.19)' : '#E8E8E8'
              }
            },
            name: '%',
            nameTextStyle: {
              color: this.isDark ? '#8DCBFB' : '#333',
              padding: [0, 0, 0, -25]
            }
          }
        ],
        series: this.tipsData.map((item) => ({
          name: item.title,
          type: 'bar',
          // barCategoryGap: 60,
          barMaxWidth: 20,
          data: (this.dataResult || []).map((innerItem) => ({
            value: innerItem[item.key],
            toFixed: item.toFixed,
            multiple: item.multiple
          })),
          itemStyle: {
            color: Array.isArray(item.gradient) ? itemStyleColorFn(item.gradient[0], item.gradient[1]) : item.gradient
          }
        }))
      };
    }
  }
};
</script>

<style scoped lang="less">
.seven-day-chart {
  width: 100%;
  height: 400px;
}
</style>
