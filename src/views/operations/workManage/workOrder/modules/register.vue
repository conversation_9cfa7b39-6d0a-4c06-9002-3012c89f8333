<!-- 工单 - 缺陷登记基本信息 -->
<template>
  <a-form-model ref="registerForm" :selfUpdate="true" :model="register" :rules="rules" :labelCol="{ style:'width: 120px' }" :wrapperCol="{ style:'width: calc(100% - 120px)' }">
    <a-row :gutter="24">
      <!-- <a-col :xl='8' :sm='12' :xs='24'>
        <a-form-model-item label="缺陷来源">
          <a-select disabled v-model="register.faultFrom" placeholder="请选择" :allowClear="false"
            style="width: 100%;">
            <a-select-option value="0">设备缺陷</a-select-option>
            <a-select-option value="1">安全隐患</a-select-option>
          </a-select>
        </a-form-model-item>
      </a-col> -->
      <a-col :xl='8' :sm='12' :xs='24'>
        <a-form-model-item label="电站名称" prop="psId">
          <psa-select @select="psChange" v-model="register.psId" :disabled="['2','9'].includes(type)"></psa-select>
          <!-- <ps-tree-select @change="psChange" v-model="register.psId" :disabled="['2','9'].includes(type)" style="width: 100%;" :allowClear="false"/> -->
        </a-form-model-item>
      </a-col>
      <a-col :xl='8' :sm='12' :xs='24'>
        <a-form-model-item label="设备状态" prop="twoDeviceRunSts">
          <a-select v-model="register.twoDeviceRunSts" placeholder="请选择" style="width: 100%;" @change="getfaultNameList">
            <a-select-option v-for="item in dict.two_device_run_sts" :key="item.key" :value="item.dataValue">{{item.dataLable}}</a-select-option>
          </a-select>
        </a-form-model-item>
      </a-col>
      <a-col :span="24">
        <a-form-model-item label="缺陷描述" prop="faultContent">
          <a-textarea v-if="type != '9'" :max-length="300" v-model="register.faultContent" :title="register.faultContent" placeholder="请输入" style="width: 100%;" />
          <!-- 告警转工单时使用勾选的方式 -->
          <a-checkbox-group v-else v-model="register.faultContent" :options="faultContentOptions"/>
        </a-form-model-item>
      </a-col>
      <a-col :xl='8' :sm='12' :xs='24'>
        <a-form-model-item label="设备类型" prop="deviceTypeList">
          <g-cascader v-model="register.deviceTypeList" :options="options"
            @change="deviceTypeChange" style="width: 100%;" />
        </a-form-model-item>
      </a-col>
      <!--  -->
      <a-col :xl='8' :sm='12' :xs='24'>
        <a-form-model-item label="设备名称" prop="faultDeviceName">
          <a-select @change="changeDeviceName" v-model="register.faultDeviceName"
            placeholder="请选择" allowClear style="width: 100%;" show-search>
            <a-select-option v-for="item in deviceNameList" :key="item.id" :value="item.name">{{item.name}}
            </a-select-option>
          </a-select>
        </a-form-model-item>
      </a-col>
      <a-col :xl='8' :sm='12' :xs='24'>
        <a-form-model-item label="设备编号" prop="faultDeviceUuid">
          <a-select v-model="register.faultDeviceUuid" placeholder="请选择" allowClear
            style="width: 100%;" show-search>
            <a-select-option v-for="item in deviceNoList" :key="item.deviceId" :value="item.deviceId">
              {{item.deviceId}}
            </a-select-option>
          </a-select>
        </a-form-model-item>
      </a-col>
      <a-col :xl='8' :sm='12' :xs='24'>
        <a-form-model-item label="故障名称" prop="faultCause">
          <a-select v-model="register.faultCause" placeholder="请选择" allowClear
            style="width: 100%;">
            <a-select-option v-for="aa in  faultNameList" :key="aa.id.toString()" :value="aa.id.toString()">
              {{aa.name}}
            </a-select-option>
          </a-select>
        </a-form-model-item>
      </a-col>
      <a-col :xl='8' :sm='12' :xs='24'>
        <a-form-model-item label="缺陷类别" prop="faultClassify">
          <a-select v-model="register.faultClassify" disabled placeholder="请选择" style="width: 100%;">
            <a-select-option v-for="item in dict.two_fault_classify" :key="item.key" :value="item.dataValue">{{item.dataLable}}</a-select-option>
          </a-select>
        </a-form-model-item>
      </a-col>
      <a-col :xl='8' :sm='12' :xs='24'>
        <a-form-model-item label="发现人" prop="findUser">
          <a-input :max-length="150" :title="register.findUser" v-model="register.findUser" disabled
            style="width: 100%;" />
        </a-form-model-item>
      </a-col>
      <a-col :xl='8' :sm='12' :xs='24'>
        <a-form-model-item label="发现时间" prop="findTimeString">
          <a-date-picker style="width:100%" show-time format="YYYY-MM-DD HH:mm" :allowClear="false"
           valueFormat="YYYY-MM-DD HH:mm" placeholder="请选择" v-model="register.findTimeString" :disabled-date="disabledFindTime"/>
        </a-form-model-item>
      </a-col>
      <a-col :span="24">
        <a-form-model-item label="备注" prop="faultRemark">
          <a-textarea :max-length="1000" :title="register.faultRemark" v-model="register.faultRemark"
           placeholder="请输入" style="width: 100%;" />
        </a-form-model-item>
      </a-col>
      <a-col :span="24">
        <a-form-model-item label="附件">
          <uploadFile v-model="register.docs" :maxNum="5" tip="最多上传5个文件,且上传的附件最大不超过10MB!" :tipIsInline="true">上传</uploadFile>
        </a-form-model-item>
      </a-col>
    </a-row>
  </a-form-model>
</template>

<script>
import moment from 'moment';
import { USER_INFO } from '@/store/mutation-types';
import uploadFile from '@/components/common_gy/uploadFiles';
import { getFaultName, getDeviceIdList, getDeviceTypeTree, getDeviceNameList, getDeviecClassByDeviceId } from '@/api/common_gy/faultManage.js';
export default {
  name: 'register',
  props: {
    // 数据字典
    dict: {
      type: Object,
      default: null
    },
    // 操作区分
    type: {
      type: [String, Number],
      default: ''
    }
  },
  components: {
    uploadFile
  },
  data () {
    return {
      // 普通表单
      register: {
        faultFrom: '0', // 缺陷来源
        faultNo: undefined,
        faultId: undefined,
        psId: undefined, // 电站id
        deviceTypeList: undefined, // 设备类型
        faultDeviceName: undefined, // 设备名称
        faultDeviceUuid: undefined, // 设备编号
        faultSource: undefined, // 缺陷来源
        faultClassify: undefined, // 缺陷类别
        faultCause: undefined, // 故障名称
        faultContent: undefined, // 缺陷描述
        findUser: undefined, // 发现人
        findTimeString: undefined, // 发现时间
        twoDeviceRunSts: undefined, // 设备状态
        predictRecoveTime: undefined, // 预计消除时间
        eliminateId: undefined, // 消除人
        actualResolveTime: undefined, // 实际消除时间
        dealProgress: undefined, // 处理进度
        faultStopScale: undefined, // 故障容停
        totalRepairTime: undefined, // 累计修复时间
        totalLossPower: undefined, // 累计损失
        delayCause: undefined, // 逾期原因
        acceptor: undefined, // 验收人
        acceptanceTime: undefined, // 验收时间
        acceptType: undefined, // 验收情况
        faultRemark: undefined, // 备注
        docs: [],
        isFarm: ''
      },
      rules: {
        faultRemark: [{
          required: false,
          message: ''
        }],
        psId: [{
          required: true,
          message: '请选择电站'
        }],
        deviceTypeList: [{
          required: true,
          message: '请选择设备类型'
        }], // 设备类型
        faultDeviceName: [{
          required: true,
          message: '请选择设备名称'
        }], // 设备名称
        faultClassify: [{
          required: true,
          message: '请在缺陷定级中配置缺陷类别'
        }], // 缺陷类别
        faultContent: [{
          required: true,
          message: '缺陷描述为必填项'
        }], // 缺陷描述
        findUser: [{
          required: true,
          message: '请选择发现人'
        }], // 发现人
        findTimeString: [{
          required: true,
          message: '请选择发现时间'
        }], // 发现时间
        predictRecoveTime: [{
          required: true,
          message: '请选择预计消除时间'
        }],
        eliminateId: [{
          required: true,
          message: '请选择消除人'
        }], // 消除人
        faultCause: [{
          required: true,
          message: '请选择故障名称'
        }], // 故障名称
        twoDeviceRunSts: [{
          required: true,
          message: '请选择设备状态'
        }]
      },
      faultContentOptions: [], // 告警转工单数据
      options: [], // 设备类型数据
      faultNameList: [], // 故障名称
      deviceNameList: [], // 设备名称
      deviceNoList: [] // 设备编号
    };
  },
  methods: {
    moment,
    initRegister (register) {
      let user = Vue.ls.get(USER_INFO);
      if (!register) {
        this.register.findUser = user.realname;
        this.register.findTimeString = moment().format('YYYY-MM-DD HH:mm');
      } else {
        if (this.type == '9') {
          this.faultContentOptions = register.faultContentOptions;
          this.register.faultContent = register.faultContentOptions.map(item => item.value);
          this.register.findUser = user.realname;
          this.register.findTimeString = moment().format('YYYY-MM-DD HH:mm');
        }
        this.register.twoDeviceRunSts = register.twoDeviceRunSts;
        if (register.psId) {
          this.register.psId = register.psId;
          register.isEdit = true;
          // 初始化其他数据
          this.psChange(register);
          this.register.deviceTypeList = (Array.isArray(register.deviceTypeList) ? register.deviceTypeList : undefined);
          this.deviceTypeChange(register.deviceTypeList);
          this.changeDeviceName(register.faultDeviceName);
        }
        this.$nextTick(() => {
          Object.assign(this.register, register);
          delete this.register.faultContentOptions;
        });
      }
    },
    // 校验表单返回表单数据
    getValidate () {
      return new Promise((resolve, reject) => {
        this.$refs.registerForm.validate((valid) => {
          if (valid) {
            if (this.type == '9') {
              this.register.faultSource = '1';
              let alarmIds = this.register.faultContent;
              this.register.alarmIds = alarmIds;
              let arr = alarmIds.map(item => this.getLabel(item, this.faultContentOptions));
              this.register.faultContent = arr.join(',').substr(0, 300);
            }
            resolve(this.register);
          } else {
            reject(new Error());
          }
        });
      });
    },
    // 电站change事件
    psChange (obj) {
      this.register.deviceTypeList = undefined;
      this.register.faultDeviceUuid = undefined;
      this.register.faultDeviceName = undefined;
      this.register.faultCause = undefined;
      this.register.faultClassify = undefined;
      this.options = [];
      this.faultNameList = [];
      this.deviceNoList = [];
      // if(!psId){
      //   return;
      // }
      // this.register.psId = obj.isEdit? obj.psId : obj.id
      this.register.isFarm = obj.isFarm;
      // 获取设备树
      let map = {
        'psId': obj.isEdit ? obj.psId : (obj.id ? obj.id : obj.psId)
      };
      this.$emit('change', obj.isFarm);
      getDeviceTypeTree(map).then((res) => {
        this.options = res.result;
      }).catch(() => {
        this.options = [];
      });
    },
    // 设备类型change事件
    deviceTypeChange (val) {
      this.register.faultCause = undefined;
      this.register.faultDeviceUuid = undefined;
      this.register.faultDeviceName = undefined;
      this.register.faultClassify = undefined;
      this.faultNameList = [];
      this.deviceNoList = [];
      if (!Array.isArray(val) || !val.length || !this.register.psId) {
        this.register.deviceTypeList = undefined;
        return;
      }
      // 获取设备名称
      let map = {
        deviceTypeId: val[val.length - 1],
        psId: this.register.psId
      };
      getDeviceNameList(map).then((res) => {
        this.deviceNameList = res.result;
      }).catch(() => {
        this.deviceNameList = [];
      });
      // 获取故障名称
      this.getfaultNameList(null);
    },
    /*
        获取故障名称
      */
    getfaultNameList (val) {
      if (this.register.twoDeviceRunSts && Array.isArray(this.register.deviceTypeList) && this.register.deviceTypeList.length) {
        let map = {
          deviceTypeId: this.register.deviceTypeList[this.register.deviceTypeList.length - 1],
          equipmentStatus: this.register.twoDeviceRunSts
        };
        getFaultName(map).then((res) => {
          this.faultNameList = res.result;
          if (val) {
            this.register.faultCause = undefined;
          }
        }).catch(() => {
          this.faultNameList = [];
          this.register.faultCause = undefined;
        });
        // 设置缺陷类别
        getDeviecClassByDeviceId({
          typeId: map.deviceTypeId,
          faultType: this.register.twoDeviceRunSts
        }).then((res) => {
          this.register.faultClassify = res.result;
        });
      } else {
        this.faultNameList = [];
        this.register.faultClassify = this.register.faultCause = undefined;
      }
    },
    // 设备名称change事件
    changeDeviceName (val) {
      this.register.faultDeviceUuid = undefined;
      this.deviceNoList = [];
      let type = this.register.deviceTypeList;
      if (!val || !this.register.psId || !Array.isArray(type) || !type.length) {
        return;
      }
      let map = {
        deviceTypeId: type[type.length - 1],
        psId: this.register.psId,
        name: val
      };
      getDeviceIdList(map).then((res) => {
        this.deviceNoList = res.result;
      });
    },
    // 发现时间不能超过系统当前时间
    disabledFindTime (endValue) {
      const startValue = moment().add(1, 'days').format('YYYY-MM-DD HH:mm');
      if (!endValue || !startValue) {
        return false;
      }
      return moment(endValue, 'YYYY-MM-DD HH:mm').valueOf() >= moment(startValue, 'YYYY-MM-DD HH:mm').valueOf();
    },
    // 清除表单信息
    clearValidate () {
      Object.assign(this.register, this.$options.data().register);
      this.$refs['registerForm'].resetFields();
      this.$refs['registerForm'].clearValidate();
    }
  }
};
</script>
<style lang="less" scoped>
  :deep(.ant-form-item) {
    width: 100%;
    display: inline-flex;
  }
</style>
