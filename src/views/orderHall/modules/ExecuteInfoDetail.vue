<template>
  <detail-layout :labelList="labelList" :form="baseForm" title="执行信息"> </detail-layout>
</template>

<script>
export default {
  name: 'ExecuteInfoDetail',
  props: {
    baseForm: {
      type: Object,
      default: () => {
        return {};
      }
    }
  },
  data () {
    return {
      labelList: [
        {
          label: '现场情况',
          key: 'sceneConditionName',
          func: (params) => {
            return this.conditionShow;
          }
        },
        {
          label: '故障停运容量(MWp)',
          key: 'faultCap',
          func: (params) => {
            return this.realConditionShow;
          }
        },
        {
          label: '累计损失电量(万KWh)',
          key: 'totalLossPower',
          func: (params) => {
            return this.realConditionShow;
          }
        },
        {
          label: '派发/领取时间',
          key: 'realStartTime'
        },
        {
          label: '执行提交时间',
          key: 'impCommitTime'
        },
        {
          label: '累计修复时间(h)',
          key: 'totalRepairTime',
          func: (params) => {
            return this.realConditionShow;
          }
        },
        {
          label: '逾期原因',
          key: 'overdueReason',
          func: (params) => {
            return this.realConditionShow;
          }
        },

        {
          label: '厂家名称',
          key: 'meteoMaker',
          func: (params) => {
            // '系统维护/气象服务'
            return params.taskType === '4' && params.workSubclass === '20';
          }
        },
        {
          label: '备注',
          key: 'conditionRemark',
          span: 24,
          func: (params) => {
            return this.realConditionShow || !['7', '8'].includes(params.taskType);
          }
        },
        {
          label: '现场图片',
          key: 'sceneFileList',
          span: 24,
          type: 'file:text',
          func: (params) => {
            return this.otherConditionShow;
          }
        }
      ]
    };
  },
  methods: {
    show () {
      return ['7', '8'].includes(this.baseForm.taskType);
    }
  },
  computed: {
    conditionShow () {
      const formData = this.baseForm;
      // 6: 智能巡检
      return ['4', '5', '6'].includes(formData.orderSource) && ['7', '8'].includes(formData.taskType);
    },
    realConditionShow () {
      const formData = this.baseForm;
      if (this.conditionShow) {
        return formData.sceneCondition === '1' && ['7', '8'].includes(formData.taskType);
      }
      return ['7', '8'].includes(formData.taskType);
    },
    otherConditionShow () {
      const formData = this.baseForm;
      if (this.conditionShow) {
        return formData.sceneCondition && formData.sceneCondition !== '1';
      }
      return false;
    }
  }
};
</script>
<style lang="less" scoped>
:deep(.ant-form-item) {
  width: 100%;
  display: inline-flex;
}

:deep(.ant-steps-vertical .ant-steps-item-description) {
  padding: 24px 0 0;
}

.step-box {
  :deep(.ant-form-item-label) {
    width: 134px !important;
  }
}

.last-form-item {
  :deep(.ant-form-item-label) {
    width: 62px !important;
  }

  :deep(.ant-form-item-control-wrapper) {
    width: calc(100% - 62px) !important;
  }
}

.example-btn {
  font-size: 12px !important;
  padding: 0 8px !important;
  height: 22px !important;
  margin-left: 16px;
}

.title-box {
  margin-top: -8px;
}
</style>
