<template>
  <div class="ticket-info">
    <TicketInfoDetail :type="type" :baseForm="formData" @toTicketEven="toTicketEven"/>
    <!-- <drawer-view ref="drawerForm" :parentId="parentId" /> -->
  </div>
</template>

<script>
import PropTypes from 'ant-design-vue/es/_util/vue-types';
import TicketInfoDetail from './TicketInfoDetail';
import TaskAndOrder from '@/mixins/TaskAndOrder';
export default {
  name: 'TicketInfo',
  mixins: [TaskAndOrder],
  components: { TicketInfoDetail },
  props: {
    ticketRequired: PropTypes.bool.def(false),
    formData: PropTypes.object,
    dictMap: PropTypes.object,
    type: PropTypes.string,
    isDisabled: PropTypes.bool,
    parentId: {
      type: String,
      required: true,
      default: ''
    }
  },
  data () {
    return {};
  },
  methods: {
    // 两票详情跳转
    toTicketEven (row) {
      // this.$refs.drawerForm.init('3', { fromOrderHallToTicket: true, id: row.ticketId }, '/distributed/ticketManage/modules/ticketForm');
    }
  }
};
</script>

<style lang="less" scoped>
.ticket-info {
  .link-text {
    display: flex;
    align-items: center;
    cursor: pointer;
    height: 32px;
    line-height: 32px;
    margin-top: 4px;
    .ticket-label{
      margin: 0 4px;
    }
  }
  .resolve-color {
    color: #1366ec;
  }
  .end-color {
    color: #2ba471;
  }
  .custom-select-col {
    :deep(.ant-form-item-control) {
      line-height: normal;
    }
  }
}
</style>
