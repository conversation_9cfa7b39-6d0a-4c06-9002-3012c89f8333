<template>
  <div class="drawer-form-com" id="orderHallBox">
    <div class="drawer-form-content">
      <a-spin size="small" :spinning="loading" style="height: 100%">
        <a-form-model
          :model="formData"
          ref="orderHallForm"
          :labelCol="{ style: 'width: 150px' }"
          :wrapperCol="{ style: 'width: calc(100% - 150px)' }"
          style='display: flex;flex-direction: column;gap: 24px'
        >
            <!-- 基本信息 -->
            <BasicInfo :formData.sync="formData" :type="type" :dictMap="dictMap" :isDroneOrder='isDroneOrder'/>
            <!-- 设备信息  -->
            <DeviceInfo :formData.sync="formData" :type="type" ref="device" :dictMap="dictMap" :isDroneOrder='isDroneOrder'/>
             <!-- 两票信息 -->
            <TicketInfo :formData.sync="formData" :type="type" :dictMap="dictMap" :isDisabled="formData.isHangUp == '1'"
              v-if="showExecuteOrSteps" @validateOrderFiled='validateOrderFiled'/>
            <!-- 风险信息 -->
            <!-- <RiskInfo :formData="formData" :type="type" :dictMap="dictMap" /> -->
            <!-- 附件信息 -->
            <!-- <AnnexInfo :formData="formData" :type="type" :dictMap="dictMap" /> -->
            <!-- 执行信息 -->
            <ExecuteInfo
              :formData="formData"
              :type="type"
              :isDisabled="formData.isHangUp == '1'"
              v-if="showExecuteOrSteps"
              :dictMap="dictMap"
              :rowInfo="rowInfo"
              @validateField='validateField'
              :isDroneOrder='isDroneOrder'
            />
            <!-- 作业步骤 -->
            <WorkSteps
              :formData="formData"
              :type="type"
              :isDisabled="formData.isHangUp == '1'"
              v-if="showExecuteOrSteps"
              :rowInfo="rowInfo"
            />
            <HangUpReason ref="hangupReason" v-if="step == 1" :formData="formData" :type="type" :show="reasonShow" />
        </a-form-model>
      </a-spin>
    </div>
    <div class="drawer-form-foot">
      <throttle-button v-if="['3'].includes(type)" :loading="loading" label="返回" @click="$emit('cancel')" />
      <throttle-button
        v-if="['6', '5'].includes(type)"
        :loading="loading"
        label="提交"
        @click="reviceAndAssignSubmit"
      />
      <template v-else-if="type == '4' || type == '8'">
        <throttle-button label="通过" :loading="loading" @click="checkOrder('1')" />
        <throttle-button label="退回" :loading="loading" @click="checkOrder('0')" />
        <throttle-button label="取消" :disabled="loading" type="info"  @click="$emit('cancel')" />
      </template>
      <template v-if="type == '7'">
        <template v-if="step == 0">
          <div v-if="executeButtons">
            <template v-if="formData.isHangUp == '0'">
              <a-button :loading="loading" @click="resolveQuery">挂起</a-button>
              <a-button :loading="loading" @click="backNullifyEven('1')">退回</a-button>
              <throttle-button :loading="loading" label="保存" @click="confirmOrderResolve('2')" />
              <a-button :loading="loading" @click="confirmOrderResolve('1')" class="ant-btn-primary">提交</a-button>
            </template>
            <template v-else-if="formData.isHangUp == '1'">
              <throttle-button :loading="loading" label="开启" @click="hangUp('0')" />
            </template>
          </div>
        </template>
        <template v-else-if="step == 1">
          <throttle-button :loading="loading" label="返回" @click="hangupBack" />
          <throttle-button :loading="loading" label="提交" @click="hangupSubmit" />
        </template>
        <template v-else>
          <throttle-button v-if="executeButtons" :loading="loading" label="开启" @click="hangUp('0')" />
        </template>
      </template>
    </div>
    <div @click="openFlowChart" class="flow-chart-btn">
      <svg-icon iconClass="flow"></svg-icon>
      流程图
    </div>
    <flow-chart-drawer
      v-if="showDiagram"
      ref="flowChartDrawer"
      :parentId="parentId"
      :processInstanceId="formData.processInstanceId"
      :flowUser="formData.flowUser"
    />

    <!-- 站内审核、区域审核、站内验收、区域验收 -->
    <flow-review v-model="showAll" :visible="showAll" :type="examineType" @reviewFlow="reviewFlow" />

    <!-- 退回 弹窗 -->
    <backNullify
      v-model="backNullifyShow"
      :loading="loading"
      :visible="backNullifyShow"
      :type="backNullifyType"
      @backNullify="backConfirm"
    />
  </div>
</template>

<script>
import BasicInfo from './BasicInfo';
import DeviceInfo from './DeviceInfo';
import ExecuteInfo from './ExecuteInfo';
// import RiskInfo from './RiskInfo';
import initDict from '@/mixins/initDict';
// import AnnexInfo from './AnnexInfo';
import WorkSteps from './WorkSteps';
import HangUpReason from './HangUpReason';
import orderHallMixin from './mixin';
import { gatOrderDetail, orderReceive, executeOrder, checkOrder } from '@/api/isolarErp/orderHall';
import { delayOrTerminateQuery, hangUp, getWorkDetail } from '@/api/operations/digitalOrder';
import { getSpaceByPsId } from '@/api/common_gy/plan.js';
import flowReview from '@/components/erp/activiti/review';
import { distributeTangoTask } from '@/api/operations/unPlanedWork';
import { isEmpty, isNumber } from 'xe-utils';
import moment from 'moment';
import TicketInfo from './TicketInfo';

export default {
  name: 'OrderHallForm',
  mixins: [initDict, orderHallMixin],
  components: {
    BasicInfo,
    ExecuteInfo,
    DeviceInfo,
    // RiskInfo,
    // AnnexInfo,
    WorkSteps,
    HangUpReason,
    flowReview,
    TicketInfo
  },
  props: {
    parentId: {
      type: String,
      required: true,
      default: ''
    }
  },
  data () {
    return {
      type: '', // 类型
      rowInfo: {}, // 当前行相关信息
      formData: {
        deviceId: undefined, // 设备名称
        deviceNo: undefined, // 设备编号
        defectName: undefined, // 缺陷（故障）名称
        taskType: undefined, // 任务名称
        powerCutRange: undefined,
        defectHandleCondition: undefined
      },
      loading: false,
      showDiagram: false,
      step: 0,
      showExecuteOrSteps: true,
      backNullifyShow: false,
      backNullifyType: null,
      // 审批、验收
      showAll: false,
      examineType: null
    };
  },
  mounted () {
    this.getDictMap(
      `ops_task_type,ops_task_sub_type,pdca_grass_type,pdca_clean_type,psa_model,two_order_source,find_place,two_fault_classify,two_device_run_sts,pdca_defect_name,farm_scene_condition,risk_level,job_category,care_scene_condition,power_cut_centre,power_cut_dis,defect_handle_condit,ops_scene_condition`
    );
  },
  methods: {
    reset () {},
    async init (type, row) {
      this.type = type;
      this.rowInfo = row;
      this.loading = true;

      const data = await this.getDetail(type, row);
      return `工单${this.enumTypes[type]}-${data.workCode}【${data.flowStsName}】`;
    },
    async getDetail (type, row) {
      let res;
      try {
        res = await gatOrderDetail({ businessesId: row.id });
        this.formData = res.result_data;
        const deviceTypeId = res.result_data.deviceTypeId;
        const newDeviceTypeId = deviceTypeId && deviceTypeId.split(',');
        const stationEnvironment = res.result_data.stationEnvironment || '';
        const riskJobDate = res.result_data.riskJobDate || null;
        const cleanCap = res.result_data.cleanCap || null;
        Object.assign(this.formData, {
          deviceTypeId: newDeviceTypeId,
          stationEnvironment: stationEnvironment.split(','),
          planTime: [
            moment(res.result_data.planStartTime).format('YYYY-MM'),
            moment(res.result_data.planEndTime).format('YYYY-MM')
          ],
          riskJobDate: type == '3' ? riskJobDate : (riskJobDate || moment().format('YYYY-MM-DD')),
          ticketDtoList: res.result_data.ticketVoList || []
        });

        if (!this.getStatus() && isEmpty(res.result_data.stepVoList) && type == 7) {
          this.getStepList();
        }
        // 领取（指派）时，除草、清洗获取清洗（除草）容量
        if (!cleanCap && ['5', '6'].includes(type) && ['1', '2'].includes(res.result_data.taskType)) {
          getSpaceByPsId({ psaId: res.result_data.psaId }).then((res) => {
            let result = res.result ? res.result : {};
            const cleanCap = result.gridConnectedScale;
            Object.assign(this.formData, { cleanCap });
          });
        }
        // 缺陷/故障工程遗留默认为否，防止后端未返回的兜底策略
        if (['7', '8'].includes(this.formData.taskType)) {
          const { isEngLegacy } = this.formData;
          Object.assign(this.formData, {
            isEngLegacy: isNumber(isEngLegacy) ? isEngLegacy : 0
          });
        }
        this.loading = false;
      } catch (err) {
        this.loading = false;
      }
      return new Promise((resolve) => {
        resolve(res.result_data);
      });
    },
    // 无人机巡检任务-获取上传巡检图上传文件状态；表单页面关闭时需要判断是否有文件正在上传，并清除未保存的文件
    getUploadStatus () {
      this.$emit('doClose');
    },
    reviceAndAssignSubmit () {
      this.$refs.orderHallForm.validate(async (valid) => {
        if (valid) {
          this.loading = true;
          const formData = this.formData;

          //  activiti3373Workflow无人机 领取
          if (formData.flowDefKey === 'activiti3373Workflow' && this.type === '5') {
            const map = this.formatTangoTaskParams();
            distributeTangoTask(map)
              .then((res) => {
                this.$message.success(res.result_msg || '操作成功');
                this.$emit('cancel');
              })
              .finally(() => {
                this.loading = false;
              });
          } else if (formData.flowDefKey === 'activiti3373Workflow' && this.type === '6') {
            const result = await getWorkDetail({ id: formData.id });
            let map = {
              auditStatus: 1,
              processInstanceId: formData.processInstanceId,
              processDefKey: formData.processDefKey,
              taskDefKey: formData.taskDefKey,
              workFlowId: formData.taskId,
              id: formData.id,
              taskSource: result.result_data.taskSource,
              psaId: formData.psaId,
              assignor: formData.liablePerson
            };
            distributeTangoTask(map)
              .then((res) => {
                this.$message.success(res.result_msg || '操作成功');
                this.$emit('cancel');
              })
              .finally(() => {
                this.loading = false;
              });
          } else {
            const commonParams = this.getCommonParams();
            const savedParams = this.receiveCommonKeys.reduce((acc, cur) => {
              acc[cur] = this.formData[cur];
              return acc;
            }, {});
            const deviceTypeId = formData.deviceTypeId && formData.deviceTypeId.join(',');
            const params = {
              ...savedParams,
              ...commonParams,
              deviceTypeId,
              auditStatusType: this.type === '6' ? 4 : 3
            };
            orderReceive(params)
              .then((res) => {
                if (res.result_code === '1') {
                  this.loading = false;
                  this.$emit('cancel');
                }
              })
              .catch(() => {
                this.loading = false;
              });
          }
        }
      });
    },
    hangupSubmit () {
      this.$refs.orderHallForm.validate((valid) => {
        if (valid) {
          this.hangUp('1');
        }
      });
    },
    // 挂起前查询
    resolveQuery () {
      this.loading = true;
      let map = {
        businessesId: this.formData.id,
        type: '1'
      };
      delayOrTerminateQuery(map)
        .then((res) => {
          this.loading = false;
          if (res.result_data.isOperation) {
            this.step += 1;
            this.showExecuteOrSteps = false;
          } else {
            this.$message.warning(res.result_data.errorMessage);
          }
        })
        .catch(() => {
          this.loading = false;
        });
    },
    // 挂起/开启
    hangUp (type) {
      this.loading = true;
      let map = {
        erpPdcaWorkOrderId: this.formData.id,
        workOrderProcInstId: this.formData.processInstanceId,
        hangUpType: type,
        reason: type === '1' ? this.formData.reason : null
      };
      hangUp(map)
        .then((res) => {
          this.loading = false;
          this.$message.success(res.message ? res.message : '操作成功');
          if (type == '1') {
            this.step += 1;
            this.showExecuteOrSteps = true;
          } else {
            this.step = 0;
          }
          Object.assign(this.formData, { isHangUp: type });
        })
        .catch(() => {
          this.loading = false;
        });
    },
    openFlowChart () {
      this.showDiagram = true;
      const _self = this;
      this.$nextTick(() => {
        _self.$refs.flowChartDrawer.openView();
      });
    },
    hangupBack () {
      Object.assign(this.formData, { reason: undefined });
      this.step -= 1;
      this.showExecuteOrSteps = true;
    },
    // 显示退回弹窗
    backNullifyEven (type) {
      this.backNullifyType = type;
      this.backNullifyShow = true;
    },
    // 退回回调
    backConfirm (message) {
      this.loading = true;
      let map = {
        ...this.getCommonParams(),
        auditOpinion: message,
        auditStatus: '0',
        auditStatusType: 0
      };
      executeOrder(map)
        .then((res) => {
          this.backNullifyType = null;
          this.backNullifyShow = false;
          this.$nextTick(() => {
            this.$message.success(res.message ? res.message : '操作成功');
            this.$emit('cancel');
          });
        })
        .catch(() => {
          this.loading = false;
        });
    },
    confirmOrderResolve (type) {
      const savedParams = this.executeCommonKeys.reduce((acc, cur) => {
        acc[cur] = this.formData[cur];
        return acc;
      }, {});
      let map = {
        auditStatus: type,
        auditStatusType: 2,
        ...savedParams,
        ...this.getCommonParams()
      };
      // 提交时，添加累计修复时间并且为缺陷和故障
      if (type === '1' && ['7', '8'].includes(this.formData.taskType)) {
        let time = new Date().getTime() - new Date(this.formData.findTime).getTime();
        map.totalRepairTime = (time / 1000 / 60 / 60).toFixed(2);
      }
      // 1提交 2保存
      if (type === '1') {
        this.$refs.orderHallForm.validate(async (valid) => {
          if (valid) {
            this.loading = true;
            this.excuteFn(map);
          }
        });
      } else if (type === '2') {
        this.loading = true;
        this.excuteFn(map);
      }
    },
    // 执行方法
    excuteFn (map) {
      executeOrder(map)
        .then((res) => {
          if (res.result_code === '1') {
            this.loading = false;
            this.$message.success(res.message ? res.message : '操作成功');
            this.$emit('cancel');
          }
        })
        .catch(() => {
          this.loading = false;
        });
    },
    // 显示审批弹窗
    checkOrder (type) {
      this.examineType = type; // 1、通过 0、退回
      this.showAll = true;
    },
    async reviewFlow (message, type) {
      let map = {
        auditStatus: type,
        auditOpinion: message,
        auditStatusType: type,
        ...this.getCommonParams()
      };
      checkOrder(map)
        .then((res) => {
          if (res.result_code === '1') {
            this.examineType = null;
            this.showAll = false;
            this.$message.success(res.message ? res.message : '操作成功');
            this.$emit('cancel');
          }
        })
        .catch(() => {
          this.loading = false;
        });
    },
    // 手动校验上传附件
    validateField () {
      this.$refs.orderHallForm.validateField('sceneFileList');
    }
  },
  computed: {
    executeButtons () {
      return ['7'].includes(this.type);
    },
    // 是否无人机派单
    isDroneOrder () {
      const { opsSourceCategory } = this.formData;
      return opsSourceCategory === '3';
    }
  },
  watch: {}
};
</script>
<style lang="less" scoped>
#orderHallBox {
  :deep(.ant-form-item) {
    // width: 100%;
    display: flex;
  }
}
</style>

<style lang="less" scoped>
.view_detail_box {
  padding: 8px 0 0;

  .title_box_top {
    font-size: 18px;
    font-weight: 550;
    line-height: 25px;
    padding-bottom: 20px;
    display: flex;
    flex-flow: row nowrap;
    justify-content: space-between;
  }
}

:deep(.ant-form-item) {
  width: 100%;
  display: inline-flex;
}

:deep(.ant-steps-icon) {
  display: none;
}

:deep(.ant-steps-item-icon) {
  border: 4px solid #fb8e46 !important;
  height: 18px;
  width: 18px;
  position: relative;
  top: 18px;
  left: 18px;
}

:deep(.ant-steps-item-title) {
  position: relative;
  top: 10px;
  left: 10px;
  color: #5b5b5b !important;
  font-weight: 500;
}

:deep(.ant-steps-item) {
  // margin-top: -9px;
}

:deep(.ant-steps-item-process .ant-steps-item-icon) {
  background: none !important;
}

:deep(.ant-steps-item-wait > .ant-steps-item-container > .ant-steps-item-tail::after) {
  background: none !important;
}

:deep(.ant-steps-item-process > .ant-steps-item-container > .ant-steps-item-tail::after) {
  background: none !important;
}

:deep(.ant-steps-item-tail::after) {
  border-left: 1px dashed #d6d6d6;
  position: relative;
  left: 10px;
}

:deep(.ant-steps-item-finish > .ant-steps-item-container > .ant-steps-item-tail::after) {
  background: none !important;
  border-left-color: #ff8f33;
}

:deep(.ant-steps-item-finish .ant-steps-item-icon),
:deep(.ant-steps-item-wait .ant-steps-item-icon) {
  background-color: transparent;
}

.title-box {
  padding-bottom: 8px;
}

:root[data-theme='dark'] {
  .line_height .left {
    color: #fff !important;
  }
}
</style>
