/**
 * author zu<PERSON><PERSON>
 * 20230601 add
 * 省市县
 */
import { selectAreaLazily, getOwner, getProjectByOwner } from '@/api/isolarErp/config/archives';
export default {
  data () {
    return {
      owners: [],
      ownerProjects: [],
      options: [],
      params: {
        ownerId: undefined, // 所属业主
        ownerProjectId: undefined, // 项目公司
        placeCountry: '',
        psaName: '',
        placeProvince: '',
        placeCity: '',
        placeCounties: ''

      },
      place: []
    };
  },
  computed: {

  },
  methods: {
  // 国、省、市、区级联change事件
    onChange (value) {
      let form = { placeCountry: '', placeProvince: '', placeCity: '', placeCounties: '' };
      if (value && value.length > 3) {
        form.placeCounties = value[3];
      }
      if (value && value.length > 2) {
        form.placeCity = value[2];
      }
      if (value && value.length > 1) {
        form.placeProvince = value[1];
      }
      if (value && value.length > 0) {
        form.placeCountry = value[0];
      }
      Object.assign(this.params, form);
    },
    // 初始化加载国家数据
    getAreaLazily () {
      selectAreaLazily({}).then(res => {
        let options = (res.result_code == '1' ? res.result_data.treeData : []);
        this.options = options;
      });
    },
    /* 获取业主事件 */
    getOwners () {
      getOwner({}).then(res => {
        this.owners = res.payload;
      }).catch(() => {
        this.owners = [];
      });
    },
    /* 业主change事件 */
    ownerChange (val, clear = true) {
      if (clear) {
        this.params.ownerProjectId = undefined;
      }
      if (!val) {
        this.ownerProjects = [];
        return;
      }
      getProjectByOwner({ 'ownerId': val }).then(res => {
        this.ownerProjects = res.payload;
      }).catch(() => {
        this.ownerProjects = [];
      });
    },
    /* 业主 - 项目 下拉筛选 */
    filterOption (input, option) {
      return (
        option.componentOptions.children[0].text.indexOf(input) >= 0
      );
    },
    // 加载地区数据
    loadOptions (selectedOptions) {
      const targetOption = selectedOptions[selectedOptions.length - 1];
      targetOption.loading = true;
      let map = {
        expandAreaId: targetOption.value
      };
      selectAreaLazily(map).then(res => {
        targetOption.loading = false;
        let options = (res.result_code == '1' ? res.result_data.treeData : []);
        targetOption.children = options;
        this.options = [...this.options];
      });
    }

  }
};
