<!--全景图-->
<template>
<svg width="1872px" height="932px" viewBox="0 0 1872 932"  class="bg full-view">
  <image xlink:href="@/assets/images/monitor/device/UAV.png" x="20%" y="18.5%" height="65px" width="87px"/>

    <svg width="125px" height="70px" viewBox="0 0 125 70" x="20%" y="54.9%" version="1.1" class="start-line">
      <g stroke="none" stroke-width="1" fill="none" fill-rule="evenodd" fill-opacity="0.2" >
        <g transform="translate(-403.000000, -631.000000)" fill="#FFFFFF" fill-rule="nonzero">
          <g transform="translate(405.048116, 632.718319)">
            <g>
              <path
                d="M100.956244,-1.3059757 L102.431951,1.3059757 L4.86298219,56.4278883 C2.82764026,57.428396 1.36167566,57.7608838 0.20347732,57.1700155 C-1.00057527,56.5557541 -1.45280418,55.2209771 -1.49653629,53.2159757 L-1.5,52.9384169 L-1.5,48.0906583 L1.5,48.0906583 L1.5,52.933106 C1.50210173,53.5293607 1.54770026,53.9953142 1.62503526,54.3182796 L1.658,54.44 L1.82062179,54.4065451 C2.19052895,54.3226484 2.68039127,54.1416161 3.22623245,53.8885539 L3.46340513,53.7757619 L100.956244,-1.3059757 Z"
              ></path>
            </g>
            <g transform="translate(9.000000, 5.000000)">
              <path
                d="M100.956244,-1.3059757 L102.431951,1.3059757 L4.86298219,56.4278883 C2.82764026,57.428396 1.36167566,57.7608838 0.20347732,57.1700155 C-1.00057527,56.5557541 -1.45280418,55.2209771 -1.49653629,53.2159757 L-1.5,52.9384169 L-1.5,48.0906583 L1.5,48.0906583 L1.5,52.933106 C1.50210173,53.5293607 1.54770026,53.9953142 1.62503526,54.3182796 L1.658,54.44 L1.82062179,54.4065451 C2.19052895,54.3226484 2.68039127,54.1416161 3.22623245,53.8885539 L3.46340513,53.7757619 L100.956244,-1.3059757 Z"
              ></path>
            </g>
            <g transform="translate(20.000000, 10.000000)">
              <path
                d="M100.956244,-1.3059757 L102.431951,1.3059757 L4.86298219,56.4278883 C2.82764026,57.428396 1.36167566,57.7608838 0.20347732,57.1700155 C-1.00057527,56.5557541 -1.45280418,55.2209771 -1.49653629,53.2159757 L-1.5,52.9384169 L-1.5,48.0906583 L1.5,48.0906583 L1.5,52.933106 C1.50210173,53.5293607 1.54770026,53.9953142 1.62503526,54.3182796 L1.658,54.44 L1.82062179,54.4065451 C2.19052895,54.3226484 2.68039127,54.1416161 3.22623245,53.8885539 L3.46340513,53.7757619 L100.956244,-1.3059757 Z"
              ></path>
            </g>
          </g>
        </g>
      </g>
      <line class="bg-dyLine-2" x1="24.5" y1="68.6" x2="123.7" y2="12.6" :class="getBgClass('4', 'bg-dyLine')" />
      <line class="bg-dyLine-2" x1="13.4" y1="63.1" x2="112.7" y2="7.6" :class="getBgClass('4', 'bg-dyLine')" />
      <line class="bg-dyLine-2" x1="5.2" y1="58.2" x2="103.7" y2="2.5" :class="getBgClass('4', 'bg-dyLine')" />
    </g>

    </svg>
     <svg width="316px" height="205px" viewBox="0 0 316 205" version="1.1" class="pipeline" x="21%" y="37%">
      <defs>
        <linearGradient x1="49.1327513%" y1="46.8926059%" x2="53.4742526%" y2="51.7729529%" id="linearGradient-1">
          <stop stop-color="#FFFFFF" stop-opacity="0.1" offset="0%"></stop>
          <stop stop-color="#F1F1F1" stop-opacity="0.3" offset="30.5139977%"></stop>
          <stop stop-color="#D9D9D9" stop-opacity="0.1" offset="71.143781%"></stop>
          <stop stop-color="#979797" stop-opacity="0.1" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="0%" y1="0" x2="100%" y2="0" id="green">
          <stop stop-color="#68FE86" stop-opacity="0" offset="0%"></stop>
          <stop stop-color="#7CFFB9" stop-opacity="0.5" offset="25%"></stop>
          <stop stop-color="#68FE9E" stop-opacity="0.4" offset="50%"></stop>
          <stop stop-color="#7CFFB9" stop-opacity="0.6" offset="70%"></stop>
          <stop stop-color="#7CFFB9" stop-opacity="1" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="0%" y1="0" x2="100%" y2="0" id="red">
          <stop stop-color="#F0313C" stop-opacity="0" offset="0%"></stop>
          <stop stop-color="#FA5861" stop-opacity="0.5" offset="25%"></stop>
          <stop stop-color="#F0313C" stop-opacity="0.4" offset="50%"></stop>
          <stop stop-color="#FA5861" stop-opacity="0.6" offset="70%"></stop>
          <stop stop-color="#FA5861" stop-opacity="1" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="0%" y1="0" x2="100%" y2="0" id="red1">
          <stop stop-color="#FA5861" stop-opacity="0" offset="0%"></stop>
          <stop stop-color="#F0313C" stop-opacity="0.4" offset="50%"></stop>
          <stop stop-color="#F0313C" stop-opacity="1" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="0%" y1="0" x2="100%" y2="0" id="warning">
          <stop stop-color="#FEF268" stop-opacity="0" offset="0%"></stop>
          <stop stop-color="#FEFE68" stop-opacity="0.4" offset="50%"></stop>
          <stop stop-color="#FFF277" stop-opacity="1" offset="100%"></stop>
        </linearGradient>
      </defs>
      <g stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g transform="translate(-406.000000, -473.000000)" fill="url(#linearGradient-1)" fill-rule="nonzero">
          <g transform="translate(406.000000, 351.000000)">
            <path
              d="M296.495747,122.882639 C302.764508,121.830607 307.951489,124.887077 312.05669,132.05205 C316.161892,139.217024 316.161892,145.500024 312.05669,150.90105 C120.645565,265.580932 22.1420047,323.776393 16.5460106,325.487433 C6.9581407,328.419034 1.76193864,319.557197 0.957404463,298.90192 L234.721758,158.352029 L248.165706,150.632783 L296.495747,122.882639 Z"
              id="路径"
            ></path>
          </g>
        </g>
      </g>
      <line x1="3.6" y1="195.9" x2="300" y2="8.9" class="bg-line" :class="getBgClass('1')" filter="url(#filter-1)" />
      <line x1="3.6" y1="195.9" x2="300" y2="8.9" class="bg-dyLine" :class="getBgClass('1', 'bg-dyLine')" />
      <image xlink:href="@/assets/images/monitor/device/connector.png" x="285" y="0" height="35px" width="32px" />
    </svg>
    <svg  width="380px" height="460px" viewBox="0 0 380 460" x="13%" y="35.8%" version="1.1">
    <foreignObject x='0' y="0" width="100%" height="100%">
      <div class="panel">
      <a-popover overlayClassName="device" placement="left">
      <template slot="content">
        <div class="popover-bg">
          <section>
            <div class="params">
              <span class="left"> {{ getDeviceInfo('10').deviceTypeName?getDeviceInfo('10').deviceTypeName:'组串' }}:</span>
              <span class="right font-16"><span class="color_r"> {{ getDeviceInfo('10').deviceCount }}</span>&nbsp;&nbsp;台</span>
            </div>
            <template v-for="item in getDeviceInfo('10').alarm">
              <div class="params" :key="item.alarmRemark">
                <span class="left"> {{ item.remarkName }}: </span>
                <span class="right font-16"><span class="color_r"> {{ item.alarmCount }}</span>&nbsp;&nbsp;台</span>
              </div>
            </template>
          </section>
        </div>
      </template>
      <img src="@/assets/images/monitor/device/panel2.png" alt="" class="group_string" />
     </a-popover>
      <div class="group_box_alarm">
      <div class="circle-wrapper" :class="getCircleClass(10, 'wrapper')">
        <div class="circle" :class="getCircleClass(10)"></div>
      </div>
    </div>
      <img src="@/assets/images/monitor/device/surface_pile.png" class="group_shelf"  />
      <img src="@/assets/images/monitor/device/projection.png" class="box-shadow" />

    <a-popover overlayClassName="device" placement="left">
      <template slot="content">
        <div class="popover-bg">
          <section>
            <div class="params">
              <span class="left"> {{ getDeviceInfo('4').deviceTypeName }}：</span>
              <span class="right font-16"
                ><span class="color_r"> {{ getDeviceInfo('4').deviceCount }}</span
                >&nbsp;&nbsp;台</span
              >
            </div>
            <template v-for="item in getDeviceInfo('4').alarm">
              <div class="params" :key="item.alarmRemark">
                <span class="left"> {{ item.remarkName ? item.remarkName : '--' }}：</span>
                <span class="right font-16">
                  <span class="color_r">{{ item.alarmCount }}</span
                  >&nbsp;&nbsp;台</span
                >
              </div>
            </template>
          </section>
        </div>
      </template>
      <img
        src="@/assets/images/monitor/device/combinerBox.png"
        class="combiner_box"
        @click="changeDevice({ value: 'CombinerBox', name: '汇流箱', type: 4 }, '4')"
      />
    </a-popover>
      <div class="combiner_box_alarm">
      <div class="circle-wrapper" :class="getCircleClass(4, 'wrapper')">
        <div class="circle" :class="getCircleClass(4)"></div>
      </div>
    </div>
    </div>
    </foreignObject>
    </svg>
     <image xlink:href="@/assets/images/monitor/device/panel.png" x="9.5%" y="59.2%" height="419px" width="346px"  />
    <image xlink:href="@/assets/images/monitor/device/robot.png" x="12%" y="63.4%" class="robot_img"  height="124px" width="140px"/>

    <svg width="184px" height="79px" viewBox="0 0 184 79" version="1.1" class="pipeline1" x="44%" y="26%">
      <defs>
        <filter x="-10.5%" y="-39.5%" width="121.0%" height="179.0%" filterUnits="objectBoundingBox" id="filter-2">
          <feGaussianBlur stdDeviation="4" in="SourceGraphic"></feGaussianBlur>
        </filter>
      </defs>
      <g id="页面-1" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="正常能量流备份-2" transform="translate(-854.000000, -351.000000)">
          <g id="编组-12" transform="translate(406.000000, 351.000000)">
            <g id="编组-5" transform="translate(448.730707, 4.045688)">
              <path
                d="M87.7598041,0 L88.2386918,0.0072741812 C95.7918791,0.184869624 104.267995,3.41364204 114.488802,9.45656246 L115.723726,10.1952057 C116.138194,10.4459126 116.555506,10.701107 116.975715,10.9607736 L118.245084,11.7531754 L118.683293,12.0303122 L182.98624,50.1969261 L168.69501,74.2751474 L104.03628,35.8980275 L103.852359,35.7809704 C102.870758,35.1562268 101.92018,34.5679374 101.001388,34.0161233 L99.9141429,33.3714557 C97.0557689,31.6990376 94.5260001,30.4002116 92.3498078,29.4756653 L92.0392564,29.3452548 L91.4440636,29.1041994 L90.8841619,28.8889308 L90.3604444,28.6987678 L89.8738044,28.5330294 C89.7958371,28.5074038 89.719452,28.4827676 89.6446675,28.4591065 L89.2153183,28.328728 C89.078704,28.2890932 88.9486415,28.2532455 88.8252795,28.2210712 L88.4754443,28.1354553 L88.1667058,28.0711991 C87.9267955,28.0263629 87.7306248,28.0030669 87.5805195,27.9995376 C87.4966399,27.9975653 87.3955195,28.0025861 87.2775644,28.0152695 L87.0193286,28.0510514 C86.9725884,28.0587839 86.9240078,28.0674175 86.8736018,28.0769769 L86.5493816,28.145641 C86.3764396,28.1857263 86.18734,28.2345901 85.9824891,28.292902 L85.5519058,28.4223217 C85.3262336,28.4935288 85.0850809,28.5746303 84.8288538,28.6662959 L84.2964208,28.8639109 L83.7245118,29.0908873 C81.162932,30.1393033 78.0436252,31.8450511 74.4345675,34.2161985 L73.6032577,34.7675186 L26.6747203,62.9434681 L0,45.8870115 L58.7202933,11.0383122 L58.9979256,10.8555958 C69.6207635,3.86491079 78.7822458,0.0820663173 87.2795297,0 L87.7598041,0 Z"
                id="路径"
                fill-opacity="0.2"
                fill="#rgba(18,255,133)"
              ></path>
              <path
                d="M16.1767756,48.9543122 L70.6663302,17.4370246 C76.2597349,13.0971926 82.0074934,10.9526531 87.9096057,11.0034059 C93.8117179,11.0541587 99.7016932,13.2987722 105.579531,17.7372464 L159.269293,48.9543122"
                id="路径-41"
                class="bg-line"
                :class="getBgClass('6-4')"
                filter="url(#filter-2)"
              ></path>
              <path
                d="M16.1767756,48.9543122 L70.6663302,17.4370246 C76.2597349,13.0971926 82.0074934,10.9526531 87.9096057,11.0034059 C93.8117179,11.0541587 99.7016932,13.2987722 105.579531,17.7372464 L159.269293,48.9543122"
                class="bg-dyLine"
                :class="getBgClass('6-4', 'bg-dyLine')"
              ></path>
            </g>
          </g>
        </g>
      </g>
    </svg>
    <svg width="322px" height="227px" viewBox="0 0 322 227" version="1.1" class="pipeline2" x="45%" y="37.5%">
      <defs>
        <filter x="-5.2%" y="-7.6%" width="110.4%" height="115.3%" filterUnits="objectBoundingBox" id="filter-3">
          <feGaussianBlur stdDeviation="4" in="SourceGraphic"></feGaussianBlur>
        </filter>
      </defs>
      <image xlink:href="@/assets/images/monitor/device/connector_1.png" x="235" y="0" height="35px" width="32px" />
      <g id="页面-1" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="正常能量流备份-2" transform="translate(-883.000000, -463.000000)">
          <g id="编组-12" transform="translate(406.000000, 351.000000)">
            <g id="编组-13" transform="translate(477.893116, 115.708257)">
              <path
                d="M246.271078,23.8049627 C244.586493,18.2787933 245.022623,12.834306 247.57947,7.47150097 C250.136316,2.10869591 254.441519,-0.367171349 260.495078,0.0438991943 L306.952337,28.3643827 C316.032689,34.1334574 320.572866,40.3720859 320.572866,47.0802684 C320.572866,53.7884509 316.032689,59.8809734 306.952337,65.3578357 L24.311884,221.244498 L0,207.720408 L277.05248,49.4722963 C278.571486,48.514931 279.348853,47.5130799 279.384579,46.4667429 C279.420305,45.4204059 278.714144,44.336809 277.266097,43.2159521 L246.271078,23.8049627 Z"
                id="路径"
                fill-opacity="0.1"
                fill="#FFFFFF"
                fill-rule="nonzero"
              ></path>
              <path
                d="M253.611651,11.4507234 L295.892575,38.5970919 C300.907845,41.5903404 303.243938,44.6072764 302.900853,47.6479 C302.557768,50.6885236 299.554727,53.5824883 293.891728,56.329794 L14.471792,207.720408"
                id="路径-41"
                class="bg-line"
                :class="getBgClass(3)"
                filter="url(#filter-3)"
              ></path>
              <path
                d="M253.611651,11.4507234 L295.892575,38.5970919 C300.907845,41.5903404 303.243938,44.6072764 302.900853,47.6479 C302.557768,50.6885236 299.554727,53.5824883 293.891728,56.329794 L14.471792,207.720408"
                class="bg-dyLine"
                :class="getBgClass(3, 'bg-dyLine')"
              ></path>
            </g>
          </g>
        </g>
      </g>
    </svg>
    <svg width="505px" height="261px" viewBox="0 0 505 261" version="1.1" class="pipeline3" x="56%" y="58%">
      <defs>
        <filter x="-3.2%" y="-6.6%" width="106.3%" height="113.3%" filterUnits="objectBoundingBox" id="filter-4">
          <feGaussianBlur stdDeviation="4" in="SourceGraphic"></feGaussianBlur>
        </filter>
      </defs>
      <image xlink:href="@/assets/images/monitor/device/connector_1.png" x="0" y="185" height="35px" width="32px" />
      <image xlink:href="@/assets/images/monitor/device/connector.png" x="470" y="5" height="35px" width="32px" />
      <g id="页面-1" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="正常能量流备份-2" transform="translate(-1068.000000, -658.000000)">
          <g id="编组-12" transform="translate(406.000000, 351.000000)">
            <g id="编组-6" transform="translate(669.308858, 320.054047)">
              <g id="编组-14" transform="translate(-0.000000, 0.000000)">
                <path
                  d="M459.229663,0.244913433 C465.380628,-0.875486347 470.358067,1.84821396 474.161981,8.41601435 C477.965895,14.9838147 478.754415,21.347644 476.52754,27.5075022 L94.0792157,242.119452 C87.1730597,245.788524 80.3298162,247.582587 73.549485,247.501641 C66.7691539,247.420694 60.1177497,245.465526 53.5952725,241.636136 L1.18101561,206.945953 C-0.996294849,198.183855 -0.16305185,190.973856 3.6807446,185.315959 C7.52454106,179.658061 12.422004,176.782118 18.3731335,176.688129 L57.9528313,201.084 C64.1719684,204.844801 69.4567384,206.798785 73.8071415,206.945953 C78.1575447,207.093121 83.4719246,205.139137 89.7502814,201.084 L459.229663,0.244913433 Z"
                  id="路径"
                  fill-opacity="0.1"
                  fill="#FFFFFF"
                  fill-rule="nonzero"
                ></path>
                <path
                  d="M7.87111842,191.373413 L59.6553079,222.129777 C65.7102969,225.938275 70.5926809,228.064187 74.3024598,228.507511 C78.0122388,228.950835 82.4110798,227.934045 87.4989829,225.45714 L482.040168,2.37461808"
                  id="路径-41"
                  class="bg-line"
                  :class="getBgClass('6-1')"
                  filter="url(#filter-4)"
                ></path>
                <path
                  d="M7.87111842,191.373413 L59.6553079,222.129777 C65.7102969,225.938275 70.5926809,228.064187 74.3024598,228.507511 C78.0122388,228.950835 82.4110798,227.934045 87.4989829,225.45714 L482.040168,2.37461808"
                  class="bg-dyLine bg-dyLine-3"
                  :class="getBgClass('6-1', 'bg-dyLine')"
                ></path>
              </g>
            </g>
          </g>
        </g>
      </g>
    </svg>
    <svg width="157px" height="96px" viewBox="0 0 157 96" class="pipeline4" x="71%" y="65%">
      <defs>
        <filter x="-8.3%" y="-14.4%" width="116.5%" height="128.7%" filterUnits="objectBoundingBox" id="filter-1">
          <feGaussianBlur stdDeviation="3" in="SourceGraphic"></feGaussianBlur>
        </filter>
      </defs>
      <g id="页面-1" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="正常能量流备份-2" transform="translate(-1421.000000, -759.000000)">
          <g id="编组-12" transform="translate(406.000000, 351.000000)">
            <g id="编组-9" transform="translate(1015.062472, 411.575540)">
              <g id="编组-15" transform="translate(-0.000000, 0.000000)">
                <polygon
                  fill-opacity="0.1"
                  fill="#FFFFFF"
                  fill-rule="nonzero"
                  points="-9.87564423e-13 10.5923745 18.7060635 0 156.937528 81.6744602 138.499041 91.9952676"
                ></polygon>
                <line
                  x1="133.937528"
                  y1="76.275355"
                  x2="12.8771271"
                  y2="6.65530758"
                  stroke-width="2"
                  class="bg-line"
                  :class="getBgClass('6-1')"
                  filter="url(#filter-1)"
                ></line>
                <line
                  x1="133.937528"
                  y1="76.275355"
                  x2="12.8771271"
                  y2="6.65530758"
                  stroke-width="2"
                  class="bg-dyLine bg-dyLine-3 bg-dyLine-f"
                  :class="getBgClass('6-1', 'bg-dyLine', false)"
                ></line>
              </g>
            </g>
          </g>
        </g>
      </g>
    </svg>
    <svg width="72px" height="49px" viewBox="0 0 72 49" version="1.1" class="pipeline5" x="70%" y="63.4%">
      <defs>
        <filter x="-19.9%" y="-35.2%" width="139.9%" height="170.4%" filterUnits="objectBoundingBox" id="filter-1">
          <feGaussianBlur stdDeviation="3" in="SourceGraphic"></feGaussianBlur>
        </filter>
      </defs>
      <image xlink:href="@/assets/images/monitor/device/connector_1.png" x="0" y="0" height="35px" width="32px" />
      <g id="页面-1" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="正常能量流备份-2" transform="translate(-1342.000000, -710.000000)">
          <g id="编组-12" transform="translate(406.000000, 351.000000)">
            <g id="编组-17" transform="translate(940.560664, 364.069567)">
              <path
                d="M1.19407993,15.1100913 C-0.56416498,12.6318063 -0.380689898,9.35575259 1.74450518,5.2819302 C3.86970025,1.20810781 6.61428842,-0.509185586 9.97826968,0.130050004 L60.9954764,30.2103894 L43.5373113,39.4878823 L1.19407993,15.1100913 Z"
                id="路径"
                fill-opacity="0.1"
                fill="#FFFFFF"
                fill-rule="nonzero"
              ></path>
              <line
                x1="56.5228183"
                y1="33.5826072"
                x2="6.37893534"
                y2="5.16128067"
                class="bg-line"
                filter="url(#filter-1)"
                :class="getBgClass('6-1')"
              ></line>
              <line
                x1="56.5228183"
                y1="33.5826072"
                x2="6.37893534"
                y2="5.16128067"
                class="bg-dyLine bg-dyLine-3"
                :class="getBgClass('6-1', 'bg-dyLine', false)"
              ></line>
            </g>
          </g>
        </g>
      </g>
    </svg>
    <svg width="463px" height="263px" viewBox="0 0 463 263" class="end-line1" x='63%' y="0%">
      <title>编组 20</title>
      <g id="页面-1" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="正常能量流备份-2" transform="translate(-1201.000000, -123.000000)" stroke-width="2">
          <g id="编组-20" transform="translate(1201.706078, 124.000000)">
            <path
              d="M88.3247215,69.0306308 C148.332786,133.819359 203.308531,179.978855 253.251957,207.509118 C303.195383,235.039381 342.228424,252.879886 370.351079,261.030631"
              class="bg-dyLine-4"
              :class="getBgClass('6-1', 'bg-dyLine')"
            ></path>
            <path
              d="M82.2939215,63.1196475 C60.3497805,49.986246 37.8722285,33.5556638 14.8612657,13.8279007 C9.52598848,9.25385981 4.57223323,4.88442555 0,0.71959794"
              class="bg-dyLine-4"
              :class="getBgClass('6-1', 'bg-dyLine')"
            ></path>
            <path
              d="M241.098109,2.05843439 C262.787562,69.0714373 287.519499,116.938813 315.293922,145.660562 C343.068344,174.382311 374.720077,201.565823 410.249122,227.211097"
              class="bg-dyLine-4"
              :class="getBgClass('6-1', 'bg-dyLine')"
            ></path>
            <path
              d="M315.293922,0 C327.960588,49.2793484 342.293922,87.4909369 358.293922,114.634766 C374.293922,141.778594 408.640588,170.697346 461.333922,201.391021"
              class="bg-dyLine-4"
              :class="getBgClass('6-1', 'bg-dyLine')"
            ></path>
          </g>
        </g>
      </g>
    </svg>
    <svg width="154px" height="125px" viewBox="0 0 154 125" version="1.1" class="end-line4" x='83%' y="22%">
      <title>编组 19</title>
      <g id="页面-1" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="正常能量流备份-2" transform="translate(-1687.000000, -362.000000)" stroke="#101010" stroke-width="2">
          <g id="编组-25" transform="translate(1201.706078, 124.000000)">
            <g id="编组-19" transform="translate(486.351079, 238.391021)">
              <path
                d="M0,60.6360675 C5.80445466,74.4857742 16.1711609,87.2132412 31.1001187,98.8184686 C46.0290765,110.423696 59.573145,118.390239 71.7323242,122.718098"
                class="bg-dyLine-4"
                :class="getBgClass('6-1', 'bg-dyLine')"
              ></path>
              <path
                d="M39.8980425,25.8200757 C47.4409905,42.2469543 58.0524177,55.6971294 71.7323242,66.1706012 C85.4122307,76.6440729 102.954735,84.0899891 124.359838,88.5083498"
                class="bg-dyLine-4"
                :class="getBgClass('6-1', 'bg-dyLine')"
              ></path>
              <path
                d="M92.7682285,0 C93.8333673,13.684011 99.2138635,27.211025 108.909717,40.581042 C118.605571,53.951059 133.097253,63.6769187 152.384764,69.7586211"
                class="bg-dyLine-4"
                :class="getBgClass('6-1', 'bg-dyLine')"
              ></path>
            </g>
          </g>
        </g>
      </g>
    </svg>
    <foreignObject x='30%' y="0" width="70%" height='100%'>

    <div class="inverter" @click="changeDevice({ value: 'Inverter', name: '逆变器', type: 1 }, '1')">
      <a-popover overlayClassName="device" placement="left">
        <template slot="content">
          <div class="popover-bg">
            <section>
              <div class="params">
                <span class="left"> {{ getDeviceInfo('1').deviceTypeName }}：</span>
                <span class="right font-16">
                  <span class="color_r">{{ getDeviceInfo('1').deviceCount }}</span
                  >&nbsp;&nbsp;台</span
                >
              </div>
              <template v-for="item in getDeviceInfo('1').alarm">
                <div class="params" :key="item.alarmRemark">
                  <span class="left"> {{ item.remarkName }}：</span>
                  <span class="right font-16"
                    ><span class="color_r"> {{ item.alarmCount }}</span
                    >&nbsp;&nbsp;台</span
                  >
                </div>
              </template>
            </section>
          </div>
        </template>
        <img src="@/assets/images/monitor/device/inverter.png"/>
      </a-popover>
    </div>

    <div class="inverter_alarm">
      <div class="circle-wrapper" :class="getCircleClass(1, 'wrapper')">
        <div class="circle" :class="getCircleClass(1)"></div>
      </div>
    </div>
    <div class="box_change" @click="changeDevice({ value: 'BoxChange', name: '箱变', type: 6, deviceSubType: 4 }, '6-4')">
      <a-popover overlayClassName="device" placement="left">
        <template slot="content">
          <div class="popover-bg">
            <section>
              <div class="params">
                <!-- <span class="left"> {{ getDeviceInfo('6', '4').deviceTypeName }}:</span> -->
                <span class="left"> 箱变：</span>
                <span class="right font-16">
                  <span class="color_r">{{ getDeviceInfo('6','4').deviceCount }}</span
                  >&nbsp;&nbsp;台</span
                >
              </div>
              <template v-for="item in getDeviceInfo('6','4').alarm">
                <div class="params" :key="item.alarmRemark">
                  <span class="left"> {{ item.remarkName }}：</span>
                  <span class="right font-16"
                    ><span class="color_r"> {{ item.alarmCount }}</span
                    >&nbsp;&nbsp;台</span
                  >
                </div>
              </template>
            </section>
          </div>
        </template>
        <img src="@/assets/images/monitor/device/box.png"/>
      </a-popover>
    </div>
    <div class="box_change_alarm">
      <div class="circle-wrapper" :class="getCircleClass('6-4', 'wrapper')">
        <div class="circle" :class="getCircleClass('6-4')"></div>
      </div>
    </div>
    <div class="collector_line"   @click="changeDevice({ value: 'BoostVoltage', name: '升压站', type: 22 }, '3')">
      <a-popover overlayClassName="device" placement="left">
        <template slot="content">
          <div class="popover-bg">
            <section>
              <div class="params">
                <!-- <span class="left"> {{ getDeviceInfo('3').deviceTypeName }}:</span> -->
                <span class="left"> 集电线：</span>
                <span class="right font-16"
                  ><span class="color_r"> {{ getDeviceInfo('3').deviceCount }}</span
                  >&nbsp;&nbsp;台</span
                >
              </div>
              <template v-for="item in getDeviceInfo('3').alarm">
                <div class="params" :key="item.alarmRemark">
                  <span class="left"> {{ item.remarkName }}：</span>
                  <span class="right font-16"
                    ><span class="color_r"> {{ item.alarmCount }}</span
                    >&nbsp;&nbsp;台</span
                  >
                </div>
              </template>
            </section>
          </div>
        </template>
        <img src="@/assets/images/monitor/device/circut.png" />
      </a-popover>
      <div class="collector_line_alarm">
        <div class="circle-wrapper" :class="getCircleClass(3, 'wrapper')">
          <div class="circle" :class="getCircleClass(3)"></div>
        </div>
      </div>
      <img
        src="@/assets/images/monitor/device/electricMeter.png"
        @click="changeDevice({ value: 'ElectricMeter', name: '电表', type: 7 }, '7')"
        class="electric_meter"
      />
      <div class="electric_meter_alarm">
        <div class="circle-wrapper" :class="getCircleClass(7, 'wrapper')">
          <div class="circle" :class="getCircleClass(7)"></div>
        </div>
      </div>
    </div>
    <div class="group_change" @click="changeDevice({ value: 'BoostVoltage', name: '升压站', type: 22 }, '12-10')">
      <img src="@/assets/images/monitor/device/groundChange.png"/>
    </div>
    <div class="group_change_alarm">
      <div class="circle-wrapper" :class="getCircleClass('12-10', 'wrapper')">
        <div class="circle" :class="getCircleClass('12-10')"></div>
      </div>
    </div>
    <div class="svg" @click="changeDevice({ value: 'BoostVoltage', name: '升压站', type: 22 }, '12-11')">
      <img src="@/assets/images/monitor/device/SVG.png"/>
    </div>
    <div class="svg_alarm">
      <div class="circle-wrapper" :class="getCircleClass('12-11', 'wrapper')">
        <div class="circle" :class="getCircleClass('12-11')"></div>
      </div>
    </div>
    <div class="boost_voltage" @click="changeDevice({ value: 'BoostVoltage', name: '升压站', type: 22 }, '6-1')">
      <img src="@/assets/images/monitor/device/boostVoltage.png"/>
    </div>
    <div class="boost_voltage_alarm">
      <div class="circle-wrapper" :class="getCircleClass('6-1', 'wrapper')">
        <div class="circle" :class="getCircleClass('6-1')"></div>
      </div>
    </div>
    <div class="env_monitor" @click="changeDevice({ value: 'EnviMonitor', name: '环境检测仪', type: 5 }, '5')">
      <img src="@/assets/images/monitor/device/enviMonitor.png"/>
    </div>
    <div class="env_monitor_alarm">
      <div class="circle-wrapper" :class="getCircleClass(5, 'wrapper')">
        <div class="circle" :class="getCircleClass(5)"></div>
      </div>
    </div>
</foreignObject>
</svg>
</template>
<script>
import { getFaultDeviceCount } from '@/api/monitor/device';
export default {
  props: {
    list: Array,
    default: () => {
      return [];
    }
  },
  inject: ['params', 'status'],
  data () {
    return {
      faultDeviceList: [],
      isAllStop: false
    };
  },
  created () {
    this.faultDeviceList = this.list;
  },
  watch: {
    list (value, old) {
      this.faultDeviceList = this.list;
      if (value) {
      }
    }
  },
  methods: {
    getBgClass (prevStatus, prefix = 'bg', isRed = true) {
      if (this.faultDeviceList.length == 0) {
        return;
      }
      let arr = String(prevStatus).indexOf('-') > -1 ? prevStatus.split('-') : [];
      let getInfo = arr.length > 0 ? this.getDeviceInfo(arr[0], arr[1]) : this.getDeviceInfo(prevStatus);
      // status 上游设备的状态
      let bgRed = `${prefix}-${isRed ? 'red' : 'fred'}`;
      let status = getInfo.faultStatus; // 0 正常，1 异常
      let psStatus = this.status() == 2;
      return {
        [bgRed]: psStatus,
        [`${prefix}-green`]: !psStatus && status == 0 && this.status() != 3,
        [`${prefix}-warn`]: (!psStatus && status == 1) || this.status() == 3
      };
    },
    getCircleClass (prevStatus, prefix = 'bg', isRed = true) {
      if (this.faultDeviceList.length == 0) {
        return;
      }
      let arr = String(prevStatus).indexOf('-') > -1 ? prevStatus.split('-') : [];
      let getInfo = arr.length > 0 ? this.getDeviceInfo(arr[0], arr[1]) : this.getDeviceInfo(prevStatus);
      // status 上游设备的状态
      let status = getInfo.faultStatus; // 0 正常，1 异常
      return {
        [`circle-${prefix}-green`]: status == 0 || !getInfo.alarm,
        [`circle-${prefix}-warn`]: status == 1 && getInfo.alarm && getInfo.alarm.length > 0
      };
    },
    removeClass (arr, className1, className2) {
      for (let index = 0; index < arr.length; index++) {
        const element = arr[index];
        element.classList.remove(className1);
        className2 && element.classList.remove(className2);
      }
    },
    addClass (arr, className) {
      let isNeedAni = this.status() == 6 || this.status() == 2;
      if (isNeedAni) return;
      for (let index = 0; index < arr.length; index++) {
        const element = arr[index];
        element.classList.add(className);
      }
    },
    async initData (data) {
      let res = data || (await this.getData());
      this.faultDeviceList = res.result_data;
    },
    getData () {
      return new Promise((resolve, reject) => {
        getFaultDeviceCount({ psId: this.psId })
          .then((res) => {
            resolve(res);
          })
          .catch((res) => {
            reject(res);
          });
      });
    },
    getDeviceInfo (deviceType, subDevice) {
      let deviceList = this.faultDeviceList.filter((item) => {
        return !subDevice
          ? deviceType == item.deviceType
          : deviceType == item.deviceType && item.deviceSubType == subDevice;
      });
      return deviceList.length > 0 ? deviceList[0] : [];
    },
    changeDevice (obj, deviceType) {
      let arr = String(deviceType).indexOf('-') > -1 ? deviceType.split('-') : [];
      let getInfo = arr.length > 0 ? this.getDeviceInfo(arr[0], arr[1]) : this.getDeviceInfo(deviceType);
      if (getInfo.deviceCount == 0) {
        return;
      }
      this.$emit('select', obj);
    }
  },
  mounted () {
    let bgLine = Array.from(document.getElementsByClassName('bg-dyLine'));
    let bgLine2 = document.getElementsByClassName('bg-dyLine-2');
    let bgLine4 = Array.from(document.getElementsByClassName('bg-dyLine-4'));
    let that = this;
    let i = 0;
    let timer = setInterval(() => {
      let isNeedAni = that.status() == 6 || that.status() == 2;
      i++;
      that.removeClass(bgLine4, 'ani2', 'ani3');
      that.removeClass(bgLine.slice(2, bgLine.length), 'ani');
      if (i == 1) {
        that.addClass(bgLine2, 'ani');
      } else if (i > 5) {
        if (i == 7) {
          i = 0;
          that.removeClass(bgLine4, 'ani2', 'ani3');
          that.addClass(bgLine4.slice(0, 4), 'ani2');
        }
        if (i == 6) {
          that.addClass(bgLine4.slice(4, bgLine4.length), 'ani3');
        }
      } else {
        for (let index = 0; index < bgLine.length; index++) {
          const element = bgLine[index];
          const element2 = bgLine2[index];
          element.classList.remove('ani');
          index <= 2 && element2.classList.remove('ani');
        }
        if (!isNeedAni) {
          bgLine[i - 2] && bgLine[i - 2].classList.add('ani');
        }

        if (i === 5) {
          that.addClass(bgLine.slice(4, bgLine.length), 'ani');
        }
      }
    }, 1000);
    this.$once('hook:beforeDestroy', () => {
      clearInterval(timer);
      timer = null;
    });
    this.$once('hook:deactivated', () => {
      clearInterval(timer);
      timer = null;
    });
  }
};
</script>
<style lang="less" scoped>
@import url('../../less/base.less');
@px: 0px;
.position(@top:120, @left: 180) {
  position: absolute;
  top: @top + @px;
  left: @left + @px;
}
.bg {
    background: url('../../../../assets/images/monitor/device/ps-bg.png');
    background-size: cover;
  }
.position-r(@top:120, @right: 180) {
  position: absolute;
  top: @top + @px;
  right: @right + @px;
}
.circle {
  width: 8px;
  height: 8px;
  box-shadow: 0px 0px 4px 2px rgba(36, 204, 255, 0.3);
  border-radius: 100%;
  .position(2px, 2px);
}
@keyframes circle {
  0% {
    transform: scale(1);
  }
  25% {
    transform: scale(1.05);
  }
  50% {
    transform: scale(1.15);
  }
  75% {
    transform: scale(1.05);
  }
}
.circle-wrapper {
  width: 16px;
  height: 16px;

  border: 2px solid transparent;
  border-radius: 100%;
  position: relative;
  animation: circle-wrapper 2s infinite 1s;
}
.circle-wrapper-warn,
.circle-wrapper-red {
  animation-name: circle-wrapper-red;
}
.circle-wrapper-green {
  animation-name: circle-wrapper-green;
}
@keyframes circle-wrapper-red {
  from {
    border-color: #ec4255;
    box-shadow: 0px 0px 4px 8px rgba(236, 66, 85, 0.4);
  }
  to {
    border-color: transparent;
    box-shadow: none;
  }
}
@keyframes circle-wrapper-green {
  from {
    border-color: #24ccff;
    box-shadow: 0px 0px 4px 8px rgba(36, 204, 255, 0.4);
  }
  to {
    border-color: transparent;
    box-shadow: none;
  }
}
.full-view {
  .start-line {
    .position(514, 382);
    z-index: 0;
  }
  .pipeline {
    position: relative;
    top: 295px;
    left: 130px;
  }
  .pipeline1 {
    position: relative;
    top: 50px;
    left: 235px;
  }
  .pipeline2 {
    position: relative;
    top: 320px;
    left: 70px;
  }
  .pipeline3 {
    position: relative;
    top: 540px;
    left: -60px;
  }
  .pipeline4 {
    .position-r(610, 374);
    z-index: 0;
  }
  .pipeline5 {
    .position-r(585, 495);
  }
  .bg-line {
    stroke-width: 6px;
    stroke-linecap: round;
    stroke-linejoin: round;
  }
  .bg-white {
    stroke: white;
    opacity: 0.32976859;
  }
  .circle-bg-green{
    background: #24ccff;
  }
  .circle-bg-warn {
    background: #ec4255;
  }
  .circle-bg-red {
     background: #ec4255;
  }
  .bg-green {
    stroke: rgba(13, 112, 53, 1);
  }
  .end-line1 {
    .position-r(-8, 242);
  }
  .end-line4 {
    .position-r(200, 175);
  }
  .bg-red {
    stroke: rgba(90, 37, 37, 1);

  }
  .bg-warn {
    stroke: rgba(90, 71, 37, 1);
  }
  .bg-dyLine {
    stroke-width: 10px;
    stroke-linecap: round;
    stroke-linejoin: round;
    stroke-dashoffset: 80;
    stroke-dasharray: 180 60;
    visibility: hidden;
  }
  .bg-dyLine-2 {
    stroke-width: 2px;
    visibility: hidden;
    stroke-dashoffset: 10;
    stroke-dasharray: 50 80;
  }
  .bg-dyLine-4 {
    stroke-width: 3px;
    visibility: hidden;
    stroke-dashoffset: 0;
    stroke-dasharray: 100 50;
  }
  .ani1 {
    animation: svg-path-animation1 1s ease infinite;
  }
  .ani2,
  .ani3 {
    animation: svg-path-animation2 1s ease infinite;
    animation-direction: reverse;
  }
  .ani3 {
    animation-name: svg-path-animation3;
  }
  .ani {
    animation: svg-path-animation 1s ease infinite;
  }
  .bg-dyLine-f {
    animation-direction: reverse;
  }
  .bg-dyLine-3 {
    stroke-dasharray: 220 150;
  }
  .bg-dyLine-red {
    stroke: url(#red);
  }
  .bg-dyLine-fred {
    stroke-dasharray: 60 150;
    stroke: url(#red1);
  }
  .bg-dyLine-f {
    animation-direction: reverse;
  }
  .bg-dyLine-green {
    stroke: url(#green);
  }
  .bg-dyLine-warn {
    stroke: url(#warning);
  }
  @keyframes svg-path-animation {
    from {
      stroke-dashoffset: 180;
      visibility: visible;
    }
    to {
      stroke-dashoffset: 0;
      visibility: hidden;
    }
  }
  @keyframes svg-path-animation1 {
    from {
      stroke-dashoffset: 80;
      visibility: visible;
    }
    to {
      stroke-dashoffset: 0;
      visibility: hidden;
    }
  }
  @keyframes svg-path-animation2 {
    from {
      stroke-dasharray: 0, 411px;
      visibility: visible;
    }
    to {
      stroke-dasharray: 411px, 0;
      visibility: hidden;
    }
  }
  @keyframes svg-path-animation3 {
    from {
      stroke-dasharray: 0, 111px;
      visibility: visible;
    }
    to {
      stroke-dasharray: 111px, 0;
      visibility: hidden;
    }
  }
      .robot_img {
        .position(10, 20);
        animation: robot 2s cubic-bezier(0.6, -0.47, 0.63, 1) 0s infinite;
        z-index: 9;
      }

  .panel {
    position: absolute;
   // .position(335, 250);
    .group_string {
      .position(40, 44);
      z-index: 4;
      transform: rotate(209deg);
    }
    .box-shadow {
      position: relative;
      top: 135px;
      left: 20px;
    }
    .group_string_alarm {
      .position(123, 249);
      z-index: 12;
    }
    .group_shelf {
      .position(46, 71);
      z-index: 1;
    }
  }
  .combiner_box {
    position: absolute;
    bottom: 70px;
    left: 105px;
    z-index: 3;
    cursor: pointer;
  }
  .combiner_box_alarm {
    position: absolute;
    bottom: 98px;
    left: 124px;
    z-index: 4;
  }
  .group_box_alarm {
   position: absolute;
    bottom: 188px;
    left: 230px;
    z-index: 5;
}
  .inverter {
    .position(182, 140);
    z-index: 3;
    cursor: pointer;
  }
  .inverter_alarm {
    .position(290, 221);
    z-index: 4;
  }
  .box_change {
    .position(186, 395);
    z-index: 3;
     cursor: pointer;
  }
  .box_change_alarm {
    .position(255, 475);
    z-index: 4;
  }
  .collector_line {
    .position(450, 180);
    cursor:pointer;
    z-index: 2;
  }
  .collector_line_alarm {
    .position(135, 175);
    z-index: 4;
  }
  .group_change {
    .position(440, 625);
    cursor: pointer;
  }
  .group_change_alarm {
    .position(502, 703);
  }
  .svg {
    .position(630, 900);
    z-index: 11;
    cursor: pointer;
  }
  .svg_alarm {
    .position(690, 1000);
    z-index: 11;
  }
  .boost_voltage {
    .position(273, 958);
    cursor: pointer;
  }
  .boost_voltage_alarm {
    .position(400, 1095);
  }
  .electric_meter {
    position: absolute;
    right: 108px;
    bottom: 36px;
    cursor: pointer;
  }
  .electric_meter_alarm {
    position: absolute;
    right: 148px;
    bottom: 45px;
  }
  .env_monitor {
    position: absolute;
    right: 60px;
    bottom: 110px;
    cursor: pointer;
  }
  .env_monitor_alarm {
    position: absolute;
    right: 60px;
    bottom: 135px;
  }
  @keyframes robot {
    from {
      transform: translate(10px);
    }
    to {
      transform: translate(80px, 30px);
    }
  }
}
.font-16 {
  font-size: 16px;
}
.color_r {
  color: rgb(255, 77, 79);
}

</style>
<style lang="less">
@import url('../../less/popover.less');
</style>
