<template>
  <a-dropdown
    overlayClassName='ps-tree-select'
    :trigger="['click']"
    class='solareye-virtual-tree'
    :getPopupContainer="triggerNode => {return isBody ? body : triggerNode.parentNode}"
    v-model='visible'>
    <div class='ant-input ps-tree-input' @click='stopEvent' :class="{ 'ant-input-disabled': disabled }" v-if="isInput">
      <span v-if='checked'>已选电站&nbsp;{{ psNum }}&nbsp;座</span>
      <div class='ellipsis' v-else :title='psName'>{{ psName }}</div>
    </div>
    <div @click='stopEvent' v-else><a-icon type="swap" /></div>
    <a-menu slot='overlay' @select='() => {}'>
      <a-menu-item key='1'>
        <a-input-search style='margin: 8px; width: 92%' v-model='searchValue' @search='search' :disabled="spinning">
        </a-input-search>
        <virtual-tree
          class='tree-content'
          v-if='refresh'
          ref='veTree'
          node-key='key'
          :height='height'
          :data='treeData'
          :props='props'
          :show-checkbox='checked'
          @check='onChange'
          @node-click='onSelect'
          @node-expand='nodeExpend'
          :render-content='renderContent'
          :current-node-key='selectId'
          :expand-on-click-node='checked'
          :default-expanded-keys='treeExpandedKeys'
          :default-checked-keys='defaultCheckedKeys'
        />
        <div v-else class='tree-loading' :style="{height: height}">
          <a-spin :spinning='spinning'>
          </a-spin>
        </div>
      </a-menu-item>
    </a-menu>
  </a-dropdown>
</template>
<script>
import { getTreeList, searchTreeList, getDepartPsaCount, getDepartPsaTree } from '@/api/isolarErp/com/funnel';
import virtualTree from '@/components/virtrulTree/ve-tree.vue';
import { monitorPowerStationList } from '@/api/monitor/runMonitor';
export default {
  name: 'PsTreeSelectHealth',
  components: { virtualTree },
  props: {
    value: {
      type: String,
      required: false
    },
    allowClear: {
      type: Boolean,
      default: true
    },
    disabled: {
      type: Boolean,
      default: false
    },
    // 是否需要禁用组织
    isPsaDisable: {
      type: Boolean,
      default: true
    },
    isQueryPs: {
      default: 0,
      type: [Number, String]
    },
    checked: {
      default: false,
      type: Boolean
    },
    isPsName: {
      default: '',
      type: String
    },
    isReset: {// 是否重置按钮
      type: Boolean,
      default: false
    },
    // 是否只查询组织
    isOnlyDep: {
      type: Boolean,
      default: false
    },
    // 无人机-是否只查询已建模电站
    hasModel: {
      type: Boolean,
      default: false
    },
    height: {
      type: String,
      default: '40vh'
    },
    isPsManage: {
      type: Boolean,
      default: false
    },
    isInsight: {
      type: String,
      default: '0'
    },
    isBody: {
      type: Boolean,
      default: false
    },
    isInput: {
      type: Boolean,
      default: true
    },
    psCateGory: {
      default: undefined
    },
    // 只查询运维中的电站
    hasMaintenanceStatusParams: {
      type: Boolean,
      default: false
    },
    // 是否使用新的已选电站数量接口
    newCount: {
      type: Boolean,
      default: false
    }
  },
  created () {
    // 树型数据模拟
    // console.log('-------- 生成模拟数据中 --------')
    // const data = [],
    //   root = 10,
    //   children = 10,
    //   base = 10000;
    // for (let i = 0; i < root; i++) {
    //   data.push({
    //     id: `${i}`,
    //     key: `${i}`,
    //     name: `test-${i}`,
    //     children: []
    //   });
    //   for (let j = 0; j < children; j++) {
    //     data[i].children.push({
    //       id: `${i}-${j}`,
    //       key: `${i}-${j}`,
    //       name: `test-${i}-${j}`,
    //       children: []
    //     });
    //     for (let k = 0; k < base; k++) {
    //       data[i].children[j].children.push({
    //         id: `${i}-${j}-${k}`,
    //         key: `${i}-${j}-${k}`,
    //         name: `test-${i}-${j}-${k}`
    //       });
    //     }
    //   }
    // }
    // this.treeData = data;
    // console.log('-------- 生成模拟数据中 --------')

    this.selectId = this.value;
    this.psName = this.isPsName;
    this.psNum = 0;
    if (!this.disabled) {
      this.loadDeptTree(false, !!this.isPsName);
    } else if (this.disabled && this.value) {
      this.selectId = this.value.toString();
      this.loadDeptTree(true);
    }
  },
  watch: {
    value (val, old) {
      if (this.isOnlyDep) return;

      if (this.disabled && this.value) {
        this.selectId = this.value.toString();
        this.loadDeptTree(true);
      }
      // if(!val || val == this.treeData[0].id) {
      //   this.loadDeptTree(false)
      // }
      if (!val) {
        this.selectedKey = '';
        this.psName = '';
      }
      if (this.isReset) {
        this.selectId = this.checked ? [this.treeData[0].key] : this.treeData[0].key;
        this.selectedKey = this.treeData[0].key;
        this.node = this.treeData[0];
        this.psName = this.treeData[0].name;
        this.initSendData(0);
      }
    },
    nodes (val) {
    },
    isPsName () {
      if (this.isPsName) {
        this.psName = this.isPsName;
        // this.treeExpandedKeys=[];
        // this.selectId = [];
      }
    }
  },
  data () {
    return {
      selectId: null,
      treeData: [],
      treeExpandedKeys: [],
      defaultCheckedKeys: [],
      nodes: [],
      searchValue: '',
      lastSearch: '',
      props: {
        label: 'name',
        isLeaf: 'isLeaf',
        children: 'children'
      },
      expended: false,
      node: {},
      visible: false,
      expand: false,
      selectedKey: '',
      psNum: 0,
      autoExpandParent: false,
      loadedKeys: [],
      psName: '',
      isSeach: false,
      spinning: false,
      refresh: true,
      depList: [],
      psaList: [],
      depCodeList: [],
      parentName: '',
      parentId: '',
      parentType: '',
      parentOrgCode: '',
      rootNode: {},
      body: document.body
    };
  },
  methods: {
    stopEvent (e) {
      e.preventDefault();
      this.visible = !this.visible;
      // this.searchValue = this.checked ? '' : this.searchValue
      if (this.treeData.length == 0) {
        this.loadDeptTree(false, 1);
      }
    },
    // setTreeExpandedKeys() {
    //   let _this = this
    //   let treeExpandedKeys = (this.treeExpandedKeys = [])
    //   _this.$nextTick(() => {
    //     if (!this.selectId || (Array.isArray(this.selectId) && this.selectId.length == 0)) {
    //       return
    //     }
    //     let keyId = this.selectId
    //
    //     function find(selectId) {
    //       let node = _this.nodes.find((item) => item.key == selectId)
    //       if (node) {
    //         treeExpandedKeys.push(node.key)
    //         if (node.parentKey) {
    //           find(node.parentKey)
    //         }
    //       }
    //     }
    //
    //     find(keyId)
    //     this.treeExpandedKeys = treeExpandedKeys.reverse()
    //   })
    // },
    // onExpand(expandedKeys) {
    //   this.treeExpandedKeys = expandedKeys
    //   this.loadedKeys = expandedKeys
    //   this.autoExpandParent = false
    // },
    /*
    * 点击节点展开方法 第一次点击非叶子节点时为懒加载
    * */
    async nodeExpend (data, node, vueComponent) {
      // 设置图标为收缩图标，等加载数据后再展开，达到与el-tree默认懒加载同样的效果
      node.expanded = false;
      if (!node.loaded) { // 当没有加载过的节点才会请求数据
        node.loading = true;
        let treeNodeData = data;
        let res;

        if (this.isOnlyDep) {
          res = await getDepartPsaTree({ initValue: treeNodeData.orgCode, initValueId: treeNodeData.id });
        } else {
          let params = { initTreeEntity: treeNodeData, isQueryPs: this.isQueryPs, hasModel: this.hasModel, isPsManage: this.isPsManage ? '1' : undefined, isInsight: this.isInsight, psCateGory: this.psCateGory };
          if (this.hasMaintenanceStatusParams) {
            params.maintenanceStatus = '2';
          }
          res = await getTreeList(params);
        }

        let children = res.result_data;
        children.map((item, index) => {
          if ((item.isPsa == '1' && this.isQueryPs == 0) || item.isParent == '0') {
            item.isLeaf = true;
          }
          if ((this.isQueryPs == 1 && item.isPs == 1) || item.isParent == '0') {
            item.isLeaf = true;
          }
          if (item.isParent == '1') {
            item.isLeaf = false;
          }
          if (!this.checked && (this.value && !this.selectedKeys) && item.id == this.value) {
            this.selectedKey = [item.key];
            this.selectId = [item.key];
          }
        });
        this.nodes = [...this.nodes, ...children];
        children = this.isPsaDisable ? this.getAllPsa(children) : children;
        node.loaded = true;
        node.loading = false;
        node.expanded = true;

        if (children.length > 0) {
          this.$refs.veTree.updateKeyChildren(node.key, children);
          if (node.checked && this.checked) {
            this.$nextTick(() => {
              this.$refs.veTree.setChecked(node.key, true, true);
            });
          }
        } else {
          node.isLeaf = true;
        }
      } else {
        node.expanded = true;
      }
    },
    // element 自带节点渲染方法
    renderContent (h, { node, data, store }) {
      // 如果是叶子节点，就让展开/收缩按钮显示
      if (node.childNodes.length === 0) {
        node.isLeaf = false;
        node.expanded = false;
      } else {
        // 如果节点已经展开，并且存在子节点，则loaded设为true
        // 表示已加载过了，下次展开时，就不要请求数据了
        if (node.expanded) {
          node.loaded = true;
        }
      }
      node.isLeaf = data.isLeaf;
      if (data.disabled) {
        return (
          <span class='custom-tree-node'>
            <span class='node-disabled' title={node.label}>{node.label}</span>
          </span>);
      } else {
        return (
          <span class='custom-tree-node'>
            <span title={node.label}>{node.label}</span>
          </span>);
      }
    },
    /**
     * func 获取部门树
     *  params flag： 详情等页面只返回一个电站节点，false 返回所有节点
     * */
    async loadDeptTree (flag, isChild) {
      this.spinning = true;
      this.isSeach = false;
      this.expended = false;
      this.isShow = true;
      this.treeExpandedKeys = [];
      this.selectId = [];
      this.loadedKeys = [];
      let params = { isQueryPs: this.isQueryPs };
      if (flag) {
        params = {
          initValue: this.value,
          disabled: this.disabled
        };
      }
      params.hasModel = this.hasModel;
      params.isPsManage = this.isPsManage ? '1' : undefined;
      params.isInsight = this.isInsight;
      params.psCateGory = this.psCateGory;
      this.treeData = this.nodes = [];
      this.node = {};
      try {
        let res = {};
        if (this.hasMaintenanceStatusParams && !this.isOnlyDep) {
          params.maintenanceStatus = '2';
        }
        res = this.isOnlyDep ? await getDepartPsaTree({}) : await getTreeList(params);
        this.spinning = false;
        if (res.result_code == '1') {
          let items = [];
          items = this.isPsaDisable ? this.getAllPsa(res.result_data) : res.result_data; // 禁用电站档案和已关闭的电站
          items[0].isLeaf = false;
          this.nodes = items;
          this.treeData = this.getNodes(items);
          this.defaultCheckedKeys = items.map(item => item.key);
          this.refresh = true;
          this.rootNode = this.treeData[0];
          this.rootNode.parentName = '';
          this.rootNode.parentType = '';
          this.rootNode.parentOrgCode = '';
        }
        if (this.treeData.length == 0) {
          return;
        }
        if (flag) {
          this.selectedKey = this.treeData[0].key;
          this.psName = this.treeData[0].name;
        }
        if (isChild == 1) {
          return;
        }
        if (!this.isPsaDisable) {
          // 默认选中根节点
          this.selectId = this.checked ? [this.treeData[0].key] : this.treeData[0].key;
          this.selectedKey = this.treeData[0].key;
          this.node = this.treeData[0];
          this.psName = this.treeData[0].name;
          this.rootNode = this.treeData[0];
          this.rootNode.parentName = '';
          this.rootNode.parentType = '';
          this.rootNode.parentOrgCode = '';
          if (isChild) {
            return;
          }
          if (!this.checked) {
            this.initSendData(0);
          } else {
            this.getCountNum([this.treeData[0].orgCode], [], [], false).then((res) => {
              this.initSendData(res, isChild);
            });
          }
        }
        this.node = this.treeData[0];
      } catch (e) {
        this.treeData = [];
      }
    },
    initSendData (num) {
      this.psNum = num;
      this.sendDataToParent(this.selectId, {
        depIds: this.treeData[0].id,
        psaIds: '',
        psIds: '',
        depCodes: this.treeData[0].orgCode,
        isFirstReq: false,
        psNum: num,
        isSeach: false,
        ...this.treeData[0]
      });
    },
    // 树形数据转换
    getNodes (data) {
      let result = [];
      if (!Array.isArray(data)) {
        return result;
      }
      //  空对象
      let map = {};
      data.forEach((item) => {
        delete item.children;
        map[item.key] = item;
      });
      data.forEach((item) => {
        let parent = map[item.parentKey];
        if (parent) {
          ;(parent.children || (parent.children = [])).push(item);
        } else {
          result.push(item);
        }
      });
      return result;
    },
    /**
     * func 电站档案及关闭的电站置灰
     * params items 树节点
     * */
    getAllPsa (items) {
      if (items instanceof Array) {
        items.forEach((item) => {
          if (this.isQueryPs == 1) {
            item.disabled = item.isPs == 0 || (item.status == '3' && this.isInsight != '1'); // isPs 是否是实体电站
          } else {
            item.disabled = item.isPsa == '0' || item.status == '3'; // isPjPsa 电站档案  /* psaStatus == '3' 表示已关闭  */
          }
        });
        return items;
      }
      return [];
    },

    /**
     * func 选中树节点
     * params value 电站id
     * params label 名称
     * extra 主要是用来获取node节点其他参数信息
     * */
    onChange (value, { checkedKeys, checkedNodes }) {
      this.isSeach = false;
      this.$emit('checkStart');
      this.getSelectObj(checkedNodes, true);
    },
    /*
    * 点击树节点时
    * */
    onSelect (data, node) {
      if (this.checked) { // 多选时 屏蔽单选
        this.$refs.veTree.setCurrentKey(null);
        return;
      } else {
        // 单选点击时 如果是disabled 则屏蔽掉element tree自带的 点击选中效果
        if (data.disabled) {
          this.$refs.veTree.setCurrentKey(Array.isArray(this.selectId) ? null : this.selectId);
          return;
        } else {
          this.selectId = data.key;
        }
      }

      this.parentName = '';
      this.parentId = '';
      this.parentType = '';
      this.parentOrgCode = '';
      let dataRef = data;
      this.selectedKey = data.key;
      this.psName = dataRef.name;
      this.visible = false;
      this.node = dataRef;
      this.sendDataToParent(dataRef.id, dataRef);
    },
    // 获取选中数量
    getCountNum (depCodes = [], psaIds = [], psIds = [], isSelect = false) {
      let params = {
        depCodes: depCodes.join(','),
        psaIds: psaIds.join(','),
        psCateGory: this.psCateGory
      };
      if (this.hasMaintenanceStatusParams) {
        params.maintenanceStatus = '2';
      }
      if (this.newCount) {
        let obj = {
          pageSize: 8,
          currentPage: 1,
          treePsId: psIds.join(','),
          elecEnsureType: '0',
          order: 'tmp.ps_id desc'
        };
        Object.assign(params, obj);
      } else {
        params.psIds = psIds.join(',');
      }
      return new Promise((resolve) => {
        let countApi = this.newCount ? monitorPowerStationList : getDepartPsaCount;
        countApi(params).then((res) => {
          let data = this.newCount ? res.result_data.rowCount : res.result_data;
          this.psNum = data;
          resolve(data);
        });
      });
    },
    /**
     * func 发送数据到父元素
     * params value 电站id
     * params node 选中的节点对象
     */
    sendDataToParent (value, node) {
      let obj = Object.assign({}, node);
      delete obj['children'];
      this.filterParent(this.treeData);
      obj.parentName = this.parentName;
      obj.parentId = this.parentId;
      obj.parentType = this.parentType;
      obj.parentOrgCode = this.parentOrgCode;
      obj.searchValue = this.lastSearch;
      this.$emit('change', value, obj);
    },
    search (value) {
      this.$emit('treeSearchStart');
      this.searchEvent();
    },
    /*
    *  搜索时间 每次搜索会重新渲染树 屏蔽搜索后非叶子节点不能正常展示展开图标问题
    * */
    async searchEvent () {
      this.refresh = false;
      this.isSeach = true;
      this.spinning = true;
      let value = this.searchValue;
      if (this.checked && !value) {
        this.loadDeptTree(false);
        return;
      }
      if ((!this.checked && !value)) {
        this.isShow = false;
        this.loadDeptTree(false);
        return;
      }

      let res;
      if (this.isOnlyDep) {
        res = await getDepartPsaTree({ search: this.searchValue }).catch(e => {
          this.spinning = false;
          this.refresh = true;
        });
      } else {
        let params = { search: value, initTreeEntity: this.node, isQueryPs: this.isQueryPs, hasModel: this.hasModel, isPsManage: this.isPsManage ? '1' : undefined, isInsight: this.isInsight, psCateGory: this.psCateGory };
        if (this.hasMaintenanceStatusParams) {
          params.maintenanceStatus = '2';
        }
        res = await searchTreeList(params).catch(e => {
          this.spinning = false;
          this.refresh = true;
        });
        this.$emit('treeSearchEnd');
      }
      this.lastSearch = this.searchValue;
      this.spinning = false;
      this.treeData = [];
      this.nodes = [];
      this.treeExpandedKeys = [];
      this.selectId = [];
      if (res.result_code == '1') {
        this.expended = true;
        let children = res.result_data;
        let items = [];
        items = this.isPsaDisable ? this.getAllPsa(children) : children; // 禁用电站档案和已关闭的电站
        items = items.map((item) => {
          if (this.checked) {
            this.selectId.push(item.key);
          } else {
            if (item.id == this.value) {
              this.selectedKey = [item.key];
              this.selectId = [item.key];
            }
          }
          if (item.parentKey && this.treeExpandedKeys.indexOf(item.parentKey) == -1) {
            this.treeExpandedKeys.push(item.parentKey);
          }
          return {
            ...item,
            isLeaf: item.isParent != 1
          };
        });
        this.autoExpandParent = true;

        this.nodes = items;
        this.treeData = this.getNodes(items);
        this.defaultCheckedKeys = items.map(item => item.key);
        this.refresh = true;

        if (this.checked) {
          this.getSelectObj(res.result_data);
        }
      }

      if (this.treeData.length == 0) {

      }
    },
    getTreeChild (arr) {
      arr &&
    arr.forEach((item) => {
      if (item.isParent == '1') {
        item.isLeaf = false;
      } else {
        item.isLeaf = true;
      }
    });
    },
    // 搜索展开节点
    setExpandedKeys () {
      this.treeExpandedKeys = this.nodes.map((item) => {
        return item.key;
      });
    },
    filterParent (arr, depIds, psaIds, codes) {
      arr.forEach((item) => {
        if (item.children && depIds) {
          this.depList = depIds.filter(() => depIds.indexOf(item.id) == -1);
          this.psaList = psaIds.filter(() => psaIds.indexOf(item.id) == -1);
          this.depCodeList = codes.filter(() => codes.indexOf(item.orgCode) == -1);
          this.filterParent(item.children, this.depList, this.psaList, this.depCodeList);
        } else if (item.children && !depIds) {
          this.filterParent(item.children);
        }
        if (item.key == this.node.parentKey) {
          this.parentName = item.name;
          this.parentOrgCode = item.orgCode;
          this.parentId = item.id;
          this.parentType = item.isPsa == '1' ? '2' : '1';
        }
      });
    },
    // isSelect true 手动change 事件， false 查询
    getSelectObj (arr, isSelect) {
      let psIds = [];
      let depIds = [];
      let psaIds = [];
      let depCodes = [];
      // 过滤出现有树的所有最末节点
      arr = arr.filter(item => { return arr.every(el => el.parentKey != item.key); });
      // 将过滤后的节点分类为 实体电站 部门 电站档案
      if (arr.length > 0) {
        arr.forEach((item) => {
          let dataProps = item;
          let treeId = dataProps.id;
          let istrue = this.isSeach ? dataProps.isLastLevel == 1 : true;
          if (dataProps.isPsa == 0 && !dataProps.isPs && istrue) {
            depIds.push(treeId);
            depCodes.push(dataProps.orgCode);
          }
          if (dataProps.isPsa == 1 && istrue) {
          // 部门id
            psaIds.push(treeId);
          }
          if (dataProps.isPs == 1 && istrue) {
          // 实体电站
            psIds.push(treeId);
          }
        });
      }

      // this.filterParent(this.treeData, depIds, psaIds, depCodes)

      this.getCountNum(depCodes, psaIds, psIds, this.isSeach).then((res) => {
        this.psNum = res;
        this.sendDataToParent(this.selectId, {
          depIds: depIds.join(','),
          psaIds: psaIds.join(','),
          psIds: psIds.join(','),
          depCodes: depCodes.join(','),
          psNum: res,
          isFirstReq: false,
          isSeach: this.isSeach
        });
      });
    }
  }
};
</script>
<style lang='less' scoped>

:deep(.ant-select) {
  :deep(.ant-select-selection__choice__content) {
    max-width: 90px !important;
  }
}

.tree-loading {
  width: 474px;
  height: 500px;
  display: block;
  display: flex;
  justify-content: center;
  align-items: center;
}
.tree-content{
  width: 474px;
}

[dropdownclassname='pro-tree-select-dropdown'] {
  height: 50vh;
  max-height: 50vh;
  overflow: auto;
}

.ant-dropdown.ps-tree-select {
  .ant-dropdown-menu,
  .ant-select-dropdown-placement-bottomLeft {
    margin-top: 0px;

    &::after,
    &::before {
      display: none !important;
    }
  }

  &::before,
  &::after {
    display: none;
  }
}

 :deep(.ant-dropdown-menu-item:hover) {
  background: none !important;
}

.ps-tree-input {
  margin-bottom: 2px;
 // vertical-align: text-top;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  display: inline-flex;
  vertical-align: middle;
  &.ant-input-disabled {
    pointer-events: none;
    cursor: not-allowed;
  }

  .ellipsis {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
}
:deep(.custom-tree-node) {
  max-width: 410px;
  overflow: hidden;
  text-overflow: ellipsis;
}

.search-item, .search-select {
  .ps-tree-input {
    margin-bottom: 0 !important;
  }
}
</style>
