// 分布式 - 工单大厅
import { axios } from '@/utils/erp-request';
// 工单分页
export function gatPageList (data) {
  return axios({
    url: '/industry/ops/order/page',
    method: 'POST',
    data
  });
}
// 工单求和
export function getOrderCount (data) {
  return axios({
    url: '/industry/ops/order/count',
    method: 'POST',
    data
  });
}
// 工单验收
export function checkOrder (data) {
  return axios({
    url: '/industry/ops/order/check',
    method: 'POST',
    data
  });
}
// 工单详情
export function gatOrderDetail (data) {
  return axios({
    url: '/industry/ops/order/detail',
    method: 'POST',
    data
  });
}
// 查询缺陷故障预计消除时间
export function getPredictPlanTimeLimit (parameter) {
  return axios({
    url: '/industry/pdca/wotConfig/getPredictPlanTimeLimit',
    method: 'post',
    data: parameter
  });
}
// 根据停电范围或缺陷处理条件查询缺陷级别
export function getDefectType (parameter) {
  return axios({
    url: '/industry/defect/level/defectType',
    method: 'post',
    data: parameter
  });
}
// 新增或修改执行日志
export function insertCleanData (parameter) {
  return axios({
    url: '/industry/order/clean/insert',
    method: 'post',
    data: parameter
  });
}
// 根据当前填报日期清洗容量 计算累计及清洗进度
export function getCleanCap (parameter) {
  return axios({
    url: '/industry/order/clean/query/cap',
    method: 'post',
    data: parameter
  });
}
// 查询单个执行日志
export function getSingleCleanDetail (parameter) {
  return axios({
    url: '/industry/order/clean/select',
    method: 'post',
    data: parameter
  });
}
// 复制日志文本-日志复制
export function copyTextApi (parameter) {
  return axios({
    url: '/industry/order/clean/copy',
    method: 'post',
    data: parameter
  });
}
// 检验日期重复-查询当前是否可填写日志
export function checkCleanDateApi (parameter) {
  return axios({
    url: '/industry/order/clean/check',
    method: 'post',
    data: parameter
  });
}
// 日志删除-删除执行日志
export function deleteCleanLogApi (parameter) {
  return axios({
    url: '/industry/order/clean/delete',
    method: 'post',
    data: parameter
  });
}
// 清洗执行日志导出
export function exportData (parameter) {
  return axios({
    url: '/industry/order/clean/export',
    method: 'post',
    data: parameter
  });
}
// oa流程审批执行
export function executeOaApi (parameter) {
  return axios({
    url: '/industry/order/oa/approve',
    method: 'post',
    data: parameter
  });
}
// oa执行流程详情
export function getOaDetailApi (parameter) {
  return axios({
    url: '/industry/order/oa/detail',
    method: 'post',
    data: parameter
  });
}
// 工单指派领取
export function orderReceive (data) {
  return axios({
    url: '/industry/ops/order/assign',
    method: 'POST',
    data
  });
}
// 工单执行
export function executeOrder (data) {
  return axios({
    url: '/industry/ops/order/execute',
    method: 'POST',
    data
  });
}
// 履约组负责人-查询 保险购买工单 对应电站 履约组角色人员列表
export function getInsuranceAssignList (parameter) {
  return axios({
    url: '/industry/ops/order/insurance/assign',
    method: 'post',
    data: parameter
  });
}
// 获取站荣站貌不分页列表
export function getAppearanceConfigListApi (parameter) {
  return axios({
    url: '/industry/order/config/appearance/list',
    method: 'post',
    data: parameter
  });
}
// 获取工单是否需要必填-查询任务类型是否需要关联两票
export function getIsNeedTicketApi (parameter) {
  return axios({
    url: '/industry/order/config/isNeedTicket',
    method: 'post',
    data: parameter
  });
}
//
export function getExperimentDate (parameter) {
  return axios({
    url: '/industry/ops/order/beforeExperimentDate',
    method: 'post',
    data: parameter
  });
}

// 暂时未使用

// 查询工单版本：1：new，反之 resolveOrderPath
// export function getOrderVersion (data) {
//   return axios({
//     url: '/industry/ops/order/version',
//     method: 'POST',
//     data
//   });
// }
// 导出
// export function exportOrderFile (data) {
//   return axios({
//     url: '/industry/ops/order/exportDetail',
//     method: 'POST',
//     data
//   });
// }

// 有使用还未迁移

// 获取两票是否全部已终结
export function getTicketAllEndApi (parameter) {
  return axios({
    url: '/open/ticket/order/ticket/ticketAllEnd',
    method: 'post',
    data: parameter
  });
}
// 获取两票列表
export function getTicketPageApi (parameter) {
  return axios({
    url: '/open/ticket/order/ticket/page',
    method: 'post',
    data: parameter
  });
}

