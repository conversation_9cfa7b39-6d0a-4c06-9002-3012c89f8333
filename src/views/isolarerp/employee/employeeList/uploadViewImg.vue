<template>
  <a-upload ref="upload" action="#"
    :accept="accept"
    :beforeUpload="beforeFileUpload" :show-upload-list="false">
    <a-icon title="上传" type="cloud-upload" />
  </a-upload>
</template>
<script>
import { addWaterMarker } from '@/utils/addWaterMarker';
import { certRecognition } from '@/api/isolarErp/employee/employee';
export default {
  name: 'uploadViewImg',
  props: {
    value: {
      type: [Number, String],
      default: null
    },
    accept: {
      type: String,
      default: '.jpg, .png, .jpeg, .bmp'
    },
    // 是否是照片附件页面
    isPicFile: {
      type: Boolean,
      default: false
    },
    empName: {
      type: String,
      default: undefined
    },
    // 是否添加水印
    addWaterMarker: {
      type: Boolean,
      default: false
    }
  },
  data () {
    return {
      loading: false
    };
  },
  methods: {
    // 上传文件前的判断
    beforeFileUpload (file) {
      let self = this;
      self.loading = true;
      // 图片附件上传时需要先填写姓名
      if (self.isPicFile && !self.empName) {
        self.$message.warning('请先填写姓名');
        self.loading = false;
        return false;
      }
      let testmsg = file.name.substring(file.name.lastIndexOf('.') + 1).toLowerCase();
      let accept = (self.accept ? self.accept.toString() : '');
      if (accept && accept.indexOf(testmsg) == -1) {
        self.$message.warning('只能上传' + accept + '格式的文件!');
        self.loading = false;
        return false;
      }
      if (file.size / 1024 / 1024 > 10) {
        self.$message.warning('上传文件的大小不能超过10MB!');
        self.loading = false;
        return false;
      }
      let reader = new FileReader();
      reader.readAsDataURL(file);
      // 转base64
      reader.onload = function () {
        self.loading = false;
        let files = reader.result.split(',');
        if (self.addWaterMarker) {
          addWaterMarker(files[1]).then(resultImg => {
            files = resultImg.split(',');
            let blob = files[1];
            const obj = {
              'file': blob,
              'fileName': file.name
            };
            self.$emit('change', self.value, obj);
            self.$message.success('上传成功');
          });
        } else {
          self.$emit('loading', self.value, true);
          let blob = files[1];
          let obj = {
            'file': blob,
            'fileName': file.name,
            mime: testmsg
          };
          const data = Object.assign({}, obj);
          // zuomanman modify ocr 识别
          certRecognition(obj).then(res => {
            //
            self.$emit('change', self.value, { ...data, ...res.result_data, autoRecognized: 1 });
            self.$message.success('上传成功');
          }).catch(() => {
            self.$emit('change', self.value, { ...data, autoRecognized: 0 });
          });
          // self.$emit('change', self.value, obj);
          // self.$message.success('上传成功');
        }
      };
      return false;
    },
    // 提交上传文件
    submitFileForm () {
      const self = this;
      self.loading = true;
      if (self.constFile == null) {
        self.loading = false;
        return;
      }
      let reader = new FileReader();
      reader.readAsDataURL(self.constFile);
      // 转base64
      reader.onload = function () {
        self.blobFile = reader.result.split(',')[1];
        self.upLoadPic(self.constFile);
      };
    }
  }
};
</script>
<style lang="less" scoped>
  :deep(.anticon-cloud-upload) {
    font-size: 16px;
    color: #1890FF;
    cursor: pointer;
    margin-right: 10px;
  }
</style>
