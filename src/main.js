/** init domain config */
import './config';
// 引入wujie微前端
import '@/wujie';

/// / import Vue from 'vue'
import Storage from 'vue-ls';

import store from './store/';
import { VueAxios } from '@/utils/request';

import Antd from 'ant-design-vue';
import 'ant-design-vue/dist/antd.less'; // or 'ant-design-vue/dist/antd.less'
import VueAwesomeSwiper from 'vue-awesome-swiper';
import 'swiper/dist/css/swiper.css';
import '@/permission'; // permission control
import '@/utils/filter';
import SSO from '@/cas/sso.js';

import {
  ACCESS_TOKEN,
  DEFAULT_COLOR,
  DEFAULT_THEME,
  DEFAULT_LAYOUT_MODE,
  DEFAULT_COLOR_WEAK,
  SIDEBAR_TYPE,
  DEFAULT_FIXED_HEADER,
  DEFAULT_FIXED_HEADER_HIDDEN,
  DEFAULT_FIXED_SIDEMENU,
  DEFAULT_CONTENT_WIDTH_TYPE,
  DEFAULT_MULTI_PAGE
} from '@/store/mutation-types';
import config from '@/defaultSettings';
import SolareyeDictSelectTag from './components/dict/index.js';
import hasPermission, { isSupportWebp } from '@/utils/hasPermission';

// import vueBus from '@/utils/vueBus';
import SolareyeComponents from '@/components/solareye/index';
import '@/components/solareye/SVxeTable/install';
import '@/assets/less/erpcom.less';
// 挂载全局使用的方法
// 平台封装的公共组件在com里面全局注册
import CommonComponents from '@/components/com/index';

import { $trim, $downloadFile, getNowTime, tabFormatter, getLabel, showHandle, $windowOpenPdf, errorScroll, filterOption, isEmpty,
  getRootColor } from '@/utils/erpcommon.js';

import { getAction, postFormAction, postAction } from '@/api/manage';

import { isLoadedJs } from '@/utils';

import './assets/icons'; // icon

// health
// import htmlToPDF from "@/utils/htmlToPdf.js" // 导出pdf 只有清洁度评估使用，移动到具体的页面
import '@/assets/styles/index.less';
// Vue.use(htmlToPDF);
import App from './App.vue';
import router from './router';
import { captureVueError } from '@/utils/webTracking';

Vue.config.devtools = true;
Vue.config.errorHandler = captureVueError;

// import moment from 'moment';
// import 'moment/locale/zh-cn';
// moment.locale('zh-cn');

Vue.config.productionTip = false;
Vue.use(Storage, config.storageOptions);
// Vue.use(moment)
Vue.use(Antd);
Vue.use(VueAwesomeSwiper);
Vue.use(VueAxios, router);
// Vue.use(Viser)
Vue.use(hasPermission);
Vue.use(SolareyeDictSelectTag);
// Vue.use(Print)
// Vue.use(preview)
// Vue.use(vueBus);
Vue.use(SolareyeComponents);
Vue.use(CommonComponents);
Vue.prototype.$trim = $trim;
Vue.prototype.$downloadFile = $downloadFile;
Vue.prototype.getNowTime = getNowTime;
Vue.prototype.tabFormatter = tabFormatter;
Vue.prototype.getLabel = getLabel;
Vue.prototype.showHandle = showHandle;
Vue.prototype.$windowOpenPdf = $windowOpenPdf;

Vue.prototype.isSupportWebp = isSupportWebp();
Vue.prototype.postFormAction = postFormAction;
Vue.prototype.postDataAction = postAction;
Vue.prototype.getAction = getAction;
Vue.prototype.isLoadedJs = isLoadedJs;
Vue.prototype.$errorScroll = errorScroll;
Vue.prototype.$filterOption = filterOption;
Vue.prototype.$isEmpty = isEmpty;
Vue.prototype.$getRootColor = getRootColor;

SSO.init(() => {
  main();
});

function main () {
  new Vue({
    router,
    store,
    mounted () {
      store.commit('SET_SIDEBAR_TYPE', Vue.ls.get(SIDEBAR_TYPE, true));
      store.commit('TOGGLE_THEME', Vue.ls.get(DEFAULT_THEME, config.navTheme));
      store.commit('TOGGLE_LAYOUT_MODE', Vue.ls.get(DEFAULT_LAYOUT_MODE, config.layout));
      store.commit('TOGGLE_FIXED_HEADER', Vue.ls.get(DEFAULT_FIXED_HEADER, config.fixedHeader));
      store.commit('TOGGLE_FIXED_SIDERBAR', Vue.ls.get(DEFAULT_FIXED_SIDEMENU, config.fixSiderbar));
      store.commit('TOGGLE_CONTENT_WIDTH', Vue.ls.get(DEFAULT_CONTENT_WIDTH_TYPE, config.contentWidth));
      store.commit('TOGGLE_FIXED_HEADER_HIDDEN', Vue.ls.get(DEFAULT_FIXED_HEADER_HIDDEN, config.autoHideHeader));
      store.commit('TOGGLE_WEAK', Vue.ls.get(DEFAULT_COLOR_WEAK, config.colorWeak));
      store.commit('TOGGLE_COLOR', Vue.ls.get(DEFAULT_COLOR, config.primaryColor));
      store.commit('SET_TOKEN', Vue.ls.get(ACCESS_TOKEN));
      store.commit('SET_MULTI_PAGE', Vue.ls.get(DEFAULT_MULTI_PAGE, config.multipage));
    },
    render: h => h(App)
  }).$mount('#app');
}
