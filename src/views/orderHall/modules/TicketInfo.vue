<template>
  <div class="ticket-info" v-if="ticketInfoShow">
    <!-- 详情、审批、验收 -->
    <template v-if="['3','4', '8'].includes(type)">
      <TicketInfoDetail :type="type" :baseForm="formData"/>
    </template>
  </div>
</template>

<script>
import PropTypes from 'ant-design-vue/es/_util/vue-types';
import TicketInfoDetail from './TicketInfoDetail';
export default {
  name: 'TicketInfo',
  components: { TicketInfoDetail },
  props: {
    ticketRequired: PropTypes.bool.def(false),
    formData: PropTypes.object,
    dictMap: PropTypes.object,
    type: PropTypes.string,
    isDisabled: PropTypes.bool
  },
  data () {
    return {};
  },
  methods: {

  },
  computed: {
    ticketInfoShow () {
      const { id, taskType, workSubclass } = this.formData;
      console.log(taskType, workSubclass);
      // 站荣站貌不展示
      const notShowList = ['18', '16', '14', '17'];
      if (taskType === '3' && ['14'].includes(workSubclass)) {
        return false;
      } else if (taskType === '4' && ['19', '20'].includes(workSubclass)) {
        return false;
      } else if (notShowList.includes(taskType)) {
        return false;
      }
      return !!id;
    }
  }
};
</script>

<style lang="less" scoped>
.ticket-info {
  .link-text {
    display: flex;
    align-items: center;
    cursor: pointer;
    height: 32px;
    line-height: 32px;
    margin-top: 4px;
    .ticket-label{
      margin: 0 4px;
    }
  }
  .resolve-color {
    color: #1366ec;
  }
  .end-color {
    color: #2ba471;
  }
  .custom-select-col {
    :deep(.ant-form-item-control) {
      line-height: normal;
    }
  }
}
</style>
