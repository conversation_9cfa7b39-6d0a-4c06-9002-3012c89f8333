<!-- 左侧的电站和测点选择 -->
<template>
  <div class='flex-between width-height-100'>
    <div class='ps-area' v-show='!expend'>
      <div class='top-search flex-between'>
        <!-- 电站顶部区域 -->
        <div class='ps-search' >
          <a-select v-model="selectedPsFromSearch" show-search class='search-select'
           :default-active-first-option='false' @search='psSearch' :showArrow="false"
            @change='changePs' :filter-option='false' placeholder="输入电站名称搜索" id="insight-tool-guide-step2">
            <!-- <a-icon slot='suffixIcon' type='search' class='search-icon-fake' /> -->
            <a-select-option v-for='d in filterPsList' :key='d.value' :title="d.label">
              {{ d.label }}
            </a-select-option>
          </a-select>
          <a-icon slot='suffixIcon' type='search' class='search-icon'/>
        </div>
        <!-- 筛选电站图标 -->
        <svg-icon class='filter-icon solar-eye-hover-primary cursor-pointer m-l-16' @click='doFilter'
          iconClass='insight-filter' id="insight-tool-guide-step1"></svg-icon>
      </div>
      <div class='title-area m-t-16 flex-between'>
        <a-tooltip placement='right' @mouseenter="showPsaTooltip = true" @mouseleave="showPsaTooltip = false"
          :visible="showPsaTooltip" overlayClassName="insight-warning-tooltip">
          <template slot='title'>
            <span>请选择电站</span>
          </template>
          <span class='text-blod'>电站</span>
        </a-tooltip>
        <span class='solareye-color-primary cursor-pointer' @click='doReset'>重置</span>
      </div>
      <!-- 电站分析电站列表 -->
      <div class='ps-list m-t-16' v-if='tabType == 1'>
        <div class='flex-start m-b-16' v-for='(item, index) in psList' :key='index' @click='checkPs(item)'>
          <a-checkbox style="white-space: nowrap" v-if='isPsMultiple' class='m-r-8' :checked='chosenPsList.includes(item.value)' />
          <p style="white-space: nowrap" class='m-b-0 solar-eye-hover-primary cursor-pointer'
            :class='{ "solareye-color-primary text-blod": chosenPsList.includes(item.value) }' :key='index'>
            {{ item.label }}
          </p>
        </div>
      </div>
      <!-- 设备分析电站/设备/筛选区域 -->
      <div class='virtual-area m-t-16' v-if='tabType == 2'>
         <PsAndPointVirtualList :chosenPsList="chosenPsList" :fromPoint="fromPoint || fromRoute" :defaultCheckedKeys="defaultCheckedKeys"
          ref="psAndPointVirtualList" :psList="psList" @changeFilterDevice="changeFilterDevice" @changeCheckedKeys="changeCheckedKeys"/>
      </div>
    </div>
    <!-- 筛选部门/组织后的分页区域 -->

     <a-pagination v-if="paginationShow" class="insight-page-pagination" simple :pageSize="pageParams.size" :current="pageParams.curPage"
       :total="total" @change="handleSizeChange"/>
    <!-- tab切换区域 -->
    <div class='tab-area'>
      <!-- 电站分析增加按钮权限 -->
      <div v-if="showHandle('insightTool:indicator')" class='tab-item cursor-pointer' :class='{ "tab-item-active": tabType == 1 }'
        @click='changeTab(1)' id="insight-tool-guide-step3">
        电站分析
      </div>
      <div class='tab-item cursor-pointer' :class='{ "tab-item-active": tabType == 2 }' @click='changeTab(2)' id="insight-tool-guide-step4">
        设备分析
      </div>
    </div>
    <!-- 电站指标区域 -->
    <div class='indicator-area' v-if='tabType == 1'>
      <div v-for='(item, index) in indicators' :key='index' class="m-b-32">
        <p class='text-blod align-center'>
           <svg-icon class='filter-icon m-r-4' :iconClass='item.icon'/>
          {{ item.title }}
        </p>
        <p v-for='(el, subIndex) in item.items' :key='subIndex'>
          <a-tooltip placement='right'>
            <template slot='title'>
              <span>{{ el.tip }}</span>
            </template>
            <span @click='changeIndicator(el)' class='cursor-pointer'
              :class='{ "solareye-color-primary text-blod": el.value == chosenIndicator.value }' :key='subIndex'>
              {{el.title}}
            </span>
          </a-tooltip>
        </p>
      </div>
    </div>
    <!-- 自定义测点区域 -->
    <div class='point-area' v-if='tabType == 2'>
       <CustomPointArea :isPoint="isPoint" :fromPoint="fromPoint || fromRoute"  :pointExpandKeysList="pointExpandKeysList" ref="customPointArea"
        :bindCheckedList="bindCheckedList" :psList="psList" @selectedPoint="selectedPoint" @echoInfo="echoInfo" @pointsDeviceList="pointsDeviceList"/>
    </div>
    <!-- 收起/展开电站区域 -->
    <div class='expend-icon-area'>
      <svg-icon class='expend-icon cursor-pointer' @click='doExpend' :iconClass='expend ? "insight-fold" : "insight-expend"'/>
    </div>
    <!-- 筛选电站modal -->
    <a-modal title='筛选条件' :visible='formModalVisible' :confirm-loading='formModalLoading' @ok='formModalOk' @cancel='formModalCancel'>
      <role-tree-select depLabel='所属组织' psaLabel='履约项目' :hasDepDefaultValue='false' :depTreeAllowClear='true'
        isColumn @change='changeDept' ref='roleTree'/>
    </a-modal>
  </div>
</template>

<script>
import { indicatorsSinglePs, indicatorsMultiplePs } from '../data';
import { searchPowerStation } from '@/api/health/healthapi';
import { insightToolsGetDefaultPs, getIndexTypeApi, getIndexPsListApi } from '@/api/dataCenter';
import PsAndPointVirtualList from './PsAndPointVirtualList';
import CustomPointArea from './CustomPointArea';
import { clone, debounce } from 'xe-utils';
import { uniqBy } from 'lodash';
import moment from 'moment';
export default {
  name: 'LeftStationAndPointChoose',
  components: { PsAndPointVirtualList, CustomPointArea },
  inject: ['updateLoading', 'updateUnitFromCommonPoint', 'updateLeftContentLoading'],
  props: {
    tabType: {
      type: Number,
      default: 1
    }
  },
  data () {
    return {
      isPoint: false, // 是否带测点 从报文页面跳转特殊处理
      fromPoint: false, // 来自常用测点
      timer: null,
      formModalVisible: false, // 带表单的model visible
      fromRoute: false, // 是否来自诊断
      defaultCheckedKeys: [], // 默认回显树的节点
      pointExpandKeysList: [], // 测点默认展开
      formModalLoading: false,
      expend: false, // 是否折叠电站区域
      formModalType: '', // 表单弹窗类型
      indicators: indicatorsSinglePs, // 电站的指标
      indicatorsMultiplePs: indicatorsMultiplePs, // 多电站的指标
      filterPsList: [], // 模糊匹配的电站列表
      psList: [], // 电站列表
      chosenPsList: [], // 选中的电站列表
      chosenIndicator: {
        title: '发电趋势',
        tip: '通过整站辐照数据和交流功率归一化的变化趋势，帮助发现整站功率是否下降',
        value: 101,
        dates: [1],
        default: 1,
        defaultDate: moment().format('YYYY-MM-DD')
      }, // 选中的指标
      depCodes: null, // 部门树code
      treePsaIds: null, // 履约项目集合字段
      defaultPsList: [], // 初始化进来的默认电站列表
      selectedPsFromSearch: undefined, // 从搜索结果中选中的电站
      showPsaTooltip: false, // 请选择电站的动效提示
      pageParams: {
        curPage: 1,
        size: 15
      },
      total: 0,
      recordPsList: [] // 记录所有的电站
    };
  },
  created () {
    this.getDefaultPs();
    // 如果没有电站分析权限，跳转至设备分析
    if (!this.showHandle('insightTool:indicator')) this.changeTab(2);
  },
  computed: {
    // 是否是多选
    isPsMultiple () {
      return this.indicatorsMultiplePs.map(item => item.value).includes(this.chosenIndicator.value);
    },
    paginationShow () {
      return this.formModalType === 'psFilter' && this.total && !this.expend;
    }
  },
  watch: {
    psList: {
      immediate: true,
      handler: function (newVal, oldVal) {
        const arr = clone(this.recordPsList, true);
        arr.push(...(oldVal || []));
        arr.push(...(newVal || []));
        this.recordPsList = Object.freeze(uniqBy(arr, 'value'));
      }
    }
  },
  beforeDestroy () {
    clearTimeout(this.timer);
  },
  methods: {
    // 切换电站 默认展开电站
    changeCheckedKeys (value) {
      this.defaultCheckedKeys = [value];
    },
    // 选中设备列表
    changeFilterDevice (value) {
      if (value.changeRouter) {
        this.fromRoute = false;
      }
      this.$emit('changeFilterDevice', { value: value.devices, fromRoute: this.fromRoute });
    },
    // 选中测点列表
    selectedPoint (value) {
      this.bindCheckedList = value.selectedPointsData;
      if (value.changeRouter) {
        this.fromRoute = false;
      }
      this.$emit('selectedPoint', { value: value.selectedPointsData, fromRoute: this.fromRoute });
    },
    // 测点类型与值集合
    pointsDeviceList (value) {
      this.$emit('pointsDeviceList', value);
    },
    // 获取默认电站
    async getDefaultPs () {
      this.fromPoint = false;
      if (window.comefromAlarmCenter) {
        this.dealRouterEvent();
      } else {
        this.fromRoute = false;
        this.updateLoading(true);
        try {
          let res = await insightToolsGetDefaultPs();
          this.updateLoading(false);
          if (res.result_data && res.result_data.ps_id) {
            this.psList = [{
              value: res.result_data.ps_id,
              label: res.result_data.ps_name,
              isTree: true
            }];
            this.chosenPsList = [res.result_data.ps_id];
            this.defaultPsList = clone(this.psList, true);
            // 查询默认电站是否含有方正逆变器
            await this.getIndexType();
            // 默认查询图表
            this.changeLeftFilter();
          }
        } catch (err) {
          this.updateLoading(false);
          console.log(err);
        }
      }
    },
    // 常用测点回显
    echoInfo (data) {
      this.fromRoute = false;
      if (data && data.length) {
        this.fromPoint = true;
        this.defaultCheckedKeys = [];
        this.bindCheckedList = [];
        this.pointExpandKeysList = [];
        // 存电站  设备
        if (data[0].psId) {
          let psListTemplt = [];
          data.forEach(item => {
            psListTemplt.push({
              value: item.psId,
              label: item.psName,
              isTree: true
            });
            this.defaultCheckedKeys = this.defaultCheckedKeys.concat(item.list.devices);
          });
          this.defaultCheckedKeys = this.defaultCheckedKeys.map(item => {
            // 类型5的特殊处理
            if (item.psKey.split('_')[1] == 5 && item.psId != item.psKey.split('_')[0]) {
              return item.psKey.split('_')[0] + ',' + item.psKey + ',' + item.psId;
            } else {
              return item.psId + ',' + item.psKey;
            }
          });
          this.psList = psListTemplt;
          this.chosenPsList = [this.psList[0].value];
        }
        this.bindCheckedList = data[0].list.points.map(item => item) || [];
        this.pointExpandKeysList = Array.from(new Set(data[0].list.types || []));
        this.timer = setTimeout(() => {
          this.changeTab(2);
          // 取最后两个更新Y轴坐标单位
          this.updateUnitFromCommonPoint(this.bindCheckedList);
        }, 50);
      }
    },
    dealRouterEvent () {
      if (this.$route.params.source == 1) {
        this.isPoint = true;
      }
      this.selectedPsFromSearch = undefined;
      this.filterPsList = [];
      this.depCodes = null;
      this.treePsaIds = null;
      if (this.$refs.roleTree) {
        this.$refs.roleTree.reset();
      }
      this.defaultCheckedKeys = [];
      this.bindCheckedList = [];
      this.pointExpandKeysList = [];
      this.psList = [{
        value: this.$route.params.psId,
        label: this.$route.params.psName,
        isTree: true
      }];
      this.chosenPsList = [this.$route.params.psId];
      // 同级引入设备处理
      let psId = this.$route.params.psId;
      this.defaultCheckedKeys = [];
      if (this.$route.params.list && this.$route.params.list.devices && this.$route.params.list.devices.length) {
        this.$route.params.list.devices.forEach(item => {
          // 同一电站下设备
          if (psId == item.psKey.split('_')[0]) {
            this.defaultCheckedKeys.push(psId + ',' + item.psKey);
          } else {
            // 类型为5的最后拼前面psId 防止多个设备挂载一样的环境监测仪
            this.defaultCheckedKeys.push(item.psKey.split('_')[0] + ',' + item.psKey + ',' + psId);
          }
        });
      }
      this.$route.params.list.devices.map(item => { return item.psKey.split('_'[0] != psId); });
      window.comefromAlarmCenter = false;
      this.fromRoute = true;
      this.timer = null;
      this.timer = setTimeout(() => {
        this.changeTab(2);
        this.$nextTick(() => {
          this.$refs.customPointArea.changeList('fromRouter');
          // 跳转过来 常用测点收起
          this.bindCheckedList = this.$route.params.list.points || [];
          this.pointExpandKeysList = Array.from(new Set(this.$route.params.list.types || []));
          // 取最后两个更新Y轴坐标单位
          this.updateUnitFromCommonPoint(this.bindCheckedList);
        });
      }, 50);
      // 处理时间
      let timeRange = this.$route.params.time;
      this.$emit('changeChosenRange', timeRange);
    },
    // 左侧筛选部分 电站选择/指标选择/测点选择 发生变化
    changeLeftFilter () {
      let params = {
        chosenPsList: this.chosenPsList,
        psNameList: this.chosenPsList.map(item => {
          let temp = this.recordPsList.find(el => el.value == item) || {};
          if (!temp.label) return;
          return temp.label;
        }),
        chosenIndicator: this.chosenIndicator,
        tabType: this.tabType,
        leftPsaList: this.psList, // 左侧所展示的电站
        fromPoint: this.fromPoint // 是否来自切换常用测点
      };
      this.$emit('changeLeftFilter', params);
    },
    // 电站实时搜索
    psSearch: debounce(function (text) {
      if (!this.$isEmpty(text)) this.getPsList(text, true);
    }, 500),
    async formModalOk () {
      this.isPoint = false;
      this.formModalVisible = false;
      // 重置精准搜索
      this.selectedPsFromSearch = undefined;
      this.filterPsList = [];
      // 置空已选中电站
      this.chosenPsList = [];
      // 重置分页参数
      this.pageParams = this.$options.data().pageParams;
      if (this.formModalType == 'psFilter') {
        this.updateLeftContentLoading(true);
        await this.getPsList();
        this.updateLeftContentLoading(false);
      }
    },
    // 左侧重置
    async doReset () {
      if (this.tabType == 1) {
        this.chosenPsList = this.defaultPsList.map(o => o.value);
        this.psList = this.defaultPsList;
        this.chosenIndicator = indicatorsSinglePs[0].items[0];
        this.filterPsList = [];
        this.selectedPsFromSearch = undefined;
        // 重置formModalType
        this.formModalType = '';
        // 重新查询电站指标
        await this.getIndexType();
        this.changeLeftFilter();
      } else {
        this.fromPoint = false;
        this.fromRoute = false;
        this.defaultCheckedKeys = [];
        this.timer = null;
        // 由于props值  fromPoint  fromRoute 慢于reset方法  所以加定时器
        this.timer = setTimeout(() => {
          this.$refs.psAndPointVirtualList.reset();
          this.$refs.customPointArea.reset();
        }, 50);
      }
    },
    // 电站搜索change事件
    async changePs (value, option) {
      // 切换电站 测点为空
      this.bindCheckedList = [];
      this.isPoint = false;
      let item = this.filterPsList.find(item => item.value == value);
      this.psList = [{ value, label: item.label }];
      this.chosenPsList = [value];
      this.selectedPsFromSearch = value;
      // 重置formModalType类型
      this.formModalType = '';
      // 查询指标是否含有方正逆变器
      await this.getIndexType();
      // 默认查询图表
      this.changeLeftFilter();
    },
    doExpend () {
      this.showPsaTooltip = false;
      this.expend = !this.expend;
      // 触发重新渲染图表和表格
      this.$emit('resizeChartOrTable');
    },
    checkPs: debounce(function (item) {
      // 多选选中/取消选中 单选则切换
      if (this.isPsMultiple) {
        if (this.chosenPsList.includes(item.value)) {
          let index = this.chosenPsList.findIndex(el => el == item.value);
          this.chosenPsList.splice(index, 1);
        } else {
          this.chosenPsList.push(item.value);
        }
        // 如果电站是多选，等效小时数的默认日期要变化
        if (this.chosenPsList.length > 1 && this.chosenIndicator.value == 103) {
          Object.assign(this.chosenIndicator, { dates: [1, 2, 3], default: 1, defaultDate: moment().subtract(1, 'days').format('YYYY-MM-DD') });
        } else {
          Object.assign(this.chosenIndicator, { dates: [2, 3, 4], default: 2, defaultDate: moment().format('YYYY-MM') });
        }
      } else {
        this.chosenPsList = [item.value];
      }
      this.changeLeftFilter();
    }, 500),
    changeIndicator: debounce(function (item) {
      // 多电站切换到其他指标单电站时，重置为默认电站
      if (this.chosenIndicator.value != item.value && item.value != 103 && this.chosenIndicator.value == 103) {
        this.chosenPsList = this.chosenPsList.length ? [this.chosenPsList[0]] : this.psList.length && [this.psList[0].value];
      }
      this.chosenIndicator = item;
      this.changeLeftFilter();
    }, 500),
    changeTab (type) {
      this.tabType = type;
      // 重新resize图表
      this.$emit('resizeChartOrTable');
      if (this.tabType == 1) {
        this.fromPoint = false;
        this.fromRoute = false;
      } else if (this.tabType == 2) {
        this.showPsaTooltip = false;
      }
      this.changeLeftFilter();
    },
    doFilter () {
      this.formModalType = 'psFilter';
      this.formModalVisible = true;
    },
    formModalCancel () {
      this.formModalVisible = false;
      this.formModalType = '';
    },
    changeDept (deptCode, psaIds) {
      this.depCodes = deptCode;
      this.treePsaIds = psaIds;
    },
    async getPsList (psName = '', isPreciseSeach = false) {
      // 重置常用测点
      if (this.tabType == 2) {
        // this.$refs.customPointArea.commonIndex = null;
        this.$refs.customPointArea.showPointTooltip = false;
      }
      this.fromPoint = false;
      this.fromRoute = false;
      let params = {
        depCodes: psName == '' ? this.depCodes : undefined,
        treePsaIds: psName == '' ? this.treePsaIds : undefined,
        psName,
        validFlag: '1',
        isCheckDataRole: '1',
        communicatStatusList: [1, 2, 3],
        curPage: psName == '' ? this.pageParams.curPage : 1,
        size: psName == '' ? this.pageParams.size : 10
      };
      this.defaultCheckedKeys = [];
      const serviceApi = isPreciseSeach ? getIndexPsListApi : searchPowerStation;
      let res = await serviceApi(params);
      let arr = res.result_data.pageList.map(item => {
        return {
          label: item.psName,
          value: item.psId,
          isStation: true
        };
      });

      if (psName == '') {
        // 如果是设备分析 默认展开第一个电站下的设备
        if (this.tabType == 2) this.chosenPsList = arr && arr.length ? [arr[0].value] : [];
        this.psList = arr;
        this.total = res.result_data.rowCount;
        // 筛选完该部门履约项目下所有电站，查询指标是否含有方正逆变器
        this.getIndexType();
      } else {
        this.filterPsList = arr;
      }
      // 触发树重新渲染
      if (this.tabType == 2) {
        this.fromPoint = false;
        this.fromRoute = false;
        // 更新左侧电站
        this.$emit('updateLeftPsaList', this.psList);
        // 重置单位
        this.updateUnitFromCommonPoint([]);
        this.$refs.psAndPointVirtualList.setTree();
      }
    },
    async getIndexType () {
      const psIdList = this.psList.map(item => item.value);
      const res = await getIndexTypeApi({ psIdList });
      const list = clone(indicatorsSinglePs, true);
      if (res.result_code == '1') {
        // 1是逆变器，17是方阵
        if (!res.result_data.includes('17')) {
          list.splice(1, 1);
        }
        if (!res.result_data.includes('1')) {
          list.splice(-1, 1);
        }
        this.indicators = list;
      }
      return this.indicators;
    },
    // 更新请选择电站的动效提示
    updateTooltipVisible (visible) {
      this.showPsaTooltip = visible;
    },
    async handleSizeChange (curPage, size) {
      Object.assign(this.pageParams, { curPage, size });
      this.updateLeftContentLoading(true);
      await this.getPsList();
      this.updateLeftContentLoading(false);
    }
  }
};
</script>

<style lang='less' scoped>
.ps-area {
  width: 267px;
  height: 100%;
  // overflow-x: auto;

  .ps-search {
    width: 231px;
    position: relative;

    .search-select {
      width: 100%;
      box-sizing: border-box;
      :deep(.ant-select-selection){
        padding-left: 16px;
      }
      :deep(.ant-select-selection__placeholder,.ant-select-selection-selected-value){
        padding-left: 3px;
      }
      :deep(.ant-select-selection-selected-value){
        padding-left: 3px;
      }
    }

    .search-icon {
      position: absolute;
      left: 10px;
      top: 10px;
    }

    .search-icon-fake {
      opacity: 0;
    }
  }
  .ps-list{
    height: calc(100% - 130px);
    width: 250px;
    overflow: auto;
  }
  .virtual-area{
    width: 250px;
  }
}

:deep(.search-label) {
  width: 80px !important;
}

.filter-icon {
  font-size: 20px;
}

.title-area {
  width: 100%;
}

.indicator-area {
  width: 186px;
  height: 100%;
  margin-left: 24px;
}

.point-area {
  width: 186px;
  height: 100%;
  margin-left: 24px;
}

.tab-area {
  width: 27px;
  height: 100%;
  border-right: 1px solid var(--zw-border-color--default);
  padding-top: 96px;
}

.tab-item {
  width: 26px;
  height: 104px;
  border-radius: 3px 0px 0px 3px;
  text-align: center;
  font-size: 14px;
  padding: 8px 6px;
  margin-bottom: 8px;
  background: var(--zw-table-header-bg-color--default);
  color: var(--zw-text-2-color--default);
}

.tab-item:hover {
  background: var(--zw-border-color--default);
}

.tab-item-active {
  background: var(--zw-card-light-bg-color--default) !important;
  color: var(--zw-primary-color--default);
}

.expend-icon-area {
  position: absolute;
  left: 0px;
  bottom: 0px;

  .expend-icon {
    font-size: 24px;
    color: var(--zw-primary-color--default);
  }

  .expend-icon:hover {
    color: var(--zw-primary-color--hover);
    cursor: pointer;
  }
}
.insight-page-pagination{
  position: absolute;
  left: 40px;
  bottom: 0px;
  width: auto;
  text-align: left;
  padding: 0;
}
</style>
<style lang="less">
.animation-jelly{
 animation: jelly 0.5s;
}
@keyframes jelly {
  0%,
  100% {
    transform: scale(1, 1);
  }
  25% {
    transform: scale(0.9, 1.1);
  }
  50% {
    transform: scale(1.1, 0.9);
  }
  75% {
    transform: scale(0.95, 1.05);
  }
}
.insight-dropdown-arrow{
  &::before {
      border-bottom: none !important;
    }

    &::after {
      border-bottom: none !important;
    }
    box-shadow: none !important;
    border: none !important;
}
</style>
