<template>
  <table class="table-item" border="1">
    <thead>
      <tr>
        <th :colspan="isHaveSubTitle ? 2 : 1">试验项目/测点</th>
        <td :colspan="1" v-for="(col, index) in colList" :key="index">{{ col.rowTitle }}</td>
      </tr>
    </thead>
    <tbody>
      <tr v-for="(row, index) in rowList" :key="index">
        <td :width="tdWidth" v-if="isHaveSubTitle ? row.childSort : true" :rowspan="rowSpanFn(row)">
          {{ row.rowTitle }}
        </td>
        <td :width="tdWidth" v-if="isHaveSubTitle">{{ row.rowSubTitle }}</td>
        <td :width="tdWidth" v-for="(col, index) in colList" :key="index"></td>
      </tr>
    </tbody>
    <tfoot v-if='projectTableRemark'>
      <tr>
        <td :colspan="colList.length + rowList.length + (isHaveSubTitle ? 1 : 0)">
          <span v-html='projectTableRemark'></span>
        </td>
      </tr>
    </tfoot>
  </table>
</template>

<script>
export default {
  name: 'TableItem',
  props: {
    colList: {
      type: Array
    },
    rowList: {
      type: Array
    },
    isHaveSubTitle: {
      type: Boolean
    },
    projectTableRemark: {
      type: [String, undefined, null]
    }
  },
  methods: {
    rowSpanFn (row) {
      if (!row.childSort) {
        return 1;
      }
      const len = row.childSort.split(',').length;
      return len;
    }
  },
  computed: {
    tdWidth () {
      return 100 / (this.colList.length + (this.isHaveSubTitle ? 2 : 1)) + '%';
    }
  }
};
</script>

<style lang="less" scoped>
.table-item {
  width: 100%;
  margin-bottom: 16px;
  background: #fff;

  thead,
  tbody {
    tr td,
    tr th {
      text-align: left;
      height: 36px;
      padding: 0 12px;
    }
  }
  tfoot tr td {
    height: 36px;
    box-sizing: border-box;
    padding: 16px;
    white-space: pre-line;
  }
}

:root[data-theme='dark']{
  .table-item{
    background: transparent;
  }
}
</style>
