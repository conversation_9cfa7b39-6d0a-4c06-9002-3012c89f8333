<template>
  <div class="screen-page">
    <a-spin :spinning="loading">
      <div class="solar_eye_logo" @click="toNewPage()">
        <div class="left-icon-group">
          <div class="left-icon" style="border-color: rgba(255,255,255,0.6);"></div>
          <div class="left-icon" style="border-color: rgba(255,255,255,0.4);"></div>
          <div class="left-icon" style="border-color: rgba(255,255,255,0.2);"></div>
        </div>
        <img src="../../assets/images/screen/custom_logo_s.png" />
         <img src="../../assets/images/screen/custom_trend.png" style="margin-left:60px" />
      </div>

      <div id="timeModel" class="right-time-model">
        <span class="now-time">{{nowTime}}</span>
        <br />
        <span class="now-day">{{nowDay}}</span>
      </div>
      <div class="top-icon"></div>
      <!-- 总发电量、装机容量 -->
      <div class="all-elec-power">
        <div class="all-elec">
          <div class="data">{{allElec.value}}</div>
          <div class="unit">年发电量&nbsp;/&nbsp;{{allElec.unit}}</div>
        </div>
        <div class="all-power">
          <div class="data">{{allPower.value}}</div>
          <div class="unit">运维容量&nbsp;/&nbsp;{{allPower.unit}}</div>
        </div>
      </div>
      <div class="amap-model">
        <!-- 地图 -->
        <div class="amap-div custom_div" id="screenAmap"></div>
        <!-- 搜索模块以及氮化物等统计数 -->
        <div class="search-model">
          <a-select v-model="psId" class="screen-select" dropdownClassName="screen-dropdown" show-search
            style="width: 13vw" :default-active-first-option="false" :allowClear="true" :showArrow="false"
            @select="onSelect" :filter-option="false" :not-found-content="null" @change="onChange" @search="onSearch"
            placeholder="请输入关键字搜索">
            <div slot="dropdownRender" slot-scope="menu">
              <v-nodes :vnodes="menu" />
              <a-divider v-show="psaItems.length" style="margin: 4px 0;" />
              <div v-show="psaItems.length" style="cursor: pointer;text-align: right; padding-bottom: 4px;"
                @mousedown="e => e.preventDefault()">
                <a-button :loading="loadingMore" :disabled="loadingMore" @click="onLoadMore">loading more</a-button>
              </div>
            </div>
            <a-select-option v-for="psa in refreshList" :key="psa.psId" :value="psa.psId" :title="psa.psName">
              {{ psa.psName }}
            </a-select-option>
          </a-select>
          <br />
          <card-cycle ref="environmentalCycle" :isNeedRequest="false" />
        </div>
        <!-- 全部数据总览 -->
        <div class="status_desc">
          <span class="ps_status type-builded"></span>已建<span class="ps_status type-building"></span>在建<span class="ps_status type-unbuild"></span>拟建
        </div>
        <div class="all-preview" v-bind:style="{'visibility': !show ? 'visible' : 'hidden'}">
          <a-row :gutter="24">
            <!-- 日发电量 -->
            <a-col :sm="24" :md="12" :xl="12" style="position: relative;">
              <div class="all-preview-com">
                <line-model :type="0" ref="dayLine" />
              </div>
            </a-col>
            <!-- 计划完成率 -->
            <a-col :sm="24" :md="12" :xl="12" style="position: relative;">
              <div class="all-preview-com">
                <bar-model ref="planRateBar" title="发电计划完成率" legendName="已完成" />
              </div>
            </a-col>
          </a-row>
          <a-row :gutter="24">
            <!-- 月发电量 -->
            <a-col :sm="24" :md="12" :xl="12" style="position: relative;">
              <div class="all-preview-com">
                <line-model :type="1" ref="monthLine" />
              </div>
            </a-col>
            <!-- 等效小时数 -->
            <a-col :sm="24" :md="12" :xl="12" style="position: relative;">
              <div class="all-preview-com">
                <other-bar :type="1" ref="otherBarModel" />

              </div>
            </a-col>
          </a-row>
          <a-row :gutter="24" style="margin-bottom: 0;">
            <!-- 年发电量 -->
            <a-col :sm="24" :md="12" :xl="12" style="position: relative;">
              <div class="all-preview-com">
                <line-model :type="2" ref="yearLine" @setTotal="setTotal" />
              </div>
            </a-col>
            <!-- 电站运行情况 -->
            <a-col :sm="24" :md="12" :xl="12" style="position: relative;">
              <div class="all-preview-com">
                <pie-model title="电站运行情况" type="1" ref="psaRunPreview" />
              </div>
            </a-col>
          </a-row>
        </div>
        <!-- 单站详情页 -->
        <psa-detail @close="closeDetail" class="psa-detail-info" v-bind:style="{'visibility':(show ? 'visible' : 'hidden')}" ref="psaDetail"/>
      </div>
    </a-spin>
  </div>
</template>

<script>
import echarts from '@/utils/enquireEchart';
import moment from 'moment';
import otherBar from './modules/otherBar';
import barModel from './modules/barModel';
import pieModel from './modules/pieModel';
import cardCycle from './modules/cardCycle';
import lineModel from './modules/lineModel';
import psaDetail from './modules/psaDetail';
import { getAllStationInfo, getSumScale, getTenant22Data } from '@/api/screen/screen';
require('echarts-extension-amap');
const charge_gif = require('../../assets/images/screen/charge.gif');
export default {
  components: {
    cardCycle,
    pieModel,
    lineModel,
    barModel,
    otherBar,
    psaDetail,
    VNodes: {
      functional: true,
      render: (h, ctx) => ctx.props.vnodes
    }
  },
  computed: {
    firstMenu () {
      return this.$store.state.user.firstMenu;
    }
  },
  data () {
    return {
      psId: undefined,
      loading: false,
      allElec: {
        value: 0,
        unit: '万kWh'
      },
      allPower: {
        value: 0,
        unit: '万kW'
      },
      // 单站详情参数
      show: false,
      nowTime: '',
      timeInterval: null,
      nowDay: '',
      psaItems: [],
      refreshList: [],
      loadingMore: false,
      mapText: null,
      allPsa: [],
      infoWindow: null,
      times: null,
      allInterval: null,
      tanant22List: [],
      addr: ''
    };
  },
  mounted () {
    let self = this;
    self.nowTime = moment().format('HH:mm:ss');
    self.nowDay = moment().format('YYYY-MM-DD');
    self.timeInterval = setInterval(function () {
      self.nowTime = moment().format('HH:mm:ss');
      self.nowDay = moment().format('YYYY-MM-DD');
    }, 1000);
    // 装机容量
    self.getScale();
    // 定时任务，15分钟刷新数据
    self.intervalWork();
    self.allInterval = window.setInterval(function () {
      self.intervalWork();
    }, 1000 * 60 * 15);
    // 加载地图
    self.getAllRoute();
    $('.all-elec-power').mouseenter(function () {
      window.setTimeout(function () {
        self.isBackAll();
      }, 500);
    });
    window.infoClick = function (psId) {
      let psa = self.allPsa.find(item => item.psId == psId);
      psa && self.showMarker(psa);
    };
  },
  methods: {
    /*
        定时任务，15分钟刷新数据
      */
    intervalWork () {
      let self = this;
      self.$refs.dayLine && self.$refs.dayLine.getElec(echarts);
      self.getTenant22Data();
      self.$refs.otherBarModel.hoursRank(echarts);
      self.$refs.psaRunPreview.init(echarts);
      self.getScale();
    },
    /*
        租户22的查询
      */
    getTenant22Data () {
      let self = this;
      getTenant22Data({}).then(res => {
        let dataObj = res.result_data;
        let yearSumElec = dataObj.yearSumElec;
        self.setTotal(yearSumElec.totalElec);
        self.$refs.monthLine.initLineBar(echarts, dataObj.monthSumElec.x, [dataObj.monthSumElec.data.value], [ dataObj.monthSumElec.data.unit ]);
        self.$refs.yearLine.initLineBar(echarts, yearSumElec.x, [yearSumElec.yearElec.value], [yearSumElec.yearElec.unit]);
        self.$refs.planRateBar.initLineBar(echarts, yearSumElec.x, yearSumElec.planRateData.value, yearSumElec.planRateData.unit);
        self.$refs.environmentalCycle.getEnvironmental(dataObj.environmentalData);
      });
    },
    /*
        总装机容量
      */
    getScale () {
      getSumScale({}).then(res => {
        this.allPower = res.result_data;
      }).catch(() => {
        Object.assign(this.allPower, this.$options.data().allPower);
      });
    },
    // 获取路径
    getAllRoute () {
      let _this = this;
      _this.loading = true;
      _this.initMap();
      Promise.all([_this.getAllStationNumberPromise()]).then(() => {
        _this.drawPsPoints();
      }).catch(() => {
        _this.loading = false;
      });
    },
    /*
        设置不同形式的聚合点
      */
    setClusterMarkerContent (dataItems) {
      let count = this.allPsa.length;
      let div = document.createElement('div');
      div.className = 'render-cluster-marker';
      let size = Math.round(30 + Math.pow(dataItems.length / count, 1 / 5) * 20);
      div.style.width = div.style.height = size + 'px';
      div.style.borderRadius = size / 2 + 'px';
      div.innerHTML = dataItems.length;
      div.style.lineHeight = size + 'px';
      return div;
    },
    // 获取电站数据 接口要改估计
    getAllStationNumberPromise () {
      this.allPsa = [];
      return new Promise((resolve, reject) => {
        getAllStationInfo({}).then(res => {
          this.resetSubData(res);
          resolve();
        }).catch(err => {
          reject(err);
        });
      });
    },
    /*
        初始化静态地图及地图相关
      */
    initMap () {
      let _this = this;
      window.aMap = new AMap.Map('screenAmap', {
        resizeEnable: true,
        center: [119.783167, 32.400000],
        zooms: [7.5, 18], // 设置地图级别范围
        zoom: 7.5, // 地图视图缩放级别
        pitch: 0, // 地图俯仰角度，有效范围 0 度- 83 度
        viewMode: '3D',
        rotateEnable: true,
        pitchEnable: true,
        mapStyle: 'amap://styles/grey',
        skyColor: '#1ff0f0',
        labelStyle: {
          color: 'red'
        }
      });
      this.hollow();
      this.addStroke(); // 安徽省添加描边
      window.aMap.on('complete', function () {
        _this.loading = false;
        $('#screenAmap').css({
          'visibility': 'visible'
        });
      });
      window.aMap.on('zoomend', _this.zoomEnd);
    },
    zoomEnd () {
      let zoom = window.aMap.getZoom();
      if (zoom <= 12) {
        this.clearText();
      }
    },
    getAllRings (feature) {
      var coords = feature.geometry.coordinates;
      var rings = [];
      for (var i = 0, len = coords.length; i < len; i++) {
        rings.push(coords[i][0]);
      }
      return rings;
    },
    getLongestRing (feature) {
      var rings = this.getAllRings(feature);
      rings.sort(function (a, b) {
        return b.length - a.length;
      });
      return rings[0];
    },
    addStroke () {
      let district = new AMap.DistrictSearch({
        subdistrict: 0,
        extensions: 'all',
        level: 'province'
      });
      district.search('安徽省', function (status, result) {
        var bounds = result.districtList[0]['boundaries'];
        var mask = [];
        for (var i = 0; i < bounds.length; i++) {
          mask.push([bounds[i]]);
        }
        // 添加描边
        for (let i = 0; i < bounds.length; i++) {
          // eslint-disable-next-line no-unused-vars
          var polyline = new AMap.Polyline({
            bubble: true,
            lineJoin: 'round',
            path: bounds[i],
            strokeColor: '#3078AC',
            fillColor: 'white', // 填充色
            fillOpacity: 1, // 填充透明度
            strokeWeight: 2,
            map: window.aMap
          });
        }
      });
    },
    // 镂空地图
    hollow () {
      let _this = this;
      AMapUI.load(['ui/geo/DistrictExplorer'], function (DistrictExplorer) {
        let districtExplorer = new DistrictExplorer({
          map: window.aMap
        });
        var provCodes = [340000];
        var cityCodes = [

        ];
        districtExplorer.loadMultiAreaNodes(
          // 只需加载全国和市，全国的节点包含省级
          [100000].concat(cityCodes),
          (error, areaNodes) => {
            if (error) return;
            let countryNode = areaNodes[0];
            let cityNodes = areaNodes.slice(1);
            let path = [];
            // 首先放置背景区域，这里是大陆的边界
            path.push(_this.getLongestRing(countryNode.getParentFeature()));
            for (let i = 0, len = provCodes.length; i < len; i++) {
              // 逐个放置需要镂空的省级区域
              path.push.apply(path, _this.getAllRings(countryNode.getSubFeatureByAdcode(provCodes[i])));
            }
            for (let i = 0, len = cityNodes.length; i < len; i++) {
              // 逐个放置需要镂空的市级区域
              path.push.apply(path, _this.getAllRings(cityNodes[i].getParentFeature()));
            }
            // 绘制带环多边形
            // https://lbs.amap.com/api/javascript-api/reference/overlay#Polygon
            // eslint-disable-next-line no-unused-vars
            let polygon = new AMap.Polygon({
              bubble: true,
              lineJoin: 'round',
              strokeColor: '#0a141c', // 线颜色
              strokeOpacity: 0, // 线透明度
              strokeWeight: 0, // 线宽
              fillColor: '#0a141c', // 填充色
              fillOpacity: 0.7, // 填充透明度
              map: window.aMap,
              path: path
            });
          });
      });
    },
    toNewPage () {
      this.$router.push({
        path: this.firstMenu.path
      }).catch(() => {
        console.log('登录跳转首页出错,这个错误从哪里来的');
      });
    },
    // 年发电量统计
    setTotal (total) {
      this.allElec = total;
    },
    // 按区域随机更改电站类型
    resetSubData (res) {
      let data = (res.result_code == '1' ? res.result_data : []);
      if (!data || !data.length) {
        return;
      }
      data.map(item => {
        // eslint-disable-next-line no-return-assign
        return item.type = 2;
      });
      this.allPsa = Object.freeze(data);
    },
    container2lnglat (obj) { // 根据容器坐标获取经纬度
      if (!obj) return;
      var pixel = new AMap.Pixel(obj.x, obj.y);
      var lnglat = window.aMap.containerToLngLat(pixel);
      return lnglat;
    },
    /*
        电站渲染
      */
    drawPsPoints () {
      let _this = this;
      let markers = this.setMarkers();
      // 自定义聚合图标
      let count = markers.length;
      let _renderClusterMarker = function (context) {
        let clusterMarks = context.markers;
        let types = _this.getPsStatus(clusterMarks);
        let div = document.createElement('div');
        div.className = 'render-cluster-marker';
        let size = Math.round(30 + Math.pow(context.count / count, 1 / 5) * 20);
        div.style.width = div.style.height = size + 'px';
        div.style.borderRadius = size / 2 + 'px';
        div.innerHTML = context.count;
        div.style.lineHeight = size + 'px';
        context.marker.setOffset(new AMap.Pixel(-size / 2, -size / 2));
        context.marker.setContent(div);
        div.addEventListener('mousemove', (cluster) => {
          context.marker.setLabel({
            direction: 'top',
            content: _this.getLabel(types)
          });
        });
        div.addEventListener('mouseout', () => {
          context.marker.setLabel({

          });
        });
      };
        // 聚合
      window.aMap.plugin(['AMap.MarkerClusterer'], function () {
        // 自定义-集合的点击事件（更改放大比例）
        let markerClusterer = new AMap.MarkerClusterer(window.aMap, markers, {
          gridSize: 80,
          renderClusterMarker: _renderClusterMarker,
          zoomOnClick: false
        });
        markerClusterer.on('click', function (cluster) {
          _this.clustererClick(cluster);
        });
      });
    },
    getPsStatus (markers) {
      let pending = []; let building = []; let builded = [];
      markers.forEach(item => { // psaStatus 1在建，2已建 4拟建
        let psaStatus = item.getExtData() && item.getExtData().psaStatus;
        if (psaStatus == 4) {
          pending.push(item.validFlag);
        }
        if (psaStatus == 1) {
          building.push(item.validFlag);
        }
        if (psaStatus == 2) {
          builded.push(item.validFlag);
        }
      });
      return [builded.length, building.length, pending.length];
    },
    // 气泡悬浮效果
    getLabel (types, name) {
      let html = '';
      html += '<div class="effect-scatter">';
      // 电站类型统计
      html += '<div class="effect-scatter-center">';
      html += '<div class="type type-builded"></div>';
      html += `<div class="psa-num">${types[0]}&nbsp;&nbsp;个</div>`;
      html += '<div class="type type-building"></div>';
      html += `<div class="psa-num">${types[1]}&nbsp;&nbsp;个</div>`;
      html += '<div class="type type-unbuild"></div>';
      html += `<div class="psa-num last-data">${types[2]}&nbsp;&nbsp;个</div>`;
      html += '</div>';
      html += '</div>';
      return html;
    },
    /*
        聚合点击事件
      */
    clustererClick (cluster) {
      let _this = this;
      let { infoWindow } = this;
      let fn = () => {
        _this.clearText();
        let zoom = window.aMap.getZoom();
        if (zoom > 17) {
          if (!infoWindow) {
            _this.infoWindow = new AMap.InfoWindow({
              isCustom: true,
              autoMove: true,
              anchor: 'middle-left',
              closeWhenClickMap: true,
              offset: new AMap.Pixel(20, 0)
            });
          }
          _this.infoWindow.setContent(_this.getInfoWindowContent(cluster));
          _this.infoWindow.open(window.aMap, [cluster.lnglat.lng, cluster.lnglat.lat]);
        } else {
          window.aMap.setZoomAndCenter(zoom + 2, [cluster.lnglat.lng, cluster.lnglat.lat]);
        }
      };
      _this.debounce(fn);
    },
    /*
        获取InfoWindow的content
      */
    getInfoWindowContent (cluster) {
      let content = '<div class="info-window-content">';
      let cluster_psa = cluster.markers.map(item => {
        return item.De.extData;
      });
      cluster_psa.forEach(psa => {
        if (psa) {
          content += `<div class="psa-name" onclick="infoClick(${psa.psId})">${psa.psName}</div>`;
        }
      });
      content += '</div>';
      return content;
    },
    // 设置电站坐标点
    setMarkers () {
      let _this = this;
      let allPsa = this.allPsa;
      let markers = allPsa.map(item => {
        let marker = new AMap.Marker({
          clickable: true,
          title: item.psName,
          content: _this.getContent(item),
          position: [item.lon, item.lat], // 位置
          extData: item,
          map: window.aMap
        });
        marker.setExtData(item);
        // 坐标点-电站点击事件(validFlag == '1'正常状态)
        if (item.validFlag == '1' && item.type != '0' && item.type != '1') {
          marker.on('click', function (e) {
            _this.showMarker(item);
          });
        }
        return marker;
      });
      return markers;
    },
    /*
        marker 的 content
      */
    getContent (psa) {
      let content = '';
      // 未投入使用的电站置灰
      if (psa.validFlag == '1' && psa.type == '2') {
        content = '<div class="station-com">';
      } else {
        content = '<div class="station-com station-no">';
      }
      // 风力：0，储能：1， 光伏：其他
      if (psa.type == 0) {
        content += '<div class="wind"></div>';
      } else if (psa.type == 1) {
        content += '<img class="charge-pv-gif" src="' + charge_gif + '"/>';
      } else {
        let className = psa.psaStatus == 2 ? '' : (psa.psaStatus === 1 ? 'type-building-no' : 'type-unbuild-no'); // 1在建，2已建 4拟建
        content += `<div class='pv ${className}'>`;
        content += '<div class="pv-img"></div>';
        content += '</div>';
      }
      content += '</div>';
      return content;
    },
    // 查看单站详情
    showMarker (marker) {
      this.$refs.psaDetail.initData(marker);
      this.show = true;
    },
    // 单站详情关闭回调事件
    closeDetail () {
      this.psId = undefined;
      this.show = false;
      this.clearText();
      this.psaItems = this.refreshList = [];
      this.infoWindow && this.infoWindow.close();
    },
    onLoadMore () {
      this.loadingMore = true;
      let { psaItems, refreshList } = this;
      if (psaItems.length) {
        let items = [...refreshList, ...psaItems.splice(0, 20)];
        items.forEach(item => {
          item.psId = (item.psId).toString();
        });
        this.refreshList = items;
      }
      this.$nextTick(() => {
        this.loadingMore = false;
      });
    },
    // 电站搜索
    onSearch (value) {
      let self = this;
      // eslint-disable-next-line no-unused-vars
      let { psaItems, allPsa, refreshList } = this;
      let fn = () => {
        psaItems = refreshList = [];
        psaItems = (!value ? [] : allPsa.filter(psa => (psa.psName.indexOf(value) != -1)));
        let items = psaItems.splice(0, 20);
        items.forEach(item => {
          item.psId = (item.psId).toString();
        });
        self.refreshList = items;
      };
      self.debounce(fn);
    },
    // 搜索选中事件-选中后自动到相应电站位置
    onSelect (value) {
      if (!value) {
        this.psaItems = this.refreshList = [];
        return;
      }
      let psa = this.allPsa.find(item => item.psId == value);
      if (psa && psa.lon && psa.lat) {
        this.setMapText(psa.psName, [psa.lon, psa.lat]);
        window.aMap.setZoomAndCenter(18, [Number(psa.lon) + 0.0004, Number(psa.lat)]);
      }
      this.psaItems = [];
      this.refreshList = psa ? [psa] : [];
    },
    // 返回至全国地图
    isBackAll () {
      let self = this;
      let fn = () => {
        self.clearText();
        self.show = false;
        self.psId = undefined;
        self.zoom_change = true;
        self.psaItems = self.refreshList = [];
        self.infoWindow && self.infoWindow.close();
        self.$refs.psaDetail && self.$refs.psaDetail.cancel();
        window.aMap && window.aMap.setZoomAndCenter(7, [119.783167, 32.400000]);
      };
      self.debounce(fn);
    },
    /*
        设置文本标记
      */
    setMapText (psName, position) {
      let { mapText } = this;
      if (!mapText) {
        this.mapText = new AMap.Text({
          offset: new AMap.Pixel(0, -50)
        });
      }
      this.mapText.setText(psName);
      this.mapText.setPosition(position);
      window.aMap.add(this.mapText);
    },
    /*
        清除文本标记
      */
    clearText () {
      window.aMap && this.mapText && window.aMap.remove(this.mapText);
    },
    onChange (val) {
      if (!val) {
        this.psaItems = this.refreshList = [];
      }
    },
    /*
        防抖
      */
    debounce (fn, wait = 500) {
      if (this.times) {
        clearTimeout(this.times);
        this.times = null;
      }
      this.times = setTimeout(fn, wait);
    }
  },
  beforeDestroy () {
    this.allPsa = this.psaItems = this.refreshList = [];
    window.infoClick = undefined;
    this.times && clearInterval(this.times);
    this.allInterval && clearInterval(this.allInterval);
    this.allInterval = null;
    this.timeInterval && clearInterval(this.timeInterval);
    this.timeInterval = null;
    if (window.aMap) {
      window.aMap.clearMap();
      window.aMap.clearInfoWindow();
      window.aMap.destroy();
    }
    window.aMap = undefined;
  }
};
</script>
<style lang="less" scoped>
 @import "./screen.less";
.solar_eye_logo {
  width: 44.5vw !important;
  background: url('../../assets/images/screen/custom_bg.png') no-repeat !important;
}
.custom_log {
    top: 60px;
    left: 420px;
    z-index: 9999;
    position: fixed;
}

</style>
<style>
.overlay-container {
  cursor: pointer;
}
.overlay-title {
  font-size: 14px !important;;
  background-color: #fff !important;
  border-color: #fff !important;
  line-height: 34px;
}
  .amap-marker-label {
    background: rgba(0, 0, 0, 0.1);
    padding: 12px;
    color: #FFFFFF;
    font-size: 14px;
    border: 0;
  }

  .info-window-content {
    max-height: 30vh;
    overflow: auto;
    background: linear-gradient(180deg, #0E1820 0%, #1B242D 100%);
    border: 1px solid #51637C;
    padding: 12px;
    border-radius: 4px;
    color: #fff;
  }

  .info-window-content .psa-name {
    cursor: pointer;
  }

  /* 更改高德地图聚合图标样式 */
  .render-cluster-marker {
    background: radial-gradient(rgba(0, 0, 0, 0) 0%, rgba(0, 0, 0, 0.1) 10%, rgba(8, 173, 235, 0.3) 100%);
    text-align: center;
    font-size: 14px;
    color: #40C7FF;
    animation: shadow 3s linear infinite;
  }

  @keyframes shadow {
    0% {
      box-shadow: 0px 0px 0px rgba(8, 173, 235, 0);
    }

    50% {
      box-shadow: 0px 0px 20px rgba(8, 173, 235, 0.3);
    }

    100% {
      box-shadow: 0px 0px 0px rgba(8, 173, 235, 0);
    }
  }

  .effect-scatter {
    background: #212D3A;
    opacity: 0.79;
    border: 1px solid #3C96BC;
    border-radius: 4px;
    padding: 7px 15px 8px 15px;
  }

  .effect-scatter-top {
    width: 100%;
    text-align: center;
    position: relative;
  }

  .effect-scatter-name {
    display: initial;
    margin: auto;
    min-width: 30px;
    font-size: 16px;
    font-family: PingFangSC-Regular, PingFang SC;
    font-weight: 400;
    color: #59CEFF;
    line-height: 22px;
    padding: 0 6px;
    background: #212D3A;
  }

  .effect-scatter-left {
    position: absolute;
    left: 0;
    top: 10.5px;
    width: calc(50% - 15px);
    height: 2px;
    background: linear-gradient(270deg, #59CEFF 0%, rgba(89, 206, 255, 0) 100%);
    opacity: 0.6;
    z-index: -1;
  }

  .effect-scatter-right {
    position: absolute;
    right: 0;
    top: 10.5px;
    width: calc(50% - 15px);
    height: 2px;
    background: linear-gradient(270deg, rgba(89, 206, 255, 0) 0%, #59CEFF 100%);
    opacity: 0.6;
    z-index: -1;
  }

  .effect-scatter-center {
    display: flex;
    vertical-align: initial;
    align-items: center;
  }
  .type {
    width: 18px;
    height: 18px;
  }

  .psa-num {
    margin: 0 24px 0 8px;
    font-size: 16px;
    font-family: PingFangSC-Medium, PingFang SC;
    font-weight: 500;
    color: #FFFFFF;
    line-height: 33px;
  }

  .last-data {
    margin-right: 0;
  }

  .hide {
    display: none !important;
  }

  .show {
    display: block !important;
  }

  .min-z-index {
    z-index: 0;
  }

  .max-z-index {
    z-index: 120;
  }

  @keyframes wind {
    0% {
      transform: rotate(0deg);
    }

    100% {
      transform: rotate(360deg);
    }
  }

  .station-com {
    min-height: 28px;
    min-width: 28px;
  }

  .station-no {
    filter: brightness(0.5);
    cursor: default;
  }

  .wind {
    height: 32px;
    width: 32px;
    background: url(../../assets/images/screen/wind.png);
    animation: wind 8s linear infinite;
    background-size: 100% 100%;
  }

  .storage {
    height: 28px;
    width: 36px;
    position: relative;
    display: flex;
    background: url(../../assets/images/screen/storage.png) no-repeat;
    background-size: 100% 100%;
  }

  .storage-bolt {
    margin: auto;
    transform: skewX(-10deg);
    animation: lightning-anim 4s linear infinite;
  }

  .charge-pv-gif {
    width: 30px;
    height: 30px;
  }

  .storage-bolt::before {
    content: "";
    position: absolute;
    margin-top: -9px;
    margin-left: -6.5px;
    border-top: 6px solid transparent;
    border-right: 3px solid yellow;
    border-bottom: 6px solid yellow;
    border-left: 3px solid transparent;
  }

  .storage-bolt::after {
    content: "";
    position: absolute;
    margin-left: -0.5px;
    margin-top: -1px;
    border-top: 6px solid yellow;
    border-right: 3px solid transparent;
    border-bottom: 6px solid transparent;
    border-left: 3px solid yellow;
  }

  @keyframes lightning-anim {
    0% {
      opacity: 0;
    }

    15% {
      opacity: 1;
    }

    25% {
      opacity: 0;
    }

    35% {
      opacity: 1;
    }

    100% {
      opacity: 0;
    }
  }

  .pv {
    height: 24px;
    width: 24px;
    position: relative;
    background: url(../../assets/images/screen/pv.png) no-repeat;
    background-size: 100% 100%;
  }

  .pv-img {
    opacity: 0.9;
    position: absolute;
    right: -2px;
    top: -5px;
    height: 18px;
    width: 18px;
    background: url(../../assets/images/weather/W0.png) no-repeat;
    background-size: 100% 100%;
    animation: wind 8s linear infinite;
  }
 .type-building {
    background: url(../../assets/images/screen/building.svg) no-repeat;
    background-size: 100% 100%;
  }

  .type-builded {
    background: url(../../assets/images/screen/builded.svg) no-repeat;
    background-size: 100% 100%;
  }

  .type-unbuild {
    background: url(../../assets/images/screen/pending.svg) no-repeat;
    background-size: 100% 100%;
  }
    .type-building-no {
    background: url(../../assets/images/screen/building_1.svg) no-repeat;
    background-size: 100% 100%;
  }

  .type-unbuild-no {
    background: url(../../assets/images/screen/unbuild.svg) no-repeat;
    background-size: 100% 100%;
  }
  @-webkit-keyframes breath {
    0% {
      opacity: 0.2;
    }

    50% {
      opacity: 1;
    }

    100% {
      opacity: 0.2;
    }
  }

  @keyframes breath {
    0% {
      opacity: 0.2;
    }

    50% {
      opacity: 1;
    }

    100% {
      opacity: 0.2;
    }
  }

  .marker-active {
    background-color: #CF1322;
  }

  .amap-logo {
    display: none !important;
  }
</style>
<style type="text/css">
  .screen-dropdown {
    border: 0 !important;
  }

  .screen-dropdown::before {
    display: none !important;
  }

  .screen-dropdown::after {
    display: none !important;
  }
  .status_desc {
    display: flex;
    position: absolute;
    right: 43vw;
    bottom: 36px;
    justify-content: space-evenly;
    align-items: center;
    color: white;
  }
  .status_desc .ps_status {
    width: 24px;
    height: 24px;
    margin:0 10px;
  }
  .status_desc .builded {
     background-color: #4eafd9;
  }
  .status_desc .building {
    background-color: #d8c557;
  }
  .status_desc .unbuild {
    background-color: #17bf27;
  }
</style>
