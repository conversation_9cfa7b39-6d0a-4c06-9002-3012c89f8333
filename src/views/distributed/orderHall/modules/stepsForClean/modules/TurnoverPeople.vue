<template>
  <a-modal
    title="移交"
    :visible="visible"
    :confirm-loading="loading"
    @cancel="handleCancel"
    width="450px"
  >
    <a-form-model
      :model="form"
      ref="turnover"
      :wrapperCol="{ style: 'width: calc(100% - 150px)' }"
      :rules='rules'
      labelAlign='left'
    >
      <a-row>
        <a-form-model-item label="移交负责人" prop="newUserAccount" class='require-none'>
          <a-select placeholder='请选择移交负责人' v-model="form.newUserAccount" allowClear style="width: 100%" allow-clear>
            <a-select-option v-for="(item, k) in filtedAssignList"  :key="k" :value="item.username">
              {{ item.realName }}
            </a-select-option>
          </a-select>
        </a-form-model-item>
      </a-row>
    </a-form-model>
    <div slot="footer">
      <a-button size="default" class="solar-eye-btn-primary" :loading="loading" @click="handleOk">确定</a-button>
      <a-button size="default" :disabled="loading" @click="handleCancel">取消</a-button>
    </div>
  </a-modal>
</template>
<script>
import { reassignmentSave } from '@api/isolarErp/workBench/workBench';
import { USER_INFO } from '@/store/mutation-types';

export default {
  props: {
    visible: {
      type: Boolean,
      default: () => false
    },
    assignList: {
      type: Array,
      default: () => ([])
    },
    orderId: {
      type: [Number, String],
      default: () => ''
    }
  },
  data () {
    return {
      loading: false,
      form: {
        newUserAccount: undefined
      },
      rules: {
        newUserAccount: [
          { required: true, message: '请选择移交负责人', trigger: 'change' }
        ]
      }
    };
  },
  methods: {
    handleOk () {
      this.$refs.turnover.validate(valid => {
        if (valid) {
          this.loading = true;
          reassignmentSave({
            ...this.form,
            id: this.orderId,
            isAddAuditRecord: false
          }).then((res) => {
            if (res.result_code == '1') {
              this.$message.success('操作成功');
              this.loading = false;
              this.$parent.turnoverPeopleVisible = false;
              this.$parent.$emit('cancel');
            }
          }).catch(() => {
            this.loading = false;
          });
        }
      });
    },
    handleCancel () {
      this.$parent.turnoverPeopleVisible = false;
    }
  },
  computed: {
    filtedAssignList () {
      let user = Vue.ls.get(USER_INFO);
      return this.assignList.filter(item => item.username != user.username);
    }
  }
};
</script>
<style lang='less' scoped>
:deep(.require-none){
  .ant-form-item-required:before{
    content:''
  }
}
</style>
