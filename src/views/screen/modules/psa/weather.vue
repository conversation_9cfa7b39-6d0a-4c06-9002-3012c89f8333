<template>
  <div class="weather-model">
    <div class="left-today-city-weather">
      <div class="city-name">{{cityName}}</div>
      <div class="avg-temp">{{avgTemp}}°C</div>
      <div class="temp-interval">{{tempDay}}°C&nbsp;/&nbsp;{{tempNight}}°C</div>
    </div>
    <div class="right-weather-lines">
      <div class="lines" ref="weatherLines"></div>
      <div class="weather-icons">
        <div class="icon-item" v-for="(icon,index) in icons" :key="index">
          <img :src="icon"/>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
// 引入基本模板
const echarts = require('echarts');
export default {
  data () {
    return {
      cityName: '',
      avgTemp: '',
      tempDay: '',
      tempNight: '',
      icons: []
    };
  },
  methods: {
    init (val) {
      const today = val.hasOwnProperty('today') ? val.today : '';
      this.cityName = today.hasOwnProperty('name') ? today.name : '--';
      this.avgTemp = today.hasOwnProperty('avgTemp') ? today.avgTemp : '--';
      this.tempDay = today.hasOwnProperty('tempDay') ? today.tempDay : '--';
      this.tempNight = today.hasOwnProperty('name') ? today.tempNight : '--';
      const x_data = val.hasOwnProperty('dayOfWeek') ? val.dayOfWeek : [];
      const max = val.hasOwnProperty('maxTemp') ? val.maxTemp : [];
      const min = val.hasOwnProperty('minTemp') ? val.minTemp : [];
      const conditionId = val.hasOwnProperty('conditionId') ? val.conditionId : [];
      let icons = [];
      conditionId.forEach(icon => {
        icons.push(require('@/assets/images/weather/W' + icon + '.png'));
      });
      this.icons = icons;
      this.initWeatherLines(x_data, max, min);
    },
    initWeatherLines (x_data, max, min) {
      if (!this.$refs.weatherLines) { // dom 不存在时不画
        return;
      }
      // 不重复初始化
      let weatherLines = echarts.getInstanceByDom(this.$refs.weatherLines);
      if (!weatherLines) {
        weatherLines = echarts.init(this.$refs.weatherLines);
        this.$once('hook:beforeDestroy', function () {
          echarts.dispose(weatherLines);
        });
      }
      let option = {
        color: ['rgba(248, 86, 125, 0.7)', 'rgba(82, 187, 233, 0.7)'],
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow',
            shadowStyle: {
              color: 'rgba(150,150,150,0.1)'
            }
          },
          backgroundColor: 'rgba(55, 71, 89, 0.9)',
          borderColor: '#69C7EB',
          borderWidth: '1',
          textStyle: {
            color: 'rgba(255, 255, 255, 0.8)'
          },
          formatter: function (params) {
            const data = params[0];
            let html = data.name + '<br>最高温度：';
            html += data.data;
            html += '°C<br>最低温度：';
            html += min[data.dataIndex];
            html += '°C';
            return html;
          }
        },
        grid: {
          x: 0,
          y: 20,
          x2: 0,
          y2: 15,
          containLabel: true
        },
        xAxis: {
          axisLabel: {
            textStyle: {
              color: 'rgba(255, 255, 255, 0.8)'
            }
          },
          axisTick: false,
          axisLine: {
            show: false
          },
          offset: 15,
          data: x_data
        },
        legend: {
          show: false
        },
        yAxis: [{
          show: false
        }],
        series: [{
          name: '温度',
          type: 'line',
          smooth: true,
          zlevel: 3,
          // symbol: 'none', //数据交叉点样式
          symbolSize: 8,
          lineStyle: {
            color: 'rgba(248, 86, 125, 0.7)'
          },
          label: {
            show: true,
            position: 'top',
            fontSize: 8,
            color: '#fff'
          },
          // areaStyle: {
          //   normal: {
          //     color: new echarts.graphic.LinearGradient(
          //       0,
          //       0,
          //       0,
          //       1,
          //       [{
          //           offset: 0,
          //           color: 'rgba(248, 86, 125, 0.7)',
          //         },
          //         {
          //           offset: 1,
          //           color: 'rgba(248, 86, 125, 0)',
          //         }
          //       ],
          //       false
          //     ),
          //     // shadowColor: 'rgba(248, 86, 125, 0.4)',
          //     // shadowBlur: 10
          //   }
          // },
          connectNulls: true,
          data: max
        }, {
          name: '',
          type: 'line',
          smooth: true,
          // symbol: 'none', //数据交叉点样式
          symbolSize: 8,
          lineStyle: {
            color: 'rgba(82, 187, 233, 0.7)'
          },
          label: {
            show: true,
            position: 'bottom',
            fontSize: 8,
            color: '#fff'
          },
          // areaStyle: {
          //   normal: {
          //     color: new echarts.graphic.LinearGradient(
          //       0,
          //       0,
          //       0,
          //       1,
          //       [{
          //           offset: 0,
          //           color: 'rgba(82, 187, 233, 0)',
          //         },
          //         {
          //           offset: 1,
          //           color: 'rgba(82, 187, 233, 0.7)',
          //         }
          //       ],
          //       false
          //     ),
          //     // shadowColor: 'rgba(82, 187, 233, 0.4)',
          //     // shadowBlur: 10
          //   }
          // },
          connectNulls: true,
          data: min
        }]
      };
      weatherLines.setOption(option, true);
    }
  }
};
</script>

<style lang="less" scoped>
  .weather-model {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;

    .left-today-city-weather {
      width: 80px;
      height: 100%;
      color: rgba(255, 255, 255, 0.7)
    }

    .right-weather-lines {
      width: calc(100% - 80px);
      height: 100%;
    }

    .lines {
      margin-left: -30px;
      width: calc(100% + 50px);
      height: calc(100% - 20px);
    }

    .weather-icons {
      // margin-left: -10px;
      width: calc(100% + 15px);
      height: 20px;
      display: flex;
      align-items: center;
      .icon-item{
        width: 33.33%;
        display: flex;
        margin: auto;
        height: 100%;
      }
      img{
        max-height: 100%;
        margin: auto;
        width: 15px;
      }
    }

    .city-name {
      font-size: 14px;
      font-family: PingFangSC-Semibold, PingFang SC;
      font-weight: 600;
      line-height: 28px;
    }

    .avg-temp {
      font-size: 20px;
      font-family: PingFangSC-Medium, PingFang SC;
      font-weight: 500;
      line-height: 40px;
    }

    .temp-interval {
      font-size: 12px;
      font-family: PingFangSC-Regular, PingFang SC;
      font-weight: 400;
    }
  }
</style>
