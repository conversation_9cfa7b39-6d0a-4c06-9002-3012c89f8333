<template>
  <a-modal
    :visible="visible"
    footer
    @cancel="visible = false"
    width="300"
  >
    <div class="down-content">
      <div class="img-box"><img class='width-height-100 qr-code' :src="codeUrl"></div>
      <div class="down-text">下载SolarEye APP</div>
    </div>

  </a-modal>
</template>
<script>
const codeUrl = process.env.VUE_APP_API_CODE_URL;
export default {
  data () {
    return {
      visible: false,
      codeUrl: codeUrl
    };
  },
  methods: {
    init () {
      this.visible = true;
    }
  }
};
</script>
<style lang="less" scoped>
.down-content {
  padding: 60px 66px;
}
.down-text {
  margin-top: 32px;
  font-size: 18px;
  text-align: center;
  color: var(--zw-text-1-color--default);
  font-family: 'PingFangSC-Medium';
}
.img-box {
  width: 248px;
  height: 248px;
}
</style>
