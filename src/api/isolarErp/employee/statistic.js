import { axios } from '@/utils/erp-request';

/**
 * 获取页面初始化数据
 *
 * @param data 检索参数
 */
export function getEmployeeStatisticInfo (data) {
  return axios({
    url: '/plan/v1/erpEmployeeStatistic/getEmployeeStatisticInfo',
    method: 'post',
    data
  });
}

/**
 * 导出
 * @param data 检索参数
 */
export function exportExcel (data) {
  return axios({
    url: '/plan/v1/erpEmployeeStatistic/export',
    method: 'post',
    data
  });
}

/**
 * 查询员工列表
 * @param data 检索参数
 */
export function getEmployeeList (data) {
  return axios({
    url: '/plan/v1/erpEmployeeStatistic/getEmployeeList',
    method: 'post',
    data
  });
}
