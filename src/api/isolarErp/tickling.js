import { axios } from '@/utils/erp-request';

// 意见反馈列表
export function selectListFeedBack (data) {
  return axios({
    url: '/planManage/erpSecFeedback/selectListFeedBack',
    method: 'post',
    data
  });
}
// 意见反馈导出列表
export function exportFeedBack (data) {
  return axios({
    url: '/planManage/erpSecFeedback/exportFeedBack',
    method: 'post',
    data
  });
}
// 意见反馈关闭
export function closeFeedBack (data) {
  return axios({
    url: '/planManage/erpSecFeedback/closeFeedBack',
    method: 'post',
    data
  });
}
// 意见反馈删除
export function deleteFeedBack (data) {
  return axios({
    url: '/planManage/erpSecFeedback/deleteFeedBack',
    method: 'post',
    data
  });
}
// 意见反馈提交
export function saveFeedBack (data) {
  return axios({
    url: '/planManage/erpSecFeedback/saveFeedBack',
    method: 'post',
    data
  });
}
// 意见反馈详情
export function detailFeedBack (data) {
  return axios({
    url: '/planManage/erpSecFeedback/detailFeedBack',
    method: 'post',
    data
  });
}
