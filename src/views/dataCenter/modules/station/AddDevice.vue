<template>
  <a-drawer
    width="100%"
    :visible="visible"
    title="添加设备"
    @close="onClose(false)"
    :get-container="getDom"
    :wrap-style="{ position: 'absolute' }">
    <a-spin :spinning="loading" style="height:100%;">
      <a-form-model class="form" ref="baseForm" :model="baseForm" :rules="baseFormRules" :labelCol="{ style:'width: 25%' }" :wrapperCol="{ style:'width: 480px' }">
         <a-row>
           <a-col :xl='12' :sm='12' :xs='24'>
              <a-form-model-item label="设备名称" prop="deviceName">
                <a-input v-model="baseForm.deviceName" @blur="baseForm.deviceName = $trim($event)" :maxLength="30" placeholder="请输入设备名称"/>
              </a-form-model-item>
           </a-col>
           <a-col :xl='12' :sm='12' :xs='24'>
              <a-form-model-item label="设备分类" prop="deviceType">
                <a-select v-model="baseForm.deviceType" placeholder="请选择" style="width: 100%;">
                  <a-select-option value="3">并网点</a-select-option>
                  <a-select-option value="17">单元</a-select-option>
                  <a-select-option value="47">储能单元</a-select-option>
                </a-select>
              </a-form-model-item>
           </a-col>
         </a-row>
      </a-form-model>
      <div class="drawer-form-foot">
        <a-button  @click="onClose(false)">取消</a-button>
        <a-button class="solar-eye-btn-primary" @click="saveData">保存</a-button>
      </div>
    </a-spin>
  </a-drawer>
</template>
<script>
import { DeviceMixins } from '../../mixins/device.js';
import { manageDeviceSaveBatch } from '@/api/health/healthapi.js';
export default {
  name: 'AddDevice',
  mixins: [DeviceMixins],
  inject: ['collectorWire'],
  data () {
    return {
      loading: false,
      visible: false,
      baseForm: {},
      baseFormRules: {
        deviceName: [{
          required: true,
          trigger: 'blur',
          message: '请输入设备名称'
        }],
        deviceType: [{
          required: true,
          message: '请选择设备分类'
        }]
      }
    };
  },
  methods: {
    getDom () {
      return document.getElementsByClassName('station-model')[0];
    },
    init (deviceInfo) {
      this.baseForm = {
        deviceName: undefined,
        deviceType: undefined
      };
      this.baseForm = Object.assign(this.baseForm, deviceInfo);
      this.visible = true;
    },
    onClose (flag) {
      this.visible = false;
      this.$refs.baseForm.clearValidate();
      if (flag) {
        this.$emit('refresh');
      }
    },
    saveData () {
      this.$refs.baseForm.validate(valid => {
        if (valid) {
          this.loading = true;
          let map = {
            manageDeviceSaveReq: [
              this.baseForm
            ]
          };
          manageDeviceSaveBatch(map).then(res => {
            this.onClose(true);
            this.$message.success('添加设备成功！');
            let resData = Array.isArray(res) && res.length ? res[0] : {};
            this.$emit('insertDevice', resData, this.baseForm.parentKey);
            this.loading = false;
          }).catch(() => { this.loading = false; });
        }
      });
    }
  }
};
</script>
<style lang="less" scoped>
:deep(.ant-form-item) {
  width: 100%;
  display: inline-flex;
}
:deep(.ant-spin-container) {
  height: 100%;
}
:deep(.ant-drawer-body) {
  height: calc(100% - 55px);
  padding: 24px 0 0 0;
}
.form {
  height: calc(100% - 53px);
  overflow: auto;
}

</style>
