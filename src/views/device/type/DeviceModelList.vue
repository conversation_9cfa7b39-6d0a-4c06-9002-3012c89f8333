<!-- 设备型号列表 -->
<template>
  <a-select
  v-model="idList"
  @change="dataChange"
  @dropdownVisibleChange="search(null)"
  :allowClear="allowClear"
  :maxTagCount="1"
  :maxTagTextLength="32"
  :mode="isMultiple ? 'multiple' : ''"
  show-search
  :filter-option="false"
  @search="search"
  style="width: 100%"
  dropdownClassName="all-psa-dropdown"
  >
    <template slot="placeholder">请选择设备型号</template>
    <template slot="dropdownRender" slot-scope="menu">
      <v-nodes :vnodes="menu" />
      <template v-if="showMore">
        <a-divider style="margin: 4px 0" />
        <div style="padding: 4px 8px; text-align: right" @mousedown="(e) => e.preventDefault()">
          <a-button type="link" :loading="moreLoading" :disabled="moreLoading" @click="loadMore()">更多</a-button>
        </div>
      </template>
    </template>
    <a-select-option
      v-for="item in options"
      :key="item.id"
      :value="item.id"
      :disabled="item.disabled"
      :title="item.deviceModel"
      >{{ item.deviceModel }}</a-select-option
    >
  </a-select>
</template>

<script>
import { getDeviceModelList } from '@/api/device/type';
export default {
  name: 'MultipleSelect',
  components: {
    VNodes: {
      functional: true,
      render: (h, ctx) => ctx.props.vnodes
    }
  },
  model: {
    prop: 'ids',
    event: 'change'
  },
  props: {
    ids: {
      type: Array,
      default: () => []
    },
    disabled: {
      type: Boolean,
      default: false
    },
    isMultiple: {
      type: Boolean,
      default: false
    },
    allowClear: {
      type: Boolean,
      default: false
    },
    topIds: {
      type: Array,
      default: () => []
    },
    deviceTypeId: {
      type: String,
      default: ''
    }
  },
  data () {
    return {
      idList: [],
      times: null,
      options: [],
      showMore: false,
      moreLoading: false,
      params: {
        curPage: 1,
        size: 100,
        deviceTypeId: this.deviceTypeId,
        topIds: this.topIds,
        keyword: ''
      }
    };
  },
  created () {
    this.search(null);
  },
  watch: {
    topIds: {
      handler (val) {
        this.params.topIds = val;
        this.search(null);
      },
      deep: true,
      immediate: true
    },
    deviceTypeId (val, old) {
      this.params.deviceTypeId = val;
      this.search(null);
    }
  },
  mounted () {
  },
  methods: {
    // 重置
    reset () {
      this.idList = [];
      this.options = [];
      this.params = {
        curPage: 1,
        size: 100,
        keyword: '',
        deviceTypeId: '',
        topIds: []
      };
      this.search(null);
    },
    /*
        change事件
      */
    dataChange (value) {
      this.$emit('change', value);
      let option = this.options.find((item) => item.id === value);
      this.$emit('select', option);
      if (!value) {
        this.search(null);
      }
    },
    /*
        查询数据
      */
    getList () {
      let _this = this;
      _this.moreLoading = true;
      return new Promise((resolve, reject) => {
        let paramsList = Object.assign({}, _this.params);
        paramsList.topIds = this.params.topIds.join(',');
        getDeviceModelList(paramsList)
          .then((res) => {
            let data = res.result_data || {};
            _this.showMore = data.t1;
            let result = Array.isArray(data.t2) ? data.t2 : [];
            resolve(result);
            _this.moreLoading = false;
          })
          .catch((err) => {
            reject(err);
            _this.showMore = false;
            _this.moreLoading = false;
          });
      });
    },
    /*
        电站筛选
      */
    search (input) {
      let { ids, options } = this;
      let _this = this;
      let filterData = () => {
        let selected = [];
        if (!Array.isArray(ids)) {
          ids = [ids];
        }
        // 找出已选项
        if (ids) {
          selected = options.filter((item) => ids.toString().includes(item.id));
        }
        Object.assign(_this.params, { curPage: 1, keyword: input });
        _this
          .getList()
          .then((res) => {
            _this.idList = _this.ids;
            _this.options = Object.freeze(
              selected.length == 0 ? res : selected.concat(res.filter((item) => !ids.includes(item.id)))
            );
          })
          .catch(() => {
            _this.$forceUpdate();
          });
      };
      this.debounce(filterData, 800);
    },
    /*
        加载更多
      */
    loadMore () {
      let { value, options } = this;
      if (this.params.curPage >= 5) {
        this.$notification.info({ message: '提示', description: '试试搜索', duration: 3 });
        return;
      }
      this.params.curPage += 1;
      let selected = [];
      // 找出已选项
      if (value) {
        selected = options.filter((item) => value == item.id);
      }
      this.getList()
        .then((res) => {
          let all = res.concat(options);
          this.options = Object.freeze(selected.concat(all.filter((item) => value != item.id)));
          this.$forceUpdate();
          let dropdown = document.querySelector('.all-psa-dropdown .ant-select-dropdown-menu');
          dropdown && dropdown.scrollTo(0, 0);
        })
        .catch(() => {
          this.$forceUpdate();
        });
    },
    /*
        防抖
      */
    debounce (fn, wait = 500) {
      if (this.times) {
        clearTimeout(this.times);
        this.times = null;
      }
      this.times = setTimeout(fn, wait);
    }
  },
  beforeDestroy () {
    this.options = [];
    this.times && clearTimeout(this.times);
    this.times = null;
  }
};
</script>

<style lang="less" scoped>
:deep(.ant-btn-link) {
  border: 0;
  color: var(--zw-primary-color--default);
}
:deep(.ant-select-selection--multiple) {
  height: 32px;
}
</style>
