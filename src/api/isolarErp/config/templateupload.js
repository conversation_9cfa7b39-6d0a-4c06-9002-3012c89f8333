import { axios } from '@/utils/erp-request';

// 模板列表
export function getTemplateList (data) {
  return axios({
    url: 'system/v1/template/getTemplateList',
    method: 'post',
    data
  });
}
export function downloadOperationManual (data) {
  return axios({
    url: 'system/v1/annex/downloadOperationManual',
    method: 'post',
    data
  });
}

export function downloadTemplate (data) {
  return axios({
    url: 'system/v1/template/downloadTemplate',
    method: 'post',
    data
  });
}

export function deleteTemplate (data) {
  return axios({
    url: 'system/v1/template/deleteTemplate',
    method: 'post',
    data
  });
}

export function downloadTemplates (data) {
  return axios({
    url: 'system/v1/template/downloadTemplates',
    method: 'post',
    data
  });
}
