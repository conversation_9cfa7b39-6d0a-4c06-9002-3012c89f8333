<template>
<a-drawer width="100%" :visible="drawerInfo.visible" :body-style="{ padding: '0 0 40px', overflow:'hidden' }" :drawerStyle="{ background: navTheme=='dark' ? '#111C2D':'#f0f0f0', padding:0,overflow:'hidden' }" @close="onClose" :get-container="dom" :wrap-style="{ position: 'absolute',  }">
    <template slot="title"></template>
    <a-row :gutter="16">
        <a-form-model :model="form" :rules="rules" layout="horizontal" v-bind="{
          labelCol: { span: 4 },
          wrapperCol: { span: 20 },
        }" ref="ruleForm">
            <a-col :span="4">
                <a-card :body-style="{ height: height  +'px' }">
                    <ps-tree-select @change="getChild"  v-model="psId" :isPsaDisable="true" :isPsManage="true" :isQueryPs="1" class="ps-tree" />
                     <virtual-tree :data='treeData' :draggable="true" :height="'calc(100vh - 264px)'"
                     node-key='key' ref="deviceTree"
                     @node-contextmenu="rightClick"
                      @node-click="onClick"
                      :allow-drop="allowDrop"
                      @node-drag-end="onDrop"
                      @node-drag-start="dragStart"
                      :current-node-key="selectedKeys"
                      :expand-on-click-node="false"
                     :props="{
                        label: 'title',
                        isLeaf: 'isLeaf',
                        children: 'children',
                        key: 'psKey',
                        disabled:'disabled'
                      }" >
                      <span class="custom-tree-node" slot-scope="{ node, data }">
                        <a-dropdown :trigger="['contextmenu']" v-if="(data.isLocal != '1' && data.deviceStatus != 1 || ((data.isLocal == '1' && data.overTime != '1') || ['3', '17', '47'].includes(data.deviceType)))">
                          <span :title="data.deviceStatus == 1 ? data.title + '（停用）' : data.title" :class="data.selectable ? '' : 'unSelectable'">
                            {{ data.deviceStatus == 1 ? data.title + '（停用）' : data.title}}
                          </span>
                          <template #overlay>
                            <a-menu @click="({ key: menuKey }) => onContextMenuClick(menuKey, '2', data.id, data.psKey)">
                              <a-menu-item v-if="data.isLocal != '1' && data.deviceStatus != 1" key="1">新增</a-menu-item>
                              <a-menu-item v-if="data.thirdId == null && ((data.isLocal == '1' && data.overTime != '1') || ['3', '17', '47'].includes(data.deviceType))" key="2">删除</a-menu-item>
                            </a-menu>
                          </template>
                        </a-dropdown>
                        <span v-else :title="data.deviceStatus == 1 ? data.title + '（停用）' : data.title" :class="data.selectable ? '' : 'unSelectable'">
                          {{ data.deviceStatus == 1 ? data.title + '（停用）' : data.title}}
                        </span>
                      </span>
                    </virtual-tree>
                </a-card>
            </a-col>
            <a-col id="config" :span="20">
                <component ref="currentPage" :is="currentComponent" v-if="psId"  @saveData="saveData" :isUpdateEdit='isEdit'
                  :deviceType="deviceType" :isLocal="isLocal" :isSelf="isSelf" @setChangeData="setChangeData"> </component>
            </a-col>
        </a-form-model>
    </a-row>
    <add-device ref="addDevice" @insertDevice="insertDevice"/>
    <batch-adjust ref="batchAdjust" @refresh="refresh" :isSelf="isSelf" />
</a-drawer>
</template>
<script>
import {
  updatePsAttribute,
  getDeviceTypeTree,
  manageDeviceSaveBatch,
  deviceNodeMove,
  deleteDevice
} from '@/api/health/healthapi.js';
import Station from './configInfo/Station';
import AddDevice from './station/AddDevice';
import ZTree from '@/components/ZTree';
import { mixin } from '@/utils/mixin.js';
import BatchAdjust from './configInfo/BatchAdjustment.vue';
import ComDevice from './configInfo/ComDevice';
import virtualTree from '@/components/virtrulTree/ve-tree.vue';
let innerHeight = window.innerHeight - 60 - 40 - 24;

export default {
  name: 'StationMageDrawer',
  mixins: [mixin],
  components: {
    Station,
    ZTree,
    AddDevice,
    BatchAdjust,
    virtualTree,
    ComDevice
  },
  props: {
    psId: {
      type: [String, Number],
      default: ''
    },
    psName: {
      type: String,
      default: ''
    }
  },
  data () {
    return {
      deviceDisabled: false, // 选中不可添加设备
      drawerInfo: {
        title: '',
        visible: false,
        type: ''
      },
      url: {
        sourceList: '/versionManagement/deviceParameter',
        pointList: '/versionManagement/pointsByDeviceType',
        update: '/versionManagement/update',
        add: '/versionManagement/add'
      },
      dom: 'body',
      treeData: [],
      ztreeObj: '',
      selectNode: '',
      clickNode: '',
      parentNode: '',
      selectedKeys: '',
      expandedKeys: [],
      height: innerHeight > 700 ? innerHeight : 700,
      defaultProps: {
        children: 'childrenList',
        title: 'device_name',
        key: 'ps_key'
      },
      currentComponent: 'station',
      deviceType: '',
      psInfo: {
        psId: '',
        id: '',
        pid: '',
        pidUuidIndexCode: '',
        psKey: '',
        deviceType: '',
        psName: '',
        isEdit: false,
        communicateVersion: '',
        isLocal: ''
      },
      isEdit: true,
      isLocal: false,
      isSelf: false,
      changeData: false,
      collectorWire: [],
      prev: null,
      next: null,
      isExport: ''
    };
  },
  provide () {
    return {
      provideData: this.psInfo,
      psInfo: () => this.psInfo,
      collectorWire: () => this.collectorWire,
      selectNode: () => this.selectNode
    };
  },
  created () {
    this.psInfo.psId = this.psId;
    this.psInfo.psName = this.psName;
    this.psInfo.deviceType = 11;
  },
  watch: {
    'provideData.psId' () {
      this.getDeviceTypeTree(tree);
    }
  },
  mounted () {
    this.dom = document.getElementsByClassName('station-model')[0];
  },
  methods: {
    getChild (val, node) {
      this.psInfo.psId = node.id;
      this.psInfo.psName = node.name;
      this.psInfo.psKey = '';
      this.psInfo.deviceType = 11;
      this.isSelf = node.source == '0';
      this.getPsTreeMenu({
        psId: node.id
      });
      this.currentComponent = 'station';
    },
    /**
         * 初始化抽屉
         * params {Object} column 点击列的对象
         * params {Object} title ，type 等基本信息
         * */
    initDrawer (column, obj) {
      this.loading = false;
      this.isSelf = column.source == '0';
      // this.isSelf = true;
      this.drawerInfo = Object.assign({}, this.drawerInfo, obj);
      for (let item in this.form) {
        if (item != 'id') {
          this.form[item] = '';
        }
      }
      if (obj.type != 'add') {
        this.getPsTreeMenu({
          psId: column.psId
        });
      }
    },
    onClick (val, event) {
      if (!val.selectable) {
        this.deviceDisabled = true;
        return;
      } else {
        this.deviceDisabled = false;
      };
      this.setSelectNodeInfo(val);
      this.removeClass();
    },
    setSelectNodeInfo (node) {
      this.selectNode = node;
      this.selectedKeys = node.psKey;
      this.psInfo.id = node.id;
      this.psInfo.pid = node.pid;
      this.psInfo.pidUuidIndexCode = node.uuidIndexCode;
      this.psInfo.psId = node.psId;
      this.psInfo.psKey = node.psKey;
      this.psInfo.deviceType = Number(node.deviceType);
      this.psInfo.communicateVersion = node.communicateVersion;
      this.psInfo.isLocal = node.isLocal;
      this.deviceType = node.deviceType;
      this.isLocal = node.isLocal == '1' || (node.isLocal == '2' && this.deviceType == '5');
      this.isExport = node.isLocal;
      this.currentComponent = this.returnCurrentComponent(node.deviceType);
    },
    allowDrop (draggingNode, dropNode, type) {
      return type == 'inner' && dropNode.parent != draggingNode && draggingNode.level != 1;
    },
    dragStart (node, event) {
      this.parentNode = node.parent;
      this.prev = node.previousSibling;
      this.next = node.nextSibling;
    },
    onDrop (dragNode, node, place, event) {
      if (dragNode.data.psKey == node.data.psKey || place === 'none') {
        return;
      }
      let that = this;
      let dragData = JSON.stringify(dragNode.data);
      this.$confirm({
        title: '提示',
        content: '是否确认移动节点？',
        okText: '确定',
        cancelText: '取消',
        onOk: () => {
          let params = {
            psId: this.psInfo.psId,
            id: dragNode.data.id,
            idTo: node.data.id,
            psKey: node.data.psKey
          };
          this.loading = true;
          deviceNodeMove(params).then(res => {
            this.loading = false;
          }).catch(() => {
            that.$refs.deviceTree.remove(JSON.parse(dragData));
            that.dealNode(dragNode.data);
          });
        },
        onCancel () {
          that.$refs.deviceTree.remove(JSON.parse(dragData));
          that.dealNode(dragNode.data);
        }
      });
    },
    dealNode (data) {
      let that = this;
      if (that.prev) {
        that.$refs.deviceTree.insertAfter(data, that.prev);
      } else if (that.next) {
        that.$refs.deviceTree.insertBefore(data, that.next);
      } else {
        that.$refs.deviceTree.append(data, that.parentNode);
      }
      this.removeClass();
      this.prev = this.next = null;
    },
    onExpand (val) {
      this.expandedKeys = val;
      this.removeClass();
    },
    removeClass () {
      this.$nextTick(() => {
        document.getElementsByClassName('drag-over')[0] && document.getElementsByClassName('drag-over')[0].classList.remove('drag-over');
      });
    },
    getPsTreeMenu (obj) {
      getDeviceTypeTree(obj).then((res) => {
        if (res.result_data && res.result_data.length > 0) {
          this.treeData = res.result_data.filter(item => {
            return !(item.pid == '0' && item.deviceType == '5');
          });
          this.treeData = this.arrayToTree(this.treeData, '0');
          this.selectNode = this.treeData[0];
          this.selectedKeys = this.treeData[0].key;
        }
        this.treeLoading = false;
      });
    },
    //  高性能数组转树 此方法利用引用类型的内存 需要给出初始pid
    arrayToTree (items, minPid) {
      const result = []; // 存放结果集
      const itemMap = {}; //
      this.collectorWire = [];
      for (const item of items) {
        if (item.deviceType == 3 && !item.deviceName.includes('升压站')) {
          this.collectorWire.push(item);
        }
        const id = item.id;
        const pid = item.pid;
        if (!itemMap[id] || !items.find(ele => ele.id == pid)) {
          itemMap[id] = {
            children: []
          };
        }
        let selectable = ['1', '4', '5', '11', '3', '6', '7', '12', '17', '29', '30', '23', '24', '26', '37', '47'].includes(item.deviceType) || (item.isLocal == '1' && ['301', '302', '7'].includes(item.deviceType));
        itemMap[id] = {
          ...item,
          title: item.deviceName,
          key: item.psKey,
          selectable: selectable,
          disabled: selectable,
          children: itemMap[id]['children']
        };

        const treeItem = itemMap[id];

        if (pid === minPid) {
          result.push(treeItem);
        } else {
          if (!itemMap[pid] || !items.find(ele => ele.id == pid)) {
            itemMap[pid] = {
              children: []
            };
          }
          itemMap[pid].children.push(treeItem);
        }
      }
      return result;
    },
    returnCurrentComponent (value) {
      let keyName = '';
      if (value != '11') {
        keyName = 'comDevice';
      } else {
        keyName = 'station';
      }
      return keyName;
    },
    onClose () {
      this.drawerInfo.visible = false;
      this.$emit('click', this.visible, this.changeData);
    },
    // 保存
    saveData (obj, deviceName, deviceStatus) {
      if (obj.isStation) {
        this.managePsSave(obj, deviceName, deviceStatus);
      } else {
        this.manageDeviceSave(obj, deviceName);
      }
    },
    // 设备编辑保存
    manageDeviceSave (obj, deviceName) {
      let params = {
        manageDeviceSaveReq: [{
          psId: this.psInfo.psId,
          uuid: this.psInfo.id,
          upUuid: this.psInfo.pid === 'aaaaaa' ? this.treeData[0].id : this.psInfo.pid,
          pidUuidIndexCode: this.psInfo.pidUuidIndexCode,
          psKey: this.psInfo.psKey,
          ...obj
        }]
      };
      manageDeviceSaveBatch(params).then((res) => {
        this.$message.success('配置成功!');
        this.psInfo.isEdit = !this.psInfo.isEdit;
        if (deviceName && (this.isLocal || this.isSelf)) {
          this.selectNode.deviceName = deviceName;
          this.selectNode.title = deviceName;
          this.collectorWire.map(item => {
            if (item.psKey == params.psKey) {
              item.deviceName = deviceName;
              item.title = deviceName;
            }
          });
        }
      });
    },
    // 电站编辑保存
    managePsSave (obj, deviceName, deviceStatus) {
      let params = {
        psId: this.psInfo.psId,
        attributeType: this.psInfo.psKey ? '02' : '01',
        psKey: this.psInfo.psKey,
        isLocal: this.selectNode.isLocal == 2 ? 2 : (this.isLocal ? 1 : 0),
        source: this.isSelf ? '0' : '1',
        communicateVersion: this.psInfo.communicateVersion
      };
      if (obj.isStation) {
        params = Object.assign({}, params, {
          psConfig: obj.psConfig,
          collectorConfig: obj.collectorConfig
        });
      } else {
        obj.form.psKey = this.psInfo.psKey;
        params = Object.assign({}, params, {
          deviceConfig: obj.form,
          seriesConfig: obj.form.tableData,
          seriesEnableConfigAutoSource: obj.seriesEnableConfig || undefined,
          isAdjust: obj.isAdjust || undefined
        });
      }
      this.changeData = true;
      updatePsAttribute(params).then((res) => {
        if (res.result_code === '1') {
          this.$message.success('配置成功!');
          this.psInfo.isEdit = !this.psInfo.isEdit;
          if (deviceName && (this.isLocal || this.isSelf)) {
            this.selectNode.deviceName = deviceName;
            this.selectNode.title = deviceName;
            this.collectorWire.map(item => {
              if (item.psKey == params.psKey) {
                item.deviceName = deviceName;
                item.title = deviceName;
              }
            });
          }
          if (this.selectNode.deviceType == '11') {
            this.getPsTreeMenu({ psId: this.psInfo.psId });
          }
          if (deviceStatus && this.isSelf) {
            this.selectNode.deviceStatus = deviceStatus == '正常' ? 0 : 1;
            if (deviceStatus == '停用') {
              this.setChildrenStatus(this.selectNode);
            }
          }
        } else {
          this.$message.error(res.result_msg);
        }
      });
    },
    setChildrenStatus (node) {
      if (node.children && node.children.length > 0) {
        node.children.forEach(item => {
          item.deviceStatus = 1;
          this.setChildrenStatus(item);
        });
      }
    },
    addDevice (type) {
      this.addType = type;
      if (type == '1') {
        if (this.selectNode.deviceStatus != '1') {
          this.clickNode = this.selectNode;
        } else {
          this.$message.destroy();
          this.$message.warning('停用设备下不允许添加新设备!');
          return false;
        }
      }
      let deviceInfo = {
        psId: this.psInfo.psId,
        psName: this.psInfo.psName,
        pidUuidIndexCode: this.clickNode.uuidIndexCode,
        upUuid: this.clickNode.id,
        parentKey: this.clickNode.key
      };
      this.$refs.addDevice.init(deviceInfo);
    },
    onContextMenuClick (menuKey, type, id, psKey) {
      if (menuKey == '1') {
        this.addDevice(type);
      } else {
        this.$confirm({
          title: '提示',
          content: '是否确认删除该设备节点？',
          okText: '确定',
          cancelText: '取消',
          onOk: () => {
            deleteDevice({ uuid: id, psKey: psKey, psId: this.psInfo.psId }).then(res => {
              this.$message.success('删除成功！');
              this.getPsTreeMenu({ psId: this.psInfo.psId });
              this.parentNode.children = this.parentNode.children.filter(item => {
                return item.key != this.clickNode.key;
              });
              if (this.currentComponent == 'station') {
                this.$refs.currentPage.getDeviceInfo(true);
              } else {
                let arr = [this.clickNode.id];
                if (this.clickNode.children && this.clickNode.children.length > 0) {
                  let childNodes = this.getChildNodes(this.clickNode.children, []);
                  arr = [...childNodes, this.clickNode.id];
                }
                if (arr.includes(this.selectNode.id)) {
                  this.setSelectNodeInfo(this.treeData[0]);
                }
              }
            });
          }
        });
      }
    },
    getChildNodes (data, arr) {
      data.forEach(item => {
        arr.push(item.id);
        if (item.children && item.children.length > 0) {
          this.getChildNodes(item.children, arr);
        }
      });
      return arr;
    },
    batchAdjust () {
      let params = {
        psId: this.psInfo.psId,
        id: this.treeData[0].id,
        psKey: this.psInfo.psKey,
        deviceType: '1'
      };
      this.$refs.batchAdjust.init(params, this.isSelf);
    },
    rightClick (event, data, node, self) {
      if (data.pid != '0') {
        this.parentNode = node.parent.data;
      }
      this.clickNode = data;
    },
    insertDevice (node, key) {
      node.title = node.deviceName;
      node.key = node.psKey;
      node.selectable = ['1', '4', '5', '11', '3', '6', '7', '12', '17', '29', '30', '23', '24', '26', '37', '47'].includes(node.deviceType);
      node.id = node.uuid;
      node.children = [];
      if (node.deviceType == 3) {
        this.collectorWire.push(node);
      }
      this.expandedKeys.push(key);
      // if (this.addType == '1') {
      //   this.$refs.deviceTree.append(node, this.treeData[0]);
      // } else {
      //   // this.clickNode..insertAfter(node);
      // }
      this.$refs.deviceTree.append(node, this.clickNode);
      if (this.currentComponent == 'station') {
        this.$refs.currentPage.getDeviceInfo(true);
      }
    },
    // 根据权限标识和外部指示状态进行权限判断
    show (perms) {
      return this.showHandle(perms);
    },
    refresh () {
      this.$refs.currentPage.isEdit = false;
      this.$refs.currentPage.getDeviceInfo(true);
    },
    setChangeData (isRefresh) {
      this.changeData = true;
      if (isRefresh && this.selectNode.deviceType == '11') {
        this.getPsTreeMenu({ psId: this.psInfo.psId });
      }
    }
  }
};
</script>
<style lang="less" scoped>
.ps-tree {
  width: 100%;
  margin-bottom:16px;
}
.top-btn {
  margin-bottom: 16px;
  .right-btn {
    position: absolute;
    right: 24px;
  }
}
.unSelectable {
  color: #9D9997;
  pointer-events: none;
}
.main-tree {
  overflow: auto;
}
:deep(.ant-tree li.drag-over-gap-bottom > span) {
  border-bottom: 1px solid transparent !important;
}
:deep(.ant-tree li.drag-over-gap-top > span) {
  border-top: 1px solid transparent !important;
}
.custom-tree-node {
    overflow: hidden;
    text-overflow: ellipsis;
}
</style>
