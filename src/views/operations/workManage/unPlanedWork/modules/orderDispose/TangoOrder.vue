<template>
  <div class="view_detail_box">
    <a-spin :spinning="loading">
      <div class="title">
        <span class="before"></span>
        <span>工单处理</span>
      </div>
      <div class="content">
        <a-tabs v-model="searchData.defectType" @change="sizeChange(1, 10)">
          <a-tab-pane key="1" :tab="'严重缺陷（' + seriousNum + '）'">
          </a-tab-pane>
          <a-tab-pane key="2" :tab="'一般缺陷（' + normalNum + '）'">
          </a-tab-pane>
          <a-tab-pane key="3" :tab="'轻微缺陷（' + slightNum + '）'">
          </a-tab-pane>
        </a-tabs>
        <div v-for="(item, index) in faultData" :key="item.id">
          <div class="order-deal-head">
            <a-icon v-show="!item.expand" class="expand-icon" type="right" @click="expandFault(index)" />
            <a-icon v-show="item.expand" class="expand-icon" type="down" @click="expandFault(index)" />
            {{item.tagNo}} / {{getFaultTypeName(item.faultType)}}
            <span :class="item.handleStatus == '0' ? 'handle-status undeal' : 'handle-status'">
              {{item.handleStatus == '0' ? '未执行' : '已执行'}}
            </span>
          </div>
          <div v-if="item.expand">
            <a-form-model :ref="'disposeForm' + index" :model="item" :rules="disposeFormRules" :labelCol="{ style:'width: 180px' }" :wrapperCol="{ style:'width: calc(100% - 145px)' }">
              <a-row>
                <a-col :xl='8' :sm='12' :xs='24'>
                  <a-form-model-item label="处理结果" prop="resolveResult">
                    <a-radio-group v-model="item.resolveResult" :disabled="disabled">
                      <a-radio value="1">已完成</a-radio>
                      <a-radio value="2">待协调</a-radio>
                    </a-radio-group>
                  </a-form-model-item>
                </a-col>
                <a-col :xl='8' :sm='12' :xs='24' v-if="item.actualResolveTime">
                  <a-form-model-item label="实际完成时间" prop="actualResolveTime">
                    <a-date-picker disabled v-model="item.actualResolveTime" @change="getRepairTime(index)" :disabled-date="disabledRecoverTime"
                      showTime valueFormat="YYYY-MM-DD HH:mm" format="YYYY-MM-DD HH:mm" style="width:100%" placeholder="请选择" />
                  </a-form-model-item>
                </a-col>
                <a-col :xl='8' :sm='12' :xs='24' v-if="item.repairTime">
                  <a-form-model-item label="累计修复时间(h)" prop="repairTime">
                    <a-input-number :max="99999999.99" :min="0" :precision="2" disabled v-model="item.repairTime" placeholder="请输入" style="width:100%" />
                  </a-form-model-item>
                </a-col>
                <a-col :span="24">
                  <a-form-model-item label="情况说明" prop="orderResolveRemark">
                    <a-textarea :max-length="1000" :disabled="disabled" v-model="item.orderResolveRemark" :title="item.orderResolveRemark" placeholder="请输入" style="width:100%" />
                  </a-form-model-item>
                </a-col>
                <a-col :xl='8' :sm='12' :xs='24'>
                  <a-form-model-item label="现场情况" prop="sceneCondition">
                    <a-select :disabled="disabled" v-model="item.sceneCondition" :getPopupContainer="(node) => node.parentNode" placeholder="请选择现场情况">
                      <a-select-option v-for="item in dictMap.care_scene_condition" :key="item.dataValue">
                        {{ item.dataLable }}
                      </a-select-option>
                    </a-select>
                  </a-form-model-item>
                </a-col>
                <a-col :xl='8' :sm='12' :xs='24'>
                  <a-form-model-item label="故障停运容量(MWp)" prop="faultCapacityNum">
                    <a-input-number :max="999999.9999" :min="0" :precision="4"
                      :disabled="disabled" v-model="item.faultCapacityNum" placeholder="请输入" style="width:100%" />
                  </a-form-model-item>
                </a-col>
                <a-col :xl='8' :sm='12' :xs='24'>
                  <a-form-model-item label="累计损失电量(万KWh)" prop="lossNum">
                    <a-input-number :precision="4" :min="0" :max="9999999.999" :disabled="disabled" v-model="item.lossNum" placeholder="请输入" style="width:100%" />
                  </a-form-model-item>
                </a-col>
                <a-col :span="24">
                  <a-form-model-item label="附件">
                    <uploadFile :disabled="disabled" v-model="item.files" :maxNum="5" tip="最多上传5个文件,且上传的附件最大不超过10MB!">上传</uploadFile>
                  </a-form-model-item>
                </a-col>
                <a-col :span="24">
                  <a-form-model-item label="逾期原因" prop="overdueReason">
                    <a-textarea :max-length="1000" :disabled="disabled" v-model="item.overdueReason" :title="item.overdueReason" placeholder="请输入" style="width:100%" />
                  </a-form-model-item>
                </a-col>
              </a-row>
            </a-form-model>
            <div class="order-deal-btn" v-if="!disabled">
              <throttle-button label="保存" :loading="loading" @click="handleFault('0', index, item.id)" />
              <throttle-button label="执行" :loading="loading" @click="handleFault('1', index, item.id)" />
            </div>
          </div>

        </div>
        <page-pagination :pageSize="searchData.size" :current="searchData.curPage" :total="total" @size-change="sizeChange" />
      </div>
    </a-spin>
  </div>
</template>
<script>
import { faultPage, faultHandle, countDefectByOrderId } from '@/api/operations/unPlanedWork';
import uploadFile from '@/components/com/fileUploadView';
import moment from 'moment';
import initDict from '@/mixins/initDict';
import { TENANT_ID } from '@/store/mutation-types';
export default {
  name: 'TangoOrderDispose',
  mixins: [initDict],
  components: {
    uploadFile
  },
  props: {
    type: {
      type: String,
      default: ''
    },
    order: {
      type: Object,
      default: () => {
        return {};
      }
    },
    disabled: {
      type: Boolean,
      default: false
    }
  },
  data () {
    return {
      loading: false,
      seriousNum: 0,
      normalNum: 0,
      slightNum: 0,
      searchData: {
        defectType: '1',
        size: 10,
        curPage: 1
      },
      total: 0,
      faultData: [],
      disposeFormRules: {
        actualResolveTime: [{
          required: true,
          message: '请选择实际消除时间'
        }], // 实际消除时间
        resolveResult: [{
          required: true,
          message: '请选择处理结果'
        }], // 处理结果
        orderResolveRemark: [{
          required: true,
          message: '请输入情况说明'
        }], // 情况说明
        sceneCondition: [{
          required: true,
          message: '请选择现场情况'
        }], // 现场情况
        faultCapacityNum: [{
          required: true,
          message: '请输入故障停运容量'
        }], // 故障停运容量
        repairTime: [{
          required: false,
          message: '请输入累计修复时间'
        }], // 累计修复时间
        lossNum: [{
          required: true,
          message: '请输入累计损失电量'
        }] // 累计损失电量
      }
    };
  },
  created () {
    this.getDictMap('care_fault_type,care_scene_condition');
  },
  methods: {
    moment,
    // 页面初始化
    init (id) {
      this.searchData.orderId = id;
      this.getFaultList();
      this.getFaultCount();
    },
    // 获取缺陷列表数据
    getFaultList () {
      // this.$emit('loadPage','start');
      faultPage(this.searchData).then(res => {
        res.result_data.rows.forEach(item => {
          item.expand = false;
          item.sceneCondition = item.sceneCondition || undefined;
        });
        this.faultData = res.result_data.rows;
        this.loading = false;
        this.total = res.result_data.total;
      });
    },
    // 获取缺陷分类数量
    getFaultCount () {
      countDefectByOrderId({ orderId: this.order.id }).then(res => {
        this.seriousNum = res.result_data.defectType_1 || 0;
        this.normalNum = res.result_data.defectType_2 || 0;
        this.slightNum = res.result_data.defectType_3 || 0;
      });
    },
    // 展开、收起缺陷, 工单指派时不能展开
    expandFault (index) {
      this.faultData[index].expand = !this.faultData[index].expand;
    },
    // 设置repairTime
    getRepairTime (index) {
      if (this.faultData[index].actualResolveTime && this.faultData[index].distinguishTime) {
        let time = (new Date(this.faultData[index].actualResolveTime).getTime()) - (new Date(this.faultData[index].distinguishTime).getTime());
        this.faultData[index].repairTime = (time / 1000 / 60 / 60).toFixed(2);
      } else {
        this.faultData[index].repairTime = '';
      }
    },
    // 实际消除时间不可小于发现时间
    disabledRecoverTime (endValue) {
      const startValue = moment(this.order.findTime).format('YYYY-MM-DD HH:mm');
      if (!endValue || !startValue) {
        return false;
      }
      return moment(endValue, 'YYYY-MM-DD HH:mm').valueOf() <= moment(startValue, 'YYYY-MM-DD HH:mm').valueOf();
    },
    // 缺陷保存、提交
    handleFault (submitType, index, faultId) {
      this.$refs['disposeForm' + index][0].validate(valid => {
        if (valid) {
          if (this.faultData[index].resolveResult == '2' && submitType == '1') {
            this.$message.warning('选择协调后不能提交，请点击保存');
            return;
          }
          let obj = {
            orderId: this.order.id,
            faultId: faultId,
            processInstanceId: faultId,
            taskId: this.order.taskId,
            submitType: submitType,
            sysTenantId: Vue.ls.get(TENANT_ID)
          };
          obj = { ...this.faultData[index], ...obj };
          this.loading = true;
          faultHandle(obj).then(res => {
            this.$message.success(submitType == '0' ? '保存成功' : '执行成功');
            if (submitType == '1') {
              this.getFaultList();
            }
            this.loading = false;
          }).catch(() => { this.loading = false; });
        }
      });
    },
    // 获取故障名称
    getFaultTypeName (val) {
      let faultTypeName = '';
      this.dictMap.care_fault_type.forEach(item => {
        if (item.dataValue == val) {
          faultTypeName = item.dataLable;
        }
      });
      return faultTypeName;
    },
    // 分页事件
    sizeChange (page, size) {
      this.searchData.curPage = page;
      this.searchData.size = size;
      this.getFaultList();
    },
    // 重置变量
    reset () {
      this.faultData = [];
      this.searchData.defectType = '1';
    }
  }
};
</script>

<style lang="less" scoped>
  .view_detail_box {
    border-top: 1px solid #f2f2f2;
    padding: 20px 0;

    .content {
      padding: 0 12px;
    }
  }

  .order-deal-head {
    padding: 10px;
    background: #f0f0f0;
    margin-bottom: 24px;

    .expand-icon {
      font-size: 16px;
      margin-right: 8px;
      cursor: pointer;
    }

    .handle-status {
      position: absolute;
      right: 16px;

    }

    .undeal {
      color: #FAAD14;
    }

  }

  .order-deal-btn {
    text-align: center;
    margin-bottom: 24px;
    padding-left: 32px;

    button+button {
      margin-left: 16px;
    }
  }

  .title {
    font-size: 16px;
    font-weight: 500;
    color: #5b5b5b;
    padding-bottom: 12px;

    .before {
      width: 4px;
      height: 15px;
      border-left: 4px solid #FF8F33;
      margin-right: 16px;
      border-radius: 0px 2px 2px 0px;
    }
  }

  :root[data-theme='dark'] {
    .order-deal-head {
      background: rgba(78, 121, 177, 0.1);

      .undeal {
        color: #00D5FF;
      }
    }

    .title {
      color: #fff;

      .before {
        border-left-color: #60CAFE;
      }
    }

  }
</style>
