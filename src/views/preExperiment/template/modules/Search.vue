<template>
  <a-row :gutter="24">
    <a-col :xxl="5" :xl="8" :md="12">
      <div class="search-item">
        <span class="search-label">设备类型：</span>
        <a-cascader
          v-model="queryParams.deviceTypeIdArr"
          :options="deviceTypeList"
          style="width: 100%"
          :fieldNames="{label: 'deviceName', value: 'id', children: 'childErpDeviceTypeInfo'}"
          placeholder="请选择"
          :show-search='true'
          change-on-select
        ></a-cascader>
      </div>
    </a-col>
    <a-col :xxl="5" :xl="8" :md="12">
      <div class="search-item">
        <span class="search-label">试验项目：</span>
        <a-select v-model="queryParams.projectName" placeholder="请选择" :filter-option="$filterOption"
        option-filter-prop="children" :show-search='true' style="width: 100%; height: 32px" allowClear>
          <a-select-option v-for="(item,index) in dictMap.pre_test_project_type" :key="index" :value="item.dataValue">
            {{ item.dataLable }}
          </a-select-option>
        </a-select>
      </div>
    </a-col>
    <a-col :xxl="4" :xl="8" :md="8">
      <div class="search-item">
        <throttle-button label="查询" @click="query" />
        <throttle-button label="重置" @click='reset' class="solar-eye-btn-primary-cancel"/>
      </div>
    </a-col>
  </a-row>
</template>
<script>
import { getParentDeviceTypeInfoList } from '@api/isolarErp/equipment/type';
import initDict from '@/mixins/initDict';
import { debounce } from 'xe-utils';

export default {
  mixins: [ initDict ],
  inject: ['tarnsformDeviceFn'],
  data () {
    return {
      deviceTypeList: [],
      queryParams: {
        // deviceTypeId: undefined,
        projectName: undefined,
        deviceTypeIdArr: [],
        deviceAncestors: undefined
      }
    };
  },
  mounted () {
    this.getDictMap('pre_test_project_type');
    this.initQuerySearchData();
  },
  methods: {
    initQuerySearchData () {
      getParentDeviceTypeInfoList({ 'needAllChild': '1', 'levelLimitCheck': '2' }).then(res => {
        if (res.result_code === '1' && res.result_data) {
          this.deviceTypeList = this.tarnsformDeviceFn(res.result_data);
        } else {
          this.deviceTypeList = [];
        }
      });
    },
    reset () {
      this.queryParams = this.$options.data().queryParams;
      debounce(this.$emit('queryList'));
    },
    query () {
      const { deviceTypeIdArr } = this.queryParams;
      // const len = deviceTypeIdArr.length;
      // const deviceTypeId = deviceTypeIdArr[len - 1];
      const deviceAncestors = deviceTypeIdArr.join(',');
      const param = { deviceAncestors };
      Object.assign(this.queryParams, param);
      debounce(this.$emit('queryList'));
    }
  }
};
</script>
