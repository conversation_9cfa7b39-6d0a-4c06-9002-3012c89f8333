import {
  axios
} from '@/utils/gy-request';

/**
 * @param parameter
 * @returns {*}
 */
export function addressBookPage (parameter) {
  return axios({
    url: '/planManage/erpSecAddressBook/pageList',
    method: 'post',
    data: parameter
  });
}
//
export function addressBookItemInfo (parameter) {
  return axios({
    url: '/planManage/erpSecAddressBook/itemInfo',
    method: 'post',
    data: parameter
  });
}

export function addressBookType (parameter) {
  return axios({
    url: '/planManage/erpSecAddressBook/getDictData',
    method: 'post',
    data: parameter
  });
}

export function addressBookAddOrUpdate (parameter) {
  return axios({
    url: '/planManage/erpSecAddressBook/addOrUpdate',
    method: 'post',
    data: parameter
  });
}

export function addressBookDelete (parameter) {
  return axios({
    url: '/planManage/erpSecAddressBook/delete',
    method: 'post',
    data: parameter
  });
}
