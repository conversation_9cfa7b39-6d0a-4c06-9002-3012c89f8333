{"name": "solareye-health", "version": "1.1.0", "private": true, "scripts": {"pre": "cnpm install || yarn --registry https://registry.npm.taobao.org || npm install --registry https://registry.npm.taobao.org ", "serve": "vue-cli-service serve", "build:dev": "vue-cli-service build --mode dev", "build:fat": "vue-cli-service build --mode fat", "build:uat": "vue-cli-service build --mode uat", "build:sandbox2": "vue-cli-service build --mode sandbox2", "build:show": "vue-cli-service build --mode show", "build:pro": "vue-cli-service build --mode pro", "build": "vue-cli-service build", "lint": "vue-cli-service lint", "dll": "webpack --config webpack.dll.js ", "prepare": "husky install", "lint-staged": "lint-staged", "commitlint": "commitlint --config commitlint.config.js -e -V"}, "dependencies": {"@aliyun-sls/web-track-browser": "^0.2.6", "@form-create/ant-design-vue": "^2.5.0-alpha.4", "@wangeditor/editor": "^5.1.1", "@wangeditor/editor-for-vue": "^1.0.2", "@ztree/ztree_v3": "^3.5.46", "ant-design-vue": "^1.7.2", "area-data": "^5.0.6", "clean-webpack-plugin": "^3.0.0", "colormap": "2.3.2", "crypto-js": "^4.0.0", "dayjs": "^1.8.0", "dom-align": "1.12.0", "echarts": "^5.0.2", "echarts-extension-amap": "^1.9.3", "enquire.js": "^2.1.6", "html2canvas": "^1.0.0-rc.7", "intro.js": "^7.2.0", "jspdf": "^2.3.1", "lodash.get": "^4.4.2", "lodash.pick": "^4.4.0", "nprogress": "^0.2.0", "qiankun": "^2.8.4", "uglifyjs-webpack-plugin": "^2.2.0", "v-viewer": "^1.6.4", "vue-awesome-swiper": "^3.1.3", "vue-clipboard2": "^0.3.3", "vue-count-to": "^1.0.13", "vue-i18n": "^8.7.0", "vue-infinite-loading": "^2.4.5", "vue-loader": "^15.7.0", "vue-ls": "^3.2.0", "vue-template-compiler": "^2.6.14", "vue-video-player": "^5.0.2", "vue-virtual-scroll-list": "^2.3.5", "vue-virtual-scroller": "^1.0.10", "vue2-viewer": "^1.0.3", "vuedraggable": "^2.20.0", "vxe-table": "3.1.0", "vxe-table-plugin-antd": "^1.11.2", "wavesurfer.js": "6.6.4", "webpack-parallel-uglify-plugin": "^2.0.0", "wl-core": "^1.1.9", "wujie-polyfill": "^1.1.0", "wujie-vue2": "^1.0.22", "xe-utils": "^3.3.1", "xlsx": "^0.18.5"}, "devDependencies": {"@babel/polyfill": "^7.2.5", "@commitlint/cli": "^17.3.0", "@commitlint/config-conventional": "^17.3.0", "@vue/cli-plugin-babel": "^3.3.0", "@vue/cli-plugin-eslint": "^3.3.0", "@vue/cli-service": "^3.3.0", "@vue/eslint-config-standard": "^4.0.0", "babel-eslint": "7.2.3", "babel-plugin-import": "^1.13.3", "babel-plugin-transform-require-context": "^0.1.1", "compression-webpack-plugin": "^3.1.0", "eslint": "^5.16.0", "eslint-plugin-vue": "^5.1.0", "html-webpack-plugin": "^5.5.0", "husky": "^8.0.2", "less": "^3.9.0", "less-loader": "^4.1.0", "lint-staged": "13.1.0", "mini-css-extract-plugin": "^2.6.1", "svg-sprite-loader": "^4.1.6", "webpack-bundle-analyzer": "^4.3.0", "webpack-cli": "^4.10.0"}, "eslintConfig": {"root": true, "env": {"node": true}, "extends": ["plugin:vue/strongly-recommended", "@vue/standard"], "parserOptions": {"parser": "babel-es<PERSON>"}, "rules": {"generator-star-spacing": "off", "no-mixed-operators": 0, "vue/max-attributes-per-line": [2, {"singleline": 5, "multiline": {"max": 1, "allowFirstLine": false}}], "vue/attribute-hyphenation": 0, "vue/html-self-closing": 0, "vue/component-name-in-template-casing": 0, "vue/html-closing-bracket-spacing": 0, "vue/singleline-html-element-content-newline": 0, "vue/no-unused-components": 0, "vue/multiline-html-element-content-newline": 0, "vue/no-use-v-if-with-v-for": 0, "vue/html-closing-bracket-newline": 0, "vue/no-parsing-error": 0, "no-tabs": 0, "indent": ["off", 2], "no-console": 0, "space-before-function-paren": 0}}, "postcss": {"plugins": {"autoprefixer": {}}}, "browserslist": ["> 1%", "last 2 versions", "not ie <= 10"]}