<template>
<div>
   <div id="timeAalysisEchart" ref="chart" :style="{height: getChartHeight}">
  </div>
</div>

</template>
<script>
import echarts from '@/utils/enquireEchart';
let myEchart;
export default {
  name: 'TimeAalysisChart',
  data () {
    return {
      option: {},
      color: [],
      chartData: [],
      value: '111'
    };
  },
  computed: {
    // 获取图表高度
    getChartHeight () {
      let height = document.body.offsetHeight - (this.isShowMenu ? 380 : 256) + 'px';
      return height;
    },
    navTheme () {
      return this.$store.state.app.theme;
    },
    isShowMenu () {
      return this.$store.state.user.isShowMenu;
    }
  },
  watch: {
    'navTheme' (color) {
      let isDarkColor = this.navTheme == 'dark' ? '#ffffff' : '#333';
      this.option.legend[0].textStyle.color = isDarkColor;
      this.option.legend[0].pageIconColor = isDarkColor;
      this.option.legend[0].pageTextStyle.color = isDarkColor;
      if (myEchart) {
        this.refreshChart(this.chartData);
      }
    }
  },
  mouted () {

  },
  beforeDestroy () {
    myEchart = null;
  },
  methods: {
    // 时序分析图表初始化
    drawLine (setEventFlag) {
      let chartDom = document.getElementById('timeAalysisEchart');
      if (!chartDom) return;
      if (!myEchart) {
        myEchart = echarts.init(chartDom);
      }
      this.setOption();
      if (!setEventFlag) {
        myEchart.on('legendselectchanged', params => {
          let flag = true;
          this.option.series.forEach(item => {
            if (params.selected[item.name] && flag) {
              flag = false;
              item.markArea.emphasis.itemStyle.opacity = 0.2;
              item.markArea.itemStyle.opacity = 0.2;
            } else {
              item.markArea.emphasis.itemStyle.opacity = 0;
              item.markArea.itemStyle.opacity = 0;
            }
          });
          myEchart.setOption(this.option);
        });
      }

      this.option && myEchart.setOption(this.option);
    },
    // 时序分析图表更新
    refreshChart (chartData) {
      let chartDom = document.getElementById('timeAalysisEchart');
      if (!chartDom) return;
      if (!myEchart) {
        myEchart = echarts.init(chartDom);
      }
      this.chartData = chartData;
      let yAxis = [];
      let isDark = this.navTheme === 'dark';
      if (chartData.legend[0] && chartData.legend[0].data) {
        this.color = chartData.irradiateCount == 1 ? ['#ff8f33'] : [];
        if (chartData.remoteCount == 1) {
          this.color.push(isDark ? this.randomLightHexColor() : this.randomHexColor());
        }
        for (let k = 0; k < chartData.faultPointCount; k++) {
          this.color.push('red');
        }
        for (let i = 0; i < chartData.legend[0].data.length - chartData.irradiateCount - chartData.faultPointCount - chartData.remoteCount - chartData.enableCount; i++) {
          if (chartData.faultPointCount > 0) {
            this.color.push('#75d874');
          } else {
            this.color.push(isDark ? this.randomLightHexColor() : this.randomHexColor());
          }
        }
        for (let j = 0; j < chartData.enableCount; j++) {
          this.color.push('#9b9b9b');
        }
        chartData.unitArr.forEach(item => {
          yAxis.push({
            scale: true,
            show: false,
            splitLine: {
              show: true
            }
          });
        });
      }
      this.setOption(chartData.deviceType);
      this.option.yAxis = yAxis;
      this.option.legend = chartData.legend;
      this.option.xAxis.data = chartData.xAxisData;
      this.option.series = chartData.series;
      myEchart.setOption(this.option);
      myEchart.on('datazoom', function (params) {
        myEchart.dispatchAction({
          type: 'hideTip'
        });
      });
    },
    // 随机生成颜色较深的十六进制颜色
    randomHexColor () {
      let arr = ['0', '1', '2', '3', '4', '5', '6', '7', '8', '9', 'a', 'b'];
      let str = '#';
      for (let i = 1; i <= 6; i++) {
        let index = Math.floor(Math.random() * 12);
        str += arr[index];
      }
      return str;
    },
    // 颜色较浅的
    randomLightHexColor () {
      return '#' + Math.floor(Math.random() * 0xffffff).toString(16);
    },
    // 调整图表大小
    setChartSize () {
      if (myEchart) {
        myEchart.resize();
      }
    },
    // 设置图表配置项
    setOption (deviceType) {
      let isDark = this.navTheme == 'dark';
      this.option = {
        color: this.color,
        legend: [{
          itemStyle: {
            color: isDark ? '#fff' : '#9b9b9b'
          }
        }],
        tooltip: {
          appendToBody: true,
          show: true,
          trigger: 'axis',
          triggerOn: 'mousemove',
          enterable: true,
          formatter: (params) => {
            let result = '';
            if (params.length > 0) {
              result = '<div style="display:flex;margin-bottom:12px;"><span class="time">' + params[0].axisValue + '</span>';
              if (params[0].data.alarmArr.length > 0) {
                let status = params[0].data.statusSign;
                result += '<div class="station-starus">' + this.getStationStatus(status) + '</div></div>';
                params[0].data.alarmArr.forEach(item => {
                  result += '<div class="alarm-data"><div class="data-content">' + item.alarmReason + '<div class="split-line"></div>' + '时长：' + item.duration + '</div></div>';
                });
              } else {
                result += '</div>';
              }
              let irradiateData = params.find(item => { return item.data.point == 'p2007'; });
              result += '<div style="display:flex;">';

              if (irradiateData) {
                result += '<div style="margin-bottom:12px;margin-right: 16px">' + irradiateData.marker + irradiateData.data.pointName + '：' +
                  ((irradiateData.value && (irradiateData.value * 1).toFixed(2)) || '--') + irradiateData.data.unit + '</div>';
              }
              let remoteData = params.find(item => { return item.data.pointType == '1'; });
              if (remoteData) {
                result += '<div style="margin-bottom:12px;">' + remoteData.marker + remoteData.data.pointName + '：' +
                  (remoteData.value || '--') + remoteData.data.unit + '</div>';
              }
              result += '</div>';
              let data = params.filter(item => {
                return item.data.point != 'stop' && item.data.point != 'p2007' && item.data.pointType != '1';
              });
              let arr = [];
              let pointsUnitChange = ['p1', 'p2', 'p24', 'p25', 'p14', 'p1006'];
              let obj = {
                deviceName: '',
                points: [],
                datas: []
              };
              data.forEach(item => {
                let index = arr.findIndex(o => o.deviceName == item.data.deviceName);
                let obj = index != -1 ? arr[index] : {
                  deviceName: item.data.deviceName,
                  points: [],
                  datas: []
                };
                let value = '0';
                let unit = item.data.unit == 'var' ? 'Var' : item.data.unit;
                if (pointsUnitChange.includes(item.data.point)) {
                  unit = 'k' + unit;
                  value = item.value ? parseFloat((item.value / 1000).toFixed(4)) : '--';
                } else if (item.data.pointType != '1') {
                  value = item.value ? (item.value * 1).toFixed(2) : '--';
                } else {
                  value = item.value || '--';
                }
                if (item.data.isFaultPoint) {
                  obj.points.push(item.marker + '<span style="color:red !important;">' + item.data.pointName + '</span>');
                  obj.datas.push('<span style="color:red !important;">' + value + unit + '</span>');
                } else {
                  obj.points.push(item.marker + item.data.pointName);
                  obj.datas.push(value + unit);
                }
                if (index == -1) {
                  arr.push(obj);
                }
              });

              if (data.length > 0) {
                arr.push(obj);
                if (deviceType == '4' || deviceType == '10') {
                  result += '<div style="font-weight: 600;margin-top:-4px;">' + arr[0].deviceName + '</div>';
                  result += '<div style="display:flex">';
                  let pointStr1 = this.getPointStr(arr[0].points, 3, 0, 12);
                  let dataStr1 = this.getPointStr(arr[0].datas, 3, 0, 24);
                  let pointStr2 = this.getPointStr(arr[0].points, 3, 1, 12);
                  let dataStr2 = this.getPointStr(arr[0].datas, 3, 1, 24);
                  let pointStr3 = this.getPointStr(arr[0].points, 3, 2, 12);
                  let dataStr3 = this.getPointStr(arr[0].datas, 3, 2, 0);
                  result += pointStr1 + dataStr1 + pointStr2 + dataStr2 + pointStr3 + dataStr3 + '</div>';
                } else {
                  let strList = [];
                  arr.forEach(item => {
                    let str = '<div style="margin-right:24px;">' + '<div style="font-weight: 600;">' + item.deviceName +
                              '</div>' + '<div style="display:flex"><div style="margin-right:16px">';
                    item.points.forEach(ele => {
                      str += '<div style="margin-top:4px;">' + ele + '</div>';
                    });
                    str += '</div><div>';
                    item.datas.forEach(ele => {
                      str += '<div style="margin-top:4px;">' + ele + '</div>';
                    });
                    str += '</div></div></div>';
                    strList.push(str);
                  });
                  result += '<div style="margin-top: -8px">';
                  strList.forEach((item, num) => {
                    if (num % 3 == 0) {
                      result += `<div style="display:flex;margin-top:4px;"> ${item}`;
                    } else if (num % 3 == 1) {
                      result += item;
                    } else {
                      result += `${item}</div>`;
                    }
                  });
                  if (strList.length % 3 != 0) {
                    result += '</div>';
                  }
                  result += '</div>';
                }
              }
            }
            return result;
          },
          className: 'solar-eye-tooptip alarm-events-tooptips'
        },
        xAxis: {
          type: 'category',
          position: 'bottom',
          data: [],
          axisLine: { lineStyle: { color: isDark ? 'white' : '#8392A5' } },
          axisLabel: {
            textStyle: {
              color: () => {
                return '#8392A5';
              }
            }
          }
        },
        yAxis: {
          scale: true,
          show: true,
          axisLine: { lineStyle: { color: isDark ? 'white' : '#8392A5' } },
          splitLine: {
            show: true
          }
        },
        grid: {
          bottom: 80
        },
        dataZoom: [
          {
            textStyle: {
              color: '#8392A5'
            },
            dataBackground: {
              areaStyle: {
                color: '#8392A5'
              },
              lineStyle: {
                opacity: 0.8,
                color: '#8392A5'
              }
            },
            brushSelect: true
          },
          {
            type: 'inside'
          }
        ],
        series: []
      };
    },
    // 销毁图表实例
    destroyChart () {
      if (myEchart) {
        document.getElementById('timeAalysisEchart') && echarts.init(document.getElementById('timeAalysisEchart')).dispose();
        myEchart = null;
      }
    },
    getStationStatus (status) {
      if (status == '') {
        status = '正常运行';
      } else if (status == '停机故障') {
        status = '故障停机';
      } else if (status == '故障运行' && this.chartData.type == '4') {
        status = '隐患运行';
      }
      return status;
    },
    getPointStr (data, row, num, margin) {
      let arr = data.filter((item, index) => { return index % row == num; });
      let str = '<div style="margin-right:' + margin + 'px;">';
      arr.forEach((item) => {
        str += '<div style="margin-top:4px;">' + item + '</div>';
      });
      str += '</div>';
      return str;
    },
    // 获取最大宽度数值
    getmaxWidth (obj, index) {
      let maxWidth = 0;
      switch (index % 4) {
        case 1:
          maxWidth = obj.maxWidth1;
          break;
        case 2:
          maxWidth = obj.maxWidth2;
          break;
        case 3:
          maxWidth = obj.maxWidth3;
          break;
        case 0:
          maxWidth = obj.maxWidth4;
          break;
      }
      return maxWidth;
    },
    // 时序分析-获取天气图标
    mapWeatherIconById (conditionId) {
      if (conditionId >= 0) {
        return require(`@/assets/images/weather/W${conditionId}.png`);
      }
      return null;
    }
  }
};

</script>
<style lang="less" scoped>
#timeAalysisEchart {
  width: 100%;
  padding: 0 24px;
}
.chart-title {
  padding-left:16px;
  font-weight:bold;
  margin:24px 0 12px;
}
.weather-info {
  position: absolute;
  left: 126px;
  top: 16px;
  padding: 4px 20px;
  border-radius: 16px;
  .ant-space {
    cursor: pointer;
  }
}
.weather-icon {
  height: 30px;
  object-fit: contain;
}
.operation-icon {
  position: absolute;
  right: 24px;
  top: 14px;
  font-size: 22px;
  svg {
    margin-left: 6px;
    cursor: pointer;
  }
}
</style>
