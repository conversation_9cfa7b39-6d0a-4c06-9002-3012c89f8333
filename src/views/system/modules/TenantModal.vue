<template>
  <a-modal
    :title="title"
    :width="width"
    :visible="visible"
    switchFullscreen
    @ok="handleOk"
    :okButtonProps="{ class:{'solareye-hidden': disableSubmit} }"
    @cancel="handleCancel"
     okText= "确定"
    cancelText="关闭">
    <tenant-form ref="realForm" @ok="submitCallback" :disabled="disableSubmit" normal></tenant-form>
  </a-modal>
</template>

<script>

import TenantForm from './TenantForm';
export default {
  name: 'TenantModal',
  components: {
    TenantForm
  },
  data () {
    return {
      title: '',
      width: 800,
      visible: false,
      disableSubmit: false
    };
  },
  methods: {
    add () {
      this.visible = true;
      this.$nextTick(() => {
        this.$refs.realForm.add();
      });
    },
    edit (record) {
      this.visible = true;
      this.$nextTick(() => {
        this.$refs.realForm.edit(record);
      });
    },
    close () {
      this.$emit('close');
      this.visible = false;
    },
    handleOk () {
      this.$refs.realForm.submitForm();
    },
    submitCallback () {
      this.$emit('ok');
      this.visible = false;
    },
    handleCancel () {
      this.close();
    }
  }
};
</script>
