import {
  postAction
} from '@/api/common_gy/erp-manage';
/// / import Vue from 'vue'

// 新增计划
export const addPlan = (params) => postAction('/planManage/addPlan', params);

export const planList = (params) => postAction('/planManage/planList', params);
// 获取编码
export const getCode = (params) => postAction('/planManage/getPlanCode', params);

// 修改计划
export const updatePlan = (params) => postAction('/planManage/updatePlan', params);

// 删除计划
export const deletePlan = (params) => postAction('/planManage/deletePlan', params);

export const batchSubmit = (params) => postAction('/planManage/batchSubmit', params);

export const batchAudit = (params) => postAction('/planManage/batchAudit', params);

export const getPlanById = (params) => postAction('/planManage/getPlanById', params);

export const selectByParam = (params) => postAction('/inspectionCycle/selectByParam', params);

export const itemPage = (params) => postAction('/inspectionItem/page', params);

export const itemAdd = (params) => postAction('/inspectionItem/add', params);

export const itemUpdate = (params) => postAction('/inspectionItem/update', params);

export const itemGetOne = (params) => postAction('/inspectionItem/itemInfo', params);

export const batchDel = (params) => postAction('/inspectionItem/batchDel', params);

export const cyclePage = (params) => postAction('/inspectionCycle/page', params);

export const cycleAdd = (params) => postAction('/inspectionCycle/add', params);

export const cycleUpdate = (params) => postAction('/inspectionCycle/update', params);

export const cycleOne = (params) => postAction('/inspectionCycle/selectByOne', params);

export const cycleDel = (params) => postAction('/inspectionCycle/del', params);

export const showProcessDiagram = (params) => postAction('/planManage/showProcessDiagram', params);

export const exportInfo = (params) => postAction('/planManage/export', params);

export const auditRecord = (params) => postAction('/planManage/auditRecord', params);
// 除草面积
export const getSpaceByPsId = (params) => postAction('/common/management/getSpaceByPsId', params);
