<!-- 设备类型 -->
<template>
<div >
  <a-spin :spinning="tabLoading">
    <!--工具栏-->
    <div  class="solar-eye-search-model">
     <a-row :gutter="24" class="solar-eye-search-content">
            <a-col :xxl="6" :xl="8" :md="12">
             <div class="search-item">
                <span class="search-label">设备类型</span>
          <a-input  size="default" v-model="deviceName" @blur="deviceName = $trim($event)" placeholder="请输入设备类型"
           allowClear></a-input>
        </div>
      </a-col>
      <a-col :xxl="6" :xl="8" :md="12">
        <div class="search-item">
          <span class="search-label">显示状态</span>
          <a-select size="default" v-model="deviceStatus" allowClear class="width-140">
            <a-select-option value="">全部</a-select-option>
            <a-select-option v-for="(item, index) in dictMap.device_status" :key="index" :value="item.codeValue">{{item.dispName}}</a-select-option>
          </a-select>
        </div>
      </a-col>
      <a-col :xxl="3" :xl="6" :md="8">
        <div class="search-item">
        <erp-button label="查询" perms="920114103101" size="default" class="solar-eye-btn-primary" @click="pageChange(1)"></erp-button>
        </div>
      </a-col>
    </a-row>
    </div>
    <div class="solar-eye-gap"></div>
      <a-col class="solar-eye-main-content">
      <!-- 操作按钮 -->
        <div class="operation" style="height: 32px">
          <div class="operation-btn">
          <erp-button label="新增" perms="920114103102" size="default" class="solar-eye-btn-primary" @click="addClick"></erp-button>
          <erp-button :label="showAll ? '展开' : '收起'" perms="920114103103" size="default" type="primary" icon="unordered-list" @click="showAllClick(showAll)"></erp-button>
        </div>
      </div>
        <!--表格渲染-->
        <vxe-table :data="tabData" resizable ref="multipleTable" border align="center" :tree-config="{children: 'childErpDeviceTypeInfo'}"
         :height="tableHeight - 24" size="small" show-overflow highlight-hover-row>
          <vxe-table-column show-overflow="title" :formatter="tabFormatter" field="deviceName" title="设备类型" align="center" min-width="320" tree-node></vxe-table-column>
          <vxe-table-column show-overflow="title" field="deviceUnit" title="单位" width="80">
            <template v-slot="{ row }">
              <span>{{getLabel(row.deviceUnit,dictMap.unit)}}</span>
            </template>
          </vxe-table-column>
          <vxe-table-column show-overflow="title" :formatter="tabFormatter" field="deviceStatusName" title="显示状态" width="180"></vxe-table-column>
          <vxe-table-column show-overflow="title" :formatter="tabFormatter" field="createUserName" title="创建人" width="180"></vxe-table-column>
          <vxe-table-column show-overflow="title" :formatter="tabFormatter" field="createTime" title="创建时间" width="180"></vxe-table-column>
          <vxe-table-column show-overflow="title" :formatter="tabFormatter" field="updateUserName" title="修改人" width="180"></vxe-table-column>
          <vxe-table-column show-overflow="title" :formatter="tabFormatter" field="updateTime" title="修改时间" width="180"></vxe-table-column>
          <vxe-table-column :visible="showHandle(perms)" title="操作" width="160" :resizable="false" class-name="fixed-right-column-160">
            <template v-slot="{ row }">
              <erp-button perms="920114103104" icon="edit" @click="editClick(row, true)" title="编辑"></erp-button>
              <erp-button perms="920114103106" icon="file-text" @click="editClick(row, false)" title="详情"></erp-button>
              <erp-button perms="920114103105" v-show="showDel(row)" icon="delete" @click="deleteClick(row)" title="删除"></erp-button>
            </template>
          </vxe-table-column>
          <template v-slot:empty>
            <span class="com-color">查询无数据</span>
          </template>
        </vxe-table>
        <!--分页组件-->
        <page-pagination :pageSize="size" :current="page" :total="total" @size-change="sizeChange"/>
      </a-col>
    </a-spin>
    <!-- 新增、编辑 -->
    <a-modal v-model="detailOpen" :maskClosable="false" centered @cancel="cancel" :title="detailTitle" width="85%">
      <a-spin :spinning="saveLoad">
          <a-form-model :model="deviceForm" :rules="rules" ref="deviceForm" :label-col="{span: 6}" :wrapper-col="{span: 18}">
            <a-row :gutter="24">
              <a-col :sm="24" :md="10">
                <a-form-model-item label="设备类型" prop="deviceName">
                  <a-input size="default" v-model.trim="deviceForm.deviceName" :max-length="50" style="width: 100%"></a-input>
                  </a-form-model-item>
                  </a-col>
                  <a-col :sm="24" :md="4">
                    <div style="width: 100%;height: 1px;"></div>
                    </a-col>
                    <a-col :sm="24" :md="10">
                      <a-form-model-item label="设备类型父类:">
                        <a-cascader v-model="parentId" :options="deviceParent" change-on-select size="default" :field-names="{ label: 'deviceName', value: 'deviceId', children: 'childErpDeviceTypeInfo' }"
                        allowClear placeholder="请选择设备类型父类" style="width: 100%"></a-cascader>
                        </a-form-model-item>
                        </a-col>
                </a-row>
                <a-row>
                  <a-col :sm="24" :md="10">
                    <a-form-model-item label="单位" prop="deviceUnit">
                      <a-select size="default" v-model="deviceForm.deviceUnit" allowClear style="width: 100%">
                        <a-select-option v-for="(item, index) in dictMap.unit" :key="index" :value="item.codeValue">{{item.dispName}}</a-select-option>
                      </a-select>
                    </a-form-model-item>
                  </a-col>
                  <a-col :sm="24" :md="4">
                  <div style="width: 100%;height: 1px;"></div>
                  </a-col>
                  <a-col :sm="24" :md="10">
                    <a-form-model-item label="显示状态" prop="deviceStatus">
                      <a-select size="default" v-model="deviceForm.deviceStatus" allowClear style="width: 100%">
                        <a-select-option v-for="(item, index) in dictMap.device_status" :key="index" :value="item.codeValue" :title="item.setItemDataName">{{item.dispName}}</a-select-option>
                      </a-select>
                    </a-form-model-item>
                  </a-col>
                </a-row>
            <a-col :sm="24" :md="10">
              <a-form-model-item label="设备固有项目:" style="margin-bottom:10px">
                <a-input size="default" v-model="setItemDataName" @input="filterTable" style="width: 100%" suffix-icon="el-icon-search"></a-input>
                <div class="erp-group-layout">
                  <a-checkbox-group v-model="setItemDataIds">
                    <a-checkbox v-for="item in projectList" :key="item.setItemDataId" :value="item.setItemDataId" :title="item.setItemDataName">{{ item.setItemDataName }}</a-checkbox>
                    <div v-show="projectList.length==0" class="no-data com-color">查询无数据</div>
                  </a-checkbox-group>
                </div>
              </a-form-model-item>
            </a-col>
            <a-col :sm="24" :md="4" style="height: 40vh;display: flex;padding-top: 42px;">
              <div class="middle">
                <a-button type="primary" size="default" @click="add()" :disabled="setItemDataIds.length > 0 ? false : true">添加<a-icon
                   type="right"></a-icon></a-button>
                <br />
                <a-button type="primary" size="default" style="margin-top: 10px; margin-left: 0" @click="remove" :disabled="selectPorjIds.length > 0 ? false : true">移除<a-icon
                   type="left"></a-icon></a-button>
              </div>
            </a-col>
            <a-col :sm="24" :md="10">
              <a-col :span="16" style="line-height: 32px;margin-bottom: 8px;">
                <label class="com-color">已选项目</label>
              </a-col>
              <a-col :span="8" style="text-align: right;line-height: 32px;margin-bottom: 8px;">
                <label class="com-color">是否为必填</label>
              </a-col>
              <a-col :span="24">
                <div class="erp-group-layout">
                  <div style="display: flex" v-for="item in deviceForm.setItemList" :key="item.setItemDataId">
                    <a-checkbox :label="item.setItemDataId" @change="changeSelectProj(item.setItemDataId, $event)" style="width: 90%">
                      {{ item.setItemDataName }}
                    </a-checkbox>
                    <a-checkbox class="check-right" v-model="item.isRequired" :true-label="1" :false-label="0"></a-checkbox>
                  </div>
                </div>
              </a-col>
            </a-col>
          </a-form-model>
      </a-spin>
      <template slot="footer">
        <div class="modal-footer">
          <a-button size="default" :loading="saveLoad" type="primary" @click="submitForm()">保存</a-button>
          <a-button size="default" :disabled="saveLoad" @click="resetForm(true)">取消</a-button>
        </div>
      </template>
    </a-modal>

    <Detail v-model="detailDialog" :typeDetail="deviceForm" :dictMap="dictMap"></Detail>
    </div>
</template>

<script>
import {
  queryList,
  insertErpDeviceType,
  getParentDeviceTypeInfoList,
  updateErpDeviceType,
  deleteErpDeviceType,
  getErpDeviceTypeDetail
} from '@/api/isolarErp/equipment/type';
import initDict from '@/mixins/initDict';
import tableHeight from '@/mixins/tableHeightAndSearchModel';
import Detail from './Detail';
export default {
  mixins: [initDict, tableHeight],
  components: {
    Detail
  },
  data () {
    var validateEquipmentType = (rule, value, callback) => {
      if (value && /[\/\\"<>{}|:.\?\*]/gi.test(value)) {
        callback(new Error('设备类型暂不支持 /\\ : < > { } . * ? " < > |'));
      } else {
        callback();
      }
      // }
    };
    return {
      perms: '920114103104,920114103106,920114103105',
      showAll: true,
      deviceStatus: '',
      deviceUnit: '',
      deviceName: '',
      tabLoading: false,
      tabData: [],
      total: 0,
      size: 10,
      page: 1,
      addLoad: false,
      // 弹窗
      saveLoad: false,
      detailOpen: false,
      detailTitle: '',
      addOrEdit: '',
      setItemDataIds: [],
      deviceForm: {
        id: '',
        deviceName: '',
        parentId: '',
        deviceStatus: '1',
        deviceUnit: '',
        setItemList: []
      },
      detailDialog: false,
      projectList: [],
      selectProj: [],
      deviceParent: [],
      rules: {
        deviceName: [{
          required: true,
          message: '请输入设备类型',
          trigger: 'blur'
        }, {
          validator: validateEquipmentType,
          trigger: 'blur'
        },
        {
          min: 1,
          max: 50,
          message: '长度在1到50个字符',
          trigger: 'blur'
        }
        ],
        deviceUnit: [{
          required: true,
          message: '请选择单位',
          trigger: 'change'
        } ],
        deviceStatus: [{
          required: true,
          message: '请选择显示状态',
          trigger: 'change'
        }]
      },
      setItemDataName: '',
      filterProject: [],
      selectPorjIds: [],
      parentId: '',
      projectLoading: true
    };
  },
  updated () {
    ;
  },
  created () {
    // 获取字典
    this.getDictMap('device_status,unit,device_type_set_item');
  },
  mounted () {
    this.pageChange(1);
  },
  methods: {
    // 查询
    async queryClick () {
      this.tabLoading = true;
      let $table = this.$refs.multipleTable;
      if ($table) {
        $table && await $table.clearScroll();
        $table.clearTreeExpand();
      }
      this.showAll = true;
      let map = {
        deviceName: this.deviceName,
        deviceStatus: this.deviceStatus,
        curPage: this.page,
        size: this.size
      };
      queryList(map)
        .then((res) => {
          if (res.result_code == '1' && res.result_data.total > 0) {
            this.tabData = res.result_data.rows;
            this.total = res.result_data.total;
          } else {
            this.tabData = [];
            this.total = 0;
          }
          this.tabLoading = false;
        })
        .catch(() => {
          this.total = 0;
          this.tabData = [];
          this.tabLoading = false;
        });
    },
    // 新增
    addClick () {
      this.resetForm(true);
      this.addOrEdit = 'add';
      this.initParentDeviceTypeInfoList();
      this.detailTitle = '新增';
      this.detailOpen = true;
      this.getProjectList([]);
    },
    // 保存
    submitForm () {
      this.saveLoad = true;
      this.$refs['deviceForm'].validate((valid) => {
        if (valid) {
          this.save();
        } else {
          this.saveLoad = false;
          return false;
        }
      });
    },
    // 保存数据
    save () {
      this.deviceForm.setItemList.forEach(item => {
        item.isRequired = (item.isRequired ? '1' : '0');
      });
      if (this.addOrEdit == 'add') {
        // 新增
        this.deviceForm.parentId = this.parentId[this.parentId.length - 1];
        if (this.deviceForm.parentId != '') {
          for (var i = 0; i < this.deviceParent.length; i++) {
            if (this.deviceParent[i].id == this.deviceForm.parentId) {
              this.deviceForm.deviceParentName = this.deviceParent[i].deviceName;
            }
          }
        }
        insertErpDeviceType(this.deviceForm)
          .then((res) => {
            this.saveLoad = false;
            if (res.result_code == '1') {
              this.resetForm(false);
              this.$message.success('操作成功');
            } else {
              this.$message.warning(res.result_msg);
            }
          }).catch(() => {
            this.saveLoad = false;
          });
      } else {
        this.deviceForm.parentId = typeof this.parentId == 'number' ? this.parentId : this.parentId[this.parentId.length - 1];
        if (this.deviceForm.parentId == 0) {
          // 修改前的子类设备名称
          this.deviceForm.oldDeviceChildName = this.deviceForm.oldDeviceName;
          for (let i = 0; i < this.deviceParent.length; i++) {
            if (this.deviceParent[i].id == this.deviceForm.parentId) {
              // 修改前的父类设备名称
              this.deviceForm.oldDeviceName = this.deviceParent[i].deviceName;
            }
          }
        }
        updateErpDeviceType(this.deviceForm)
          .then((res) => {
            this.saveLoad = false;
            if (res.result_code == '1') {
              this.resetForm(false);
              this.$message.success('操作成功');
            } else {
              this.$message.warning(res.result_msg);
            }
          }).catch(() => {
            this.saveLoad = false;
          });
      }
    },
    // 是否隐藏删除按钮
    showDel (row) {
      return !row.childErpDeviceTypeInfo || row.childErpDeviceTypeInfo.length == 0;
    },
    // 查看详情
    editClick (row, isEdit) {
      if (isEdit) {
        let parentId = row.parentId;
        let parent = this.tabData.filter((item) => item.id === parentId);
        if (parent.length == 1 && parent[0].deviceStatus == '0') {
          this.$message.warning('请先解除父类禁用状态');
          return;
        }
        this.addOrEdit = 'edit';
        this.parentId = '';
        this.initParentDeviceTypeInfoList();

        this.deviceForm.oldDeviceName = this.deviceForm.deviceName;
        this.detailTitle = '编辑';
        this.detailOpen = true;
      } else {
        this.deviceForm = {};
        this.detailDialog = true;
      }
      getErpDeviceTypeDetail({
        id: row.id
      }).then((res) => {
        if (res.result_code === '1') {
          let data = res.result_data;
          if (isEdit) {
            this.deviceForm = {
              id: data.id,
              deviceName: data.deviceName,
              parentId: data.parentDeviceId,
              deviceStatus: data.deviceStatus,
              deviceUnit: data.deviceUnit,
              setItemList: data.setItemList
            };
            let array = this.deviceParent;
            let parentDeviceId = data.parentDeviceId;
            this.parentId = this.familyTree(array, parentDeviceId);
            let selectList = [];
            this.deviceForm.setItemList.map((item) => {
              selectList.push(item.setItemDataId);
            });
            this.deviceForm.setItemList = this.sortProj(this.deviceForm.setItemList);
            this.getProjectList(selectList);
          } else {
            this.deviceForm = data;
          }
        }
      });
    },
    // 获取父类
    initParentDeviceTypeInfoList () {
      getParentDeviceTypeInfoList({
        needAllChild: 1,
        levelLimitCheck: '1'
      }).then((res) => {
        if (res.result_code == '1' && res.result_data.length > 0) {
          this.deviceParent = this.getDeviceParent(res.result_data);
        }
      });
    },
    // 根据节点找到父节点一直到顶级节点
    familyTree (array, id) {
      let temp = [];
      const forFn = function (arr, id) {
        for (let i = 0; i < arr.length; i++) {
          let item = arr[i];
          if (item.deviceId == id) {
            temp.unshift(Number(item.parentId));
            forFn(array, item.parentId);
            break;
          } else {
            if (item.childErpDeviceTypeInfo) {
              forFn(item.childErpDeviceTypeInfo, id);
            }
          }
        }
      };
      forFn(array, id);
      temp.push(Number(id));
      let type = temp.filter(item => item != '0');
      return type;
    },
    // 递归判断设备级联，把最后的 childErpDeviceTypeInfo 设为undefined
    getDeviceParent (data) {
      for (var i = 0; i < data.length; i++) {
        if (data[i].childErpDeviceTypeInfo.length < 1) {
          // childErpDeviceTypeInfo 若为空数组，则将childErpDeviceTypeInfo设为undefined
          data[i].childErpDeviceTypeInfo = undefined;
        } else {
          // children若不为空数组，则继续 递归调用 本方法
          this.getDeviceParent(data[i].childErpDeviceTypeInfo);
        }
      }
      return data;
    },
    // 关闭弹窗
    cancel () {
      this.resetForm(true);
    },
    // 取消
    resetForm (flag) {
      if (this.$refs['deviceForm']) {
        this.$refs['deviceForm'].resetFields();
      }
      this.addOrEdit = '';
      this.deviceForm = {
        deviceName: '',
        parentId: '',
        deviceUnit: '',
        deviceStatus: '1',
        setItemList: []
      };
      this.saveLoad = false;
      this.detailOpen = false;
      this.selectPorjIds = [];
      this.setItemDataIds = [];
      this.projectList = [];
      this.setItemDataName = '';
      this.parentId = '';
      if (!flag) {
        this.queryClick();
      }
    },
    // 删除
    deleteClick (row) {
      const self = this;
      self.$confirm({
        title: '确定要删除吗?',
        okText: '确定',
        cancelText: '取消',
        onOk () {
          deleteErpDeviceType({
            id: row.id
          }).then((res) => {
            if (res.result_code == '1') {
              self.$message.success('操作成功');
              self.pageChange(1);
            } else {
              self.$message.warning(res.result_msg);
            }
          });
        }
      });
    },
    // 展开所有
    showAllClick (value) {
      this.showAll = !value;
      if (value) {
        this.$refs.multipleTable.setAllTreeExpand(true);
      } else {
        this.$refs.multipleTable.clearTreeExpand();
      }
    },
    // 分页事件
    pageChange (e) {
      this.page = e;
      this.queryClick();
    },
    // 分页事件
    sizeChange (p, e) {
      this.page = p;
      this.size = e;
      this.queryClick();
    },
    // 获取固有的设备
    getProjectList (arr) {
      let dataList = [];
      this.projectList = [];
      this.filterProject = [];
      dataList = this.dictMap.device_type_set_item;
      dataList.forEach((item) => {
        let data = {};
        data.setItemDataId = item['dataValue'];
        data.setItemDataName = item['dataLable'];
        data.isRequired = '0';
        data.id = '';
        data.dataSort = item.dataSort;
        if (arr.indexOf(item['dataValue']) === -1) {
          this.projectList.push(data);
        } else {
          let number = arr.indexOf(item['dataValue']);
          this.deviceForm.setItemList[number].dataSort = data.dataSort;
        }
        this.filterProject.push(data);
      });
      this.projectList = this.sortProj(this.projectList);
      this.filterProject = this.sortProj(this.filterProject);
    },
    // 根据项目项目和是否必填字段返回列表数据
    filterTable () {
      let filterProject = Array.isArray(this.filterProject)
        ? this.filterProject
        : JSON.parse(this.filterProject);
      let projIds = [];
      this.deviceForm.setItemList.map(item => {
        projIds.push(item.setItemDataId);
      });
      this.projectList = filterProject.filter((item) => {
        return item.setItemDataName.indexOf(this.setItemDataName) > -1 && projIds.indexOf(item.setItemDataId) == -1;
      });
    },
    // 添加固有项目到已有项目
    add () {
      this.projectList = this.projectList.filter((item) => {
        if (this.setItemDataIds.indexOf(item.setItemDataId) > -1) {
          this.deviceForm.setItemList.push(item);
        }
        return this.setItemDataIds.indexOf(item.setItemDataId) === -1;
      });
      this.setItemDataIds = [];
      this.deviceForm.setItemList = this.sortProj(this.deviceForm.setItemList);
    },
    // 移除已选项目
    remove () {
      this.deviceForm.setItemList = this.deviceForm.setItemList.filter(
        (item) => {
          if (this.selectPorjIds.indexOf(item.setItemDataId) > -1) {
            this.projectList.push(item);
          }
          return this.selectPorjIds.indexOf(item.setItemDataId) === -1;
        }
      );
      this.selectPorjIds = [];
      this.projectList = this.sortProj(this.projectList);
    },
    /**
     *  排序项目名称
     *  params {* Array } arr
     * */
    sortProj (arr) {
      arr.sort((a, b) => {
        // return a.dataSort - b.dataSort; // 根据dataSort字段升序
        return a.setItemDataName.localeCompare(b.setItemDataName);
      });
      return arr;
    },
    /**
     *  改变已选项目的状态
     * params {*Array} 选中固有项目的id
     * params {*Boolean} checkBox的选中状态 true 表示选中，false取消选中
     * */
    changeSelectProj (val, event) {
      if (event && this.selectPorjIds.indexOf(val) === -1) {
        this.selectPorjIds.push(val);
      } else {
        this.selectPorjIds = this.selectPorjIds.filter((item) => {
          return item != val;
        });
      }
    }
  }
};
</script>

<style lang="less" scoped>
.erp-group-layout{
    margin-top: 10px;
    border: 1px solid #ccc;
    height: 40vh;
    overflow: auto;
    padding: 10px;
    :deep(.ant-checkbox-wrapper) {
      width: 100%;
      margin-left: 8px;
    }

  }

  .middle{
    margin: auto;
  }
  .bottom-item{
    width: 100%;
    text-align: center;
    margin: 15px;
  }
  .check-right{
    text-align: right;
  }

</style>
