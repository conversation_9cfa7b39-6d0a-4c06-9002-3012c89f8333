// 预防/试验
import { axios } from '@/utils/erp-request';
// 详情
export function getDetailApi (data) {
  return axios({
    url: '/pre/test/detail',
    method: 'POST',
    data
  });
}
// 组员
export function getTeamMembersApi (data) {
  return axios({
    url: '/pre/test/config/members',
    method: 'POST',
    data
  });
}
// 领取查询仪器配置
export function getInstrumentApi (data) {
  return axios({
    url: '/pre/test/config/instrument',
    method: 'POST',
    data
  });
}
// 新增活编辑仪器配置
export function updateInstrumentConfigApi (data) {
  return axios({
    url: '/pre/test/instrumentConfig/saveOrUpdate',
    method: 'POST',
    data
  });
}
// 仪器删除
export function deleteInstrumentConfigApi (data) {
  return axios({
    url: '/pre/test/instrumentConfig/delete',
    method: 'POST',
    data
  });
}

// 新增模板配置
export function addPreTemplateConfigApi (data) {
  return axios({
    url: '/pre/test/templateConfig/saveOrCommit',
    method: 'POST',
    data
  });
}

// 查询模板是否已配置
export function checkDeviceTypeIdApi (data) {
  return axios({
    url: '/pre/test/templateConfig/checkDeviceTypeId',
    method: 'POST',
    data
  });
}

// 预试模板配置列表
export function templateConfigListApi (data) {
  return axios({
    url: '/pre/test/templateConfig/list',
    method: 'POST',
    data
  });
}

// 预试模板配置详情
export function templateConfigDetailApi (data) {
  return axios({
    url: '/pre/test/templateConfig/detail',
    method: 'POST',
    data
  });
}
export function instrumentConfigListApi (data) {
  return axios({
    url: '/pre/test/instrumentConfig/list',
    method: 'POST',
    data
  });
}

// 查询项目名称列表
export function getProjectListApi (data) {
  return axios({
    url: '/pre/test/config/project/list',
    method: 'POST',
    data
  });
}
// 查询项目配置
export function getProjectConfigApi (data) {
  return axios({
    url: '/pre/test/config/project',
    method: 'POST',
    data
  });
}
// 预试验领取
export function testClaimApi (data) {
  return axios({
    url: '/pre/test/claim',
    method: 'POST',
    data
  });
}

// 报告提交
export function preTestReportCommitApi (data) {
  return axios({
    url: '/pre/test/report/commit',
    method: 'POST',
    data
  });
}

// 审批
export function preTestApproveApi (data) {
  return axios({
    url: '/pre/test/approve',
    method: 'POST',
    data
  });
}
// 查询所有试验项目
export function preTestConfigProjectMapApi (data) {
  return axios({
    url: '/pre/test/config/project/map',
    method: 'POST',
    data
  });
}
