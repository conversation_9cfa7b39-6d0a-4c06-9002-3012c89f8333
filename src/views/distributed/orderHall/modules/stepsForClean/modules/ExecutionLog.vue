<template>
   <div class="execute-log">
     <div :class="['execute-log-container',itemData.editable ? 'execute-log-container-bg':'']">
       <div class='execute-box'>
         <div class='execute-title'>执行信息</div>
         <div class='right-btn' v-if='!itemData.editable && !isDisabled'>
           <a-button type='link' size='small' @click='editEvent' >编辑 <a-icon type="form" /></a-button>
           <a-button type='link' size='small' @click='copyTextEvent'>生成文本 <a-icon type="file-text" /></a-button>
         </div>
         <div class='editable-right-btn' v-else-if='itemData.editable && !isDisabled'>
           <throttle-button @click="submit('1')" :loading="loading" label="提交日志"/>
           <a-button  @click='clearAll' v-if="itemData.type === 'add'">清空日志</a-button>
           <a-button @click='cancel' v-else>取消</a-button>
         </div>
       </div>
       <a-spin :spinning='loading'>
         <a-form-model
           :model="itemData"
           ref="executionLog"
           :labelCol="{ style: 'width: 150px' }"
           :wrapperCol="{ style: 'width: calc(100% - 150px)' }"
           v-if='itemData.editable'
           style='padding: 24px 120px 12px 0'
         >
           <a-row :gutter="24">
             <a-col :xl="8" :sm="12" :xs="24">
               <a-form-model-item label="清洗日期" prop="cleanDate" :rules="[{ required: true, message: '此项为必填项' }]">
                 <a-date-picker :disabled-date="disabledDate" placeholder='请选择清洗日期'  @change='cleanDateChange' v-model="itemData.cleanDate" format="YYYY-MM-DD" valueFormat="YYYY-MM-DD" size="default" style="width: 100%;" />
               </a-form-model-item>
             </a-col>
             <a-col :xl="8" :sm="12" :xs="24">
               <a-form-model-item label="今日天气" prop="todayWeather" :rules="[{ required: true, message: '此项为必填项' }]">
                 <a-select placeholder='请选择今日天气' v-model="itemData.todayWeather" allowClear style="width: 100%">
                   <a-select-option v-for="item in dictMap.yiyuan_weather" :key="item.dataValue" :value="item.dataValue">
                     {{ item.dataLable }}
                   </a-select-option>
                 </a-select>
               </a-form-model-item>
             </a-col>
             <a-col :xl="8" :sm="12" :xs="24">
               <a-form-model-item label="清洗地点" prop="cleanPlace"  :rules="[{ required: true, message: '此项为必填项' }]">
                 <a-input placeholder='请输入清洗地点' v-model='itemData.cleanPlace'  :maxLength="50" @blur="itemData.cleanPlace = $trim($event)" style="width: 100%;"/>
               </a-form-model-item>
             </a-col>
             <a-col :xl="8" :sm="12" :xs="24">
               <a-form-model-item  label="清洗人数" prop="cleanPeopleCount" :rules="[{ required: true, message: '此项为必填项' }]">
                 <a-input-number
                   placeholder='请输入清洗人数'
                   @blur='getCleanCapFn'
                   class="rang-input-left"
                   :min="1"
                   :max="999999999"
                   :precision='0'
                   v-model="itemData.cleanPeopleCount"
                   style="width: 100%;"
                 />
               </a-form-model-item>
             </a-col>
             <a-col :xl="8" :sm="12" :xs="24">
               <a-form-model-item label="清洗工具" prop="cleanTool"  :rules="[{ required: true, message: '此项为必填项' }]">
                 <a-select    placeholder='请输入清洗工具' v-model="itemData.cleanTool" allowClear size="default"  style="width: 100%">
                   <a-select-option v-for="item in dictMap.wash_tool" :key="item.dataValue" :value="item.dataValue">
                     {{ item.dataLable }}
                   </a-select-option>
                 </a-select>
               </a-form-model-item>
             </a-col>
             <a-col :xl="8" :sm="12" :xs="24">
               <a-form-model-item label="清洗容量(MW)" prop="cleanCap"  :rules="[{ required: true, message: '此项为必填项' }]">
                 <a-input-number placeholder='填写验收合格的容量' @blur='getCleanCapFn' v-model="itemData.cleanCap" :min="0.0001" :max="9999.9999" :precision="4" style="width:100%" />
               </a-form-model-item>
             </a-col>
             <a-col :xl="8" :sm="12" :xs="24">
               <a-form-model-item label="人均清洗容量(MW)" prop="cleanCapPeople">
                 <a-input placeholder='系统自动生成' v-model="itemData.cleanCapPeople" disabled style="width: 100%;"/>
               </a-form-model-item>
             </a-col>
             <a-col :xl="8" :sm="12" :xs="24">
               <a-form-model-item label="累计清洗容量(MW)" prop="totalCleanCap">
                 <a-input placeholder='系统自动生成' v-model="itemData.totalCleanCap" disabled style="width: 100%;"/>
               </a-form-model-item>
             </a-col>
             <a-col :xl="8" :sm="12" :xs="24">
               <a-form-model-item label="清洗进度(%)" prop="cleanRate">
                 <div>
                  <a-input placeholder='系统自动生成' v-model="itemData.cleanRate" disabled style="display: inline-block;width: calc(100% - 20px)"/>
                  <a-popover arrow-point-at-center>
                    <template slot="content">
                      <span>累计清洗容量/计划清洗容量*100%</span>
                    </template>
                    <span>
                      <svg-icon style="display: inline-block;margin-left: 6px" iconClass="health-info"></svg-icon>
                    </span>
                  </a-popover>
                 </div>
                 <div v-if="judgeCleanRate" class="check-clean-cap-msg ant-form-explain">
                   <a-icon type="exclamation-circle" />&nbsp;
                   清洗进度超过100%，请确认清洗容量准确填写
                 </div>
               </a-form-model-item>
             </a-col>
             <a-col :xl="8" :sm="12" :xs="24">
               <a-form-model-item label="进度说明" prop="cleanRateExplain"  :rules="[{ required: false, message: '此项为必填项' }]">
                 <a-input v-model="itemData.cleanRateExplain" placeholder='进度异常情况说明'  :maxLength="200" style="width: 100%;"/>
               </a-form-model-item>
             </a-col>
             <a-col :xl="8" :sm="12" :xs="24">
               <a-form-model-item label="清洗效果" prop="cleanEffect"  :rules="[{ required: true, message: '此项为必填项' }]">
                 <a-radio-group v-model="itemData.cleanEffect" >
                   <a-radio  v-for="item in dictMap.clean_effect" :key="item.dataValue" :value="item.dataValue">{{item.dataLable}}</a-radio>
                 </a-radio-group>
               </a-form-model-item>
             </a-col>
             <a-col :xl="8" :sm="12" :xs="24">
               <a-form-model-item label="验收人" prop="checkUser" :rules="[{ required: true, message: '此项为必填项' }]">
                 <a-select
                   :getPopupContainer="(target) => target.parentNode"
                   v-model="itemData.checkUser"
                   placeholder="请选择"
                   style="width: 100%"
                   allowClear
                 >
                   <a-select-option v-for="(item, k) in assignList()"  :key="k" :value="item.username">
                     {{ item.realName }}
                   </a-select-option>
                 </a-select>
               </a-form-model-item>
             </a-col>
           </a-row>
           <a-row :gutter="24">
             <a-col :span="8">
               <a-form-model-item label="清洗中(带水印)" prop="sceneFileOneList"  :rules="[{ required: true, message: '此项为必填项' }]">
                 <uploadFileView
                   v-model="itemData.sceneFileOneList"
                   listType="picture-card"
                   uploadText='上传现场图片'
                   accept=".jpg,.png,.jpeg,.bmp,.JPG,.PNG,.JPEG,.BMP"
                   :max-num='1'
                   @change='checkPickure("sceneFileOneList")'
                   :cacheMax='false'
                 />
                 <ExampleImg name='cleaning1.png' imgStyle='position: absolute;left: 112px;top:0'/>
               </a-form-model-item>
             </a-col>
             <a-col :span='16'>
               <a-form-model-item label="清洗效果对比(带水印)" prop="cleanBeforeSceneFileList"  :rules="[{ required: true, message: '此项为必填项' }]" >
                 <uploadFileView
                   v-model="itemData.cleanBeforeSceneFileList"
                   listType="picture-card"
                   uploadText='上传清洗前现场'
                   accept=".jpg,.png,.jpeg,.bmp,.JPG,.PNG,.JPEG,.BMP"
                   :max-num='1'
                   @change='checkPickure("cleanBeforeSceneFileList")'
                   :cacheMax='false'
                 />
               </a-form-model-item>
               <a-form-model-item  prop="cleanAfterSceneFileList" class='require-none' :rules="[{ required: true, message: '此项为必填项' }]" style='position: absolute;top:0;left: 273px;'>
                 <uploadFileView
                   v-model="itemData.cleanAfterSceneFileList"
                   listType="picture-card"
                   uploadText='上传清洗后现场'
                   accept=".jpg,.png,.jpeg,.bmp,.JPG,.PNG,.JPEG,.BMP"
                   :max-num='1'
                   @change='checkPickure("cleanAfterSceneFileList")'
                   :cacheMax='false'
                 />
               </a-form-model-item>
               <ExampleImg name='clean_before_scene.png' imgStyle='position: absolute;left: 385px;top:0'/>
               <ExampleImg name='clean_after_scene.png' imgStyle='position: absolute;left: 493px;top:0' />

             </a-col>
           </a-row>
           <a-row  :gutter="24">
             <a-col :span='8'>
               <a-form-model-item prop="sceneFileTwoList" class='require-none' :rules="[{ required: true, message: '此项为必填项' }]" >
                 <template slot='label'><span style='width: 63px;display: inline-block'></span></template>
                 <uploadFileView
                   v-model="itemData.sceneFileTwoList"
                   listType="picture-card"
                   uploadText='上传现场图片'
                   accept=".jpg,.png,.jpeg,.bmp,.JPG,.PNG,.JPEG,.BMP"
                   :max-num='1'
                   tip="单张图片不能超过10MB，仅支持上传jpg、jpeg、png、bmp格式"
                   extraTipSty='width: 100vw'
                   @change='checkPickure("sceneFileTwoList")'
                   :cacheMax='false'
                 />
                 <ExampleImg name='cleaning2.png' imgStyle='position: absolute;left: 112px;top:0'/>
               </a-form-model-item>
             </a-col>
             <a-col :span='16'>
               <a-form-model-item prop="cleanBeforeElectricFileList" :rules="[{ required: true, message: '此项为必填项' }]"  class='require-none'>
                 <template slot='label'><span style='width: 63px;display: inline-block'></span></template>
                 <uploadFileView
                   v-model="itemData.cleanBeforeElectricFileList"
                   listType="picture-card"
                   uploadText='上传清洗前电流'
                   accept=".jpg,.png,.jpeg,.bmp,.JPG,.PNG,.JPEG,.BMP"
                   tip="单张图片不能超过10MB，仅支持上传jpg、jpeg、png、bmp格式"
                   :max-num='1'
                   @change='checkPickure("cleanBeforeElectricFileList")'
                   :cacheMax='false'
                 />
               </a-form-model-item>
               <a-form-model-item :rules="[{ required: true, message: '此项为必填项' }]"  prop="cleanAfterElectricFileList" class='require-none' style='position: absolute;top:0;left: 273px;'>
                 <uploadFileView
                   v-model="itemData.cleanAfterElectricFileList"
                   listType="picture-card"
                   uploadText='上传清洗后电流'
                   accept=".jpg,.png,.jpeg,.bmp,.JPG,.PNG,.JPEG,.BMP"
                   :max-num='1'
                   @change='checkPickure("cleanAfterElectricFileList")'
                   :cacheMax='false'
                 />
               </a-form-model-item>
               <ExampleImg name='clean_before_electricity.png' imgStyle='position: absolute;left: 385px;top:0' />
               <ExampleImg name='clean_after_electricity.png' imgStyle='position: absolute;left: 493px;top:0'/>

             </a-col>
           </a-row>
           <!--         <a-row class='spot-supervise-btns'>-->
           <!--           <a-button @click='clearAll' v-if="itemData.type === 'add'">清空</a-button>-->
           <!--           <a-button @click='cancel' v-else>取消</a-button>-->
           <!--           <throttle-button label="暂存" @click="submit('0')" />-->
           <!--           <a-button class="ant-btn-primary" @click="submit('1')">完成</a-button>-->
           <!--         </a-row>-->
         </a-form-model>
         <detail-layout v-else :labelList="baseList" :form="itemData" title='' >
           <template v-slot:cleanRateExplain>
             <a-col :span="8" class="detail_layout_content" >
               <span class="left">进度说明</span>
               <span class="right ellipsis" :title='itemData.cleanRateExplain'>
                  {{itemData.cleanRateExplain||'--'}}
                  </span>
             </a-col>
           </template>
           <template v-slot:cleaning>
             <a-col :span="8" class="detail_layout_content" >
               <span class="left">清洗中</span>
               <span class="right">
                     <uploadFileView v-model="cleaning" listType="picture-card"  disabled />
                  </span>
             </a-col>
           </template>
           <template v-slot:cleanEffectCompare>
             <a-col :span="16" class="detail_layout_content" >
               <span class="left">清洗效果对比</span>
               <span class="right">
                     <uploadFileView v-model="cleanEffectCompare" listType="picture-card"  disabled />
                  </span>
             </a-col>
           </template>
         </detail-layout>
       </a-spin>
     </div>
   </div>
</template>

<script>

import uploadFileView from '@comp/com/fileUploadView.vue';
import ExampleImg from '@views/distributed/orderHall/modules/stepsForClean/modules/ExampleImg.vue';
import { insertCleanData, getCleanCap, getSingleCleanDetail, copyTextApi, checkCleanDateApi } from '@api/distributed/orderHall';
import { getWeatherApi } from '@api/common_gy/report';
import { baseList } from '../js/utils';
import VueClipboard from 'vue-clipboard2';
import moment from 'moment';
VueClipboard.config.autoSetContainer = true;
Vue.use(VueClipboard);
export default {
  name: 'ExecutionLog',
  inject: ['trigggleFootShow', 'updateOpsWorkOrderCleanVo', 'spotSuperviseIndex', 'assignList'],
  components: { uploadFileView, ExampleImg },
  props: {
    itemData: {
      type: Object,
      default: () => ({})
    },
    dictMap: {
      type: Object,
      default: () => ({})
    },
    // assignList: {
    //   type: Array,
    //   default: () => ([])
    // },
    orderForm: {
      type: Object,
      default: () => ({})
    },
    current: {
      type: Boolean,
      default: () => 0
    },
    isDisabled: {
      type: Boolean,
      default: () => false
    },
    activeKey: {
      type: String,
      default: () => null
    }
  },
  data () {
    return {
      baseList,
      loading: false
    };
  },
  mounted () {
    // if(this.activeKey === this.itemData.id && this.itemData.editable && this.itemData.type === 'add')  this.getWeatherFn();
  },
  computed: {
    cleaning () {
      const { sceneFileOneList, sceneFileTwoList } = this.itemData;
      return [...(sceneFileOneList || []), ...(sceneFileTwoList || [])];
    },
    cleanEffectCompare () {
      const { cleanBeforeSceneFileList, cleanAfterSceneFileList, cleanBeforeElectricFileList, cleanAfterElectricFileList } = this.itemData;
      return [...(cleanBeforeSceneFileList || []), ...(cleanAfterSceneFileList || []), ...(cleanBeforeElectricFileList || []), ...(cleanAfterElectricFileList || [])];
    },
    judgeCleanRate () {
      const { cleanRate } = this.itemData;
      return cleanRate && cleanRate.replace(/,/g, '') > 100;
    }
  },
  // watch: {
  //   itemData: {
  //     deep: true,
  //     immediate: true,
  //     handler (val, oldVal) {
  //       const _self = this;
  //       if (_self.current == this.spotSuperviseIndex()) _self.trigggleFootShow(!val.editable);
  //     }
  //   }
  // },
  methods: {
    async submit (isCommit, isLeave = false) {
      if (isCommit === '1') {
        this.$refs.executionLog.validate(valid => {
          if (valid) {
            this.doExecute(isCommit, isLeave);
          } else {
            this.$errorScroll();
          }
        });
      } else {
        this.$refs.executionLog.validateField('cleanDate', (errMsg) => {
          if (!errMsg) {
            this.doExecute(isCommit, isLeave);
          }
        });
      }
    },
    // 0：暂存 1：提交
    async doExecute (type, isLeave = false) {
      this.loading = true;
      const map = { ...this.itemData, isCommit: type, orderId: this.orderForm.id };
      const uuidArr = this.itemData.id.split('-');
      if (uuidArr.length > 1) map.id = null;
      let res = null;
      try {
        res = await insertCleanData(map);
        const { id, orderExecuteRate } = res.result_data;
        const detailResult = await getSingleCleanDetail({ businessesId: id });
        this.$emit('updateActiveKey', { id, orderExecuteRate, editable: type === '0', type: this.itemData.type, isLeave, detail: detailResult.result_data });
        this.loading = false;
        if (type === '1') this.$message.success('填写成功');
      } catch (err) {
        this.loading = false;
      }
      return Promise.resolve(res);
    },
    getCleanCapFn () {
      const initObj = { cleanCapPeople: null, totalCleanCap: null, cleanRate: null };
      const { cleanDate, cleanCap, cleanPeopleCount } = this.itemData;
      if (!cleanDate || !cleanCap) {
        Object.assign(this.itemData, initObj);
        return;
      }
      const param = { cleanDate, cleanCap, cleanPeopleCount, orderId: this.orderForm.id };
      getCleanCap(param).then(res => {
        if (res.result_code === '1') {
          Object.assign(this.itemData, res.result_data);
        }
      }).catch(() => {
        Object.assign(this.itemData, initObj);
      });
    },
    cleanDateChange (val) {
      this.validateCleanDate(val);
      if (!val) {
        Object.assign(this.itemData, { todayWeather: null, cleanCapPeople: null, totalCleanCap: null, cleanRate: null });
        return;
      }
      this.getCleanCapFn();
      this.getWeatherFn();
    },
    getWeatherFn () {
      const { cleanDate } = this.itemData;
      getWeatherApi({
        reportTime: cleanDate,
        psaId: this.orderForm.psaId
      }).then((res) => {
        if (res.result_code === '1') {
          Object.assign(this.itemData, { todayWeather: res.result_data });
        }
      }).catch(() => {
        this.itemData.todayWeather = null;
      });
    },
    async validate () {
      return new Promise(async resolve => {
        let valid;
        try {
          valid = await this.$refs.executionLog.validate();
        } catch (err) {
          valid = false;
          this.$errorScroll();
        }
        resolve(valid);
      });
    },
    // 校验日期是否重复
    validateCleanDate (val) {
      if (!val) {
        this.itemData.todayWeather = undefined;
        return;
      }
      const { cleanDate } = this.itemData;
      const isUUd = this.itemData.id.split('-').length > 1;
      checkCleanDateApi({ cleanDate, orderId: this.orderForm.id, cleanId: isUUd ? null : this.itemData.id }).then(res => {
        if (res.result_code === '1' && !res.result_data) {
          this.$notification.error({ message: '该日期已填报日志，请重新选择日期' });
        }
      }).catch(() => {

      });
    },
    editEvent () {
      // this.itemData.editable = true;
      Object.assign(this.itemData, { editable: true, type: 'edit' });
    },
    clearAll () {
      const _self = this;
      Object.keys(_self.itemData).forEach(item => {
        if (!['editable', 'tabTitle', 'type', 'id'].includes(item)) {
          _self.itemData[item] = null;
        }
      });
      this.$nextTick(() => {
        _self.$refs.executionLog.clearValidate();
      });
    },
    checkPickure (propertyName) {
      this.$nextTick(() => {
        this.$refs.executionLog.validateField(propertyName);
      });
    },
    cancel () {
      Object.assign(this.itemData, { editable: false });
      // this.trigggleFootShow(true);
    },
    copyTextEvent () {
      copyTextApi({ businessesId: this.itemData.id }).then(res => {
        if (res.result_code === '1') {
          this.$copyText(res.result_data).then(e => {
            this.$notification.success({
              message: '消息提示',
              description: `生成成功，可直接去粘贴`,
              duration: 3
            });
          });
          ;
        }
      }).catch(() => {

      });
    },
    clearValidate () {
      this.$refs.executionLog.clearValidate();
    },
    disabledDate (current) {
      return current && current > moment().endOf('day');
    },
    async validDate () {
      let msg;
      this.$refs.executionLog.validateField('cleanDate', (errMsg) => {
        msg = errMsg;
      });
      return msg;
    }
  }

};
</script>

<style lang='less' scoped>
.execute-log{
  padding-bottom: 24px;
  .execute-log-container{
    &.execute-log-container-bg{
      background: #F8F8F8;
      border-radius: 4px;
    }
    .execute-box{
      display: flex;
      justify-content: space-between;
      padding: 24px 24px 0 24px;

      .execute-title{
        font-size: 16px;
        font-weight: 500;
      }
      .right-btn{
        .ant-btn .anticon{
          vertical-align: middle;
        }
      }
      .editable-right-btn{
        display: flex;
        gap: 8px;
      }
    }
    .flex-row{
      display: flex;
      .ant-form-item{
        width: auto;
      }
    }
    .spot-supervise-btns{

      display: -webkit-flex;
      display: flex;
      justify-content: center;
      margin-left: -10%;
      padding: 12px 0;
      .ant-btn{
        margin-right: 10px;
      }
    }
    :deep(.ellipsis){
      overflow: hidden;
      display: inline-block;
      text-overflow: ellipsis;
      white-space: nowrap;
      max-width: 350px;
    }
    :deep(.check-clean-cap-msg){
        color: var(--zw-primary-color--default);
        font-size: 14px;
        width: max-content;
        position: absolute;
      }
  }
}
:root[data-theme='dark']{
  .execute-log{
    .execute-log-container.execute-log-container-bg{
      background: #1D2B40;
    }
  }
}

</style>
