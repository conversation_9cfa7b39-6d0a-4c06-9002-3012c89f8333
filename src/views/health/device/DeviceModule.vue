<template>
  <div class="height-100" id="deviceModule" :data-theme="theme">
    <a-spin :spinning="loading" class="width-height-100">
      <div class="type-box flex-start flex-gap-16">
        <div
          class="type-item flex-center"
          v-for="item in Object.keys(deviceTypeBelongMap)"
          :key="item"
          :class="{ active: searchParams.psType === item }"
          @click="deviceTypeBelongChange(item)"
        >
          <svg-icon :iconClass="deviceTypeBelongMap[item].iconClass" />
        </div>
      </div>
      <a-tabs v-model="deviceType" @change="deviceTypeChange" :animated="false">
        <a-tab-pane v-for="item in deviceTabList" :key="item.keyValue" :tab="item.remark + '(' + item.total + ')'" />
      </a-tabs>
      <NewTabList :key="deviceType" ref="tabList" class="m-b-16" :list="activeDiaList" :total="typeTotal" @change="typeChange" />
      <div class="table-container">
        <search-module
          ref="searchModule"
          :deviceType="deviceType"
          :type="type"
          :psType="searchParams.psType"
          @searchParamsChange="searchParamsChange"
          @doSearch="pageChange"
          @reasonNameChange="reasonNameChange"
          @cancelLoading="loading = false"
        />
        <div class="m-t-4 m-b-16 divide-line"></div>
        <div class="operation" style="height: 32px">
          <div class="operation-btn" style="justify-content: flex-end">
            <div>
              <a-checkbox
                ref="faultStatus"
                :defaultChecked="true"
                :checked="faultStatus == '1'"
                @change="faultStatusChange"
                class="faultStatusBox"
              >
                只看持续
              </a-checkbox>
              <a-dropdown
                :disabled="isShowBatchBtn || isIncludeStorage"
                v-if="(type == '1' || type == '2') && dealStatus == '01'"
              >
                <a-menu slot="overlay" @click="handleImportMenuClick">
                  <a-menu-item key="1">添加挂牌</a-menu-item>
                  <a-menu-item key="2">取消挂牌</a-menu-item>
                </a-menu>
                <a-button class="di-ant-btn-cncel" v-has="'alarmCenter:batchEdit'">
                  批量挂牌
                  <a-icon type="down" />
                </a-button>
              </a-dropdown>

              <erp-button
                label="导出"
                perms="alarmCenter:export"
                @click="doExport"
                class="solar-eye-btn-primary-cancel export-btn m-l-12"
              />
            </div>
          </div>
        </div>
        <vxe-table
          :tree-config="treeObj"
          :data="data"
          :height="tableHeight - 250"
          ref="multipleTable"
          resizable
          show-overflow
          size="small"
          :row-class-name="rowClassName"
          highlight-hover-row
          :seq-config="{ startIndex: (page - 1) * size }"
          @scroll="onScroll"
          border
          @checkbox-all="handleSelectionChange"
          @checkbox-change="handleSelectionChange"
          class="my-table"
        >
          <vxe-table-column type="checkbox" v-if="(type == '1' || type == '2') && dealStatus == '01'" :width="60">
          </vxe-table-column>
          <vxe-table-column type="seq" :width="80" title="序号">
            <template v-slot="{ row, rowIndex }">
              <span :class="{ 'expand-index': rowIndex == '-1' }">
                {{ rowIndex != '-1' ? (page - 1) * size + rowIndex + 1 : row.index }}
              </span>
            </template>
          </vxe-table-column>
          <vxe-table-column tree-node field="alarmLevel" title="诊断等级" width="110">
            <template #header="{ column }">
              <table-sort
                :isUp="isUp"
                :column="column"
                filed="alarmLevel"
                @sortChange="mySortChange"
                :downSort="downSort"
                :upSort="upSort"
                :sortFiled="sortFiled"
                :isLeftOffset="false"
              />
            </template>
            <template v-slot="{ row }">
              <div class="alarm-grade flex-start" :style="{ 'margin-left': !row.id ? '0' : '-1.5em' }">
                <svg-icon v-if="row.alarmLevel" :icon-class="'grade-' + row.alarmLevel" class="m-r-4"/>
                <span :title="row.alarmLevelName || '--'">{{ row.alarmLevelName || '--' }}</span>
              </div>
            </template>
          </vxe-table-column>
          <vxe-table-column
            v-for="item in columns"
            :key="item.field"
            :title="item.title"
            :field="item.field"
            :min-width="item.width"
            :visible="showFieldVisible(item)"
          >
            <template v-if="item.hasSort" #header="{ column }">
              <table-sort
                :isUp="isUp"
                :column="column"
                :filed="item.field"
                @sortChange="mySortChange"
                :downSort="downSort"
                :upSort="upSort"
                :sortFiled="sortFiled"
                :isLeftOffset="false"
              />
            </template>
            <template v-slot="{ row, rowIndex }">
              <span
                v-if="item.field == 'deviceNameShow'"
                @click="expandTree(row, rowIndex)"
                :class="{ pointer: !row.id }"
              >
                {{ row.deviceNameShow || '--' }}
              </span>
              <template v-else-if="item.field == 'alarmReasonName'">
                <div style="display: inline-block; align-items: center" v-if="row.alarmStatus == '01'">
                  <div v-if="row.tagName" class="tag-name">
                    <div class="div-center">
                      <span>{{ row.tagName }}</span>
                      <template v-if="dealStatus != '03'">
                        <a-icon @click="delReason(row)" class="close-icon" type="close" />
                      </template>
                    </div>
                  </div>
                </div>
                <span>{{ row.alarmReasonName }}</span>
              </template>
              <span v-else-if="item.field == 'powerLoss'">{{ row.powerLoss === 0 ? 0 : row.powerLoss || '--' }}</span>
              <template v-else-if="item.field === 'workCode'">
                <span v-if="row.workCode" @click="handleWorkCodeClick(row)" class="blue pointer">{{ row.workCode }}</span>
                <span v-else>--</span>
              </template>
              <span v-else-if="item.field === 'faultStatus'">{{row.faultStatusName || '--'}}</span>
              <span v-else>{{ row[item.field] || '--' }}</span>
            </template>
          </vxe-table-column>
          <vxe-table-column title="处理状态" field="alarmStatusName" min-width="110" fixed="right">
            <template v-slot="{ row }">
              <div class="flex-start">
                <div class="status-div" :class="getStatusClass(row.alarmStatus)"></div>
                <span :title="row.alarmStatusName || '--'">{{ row.alarmStatusName || '--' }}</span>
              </div>
            </template>
          </vxe-table-column>
          <vxe-table-column fixed="right" title="操作" min-width="160">
            <template v-slot="{ row }">
              <div class="flex-start flex-gap-16">
                <span title="详情" v-if="row.id" v-has="'910108101101'">
                  <svg-icon @click="doAnalysis(row)" iconClass="zw-detail" class="health-icon" />
                </span>
                <span title="派发" v-if="row.id" v-has="'910108101102'">
                  <svg-icon
                    v-show="row.alarmStatus == '01'"
                    class="health-icon"
                    :class="{ disabled: !isDisplayBtn(row) }"
                    @click="batchDistributeEvent(row, '1', '4')"
                    iconClass="zw-execute"
                  />
                </span>
                <span
                  v-if="(type == '1' || type == '2') && row.alarmStatus == '01'"
                  title="挂牌"
                  v-has="'alarmCenter:hangtag'"
                >
                  <svg-icon
                    v-show="row.alarmStatus == '01'"
                    class="health-icon"
                    :class="{ disabled: !isDisplayBtn(row) }"
                    iconClass="zw-hangup"
                    @click="showSelecteTag(row)"
                  />
                </span>
                <span title="删除" v-if="row.id" v-has="'910108101106'">
                  <svg-icon
                    v-show="row.alarmStatus == '01'"
                    class="health-icon"
                    @click="doDelete(row, '1')"
                    iconClass="zw-delete"
                  />
                </span>
              </div>
            </template>
          </vxe-table-column>
          <template v-slot:empty>
            <span>查询无数据</span>
          </template>
        </vxe-table>
        <page-pagination :pageSize="size" :current="page" :total="total" @size-change="sizeChange" />
      </div>
    </a-spin>
    <!--  工单详情  -->
    <order-detail ref="orderForm" @close="drawerClose" parentId="deviceModule" />
    <!--  时序分析  -->
    <alarm-analysis
      ref="alarmAnalysis"
      @alarmAnalysisClose="alarmAnalysisClose"
      :type="type"
      :deviceType="deviceType"
      parentId="deviceModule"
    />
    <!--  批量打标签  -->
    <tag-modal
      ref="tagMadal"
      :selectedRows="selectedRows"
      :selectedRowData="selectedRowData"
      :isBatchTag="isBatchTag"
      @addTag="addTag"
      :isDisableBtn="isDisableBtn"
    />
    <!--  显示声纹故障详情  -->
    <drawer-view ref="newAnalysis" parentId="deviceModule" />
    <!--  户用工单详情  -->
    <drawer-view ref="farmOrderDrawer" parentId="deviceModule" />
  </div>
</template>
<script>
import Vue from 'vue';
import { clone } from 'xe-utils';
import { mapGetters } from 'vuex';
import AlarmAnalysis from './modules/AlarmAnalysis';
import NewTabList from './modules/NewTabList.vue';
import SearchModule from './modules/SearchModule.vue';
import TagModal from './modules/TagModal.vue';
import OrderDetail from './modules/OrderDetail';
import { USER_INFO } from '@/store/mutation-types';
import { LeftMixin } from '@/mixins/LeftMixin';
import { AlarmEventMixins } from '../mixins/alarmEventMixins';
import { diagnosticTypeList, deviceTypeBelongMap, handleNullToEmptyObj } from '../config';
import {
  getAlarmEvents,
  exportAlarmEvents,
  tagging,
  taggingList,
  getRemarkDynamic,
  diagnosisStatistics,
  workOrderDetail
} from '@/api/health/AlarmEvents.js';
import tableHeight from '@/mixins/tableHeightAndSearchModel';

export default {
  name: 'DeviceModule',
  mixins: [LeftMixin, AlarmEventMixins, tableHeight],
  components: {
    AlarmAnalysis,
    NewTabList,
    TagModal,
    SearchModule,
    OrderDetail
  },
  data() {
    return {
      loading: false,
      deviceType: '',
      type: '',
      diaList: diagnosticTypeList, // 诊断类型集合
      activeDiaList: [], // 根据设备类型筛选的诊断类型集合
      typeTotal: diagnosticTypeList.reduce((acc, cur) => Object.assign(acc, { [cur.keyValue]: 0 }), {}), // 根据诊断类型筛选的总数
      isShowBatchBtn: true, // 批量按钮
      faultStatus: '1', // 是否只看持续
      dealStatus: '', // 处理状态
      selectedRows: [], // 选中列表
      selectedRowData: [], // 打标签 选中的行数据
      isBatchTag: false, // 添加标签时候区分是否是批量  true 批量  false 单个
      columns: [
        // { title: '诊断等级', field: 'alarmLevel', width: 110, hasSort: true},
        { title: '诊断原因', field: 'alarmReasonName', width: 160, hasSort: false },
        { title: '风险提示', field: 'riskWarning', width: 120, hasSort: false },
        { title: '设备名称', field: 'deviceNameShow', width: 120, hasSort: true },
        { title: '设备类型', field: 'deviceTypeName', width: 100, hasSort: false },
        { title: '电站名称', field: 'psName', width: 200, hasSort: true },
        { title: '发生时间', field: 'happenTime', width: 120, hasSort: true },
        { title: ' 更新时间 ', field: 'lastHappenTime', width: 140, hasSort: true },
        { title: '次数', field: 'updateTimes', width: 120, hasSort: true },
        { title: '诊断状态', field: 'faultStatus', width: 100, hasSort: true },
        { title: '电量损失值(kWh)', field: 'powerLoss', width: 140, hasSort: true },
        { title: '派发时间', field: 'distributeTime', width: 110, hasSort: true, status: ['', '03', '04'] },
        { title: '闭环时间', field: 'finishedTime', width: 110, hasSort: true, status: ['', '04'] },
        { title: '关联工单', field: 'workCode', width: 160, hasSort: false, status: ['', '03', '04'] },
        { title: '现场情况', field: 'closedTypeShow', width: 120, hasSort: false, status: ['', '04'] },
        { title: '处理建议', field: 'opinion', width: 200, hasSort: false }
      ], // 表格列
      isUp: true,
      treeObj: null, // 树形表格配置
      data: [], // 表格数据
      isDisableBtn: false, // 行打标签时判断是否是组串未接 组串未接只限制在组串类型
      timeInterval: null,
      clickedIds: [],
      lastClickId: null,
      userInfo: Vue.ls.get(USER_INFO),
      scrollHeight: 0,
      remarkList: [],
      comeFromAlarm: false,
      searchParams: {
        startTime: null,
        endTime: null,
        alarmReason: '',
        alarmStatus: '01',
        psType: '1'
      },
      searchData: {},
      alarmRemarkList: undefined,
      alarmReasonName: '',
      deviceTypeBelongMap,
      firstLoading: true
    };
  },
  created() {
    this.dealRouterEvent();
    this.timeInterval = setInterval(this.getList, (this.configDataFrequency || 5) * 60 * 1000);
  },
  mounted() {
    // 设置表格高度
    this.getTableHeight();
    window.onresize = () => {
      this.getTableHeight();
    };
  },
  activated() {
    if (window.comFromMonitor && !this.firstLoading) {
      this.dealRouterEvent();
    }
    this.firstLoading = false;
  },
  deactivated() {
    this.cleanIcon();
    this.clearUp();
  },
  beforeDestroy() {
    this.$nextTick(() => {
      this.cleanIcon();
    });
    this.clearUp();
  },
  computed: {
    isIncludeStorage() {
      return (
        this.selectedRows.filter((item) => {
          return item.psCategory && item.psCategory.includes('Storage');
        }).length !== 0
      );
    },
    deviceTabList() {
      return this.deviceTypeBelongMap[this.searchParams.psType].deviceTypeList;
    },
    // 是否显示树形表格
    isShowTree() {
      return this.type == '4' && this.deviceType == '10';
    },
    ...mapGetters(['configDataFrequency', 'theme'])
  },
  methods: {
    // 处理告警列表、诊断概览跳转过来的情况
    async dealRouterEvent() {
      this.activeDiaList = clone(this.diaList, true);
      this.loading = true;
      let res = await getRemarkDynamic();
      this.remarkList = res.result_data;
      if (window.comeFromAlarm) {
        this.handleRouteFromAlarm();
      } else if (window.comeFromOverview) {
        this.handleRouteFromOverview();
      } else if (window.comFromMonitor) {
        this.handleRouteFromRunMonitor();
      } else {
        this.$nextTick(() => {
          this.$refs.tabList.activeKey = '';
        });
        // this.pageChange(1, true);
      }
      this.getAlarmReasionOptions(this.alarmReasonName);
    },
    // 处理路由跳转数据，设置查询条件
    setRouteData(params) {
      this.searchParams.alarmStatus = params.handleStatus;
      if (params.isFromRealtime && window.comeFromOverview) this.faultStatus = '1';
      else this.faultStatus = undefined;
      let index = this.diaList.findIndex((item) => {
        return item.remark == params.tabName;
      });
      this.type = this.diaList[index].keyValue;
      if (this.$refs.alarmAnalysis) {
        this.$refs.alarmAnalysis.alarmAnalysisVisable = false;
      }
      if (this.$refs.newAnalysis) {
        this.$refs.newAnalysis.visible = false;
      }
      if (this.$refs.orderForm) {
        this.$refs.orderForm.visible = false;
      }
      this.$nextTick(() => {
        this.$refs.searchModule.searchData.alarmStatus = params.handleStatus;
        let typeIndex = this.deviceTabList.findIndex((item) => {
          return item.keyValue == params.deviceType;
        });
        let optList =
          typeIndex == -1 ? this.diaList.map((item) => item.keyValue) : this.deviceTabList[typeIndex].optList;
        if (!optList.includes(this.type)) {
          this.type = optList[0];
        }
        this.activeDiaList = this.diaList.filter((item) => {
          return optList.includes(item.keyValue);
        });
        let key = this.activeDiaList.findIndex((item) => {
          return item.keyValue == this.type;
        });
        this.$refs.tabList.activeKey = key == -1 ? '' : key;
      });
    },
    // 获取诊断原因下拉选项
    getAlarmReasionOptions(alarmReasonName) {
      this.$nextTick(() => {
        this.$refs.searchModule.getAlarmReasionOptions(alarmReasonName);
      });
    },
    // 设备类型变化事件
    async deviceTypeChange(val) {
      let data = this.deviceTabList.find((item) => {
        return item.keyValue == val;
      });
      this.activeDiaList = this.diaList.filter((item) => {
        return data.optList.includes(item.keyValue);
      });
      this.typeChange('');
      this.$nextTick(() => {
        this.$refs.tabList.activeKey = '';
      });
    },
    // 诊断类型变化事件
    typeChange(val) {
      this.type = val;
      this.sortFiled = '';
      this.sortKind = '';
      this.getAlarmReasionOptions(this.alarmReasonName);
      if (this.alarmReasonName) {
        this.loading = true;
      } else {
        this.pageChange(1, true);
      }
    },
    // 只看持续勾选事件
    faultStatusChange(e) {
      this.$refs.faultStatus.checked = e.target.checked;
      this.faultStatus = e.target.checked ? '1' : undefined;
      this.pageChange(1, true);
    },
    handleImportMenuClick(e) {
      const { key } = e; // 1 添加标签  2  删除标签
      if (key === '1') {
        this.isBatchTag = true;
        this.$refs.tagMadal.init('');
      } else if (key === '2') {
        let params = {
          tag: '0',
          list: []
        };
        this.selectedRows.forEach((item) => {
          params.list.push({
            id: item.id,
            psKey: item.psKey,
            alarmReason: item.alarmReason
          });
        });
        this.$confirm({
          title: '提示',
          content: '是否确认批量删除？',
          okText: '确定',
          cancelText: '取消',
          centered: true,
          onOk: () => {
            taggingList(params).then(() => {
              this.$message.success(`操作成功`);
              this.isShowBatchBtn = true;
              this.getList(false);
            });
          }
        });
      }
    },
    addTag() {
      if (this.isBatchTag) {
        this.isShowBatchBtn = true;
      }
      this.getList(false);
    },
    // 自定义表格排序事件
    mySortChange(sortKind, sortFiled, downSort, upSort) {
      this.sortKind = sortKind;
      this.sortFiled = sortFiled;
      this.downSort = downSort;
      this.upSort = upSort;
      this.page = 1;
      this.getList(true);
    },
    // 导出告警列表
    doExport() {
      if (this.total > 60000) {
        this.$message.warning('数据量超出6万条，请调整数据查询范围!');
      } else {
        this.fomartSearchData();
        this.loading = true;
        exportAlarmEvents(this.searchData)
          .then((res) => {
            this.loading = false;
            this.$downloadFile({
              fileBase64Code: res.fileBase64Code,
              fileName: res.fileName,
              fileType: res.fileType
            });
            this.loading = false;
            this.$message.success('导出成功');
          })
          .catch(() => {
            this.loading = false;
          });
      }
    },
    // 待派发-点击时序分析后表格行置灰
    rowClassName({ row }) {
      let { clickedIds, lastClickId } = this;
      if (lastClickId && lastClickId == row.id) {
        return 'health-row-last-clicked';
      } else if (clickedIds.includes(row.id)) {
        return 'health-row-clicked';
      }
    },
    onScroll() {
      this.scrollHeight = this.$refs.multipleTable.$el.querySelector('.vxe-table--body-wrapper').scrollTop;
    },
    // 行选中事件
    handleSelectionChange({ records }) {
      this.selectedRows = records;
      this.isShowBatchBtn = this.selectedRows.length === 0;
    },
    getGradePng(grade) {
      if (grade == '4') grade = '3';
      return require('@/assets/images/health/alarmEvents/grade' + grade + '.png');
    },
    // 显示选择标签
    showSelecteTag(row) {
      this.selectedRowData = [];
      this.selectedRowData.push(row);
      // 判断组串未接
      this.isDisableBtn = !(row && row.isString && row.isString === '1');
      let tagReason = row.tag ? row.tag.toString() : '';
      this.isBatchTag = false;
      this.$refs.tagMadal.init(tagReason);
    },
    clearUp() {
      if (this.timeInterval) {
        clearInterval(this.timeInterval);
        this.timeInterval = null;
      }
      window.removeEventListener('resize', this.getTableHeight);
      window.onresize = null;
    },
    cleanIcon() {
      let self = document;
      let healthClean = self.getElementsByClassName('health-clean-icon');
      if (healthClean && healthClean.length > 0) {
        self.body.removeChild(healthClean[0]);
      }
    },
    // 展开树
    expandTree(row, index) {
      if (row.isExpand) {
        this.$refs.multipleTable.setTreeExpand(this.data[index], false);
        this.$set(row, 'isExpand', false);
      } else {
        this.$refs.multipleTable.setTreeExpand(this.data[index], true);
        this.$set(row, 'isExpand', true);
      }
    },
    isDisplayBtn(row) {
      return row.psCategory ? !row.psCategory.includes('Storage') : true;
    },
    // 格式化查询、导出条件数据
    fomartSearchData() {
      this.searchData = {
        sortFiled: this.sortFiled,
        sortKind: this.sortKind,
        size: this.size,
        curPage: this.page,
        alarmRemark: this.remarkFilterByTab(this.type),
        deviceType: this.deviceType,
        elecEnsureType: '0',
        isClosed: this.searchParams.alarmStatus == '04' ? '1' : '0',
        closedType: '',
        faultStatus: this.faultStatus,
        tabCode: this.type,
        ...this.searchParams
      };
    },
    remarkFilterByTab(type) {
      let arr = [];
      this.remarkList.forEach((item) => {
        if (item.alarmGrade == type) {
          arr.push(item.alarmRemark);
        }
      });
      return arr.join(',');
    },
    // 获取告警列表
    async getList(refreshScroll) {
      this.loading = true;
      this.treeObj = null;
      this.fomartSearchData();
      this.getTabTotal(this.searchData);
      if (this.$refs.multipleTable && refreshScroll) {
        this.$refs.multipleTable.clearScroll();
      }
      // 也不知道原逻辑为什么那么德比，多一层赋值干啥的
      this.dealStatus = this.searchParams.alarmStatus;
      getAlarmEvents(this.searchData)
        .then((res) => {
          if (res.result_code == '1') {
            if (res.result_data.rowCount == 0 && this.page != 1) {
              this.pageChange(1, true);
            } else {
              this.data = res.result_data.pageList;
              // 合并项加表示符 低效&&设备类型为组串时，列表有展开项
              if (this.data && this.data.length && this.data.some(item => this.$isEmpty(item.id))) {
                this.treeObj = { children: 'displayList' };
                this.data.forEach((item) => {
                  this.$set(item, 'isExpand', false);
                  item.isShow = false;
                  if (item.displayList && item.displayList.length) {
                    item.displayList.forEach((list, index) => {
                      this.$set(list, 'mergeRow', true);
                      this.$set(list, 'index', index + 1);
                    });
                  }
                });
              }
              this.total = res.result_data.rowCount;
              this.$refs.multipleTable.loadData(this.data);
              this.$refs.multipleTable.refreshColumn();
            }
          }
          this.loading = false;
          window.comeFromOverview = false;
        })
        .catch(() => {
          this.loading = false;
          this.total = 0;
          this.$refs.multipleTable.loadData([]);
          this.$refs.multipleTable.refreshColumn();
          window.comeFromOverview = false;
        });
    },
    // 获取设备类型统计数据
    async getTabTotal() {
      let params = {
        dataRoles: this.userInfo.dataRoles.toString(),
        deviceTypeList: this.deviceType,
        faultStatus: this.faultStatus,
        ...this.searchParams
      };
      params.alarmReasonList = params.alarmReason;
      delete params.alarmReason;
      if (window.comeFromOverview) {
        params.alarmRemarkList = this.alarmRemarkList;
      }
      const res = await diagnosisStatistics(params).catch(() => (this.loading = false));
      const { devcieTypeCount, alarmCount } = res;
      const currentDeviceTypeKeys = this.deviceTabList.map((item) => item.keyValue);
      // 计算设备类型总数
      this.deviceTabList.forEach((item) => {
        if (!item.keyValue) {
          item.total = currentDeviceTypeKeys.reduce((acc, cur) => {
            return acc + (devcieTypeCount[cur] || 0);
          }, 0);
        } else {
          item.total = devcieTypeCount[item.keyValue] || 0;
        }
      });
      // 根据当前所有的设备类型同事诊断类型总数
      const resetTypeTotal = this.$options.data().typeTotal;
      if (this.deviceType) {
        let data = alarmCount[this.deviceType] || {};
        for (let key in resetTypeTotal) {
          resetTypeTotal[key] = data[key] || 0;
        }
      } else {
        for (let key in alarmCount) {
          if (!currentDeviceTypeKeys.includes(key)) continue;
          for (let k in alarmCount[key]) {
            resetTypeTotal[k] += (alarmCount[key] && alarmCount[key][k]) || 0;
          }
        }
      }
      this.typeTotal = resetTypeTotal;
    },
    // 打开分析页面
    doAnalysis(row) {
      let { clickedIds, dealStatus } = this;
      // 待处理、已派发、已闭环 状态的数据 点击需要置灰当前行
      if (clickedIds.indexOf(row.id) == -1) {
        this.clickedIds.push(row.id);
      }
      this.lastClickId = row.id;
      if (row.analyseType == 'new') {
        this.$refs.newAnalysis.init(row.alarmRemark, row, '/health/device/modules/NewAnalysis');
      } else {
        this.$refs.alarmAnalysis.init(row, dealStatus);
      }
    },
    // 时序分析页面关闭回调事件
    alarmAnalysisClose(flag) {
      if (this.comeFromAlarm) {
        this.comeFromAlarm = false;
        this.pageChange(1, true);
      } else if (flag) {
        this.getList(false);
      }
    },
    // 删除原因
    delReason(row) {
      this.$confirm({
        title: '提示',
        content: '是否确认取消挂牌？',
        okText: '确定',
        cancelText: '取消',
        centered: true,
        onOk: () => {
          let params = {
            id: row.id,
            tag: '0',
            psKey: row.psKey,
            alarmReason: row.alarmReason
          };
          tagging(params)
            .then(() => {
              this.$message.success(`操作成功`);
              this.tagVisible = false;
              this.getList(false);
            })
            .catch(() => {});
        }
      });
    },
    getStatusClass(status) {
      switch (status) {
        case '01':
          return 'status1';
        case '03':
          return 'status2';
        case '04':
          return 'status3';
        default:
          return '';
      }
    },
    // 查询条件变化
    searchParamsChange(searchParams) {
      this.searchParams = Object.assign(
        {},
        {
          ...searchParams,
          psType: this.searchParams.psType,
          alarmStatus: searchParams.alarmStatus
        }
      );
    },
    drawerClose() {
      if (this.comeFromAlarm) {
        this.comeFromAlarm = false;
        this.pageChange(1, true);
      }
    },
    // 诊断原因变化
    reasonNameChange(alarmReasonName) {
      this.alarmReasonName = alarmReasonName;
    },
    showFieldVisible(item) {
      if (!this.dealStatus || this.dealStatus.split(',').length > 1) {
        return true;
      }
      return !item.status || item.status.includes(this.dealStatus);
    },
    handleWorkCodeClick(row) {
      const { projectBusinessType } = row;
      if (projectBusinessType == '1') {
        this.$refs.farmOrderDrawer.init('3', row, '/health/device/modules/FarmOrderDetail');
        return;
      }
      workOrderDetail({ alarmId: row.id, defectId: row.defectId, psId: row.psId }).then((res) => {
        this.$refs.orderForm.init(handleNullToEmptyObj(res));
      });
    },
    deviceTypeBelongChange(key) {
      this.searchParams.psType = key;
      this.deviceType = '';
      this.deviceTypeChange('');
    }
  }
};
</script>
<style scoped lang="less">
&[data-theme='light'] {
  --gradient-selected: linear-gradient(270deg, #ff9200 0%, #ff8100 100%);
  --gradient-default: linear-gradient(180deg, #f9f9f9 0%, #f2f2f2 100%);
  --gradient-hover: linear-gradient(180deg, #FFE8D8 0%, #FFDAB6 100%);
}

&[data-theme='dark'] {
  --gradient-selected: linear-gradient(270deg, #08a4ed 0%, #0880ed 100%);
  --gradient-default: linear-gradient(180deg, #143b71 0%, #0d356d 100%);
  --gradient-hover:  linear-gradient(180deg, #18488B 0%, #0E4085 100%);
}

.type-box {
  margin-bottom: 12px;

  .type-item {
    cursor: pointer;
    width: 90px;
    height: 30px;
    border-radius: 4px;
    padding: 4px 8px;
    background-color: var(--zw-card-bg-color--default);
    color: var(--zw-primary-color--default);
    font-size: 80px;
    border: 1px solid var(--zw-primary-color--default);

    .svg-icon {
      width: 74px;
      height: 22px;
    }

    &.active {
      background: var(--gradient-selected);
      color: var(--white);
    }

    &:hover {
      background-color: var(--zw-primary-partial-areas-color--hover);
    }
  }
}

.operation-btn {
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 32px;
}

.operation {
  margin-bottom: 12px;
}

.faultStatusBox {
  color: var(--zw-text-1-color--default);
  margin-right: 10px;
}

.tag-name {
  .div-center {
    color: var(--zw-warning-color--normal);
    border: 1px solid var(--zw-warning-color--normal);
    padding: 0 4px;
    border-radius: 3px;
    margin-right: 3px;
    font-size: 12px;
    background: var(--zw-card-bg-color--default) !important;
  }

  margin: 4px;

  .close-icon {
    display: none;
    cursor: pointer;
    padding: 4px;
  }
}

.tag-name:hover {
  .close-icon {
    display: inline;
  }
}

.alarm-grade {
  width: 80px;

  img {
    margin-right: 3px;
    width: 12px;
    height: 11px;
  }
}

:deep(.vxe-table--empty-content) {
  width: auto;
}

.expand-index {
  padding-left: 20px;
}

:deep(.table-container) {
  height: calc(100% - 110px);
  background: var(--zw-card-bg-color--default);
  border-radius: 4px;
  padding: 16px 24px;
  position: relative;

  .health-row-clicked {
    color: var(--zw-text-3-color--default);
  }
  .health-row-last-clicked {
    color: var(--zw-primary-color--default);
  }

  .health-icon {
    color: var(--zw-conduct-color--normal);
    font-size: 16px;
  }

  .health-icon.disabled {
    cursor: not-allowed;
    pointer-events: none;
    opacity: 0.5;
  }

  .health-icon:hover {
    color: var(--zw-primary-color--default) !important;
  }
}

:deep(.ant-tabs) {
  border-radius: 6px;
  padding: 12px 16px 0 16px;
}

:deep(.ant-tabs .ant-tabs-bar) {
  border-bottom-color: var(--zw-divider-color--default);
  background-color: transparent !important;
}

:deep(.ant-tabs-nav .ant-tabs-tab) {
  //width: 92px;
  text-align: left;
  margin-right: 48px;
  padding: 0 0 12px;
}

.status-div {
  width: 6px;
  height: 6px;
  border-radius: 3px;
  margin-right: 6px;
}

.status1 {
  background: var(--zw-primary-color--default);
}

.status2 {
  background: var(--zw-conduct-color--normal);
}

.status3 {
  background: var(--zw-proceed-color--normal);
}
</style>
