/* 下载大文件 */
import { postAction } from '@/api/manage';
const baseUrl = process.env.VUE_APP_OSS_FILE_URL;
const OSSFileList = (params) => postAction(baseUrl + '/exportUtils/exportDownloadRecordList', params);
const deleteOSSFile = (params) => postAction(baseUrl + '/exportUtils/deleteExportRecord', params);
const exportBigExcel = (params) => postAction(baseUrl + '/exportUtils/exportBigExcel', params);
const getUrlExportFile = (params) => postAction(baseUrl + '/exportUtils/getUrlExportFile', params);
const getExportCerti = (params) => postAction(baseUrl + '/exportUtils/exportCerti', params);
// 下载报告
const exportReportApi = (params) => postAction(baseUrl.replace('/oss/v1', '') + '/poi-tl/v1/export/export-report', params);
export {
  OSSFileList,
  deleteOSSFile,
  exportBigExcel,
  getUrlExportFile,
  getExportCerti,
  exportReportApi
};
