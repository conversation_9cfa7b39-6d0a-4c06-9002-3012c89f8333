<template>
  <a-drawer
    :title="drawerInfo.title"
    width="100%"
    :visible="drawerInfo.visible"
    :body-style="{ padding: '24px' }"
    @close="onClose(false)"
    :get-container="dom"
    :wrap-style="{ position: 'absolute' }"
    :drawerStyle="{ padding: 0, 'overflow-x': 'hidden' }"
    :afterVisibleChange="afterVisibleChange"
  >
    <a-row>
      <a-form-model
        :model="form"
        :rules="rules"
        layout="horizontal"
        v-bind="{
          labelCol: { span: 7 },
          wrapperCol: { span: 17 },
        }"
        ref="ruleForm"
      >
        <a-col :span="5" :style="{ height: height - 104 + 'px' }" class="solareye-drawer-left">
          <div :style="{ height: height - 170 + 'px', overflow: auto }">
            <a-form-model-item label="设备类型" prop="deviceType">
              <a-select
                v-model="form.deviceType"
                @change="(value) => changeDevice(value, false)"
                :disabled="drawerInfo.type == 'view' || drawerInfo.type == 'edit' ? true : false"
                style="width: 100%; margin-bottom: 16px"
              >
                <a-select-option v-for="item in deviceTypeList" :key="item.code" :value="item.code">{{
                  item.name
                }}</a-select-option>
              </a-select>
            </a-form-model-item>
            <a-form-model-item label="生产厂商" prop="factoryId">
              <a-select
                :disabled="drawerInfo.type == 'view' || drawerInfo.type == 'edit' ? true : false"
                v-model="form.factoryId"
                @change="(value, option) => getDeviceModelList(value, false, option)"
                :filter-option="filterOption"
                show-search
              >
                <a-select-option v-for="item in manufacturersList" :key="item.id" :value="item.id">
                  {{ item.factory }}</a-select-option
                >
              </a-select>
            </a-form-model-item>
            <a-form-model-item label="设备型号" prop="modelId">
              <a-select
                v-if="drawerInfo.type == 'add'"
                v-model="form.modelId"
                :filter-option="filterOption"
                show-search
              >
                <a-select-option v-for="item in deviceModelList" :key="item.id" :value="item.id">
                  {{ item.data }}</a-select-option
                >
              </a-select>
              <span v-else><a-input :disabled="true" v-model="form.model"></a-input></span>
            </a-form-model-item>
          </div>
          <div
            :style="{
              width: '100%',
              textAlign: 'center',
            }"
          >
            <a-button :style="{ marginRight: '8px' }" @click="onClose(false)"
              >{{ !isReadonly ? '取消' : '关闭' }}
            </a-button>
            <a-button class="solar-eye-btn-primary" v-show="!isReadonly" @click="onSubmit" :loading="loading">
              保存
            </a-button>
          </div>
        </a-col>
        <a-col
          :offset="2"
          :span="16"
          v-if="(drawerInfo.type == 'add' || drawerInfo.type == 'edit') && drawerInfo.visible"
          :style="{ height: height - 114 + 'px' }"
        >
          <div class="drawer-right-content">
            <h2 style="text-align: center; margin-bottom: 24px">
              {{ isBindPoint ? '绑定测点编辑' : '外部平台型号替换' }}
            </h2>
            <a-form-model-item label="外部平台" :labelCol="{ span: 3 }" :labelAlign="left" v-show="!isBindPoint">
              <a-select v-model="form.platform" @change="platformChange">
                <a-select-option v-for="item in externalPlatform" :key="item.code" :value="item.code">{{
                  item.name
                }}</a-select-option>
              </a-select>
            </a-form-model-item>
            <a-transfer
              class="solareye-transfer"
              :data-source="pointList"
              show-search
              :target-keys="points"
              @change="handleChange"
              :render="(item) => `${item.pointName}`"
              v-show="isBindPoint"
              :operations="['右移', '左移']"
              ref="transferPoint"
              :list-style="{
                width: '407px',
                height: getTransferHeight + 'px',
              }"
            />
            <a-transfer
              class="solareye-transfer"
              :data-source="deviceVersionSourceList"
              show-search
              :operations="['右移', '左移']"
              :target-keys="models"
              @change="onChange"
              :render="(item) => `${item.modelCode}`"
              v-if="!isBindPoint"
              ref="transferDevice"
              :list-style="{
                width: '407px',
                height: getTransferHeight + 'px',
              }"
            />
          </div>
        </a-col>
        <a-col :span="19" v-else>
          <version-view-detail
            :isBindPoint="isBindPoint"
            @hasData="isHasData"
            :form="form"
            v-if="drawerInfo.type == 'view' && drawerInfo.visible"
          ></version-view-detail>
        </a-col>
      </a-form-model>
    </a-row>
  </a-drawer>
</template>
<script>
import { postAction } from '@/api/manage';
// const baseUrl = 'http://***************:8080';

import {
  getManufacturersList,
  getDeviceModel,
  getPointList,
  getVersionPointList
} from '@/api/dataCenter/index';
import VersionViewDetail from './VersionViewDetail.vue';
const baseUrl = process.env.VUE_APP_Health_BASE_URL;
// const baseUrl = 'http://**********:8088'
let innerHeight = window.innerHeight - 60 - 40 - 24;
export default {
  name: 'PointMageDrawer',
  props: {
    deviceTypeList: Array,
    default: []
  },
  components: { VersionViewDetail },
  data () {
    return {
      isShow: false, // 编辑页或详情页闪现问题处理
      obj: {},
      column: {},
      drawerInfo: {
        title: '',
        visible: false,
        type: ''
      },
      pointList: [],
      loading: false,
      form: {
        id: '', // 测点主键名称
        deviceType: '',
        points: [],
        version: '',
        model: '',
        models: [],
        factoryId: '',
        existVersion: '',
        existModel: '',
        platform: '阳光云',
        modelId: ''
      },
      points: [],
      models: [],
      versionList: [],
      deviceModelList: [],
      existModelList: [],
      existVersionList: [],
      variableName: '',
      rules: {
        deviceType: [
          {
            required: true,
            message: '请选择设备类型',
            trigger: 'change'
          }
        ],
        factoryId: [
          {
            required: true,
            message: '请选择生产厂商',
            trigger: 'change'
          }
        ],
        modelId: [
          {
            required: true,
            message: '请选择设备型号',
            trigger: 'change'
          }
        ]
      },
      isDevice: false,
      isPoint: false,
      deviceVersionSourceList: [],
      url: {
        detail: '/versionManagement/detail',
        update: '/versionManagement/update',
        add: '/versionManagement/add',
        modelList: '/versionManagement/modelList'
      },
      dom: document.getElementsByClassName('version-mage')[0],
      isBindPoint: false,
      manufacturersList: [],
      height: innerHeight > 700 ? innerHeight : 700,
      externalPlatform: [
        {
          code: '阳光云',
          name: '阳光云'
        },
        {
          code: '辽电投',
          name: '辽电投'
        }
      ]
    };
  },
  created () {},
  watch: {
    visible () {}
  },
  mounted () {
    this.dom = document.getElementsByClassName('version-mage')[0];
  },
  computed: {
    isReadonly () {
      return this.drawerInfo.type == 'view';
    },
    pointDesc () {
      if (this.drawerInfo.type == 'view') {
        return (this.isPoint ? '已绑定' : '未绑定') + '，点击查看';
      } else {
        return this.points.length == 0 ? '未绑定，点击添加' : '已绑定，点击修改';
      }
    },
    deviceDesc () {
      if (this.drawerInfo.type == 'view') {
        return (this.isDevice ? '已替换' : '未替换') + '，点击查看';
      } else {
        return this.models.length === 0 ? '未替换，点击替换' : '已替换，点击修改';
      }
    },
    getTransferHeight () {
      return this.drawerInfo.type === 'add' ? this.height - 160 : this.height - 220;
    }
  },
  methods: {
    getManufacturersList (value, isFirstInit) {
      getManufacturersList({ deviceType: this.form.deviceType }).then((res) => {
        this.manufacturersList = res.result_data;
      });
    },
    /**
     *  选择生产厂商
     * params {String} value生产厂商的值
     * params {isFirstInit} 是否是第一个进入页面
     */
    getDeviceModelList (value, isFirstInit, option) {
      if (!isFirstInit) {
        this.deviceModelList = [];
        this.form.modelId = '';
        this.versionList = [];
        this.form.version = '';
        this.form.factory = '';
        this.form.model = '';
        if (this.drawerInfo.type === 'add') {
          this.form.existModel = '';
          this.form.existVersion = '';
        }
      }
      let id = isFirstInit ? option.factoryId : option.key;
      getDeviceModel({ deviceType: this.form.deviceType, id: id == '--' ? '' : id, factoryId: value == '--' ? '' : value }).then((res) => {
        this.deviceModelList = res.result_data.deviceModel;
        if (this.drawerInfo.type === 'add') {
          this.existModelList = res.result_data.deviceModel;
        }
      });
    },
    getDeviceVersionSourceList (value, option) {
      this.form.id = option.key;
      this.getVersionPointList();
      this.getModelList(true);
      this.getModelList();
    },
    /**
     * 初始化抽屉
     * params {Object} column 点击列的对象
     * params {Object} title ，type 等基本信息
     * */
    initDrawer (column, obj) {
      this.column = column;
      this.initData(obj);
      this.form.platform = column.sourceName == '辽电投' ? '辽电投' : '阳光云';
      this.obj = obj;
      this.drawerInfo.visible = true;
    },
    initData (obj) {
      this.loading = false;

      if (this.$refs.ruleForm) {
        this.$refs.ruleForm.resetFields();
      }
      this.isBindPoint = false;
      this.deviceVersionSourceList = [];
      this.pointList = [];
      this.points = [];
      this.models = [];

      this.versionList = [];
      this.deviceModelList = [];
      this.existModelList = [];
      this.existVersionList = [];
      this.variableName = '';

      this.isDevice = false;
      this.isPoint = false;

      this.manufacturersList = [];

      this.$forceUpdate();
      this.$nextTick(() => {
        this.dom = document.getElementsByClassName('version-mage')[0];
      });
      if (obj.type === 'add') {
        this.drawerInfo = Object.assign({}, this.drawerInfo, obj);
      }
      for (let item in this.form) {
        if (item != 'id') {
          if (this.form[item] instanceof Array) {
            this.form[item] = [];
          } else {
            this.form[item] = '';
          }
        }
      }
    },
    afterVisibleChange () {
      if (this.drawerInfo.visible) {
        if (this.obj.type != 'add') {
          this.getDetail(this.column.id).then((res) => {
            let data = res.result_data;
            this.drawerInfo = Object.assign({}, this.drawerInfo, this.obj);
            this.changeDevice(data.deviceType, true);
            this.getModelList(true);
            this.getVersionPointList();
            this.getDeviceModelList(data.factoryId, true, this.form);
          });
        }
      }
    },
    // 获取已选中得型号
    getModelList (flag) {
      // true 是选中的，false 是左侧的
      let params = { 
        deviceType: this.form.deviceType, 
        id: this.form.id, 
        type: flag ? 1 : 0,
        source: this.form.platform == '辽电投' ? '8' : null
      };
      // 新增清除id
      if (this.drawerInfo.type === 'add') {
        params.id = '';
      }
      postAction(baseUrl + this.url.modelList, params).then((res) => {
        let arr = [];
        res.result_data.map((item) => {
          item.key = item.modelId;
          item.title = item.modelCode;
          arr.push(item.modelId);
        });
        if (flag) {
          this.form.models = arr;
          this.models = arr;
        } else {
          this.deviceVersionSourceList = res.result_data;
        }
      });
    },
    getVersionPointList (value) {
      let params = value
        ? {
          deviceType: this.form.deviceType,
          factoryId: this.form.factoryId,
          version: value,
          modelId: this.form.existModel
        }
        : { deviceType: this.form.deviceType, id: this.form.id, modelId: this.form.existModel };
      this.form.points = [];
      this.points = [];
      getVersionPointList(params).then((res) => {
        res.result_data.forEach((element) => {
          this.points.push(element.point);
        });
        this.form.points = this.points;
      });
    },
    getSelectPointList () {
      postAction(baseUrl + this.url.modelList, {
        deviceType: this.form.deviceType,
        id: this.form.id,
        source: this.form.platform == '辽电投' ? '8' : null
      }).then((res) => {
        this.form.points = res.result_data;
      });
    },
    getDetail (id) {
      return new Promise((resolve, reject) => {
        postAction(baseUrl + this.url.detail, { id: id }).then((res) => {
          let data = res.result_data;
          if (res.result_data) {
            this.form = data;
            this.form.platform = this.column.sourceName == '辽电投' ? '辽电投' : '阳光云';
            if (res.result_code == 1) {
              resolve(res);
            } else {
              reject(res);
            }
          }
        });
      });
    },
    /**
     *  选择设备类型
     * params {String} value 设备类型的值
     */
    changeDevice (value, isFirstInit) {
      this.getPointList(value, isFirstInit);
      this.getModelList(false);
      if (!isFirstInit) {
        this.manufacturersList = [];
        this.deviceModelList = [];
        this.versionList = [];
        this.form.factoryId = '';
        this.form.modelId = '';
        this.form.version = '';
        if (this.drawerInfo.type === 'add') {
          this.form.existModel = '';
          this.form.existVersion = '';
          this.existVersionList = [];
          this.existModelList = [];
        }
      }
      this.getManufacturersList(value, isFirstInit); // 获取生产厂商
    },
    onClose (flag) {
      this.drawerInfo.visible = false;
      this.drawerInfo = {
        title: '',
        visible: false,
        type: ''
      };
      this.$emit('click', { isNeedRefresh: flag, deviceType: this.form.deviceType });
    },
    onSubmit () {
      let type = this.drawerInfo.type;
      let url = type == 'add' ? this.url.add : this.url.update;
      if (type != 'add') {
        delete this.form.binding;
        delete this.form.status;
      }
      // 新增加参数
      if (type === 'add') {
        this.form.status = '1';
      }
      let params = Object.assign(this.form, {
        factory: this.getIdByName(this.manufacturersList, 'factoryId', 'factory'),
        model: this.getIdByName(this.deviceModelList, 'modelId', 'data'),
        source: this.form.platform == '辽电投' ? '8' : null
      });
      // 没有选中项  不给提交
      if (params && params.models && params.models.length === 0) {
        let msg = this.isBindPoint ? '请选择测点' : '请选择型号';
        this.$message.warning(msg);
        return;
      }
      this.$refs.ruleForm.validate((valid) => {
        if (valid) {
          this.loading = true;
          postAction(baseUrl + url, params)
            .then((res) => {
              if (res.result_code == '1') {
                this.$message.success(type == 'add' ? '新增成功' : '编辑成功');
                this.$emit('click', { isNeedRefresh: true, deviceType: this.form.deviceType });
                this.onClose(false);
              }
              this.loading = false;
            })
            .catch(() => {
              this.loading = false;
            });
        }
      });
    },
    getIdByName (arr, keyName, objKey) {
      let id = '';
      arr.forEach((item) => {
        if (item.id == this.form[keyName]) {
          id = item[objKey];
        }
      });
      return id;
    },
    /**
     *  根据设备类型获取测点名称列表
     * params {String} value 设备类型
     * params {Boolean} isFirstInit 是否是第一次进入页面进行请求
     */
    getPointList (value, isFirstInit) {
      this.form.deviceType = value;
      this.form.points = [];
      this.points = [];
      getPointList({ deviceType: value, model: this.form.existModel }).then((res) => {
        this.pointList = res.result_data;
        this.pointList.map((item) => {
          item.key = item.pointCode;
          item.title = item.pointName;
        });
      });
    },
    searchVariable () {
      this.variableSelectList = this.variableSelectListCopy.filter((item) => {
        return item.indexOf(this.variableName) > -1;
      });
    },
    handleChange (targetKeys) {
      this.points = targetKeys;
      this.form.points = targetKeys;
    },
    onChange (nextTargetKeys) {
      this.models = nextTargetKeys;
      this.form.models = nextTargetKeys;
    },
    isHasData (obj) {
      if (obj.flag === 'isPoint') {
        this.isPoint = this.isHasProperty(obj, 'isPoint');
      }
      if (obj.flag === 'isDevice') {
        this.isDevice = this.isHasProperty(obj, 'isDevice');
      }
    },
    isHasProperty (obj, keyName) {
      if (obj.hasOwnProperty(keyName) && obj[keyName]) {
        return true;
      }
      return false;
    },
    filterOption (input, option) {
      return option.componentOptions.children[0].text.toLowerCase().indexOf(input.toLowerCase()) >= 0;
    },
    platformChange () {
      if(this.form.deviceType) {
        this.getModelList(false);
        if(this.drawerInfo.type == 'edit') {
          this.getModelList(true);
        }  
      }
    }
  }
};
</script>
<style lang="less">
.solareye-dropdown-margin {
  .ant-dropdown-content {
    margin-top: 3px;
    max-height: 300px;
    overflow: auto;
    min-height: 100px;
  }
}
.solareye-drawer-left {
  border-radius: 4px;

  padding: 24px;
  .ant-form-item {
    margin-bottom: 12px !important;
  }
}
</style>
