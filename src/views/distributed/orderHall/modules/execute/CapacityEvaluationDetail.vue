<!-- 应发电量/执行信息详情 -->
<template>
  <detail-layout :labelList="CapacityEvaluationList" :form="baseForm" title="执行信息" labelWidth="240px">
    <template v-slot:actualFaultLossElectric>
      <a-col :span="24" class="detail_layout_content ">
        <span class="left ">参考故障损失电量(万kWh)</span>
        <span class="right">{{ getLabel(baseForm.actualFaultLossElectric, null) }}
          <a-popover v-model="visible" title="损失电量明细" trigger="click" placement="top"
            overlayClassName="generation-popover" :getPopupContainer="(node) => node.parentNode"
            v-if="!$isEmpty(baseForm.actualFaultLossDetail)">
            <template slot="content">
              <div class="generation-popover-inner-content">
                <span>{{ baseForm.actualFaultLossDetail || '--' }}</span>
              </div>
              <div class="flex-end m-t-16">
                <throttle-button label="关闭" size="small" class="solar-eye-btn-primary-cancel"
                  @click="visible = false" />
              </div>
            </template>
            <span class="generation-check-detail m-l-8">
              查看损失原因分析
            </span>
          </a-popover>
        </span>
      </a-col>
    </template>
    <template #insideFaultLossElectric>
      <a-col :span="8" class="detail_layout_content">
        <span class="left ">站内故障损失电量(万kWh)</span>
        <span class="right">{{ getLabel(baseForm.insideFaultLossElectric, null) }}</span>
      </a-col>
    </template>
    <template #actualInsideFaultLossCutElectric>
      <a-col :span="8" class="detail_layout_content">
        <span class="left ">站内故障可核减电量(万kWh)</span>
        <span class="right">{{ getLabel(baseForm.actualInsideFaultLossCutElectric, null) }}</span>
      </a-col>
    </template>
    <template v-slot:actualRationLossElectric>
      <a-col :span="24" class="detail_layout_content ">
        <span class="left ">参考限电损失电量(万kWh)</span>
        <span class="right">{{ getLabel(baseForm.actualRationLossElectric, null) }}</span>
      </a-col>
    </template>
    <template v-slot:rationLossElectric>
      <a-col :span="8" class="detail_layout_content ">
        <span class="left ">限电损失电量(万kWh)</span>
        <span class="right">{{ getLabel(baseForm.rationLossElectric, null) }}</span>
      </a-col>
    </template>
    <template v-slot:actualRationLossCutElectric>
      <a-col :span="8" class="detail_layout_content">
        <span class="left ">限电损失可核减电量(万kWh)</span>
        <span class="right">{{ getLabel(baseForm.actualRationLossCutElectric, null) }}</span>
      </a-col>
    </template>
    <template #actualShadowLossElectric>
      <a-col :span="24" class="detail_layout_content ">
        <span class="left ">参考阴影损失电量(万kWh)</span>
        <span class="right">{{ getLabel(baseForm.actualShadowLossElectric, null) }}</span>
      </a-col>
    </template>
    <template #actualOtherLossElectric>
      <a-col :span="24" class="detail_layout_content ">
        <span class="left ">参考其他损失电量(万kWh)</span>
        <span class="right">{{ getLabel(baseForm.actualOtherLossElectric, null) }}</span>
      </a-col>
    </template>
  </detail-layout>
</template>

<script>
export default {
  name: 'CapacityEvaluationDetail',
  props: {
    baseForm: {
      type: Object,
      default: () => ({
        actualfaultLossDetail: null
      })
    }
  },
  data () {
    return {
      visible: false,
      CapacityEvaluationList: [
        { label: '上网电量(万kWh)', key: 'actualOnElectric', span: 24 },
        { slot: 'actualFaultLossElectric' },
        { slot: 'insideFaultLossElectric' },
        { slot: 'actualInsideFaultLossCutElectric' },
        { label: '站内故障损失原因', key: 'faultLossReason', span: 24 },
        { slot: 'actualRationLossElectric' },
        { slot: 'rationLossElectric' },
        { slot: 'actualRationLossCutElectric' },
        { label: '限电损失原因', key: 'rationLossReason', span: 24 },
        { slot: 'actualShadowLossElectric' },
        { label: '阴影损失原因',
          key: 'shadowLossReason',
          span: 24
        },
        { slot: 'actualOtherLossElectric' },
        { label: '其他损失原因', key: 'otherLossReason', span: 24 }
      ]
    };
  },
  watch: {
    baseForm (val) {
      console.log('val :>> ', val);
    }
  }
};
</script>
<style lang="less" scoped>
.left-label-wrap {
  display: flex;
  justify-content: flex-end;
  line-height: 22px;
  align-items: center;
}
.left {
  width: 240px !important;
}

.generation-check-detail {
  cursor: pointer;
  color: #267DCF;
  margin-left: 16px;
  text-decoration: underline;
  text-underline-offset: 5px;
}

.generation-popover {
  .ant-popover-title {
    border-bottom: none;
    font-weight: 600;
  }

  .generation-popover-inner-content {
    max-width: 520px;
    max-height: 180px;
    overflow: auto;
  }
}
</style>
