import { axios } from '@/utils/erp-request';

// 查询列表数据
export function getUserRoleSyncInfo (data) {
  return axios({
    url: '/jobs/v1/list',
    method: 'POST',
    data
  });
}

// 执行定时任务
export function execute (data) {
  return axios({
    url: '/jobs/v1/execute',
    method: 'POST',
    data
  });
}

// 更改定时任务状态
export function updateStatus (data) {
  return axios({
    url: '/jobs/v1/updateStatus',
    method: 'POST',
    data
  });
}
