<template>
    <div :id="id" class="quality-guge"></div>
</template>
<script>
import echarts from '@/utils/enquireEchart';
export default {
  data () {
    return {
      id: Math.random(),
      chart: null,
      colorList: ['#F56C6C', '#E39756', '#44BE9B'],
      colorCircleList: ['#BF5E59', '#AF7746', '#37987C']
    };
  },
  props: {
    name: {
      required: true,
      type: String
    },
    value: {
      required: true,
      type: [String, Number]
    },
    theme: {
      type: String,
      default: 'light'
    }
  },
  created () {

  },
  watch: {
    theme () {
      if (this.chart) {
        this.chart.setOption(this.getOptions());
      }
    },
    value () {
      if (this.chart) {
        this.chart.setOption(this.getOptions());
      }
    }
  },
  mounted () {
    if (document.getElementById(this.id)) {
      this.chart = echarts.getInstanceByDom(document.getElementById(this.id));
      if (!this.chart) {
        this.chart = echarts.init(document.getElementById(this.id));
        this.$once('hook:beforeDestroy', function () {
          echarts.dispose(this.chart);
        });
      }
      this.chart.setOption(this.getOptions());
      console.log('mounted');
    }
  },
  activated () {
    if (!this.chart) {
      this.chart = echarts.init(document.getElementById(this.id));
      this.chart.setOption(this.getOptions());
    }
  },
  methods: {
    getOptions () {
      let getvalue = Number(this.value); let name = this.name; let getmax = 100;
      let color = getvalue < 20 ? this.colorList[0] : (getvalue > 85 ? this.colorList[2] : this.colorList[1]);
      let color1 = getvalue < 20 ? this.colorCircleList[0] : (getvalue > 85 ? this.colorCircleList[2] : this.colorCircleList[1]);

      let options = {
        title: [{

          text: name,

          bottom: '12%',

          x: 'center',

          borderColor: color,
          textStyle: {
            fontSize: 13,
            color: '#bfbfbf'

          }

        }],

        angleAxis: {

          show: false,

          max: getmax * 360 / 270, // -45度到225度，二者偏移值是270度除360度

          type: 'value',

          startAngle: 225, // 极坐标初始角度

          splitLine: {

            show: false

          }

        },

        barMaxWidth: 8, // 圆环宽度

        radiusAxis: {

          show: false,

          type: 'category'

        },

        // 圆环位置和大小

        polar: {

          center: ['50%', '50%'],

          radius: '140%'

        },

        series: [{

          type: 'bar',

          data: [{ // 上层圆环，显示数据

            value: getvalue,

            itemStyle: {

              color: color

            }

          }],

          barGap: '-100%', // 柱间距离,上下两层圆环重合

          coordinateSystem: 'polar',

          roundCap: true, // 顶端圆角

          z: 3 // 圆环层级，同zindex

        },

        { // 下层圆环，显示最大值

          type: 'bar',

          data: [{

            value: getmax,

            itemStyle: {

              color: this.theme == 'dark' ? '#384C6B' : '#F0F0F0',

              borderWidth: 0

            }

          }],

          barGap: '-100%',

          coordinateSystem: 'polar',

          roundCap: true,

          z: 1

        },

        // 仪表盘

        {

          type: 'gauge',

          startAngle: 225, // 起始角度，同极坐标

          endAngle: -45, // 终止角度，同极坐标

          axisLine: {

            show: false

          },

          splitLine: {

            show: false

          },

          axisTick: {

            show: false

          },

          axisLabel: {

            show: false

          },

          splitLabel: {

            show: false

          },

          pointer: { // 分隔线

            shadowColor: 'auto', // 默认透明

            shadowBlur: 20,

            length: '80%',

            width: '1'

          },

          itemStyle: {

            color: color1,

            borderColor: color1,

            borderWidth: 2

          },

          detail: {

            formatter: function (params) {
              return getvalue + '%';
            },

            color: this.theme == 'dark' ? '#fff' : '#5b5b5b',
            fontWeight: '500',
            fontSize: 16,

            offsetCenter: [0, 40]

          },

          title: {

            show: false

          },

          data: [{

            value: getvalue

          }]

        }, {

          name: '外部刻度',

          type: 'gauge',

          //  center: ['20%', '50%'],

          radius: '98%',

          min: 0, // 最小刻度

          max: 100, // 最大刻度

          splitNumber: 3, // 刻度数量

          startAngle: 225,

          endAngle: -45,

          axisLine: {

            show: true,

            lineStyle: {

              width: 2,

              color: [

                [1, 'rgba(0,0,0,0)']

              ]

            }

          }, // 仪表盘轴线

          axisLabel: {

            show: false,

            color: '#4d5bd1',

            distance: 20

          }, // 刻度标签。

          axisTick: {

            show: true,

            splitNumber: 7,

            lineStyle: {

              color: this.theme == 'dark' ? '#384C6B' : '#d6d6d6', // 用颜色渐变函数不起作用

              width: 2

            },

            length: -8

          }, // 刻度样式

          splitLine: {

            show: false,

            length: -10,

            lineStyle: {

              color: '#C7CBCF' // 用颜色渐变函数不起作用

            }

          }, // 分隔线样式

          detail: {

            show: false

          },

          pointer: {

            show: false

          }

        }, {// 指针外环

          'type': 'pie',

          'hoverAnimation': false,

          'legendHoverLink': false,

          'radius': ['10%', '15%'],

          'z': 10,

          'label': {

            'normal': {

              'show': false

            }

          },

          'labelLine': {

            'normal': {

              'show': false

            }

          },

          'data': [{

            'value': 100,

            itemStyle: {

              normal: {

                color: color1

              }

            }

          }]

        },

        {// 指针内环

          'type': 'pie',

          'hoverAnimation': false,

          'legendHoverLink': false,

          'radius': ['0%', '10%'],

          'z': 10,

          'label': {

            'normal': {

              'show': false

            }

          },

          'labelLine': {

            'normal': {

              'show': false

            }

          },

          'data': [{

            'value': 100,

            itemStyle: {

              normal: {

                color: this.theme == 'dark' ? '#1B283D' : '#fff'

              }

            }

          }]

        }

        ]
      };
      return options;
    }
  }
};
</script>
<style scoped>
.quality-guge {
    width: 180px;
    height: 184px;
}
</style>
