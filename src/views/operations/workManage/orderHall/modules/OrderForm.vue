<template>
  <!-- 工单表单 -->
<div class="drawer-form-com">
    <div class="drawer-form-content">
      <a-spin :spinning="loading">
        <order-base :id="order.id" ref='orderBase' :isFarm="isFarm" :type="type" @updateOrder="updateOrder" />
        <!-- 指派 -->
        <template v-if="type == '5'">
          <div class="order-dispose">
            <div class="title-box">
              <span class="before"></span>
              <span>执行信息</span>
            </div>
          </div>
          <a-form-model ref="sendForm" :model="sendform" :rules="rules" :labelCol="{ style:'width: 172px' }"
                        :wrapperCol="{ style:'width: calc(100% - 172px)' }">
            <a-row :gutter="24">
              <a-col :xl='8' :sm='12' :xs='24'>
                <a-form-model-item label="处理人" prop="eliminateId">
                  <a-select v-model="sendform.eliminateId" @change="eliminateChange" placeholder="请选择" style="width: 100%;">
                    <a-select-option v-for="item in users" :key="item.id" :value="item.id" :disabled="!item.ifQualified" :code="item.username">{{item.realname}}
                    </a-select-option>
                  </a-select>
                </a-form-model-item>
              </a-col>
            </a-row>
          </a-form-model>
        </template>

        <!-- 处理-执行信息 -->
        <div v-show="[3,5].includes(this.order.flowSts) || (order.orderStatus == '6' && type == '7') || (order.resolveResult && ['6','7','8','9'].includes(order.orderStatus))">
          <order-dispose ref="orderDispose" :disabled="order.orderStatus != '6' && order.flowSts != 3 || type =='3'" :isDiagnosis="order.orderSource" :isFarm="order.isFarm" />
        </div>
      </a-spin>
    </div>

    <div @click="openFlowChart" class="flow-chart-btn">
      <svg-icon iconClass="flow"></svg-icon>
      流程图</div>
    <flow-chart-drawer v-if="showDiagram" ref="flowChartDrawer" :parentId="parentId" :processInstanceId="order.processInstanceId" :flowUser="order.flowUser" processDefinitionKey="户用_工单"/>

    <!-- 查看详情 -->
    <div v-if="type == '3'" class="drawer-form-foot">
      <throttle-button label="返回" type="info" @click="cancel" class="solar-eye-btn-primary-line" />
    </div>
    <div v-else class="drawer-form-foot">
      <!-- 指派 -->
      <template v-if="type == '5'">
        <throttle-button label="指派" :loading="loading" @click="orderSend" />
      </template>
      <!-- 工单处理/执行 -->
      <template v-else-if="['3','6'].includes(order.orderStatus) || order.flowSts == 3">
        <throttle-button label="退回" :loading="loading" @click="showReturnModal()" />
        <throttle-button v-show="order.orderStatus == '6' || order.flowSts == 3" label="保存" :loading="loading"
                         @click="confirmOrderResolve('0')" />
        <throttle-button label="提交" :loading="loading" @click="confirmOrderResolve('1')" />
      </template>
    </div>
    <!-- 退回 弹窗 -->
    <Return v-model="showReturn" :visible="showReturn" :row="rowReturn" @cancel="cancel"></Return>
  </div>
</template>

<script>
import moment from 'moment';
import orderBase from './OrderBasic';
import initDict from '@/mixins/initDict';
import orderDispose from './OrderDispose';
import Return from './Return';
import { orderProcess, orderDistributeUserListByPSId } from '@api/operations/orderHall';
export default {
  name: 'orderForm',
  mixins: [initDict],
  components: {
    orderBase,
    orderDispose,
    Return
  },
  props: {
    dict: {
      type: Object,
      default: () => { return {}; }

    },
    /* 抽屉挂在的DOM节点ID，必要参数 */
    parentId: {
      type: String,
      required: true,
      default: ''
    }
  },
  data () {
    return {
      loading: false,
      order: {
        id: undefined
      }, // 工单详情信息
      type: '3',
      users: [],
      sendform: {
        eliminateId: undefined, // 处理人
        eliminateLabel: undefined // 处理人
      },
      rules: {
        eliminateId: [{
          required: true,
          message: '请选择处理人'
        }]
      },
      showReturn: false,
      rowReturn: {},
      showDiagram: false,
      isFarm: ''
    };
  },
  methods: {
    moment,
    // 初始化 type  详情: 3  指派: 5  处理: 7
    async init (type, row) {
      this.loading = true;
      if (!this.dict.hasOwnProperty('two_fault_classify') && !this.dictMap.hasOwnProperty('two_fault_classify')) {
        this.getDictMap('taskType,two_sts_task,has_elec_status,check_content,two_fault_classify,two_device_run_sts');
      }
      Object.assign(this.order, row);
      this.isFarm = row && row.isFarm ? row.isFarm : '';
      this.type = type;
      this.row = row;
      let statusLabel = row.flowStsName;
      switch (this.type) {
        case '3':
          return { 'title': '工单详情', 'statusLabel': statusLabel };
        case '5':
          return { 'title': '工单指派', 'statusLabel': statusLabel };
        case '7':
          return { 'title': '工单处理', 'statusLabel': statusLabel };
      }
    },
    // 打开流程图
    openFlowChart () {
      this.showDiagram = true;
      this.$nextTick(() => {
        this.$refs.flowChartDrawer.openView();
      });
    },

    // 更新order信息
    updateOrder (order) {
      if (order) {
        Object.assign(this.order, order);
        if (this.type == '5') {
          this.getUserList(order.psId || order.psaId);
        }
        // 执行信息
        if (this.$refs.orderDispose) {
          this.$refs.orderDispose.updataDispose(order);
        }
      }
      this.loading = false;
    },

    // 处理人change
    eliminateChange (val, option) {
      this.sendform.eliminateLabel = option.data.attrs.code;
    },

    // 指派
    orderSend () {
      this.loading = true;
      this.$refs.sendForm.validate(valid => {
        if (valid) {
          let map = {
            orderType: this.order.orderType,
            handleType: 4,
            handler: this.sendform.eliminateLabel,
            id: this.order.id,
            orderStatus: this.order.orderStatus,
            eliminateId: this.sendform.eliminateId,
            taskId: this.order.taskId,
            processInstanceId: this.order.processInstanceId,
            flowSts: this.order.flowSts,
            taskDefKey: this.order.taskDefKey
          };
          if (this.order.orderSource == '4') {
            if (!this.$refs.orderBase.getValidate()) {
              this.loading = false;
              return;
            }
            let obj = this.$refs.orderBase.form;
            map = Object.assign(map, {
              docs: obj.files,
              faultRemark: obj.faultRemark,
              orderSource: obj.orderSource,
              orderStatus: obj.orderStatus,
              defectId: obj.defectId
              // faultClassify: obj.faultClassify.toString(),
              // deviceType: obj.deviceTypeList[obj.deviceTypeList.length - 1],
              // predictPlanTimeLimit: obj.predictPlanTimeLimit,
              // deviceTypeName: obj.deviceTypeName,
              // defectId: obj.defectId
            });
          }
          orderProcess(map).then(res => {
            this.loading = false;
            this.$message.success(res.message ? res.message : '操作成功');
            this.cancel();
          }).catch(() => {
            this.loading = false;
          });
        } else {
          this.loading = false;
          return false;
        }
      });
    },
    // 获取指派人列表
    getUserList (psId) {
      orderDistributeUserListByPSId({
        'psId': psId,
        'orderType': this.order.orderType
      }).then(res => {
        this.users = res.result_data;
        this.loading = false;
      }).catch(() => {
        this.users = [];
        this.loading = false;
      });
    },
    // 显示退回弹窗
    showReturnModal () {
      this.showReturn = true;
      this.rowReturn = this.order;
    },

    //  工单处理 type 0:保存  1:提交
    confirmOrderResolve (type) {
      // 保存时不校验附件，反之
      if (type == 0) {
        Object.assign(this.$refs.orderDispose.rules, {
          resolveDocs: [{ required: false }],
          workContent: [{ required: false }],
          ticketText: [{ required: false }]
        });
      } else {
        Object.assign(this.$refs.orderDispose.rules, {
          resolveDocs: [{
            required: true,
            message: '请选择需要上传的文件',
            trigger: 'change'
          }],
          workContent: [{
            required: true,
            message: '请选择工作内容'
          }], // 工作内容
          ticketText: [{
            required: true,
            message: '当前工作内容需开具工作票'
          }] // 关联两票
        });
      }
      this.$refs.orderDispose.saveOrderDispose(type).then(params => {
        let map = {
          'handleType': type,
          'orderType': this.order.orderType,
          'sourceId': this.order.sourceId,
          'feedbackPhone': this.order.psTelephone,
          'psId': this.order.psId,
          'psaName': this.order.psaName,
          'id': this.order.id,
          'orderStatus': this.order.orderStatus,
          'taskId': this.order.taskId,
          'processInstanceId': this.order.processInstanceId,
          'type': type,
          'faultClassify': (['1', '2'].includes(this.order.faultClassify) ? '1' : '2'),
          ...params
        };
        this.loading = true;
        orderProcess(map).then(res => {
          this.loading = false;
          this.$message.success(res.message ? res.message : '操作成功');
          this.cancel();
        }).catch(() => {
          this.loading = false;
        });
      }).catch(() => {
        this.loading = false;
      });
    },
    // 取消按钮事件
    cancel () {
      this.$emit('cancel');
      this.reset();
    },
    // 数据重置
    reset () {
      let dict = { ...this.dict, ...this.dictMap };
      if (this.$refs.orderDispose) {
        this.$refs.orderDispose.clearValidate();
      }
      Object.assign(this.$data, this.$options.data());
      this.dictMap = dict;
    }
  }
};
</script>

<style lang="less" scoped>
:deep(.ant-form-item) {
  width: 100%;
  display: inline-flex;
}
</style>
