import {
  axios
} from '@/utils/request';

const url = process.env.VUE_APP_API_BI_URL;
export function getDailyList (parameter) {
  return axios({
    url: url + '/reportDaily/list',
    method: 'post',
    data: parameter
  });
}

export function dailySubmit (parameter) {
  return axios({
    url: url + '/reportDaily/submit',
    method: 'post',
    data: parameter
  });
}

export function dailyInfo (parameter) {
  return axios({
    url: url + '/reportView/dailyInfo',
    method: 'post',
    data: parameter
  });
}

export function dailyAuditPass (parameter) {
  return axios({
    url: url + '/reportDaily/auditPass',
    method: 'post',
    data: parameter
  });
}

export function dailyBatchAuditPass (parameter) {
  return axios({
    url: url + '/reportDaily/batchAuditPass',
    method: 'post',
    data: parameter
  });
}

export function dailyAuditFailed (parameter) {
  return axios({
    url: url + '/reportDaily/auditFailed',
    method: 'post',
    data: parameter
  });
}
export function dailyAdminDel (parameter) {
  return axios({
    url: url + '/reportDaily/adminDel',
    method: 'post',
    data: parameter
  });
}

export function dailyTianBaoDel (parameter) {
  return axios({
    url: url + '/reportDaily/tianBaoDel',
    method: 'post',
    data: parameter
  });
}

export function dailyImportExcel (parameter) {
  return axios({
    url: url + '/reportDaily/importExcel',
    method: 'post',
    data: parameter
  });
}

export function monthlyImportExcel (parameter) {
  return axios({
    url: url + '/reportMonthly/importExcel',
    method: 'post',
    data: parameter
  });
}

export function dailyExportDaily (parameter) {
  return axios({
    url: url + '/reportDaily/exportDaily',
    method: 'post',
    data: parameter
  });
}

export function dailyShowAuditErrorCause (parameter) {
  return axios({
    url: url + '/report/showAuditErrorCause',
    method: 'post',
    data: parameter
  });
}
export function getColumnInfo (parameter) {
  return axios({
    url: url + '/reportView/getColumnInfo',
    method: 'post',
    data: parameter
  });
}
// 日报数据一键回填
export function backFill (data) {
  return axios({
    url: url + '/reportData/backFill',
    method: 'post',
    data
  });
}

export function sampleListData (data) {
  return axios({
    url: url + '/reportData/sampleListData',
    method: 'post',
    data
  });
}
//  样板机回显确认框数据集合
export function backFillSampleListData (data) {
  return axios({
    url: url + '/reportData/backFillSampleListData',
    method: 'post',
    data
  });
}

// 查看退回原因
export function showAuditErrorCause (data) {
  return axios({
    url: url + '/report/showAuditErrorCause',
    method: 'post',
    data
  });
}

// 月报列表
export function getMonthlyList (data) {
  return axios({
    url: url + '/reportMonthly/list',
    method: 'post',
    data
  });
}
