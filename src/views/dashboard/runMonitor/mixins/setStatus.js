import {
  getSystemCodeList
} from '@/api/health/healthapi.js';
import {
  setStatusSign
} from '@/api/monitor/runMonitor';
export default {
  data () {
    return {
      stationStatusOptions: [],
      darkSealBg: require('../../../../assets/images/public/seal.png'),
      lightSealBg: require('../../../../assets/images/public/light_seal.png')
    };
  },
  props: {
    commonParams: {
      type: Object,
      default: () => {
      }
    },
    categoryList: {
      type: Array,
      default: () => {
        return [];
      }
    },
    remarkList: {
      type: Array,
      default: () => {
        return [];
      }
    }
  },
  methods: {
    gotoDiagnosisCenter (psId, alarmGrade, psName) {
      // 诊断中心停机故障、通讯中断、隐患运行权限
      // if ((this.show('alarm:stop') || this.show('alarm:comloss') || this.show('alarm:hidden')) && this.isShowMenu) {
      //
      // }
      window.comFromMonitor = true;
      this.$router.push({
        name: 'AlarmCenter',
        params: { psId: psId, psName: psName, categoryList: this.categoryList, remarkList: this.remarkList }
      });
    },
    async getSystemCodeList () {
      let res = await getSystemCodeList({ firstTypeCode: '0059' });
      this.menuItemList = res.result_data['0059'];
      this.stationStatusOptions = [
        {
          value: 'ALL',
          label: '全部'
        },
        ...this.menuItemList.map(item => {
          return {
            value: item.secondTypeCode,
            label: item.secondName
          };
        }),
        {
          value: -1,
          label: '未挂牌'
        }
      ];
    },
    getSelectColor (item, index) {
      return item === this.listData[index].statusSign ? ('#2A3E5B') : '';
    },
    handleMenuClick (e, index, item) {
      if (e.domEvent.target.innerText.length > 6) { // 屏蔽按enter键输入紊乱的情况
        return;
      }
      setStatusSign({
        psId: item.psId,
        statusSign: this.listData[index].statusSign != e.key ? e.key : ''
      }).then(res => {
        if (res.result_code === '1') {
          let statusSignName = this.listData[index].statusSign != e.key ? e.domEvent.target.innerText : '';
          let statusSign = this.listData[index].statusSign != e.key ? e.key : '';
          this.$set(this.listData[index], 'statusSignName', statusSignName);
          this.$set(this.listData[index], 'statusSign', statusSign);
        }
      });
    }
  }
};
