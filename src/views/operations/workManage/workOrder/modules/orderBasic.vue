<template>
  <a-form-model :model="form" ref="baseForm" :rules="isEdit ? rules:{}" :labelCol="{ style:'width: 120px' }" :wrapperCol="{ style:'width: calc(100% - 120px)' }">
    <a-row>
      <template v-if="isEdit">
        <a-col :span='24'>
          <div class="order-dispose">
            <div class="title-box">
              <span class="before"></span>
              <span>基本信息</span>
            </div>
          </div>
        </a-col>
        <a-col :xl='8' :sm='12' :xs='24'>
          <a-form-model-item label="缺陷类别" prop="faultClassify">
            <a-input disabled :value="getFaultClassify" style="width: 100%;" />
          </a-form-model-item>
        </a-col>
        <a-col :xl='8' :sm='12' :xs='24'>
          <a-form-model-item label="设备状态">
            <a-input disabled :value="form.twoDeviceRunStsName" style="width: 100%;" />
          </a-form-model-item>
        </a-col>
        <a-col :xl='8' :sm='12' :xs='24'>
          <a-form-model-item label="缺陷来源">
            <a-input disabled :value="form.orderFrom" style="width: 100%;" />
          </a-form-model-item>
        </a-col>
        <a-col :xl='8' :sm='12' :xs='24'>
          <a-form-model-item label="电站名称">
            <a-input disabled :value="form.psaName" :title="form.psaName" style="width: 100%;" />
          </a-form-model-item>
        </a-col>
        <a-col :xl='8' :sm='12' :xs='24'>
          <a-form-model-item label="故障名称" prop="faultName">
            <a-input disabled :value="form.faultName" style="width: 100%;" />
          </a-form-model-item>
        </a-col>
        <a-col :xl='8' :sm='12' :xs='24'>
          <a-form-model-item label="实体电站" :style="{opacity:form.orderSource=='4'?1:0}">
            <a-input disabled :value="form.realPsaName" style="width: 100%;" />
          </a-form-model-item>
        </a-col>
        <a-col :xl='8' :sm='12' :xs='24'>
          <a-form-model-item label="电站地址">
            <a-input disabled :value="form.psLocation" style="width: 100%;" />
          </a-form-model-item>
        </a-col>
        <a-col :xl='8' :sm='12' :xs='24'>
          <a-form-model-item label="户主电话">
            <a-input disabled :value="form.psTelephone" style="width: 100%;" />
          </a-form-model-item>
        </a-col>
        <a-col :xl='8' :sm='12' :xs='24'>
          <a-form-model-item label="设备类型" prop="deviceTypeList">
            <g-cascader v-model="form.deviceTypeList" :options="options" style="width: 100%;" @change="deviceTypeChange" />
          </a-form-model-item>
        </a-col>
        <a-col :xl='8' :sm='12' :xs='24'>
          <a-form-model-item label="设备名称">
            <a-input disabled :value="form.deviceName" :title="form.deviceName" style="width: 100%;" />
          </a-form-model-item>
        </a-col>
        <a-col :xl='8' :sm='12' :xs='24'>
          <a-form-model-item label="设备编号">
            <a-input disabled :value="form.deviceUuid" style="width: 100%;" />
          </a-form-model-item>
        </a-col>
        <a-col :xl='8' :sm='12' :xs='24'>
          <a-form-model-item label="发现人">
            <a-input disabled :value="form.findUserName" style="width: 100%;" />
          </a-form-model-item>
        </a-col>
        <a-col :xl='8' :sm='12' :xs='24'>
          <a-form-model-item label="发现时间">
            <a-input disabled :value="form.findTime" style="width: 100%;" />
          </a-form-model-item>
        </a-col>
        <a-col :xl='8' :sm='12' :xs='24'>
          <a-form-model-item label="预计消除时间" prop="predictPlanTimeLimit">
            <a-input disabled v-model="form.predictPlanTimeLimit" style="width: 100%;" />
          </a-form-model-item>
        </a-col>
        <template v-if="(type == '7') || (form.resolveResult && ['6','7','8','9'].includes(form.orderStatus)) ||Number(form.flowSts)>=3">
          <a-col :xl='8' :sm='12' :xs='24'>
            <a-form-model-item label="消除人">
              <a-input disabled :value="form.eliminateName" style="width: 100%;" />
            </a-form-model-item>
          </a-col>
          <a-col :xl='8' :sm='12' :xs='24'>
            <a-form-model-item label="领取时间">
              <a-input disabled :value="form.actualStartTime" style="width: 100%;" />
            </a-form-model-item>
          </a-col>
        </template>
        <a-col :xl='8' :sm='12' :xs='24' v-if="form.isFarm=='1' && Number(form.flowSts)>=3">
          <a-form-model-item label="运维经销商">
            <a-input disabled :value="form.operationalDealers" style="width: 100%;" />
          </a-form-model-item>
        </a-col>
        <a-col :span='24'>
          <a-form-model-item label="缺陷描述">
            <a-textarea disabled :value="form.faultContent" :title="form.faultContent" style="width: 100%;" />
          </a-form-model-item>
        </a-col>
        <a-col :span='24'>
          <a-form-model-item label="备注">
            <a-textarea :disabled="false" :max-length="1000" v-model="form.faultRemark" :title="form.faultRemark" style="width: 100%;" />
          </a-form-model-item>
        </a-col>
        <a-col :span='24'>
          <a-form-model-item label="附件">
            <uploadFile :disabled="false" v-model="form.files" :maxNum="5" :tipIsInline="true" tip="最多上传5个文件,且上传的附件最大不超过10MB!"></uploadFile>
          </a-form-model-item>
        </a-col>
      </template>
      <template v-else>
        <detail-layout :labelList="baseInfoDetail" :form="form" title="基本信息"></detail-layout>
      </template>
    </a-row>
  </a-form-model>
</template>

<script>
import { orderDetail, getPlanTimeLimit } from '@/api/common_gy/workOrder.js';
import uploadFile from '@/components/common_gy/uploadFiles';
import { getDeviceTypeTree, getDeviecClassByDeviceId } from '@/api/common_gy/faultManage.js';
import { TENANT_ID } from '@/store/mutation-types';
export default {
  components: {
    uploadFile
    // workOperation
  },
  props: {
    id: {
      type: [String, Number],
      default: undefined
    },
    type: {
      type: String,
      default: undefined
    },
    isFarm: {
      type: String,
      default: undefined
    }
  },
  watch: {
    id: {
      immediate: true,
      handler: function () {
        this.getOrder();
      }
    }
  },
  computed: {
    getFaultClassify () {
      let faultClassify = this.form.faultClassify;
      if (!faultClassify) {
        return '--';
      }
      return this.faultClassify[faultClassify];
    },
    isEdit () {
      return (this.form.orderSource == '4') && (this.form.flowSts < 3) && this.type != 3;
    }
  },
  data () {
    return {
      faultClassify: { '1': '一类', '2': '二类', '3': '三类', '4': '四类' },
      form: {
        planId: undefined,
        faultClassify: undefined,
        psaName: undefined,
        deviceTypeName: undefined,
        deviceName: undefined,
        deviceUuid: undefined,
        eliminateName: undefined,
        findTime: undefined,
        twoDeviceRunStsName: undefined,
        actualStartTime: undefined,
        predictPlanTimeLimit: undefined,
        faultContent: undefined,
        faultRemark: undefined,
        files: [],
        orderFrom: '',
        realPsaName: '',
        sceneCondition: '',
        deviceTypeList: []
      },
      processDefKey: '',
      rules: {
        deviceTypeList: [{
          required: true,
          message: '设备类型不能为空',
          trigger: 'change'
        }],
        predictPlanTimeLimit: [{
          required: true,
          message: '预计消除时间不能为空',
          trigger: 'blur'
        }],
        faultClassify: [{
          required: true,
          message: '缺陷类别不能为空',
          trigger: 'blur'
        }]
      },
      baseInfoDetail: [
        { label: '缺陷类别', key: 'faultClassifyName' },
        { label: '设备状态', key: 'twoDeviceRunStsName' },
        { label: '缺陷来源', key: 'orderFrom' },
        { label: '电站名称', key: 'psaName' },
        { label: '故障名称', key: 'faultName' },
        {
          label: '实体电站',
          key: 'realPsaName',
          func: (params) => {
            return params.orderSource == '4';
          }
        },
        { label: '电站地址', key: 'psLocation' },
        { label: '户主电话', key: 'psTelephone' },
        { label: '设备类型', key: 'deviceTypeName' },
        { label: '设备名称', key: 'deviceName' },
        { label: '设备编号', key: 'deviceUuid' },
        { label: '发现人', key: 'findUserName' },
        { label: '发现时间', key: 'findTime' },
        { label: '预计消除时间', key: 'predictPlanTimeLimit' },
        {
          label: '消除人',
          key: 'eliminateName',
          func: (params) => {
            return (params.type == '7') || (params.resolveResult && ['6', '7', '8', '9'].includes(params.orderStatus)) || Number(params.flowSts) >= 3;
          }
        },
        {
          label: '领取时间',
          key: 'actualStartTime',
          func: (params) => {
            return (params.type == '7') || (params.resolveResult && ['6', '7', '8', '9'].includes(params.orderStatus)) || Number(params.flowSts) >= 3;
          }
        },
        {
          label: '运维经销商',
          key: 'operationalDealers',
          func: (params) => {
            return params.isFarm == '1' && Number(params.flowSts) >= 3;
          }
        },
        { label: '缺陷描述', key: 'faultContent', span: 24 },
        { label: '备注', key: 'faultRemark', span: 24 },
        { label: '附件', key: 'files', span: 24, type: 'file:text' }
      ],
      options: [],
      deviceType: []
    };
  },
  methods: {
    // 获取工单详情
    getOrder () {
      if (this.$refs.baseForm) {
        this.$refs.baseForm.clearValidate();
      }
      if (this.id) {
        orderDetail({ 'id': this.id, isFarm: this.isFarm }).then(res => {
          let result = res.result;
          delete result.taskId;
          delete result.flowUser;
          delete result.taskDefkey;
          if (this.type != '3') {
            delete result.processInstanceId;
          }
          if ((result.orderSource == '4') && (Number(result.flowSts) < 3) && this.type != 3) {
            getDeviceTypeTree({ psId: result.psId }).then((res) => {
              this.options = res.result;
            }).catch(() => {
              this.options = [];
            });
          }
          this.form = Object.assign({}, this.form, result);
          this.form.type = this.type;
          this.form.flowSts = result.flowSts ? Number(result.flowSts) : '';
          if (this.form.deviceTypeList && this.form.deviceTypeList.length > 0) {
            this.form.deviceTypeList = this.form.deviceTypeList.map(item => (item = Number(item)));
          }
          this.$emit('updateOrder', result);
        }).catch(() => {
          this.$emit('updateOrder', null);
        });
      } else {
        this.form = Object.assign(this.form, this.$options.data().form);
      }
    },
    // 设备类型change事件
    deviceTypeChange (val) {
      this.form.faultClassify = '';
      if (!Array.isArray(val) || !val.length || !this.form.psId || val.length <= 1) {
        this.form.deviceTypeList = undefined;
        return;
      }
      // 设置缺陷类别
      getDeviecClassByDeviceId({
        typeId: val[val.length - 1],
        faultType: this.form.twoDeviceRunSts
      }).then((res) => {
        this.form.faultClassify = res.result;
      });
      this.getPlanTimeLimit();
    },
    // 获取预计消除时间
    getPlanTimeLimit () {
      getPlanTimeLimit({
        deviceTypeList: this.form.deviceTypeList,
        twoDeviceRunSts: this.form.twoDeviceRunSts,
        sysTenantId: Vue.ls.get(TENANT_ID),
        findTime: this.form.findTime
      }).then(res => {
        this.form.predictPlanTimeLimit = res.result;
      });
    },
    // 校验表单返回表单数据
    getValidate () {
      let isValid = true;
      this.$refs.baseForm.validate((valid) => {
        if (valid) {
          isValid = true;
          this.$emit('updateOrder', this.form);
        } else {
          isValid = false;
        }
      });
      return isValid;
    }
  }
};
</script>

<style lang="less" scoped>
  :deep(.ant-form-item) {
    width: 100%;
    display: inline-flex;
  }
</style>
