import {
  postAction
} from '@/api/common_gy/erp-manage';

// 新增任务
export const addTask = (params) => postAction('/task/addTask', params);
// 修改任务
export const updateTask = (params) => postAction('/task/updateTask', params);
// 提交
export const submitTask = (params) => postAction('/task/submitTask', params);
// 派发
export const distributeTask = (params) => postAction('/task/distributeTask', params);
// 列表
export const queryPageList = (params) => postAction('/task/queryPageList', params);
export const queryPageListOth = (params) => postAction('/inspect/list', params);
// 领取
export const receiveTask = (params) => postAction('/task/receiveTask ', params);
// 执行任务
export const performTask = (params) => postAction('/task/performTask', params);
// 获取历史记录
export const getHistory = (params) => postAction('/task/getHistory', params);
// 获取巡检轨迹
export const trackInspection = (params) => postAction('/task/trackInspection', params);
// 获取任务详情
export const getTaskInfo = (params) => postAction('/task/getTaskInfo', params);
// 获取巡检内容
export const getInspectionDetail = (params) => postAction('/task/getInspectionDetail', params);
// 导出
export const exportInfo = (params) => postAction('/task/export', params);
export const exportInfoOth = (params) => postAction('/inspect/export', params);
// 任务详情
export const getTaskAllInfo = (params) => postAction('/task/getTaskAllInfo', params);

// 任务验收
export const accepTask = (params) => postAction('/task/accepTask', params);

// 删除任务
export const deleteTask = (params) => postAction('/task/delete', params);

// 取消任务
export const cancelTask = (params) => postAction('/task/cancelTask', params);

// 创建子任务
export const createSubTask = (params) => postAction('/task/createSubTask', params);

// 获取执行信息
export const getErpSecTaskPerform = (params) => postAction('/task/getErpSecTaskPerform', params);
// 统计数量
export const getCount = (params) => postAction('/task/count', params);
export const getCountOth = (params) => postAction('/inspect/count', params);

// 户用任务流程处理
export const auditTask = (params) => postAction('/task/auditTask', params);
// 下载价格清单模板
export const downloadExcleTemplate = (params) => postAction('/task/downloadExcleTemplate', params);
