<template>
  <div>
    <a-card :bordered="false" class="search-card" :bodyStyle="{ height: '100%',padding:'16px' }">
      <!-- 搜索区域 -->
      <div class="table-page-search-wrapper">
        <a-form layout="inline" labelAlign="left">
          <a-row :gutter="10" align="middle">
            <a-col style="height: 32px" :md="8" :sm="24" :xxl="6" :xl="8" :lg="8">
              <a-form-item label="电站" :colon="false">
                <ps-tree-select @change="getChild" :isPsName="psName"  v-model="queryParams.psId"
                  :hasMaintenanceStatusParams="true" :isPsaDisable="true" :isQueryPs="1" />
              </a-form-item>
            </a-col>
            <a-col style="height: 32px" :md="8" :sm="24" :xxl="6" :xl="8" :lg="8">
              <a-form-item label="月份" style="margin-left: 8px" :colon="false">
                <a-month-picker class="search-time-picker" placeholder="请选择月份"
                                @change="timeChange"
                                v-model="queryParams.date"
                                :getCalendarContainer="(node) => node.parentNode"
                                :disabled-date="disabledDate"
                                format="YYYY-MM"
                                :allowClear="false"
                                valueFormat="YYYY-MM"
                >
                  <a-icon slot="suffixIcon" type="history"/>
                </a-month-picker>
              </a-form-item>
            </a-col>

            <div class="open-btn"></div>
          </a-row>
        </a-form>
      </div>
    </a-card>

    <a-card :style="{height: height+'px',overflow:'hidden'}">
      <!--      <h1 @click="goPage">设备列表</h1>-->
      <div class="chart-eg-items">
        <div class="chart-eg-item">
          <div class="line-cricle line-cricle-primary">
            <div class="circle">
            </div>
          </div>
          <span>完整率</span>
        </div>
        <div class="chart-eg-item">
          <div class="line-cricle line-cricle-blue">
            <div class="circle">
            </div>
          </div>
          <span>有效率</span>
        </div>
        <div class="chart-eg-item">
          <div class="empty-cricle">
          </div>
          <span>含有标记</span>
        </div>
      </div>
  <div class="quality-chart" :style="{height:(height - 60) + 'px'}" id="qualityAnalyseChart"></div>
  </a-card>
  </div>
</template>

<script>
import { stationIntateAndEffectiveDetail } from '@/api/dataCenter/index';
import echarts from '@/utils/enquireEchart';
import moment from 'moment';
import { mixin } from '@/utils/mixin.js';
import store from '@/store';
let innerHeight = window.innerHeight + (store.state.user.isShowMenu ? 0 : 124);
export default {
  name: 'DataView',
  mixins: [mixin],
  props: {
    pageParams: {
      type: Object
    }
  },
  data () {
    return {
      queryParams: {
        psId: '',
        order: '',
        date: ''
      },
      psName: '',
      height: innerHeight - 60 - 40 - 24 - 64 - 16 - 21,
      myChart: null,
      firstLoad: false,
      changeData: {},
      option: {
        grid: {
          left: '3.5%',
          right: '3.5%',
          top: '5%',
          bottom: 40,
          containLabel: true
        },
        tooltip: {
          trigger: 'axis',
          alwaysShowContent: false
        },
        toolbox: {},
        xAxis: [{
          type: 'category',
          boundaryGap: false,
          axisTick: {
            show: false
          },
          axisLine: {
            show: false,
            lineStyle: {
              color: '#ADADAD'
            }
          },
          axisLabel: {
            formatter: e => e.substr(5, e.length - 1)
          },
          data: []
        }, {
          type: 'category',
          boundaryGap: false,
          axisTick: {
            show: false
          },
          axisLine: {
            show: false,
            lineStyle: {
              color: '#ADADAD'
            }
          },
          data: []
        }, {
          type: 'category',
          boundaryGap: false,
          axisTick: {
            show: false
          },
          axisLine: {
            show: false,
            lineStyle: {
              color: '#ADADAD'
            }
          },
          data: []
        }],
        yAxis: [
          {
            name: '单位（%）',
            type: 'value',
            interval: 10,
            splitLine: {// 分割线配置
              show: true,
              lineStyle: {
                color: ['#d6d6d6']
              }
            }
          }
        ],
        series: [
          {
            name: '',
            type: 'bar',
            data: [],
            select: {
              selectedMode: 'single'

            },
            itemStyle: {
              color: {
                type: 'linear',
                x: 0,
                y: 0,
                x2: 0,
                y2: 1,
                colorStops: [{
                  offset: 0, color: 'rgba(78,121,177,0)' // 0% 处的颜色
                }, {
                  offset: 1, color: 'rgba(78, 121, 177, 0.13)' // 100% 处的颜色
                }],
                global: false // 缺省为 false
              }
            },
            emphasis: {
              itemStyle: {
                color: {
                  type: 'linear',
                  x: 0,
                  y: 0,
                  x2: 0,
                  y2: 1,
                  colorStops: [{
                    offset: 0, color: ' rgba(78, 121, 177, .1)' // 0% 处的颜色
                  }, {
                    offset: 1, color: 'rgba(78, 121, 177, 0.73)' // 100% 处的颜色
                  }],
                  global: false // 缺省为 false
                }
              }
            },
            z: 5
          },
          {
            name: '完整率',
            type: 'line',
            symbol: 'circle',
            smooth: true,
            lineStyle: {
              width: 2
            },
            // color: 'rgba(255, 143, 51, 1)',
            color: '#1BCACB',
            markPoint: {
              symbol: 'emptyCircle',
              symbolSize: 10,
              itemStyle: {
                color: '#F56C6C',
                borderWidth: 6,
                borderColor: '#F56C6C'
              },
              label: {
                show: false
              },
              data: []
            },
            areaStyle: {
              color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                {
                  offset: 0,
                  color: 'rgba(72, 178, 248, .2)'
                },
                {
                  offset: 1,
                  color: 'rgba(126, 217, 252, 0)'

                }
              ])
            },
            data: [],
            z: 1
          }, {
            name: '有效率',
            type: 'line',
            symbol: 'circle',
            smooth: true,
            markPoint: {
              symbol: 'emptyCircle',
              symbolSize: 10,
              itemStyle: {
                color: '#F56C6C',
                borderWidth: 6,
                borderColor: '#F56C6C'
              },
              label: {
                show: false
              },
              data: []
            },
            areaStyle: { color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
              {
                offset: 0,
                color: 'rgba(124, 215, 250, .2)'
              },
              {
                offset: 1,
                color: 'rgba(70, 174, 243, 0)'

              }
            ]) },
            lineStyle: {
              width: 2
            },
            // color: 'rgba(128, 195, 242, 1)',
            color: '#62BE93',
            opacity: 0.2,
            data: []
          }
        ]
      }
    };
  },
  created () {
    this.firstLoad = true;
    setTimeout(() => {
      this.firstLoad = false;
    }, 200);
    this.psName = this.pageParams.psName;
    this.queryParams.psId = this.pageParams.psId;
    this.queryParams.date = moment().subtract(1, 'days').format('YYYY-MM');
  },
  mounted () {
    this.initChart();
  },
  watch: {
    'navTheme' (color) {
      if (this.myChart) {
        this.option.yAxis[0].splitLine.lineStyle.color[0] = color == 'dark' ? '#354661' : '#d6d6d6';
        this.option.tooltip = this.getTooltip(this.changeData);
        this.myChart.setOption(this.option, true);
      }
    }
  },
  methods: {
    goPage (date) {
      this.$emit('changePageParams', {
        deviceName: '',
        defaultDate: date,
        psId: this.queryParams.psId,
        psName: this.psName
      });
      setTimeout(() => {
        this.$emit('changePage', 3);
      }, 10);
    },
    disabledDate (current) {
      // 不能选择今天及今天以后的
      return current && current > moment().subtract(1, 'days').endOf('day');
    },
    timeChange () {
      this.refreshChart();
    },
    initChart () {
      let chartDom = document.getElementById('qualityAnalyseChart');
      this.myChart = echarts.init(chartDom);
      const options = this.option;
      options && this.myChart.setOption(options);
      this.refreshChart();
    },
    getChild (val, node) {
      this.queryParams.psId = node.id;
      this.psName = node.name;
      if (!this.firstLoad) {
        this.refreshChart();
      }
    },
    async refreshChart () {
      let res = await stationIntateAndEffectiveDetail({
        psId: this.queryParams.psId,
        date: this.queryParams.date
      });
      let effectiveRateData = res.result_data.map(item => item.effectiveRate === '' ? '' : (item.effectiveRate * 100).toFixed(2));
      let intateRateData = res.result_data.map(item => item.intateRate === '' ? '' : (item.intateRate * 100).toFixed(2));
      let xData = res.result_data.map(item => item.timeDate);
      let markPointData = [[], []];
      res.result_data.map((item, index) => {
        if (item.statusSign != null) {
          markPointData[0].push({ coord: [index, String(effectiveRateData[index])], name: item.statusSignName });
          markPointData[1].push({ coord: [index, intateRateData[index]], name: item.statusSignName });
        }
      });

      let changeData = {
        effectiveRateData,
        intateRateData,
        xData,
        markPointData
      };
      this.changeData = changeData;
      this.randerChart(changeData);
    },
    randerChart (changeData) {
      let options = this.myChart.getOption();
      options.yAxis[0].splitLine.lineStyle.color[0] = this.navTheme == 'dark' ? '#354661' : '#d6d6d6';

      options.series[2].data = changeData.effectiveRateData;
      options.series[1].data = changeData.intateRateData;
      options.series[2].markPoint.data = changeData.markPointData[0];
      options.series[1].markPoint.data = changeData.markPointData[1];
      options.series[0].data = changeData.xData.map(item => 100);

      options.tooltip = this.getTooltip(changeData);
      options.xAxis[0].data = changeData.xData;
      this.option = options;
      this.myChart.setOption(options, true);

      this.myChart.on('click', (params) => {
        if (params.componentType == 'series') { // 点击柱状图
          this.goPage(params.name);
        } else { // 点击标识点
          let index = params.data.coord[0];
          this.goPage(changeData.xData[index]);
        }
      });
    },
    getTooltip (changeData) {
      let tooltip = {
        trigger: 'axis',
        alwaysShowContent: false,
        formatter: params => {
          let problem = changeData.markPointData[0].find(item => item.coord[0] === params[1].dataIndex);
          let str =
              params[0].axisValue + ':' +
              (problem ? problem.name : '') +
              '</br>' +
              params[1].marker +
              '完整率：' +
              (params[1].value === '' ? '--' : params[1].value) + '</br>' +
              params[2].marker +
              '有效率：' +
              (params[2].value === '' ? '--' : params[2].value) +
              '</br>';
          return str;
        }
      };
      let obj = {
        backgroundColor: '#1f334e',
        borderColor: '#51637C',
        textStyle: {
          color: '#fff'
        },
        boxShadow: '0px 2px 8px 0px rgba(0, 0, 0, 0.15), 5px 4px 9px 0px rgba(3, 18, 38, 0.5)'
      };
      if (this.navTheme == 'dark') {
        tooltip = Object.assign({}, tooltip, obj);
      }
      return tooltip;
    }
  }
};
</script>

<style lang="less" scoped>

  .quality-chart {
    width: 100%;
  }

  .chart-eg-items {
    display: flex;
    justify-content: flex-end;
    align-items: center;
    width: 100%;
  }

  .chart-eg-item {
    width: 126px;
    height: 24px;

    border-radius: 30px;
    display: flex;
    align-items: center;
    justify-content: space-evenly;
    margin-left: 8px;

    .line-cricle {
      width: 15px;
      height: 1px;

      position: relative;

      .circle {
        width: 8px;
        height: 8px;
        position: absolute;
        left: 2px;
        top: -4px;
        border-radius: 50%;
      }
    }

    .line-cricle-primary {
      border: 1px solid #1BCACB;

      .circle {
        background: #1BCACB;
      }
    }

    .line-cricle-blue {
      border: 1px solid  #62BE93;

      .circle {
        background:  #62BE93;
      }
    }

    .empty-cricle {
      width: 10px;
      height: 10px;
      background: #FFFFFF;
      border-radius: 50%;
      border: 2px solid #F56C6C;
    }
  }

</style>
