// 基本信息
export const basicsDetail = [
  { key: 'psaName', label: '电站名称', span: 6 },
  { key: 'reportType', label: '填报类别', dict: 'ticket_report_type', span: 6 },
  { key: 'ticketType', label: '两票类型', dict: 'ticket_type', span: 6 },
  { key: 'ticketTypeDtl', label: '具体类别', dict: 'ticket_type_dtl', span: 6 },
  {
    key: 'ticketNum',
    label: '票号',
    func: (params) => {
      return params.reportType == '0';
    }
  },
  {
    key: 'uploadFileList',
    label: '附件',
    span: 24,
    type: 'file:text',
    func: (params) => {
      return params.reportType == '0';
    }
  }
];
// 基本信息
export const baseDetail = [
  { slot: 'ticketNum', label: '票号' },
  { key: 'psaName', label: '电站名称' },
  { key: 'placeAddress', label: '电站地址' },
  { key: 'unit', label: '单位' },
  { key: 'directorName', label: '工作负责人' },
  { key: 'teamGroups', label: '班组' },
  { key: 'workTeamStaff', label: '工作班成员' },
  { key: 'planTime', label: '计划时间' },
  { key: 'workConditionName', label: '工作条件' },
  { key: 'operationTask', label: '工作任务' }
];
// 签发信息
export const signDetail = [
  { key: 'signerName', label: '工作签发人' }
];
// 安全措施
export const measuresDetail = [
  { key: 'licensorName', label: '操作人' },
  { slot: 'operationList', label: '安全措施' }
];
// 许可信息
export const allowWorkDetail = [
  { key: 'licensorName', label: '工作许可人' },
  { key: 'allowWorkTimeStr', label: '工作许可时间' }
];
// 工作执行
export const acceptDetail = [
  { slot: 'accept', label: '安全交底' },
  { slot: 'workEndTime' }
];
// 终结信息
export const endDetail = [
  { key: 'licensorName', label: '操作人', span: 24 },
  { slot: 'deviceMeasure' }
];
