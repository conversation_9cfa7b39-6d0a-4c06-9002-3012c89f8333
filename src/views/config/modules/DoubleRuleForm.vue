<template>
  <div class="drawer-form-com">
    <a-spin :spinning="loading" class="width-height-100">
      <div class="drawer-form-content">
        <a-form-model
          :model="formData"
          ref="doubleRuleForm"
          :labelCol="{ style: 'width: 255px' }"
          :wrapperCol="{ style: 'width: calc(100% - 255px)' }"
          :rules="rules"
          class="form-model-wrapper"
        >
          <a-row :gutter="24" class="margin-t-16">
            <a-col :span="24">
              <div class="order-dispose first-title">
                <div class="title-box">
                  <span class="before"></span>
                  <span>基本信息</span>
                </div>
              </div>
            </a-col>
            <a-col :xs="24" :sm="12" :md="8">
              <a-form-model-item label="省份" prop="areaId">
                <a-select placeholder="请选择" v-model="formData.areaId" :disabled="disabled" @change="areaIdChange">
                  <a-select-option
                    v-for="(item, index) in provinceList()"
                    :key="index"
                    :value="item.areaId"
                    :data-name="item.areaName"
                    >{{ item.areaName }}
                  </a-select-option>
                </a-select>
              </a-form-model-item>
            </a-col>
            <a-col :xs="24" :sm="12" :md="8">
              <a-form-model-item label="所属区域" prop="areaType">
                <a-select placeholder="请选择" v-model="formData.areaType" :disabled="disabled">
                  <a-select-option v-for="(item, index) in areaTypeList()" :key="index" :value="item.areaTypeName"
                    >{{ item.areaTypeName }}
                  </a-select-option>
                </a-select>
              </a-form-model-item>
            </a-col>
          </a-row>
          <a-row :gutter="24">
            <a-col :xs="24" :sm="12" :md="8">
              <a-form-model-item label="AGC调节速率系数(%)" prop="agcAdjustRateCoefficient">
                <a-input-number
                  v-model="formData.agcAdjustRateCoefficient"
                  style="width: 100%"
                  placeholder="请输入"
                  :precision="2"
                  :max="100"
                  :min="0"
                />
              </a-form-model-item>
            </a-col>
            <a-col :xs="24" :sm="12" :md="8">
              <a-form-model-item label="AGC调节速率阈值(MW)" prop="agcAdjustRateThreshold">
                <a-input-number
                  style="width: 100%"
                  v-model="formData.agcAdjustRateThreshold"
                  placeholder="请输入"
                  :precision="2"
                  :max="1000"
                  :min="0"
                />
              </a-form-model-item>
            </a-col>
            <a-col :xs="24" :sm="12" :md="8">
              <a-form-model-item label="AGC调节精度系数(%)" prop="agcAdjustAccuracyCoefficient">
                <a-input-number
                  style="width: 100%"
                  v-model="formData.agcAdjustAccuracyCoefficient"
                  placeholder="请输入"
                  :precision="2"
                  :max="100"
                  :min="0"
                />
              </a-form-model-item>
            </a-col>
            <a-col :xs="24" :sm="12" :md="8">
              <a-form-model-item prop="agcAdjustRateDuration" class="form-model-item-wrapper">
                <template slot="label">
                  <div class="label-wrap-box">
                    <span>AGC调节速率未达标持续时长</span>
                    <span>阈值(min)</span>
                  </div>
                </template>
                <a-input-number
                  style="width: 100%"
                  v-model="formData.agcAdjustRateDuration"
                  placeholder="请输入"
                  :precision="0"
                  :max="1000"
                  :min="1"
                />
              </a-form-model-item>
            </a-col>
            <a-col :xs="24" :sm="12" :md="8">
              <a-form-model-item label="AGC调节精度预警持续时长阈值(min)" prop="agcAdjustAccuracyDuration">
                <a-input-number
                  style="width: 100%"
                  v-model="formData.agcAdjustAccuracyDuration"
                  placeholder="请输入"
                  :precision="0"
                  :max="1000"
                  :min="1"
                />
              </a-form-model-item>
            </a-col>
            <a-col :xs="24" :sm="12" :md="8">
              <a-form-model-item label="AGC投运率持续时长阈值(min)" prop="agcOperationRateDuration">
                <a-input-number
                  style="width: 100%"
                  v-model="formData.agcOperationRateDuration"
                  placeholder="请输入"
                  :precision="0"
                  :max="1000"
                  :min="1"
                />
              </a-form-model-item>
            </a-col>
            <a-col :xs="24" :sm="12" :md="8">
              <a-form-model-item label="AVC调节阈值(kar)：" prop="avcAdjustThreshold">
                <a-input-number
                  style="width: 100%"
                  v-model="formData.avcAdjustThreshold"
                  placeholder="请输入"
                  :precision="2"
                  :max="1000"
                  :min="0"
                />
              </a-form-model-item>
            </a-col>
            <a-col :xs="24" :sm="12" :md="8">
              <a-form-model-item label="AVC调节未达标持续时长阈值(min)：" prop="avcAdjustRateDuration">
                <a-input-number
                  style="width: 100%"
                  v-model="formData.avcAdjustRateDuration"
                  placeholder="请输入"
                  :precision="0"
                  :max="1000"
                  :min="1"
                />
              </a-form-model-item>
            </a-col>
            <a-col :xs="24" :sm="12" :md="8">
              <a-form-model-item label="AVC投运率持续时长阈值(min)：" prop="avcOperationRateThreshold">
                <a-input-number
                  style="width: 100%"
                  v-model="formData.avcOperationRateThreshold"
                  placeholder="请输入"
                  :precision="2"
                  :max="100"
                  :min="1"
                />
              </a-form-model-item>
            </a-col>
            <a-col :xs="24" :sm="12" :md="8">
              <a-form-model-item label="短期功率预测准确率阈值(%)" prop="shortPowerPredictionAccuracyThreshold">
                <a-input-number
                  style="width: 100%"
                  v-model="formData.shortPowerPredictionAccuracyThreshold"
                  placeholder="请输入"
                  :precision="2"
                  :max="100"
                  :min="0"
                />
              </a-form-model-item>
            </a-col>
            <a-col :xs="24" :sm="12" :md="8">
              <a-form-model-item label="超短期功率预测准确率阈值(%)" prop="superShortPowerPredictionAccuracyThreshold">
                <a-input-number
                  style="width: 100%"
                  v-model="formData.superShortPowerPredictionAccuracyThreshold"
                  placeholder="请输入"
                  :precision="2"
                  :max="100"
                  :min="0"
                />
              </a-form-model-item>
            </a-col>
            <a-col :xs="24" :sm="12" :md="8">
              <a-form-model-item label="中期功率预测准确率阈值(%)" prop="middlePowerPredictionAccuracyThreshold">
                <a-input-number
                  style="width: 100%"
                  v-model="formData.middlePowerPredictionAccuracyThreshold"
                  placeholder="请输入"
                  :precision="2"
                  :max="100"
                  :min="0"
                />
              </a-form-model-item>
            </a-col>
          </a-row>
        </a-form-model>
      </div>
      <div class="drawer-form-foot">
        <a-button class="solar-eye-btn-primary-cancel" @click="$emit('cancel', false)"> 取消</a-button>
        <throttle-button label="确定" @click="handleConfirmEvent" />
      </div>
    </a-spin>
  </div>
</template>
<script>
import { ACTION_NAMES, ACTION_TYPES } from '@/utils/erpcommon';
import { createDoubleRulesApi, getDoubleRulesDetailApi, updateDoubleRulesApi } from '@/api/health/healthapi';

export default {
  name: 'DoubleRuleForm',
  inject: ['provinceList', 'areaTypeList'],
  data() {
    return {
      loading: false,
      formData: {
        areaId: undefined, // 省份
        areaType: undefined, // 所属区域
        agcAdjustRateCoefficient: 1.0, // AGC调节速率系数
        agcAdjustRateThreshold: 0.5, // AGC调节速率阈值
        agcAdjustAccuracyCoefficient: 1, // AGC调节精度系数
        agcAdjustRateDuration: 3, // AGC调节速率未达标持续时长阈值
        agcAdjustAccuracyDuration: 3, // AGC调节精度预警持续时长阈值
        agcOperationRateDuration: 20, // AGC投运率持续时长阈值
        avcAdjustThreshold: undefined, // AVC调节阈值
        avcAdjustRateDuration: 3, // AVC调节未达标持续时长阈值
        avcOperationRateThreshold: undefined, // AVC投运率持续时长阈值
        middlePowerPredictionAccuracyThreshold: undefined, // 中期功率预测准确率阈值
        superShortPowerPredictionAccuracyThreshold: 80, // 超短期功率预测准确率阈值
        shortPowerPredictionAccuracyThreshold: 85 // 短期功率预测准确率阈值
      },
      rules: {
        areaId: [{ required: true, message: '请选择省份', trigger: 'change' }, { pattern: /^/ }],
        areaType: [{ required: true, message: '请选择所属电网区域', trigger: 'change' }],
        agcAdjustRateCoefficient: [{ required: true, message: '请输入AGC调节速率系数', trigger: 'change' }],
        agcAdjustRateThreshold: [{ required: true, message: '请输入AGC调节速率阈值', trigger: 'change' }],
        agcAdjustAccuracyCoefficient: [{ required: true, message: '请输入AGC调节精度系数', trigger: 'change' }],
        agcAdjustRateDuration: [{ required: true, message: '请输入AGC调节速率未达标持续时长阈值', trigger: 'change' }],
        agcAdjustAccuracyDuration: [
          { required: true, message: '请输入AGC调节精度预警持续时长阈值', trigger: 'change' }
        ],
        agcOperationRateDuration: [{ required: true, message: '请输入AGC投运率持续时长阈值', trigger: 'change' }],
        avcAdjustThreshold: [{ required: true, message: '请输入AVC调节阈值', trigger: 'change' }],
        avcAdjustRateDuration: [{ required: true, message: '请输入AVC调节未达标持续时长阈值', trigger: 'change' }],
        avcOperationRateThreshold: [{ required: true, message: '请输入AVC投运率持续时长阈值', trigger: 'change' }],
        middlePowerPredictionAccuracyThreshold: [
          { required: true, message: '请输入中期功率预测准确率阈值', trigger: 'change' }
        ],
        superShortPowerPredictionAccuracyThreshold: [
          { required: true, message: '请输入超短期功率预测准确率阈值', trigger: 'change' }
        ],
        shortPowerPredictionAccuracyThreshold: [
          { required: true, message: '请输入短期功率预测准确率阈值', trigger: 'change' }
        ]
      },
      rowInfo: { type: null, row: {} }
    };
  },
  computed: {
    disabled() {
      return this.rowInfo.type === ACTION_TYPES.EDIT;
    }
  },
  methods: {
    init(type, row) {
      Object.assign(this.rowInfo, { type, row });
      if (ACTION_TYPES.EDIT === type) {
        this.loading = true;
        getDoubleRulesDetailApi({ id: row.id })
          .then((res) => {
            this.loading = false;
            Object.assign(this.formData, res);
          })
          .catch((err) => {
            this.loading = false;
            console.error('获取双控规则详情失败:', err);
          });
      }
      return ACTION_NAMES[type];
    },
    handleConfirmEvent() {
      this.$refs.doubleRuleForm.validate((valid) => {
        if (valid) {
          this.loading = true;
          if (this.rowInfo.type === ACTION_TYPES.EDIT) {
            updateDoubleRulesApi(this.formData)
              .then((res) => {
                this.$message.success('操作成功');
                this.$emit('cancel', true);
              })
              .finally(() => {
                this.loading = false;
              });
          } else {
            createDoubleRulesApi(this.formData)
              .then((res) => {
                this.$message.success('操作成功');
                this.$emit('cancel', true);
              })
              .finally(() => {
                this.loading = false;
              });
          }
        }
      });
    },
    areaIdChange(_, option) {
      Object.assign(this.formData, {
        areaName: option.data.attrs['data-name']
      });
    }
  }
};
</script>

<style scoped lang="less">
:deep(.width-height-100) {
  .ant-spin-container {
    width: 100%;
    height: 100%;
  }
}

:deep(.form-model-wrapper.ant-form-horizontal) {
  .ant-form-item {
    display: inline-flex;
    width: 100%;
  }

  .form-model-item-wrapper .ant-form-item-label label {
    display: flex;
    justify-content: flex-end;
    align-items: center;
    height: 40px;

    .label-wrap-box {
      display: flex;
      flex-direction: column;
      line-height: 21px;
    }
  }
}
</style>
