<!-- 版本管理 -->
<template>
<div class="solar-eye-search-model">
    <a-form labelAlign="left" class="solar-eye-search-content">
    <a-row :gutter="24" align="middle">
        <a-col :xxl="4" :xl="8" :md="12">
        <div class="search-item">
        <span class="search-label">电站名称</span>
        <a-select disabled v-show="disabled">
        <a-select-option :value="queryParams.psId">{{queryParams.psName}}</a-select-option>
        </a-select>
        <ps-tree-select @change="getStationChange" :isPsName="queryParams.psName" v-model="queryParams.psId" :isPsaDisable="true" isInsight="1" :isQueryPs="1" style="width: 100%;" v-show="!disabled" />
        </div>
        </a-col>
        <template v-if="active=='message'">
        <a-col :xxl="4" :xl="8" :md="12">
        <div class="search-item">
        <span class="search-label">SN号</span>
            <a-input
            v-model="queryParams.sn"
            placeholder
            clearable
            allow-clear
            :disabled="disabled"
            ></a-input>
            </span>
            </div>
        </a-col>
            <a-col :xxl="4" :xl="8" :md="12">
            <div class="search-item">
            <span class="search-label">报文类型</span>
            <a-select v-model="queryParams.messageType" >
            <a-select-option value="1">原始报文</a-select-option>
            <a-select-option value="2">解密报文</a-select-option>
            </a-select>
        </div>
        </a-col>

        </template>
        <a-col :xxl="4" :xl="8" :md="12">
            <div class="search-item">
            <span class="search-label">测点类型</span>
            <a-select v-model="queryParams.pointType">
            <a-select-option value="1">遥测</a-select-option>
            <a-select-option value="2">遥信</a-select-option>
            </a-select>
        </div>
        </a-col>
        <template v-if="active=='realTime'">
            <a-col :xxl="4" :xl="8" :md="12">
        <div class="search-item">
        <span class="search-label">测点编码</span>
            <a-input
            v-model="queryParams.point"
            placeholder="请输入测点编码"
            clearable
            allow-clear
            ></a-input>
            </span>
            </div>
        </a-col>
        </template>
            <template v-if="active=='message'">
            <a-col :xxl="5" :xl="8" :md="12">
        <div class="search-item">
        <span class="search-label">时间</span>
            <a-range-picker
            :disabled-date="disabledDate"
            :disabled-time="disabledRangeTime"
            :show-time="{
            hideDisabledOptions: false
            }"
            @change="okEvent"
            @ok="okEvent"
            v-model="timeRange"
            format="YYYY-MM-DD HH:mm:ss"
            ><a-input :value="!isLatest && timeRange.length > 0 ? queryParams.timeStartData + '~'+ queryParams.timeEndData: '最新'">{{}}</a-input>
            <template slot="renderExtraFooter">
            <a-checkbox v-model="isLatest" :value="1"> 最新</a-checkbox>
      </template>
            </a-range-picker>
            </div>
        </a-col>
        </template>
        <a-col :xxl="2" :xl="6" :md="2" >
        <div class="search-item ">
        <div class="search-label">
            <a-button class="solar-eye-btn-primary-cancel" @click="clickEvent(true)">重置</a-button>
            <a-button class="solar-eye-btn-primary"  @click="clickEvent(false)">查询</a-button>
        </div>
        </div>
    </a-col>
    </a-row>
    </a-form>
</div>
</template>
<script>
import moment from 'moment';
import { insightToolsGetDefaultPs } from '@/api/dataCenter/index';

export default {
  name: 'MessageSearchModel',
  data () {
    return {
      queryParams: {
        psId: '',
        point: '',
        pointType: '1',
        psName: '',
        timeStartData: undefined,
        timeEndData: undefined,
        isLast: undefined,
        sn: undefined,
        messageType: '1'
      },
      isLatest: true,
      isFirstLoad: true,
      timeRange: []
    };
  },
  props: {
    active: {
      default: 'realTime',
      type: String
    },
    disabled: {
      default: false,
      type: Boolean
    }
  },
  created () {
    this.getDefaultPsId();
  },
  watch: {
    'isLatest' (value) {
      this.dealTime(value);
    }
  },
  computed: {},
  methods: {
    moment,
    async getDefaultPsId () {
      let res = await insightToolsGetDefaultPs();
      if (res.result_data && res.result_data.ps_id) {
        this.queryParams.psId = res.result_data.ps_id;
        this.queryParams.psName = res.result_data.ps_name;
        this.$emit('search', this.queryParams, true);
      } else {
        let obj = Object.assign({}, this.queryParams, { isLoading: false });
        this.$emit('search', obj, true);
      }
      this.isFirstLoad = false;
    },
    getStationChange (val, node) {
      // 选择树节点
      if (this.isFirstLoad) return;
      this.queryParams.psId = node.id;
      this.queryParams.psName = node.name;
      this.$emit('search', this.queryParams, true);
    },
    /**
     * 查询及重置
     * param {isReset} boolean true 是重置，false 查询
     * */
    clickEvent (isReset) {
      if (isReset) {
        this.dealQueryParams(this.active, true);
        this.isFirstLoad = true;
        this.getDefaultPsId();
        this.$emit('refresh', true);
      } else {
        this.dealTime();
        this.$emit('search', this.queryParams, false);
      }
    },
    dealTime (value) {
      if (!value && this.timeRange.length > 0) {
        this.queryParams.timeStartData = this.moment(this.timeRange[0]).format('YYYY-MM-DD HH:mm:ss');
        this.queryParams.timeEndData = this.moment(this.timeRange[1]).format('YYYY-MM-DD HH:mm:ss');
        this.queryParams.isLast = '';
      } else {
        this.queryParams.timeStartData = '';
        this.queryParams.timeEndData = '';
        this.timeRange = [];
        this.queryParams.isLast = 1;
      }
    },
    dealQueryParams (active, isReset) {
      for (let item in this.queryParams) {
        let isInclude = ['sn', 'isLast', 'timeStartData', 'timeEndData'].includes(item);
        if (active == 'realTime' && isInclude) {
          this.queryParams[item] = undefined;
        } else {
          if (!isReset) {
            if (!['psId', 'psName'].includes(item)) {
              this.queryParams[item] = '';
            }
          } else {
            this.queryParams[item] = '';
          }
        }
      }
      this.queryParams.pointType = '1';
      if (active == 'message') {
        this.timeRange = [];
        this.queryParams.isLast = 1;
        this.queryParams.timeStartData = '';
        this.queryParams.timeEndData = '';
        this.queryParams.sn = '';
        this.queryParams.messageType = '1';
        this.isLatest = true;
      }
    },
    okEvent (data) {
      this.isLatest && this.$message.error('请先取消勾选最新');
      this.dealTime(this.isLatest);
    },
    disabledDate (current) {
      // Can not select days before today and today
      return current && (current > moment().endOf('day') || current < moment().add(-3, 'days').endOf('day'));
    },
    disabledRangeTime (_, type) {
      var twoDaysAgo = moment().subtract(2, 'day');
      let isTrue = this.timeRange[0] && this.moment(this.timeRange[0]).isSame(twoDaysAgo, 'day');
      let isToday = this.timeRange[1] && moment(this.timeRange[1]).isSame(moment(), 'day');
      let currentTimeH = new Date(this.moment(_).format('YYYY-MM-DD')).valueOf();
      let nowTimeH = new Date(this.moment().format('YYYY-MM-DD')).valueOf();
      let diffD = currentTimeH >= nowTimeH || !currentTimeH;
      // let currentH = new Date(this.moment(_)).getHours();
      // let currentM = new Date(this.moment(_)).getMinutes();
      // this.timeRange[0] = moment(this.timeRange[0]).minute(currentM + 1).hour(currentH).second(new Date().getSeconds());
      // this.timeRange[1] = moment(this.timeRange[1]).minute(currentM).hour(currentH).second(new Date().getSeconds() - 1);
      if (type === 'start') {
        return {
          disabledHours: () => isTrue ? this.range(0, 24).splice(0, new Date().getHours()) : this.range(0, 0),
          disabledMinutes: () => isTrue ? this.range(0, new Date().getMinutes()) : this.range(0, 0),
          disabledSeconds: () => this.range(0, 0)
        };
      }

      return {
        disabledHours: () => isToday && diffD ? this.range(0, 24).splice(new Date().getHours() + 1, 20) : this.range(0, 0),
        disabledMinutes: () => isToday ? this.range(new Date().getMinutes() + 1, 60) : this.range(0, 0),
        disabledSeconds: () => this.range(0, 0)
      };
    },
    range (start, end) {
      const result = [];
      for (let i = start; i < end; i++) {
        result.push(i);
      }
      return result;
    }
  }
};
</script>
<style lang='less' scoped>
  :deep(.ant-tabs-bar){
        margin: 0;
      }
      .right-bar, .left-tree-bg {
        background: white;
      }
      :root[data-theme='dark'] {
         .right-bar,.left-tree-bg {
        background: #1B2536;
      }
      }
</style>
