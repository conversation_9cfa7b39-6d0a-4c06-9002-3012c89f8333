<template>
  <a-modal v-model="visible" title="IEC测点" :width="856" footer @cancel="onClose">
    <vxe-table v-loading="loading" :data="data" ref="multipleTable" resizable
      align="center" show-overflow highlight-hover-row size="small" >
      <vxe-table-column type="seq" :width="80" title="序号" align="center"></vxe-table-column>
      <vxe-table-column show-overflow="title" title="通道号" :min-width="100" field="channel">
        <template slot-scope="scope">
          {{ scope.row.channel || '--' }}
        </template>
      </vxe-table-column>
      <vxe-table-column show-overflow="title" title="设备名称" :min-width="100" field="deviceName"></vxe-table-column>
      <vxe-table-column show-overflow="title" title="测点类型" :min-width="100" field="pointTypeName"></vxe-table-column>
      <vxe-table-column show-overflow="title" title="测点名称" :min-width="100" field="pointName">
        <template slot-scope="scope">
          {{ scope.row.pointName || '--' }}
        </template>
      </vxe-table-column>
      <vxe-table-column show-overflow="title" title="IEC测点" :min-width="100" field="point"></vxe-table-column>
      <vxe-table-column show-overflow="title" title="设备测点" :min-width="100" field="pointId"></vxe-table-column>
      <template v-slot:empty>
        <span>查询无数据</span>
      </template>
    </vxe-table>
  </a-modal>
</template>
<script>
import { getPointBySn } from '@/api/health/healthapi.js';
export default {
  name: 'points',
  data () {
    return {
      visible: false,
      loading: false,
      data: []
    };
  },
  methods: {
    init (sn) {
      this.visible = true;
      this.loading = true;
      this.data = [];
      getPointBySn({ snList: [{ sn: sn }] }).then(res => {
        if (res.result_code == '1') {
          this.data = res.result_data;
        }
        this.loading = false;
      }).catch(() => { this.loading = false; });
    },
    onClose () {
      this.visible = false;
    }
  }
};
</script>
<style lang="less" scoped>
:deep(.ant-modal-content) {
  .ant-modal-body {
    height: 70vh;
    overflow: hidden;
  }
}
:deep(.vxe-table--body-wrapper){
  height: calc(70vh - 88px);
}
</style>
