import CryptoJS from 'crypto-js';
export function resetSize (vm) {
  var img_width, img_height, bar_width, bar_height; // 图片的宽度、高度，移动条的宽度、高度

  var parentWidth = vm.$el.parentNode.offsetWidth || window.offsetWidth;
  var parentHeight = vm.$el.parentNode.offsetHeight || window.offsetHeight;

  if (vm.imgSize.width.indexOf('%') != -1) {
    img_width = parseInt(this.imgSize.width) / 100 * parentWidth + 'px';
  } else {
    img_width = this.imgSize.width;
  }

  if (vm.imgSize.height.indexOf('%') != -1) {
    img_height = parseInt(this.imgSize.height) / 100 * parentHeight + 'px';
  } else {
    img_height = this.imgSize.height;
  }

  if (vm.barSize.width.indexOf('%') != -1) {
    bar_width = parseInt(this.barSize.width) / 100 * parentWidth + 'px';
  } else {
    bar_width = this.barSize.width;
  }

  if (vm.barSize.height.indexOf('%') != -1) {
    bar_height = parseInt(this.barSize.height) / 100 * parentHeight + 'px';
  } else {
    bar_height = this.barSize.height;
  }

  return { imgWidth: img_width, imgHeight: img_height, barWidth: bar_width, barHeight: bar_height };
}

/**
 * @word 要加密的内容
 * @keyWord String  服务器随机返回的关键字
 *  */
export function aesEncrypt (word, keyWord = 'XwKsGlMcdPMEhR1B') {
  var key = CryptoJS.enc.Utf8.parse(keyWord);
  var srcs = CryptoJS.enc.Utf8.parse(word);
  var encrypted = CryptoJS.AES.encrypt(srcs, key, { mode: CryptoJS.mode.ECB, padding: CryptoJS.pad.Pkcs7 });
  return encrypted.toString();
}
/**
 * @word 要解密的内容
 *  */
export function aesDecrypt (word) {
  let key = CryptoJS.enc.Utf8.parse('XwKsGlMcdPMEhR1B');
  const decrypted = CryptoJS.AES.decrypt(word, key, {
    mode: CryptoJS.mode.ECB,
    padding: CryptoJS.pad.Pkcs7
  });
  return CryptoJS.enc.Utf8.stringify(decrypted).toString();
}
