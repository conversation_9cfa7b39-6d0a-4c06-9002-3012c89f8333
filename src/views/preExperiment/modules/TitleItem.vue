<template>
  <a-row :gutter="24" style='flex:1'>
    <a-col :span="20">
      <div class="order-dispose">
        <div class="title-box">
          <span class="before"></span>
          <span>{{titleLable}}</span>
        </div>
      </div>
    </a-col>
    <a-col :span='4' v-if='toggleBtnShow' style='text-align: right'>
      <span @click="toggle">{{show?'收起':'展开'}}  <svg-icon icon-class='up' :style='`transform: rotate(${show ? 0 : 180}deg)`'/></span>
    </a-col>
  </a-row>
</template>
<script>
export default {
  props: {
    // 标题
    titleLable: {
      type: String
    },
    toggleBtnShow: {
      type: Boolean,
      default: false
    }
  },
  data () {
    return {
      show: true
    };
  },
  methods: {
    toggle () {
      this.show = !this.show;
      this.$emit('toggle', this.show);
    }
  }
};
</script>
