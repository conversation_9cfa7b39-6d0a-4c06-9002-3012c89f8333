# Virtual Scroll Select 组件使用说明

## 基本用法

### 默认渲染方式
```vue
<template>
  <virtual-scroll-select
    v-model="selectedValue"
    :select-data="selectData"
    :is-multiple="true"
  />
</template>

<script>
export default {
  data() {
    return {
      selectedValue: [],
      selectData: {
        data: [
          { name: '选项1', code: 'option1' },
          { name: '选项2', code: 'option2' },
          { name: '选项3', code: 'option3' }
        ],
        label: 'name',
        value: 'code'
      }
    }
  }
}
</script>
```

### 自定义插槽渲染

通过设置 `use-slot="true"` 并提供 `option` 插槽，可以完全自定义选项的渲染内容：

```vue
<template>
  <virtual-scroll-select
    v-model="selectedValue"
    :select-data="selectData"
    :is-multiple="true"
    :use-slot="true"
  >
    <template #option="{ item, index, selected, disabled }">
      <!-- 自定义选项内容 -->
      <div class="custom-option">
        <img :src="item.avatar" class="avatar" v-if="item.avatar" />
        <div class="content">
          <div class="title">{{ item.name }}</div>
          <div class="subtitle">{{ item.description }}</div>
        </div>
        <div class="status" v-if="item.status">
          <span :class="['status-badge', item.status]">{{ item.statusText }}</span>
        </div>
      </div>
    </template>
  </virtual-scroll-select>
</template>

<script>
export default {
  data() {
    return {
      selectedValue: [],
      selectData: {
        data: [
          { 
            name: '张三', 
            code: 'user1',
            avatar: '/avatars/user1.jpg',
            description: '前端开发工程师',
            status: 'online',
            statusText: '在线'
          },
          { 
            name: '李四', 
            code: 'user2',
            avatar: '/avatars/user2.jpg',
            description: '后端开发工程师',
            status: 'offline',
            statusText: '离线'
          }
        ],
        label: 'name',
        value: 'code'
      }
    }
  }
}
</script>

<style scoped>
.custom-option {
  display: flex;
  align-items: center;
  padding: 8px 0;
}

.avatar {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  margin-right: 12px;
}

.content {
  flex: 1;
}

.title {
  font-weight: 500;
  color: var(--zw-text-1-color--default);
}

.subtitle {
  font-size: 12px;
  color: var(--zw-text-3-color--default);
  margin-top: 2px;
}

.status-badge {
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 12px;
}

.status-badge.online {
  background: #f6ffed;
  color: #52c41a;
  border: 1px solid #b7eb8f;
}

.status-badge.offline {
  background: #fff2f0;
  color: #ff4d4f;
  border: 1px solid #ffccc7;
}
</style>
```

## Props 说明

| 参数 | 说明 | 类型 | 默认值 |
|------|------|------|--------|
| modelValue | 绑定值 | String/Array | [] |
| isMultiple | 是否多选 | Boolean | true |
| selectData | 选择数据配置 | Object | { data: [], label: 'name', value: 'code' } |
| useSlot | 是否使用自定义插槽渲染 | Boolean | false |
| maxTagCount | 最大显示标签数量 | Number | 1 |
| maxTagTextLength | 标签文本最大长度 | Number | 12 |
| defaultValue | 默认选中值 | Array | [] |

## 插槽说明

### option 插槽

当 `useSlot` 为 `true` 时，可以使用 `option` 插槽自定义选项渲染。

插槽参数：
- `item`: 当前选项的数据对象
- `index`: 当前选项的索引
- `selected`: 是否已选中
- `disabled`: 是否禁用

## 注意事项

1. 使用自定义插槽时，选中状态的样式会自动应用到插槽容器上
2. 多选模式下，选中图标会自动显示在插槽内容的右侧
3. 插槽内容应该避免使用会阻止事件冒泡的元素，以免影响选择功能
4. 为了保持良好的性能，插槽内容应该尽量简洁
