<template>
  <a-spin :spinning="loading">
    <vxe-table
      :data="dataSource"
      ref="multipleTable"
      class="my-table"
      :sort-config="{ remote: true }"
      resizable
      show-overflow
      highlight-hover-row
      size="small"
      :height="tableHeight - 24"
    >
      <vxe-table-column type="seq" :width="80" title="序号" align="center"></vxe-table-column>
      <vxe-table-column show-overflow="title" title="topic名称" :min-width="120" field="topic"> </vxe-table-column>
      <vxe-table-column show-overflow="title" title="设备上传时间" field="timeSource" :min-width="160"> </vxe-table-column>
      <vxe-table-column show-overflow="title" title="平台接收时间" field="acceptTime" :min-width="160"> </vxe-table-column>
      <vxe-table-column show-overflow="title" title="SN号" field="sn" :min-width="160"> </vxe-table-column>
      <vxe-table-column show-overflow="title" title="报文" :min-width="160">
        <template slot-scope="scope">
          <span>{{ type == '1' ? scope.row.originalMessage : scope.row.message }}</span>
        </template>
      </vxe-table-column>
      <template v-slot:empty>
        <span>查询无数据</span>
      </template>
    </vxe-table>
  </a-spin>
</template>
<script>
export default {
  props: {
    dataSource: {
      type: Array,
      default: () => {
        return [];
      }
    },
    tableHeight: {
      type: Number,
      default: 400
    },
    type: {
      default: 1,
      type: [String, Number]
    },
    loading: {
      type: Boolean,
      default: true
    }
  },
  data () {
    return {
      selectList: []
    };
  },
  methods: {
    async clearScroll () {
      let $table = this.$refs.multipleTable;
      $table && (await $table.clearScroll());
    }
  }
};
</script>
