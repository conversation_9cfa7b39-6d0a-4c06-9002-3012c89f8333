import { getSopDetailForBus } from '@/api/distributed/digitalRegulation';
import { checkOrder } from '@api/distributed/orderHall';
import { REFORM_TYPE, resolvePrecion } from './config';
import { isEmpty, isNumber, clone } from 'xe-utils';
export default {
  props: {
    formData: {
      type: Object,
      default: {}
    },
    rowInfo: {
      type: Object,
      default: {}
    }
  },
  data () {
    return {
      receiveCommonKeys: ['cleanCap', 'cleanType', 'grassType', 'liablePerson', 'beforeExperimentDate',
        'physicsPsId', 'taskDescription', 'findTime', 'predictRecoverTime', 'findPlace', 'findUser',
        'rectifyReq', 'deviceTypeId', 'deviceId', 'deviceName', 'deviceNo', 'isEngLegacy',
        'defectName', 'defectType', 'deviceType', 'riskLevel', 'erpSqJobRiskMappingId', 'totalRepairTime',
        'riskJobDate', 'uploadFileList', 'powerCutRange', 'defectHandleCondition', 'orderBudget', 'purchasesWay',
        'ticketDtoList'
      ],
      enumTypes: {
        3: '详情',
        6: '领取',
        5: '指派',
        7: '执行',
        8: '验收',
        4: '审批'
      },
      executeCommonKeys: ['sceneCondition', 'faultCap', 'totalLossPower', 'overdueReason', 'meteoMaker', 'conditionRemark', 'stepVoList',
        'sceneFileList', 'orderRealCost', 'offlineElectricityFee', 'settlementDocFileList', 'serverCycleStartDate', 'serverCycleEndDate',
        'documentPicFileList', 'invoicePicFileList', 'guaranteeSlipFileList', 'executeUniversalFileList', 'ticketDtoList', 'businessData'
      ],
      initialMaps: 'industry_task_sub_type,industry_task_type,industry_task_category_type,pdca_grass_type,pdca_clean_type,psa_model,' +
      'two_order_source,find_place,two_fault_classify,two_device_run_sts,pdca_defect_name,farm_scene_condition,' +
      'risk_level,job_category,care_scene_condition,power_cut_centre,power_cut_dis,defect_handle_condit,wash_purchases_way,' +
      'group_clean_sts,fulfilment_type,industry_task_source,ops_scene_condition,ticket_type,ticket_flow_user,ticket_type_dtl,industry_task_category',
      reformTypes: Object.keys(REFORM_TYPE)
    };
  },
  methods: {
    getCommonParams () {
      const order = this.formData;
      const rowInfo = this.rowInfo;
      return {
        id: order.id,
        taskId: order.taskId,
        taskDefKey: rowInfo.taskDefKey || order.taskDefKey || (order.todoInfo || {}).taskDefKey,
        processInstanceId: rowInfo.processInstanceId || order.processInstanceId || (order.todoInfo || {}).processInstanceId,
        processDefKey: rowInfo.processDefKey || order.processDefKey,
        otherSts: order.otherSts,
        updateTime: order.updateTime,
        taskType: order.taskType || rowInfo.taskType
      };
    },
    getStatus () {
      let order = this.rowInfo;
      return (order.step > 5 && order.processDefKey == 'activiti4273Workflow') || (order.step > 5 && order.processDefKey == 'activiti4773Workflow') || (order.step > 2 && order.processDefKey ==
        'activiti4873Workflow') || (order.processDefKey == 'activiti5073Workflow' && order.step > 1) || order.flowSts == '4' || order.otherSts == '4';
    },
    getStepList () {
      const deviceTypeId = this.formData.deviceTypeId || [];
      let val = deviceTypeId ? deviceTypeId[deviceTypeId.length - 1] : 0;
      getSopDetailForBus({
        taskType: this.formData.taskType,
        deviceType: val,
        // riskLevel: this.formData.riskLevel,
        isRelevanceTicket: !this.$isEmpty(this.formData.ticketVoList)
      }).then((res) => {
        this.formData.stepVoList = [];
        if (res.result_data && res.result_data.steps) {
          this.formData.stepVoList = res.result_data.steps.map((item) => {
            return {
              stepName: item.stepName,
              stepNo: item.stepNo,
              workDesc: '',
              uploadPictureList: [],
              uploadFileList: [],
              stepContent: item.stepContent,
              uploadExampleFileList: item.uploadFileList
            };
          });
        }
      });
    },
    // 站容站貌执行
    executeAppearance (type, map) {
      const { appearanceVoList } = this.formData;
      const params = Object.assign(
        {},
        this.getCommonParams(),
        { auditStatus: type, auditStatusType: 2, executeInfo: appearanceVoList }
      );
      if (type === '1') {
        // 执行提交
        const valid = appearanceVoList.every(item => {
          return !(!item.isNotInvolve && isEmpty(item.uploadFileList));
        });
        if (valid) {
          this.loading = true;
          this.excuteFn(params);
        } else {
          for (let i = 0; i < appearanceVoList.length; i++) {
            const current = appearanceVoList[i];
            if (!current.isNotInvolve && isEmpty(current.uploadFileList)) {
              this.$message.error({
                content: `${current.project}: 必须上传1-5张水印照片`,
                key: 'uploadFileList'
              });
              const psaAppearanceEl = document.querySelector('.psa-appearance');
              const currentRow = psaAppearanceEl.querySelectorAll('.vxe-body--row')[i];
              currentRow && currentRow.scrollIntoView();
              break;
            }
          }
        }
      } else if (type === '2') {
        // 执行暂存
        this.loading = true;
        this.excuteFn(params);
      }
    },
    // 站容站貌验收
    checkAppearance (message, type) {
      const { appearanceVoList } = this.formData;
      const map = {
        auditStatus: type,
        auditOpinion: message,
        auditStatusType: type,
        ...this.getCommonParams(),
        appraiseList: appearanceVoList
      };
      this.doCheckOrder(map);
    },
    // 调取验收api
    doCheckOrder (map) {
      checkOrder(map)
        .then((res) => {
          if (res.result_code === '1') {
            this.examineType = null;
            this.showAll = false;
            this.$message.success(res.message ? res.message : '操作成功');
            this.$emit('cancel');
            this.loading = false;
          }
        })
        .catch(() => {
          this.loading = false;
        });
    },
    verify (type) {
      const { appearanceVoList, otherSts, taskType } = this.formData;
      // 1. 校验站容站貌
      // 审核通过可不填写审核建议，如果不通过，至少需填写一条审核建议，退回时需校验并提示“请输入至少一条审核建议”
      if (taskType === '18') {
        if (type === '1' && otherSts === '9') {
          const list = appearanceVoList.filter(item => !item.isNotInvolve) || [];
          if (list.find(item => !item.score && !isNumber(item.score))) {
            appearanceVoList.forEach((item, index) => {
              if (!item.isNotInvolve && !item.score && !isNumber(item.score)) {
                this.$message.error({ content: `${item.project}: 评分为必填项`, key: 'appearance' });
                $('.psa-appearance').find('.appearance-score' + index).scrollIntoView();
                throw new Error('评分为必填项');
              }
            });
          } else {
            this.examineType = type; // 1、通过 0、退回
            this.showAll = true;
          }
        } else if (type === '0' && otherSts === '9') {
          this.examineType = type; // 1、通过 0、退回
          this.showAll = true;
        }
        if (type === '0' && otherSts === '10') {
          const flag = appearanceVoList.some(item => item.examineSuggest);
          if (!flag && appearanceVoList.length) {
            this.$message.error('请输入至少一条审核建议');
            throw new Error('请输入至少一条审核建议');
          } else {
            this.$confirm({
              title: '确定退回吗?',
              okText: '确定',
              cancelText: '取消',
              onOk: () => {
                this.checkAppearance('', type);
              }
            });
          }
        } else if (type === '1' && otherSts === '10') {
          this.$confirm({
            title: '确定通过吗?',
            okText: '确定',
            cancelText: '取消',
            onOk: () => {
              this.checkAppearance('', type);
            }
          });
        }
      }
    },
    // 应发电量执行
    executePower (type, map) {
      const { businessData } = this.formData;
      const clonedBusinessData = clone(businessData, true);
      const params = Object.assign(
        {},
        this.getCommonParams(),
        {
          auditStatus: type,
          auditStatusType: 2,
          executeInfo: resolvePrecion(clonedBusinessData, 'multiply')
        }
      );
      if (type === '1') {
        this.$refs.orderHallForm.validate(async (valid) => {
          if (valid) {
            this.loading = true;
            this.excuteFn(params);
          } else {
            this.$errorScroll();
          }
        });
      } else if (type === '2') {
        // 执行暂存
        this.loading = true;
        this.excuteFn(params);
      }
    }
  }
};
