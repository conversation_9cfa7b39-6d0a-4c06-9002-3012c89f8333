<template>
  <a-modal
    title="终止原因"
    :visible="visible"
    @cancel="handleCancel"
    destroy-on-close
  >
   <a-spin :spinning='confirmLoading'>
     <a-form-model ref="stopForm" :model="formData" :rules="rules" :label-col="{span: 0}" :wrapper-col="{span: 24}">
       <a-form-model-item label="" prop="terminationReason">
         <a-textarea v-model="formData.terminationReason" @blur="formData.terminationReason = $trim($event)" :max-length="255" :auto-size="{ minRows: 4, maxRows: 6}"
                     placeholder="请输入终止原因"></a-textarea>
       </a-form-model-item>
     </a-form-model>
   </a-spin>
    <template slot="footer">
      <div class="flex-end">
        <throttle-button
          label="取消"
          class="solar-eye-btn-primary-cancel"
          @click="handleCancel"
          :loading="confirmLoading"
        />
        <a-button class="ant-btn-primary" @click="onSubmit" :loading="confirmLoading">确认</a-button>
      </div>
    </template>
  </a-modal>
</template>

<script>
import { terminationTestApi } from '@api/isolarErp/taskHall';

export default {
  name: 'StopModal',
  data () {
    return {
      visible: false,
      confirmLoading: false,
      formData: { terminationReason: undefined },
      rules: {
        terminationReason: [{ required: true, message: '请输入终止原因' }]
      },
      row: {}
    };
  },
  methods: {
    init (row) {
      this.row = row;
      this.visible = true;
    },
    handleCancel () {
      this.visible = false;
      Object.assign(this.formData, this.$options.data().formData);
    },
    onSubmit () {
      this.$refs.stopForm.validate(valid => {
        if (valid) {
          this.confirmLoading = true;
          terminationTestApi({ ...this.formData, id: this.row.id }).then(res => {
            this.confirmLoading = false;
            this.$message.success('终止成功');
            this.handleCancel();
            this.$emit('queryPageData');
          }).catch(() => {
            this.confirmLoading = false;
          });
        }
      });
    }
  }
};
</script>

<style scoped>

</style>
