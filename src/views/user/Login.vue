<template>
  <div class="main" style="padding: 0 40px">
    <img :src="getPng('login_welcom')" class="welcome_img" alt="login left" />
    <a-form :form="form" class="user-layout-login solar-login-form" ref="formLogin" id="formLogin">
      <a-form-item>
        <a-input
          size="large"
          v-decorator="['userAccount', validatorRules.username, { validator: this.handleUsernameOrEmail }]"
          type="text"
          placeholder="请输入帐户名/手机号"
        >
          <!-- <a-icon slot="prefix" type="user" :style="{ color: 'rgba(0,0,0,.25)' }"/> -->
        </a-input>
      </a-form-item>
      <a-form-item>
        <a-input-password
          v-decorator="['password', validatorRules.password]"
          size="large"
          autocomplete="false"
          placeholder="请输入登录密码"
        >
          <!-- <a-icon slot="prefix" type="lock" :style="{ color: 'rgba(0,0,0,.25)' }"/> -->
        </a-input-password>
      </a-form-item>
      <Verify
        @success="verifySuccess"
        @error="verifyError"
        @close="verifyClose"
        :mode="'pop'"
        :captchaType="'blockPuzzle'"
        :loginParams="loginParams"
        :imgSize="{ width: '330px', height: '155px' }"
        ref="verify"
      ></Verify>

      <a-form-item class='form-line'>
        <a-checkbox
          v-decorator="['rememberMe', { initialValue: true, valuePropName: 'checked' }]"
          class="forge-password"
          >记住密码</a-checkbox
        >
        <router-link :to="{ name: 'alteration' }" class="forge-password forge-password-hover" style="float: right"> 忘记密码</router-link>
      </a-form-item>

      <a-form-item style="margin-top: 60px">
        <a-button
          size="large"
          type="primary"
          htmlType="submit"
          class="login-button solar-login-btn"
          :loading="loginBtn"
          @click.stop.prevent="handleSubmit"
          :disabled="loginBtn"
          >登录
        </a-button>
      </a-form-item>
      <a-form-item class="user-text">
        登录即代表同意<span class="page-link" @click="gotoPage('agreement.html')">《SolarEye用户协议》</span>和<span
          class="page-link"
          @click="gotoPage('privacyPolicy.html')"
          >《隐私政策》</span
        >
      </a-form-item>
    </a-form>
    <two-step-captcha
      v-if="requiredTwoStepCaptcha"
      :visible="stepCaptchaVisible"
      @success="stepCaptchaSuccess"
      @cancel="stepCaptchaCancel"
    ></two-step-captcha>
    <login-select-tenant ref="loginSelect" @success="loginSelectOk"></login-select-tenant>
    <security-modal ref="security" @ok="verifySuccess"></security-modal>
  </div>
</template>

<script>
import TwoStepCaptcha from '@/components/tools/TwoStepCaptcha';
import { mapActions } from 'vuex';
import { ACCESS_TOKEN } from '@/store/mutation-types';
import { postAction } from '@/api/manage';
import { aesEncrypt, aesDecrypt } from '@/utils/verify';
import LoginSelectTenant from './LoginSelectTenant';
import Verify from '@/components/verifition/Verify';
import router from '@/router';
import { generateIndexRouter } from '@/utils/util';
import { mixin } from '@/utils/mixin.js';
import SecurityModal from './SecurityModal.vue';
const codeUrl = process.env.VUE_APP_API_CODE_URL;
const enviroment = process.env.VUE_APP_ENV;
export default {
  components: {
    LoginSelectTenant,
    TwoStepCaptcha,
    Verify,
    SecurityModal
  },
  mixins: [mixin],
  data () {
    return {
      customActiveKey: 'tab1',
      codeUrl: codeUrl,
      loginBtn: false,
      // login type: 0 email, 1 username, 2 telephone
      loginType: 0,
      requiredTwoStepCaptcha: false,
      stepCaptchaVisible: false,
      form: this.$form.createForm(this),
      encryptedString: {
        key: '',
        iv: ''
      },
      state: {
        time: 60,
        smsSendBtn: false
      },
      validatorRules: {
        username: {
          rules: [{ required: true, message: '请输入账号名/手机号!' }, { validator: this.handleUsernameOrEmail }]
        },
        password: { rules: [{ required: true, message: '请输入登录密码!', validator: 'click' }] },
        mobile: { rules: [{ validator: this.validateMobile }] },
        captcha: { rule: [{ required: true, message: '请输入验证码!' }] },
        inputCode: { rules: [{ required: true, message: '请输入验证码!' }] }
      },
      verifiedCode: '',
      inputCodeContent: '',
      inputCodeNull: true,
      currentUsername: '',
      currdatetime: '',
      randCodeImage: '',
      requestCodeSuccess: false,
      loginParams: {},
      enviroment: enviroment
    };
  },
  created () {
    this.currdatetime = new Date().getTime();
    Vue.ls.remove(ACCESS_TOKEN);
    // this.getRouterData()
    document.title = 'SolarEye登录页';
    var loginUserInfo = JSON.parse(localStorage.getItem('logingUserInfo'));
    if (loginUserInfo) {
      this.$nextTick(() => {
        let isRememberMe = loginUserInfo.remember_me;
        this.form.setFieldsValue({
          userAccount: isRememberMe ? loginUserInfo.username : '',
          password: isRememberMe ? aesDecrypt(loginUserInfo.password) : '',
          rememberMe: loginUserInfo.remember_me
        });
      });
    }
  },
  computed: {
    permissionMenuList () {
      return this.$store.state.user.permissionList;
    }
  },
  methods: {
    ...mapActions(['Login', 'Logout']),
    // handler
    handleUsernameOrEmail (rule, value, callback) {
      const regex = /^([a-zA-Z0-9_-])+@([a-zA-Z0-9_-])+((\.[a-zA-Z0-9_-]{2,3}){1,2})$/;
      if (regex.test(value)) {
        this.loginType = 0;
      } else {
        this.loginType = 1;
      }
      callback();
    },
    verifySuccess (params) {
      // params 返回的二次验证参数
      this.$refs.loginSelect.show(params);
    },
    verifyError () {},
    verifyClose (params) {
      this.loginBtn = false;

      if (params) {
        if (params.code === 600) {
          this.$refs.security.showModal(params.result);
          return false;
        } else {
          this.$notification.error({
            message: '登录失败',
            description: params.message
          });
        }
      }
    },
    showVerify () {
      // 当mode="pop"时,调用组件实例的show方法显示组件
      this.$refs.verify.show();
    },
    handleSubmit () {
      let that = this;
      let loginParams = {};
      that.loginBtn = true;
      // 使用账户密码登录
      if (that.customActiveKey === 'tab1') {
        that.form.validateFields(
          ['userAccount', 'password', 'inputCode', 'rememberMe'],
          { force: true },
          (err, values) => {
            if (!err) {
              loginParams.username = values.userAccount;
              // 密码加密逻辑暂时注释掉，有点问题
              loginParams.password = aesEncrypt(values.password);
              loginParams.remember_me = values.rememberMe;
              localStorage.setItem('logingUserInfo', JSON.stringify(loginParams));
              loginParams.checkKey = that.currdatetime;
              loginParams.slidered = that.slidered;
              that.loginParams = loginParams;
              // if (this.enviroment) {
              that.showVerify();
              // } else {
              //   that
              //     .Login(loginParams)
              //     .then((res) => {
              //       this.$refs.loginSelect.show(res.result);
              //     })
              //     .catch((err) => {
              //       that.requestFailed(err);
              //     });
              // }
            } else {
              that.loginBtn = false;
            }
          }
        );
        // 使用手机号登录
      }
    },
    getCaptcha (e) {
      e.preventDefault();
      let that = this;
      this.form.validateFields(['mobile'], { force: true }, (err, values) => {
        if (!values.mobile) {
          that.cmsFailed('请输入手机号');
        } else if (!err) {
          this.state.smsSendBtn = true;
          let interval = window.setInterval(() => {
            if (that.state.time-- <= 0) {
              that.state.time = 60;
              that.state.smsSendBtn = false;
              window.clearInterval(interval);
            }
          }, 1000);

          const hide = this.$message.loading('验证码发送中..', 0);
          let smsParams = {};
          smsParams.mobile = values.mobile;
          smsParams.smsMode = '0';
          postAction('/sys/sms', smsParams)
            .then((res) => {
              if (!res.success) {
                setTimeout(hide, 0);
                this.cmsFailed(res.message);
              }
              console.log(res);
              setTimeout(hide, 500);
            })
            .catch((err) => {
              setTimeout(hide, 1);
              clearInterval(interval);
              that.state.time = 60;
              that.state.smsSendBtn = false;
              this.requestFailed(err);
            });
        }
      });
    },
    stepCaptchaSuccess () {
      this.loginSuccess();
    },
    stepCaptchaCancel () {
      this.Logout()
        .then(() => {
          this.loginBtn = false;
          this.stepCaptchaVisible = false;
        })
        .catch(() => {});
    },
    loginSuccess () {
      let that = this;
      this.$store
        .dispatch('GetPermissionList')
        .then((res) => {
          const menuData = res;
          const systemName = window._CONFIG['system'];
          if (menuData === null || menuData === '' || menuData === undefined || (menuData && menuData.length === 0)) {
            return;
          }
          let constRoutes = [];
          constRoutes = generateIndexRouter(systemName == '/work' ? menuData : menuData[0].children);
          // 添加主界面路由
          that.$store
            .dispatch('UpdateAppRouter', {
              constRoutes
            })
            .then(() => {
              // 根据roles权限生成可访问的路由表
              // 动态添加可访问路由表
              router.addRoutes(that.$store.getters.addRouters);
            });
          that.$router.push({ path: this.$store.state.user.firstMenu.path }).catch((err) => {
            console.log(err);
          });
        })
        .catch(() => {
          that.$store.dispatch('Logout').then(() => {
            if (this.$route.path !== '/user/login') {
              this.$router
                .push({
                  path: '/user/login'
                })
                .catch((res) => {
                  console.log(res);
                });
            }
          });
        });
    },
    cmsFailed (err) {
      this.$notification['error']({
        message: '登录失败',
        description: err,
        duration: 4
      });
    },
    requestFailed (err) {
      this.$notification['error']({
        message: '登录失败',
        description: ((err.response || {}).data || {}).message || err.message || '请求出现错误，请稍后再试',
        duration: 4
      });
      this.loginBtn = false;
    },
    validateMobile (rule, value, callback) {
      if (!value || new RegExp(/^1([38][0-9]|4[579]|5[0-3,5-9]|6[6]|7[0135678]|9[89])\d{8}$/).test(value)) {
        callback();
      } else {
        callback(new Error('您的手机号码格式不正确!'));
      }
    },
    validateInputCode (rule, value, callback) {
      if (!value || this.verifiedCode == this.inputCodeContent) {
        callback();
      } else {
        callback(new Error('您输入的验证码不正确!'));
      }
    },
    generateCode (value) {
      this.verifiedCode = value.toLowerCase();
    },
    inputCodeChange (e) {
      this.inputCodeContent = e.target.value;
    },
    loginSelectOk () {
      this.loginSuccess();
    },
    getRouterData () {
      this.$nextTick(() => {
        if (this.$route.params.username) {
          this.form.setFieldsValue({
            userAccount: this.$route.params.username
          });
        }
      });
    },
    // 跳转用户协议或隐私
    gotoPage (fileName) {
      let url = 'https://staticres.isolareye.com/' + fileName;
      window.open(url, '_blank');
    },
    getPng (name) {
      let navTheme = this.navTheme;
      return 'https://staticres.isolareye.com/image/public/' + name + '_' + navTheme + '.png';
    }
  }
};
</script>

<style lang="less">
.user-text {
  margin-top: -16px;
  color: var(--zw-text-3-color--default) !important;
  .page-link {
    color: var(--zw-conduct-color--normal);
    cursor: pointer;
  }
}
.welcome_img {
  padding: 68px 0 40px;
  display: flex;
  margin: 0 auto;
  width: 175px;
  object-fit: cover;
}
.solar-login-form {
  .ant-input-lg {
    height: 44px;
  }
  .ant-input-password-icon {
    color: var(--zw-text-reset-grey-color--default);

    &:hover {
      color: var(--zw-primary-color--default);
    }
  }
  input {
    &:hover,
    &:focus {
      border-color: var(--zw-primary-color--default) !important;
      box-shadow: 0 0 0 2px rgba(192, 93, 27, 0.2);
    }
  }
  .ant-checkbox-checked .ant-checkbox-inner {
    background-color: var(--zw-primary-color--default);
    border-color: var(--zw-primary-color--default);
  }
  .solar-login-btn {
    width: 360px;
    height: 44px;
    border-radius: 3px;
    opacity: 1;
    padding: 0 15px;
    font-size: 16px;
    background: var(--zw-primary-color--default);
    border-color: var(--zw-primary-color--default);
    color: white;
    &:hover {
      background: var(--zw-primary-color--hover);
      border-color: var(--zw-primary-color--hover);
    }
  }
  .forge-password {
    font-size: 14px;
    opacity: 0.7;
    color: var(--zw-text-2-color--default);
  }
  .forge-password-hover:hover {
    color: var(--zw-primary-color--default);
  }
  .form-line .ant-form-item-control {
    line-height: 14px;
  }
}
</style>
<style lang="less" scoped>
.user-layout-login {
  button.login-button {
    padding: 0 15px;
    font-size: 16px;
    height: 44px;
    width: 100%;
  }
}
</style>
<style>
.valid-error .ant-select-selection__placeholder {
  color: var(--zw-warning-color--normal);
}
input::-webkit-autofill {
  -webkit-box-shadow: 0 0 0px 1000px white inset !important;
  box-shadow: 0 0 0px 1000px white inset !important;
}

input:-internal-autofill-previewed,
input:-internal-autofill-selected {
  transition: background-color 5000s ease-out 0.5s;
  -webkit-text-fill-color: var(--zw-text-1-color--default) !important;
}
</style>
