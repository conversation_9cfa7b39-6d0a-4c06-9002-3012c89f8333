<template>
  <div class="drawer-form-com">
    <div class="drawer-form-content">
      <a-spin :spinning="loading">
        <a-form-model class="emp-form" ref="empform" :model="empform" :rules="rules" :labelCol="{ style: 'width: 130px' }" :wrapperCol="{ style: 'width: calc(100% - 130px)' }">

          <!-- 基本信息 -->
          <template v-if="!setDisabled ()">
            <a-row :gutter="24">
              <a-col :span='24'>
                <div class="order-dispose">
                  <div class="title-box">
                    <span class="before"></span>
                    <span>基本信息</span>
                  </div>
                </div>
              </a-col>
              <template v-for="item in baseList">
                <a-col v-show="show(item.key)" :key="item.key" :xxl='8' :xl='12' :lg='24'>
                  <a-form-model-item :label="item.label" :prop="item.key">
                    <!-- 姓名、联系方式焦点离开后获取账号信息 -->
                    <template v-if="item.key == 'empName' || item.key == 'tel'">
                      <a-input v-model="empform[item.key]" size="default" :maxLength="item.length" :readOnly="setDisabled(item.key)" :disabled="isDisabled" @blur="getUserAccount(item.key)"
                        style="width: 100%;"></a-input>
                    </template>
                    <!-- 职位 -->
                    <template v-else-if="item.key == 'position'">
                      <a-select v-model="empform[item.key]" size="default" show-search option-filter-prop="children" :filter-option="filterOption"
                        :disabled="setDisabled(item.key) || isDisabled" style="width:100%;" @change="getRole">
                        <a-select-option v-for="(option,index) in dictMap[item.dict]" :key="index" :value="option.dataValue">{{option.dataLable}}</a-select-option>
                      </a-select>
                    </template>
                    <!-- 角色 -->
                    <template v-else-if="item.key == 'roleId'">
                      <a-input v-if="setDisabled(item.key)" v-model="empform['roleName']" size="default" :readOnly="setDisabled(item.key)" style="width: 100%;"></a-input>
                      <a-select v-else v-model="empform[item.key]" option-filter-prop="children" :filter-option="filterOption"
                        mode="multiple" maxTagCount="1" size="default" :disabled="setDisabled(item.key)" style="width:100%;" @change="roleChange">
                        <a-select-option v-for="(role,index) in roleList" :key="index" :value="role.id">{{role.roleName}}</a-select-option>
                      </a-select>
                    </template>
                    <!-- 所属部门 -->
                    <template v-else-if="item.key == 'depId'">
                      <a-input v-if="setDisabled(item.key)" v-model="empform['departName']" size="default" :readOnly="setDisabled(item.key)" style="width: 100%;"></a-input>
                      <dep-tree-select v-else v-model="empform[item.key]" @change="changeEvent" :disabled="setDisabled(item.key) || isDisabled" :all="true" :isExtra="true" show="0" style="width:100%;" />
                    </template>
                    <template v-else-if="item.key == 'drId'">
                    <a-tree-select
                      v-model="empform.drId"
                      :tree-data="treeData"
                      tree-checkable
                      :replaceFields="{title: 'name', key: 'id',value:'id'}"
                      allow-clear
                      :treeCheckStrictly="true"
                      :disabled="setDisabled(item.key) || isDisabled || disabledRoles"
                      search-placeholder="请选择数据权限"
                      :dropdown-style="{ maxHeight: '400px', overflow: 'auto' }"
                        treeNodeFilterProp="title"
                        />
                        <!-- <a-select v-else mode="multiple" maxTagCount="1" disabled v-model="empform.drName">
                          <a-select-option v-for="item in empform['drName'].split(',')" :key="item">{{
                            item
                          }}</a-select-option>
                        </a-select> -->
                      <!-- <dataRoleTreeSelect v-model="empform.drId" :disabled="setDisabled(item.key) || isDisabled" style="width:100%;"></dataRoleTreeSelect> -->
                    </template>
                    <!-- 文本 -->
                    <template v-else-if="item.type == 'text'">
                      <a-input v-model="empform[item.key]" size="default" :maxLength="item.length" :readOnly="setDisabled(item.key) || isDisabled" :disabled="isDisabled"
                        @blur="empform[item.key] = $trim($event)" style="width: 100%;"></a-input>
                    </template>
                    <!-- 时间 -->
                    <template v-else-if="item.type == 'date'">
                      <a-date-picker v-model="empform[item.key]" format="YYYY-MM-DD" valueFormat="YYYY-MM-DD" size="default" :disabled="setDisabled(item.key) || isDisabled" style="width: 100%;" />
                    </template>
                    <!-- 数据字典 -->
                    <template v-else>
                      <a-select v-model="empform[item.key]" size="default" :disabled="setDisabled(item.key) || isDisabled" style="width:100%;">
                        <a-select-option v-for="(option,index) in dictMap[item.dict]" :key="index" :value="option.dataValue">{{option.dataLable}}</a-select-option>
                      </a-select>
                    </template>
                  </a-form-model-item>
                </a-col>
              </template>
            </a-row>
          </template>
          <template v-else>
            <a-row>
              <detail-layout :labelList="baseList" :form="empform" title="基本信息" :dictMap="dictMap"></detail-layout>
            </a-row>
          </template>

          <!-- 个人信息 -->
          <template v-if="!setDisabled ()">
            <a-row :gutter="24">
              <a-col :span='24'>
                <div class="order-dispose">
                  <div class="title-box">
                    <span class="before"></span>
                    <span>个人信息</span>
                  </div>
                </div>
              </a-col>
              <template v-for="item in indList">
                <a-col v-show="show(item.key)" :xxl='8' :xl='12' :lg='24' :key="item.label">
                  <a-form-model-item :label="item.label" :prop="item.key">
                    <!-- 文本 -->
                    <template v-if="item.type == 'text'">
                      <a-input v-model="empform[item.key]" size="default" :maxLength="item.length" :readOnly="setDisabled(item.key)" :disabled="isDisabled"
                        @blur="empform[item.key] = $trim($event)" style="width: 100%;"></a-input>
                    </template>
                    <!-- 时间 -->
                    <template v-else-if="item.type == 'date'">
                      <a-date-picker v-model="empform[item.key]" format="YYYY-MM-DD" valueFormat="YYYY-MM-DD" size="default"
                        :disabled="param.type =='3'? setDisabled(item.key):(item.key=='expiryDate'?false : isDisabled)" style="width: 100%;" />
                    </template>
                    <!-- 数据字典 -->
                    <template v-else>
                      <a-select v-model="empform[item.key]" size="default" :disabled="setDisabled(item.key)|| isDisabled" style="width:100%;">
                        <a-select-option v-for="(option,index) in dictMap[item.dict]" :key="index" :value="option.dataValue">{{option.dataLable}}</a-select-option>
                      </a-select>
                    </template>
                  </a-form-model-item>
                </a-col>
              </template>
            </a-row>
          </template>
          <template v-else>
            <a-row>
              <detail-layout :labelList="indList" :form="empform" title="个人信息" :dictMap="dictMap"></detail-layout>
            </a-row>
          </template>

          <!-- 工作信息 -->
          <template v-if="!setDisabled ()">
            <a-row :gutter="24">
              <a-col :span='24'>
                <div class="order-dispose">
                  <div class="title-box">
                    <span class="before"></span>
                    <span>工作信息</span>
                  </div>
                </div>
              </a-col>
              <template v-for="item in workList">
                <a-col v-show="show(item.key)" :xxl='8' :xl='12' :lg='24' :key="item.label">
                  <a-form-model-item :label="item.label" :prop="item.key">
                    <!-- 导师 -->
                    <template v-if="item.key == 'tutorUserAccount'">
                      <a-input v-if="setDisabled(item.key)" v-model="empform['tutorName']" size="default" :readOnly="setDisabled(item.key)" style="width: 100%;"></a-input>
                      <tutor-model v-else v-model="empform['tutorUserAccount']" :account="empform.userAccount" :name="empform['tutorName']" :disabled="setDisabled(item.key)" style="width: 100%;" />
                    </template>
                    <!-- 工号-->
                    <template v-else-if="item.key == 'empId'">
                      <a-input v-model="empform['empId']" size="default" :maxLength="32" :readOnly="setDisabled(item.key) || fromTel" :disabled="isDisabled || empform.property=='6'"
                        @blur="empform['empId'] = $trim($event)" style="width: 100%;"></a-input>
                    </template>
                    <!-- 文本 -->
                    <template v-else-if="item.type == 'text'">
                      <a-input v-model="empform[item.key]" size="default" :maxLength="item.length" :readOnly="setDisabled(item.key)" :disabled="isDisabled"
                        @blur="empform[item.key] = $trim($event)" style="width: 100%;"></a-input>
                    </template>
                    <!-- 时间 -->
                    <template v-else-if="item.type == 'date'">
                      <a-date-picker v-model="empform[item.key]" format="YYYY-MM-DD" valueFormat="YYYY-MM-DD" size="default" :disabled="setDisabled(item.key)|| isDisabled" style="width: 100%;" />
                    </template>
                    <!-- 数据字典 -->
                    <template v-else>
                      <a-select v-model="empform[item.key]" size="default" @change="changeProperty(item.key,empform[item.key])"
                        :disabled="param.type == '3'? setDisabled(item.key):(item.dict=='work_tic_thr_ppl')?false : isDisabled" style="width:100%;">
                        <a-select-option v-for="(option,index) in dictMap[item.dict]" :key="index" :value="option.dataValue">{{option.dataLable}}</a-select-option>
                      </a-select>
                    </template>
                  </a-form-model-item>
                </a-col>
              </template>
            </a-row>
          </template>
          <template v-else>
            <a-row>
              <detail-layout :labelList="workList" :form="empform" title="工作信息" :dictMap="dictMap"></detail-layout>
            </a-row>
          </template>
        </a-form-model>
        <!-- tabs 学历、联系人、合同、银行卡、照片、资格证书 -->
        <a-tabs v-model="active">
          <a-tab-pane key="education" tab="学历信息" :forceRender="true">
            <education ref="education" :isgId="param.isgId" />
          </a-tab-pane>
          <a-tab-pane key="contacts" tab="联系人信息" :forceRender="true">
            <contacts ref="contacts" />
          </a-tab-pane>
          <a-tab-pane key="contract" tab="合同信息" :forceRender="true">
            <contract ref="contract" :isgId="param.isgId" />
          </a-tab-pane>
          <a-tab-pane key="card" tab="银行卡信息" :forceRender="true">
            <card ref="card" />
          </a-tab-pane>
          <a-tab-pane key="picFile" tab="照片附件" :forceRender="true">
            <picFile ref="picFile" :empName="empform.empName" />
          </a-tab-pane>
          <a-tab-pane key="certificate" tab="资格证书" :forceRender="true">
            <certificate ref="certificate" pageFrom="individualList" />
          </a-tab-pane>
          <a-tab-pane v-if="showHandle('************')" key="pay" tab="员工薪资" :forceRender="true">
            <pay :type="param.type != 3" ref="pay" />
          </a-tab-pane>
        </a-tabs>
      </a-spin>
    </div>

    <div class="drawer-form-foot">
      <div v-if="param.type != '3'">
        <throttle-button label="保存" :loading="loading" @click="doSubmit" />
        <throttle-button label="取消" type="info" :disabled="loading" @click="cancel" />
      </div>
      <throttle-button v-else label="返回" type="info" @click="cancel" />
    </div>
  </div>
</template>

<script>
import {
  autoGenerateEmployeeAccount,
  saveEmployeeBaseInfo,
  getEmployeeBaseInfo,
  updateEmployeeBaseInfo,
  queryRoleByPosition,
  queryAccount
} from '@/api/isolarErp/employee/employee';
import { queryall, queryDeptTree } from '@/api/api';
import initDict from '@/mixins/initDict';
import education from './education';
import contacts from './contacts';
import contract from './contract';
import pay from './pay';
import card from './card';
import picFile from './picFile';
import certificate from './certificate';
import tutorModel from './tutor';
import { TENANT_ID } from '@/store/mutation-types';
import moment from 'moment';
// import dataRoleTreeSelect from '@/components/com/dataRoleTreeSelect';
export default {
  components: {
    education,
    contacts,
    contract,
    card,
    picFile,
    pay,
    certificate,
    tutorModel
    // dataRoleTreeSelect
  },
  mixins: [initDict],
  model: {
    prop: 'value',
    event: 'change'
  },
  data () {
    return {
      disabled: false,
      editType: null,
      loading: false,
      fromTel: false, // 此时列表数据是否已经由手机号带出
      defaultRole: undefined,
      param: {},
      roleList: [], // 角色列表
      file: {
        file: '',
        fileName: ''
      },
      // 基本信息
      baseList: [
        { key: 'empName', label: '姓名', type: 'text', length: 50 },
        { key: 'tel', label: '联系方式', type: 'text', length: 11 },
        {
          key: 'depId',
          label: '所属部门',
          func: (params) => {
            return params.type != '3';
          }
        },
        { key: 'departName', label: '所属部门' },
        { key: 'position', label: '职位', dict: 'position' },
        {
          key: 'roleId',
          label: '角色分配',
          func: (params) => {
            return params.type != '3';
          }
        },
        { key: 'roleName', label: '角色分配' },
        {
          key: 'drId',
          label: '数据权限',
          func: (params) => {
            return params.type != '3';
          }
        },
        { key: 'drName', label: '数据权限' },
        { key: 'userAccount', label: '用户账号', type: 'text', length: 32 }
      ],
      // 个人信息
      indList: [
        { key: 'idCard', label: '身份证号', type: 'text', length: 20 },
        { key: 'sex', label: '性别', dict: 'sex' },
        { key: 'nation', label: '民族', dict: 'nation' },
        { key: 'politicalStatus', label: '政治面貌', dict: 'political_status' },
        { key: 'maritalSts', label: '婚姻状况', dict: 'marital_sts' },
        { key: 'country', label: '国家(地区)', dict: 'country' },
        { key: 'expiryDate', label: '证件有效期', type: 'date' },
        { key: 'mail', label: '电子邮箱', type: 'text', length: 50 },
        { key: 'birthDate', label: '出生年月日', type: 'date' },
        { key: 'age', label: '年龄', type: 'text', length: 3 },
        { key: 'workAge', label: '工龄', type: 'text', length: 32 },
        { key: 'address', label: '住址', type: 'text', length: 200 },
        { key: 'household', label: '身份证地址', type: 'text', length: 50 }
      ],
      // 工作信息
      workList: [
        { key: 'property', label: '员工类型', dict: 'property' },
        { key: 'empId', label: '工号' },
        { key: 'isleader', label: '是否是负责人', dict: 'yes_no_emp' },
        { key: 'empLevel', label: '岗位职级', dict: 'level' },
        { key: 'workSts', label: '员工状态', dict: 'work_sts_emp' },
        { key: 'startDate', label: '入职时间', type: 'date' },
        { key: 'workFirstTime', label: '首次参加工作时间', type: 'date' },
        { key: 'probationPeriod', label: '试用期', dict: 'probation_period' },
        {
          key: 'tutorUserAccount',
          label: '导师',
          func: (params) => {
            return params.type != '3';
          }
        },
        { key: 'tutorName', label: '导师' },
        { key: 'workTicThrPpl', label: '三种人', dict: 'work_tic_thr_ppl' },
        { key: 'companyAge', label: '司龄', type: 'text', length: 32 },
        { key: 'promotionDate', label: '转正日期', type: 'date' },
        { key: 'desertDate', label: '离职时间', type: 'date' },
        { key: 'salaryPayPlace', label: '发薪地', type: 'text', length: 100 }
      ],
      // 新增、编辑时不显示字段
      editHide: ['departName', 'roleName', 'drName', 'sex', 'birthDate', 'age', 'workAge', 'tutorName', 'companyAge', 'promotionDate'],
      empform: {
        empName: '',
        tel: '',
        depId: '',
        position: '',
        roleId: [],
        drId: [],
        userAccount: '',
        idCard: '',
        nation: '',
        politicalStatus: '',
        maritalSts: '',
        country: '',
        expiryDate: '',
        mail: '',
        address: '',
        household: '',
        empId: '',
        property: '',
        isleader: '',
        empLevel: '',
        workSts: '',
        startDate: '',
        workFirstTime: '',
        probationPeriod: '',
        tutorUserAccount: '',
        tutorName: '',
        workTicThrPpl: '',
        sex: '',
        birthDate: '',
        age: '',
        workAge: '',
        companyAge: '',
        promotionDate: '',
        desertDate: '',
        salaryPayPlace: ''
      },
      type: 1,
      rules: {
        empName: [
          { required: true, message: '请填写员工姓名', trigger: 'blur' }
        ],
        property: [
          { required: false, message: '员工类型不能为空', trigger: 'change' }
        ],
        tel: [
          { required: true, message: '请填写联系方式', trigger: 'blur' },
          { pattern: /^[1][3|4|5|6|7|8|9][0-9]{9}$/, message: '请输入正确的联系方式' }
        ],
        depId: [
          { required: true, message: '请选择部门', trigger: 'change' }
        ],
        position: [
          { required: true, message: '请选择职位', trigger: 'change' }
        ],
        roleId: [
          { required: true, message: '请选择角色', trigger: 'change' }
        ],
        drId: [
          { required: true, message: '请选择数据权限', trigger: 'change' }
        ],
        userAccount: [
          { required: true, message: '请填写用户账号', trigger: 'change' }
        ],
        empId: [
          { required: true, message: '请填写工号', trigger: 'blur' },
          { pattern: /^[0-9]+$/, message: '工号格式有误，请输入半角数字' }
        ],
        workSts: [
          { required: true, message: '请选择员工状态', trigger: 'change' }
        ],
        startDate: [
          { required: false, message: '请填写入职时间', trigger: 'change' }
        ]
      },
      readOnly: ['userAccount'], // 新增、编辑模式下不可编辑的字段
      active: 'education',
      disabledRoles: true,
      treeData: [],
      backTreeData: []
    };
  },
  mounted () {
    // 加载数据字典
    const dict = 'emp_pic_type,position,nation,political_status,yes_no_emp,marital_sts,country,property,level,work_sts_emp,probation_period,work_tic_thr_ppl,sex';
    this.getDictMap(dict);
    this.initAllRoleList();
    this.loadTree();
  },
  computed: {
    isDisabled () {
      return this.param.type == '2' && this.param.isgId != '';
    }
  },
  methods: {
    // 初始化表单数据
    init (type, row) {
      this.param = row;
      if (type == '1') { // 新增
        // 加载tabs
        this.$nextTick(() => {
          this.initTabs();
        });
      } else {
        this.getEmpInfo(type);
      }
      this.type = type;
      let titlePrefix = '个人档案';
      if (type == '1') return titlePrefix + '新增';
      if (type == '2') return titlePrefix + '编辑';
      if (type == '3') return titlePrefix + '详情';
    },
    // 获取员工信息
    getEmpInfo (type) {
      const self = this;
      self.loading = true;
      const map = {
        account: this.param.account
      };
      getEmployeeBaseInfo(map).then(res => {
        if (res.result_code == '1') {
          self.initForm(res.result_data, type);
        }
      }).catch(() => {
        self.cancel();
      });
    },
    parentTree (arr, id) { // arr 所有的树数据 id 某个子节点的id
      var temp = [];
      var callback = function (nowArr, id) { // 先定义个函数寻找子节点位置 找到后 再找改节点父元素位置 以此类推
        for (var i = 0; i < nowArr.length; i++) {
          var item = nowArr[i];
          if (item.id === id) {
            temp.push(item.code);
            callback(arr, item.parentId); // pid 父级ID
            break;
          } else {
            if (item.children) {
              callback(item.children, id); // menus 子节点字段名称
            }
          }
        }
      };
      callback(arr, id);
      return temp; // 最后返回
    },
    // 编辑或详情时数据加载
    initForm (data, type) {
      let form = data.baseInfo;
      form.type = type;
      form.roleId = (form.roleId ? form.roleId.split(',') : []);
      form.drId = form.dataRoles;
      this.filterTreeData(form.depId);
      // 编辑时获取默认角色
      if (type == '2') {
        queryRoleByPosition({ position: form.position }).then((res) => {
          let roleId = (res.result_data && res.result_data.roleId ? res.result_data.roleId : undefined);
          this.defaultRole = roleId;
        }).catch(() => {
          this.defaultRole = undefined;
        });
      }
      this.$nextTick(() => {
        this.empform = form;
        const erpEmpEdu = (data.hasOwnProperty('erpEmpEdu') && data.erpEmpEdu ? data.erpEmpEdu : []);
        this.$refs['education'].init(erpEmpEdu, type);
        const erpEmpEmerContact = (data.hasOwnProperty('erpEmpEmerContact') && data.erpEmpEmerContact ? data.erpEmpEmerContact : []);
        this.$refs['contacts'].init(erpEmpEmerContact, type);
        const contract = (data.hasOwnProperty('contract') && data.contract ? data.contract : []);
        this.$refs['contract'].init(contract, type);
        const bank = (data.hasOwnProperty('bank') && data.bank ? data.bank : []);
        this.$refs['card'].init(bank, type);
        const certificate = (data.hasOwnProperty('certificate') && data.certificate ? data.certificate : []);
        this.$refs['certificate'].init(certificate, type);
        if (this.showHandle('************')) {
          const pay = (data.hasOwnProperty('salaryList') && data.salaryList ? data.salaryList : []);
          this.$refs['pay'].init(pay, type);
        }
        const picFile = (data.hasOwnProperty('photo') && data.photo ? data.photo : []);
        this.$refs['picFile'].init(picFile, type);

        this.loading = false;
      });
    },

    // 初始化tabs数据
    initTabs () {
      this.$refs['education'].init([], '1');
      this.$refs['contacts'].init([], '1');
      this.$refs['contract'].init([], '1');
      this.$refs['card'].init([], '1');
      this.$refs['certificate'].init([], '1');
      this.$refs['picFile'].init([], '1');
      if (this.showHandle('************')) {
        this.$refs['pay'].init([], '1');
      }
    },
    filterTreeData (depId) {
      if (depId) {
        let parentNode = this.parentTree(this.backTreeData, depId);
        this.treeData = this.backTreeData.filter(item => {
          return parentNode.indexOf(item.code) > -1;
        });
      }
      this.disabledRoles = false;
    },
    // 根据姓名、手机号获取用户账号
    getUserAccount (key, isAccount) {
      if (this.param.type == '2') { // 编辑时账号不可变
        return;
      }
      let val = this.empform[key].trim().toString();
      this.empform[key] = val;
      let oth = (key == 'empName' ? this.empform.tel.toString() : this.empform.empName.toString());
      if (val && oth) {
        const map = {
          empName: (key == 'empName' ? val : oth),
          tel: (key == 'tel' ? val : oth)
        };
        autoGenerateEmployeeAccount(map).then(res => {
          if (res.result_code == '1') {
            let data = res.result_data;
            if (!isAccount) {
              this.empform.userAccount = (data.hasOwnProperty('userAccount') ? data.userAccount : '');
              let roleId = (data.hasOwnProperty('roleId') && data.roleId ? data.roleId : '');
              this.empform.roleId = (roleId ? roleId.split(',') : this.empform.roleId);

              this.empform.empName = (data.hasOwnProperty('realName') ? data.realName : map.empName);
              this.empform.depId = (data.hasOwnProperty('departIds') ? data.departIds : this.empform.depId);
              if (data.hasOwnProperty('workNo')) {
                this.disabled = true;
                this.fromTel = true;
              } else {
                this.disabled = false;
                this.fromTel = false;
              }
            }
            this.filterTreeData(this.empform.depId);
            this.empform.empId = (data.hasOwnProperty('workNo') ? data.workNo : '');
          } else {
            this.fromTel = false;
            self.$message.warning(res.result_msg);
          }
        });
      }
    },
    // form表单保存点击事件
    doSubmit () {
      this.loading = true;
      // 表单验证
      this.$refs['empform'].validate((valid) => {
        if (valid) {
          this.checkData();
        } else {
          this.loading = false;
          return false;
        }
      });
    },
    // tabs数据校验
    checkData () {
      const self = this;
      try {
        const form = Object.assign({}, self.empform);
        if (form.workSts == '2' && !form.desertDate) {
          throw new Error('离职状态的员工须填写离职时间!');
        }
        if (form.workFirstTime && form.startDate && (form.workFirstTime > form.startDate)) {
          throw new Error('入职时间不能小于首次参加工作时间');
        }
      } catch (e) {
        self.$message.warning(e.message);
        self.loading = false;
        return;
      }
      self.checkAll().then(res => {
        if (res[0]) {
          self.loading = false;
          self.active = 'education';
          return;
        }
        if (res[1]) {
          self.loading = false;
          self.active = 'contacts';
          return;
        }
        if (res[2]) {
          self.loading = false;
          self.active = 'contract';
          return;
        }
        if (res[3]) {
          self.loading = false;
          self.active = 'card';
          return;
        }
        if (res[4]) {
          self.loading = false;
          self.active = 'certificate';
          return;
        }
        if (res[5]) {
          self.loading = false;
          self.active = 'pay';
          return;
        }
        self.save();
      });
    },
    // checkAll
    checkAll () {
      let education = new Promise((resolve, reject) => {
        resolve(this.educationCheck());
      });
      let contacts = new Promise((resolve, reject) => {
        resolve(this.contactsCheck());
      });
      let contract = new Promise((resolve, reject) => {
        resolve(this.contractCheck());
      });
      let card = new Promise((resolve, reject) => {
        resolve(this.cardCheck());
      });
      let certificate = new Promise((resolve, reject) => {
        resolve(this.certificateCheck());
      });
      let pay = new Promise((resolve, reject) => {
        resolve(this.payCheck());
      });

      return new Promise((resolve, reject) => {
        let params = [education, contacts, contract, card, certificate, pay];
        Promise.all(params).then(values => {
          resolve(values);
        });
      });
    },
    // 学历信息校验
    async educationCheck () {
      const educationV = await this.$refs['education'].validate();
      return educationV;
    },
    // 联系人信息校验
    async contactsCheck () {
      try {
        return await this.$refs['contacts'].validate();
      } catch (error) {
        console.log(error);
      }
    },
    // 合同信息校验
    async contractCheck () {
      try {
        return await this.$refs['contract'].validate();
      } catch (error) {
        throw new Error(error);
      }
    },
    // 银行卡信息校验
    async cardCheck () {
      try {
        return await this.$refs['card'].validate();
      } catch (error) {
        throw new Error(error);
      }
    },
    // 资格证书信息校验
    async certificateCheck () {
      const certificateV = await this.$refs['certificate'].validate();
      return certificateV;
    },
    // 员工薪资
    async payCheck () {
      if (this.showHandle('************')) {
        const payV = await this.$refs['pay'].validate();
        return payV;
      } else {
        return false;
      }
    },
    // 数据保存
    save () {
      this.loading = true;
      let form = Object.assign({}, this.empform);
      let arr = [];
      this.empform.drId.forEach(item => {
        arr.push(item.value);
      });
      form.drId = arr.join(',');
      form.roleId = form.roleId.join(',');
      const education = this.$refs['education'].getData();
      const contacts = this.$refs['contacts'].getData();
      const contract = this.$refs['contract'].getData();
      const card = this.$refs['card'].getData();
      const picFile = this.$refs['picFile'].getData();
      const certificate = this.$refs['certificate'].getData();
      let salaryList;
      if (this.showHandle('************')) {
        salaryList = this.$refs['pay'].getData();
        salaryList = salaryList.filter(item => !item.hasOwnProperty('id')).map(item => {
          let obj = JSON.parse(JSON.stringify(item));
          obj.ym = moment(obj.ym).format('YYYY-MM') + '-01';
          return obj;
        });
      }

      const map = {
        'baseInfo': form,
        'erpEmpEdu': this.setProps(education, form.empId, form.userAccount),
        'erpEmpEmerContact': this.setProps(contacts, form.empId, form.userAccount),
        'contract': this.setProps(contract, form.empId, form.userAccount),
        'bank': this.setProps(card, form.empId, form.userAccount),
        'photo': this.getPhoto(picFile, form.empId, form.userAccount),
        'certificate': this.setProps(certificate, form.empId, form.userAccount),
        'salaryList': this.showHandle('************') ? this.setProps(salaryList, form.empId, form.userAccount) : undefined
      };

      if (this.param.type == '1') { // 新增
        saveEmployeeBaseInfo(map).then(res => {
          this.loading = false;
          if (res.result_code == '1') {
            this.$message.success('新增成功');
            this.cancel();
          }
        }).catch(() => {
          this.loading = false;
        });
      } else {
        map.isgId = this.param.isgId ? this.param.isgId : null;
        updateEmployeeBaseInfo(map).then(res => {
          this.loading = false;
          this.$message.success('编辑成功');
          this.cancel();
        }).catch(() => {
          this.loading = false;
        });
      }
    },
    // 保存时处理信息
    setProps (list, empId, account) {
      const sysTenantId = Vue.ls.get(TENANT_ID);
      list.forEach(item => {
        item.sysTenantId = sysTenantId;
        item.userAccount = account;
        item.empId = empId;
      });
      return list;
    },
    // 保存时处理图片信息
    getPhoto (picFile, empId, account) {
      const sysTenantId = Vue.ls.get(TENANT_ID);
      let photo = picFile.filter(item => (item.fileName));
      photo.forEach(item => {
        item.sysTenantId = sysTenantId;
        item.userAccount = account;
        item.empId = empId;
      });
      return photo;
    },
    // 新增、编辑不展示
    show (key) {
      if (this.param.type == '3') {
        return true;
      }
      return !this.editHide.includes(key);
    },
    // 设置不可编辑
    setDisabled (key) {
      if (this.param.type == '3') {
        return true;
      }
      if (this.param.type == '2' && (key == 'empId')) {
        return true;
      }
      return this.readOnly.includes(key);
    },
    // 全部角色
    initAllRoleList () {
      queryall().then((res) => {
        this.roleList = (res.success ? res.result : []);
      }).catch(() => {
        this.roleList = [];
      });
    },
    // 获取角色
    getRole (val) {
      this.empform.roleId = undefined;
      queryRoleByPosition({ position: val }).then((res) => {
        let roleId = (res.result_data && res.result_data.roleId ? res.result_data.roleId : undefined);
        this.defaultRole = roleId;
        if (roleId) {
          this.empform.roleId = [roleId];
        }
      }).catch(() => {
        this.defaultRole = undefined;
      });
    },
    roleChange (val) {
      if (this.defaultRole && !val.includes(this.defaultRole)) {
        this.$message.warning('默认角色不允许删除');
        this.empform.roleId = [this.defaultRole, ...val];
      }
    },
    // 关闭弹窗回调方法
    cancel () {
      this.$emit('cancel');
      this.reset();
    },
    reset () {
      this.defaultRole = undefined;
      this.active = 'education';
      // 清除tabs的数据
      this.$refs['education'].init();
      this.$refs['contacts'].init();
      this.$refs['contract'].init();
      this.$refs['card'].init();
      this.$refs['picFile'].init();
      this.$refs['certificate'].init();
      if (this.showHandle('************')) {
        this.$refs['pay'].init();
      }
      Object.assign(this.empform, this.$options.data().empform);
      delete this.empform.id;
      this.empform.tutorName = '';
      this.$refs['empform'].resetFields();
      this.$refs['empform'].clearValidate();
      this.$emit('change', false);
      this.$emit('closeMpdel');
      document.querySelectorAll('.is--visible').forEach(item => {
        item.style.cssText = 'z-index:1;';
      });
    },
    getAccount () {
      queryAccount().then((res) => {
        if (res.result_code == '1') {
          this.empform.empId = res.result_data;
        }
      }).catch(() => {
        this.empform.empId = '';
      });
    },
    changeProperty (filed, value) {
      if (filed != 'property' || this.param.type != 1) {

      } else {
        if (value == 6) {
          // 如果当前工号是由手机号码带出的 则不根据员工类型自动生成
          if (this.fromTel) {
            return;
          }
          this.rules.empId[0].required = false;
          this.$refs.empform.clearValidate(this.empform.empId);
          this.getAccount();
        } else {
          this.rules.empId[0].required = true;
          if (this.param.type == 1) {
            // this.empform.empId = "";
            this.getUserAccount('empName', true);
          }
        }
      }
    },
    filterOption (input, option) {
      return (
        option.componentOptions.children[0].text.toUpperCase().indexOf(input.toUpperCase()) >= 0
      );
    },
    // zuomanman add
    loadTree () {
      var that = this;
      that.treeData = [];
      that.backTreeData = [];
      // 0表示不显示关闭的，1表示显示
      queryDeptTree({ 'show': '1' }).then((res) => {
        let result = Array.isArray(res.result) ? res.result : [res.result];
        that.treeData = result;
        that.backTreeData = result;
      }).catch(() => {
      });
    },
    changeEvent (val, extra) {
      this.disabledRoles = !val;
      this.empform.drId = [];
      this.treeData = this.backTreeData.filter(item => {
        return val && extra.triggerNode.$options.propsData.dataRef.orgCode.indexOf(item.code) > -1;
      });
    }
  }
};
</script>

<style lang="less" scoped>
  :deep(.ant-form-item) {
    width: 100%;
    display: inline-flex;
  }

  :deep(.top-btn-group) {
    margin: 8px 0;

    :deep(.ant-btn) {
      margin-right: 10px;
    }
  }
  :deep(.margin-bottom) {
    margin-bottom:8px;
  }

  :deep(.vxe-cell--valid) {
    display: none !important;
  }
</style>
