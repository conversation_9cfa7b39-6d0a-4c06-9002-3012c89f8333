<template>
  <div class="psa-detail-modal">
    <a-spin :spinning="loading">
      <!-- 标题 -->
      <div class="dialog-header">
        <div class="header-title">
          <span class="psa-name">{{title}}</span>
          <span class="psa-status">正在运行</span>
        </div>
        <div class="header-close-icon">
          <a-icon @click="cancel" type="close" />
        </div>
      </div>
      <!-- 各种统计数据 -->
      <div class="top-data-group">
        <div class="top-item">
          <div class="top-data">
            <div class="icon icon-1"></div>
            <div class="right-data">
              <div class="data-value" style="color: #fff;" :title="elecValue">{{(elecValue ? elecValue : '--')}}</div>
              <div class="right-data-title">今日发电量/{{(elecUnit ? elecUnit : 'kWh')}}</div>
            </div>
          </div>
          <div class="bottom-data">
            <span>年发电量</span>
            <span style="float: right;">{{(sumValue ? sumValue : '--')}}{{(sumUnit ? sumUnit : '万kWh')}}</span>
          </div>
        </div>
        <div class="top-item">
          <div class="top-data">
            <div class="icon icon-2"></div>
            <div class="right-data">
              <div style="color: #fff;" class="data-value" :title="powValue">{{(powValue ? powValue : '--')}}</div>
              <div class="right-data-title">当前功率/{{(powUnit ? powUnit : 'kW')}}</div>

            </div>
          </div>
          <div class="bottom-data bottom-data-bg"></div>
        </div>
        <div class="station-pr">
          <!-- 上月PR -->
          <gauge ref="stationPr"/>
        </div>
        <div class="weather">
          <weather-model ref="cityWeather"/>
        </div>
      </div>
      <!-- 告警、计划 -->
      <div class="station-warn-plan">
        <div class="left-station-warn">
          <!-- 告警统计 -->
          <pie ref="stationWarn" type="2" :pasId="keyId"/>
          <!-- <double-bar ref="stationWarn"/> -->
        </div>
        <div class="right-plan-defect">
          <!-- 消缺统计 -->
          <div ref="lineBar" class="line-bar"></div>
          <!-- <double-line ref="planDefect" :type="0"/> -->
        </div>
      </div>
      <!-- 电站运行图 -->
      <div class="bottom-station-run">
        <double-line ref="stationRun" :type="1"/>
      </div>
    </a-spin>
  </div>
</template>
<script>
import gauge from './psa/gauge';
import pie from './pieModel.vue';
import doubleLine from './psa/lines';
import weatherModel from './psa/weather';
import echarts from '@/utils/enquireEchart';
import {
  getStationPr,
  getStationTodayElec,
  getStationWeatherInfo,
  getStationWarnInfo,
  getStationPlanDefectInfo
} from '@/api/screen/screen';
import { lineChart } from '@/api/monitor/runMonitor';
export default {
  components: {
    pie,
    gauge,
    doubleLine,
    weatherModel
  },
  data () {
    return {
      show: false,
      keyId: null,
      title: '',
      lonLat: {
        lon: '',
        lat: ''
      },
      elecUnit: 'kWh',
      elecValue: '--',
      sumUnit: '万kWh',
      sumValue: '--',
      powUnit: 'kWh',
      powValue: '--',
      loading: false,
      time: null
    };
  },
  methods: {
    initData (psa = null) {
      let self = this;
      if (psa) {
        self.lonLat = {
          lon: psa.lon,
          lat: psa.lat
        };
        self.keyId = psa.psId;
        self.title = psa.psName;
      }
      self.loading = true;
      self.time && clearInterval(self.time);
      self.time = null;
      let { keyId, lonLat } = this;
      let map = { 'psId': keyId };
      // 上月pr
      let stationPr = new Promise((resolve, reject) => {
        getStationPr(map).then(res => {
          resolve(res.result_code == '1' ? res.result_data : '');
        });
      });
        // 电站运行图
      let running = new Promise((resolve, reject) => {
        lineChart(map).then(res => {
          resolve(res.result_code == '1' ? res.result_data : '');
        });
      });
        // 发电量/功率
      let stationElec = new Promise((resolve, reject) => {
        getStationTodayElec(map).then(res => {
          resolve(res.result_code == '1' ? res.result_data : '');
        });
      });
        // 单站页面-天气数据
      let weather = new Promise((resolve, reject) => {
        getStationWeatherInfo(lonLat).then(res => {
          resolve(res.result_code == '1' ? res.result_data : '');
        });
      });
        // 单站页面-告警/故障
      let warn = new Promise((resolve, reject) => {
        getStationWarnInfo(map).then(res => {
          resolve(res.result_code == '1' ? res.result_data : '');
        });
      });
        // 单站页面-计划/消缺完成率
      let planDefect = new Promise((resolve, reject) => {
        getStationPlanDefectInfo(map).then(res => {
          resolve(res.result_code == '1' ? res.result_data : []);
        });
      });
      new Promise((resolve, reject) => {
        Promise.all([stationPr, running, stationElec, weather, warn, planDefect]).then(values => {
          resolve(values);
        }, reason => {
          reject(new Error('获取数据失败'));
        });
      }).then(values => {
        self.loading = false;
        self.$refs.stationPr.initGauge(values[0]);
        self.$refs.stationRun.initLines(values[1]);
        self.$refs.cityWeather.init(values[3]);
        const elec = self.updataValue(values[2].elec);
        self.elecValue = elec.value;
        self.elecUnit = elec.unit;
        const sum = self.updataValue(values[2].sumElec);
        self.sumValue = sum.value;
        self.sumUnit = sum.unit;
        const pow = self.updataValue(values[2].pow);
        self.powValue = pow.value;
        self.powUnit = pow.unit;
        self.$refs.stationWarn.init(echarts);
        self.$nextTick(() => {
          self.initLineBar(values[5]);
        });
      }).catch(() => {
        self.loading = false;
      });
      self.time = window.setInterval(function () {
        self.initData();
      }, 1000 * 60 * 15);
    },
    /*
        消缺统计
      */
    initLineBar (data) {
      if (!this.$refs.lineBar) { // dom 不存在时不画
        return;
      }
      // 不重复初始化
      let lineBar = echarts.getInstanceByDom(this.$refs.lineBar);
      if (!lineBar) {
        lineBar = echarts.init(this.$refs.lineBar);
        this.$once('hook:beforeDestroy', function () {
          echarts.dispose(lineBar);
          lineBar = null;
        });
      }
      let x_data = [];
      let line_data = [];
      let bar_data = [];
      if (Array.isArray(data)) {
        data.forEach(item => {
          x_data.push(item.month);
          line_data.push(item.defectEliminationRate);
          bar_data.push(item.defectsNumber);
        });
      }
      let option = {
        title: {
          text: '消缺统计',
          top: '0',
          left: '0',
          textStyle: {
            fontSize: 16,
            color: 'rgba(255, 255, 255, 0.8)'
          }
        },
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow',
            shadowStyle: {
              color: 'rgba(150,150,150,0.1)'
            }
          },
          backgroundColor: 'rgba(55, 71, 89, 0.9)',
          borderColor: '#69C7EB',
          borderWidth: '1',
          textStyle: {
            color: '#ffffff'
          }
        },
        grid: {
          x: 10,
          y: 60,
          x2: 0,
          y2: 10,
          containLabel: true
        },
        legend: {
          top: '0',
          right: '0',
          textStyle: {
            textAlign: 'center',
            fontSize: 14,
            color: 'rgba(255,255,255,0.7)'
          },
          data: ['消缺率', '缺陷数量']
        },
        xAxis: [
          {
            type: 'category',
            data: x_data,
            axisPointer: {
              type: 'shadow'
            },
            axisLine: {
              show: false,
              lineStyle: {
                color: 'rgba(255, 255, 255, 0.7)',
                fontSize: 12
              }
            },
            axisTick: false
          }
        ],
        yAxis: [
          {
            type: 'value',
            name: '%',
            splitLine: {
              show: true,
              lineStyle: {
                color: 'rgba(96, 130, 163, 0.19)'
              }
            },
            axisLine: {
              show: false,
              lineStyle: {
                color: 'rgba(255, 255, 255, 0.7)',
                fontSize: 12
              }
            },
            minInterval: 1,
            min: function (value) {
              if (!isFinite(value.min)) {
                return 0;
              }
            },
            max: function (value) {
              if (!isFinite(value.max)) {
                return 100;
              }
              if (value.max < 1) {
                return 1;
              }
            },
            axisTick: false
          },
          {
            type: 'value',
            show: false,
            yAxisIndex: 1,
            splitLine: {
              show: true,
              lineStyle: {
                color: 'rgba(96, 130, 163, 0.19)'
              }
            },
            axisLine: {
              show: false,
              lineStyle: {
                color: 'rgba(255, 255, 255, 0.7)',
                fontSize: 12
              }
            },
            minInterval: 1,
            min: function (value) {
              if (!isFinite(value.min)) {
                return 0;
              }
            },
            max: function (value) {
              if (!isFinite(value.max)) {
                return 100;
              }
              if (value.max < 1) {
                return 1;
              }
            },
            axisTick: false
          }
        ],
        dataZoom: [
          {
            type: 'inside',
            show: false
          }
        ],
        series: [
          {
            name: '消缺率',
            type: 'line',
            connectNulls: true,
            smooth: true,
            symbolSize: 9,
            symbol: 'circle',
            z: 9,
            lineStyle: {
              color: 'rgba(99, 206, 181, 1)'
            },
            areaStyle: {
              normal: {
                color: new echarts.graphic.LinearGradient(
                  0,
                  0,
                  0,
                  1,
                  [
                    {
                      offset: 0,
                      color: 'rgba(99, 206, 181, 0.25)'
                    },
                    {
                      offset: 1,
                      color: 'rgba(99, 206, 181, 0)'
                    }
                  ],
                  false
                )
              }
            },
            showSymbol: false,
            data: line_data
          }, {
            name: '缺陷数量',
            type: 'bar',
            barMaxWidth: 20,
            yAxisIndex: 1,
            connectNulls: true,
            smooth: true,
            symbolSize: 9,
            symbol: 'circle',
            itemStyle: {
              color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                { offset: 0, color: 'rgba(252, 174, 90, 1)' },
                { offset: 1, color: 'rgba(180, 71, 77, 0.9)' }
              ])
            },
            showSymbol: false,
            data: bar_data
          }
        ]
      };
      lineBar.setOption(option, true);
    },
    updataValue (data) {
      const obj = {
        value: isNaN(data.value) ? '--' : data.value,
        unit: data.unit
      };
      return obj;
    },
    // 关闭事件
    cancel () {
      this.elecUnit = 'kWh';
      this.elecValue = '--';
      this.sumUnit = '万kWh';
      this.sumValue = '--';
      this.powUnit = 'kWh';
      this.powValue = '--';
      this.weather = null;
      clearInterval(this.time);
      this.time = null;
      this.$emit('close');
    }
  },
  beforeDestroy () {
    clearInterval(this.time);
    this.time = null;
  }
};
</script>

<style lang="less" scoped>
  .psa-detail-modal {
   //background: #060D12;
    background: linear-gradient(180deg, #0D171F 0%, #1C242D 100%);
    //border: 2px solid rgba(136, 220, 255, 0.69);
    border: 1px solid #51637C;
    box-shadow: 5px 4px 9px 0px rgba(3, 18, 38, 0.5);
    border-radius: 10px;
    :deep(.ant-spin-nested-loading) {
      width: 100%;
      height: 100%;
    }
    :deep(.ant-spin-container) {
      width: 100%;
      height: 100%;
      padding: 2vh 1.25vw;
    }
  }
  .top-data-group{
    width: 100%;
    height: 12.5vh;
    display: flex;
    align-items: center;
    margin-bottom: 2.5vh;
  }
  .station-warn-plan{
    width: 100%;
    height: 28vh;
    margin-bottom: 2.5vh;
    display: flex;
    align-items: center;
  }
  .left-station-warn{
    width: 50%;
    height: 100%;
    padding-right: 1.2vw;
  }
  .right-plan-defect{
    width: 50%;
    height: 100%;
    padding-left: 1.2vw;
    .line-bar{
      width: 100%;
      height: 100%;
    }
  }
  .bottom-station-run{
    width: 100%;
    height: 29vh;
  }
  .dialog-header {
    display: flex;
    align-items: center;
    width: 100%;
    margin-bottom: 1.85vh;

    .header-title {
      width: calc(100% - 90px);
      display: flex;
      align-items: center;
    }

    .header-close-icon {
      width: 90px;
      text-align: right;
      font-size: 1.85vh;
      color: #FFFFFF;
    }

    .psa-name {
      font-size: 2.5vh;
      font-family: PingFangSC-Medium, PingFang SC;
      font-weight: 500;
      color: #FFFFFF;
    }

    .psa-status {
      margin-left: 20px;
      font-size: 1.3vh;
      font-family: PingFangSC-Medium, PingFang SC;
      font-weight: 500;
      color: #63CEB6;
      padding: 1px 5px;
      background: rgba(99, 207, 178, 0.13);
      border-radius: 10px;
      border: 1px solid #63CEB6;
    }
  }

  .top-item {
    width: 11.15vw;
    height: 100%;
    //background: rgba(44, 56, 70, 0.6);
    background: rgba(78, 121, 177, 0.1);
    border-radius: 4px;
    padding: 1.5vh 0.85vw;
    margin-right: 0.42vw;
    .top-data{
      height: calc(100% - 22px);
      display: flex;
      align-items: center;
    }
    .icon{
      width: 50px;
      height: 50px;
      margin-left: -6px;
    }
    .icon-1{
      background: url(../../../assets/images/screen/psa_icon1_new.png) no-repeat;
      //background-size: 110% 110%;
    }
    .icon-2{
      background: url(../../../assets/images/screen/psa_icon2_new.png) no-repeat;
     // background-size: 110% 110%;
    }
    .right-data{
      width: calc(100% - 45px);
      font-size: 1.375rem;
      font-family: Arial-Black, Arial;
      font-weight: 900;
      color: #F88F39;
      padding-left: 10px;
      line-height: 1.375rem;
    }
    .right-data-title{
      font-size: 14px;
      font-family: PingFangSC-Medium, PingFang SC;
      font-weight: 600;
      color: rgba(255, 255, 255, 0.7)
    }
    .bottom-data{
      width: 100%;
      height: 2vh;
      font-size: 0.75rem;
      font-family: PingFangSC-Medium, PingFang SC;
      font-weight: 500;
      color: rgba(255, 255, 255, 0.65);
      line-height: 2rem;
    }
    .bottom-data-bg{
      background: url(../../../assets/images/screen/psa_mw.png) no-repeat;
      background-size: 100% 100%;
    }
    .data-value{
      overflow: hidden;
      text-overflow: ellipsis;
    }
  }
  .station-pr{
    height: 100%;
    width: 8.3vw;
    //background: rgba(44, 56, 70, 0.6);
    background: rgba(78, 121, 177, 0.1);
    border-radius: 4px;
    margin-right: 0.42vw;
  }
  .weather{
    height: 100%;
    width: 13.75vw;
    //background: rgba(44, 56, 70, 0.6);
    background: rgba(78, 121, 177, 0.1);
    border-radius: 4px;
    padding: 1.5vh 0.85vw;
  }
</style>
