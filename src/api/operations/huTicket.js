import { axios } from '@/utils/erp-request';

// 查询列表
export function getTicketInfo (data) {
  return axios({
    url: '/hu/ticket/page',
    method: 'post',
    data
  });
}

// 统计状态数量
export function countTicketSts (data) {
  return axios({
    url: '/hu/ticket/count',
    method: 'post',
    data
  });
}

// 删除
export function deleteTicket (data) {
  return axios({
    url: '/hu/ticket/deleteErpPowerTicketRec',
    method: 'post',
    data
  });
}
// 获取附件信息
export function getTicketAnnex (data) {
  return axios({
    url: '/ticket/annexErpPowerTicketRec',
    method: 'post',
    data
  });
}

// 样票下载
export function downloadSpecimen (data) {
  return axios({
    url: '/ticket/downloadSpecimen',
    method: 'post',
    data
  });
}

// 详情
export function getTicketDetail (data) {
  return axios({
    url: '/hu/ticket/huDetail',
    method: 'post',
    data
  });
}

// 票面预览
export function specimenPreviewHu (data) {
  return axios({
    url: '/hu/ticket/specimen',
    method: 'post',
    data
  });
}
