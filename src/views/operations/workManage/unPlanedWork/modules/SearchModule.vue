<template>
  <a-row :gutter="24">
    <a-col :span="24">
      <div class="search-item">
        <span class="search-label">流程状态</span>
        <div>
          <a-checkbox @change="checkedAllClick" v-model="checkedAll">全部({{ countAll }})</a-checkbox>
          <a-checkbox-group v-model="queryParams.flowSts" @change="changeEvent">
            <a-checkbox value="1">{{ isOrder ?'未处理':'未提交'}}({{ getCount(1) }})</a-checkbox>
            <a-checkbox value="2">处理中({{ getCount(2) }})</a-checkbox>
            <a-checkbox value="3">{{ isOrder ? '验收中' : '已完成' }}({{ getCount(3) }})</a-checkbox>
            <a-checkbox value="4">{{ isOrder ? '已完成' : '已退回' }}({{ getCount(4) }})</a-checkbox>
            <template v-if="isOrder">
              <a-checkbox value="5">已作废({{ getCount(5) }})</a-checkbox>
            </template>
          </a-checkbox-group>
        </div>
      </div>
    </a-col>
    <a-col :xxl="12" :xl="16" :md="24">
      <role-tree-select @change="roleTreeChange" ref="unPlanRoleTree"></role-tree-select>
    </a-col>
    <a-col :xxl="6" :xl="8" :md="12">
      <div class="search-item">
        <span class="search-label">任务类型</span>
        <a-select
          v-model="queryParams.taskType"
          mode="multiple"
          :maxTagCount="1"
          placeholder="请选择"
          style="width: 100%; height: 32px"
          allowClear
          :filter-option="filterOption"
          option-filter-prop="children"
          @change="typeChange">
          <a-select-option v-for="item in taskList" :key="item.dataValue" :value="item.dataValue">
            {{item.dataLable}}
          </a-select-option>
        </a-select>
      </div>
    </a-col>
    <template v-if="toggleSearchStatus">
      <a-col :xxl="6" :xl="8" :md="12" v-show="isShowSub">
        <div class="search-item">
          <span class="search-label">任务子类</span>
          <a-select
            v-model="queryParams.workSubclass"
            placeholder="请选择"
            style="width: 100%"
            mode="multiple"
            allowClear
            :filter-option="filterOption"
            option-filter-prop="children"
            :maxTagCount="1">
            <a-select-option v-for="item in subDict" :key="item.dataValue" :value="item.dataValue">
              {{item.dataLable }}
            </a-select-option>
          </a-select>
        </div>
      </a-col>
      <a-col :xxl="6" :xl="8" :md="12">
        <div class="search-item">
          <span class="search-label">任务名称</span>
          <a-input v-model="queryParams.taskName" allowClear></a-input>
        </div>
      </a-col>

      <template v-if="isOrder">
        <a-col :xxl="6" :xl="8" :md="12">
          <div class="search-item">
            <span class="search-label">工单来源</span>
            <a-select v-model="queryParams.orderSource" allowClear>
              <a-select-option v-for="item in dictMap.two_order_source" :key="item.dataId" :value="item.dataValue">
                {{item.dataLable}}
              </a-select-option>
            </a-select>
          </div>
        </a-col>
        <a-col :xxl="6" :xl="8" :md="12">
          <div class="search-item">
            <span class="search-label">工作状态</span>
            <a-select v-model="queryParams.workStatus" allowClear>
              <a-select-option v-for="item in dictMap.ticket_work_condition" :key="item.dataId" :value="item.dataValue">
                {{item.dataLable}}
              </a-select-option>
            </a-select>
          </div>
        </a-col>
      </template>
      <a-col :xxl="6" :xl="8" :md="12">
        <div class="search-item">
          <span class="search-label">计划开始/发现</span>
          <a-range-picker
            size="default"
            v-model="planStartTime"
            style="width: 100%"
            format="YYYY-MM-DD"
            value-format="YYYY-MM-DD" />
        </div>
      </a-col>
      <a-col :xxl="6" :xl="8" :md="12">
        <div class="search-item">
          <span class="search-label">计划结束/预计消除</span>
          <a-range-picker
            size="default"
            v-model="planEndTime"
            style="width: 100%"
            format="YYYY-MM-DD"
            value-format="YYYY-MM-DD" />
        </div>
      </a-col>
      <template v-if="isOrder">
        <a-col :xxl="6" :xl="8" :md="12">
          <div class="search-item">
            <span class="search-label">派发/领取</span>
            <a-range-picker
              size="default"
              v-model="realStartTime"
              style="width: 100%"
              format="YYYY-MM-DD"
              value-format="YYYY-MM-DD" />
          </div>
        </a-col>
        <a-col :xxl="6" :xl="8" :md="12">
          <div class="search-item">
            <span class="search-label">执行提交</span>
            <a-range-picker
              size="default"
              v-model="realEndTime"
              style="width: 100%"
              format="YYYY-MM-DD"
              value-format="YYYY-MM-DD" />
          </div>
        </a-col>
        <a-col :xxl="6" :xl="8" :md="12">
          <div class="search-item">
            <span class="search-label"> 流程开始</span>
            <a-range-picker
              size="default"
              v-model="flowStartTime"
              style="width: 100%"
              format="YYYY-MM-DD"
              value-format="YYYY-MM-DD" />
          </div>
        </a-col>
        <a-col :xxl="6" :xl="8" :md="12">
          <div class="search-item">
            <span class="search-label">流程结束 </span>
            <a-range-picker
              size="default"
              v-model="flowEndTime"
              style="width: 100%"
              format="YYYY-MM-DD"
              value-format="YYYY-MM-DD" />
          </div>
        </a-col>
      </template>
      <a-col :xxl="6" :xl="8" :md="12">
        <div class="search-item">
          <span class="search-label">{{isOrder?'工单':'工作'}}编号</span>
          <a-input v-model="queryParams.workCode" allowClear></a-input>
        </div>
      </a-col>
    </template>
    <a-col :xxl="6" :xl="8" :md="8">
      <div class="search-item">
        <a-button label="重置" @click="resetChange">重置</a-button>
        <throttle-button label="查询" @click="pageChange" />
        <span class="com-color" @click="handleToggleSearch">
          {{ toggleSearchStatus ? '收起' : '展开' }}
          <a-icon :type="toggleSearchStatus ? 'up' : 'down'" />
        </span>
      </div>
    </a-col>
  </a-row>
</template>

<script>
import initDict from '@/mixins/initDict';
import moment from 'moment';
import { getSubDict } from './column';
export default {
  name: 'SearchModel',
  props: {
    options: {
      default: () => {
        return [];
      },
      type: Array
    },
    countList: {
      type: Object,
      default: () => {
        return {};
      }
    },
    isOrder: {
      type: Boolean,
      default: false
    }
  },
  mixins: [initDict],
  data () {
    return {
      queryParams: {
        taskType: [],
        treeId: '',
        taskName: '',
        planStartTimeBefore: '',
        planStartTimeAfter: '',
        flowSts: ['1', '2', '3', '4'],
        planEndTimeBefore: '',
        planEndTimeAfter: '',
        workCode: '',
        workSubclass: []
      },
      extraParams: {
        realStartTimeBefore: '',
        realStartTimeAfter: '',
        realEndTimeBefore: '',
        realEndTimeAfter: '',
        flowStartTimeBefore: '',
        flowStartTimeAfter: '',
        flowEndTimeBefore: '',
        flowEndTimeAfter: ''
      },
      isShowSub: false,
      subDict: [],
      checkedAll: true,
      isReset: false,
      toggleSearchStatus: false,
      planStartTime: [],
      planEndTime: [],
      realStartTime: [],
      flowStartTime: [],
      flowEndTime: [],
      realEndTime: [],
      deaultList: ['1', '2', '3', '4']
    };
  },
  updated () {},
  computed: {
    countAll () {
      let orderNum = this.isOrder ? this.getCount(5) : 0;
      return this.getCount(1) + this.getCount(2) + this.getCount(3) + this.getCount(4) + orderNum;
    },
    taskList () {
      return this.dictMap.pdca_task_type.slice(1);
    }
  },
  async created () {
    this.setFlowSts();
    await this.getDictMap(
      'pdca_task_type,pdca_budget,ticket_work_condition,pdca_overhaul_category,pdca_sub_task_type,two_order_source,two_fault_classify,pdca_sub_task_type_clean,pdca_sub_task_type_grass,pdca_sub_task_type_inspection'
    );
    this.dictMap.two_order_source = this.dictMap.two_order_source.slice(1);
    if (this.isOrder) {
      this.queryParams = Object.assign(this.queryParams, this.extraParams);
      this.$set(this.queryParams, 'workStatus', '');
      this.$set(this.queryParams, 'orderSource', '');
    }
  },
  mounted () {},
  methods: {
    moment,
    setFlowSts () {
      this.queryParams.flowSts = this.isOrder ? this.deaultList.concat(['5']) : this.deaultList;
    },
    checkedAllClick (e) {
      if (e.target.checked) {
        this.setFlowSts();
      } else {
        this.queryParams.flowSts = [];
      }
      this.$emit('click', this.queryParams);
    },
    changeEvent (val) {
      this.queryParams.flowSts = val;
      if (this.isOrder) {
        this.checkedAll = val.length == 5;
      } else {
        this.checkedAll = val.length == 4;
      }
      this.$emit('click', this.queryParams);
    },
    getCount (index) {
      return this.countList['taskStatus' + index] ? this.countList['taskStatus' + index] : 0;
    },
    isHasProperty (property) {
      return Object.keys(this.queryParams).indexOf(property) > -1;
    },
    pageChange () {
      this.dealTime('planStartTime');
      this.dealTime('planEndTime');
      this.dealTime('realStartTime');
      this.dealTime('realEndTime');
      this.dealTime('flowStartTime');
      this.dealTime('flowEndTime');
      this.$emit('click', this.queryParams);
    },
    dealTime (keyName) {
      if (this[keyName].length > 0) {
        this.queryParams[keyName + 'Before'] = this[keyName][0];
        this.queryParams[keyName + 'After'] = this[keyName][1];
      } else {
        this.queryParams[keyName + 'Before'] = '';
        this.queryParams[keyName + 'After'] = '';
      }
    },

    //    数据角色树
    roleTreeChange (deptCode, psaIds) {
      Object.assign(this.queryParams, { 'deptCode': deptCode, 'psaIds': psaIds });
      this.$emit('click', this.queryParams);
    },
    treeChange (val, node) {
      this.isReset = false;
      this.queryParams.treeId = node.id;
      this.$emit('click', this.queryParams);
    },
    handleToggleSearch () {
      this.toggleSearchStatus = !this.toggleSearchStatus;
      this.$emit('change', this.toggleSearchStatus);
    },
    resetChange () {
      for (let item in this.queryParams) {
        this.queryParams[item] = Array.isArray(this.queryParams[item]) ? [] : '';
        if (item == 'flowSts') {
          this.setFlowSts();
        }
      }
      this.subDict = [];
      this.planStartTime = this.planEndTime = this.realStartTime = this.realEndTime = this.flowEndTime = this.flowStartTime = [];
      this.$refs.unPlanRoleTree.reset();
    },
    filterOption (input, option) {
      return (
        option.componentOptions.children[0].text.toLowerCase().indexOf(input.toLowerCase()) >= 0
      );
    },
    //  任务类型change事件
    typeChange (value) {
      this.queryParams.workSubclass = [];
      if (value.length > 1) {
        this.subDict = [];
        this.isShowSub = false;
      } else {
        let subType = value[0];
        this.isShowSub = ['1', '2', '3', '5', '7', '8', '10'].includes(subType);
        this.subDict = getSubDict(subType, this.dictMap);
      }
      if (this.isOrder && this.toggleSearchStatus) this.$emit('change', this.toggleSearchStatus);
    }
  }
};
</script>
