<template>
  <a-spin :spinning="loading" size="large">
    <!-- <div class="iframe"> -->

    <iframe
      :id="id"
      :src="url"
      ref="testPage"
      frameborder="0"
      width="100%"
      scrolling="auto"
      :height="frameHeight"
      @load="sendBaseInfo()"
    ></iframe>

    <!-- </div> -->
  </a-spin>
</template>

<script>
// import Vue from 'vue'
import { ACCESS_TOKEN, USER_INFO, DEFAULT_COLOR, HAS_ALL_DATA_HEALTH } from '@/store/mutation-types';
import { mixin, mixinDevice } from '@/utils/mixin.js';
export default {
  name: 'IframePageContent',
  inject: ['closeCurrent'],
  props: {
    path: {
      type: String,
      default: ''
    }
  },
  data () {
    return {
      url: '',
      id: '',
      loading: true,
      iframeWin: {},
      frameHeight: '',
      count: 0
    };
  },
  mixins: [mixin, mixinDevice],
  created () {
    this.goUrl();
  },
  updated () {
    // this.sendBaseInfo()
  //  this.goUrl()
    // this.$nextTick(()=>{
    //   this.loading = false
    // })
  },
  beforeDestroy () {
    window.removeEventListener('resize', this.getFrameHeight);
  },
  deactivated () {
    window.removeEventListener('resize', this.getFrameHeight);
  },
  mounted () {
    let that = this;
    this.iframeWin = this.$refs.testPage.contentWindow;
    const { testPage } = this.$refs;
    if (testPage.attachEvent) {
      // IE
      testPage.attachEvent('onload', () => {
        that.stateChange();
      });
    } else {
      // 非IE
      testPage.onload = function () {
        that.stateChange();
      };
    }
    this.getFrameHeight();
    window.addEventListener('resize', this.getFrameHeight);
    window.addEventListener('message', function (event) {
      that.loading = false;
      var data = event.data;
      console.log(data);
      if (data) {
        switch (data.cmd) {
          case 'menu':
            // 处理业务逻辑
            // if (!that.path) {
            //   if (that.getTitle(data.params.data)) {
            //     that.$ls.set(that.getTitle(data.params.data), data.params.data)
            //     that.$router
            //       .push({
            //         // path: '/' + data.params.projectName + "/"+ that.getTitle(data.params.data, true)
            //         path: that.getTitle(data.params.data, true),
            //       })
            //       .catch((err) => {})
            //   }
            // }
            break;
          case 'logout':
            that.$store.dispatch('Logout').then(() => {
              Vue.ls.remove(ACCESS_TOKEN);
              window.location.reload();
            });
            break;
        }
      }
    });
  },
  watch: {
    $route (to, from) {
      if (!this.path) {
        if (this.count == 0) {
          this.loading = true;
        }
        this.count++;
        this.goUrl();
      }
    },
    primaryColor () {
      this.sendBaseInfo();
    },
    path () {
      this.loading = true;
      if (this.count == 0) {
        this.loading = true;
      }
      this.count++;
      this.$nextTick(() => {
        this.sendBaseInfo();
        this.goUrl();
      });
    }
  },
  computed: {
    multipage () {
      return this.$store.state.app.multipage;
    }
  },
  methods: {
    getFrameHeight () {
      let hegith = this.multipage ? 40 : 0;
      let cardHeight = this.$route.query.token ? window.innerHeight : window.innerHeight - 24 - hegith - 60;
      this.frameHeight = `${cardHeight}px`;
    },
    sendBaseInfo () {
      this.iframeWin.postMessage(
        {
          cmd: 'baseInfo',
          params: {
            token: Vue.ls.get(ACCESS_TOKEN),
            userInfo: Vue.ls.get(USER_INFO),
            device: this.$store.getters.device,
            color: this.primaryColor,
            hasAllData: Vue.ls.get(HAS_ALL_DATA_HEALTH),
            deviceId: localStorage.getItem('deviceId')
          }
        },
        '*'
      );
      // this.loading = false;
    },
    stateChange () {
      this.loading = false;
    },
    getTitle (title, flag) {
      let storageKey = '';
      JSON.parse(sessionStorage.getItem('menuList')).map((item) => {
        if (item.title == title) {
          storageKey = flag ? item.parent + '/' + item.path : 'route:title:' + item.path;
        }
      });
      return storageKey;
    },
    goUrl () {
      let url = '';
      let internalOrExternal = '';
      if (!this.path) {
        url = this.$route.meta.url;
        this.id = this.$route.path;
        internalOrExternal = this.$route.meta.internalOrExternal;
      } else {
        url = this.path.meta.url;
        this.id = this.path.path;
        internalOrExternal = this.path.meta.internalOrExternal;
      }
      if (url !== null && url !== undefined) {
        if (url.indexOf('isolareye.com') === -1) {
          this.url = url;
        } else if (
          internalOrExternal != undefined &&
          internalOrExternal == false &&
          url.indexOf('isolareye.com') > -1
        ) {
          this.url = url + '?token=' + Vue.ls.get(ACCESS_TOKEN) + '&primaryColor=' + Vue.ls.get(DEFAULT_COLOR);
        }
        if (this.count >= 1) {
          this.count = 0;
          this.loading = false;
        }
        /* update_begin author:wuxianquan date:20190908 for:判断打开方式，新窗口打开时this.$route.meta.internalOrExternal==true */
        if (internalOrExternal != undefined && internalOrExternal == true) {
          if (!this.path) {
            this.closeCurrent();
          } else {
            this.$emit('close');
          }
          // 外部url加入token
          // eslint-disable-next-line no-template-curly-in-string
          let tokenStr = '${token}';
          if (url.indexOf(tokenStr) != -1) {
            let token = Vue.ls.get(ACCESS_TOKEN);
            this.url = url.replace(tokenStr, token);
          }

          window.open(this.url ? this.url : url);
        }
        /* update_end author:wuxianquan date:20190908 for:判断打开方式，新窗口打开时this.$route.meta.internalOrExternal==true */
      }
    }
  }
};
</script>

<style>
.iframe {
  position: relative;
  overflow: hidden;
  padding-top: 43.2%;
  height: calc(100% - 64px);
}
.iframe-container {
  width: 100%;
  height: calc(100% - 64px);
  border: 0;
}
</style>
