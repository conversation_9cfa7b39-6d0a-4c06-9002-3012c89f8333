<template>
  <div class="rule-list">
    <div class="search-head">
      <span>值转换模式</span>
      <a-select v-model="searchData.convertType" placeholder="请选择" allowClear class="select-input">
        <a-select-option v-for="item in options" :key="item.secondTypeCode" :value="item.secondTypeCode">
          {{ item.secondName }}
        </a-select-option>
      </a-select>
      <a-button class="solar-eye-btn-primary" @click="pageChange">查询</a-button>
      <a-button class="solar-eye-btn-primary-cancel" @click="resetSearch">重置</a-button>
    </div>
    <div class="btn-group" v-if="type == '1'">
      <a-button class="solar-eye-btn-primary-cancel" @click="doDownload">下载参数测点库</a-button>
      <a-button class="solar-eye-btn-primary-cancel" @click="handleImport('导入参数列表', '/standardAccess/importParamMappingList', '1')">导入</a-button>
      <a-button class="solar-eye-btn-primary-cancel" @click="doExport">导出</a-button>
    </div>
    <vxe-table
      :data="data"
      ref="table"
      resizable
      show-overflow
      highlight-hover-row
      size="small"
      :height="tableHeight"
      :seq-config="{ seqMethod: seqMethod}"
      class="my-table"
      @checkbox-all="handleSelectionChange"
      @checkbox-change="handleSelectionChange"
      :tree-config="{rowField: 'id', parentField: 'pid'}"
      :row-class-name="rowClassName"
    >
      <vxe-table-column type="checkbox" :width="36"></vxe-table-column>
      <vxe-table-column :width="36" title="" tree-node></vxe-table-column>
      <vxe-table-column type="seq" :width="80" title="序号"></vxe-table-column>
      <vxe-table-column v-for="item in columns" show-overflow="title" :title="item.title"
       :field="item.field" :width="item.width">
        <template #header="{ column }" v-if="!item.disabled">
          {{ item.title }}<span class="red">*</span>
        </template>
        <template v-slot="{ row }">
          <span :class="{'disabled-text': item.disabled}">{{ row[item.field] }}</span>
        </template>
      </vxe-table-column>
      <vxe-table-column show-overflow="title" title="值转换模式" field="convertTypeName">
        <template v-slot="{ row }">
          <span v-if="row.convertType == '3'" class="link-text" @click="gotoMapping(row)"> 
            {{ row.convertTypeName }}
          </span>
          <span v-else> {{ row.convertTypeName }}</span>
        </template>
      </vxe-table-column>
      <vxe-table-column show-overflow="title" title="倍率" field="coefficient"/>
      <vxe-table-column show-overflow="title" title="偏移量" field="excursion"/>
      <template v-slot:empty>
        <span>查询无数据</span>
      </template>
    </vxe-table>
    <page-pagination
      :pageSize="searchData.pageSize"
      :current="searchData.currentPage"
      :total="total"
      @size-change="sizeChange"
    />
    <upload v-model="upload.open" :upload="upload" @fileUpload="fileUpload" :params="params" listType="text" />
    <mapping-drawer ref="mappingDrawer" parentId="abutment-config" :source="source"/>
  </div>
</template>
<script>
import { getParamList, exportParamList } from '@/api/model/abutmentConfig.js';
import { getSystemCodeList } from '@/api/health/healthapi.js';
import { downloadTemplates } from '@/api/isolarErp/config/templateupload';
import upload from '@/components/erp/upload/upload';
import MappingDrawer from '../../../dataCenter/modules/MappingDrawer.vue';
const baseUrl = process.env.VUE_APP_Health_BASE_URL;
// const baseUrl = 'http://**********:8088';
export default {
  components: {
    upload,
    MappingDrawer
  },
  props: {
    type: {
      type: String,
      default: '1'
    },
    sourceId: {
      type: String,
      default: ''
    },
    taskId: {
      type: String,
      default: ''
    },
    source: {
      type: String,
      default: ''
    }
  },
  created () {
    getSystemCodeList({ firstTypeCode: '0249' }).then(res => {
      this.options = res.result_data['0249'];
    })
  },
  data () {
    return {
      data: [],
      searchData: {
        currentPage: 1,
        pageSize: 10,
        sourceId: undefined,
        taskId: undefined,
        convertType: undefined
      },
      convertType: undefined,
      total: 0,
      tableHeight: 440,
      columns: [
        { title: '规则分类', field: 'groupInstanceName', disabled: true},
        { title: '参数标识', field: 'paramCode', disabled: true},
        { title: '参数描述 ', field: 'paramDesc', disabled: true},
        { title: '单位 ', field: 'paramUnit', disabled: true},
        { title: '对象', field: 'objName', disabled: false},
        { title: '逻辑表', field: 'domainName', disabled: false},
        { title: '参数/测点编码 ', field: 'point', disabled: false, width: 120},
        { title: '参数/测点名称 ', field: 'pointName', disabled: true, width: 120},
        { title: '数据类型 ', field: 'valueTypeName', disabled: true},
        { title: '单位 ', field: 'storageUnit', disabled: true}
      ],
      options: [],
      // 导入参数
      upload: {
        title: '',
        url: '',
        open: false
      },
      params: {},
      idList: []
    }
  },
  mounted () {
    this.getTableHeight();
    window.onresize = () => {
      this.getTableHeight();
    };
  },
  methods: {
    // 设置表格高度
    getTableHeight () {
      this.$nextTick(() => {
        this.tableHeight = document.body.offsetHeight - (this.type == '1' ? 412.5 : 364.5);
      });
    },
    // 查询列表
    pageChange(isInit) {
      this.searchData.currentPage = 1;
      this.getList(isInit);
    },
    // 获取列表数据
    getList(isInit) {
      this.searchData.taskId = this.taskId;
      this.searchData.sourceId = this.sourceId;
      this.$emit('setLoading', true);
      this.idList = [];
      if(isInit) {
        this.data = [];
        this.total = 0;
      }
      this.convertType = this.searchData.convertType;
      getParamList(this.searchData).then(res => {
        if(res.result_data.pageList && res.result_data.pageList.length > 0) {
          this.total = res.result_data.rowCount;
          res.result_data.pageList.forEach((item, index) => {
            item.pid = 0;
            item.id =  item.id || ('pid' + index);
            item.groupInstanceName = item.groupInstance;
            if(item.groupInstance && item.paramMappingList && item.paramMappingList.length > 0) {
              item.paramMappingList.forEach((ele, key) => {
                ele.pid = item.id;
                ele.groupInstanceName = '';
              });
              item.children = item.paramMappingList;
            } else {
              item.children = [];
            }
          })
          this.data = res.result_data.pageList;
        } else {
          this.data = [];
          this.total = 0;
        }
        this.$emit('setLoading', false);
      }).catch(() => { 
        this.$emit('setLoading', false); 
        this.data = [];
        this.total = 0;
      })
    },
    // 下载参数测点库
    doDownload() {
      this.$emit('setLoading', true);
      downloadTemplates({ templateName: ['对接配置-标准参数测点表'] }).then(res => {
        this.$emit('setLoading', false);
        this.$downloadFile(res.result_data[0]);
      }).catch(() => { this.$emit('setLoading', false);});
    },
    // 导出列表
    doExport() {
      this.$emit('setLoading', true);
      let params = {sourceId: this.sourceId, taskId: this.taskId, idList: this.idList, convertType: this.convertType}
      exportParamList(params).then(res => {
        this.$emit('setLoading', false);
        let obj = {
          fileName: res.result_data.fileName,
          fileBase64Code: res.result_data.strBase64
        }
        this.$downloadFile(obj);
      }).catch(() => { this.$emit('setLoading', false);});
    },
    // 导入参数列表
    handleImport (title, url) {
      this.upload = {
        title: title,
        url: baseUrl + url,
        open: true
      };
      this.params = {
        taskId: this.taskId,
        sourceId: this.sourceId
      }
    },
    // 导入回调函数
    fileUpload (res) {
      if (res == '操作成功') {
        this.$message.success('导入成功');
        this.pageChange();
      } else {
        if (res.msg) {
          this.$message.warning(res.msg);
        }
      }
    },
    // 跳转型号映射页面
    gotoMapping(row) {
      let data = {
        sourceId: this.sourceId,
        taskId: this.taskId,
        groupInstance: row.groupInstance,
        paramCode: row.paramCode,
        // deviceModel: row.deviceModel
      }
      this.$refs.mappingDrawer.initDrawer(data, '1')
    },
    // 重置
    resetSearch(isInit) {
      this.searchData.convertType = undefined;
      this.convertType = undefined;
      this.pageChange(isInit);
    },
    // 页数页码变化
    sizeChange (current, size) {
      this.searchData.pageSize = size;
      this.searchData.currentPage = current;
      this.getList();
    },
    // 勾选表格数据
    handleSelectionChange({ records }) {
      let arr = [];
      records.forEach(item => {
        if(!(item.children && item.children.length > 0)) {
          arr.push(item.id);
        }
      });
      this.idList = arr;
    },
    // 表格序号设置
    seqMethod ({ row, rowIndex }) {
      if (row.pid == 0) {
        return (this.searchData.currentPage - 1) * this.searchData.pageSize + rowIndex + 1;
      } else {
        return '';
      }
    },
    // 表格行样式设置
    rowClassName ({ row }) {
      if (row.pid) {
        return 'row-tree-children';
      } 
    }
  }
}
</script>
<style lang="less" scoped>
.table-icon {
  cursor: pointer;
}
.table-icon + .table-icon {
  margin-left: 24px;
}
.search-head {
  margin: 8px 0 20px;
  .select-input {
    margin-right: 24px;
    width: 280px;
  }
  span {
    margin-right: 16px;
  }
  button {
    margin-left: 16px;
  }
}
.btn-group {
  margin-bottom: 6px;
}
.disabled-text {
  color: #999999;
}
.link-text {
  color: #1366EC;
  cursor: pointer;
}
:deep(.vxe-table .vxe-cell) {
  white-space: normal;
}
.red {
  color: #D54941;
}
:root[data-theme='dark'] {
  .search-head {
    span {
      color: #fff;
    }
  }
}
</style>