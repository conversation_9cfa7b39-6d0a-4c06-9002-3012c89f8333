// 公共自定义样式
@import "./config.less";
@import "./text.less";
body.solar-eye-dark:not(.self-system) {
  background-color: var(--zw-primary-bg-color--default) !important;
  color: var(--zw-text-1-color--default) !important;
}
// 主页面公共样式
// 查询条件区域
.solar-eye-search-model {
  width: 100%;
  border-radius: 4px;

  &.no-radius {
    border-bottom-left-radius: 0;
    border-bottom-right-radius: 0;
  }

  .ant-tabs-bar {
    margin: 0;
  }

  .solar-eye-search-header {
    height: 64px;
    border-radius: 4px 4px 0 0
  }

  .search-title { // 实现类似a-card header 的效果
    font-size: 18px;
    font-weight: bold;
    line-height: 40px;
    margin-bottom: 12px;
    padding-left: 12px;
    box-shadow: 0px 1px 4px 0px rgba(0, 0, 0, 0.07);
    border-bottom: 1px solid var(--zw-divider-color--default);
  }

  .solar-eye-search-content {
    margin: 0 !important;
    padding: 0 12px 16px;
    background: var(--zw-card-bg-color--default);

    .solar-eye-search-content-tab {
      padding-top: 12px;
    }

    // 兼容查询条件是 form 表单的情况
    .ant-form-item {
      display: flex;
      margin: 16px 16px 0;
    }

    .ant-form-item-control-wrapper {
      flex: 1;
    }

    .com-color {
      margin-left: 8px;
      cursor: pointer;
    }
  }

  .ant-form-item {
    display: flex;
    margin: 16px 16px 0;
  }

  .ant-form-item-control-wrapper {
    flex: 1;
  }

  .search-title {
    border-bottom: 1px solid var(--zw-divider-color--default);
    font-size: 16px;
    font-weight: bold;
    line-height: 40px;
  }

  .search-item {
    margin: 24px 0 0 0;
    height: 32px;
    display: inline-flex;
    width: 100%;


    .search-label {
      width: auto;
      text-align: right;
      padding-right: 8px;
      align-items: center;
      white-space: nowrap;
    }

    .ant-select, .ant-input, .ant-cascader-picker, .ant-calendar-picker {
      width: 100%;
      overflow: hidden;
    }

    .com-project-select {
      width: 83.5% !important;
      @media screen and (min-width: 2000px) {
        width: 87% !important;
      }
      @media screen and (min-width: 3200px) {
        width: 87% !important;
      }
      @media screen and (max-width: 1366px) {
        width: 80% !important;
      }
      overflow: hidden;
    }

    .ant-btn + .ant-btn {
      margin-left: 8px;
    }
  }
}

// 间隔空白行
.solar-eye-gap {
  height: 16px;
  background: var(--zw-primary-bg-color--default)
}

// 查询内容区域
.solar-eye-main-content {
  border-radius: 4px;
  width: 100%;
  flex: 1;
  padding: 16px;
  background: var(--zw-card-bg-color--default);

  .operation {
    margin-bottom: 16px;

    .operation-btn {
      width: 100%;
      .flex-end();
    }
  }

  .operation-has-text {
    margin-bottom: 16px;
    flex-wrap: wrap;
    height: 32px;
    .flex-between();

    .operation-text {
      color: var(--zw-warning-color--normal);
      text-align: left;
    }

    .operation-btn {
      .flex-end();
    }
  }

  .operation-btn {
    text-align: right;
    margin-bottom: 16px;

    .ant-btn {
      margin-left: 10px;
      cursor: pointer;
    }

    .ant-btn[disabled] {
      cursor: not-allowed;
    }

    .ant-dropdown-link {
      color: var(--zw-text-2-color--default);
    }

    .svg-icon {
      color: var(--zw-text-3-color--default);
    }

    .ant-dropdown-link:hover {
      color: var(--zw-primary-color--default);

      .svg-icon {
        color: var(--zw-primary-color--default) !important;
      }
    }
  }

  .vxe-cell .svg-icon {
    cursor: pointer;
    margin-right: 6px;

    &:hover {
      color: var(--zw-conduct-color--hover);
    }
  }

  .ant-table-row a {
    color: var(--zw-conduct-color--normal);

    &:hover {
      color: var(--zw-conduct-color--hover);
    }
  }
}

.solar-eye-main-content.no-radius {
  border-top-left-radius: 0;
  border-top-right-radius: 0;
  padding-top: 0;

  .operation {
    padding-top: 16px;
    margin-bottom: 32px;
    border-top: 1px solid var(--zw-input-bg-color--disable);
  }
}


// 表格-状态字段
.table-statusCol {
  width: 6px;
  height: 6px;
  border-radius: 50%;
  margin-right: 8px;
}

// 表格固定操作列
.fixed-right-column-base() {
  float: right;
  display: inline-flex;
  align-items: center;
  justify-content: center;
}

.fixed-right-column {
  .fixed-right-column-base();
  width: 80px;
}

.fixed-right-column-120 {
  .fixed-right-column-base();
  width: 120px;
}

.fixed-right-column-160 {
  .fixed-right-column-base();
  width: 160px;
}

.fixed-right-column-180 {
  .fixed-right-column-base();
  width: 180px;
}

// 列表操作按钮悬停效果
.ant-btn.operation-btn-hover {
  .anticon {
    color: var(--zw-conduct-color--normal);
  }

  &:hover {
    color: var(--zw-primary-color--hover) !important;
  }
}

// 列表操作按钮悬停效果
.operation-btn-hover:hover {
  .anticon {
    color: var(--zw-primary-color--hover) !important;
  }
}

.gbutton-group {
  .ant-dropdown-trigger:hover,
  .ant-dropdown-trigger:hover svg,
  .ant-btn:hover svg,
  .gbtn-svg-icon:hover {
    color: var(--zw-primary-color--hover) !important;
  }

  .ant-btn,
  .ant-dropdown-trigger,
  .gbtn-svg-icon,
  .anticon {
    color: var(--zw-conduct-color--normal) !important;
  }
}

// 列表操作列的a标签颜色
.blue {
  color: var(--zw-conduct-color--normal);

  &:hover {
    color: var(--zw-conduct-color--hover);
  }
}

// 抽屉样式
.drawer-box {
  .ant-drawer-body {
    padding: 0;
    height: calc(100% - 55px) !important;
    overflow: auto;
  }

  .drawer-content {
    padding: 0px 24px;
  }

  .drawer-content-12 {
    padding: 12px 24px;
  }

  .drawer-foot {
    position: absolute;
    bottom: 0;
    width: 98%;
    text-align: center;
    padding: 10px 0;
    z-index: 999;

    button + button {
      margin-bottom: 0;
      margin-left: 16px;
    }
  }
}

.drawer-form-com {
  width: 100%;
  height: 100%;
  padding: 12px 12px 0;
}

.drawer-form-content {
  width: 100%;
  height: calc(100% - 55px);
  padding-bottom: 12px;
  overflow-y: auto;
  overflow-x: hidden;
  padding-right: 28px;
}

.drawer-form-foot {
  text-align: center;
  padding: 10px 0;

  button + button {
    margin-bottom: 0;
    margin-left: 16px;
  }
}

// 统一去除 form 表单label 后的：
.ant-form-item-label > label::after {
  content: '' !important;
}

// 抽屉标题后面状态
.title-statusCol {
  width: 52px !important;
  font-size: 12px;
  font-weight: normal;
  line-height: 20px;
  margin-left: 10px;
  padding: 0 8px;
  border: 1px solid;
  box-sizing: border-box;
  border-radius: 2px;
  opacity: 1;
}

// 弹窗内容 便签分类order-dispose或者drawer-content-title
.order-dispose {
  .title-box {
    font-size: 16px;
    font-weight: 500;
    color: var(--zw-text-1-color--default);
    padding-bottom: 24px;

    .before {
      width: 4px;
      height: 15px;
      border-left: 4px solid var(--zw-primary-color--default);
      margin-right: 16px;
      border-radius: 0px 2px 2px 0px;
    }
  }
}

// 弹窗内容分栏标题
.drawer-content-title {
  font-family: PingFangSC-Medium;
  font-size: 16px;
  margin-bottom: 16px;
  font-weight: normal;

  &::before {
    content: ' ';
    width: 4px;
    height: 21px;
    border-radius: 0px 2px 2px 0px;
    opacity: 1;
    background: var(--zw-primary-color--default);
    display: inline-block;
    margin-right: 16px;
    vertical-align: middle;
  }
}

// 弹窗右边流程图按钮
.flow-chart-btn {
  position: absolute;
  top: calc(50% - 40px);
  right: 4px;
  width: 24px;
  color: var(--zw-primary-color--default);
  text-align: center;
  cursor: pointer;
  border-radius: 4px;
  padding: 16px 0;
  height: 130px;
}

//zmm定义的表格，例如大屏模板
.vxe-noBorder.border--default {
  .vxe-body--column {
    border-bottom: 1px solid var(--zw-divider-color--default);
  }

  .vxe-header--column {
    .vxe-resizable {
      opacity: 0;
    }

    &:hover {
      .vxe-resizable {
        opacity: 1;
      }
    }
  }

  .table-operator-btn,
  .delete {
    margin-right: 16px;
    font-family: PingFangSC-Regular;
    font-size: 14px;
    font-weight: normal;
  }

  .delete {
    color: var(--zw-warning-color--normal);
  }

  .table-operator-btn {
    color: var(--zw-conduct-color--normal);
  }
}

// 自定义a-tooltip 显示宽度调整
.custom-tooltip-title-style {
  .ant-tooltip-content {
    width: 350px;
  }
}

.solar-eye-tooptip {
  max-width: 455px !important;
  max-height: 212px !important;
  overflow: auto;
}

// 上传组件 模板下载
.template-download {
  text-decoration: underline;
  vertical-align: bottom;
  padding-left: 16px;
}

.solar-eye-pure-bg {
  background: var(--zw-card-bg-color--default);
}

.red {
  color: var(--zw-warning-color--normal);
}

.com-color {
  color: var(--zw-text-1-color--default);
}

// a-button primary默认按钮 hover
.ant-btn-primary:hover, .ant-btn-primary:focus {
  color: var(--zw-common-white-color--default) !important;
  background: var(--zw-primary-color--hover) !important;
}

// throttle-button 默认按钮及hover
.solar-eye-btn-primary {
  &:not(.ant-btn-icon-only) {
    background: var(--zw-primary-partial-areas-color--hover) !important;
    border: 1px solid var(--zw-primary-color--default);
  }

  color: var(--zw-primary-color--default) !important;
  text-shadow: none !important;

  &:hover {
    color: var(--zw-common-white-color--default) !important;
    background: var(--zw-primary-color--default) !important;
  }

  &.ant-btn[disabled] {
    color: var(--zw-text-color--disable) !important;
    background-color: var(--zw-card-bg-color--default) !important;
    border-color: var(--zw-border-color--default);
  }
}

// throttle-button  二级暂存保存按钮及hover
.solar-eye-btn-primary-save.solar-eye-btn-primary {
  background: var(--zw-card-bg-color--default) !important;
  border: 1px solid var(--zw-primary-color--default) !important;
  color: var(--zw-primary-color--default) !important;

  &:hover {
    background: var(--zw-primary-partial-areas-color--hover) !important;
    border: 1px solid var(--zw-primary-color--default);
    color: var(--zw-primary-color--default) !important;
  }
}

// throttle-button  三级返回关闭按钮及hover
.solar-eye-btn-primary-cancel.solar-eye-btn-primary {
  background: var(--zw-card-bg-color--default) !important;
  border: 1px solid var(--zw-border-color--default) !important;
  color: var(--zw-text-1-color--default) !important;

  &:hover {
    background: var(--zw-card-bg-color--default) !important;
    border: 1px solid var(--zw-primary-color--default) !important;
    color: var(--zw-primary-color--default) !important;
  }
}
.solareye-color-primary{
  color: var(--zw-primary-color--default) !important;
}

.di-chart-tooltip {
  box-sizing: border-box;
  background: var(--zw-primary-bg-color--hover) !important;
  border-radius: 4px;
  padding: 0 !important;

  .title {
    background-color: var(--zw-table-header-bg-color--default);
    background-repeat: no-repeat;
    background-size: 100% 100%;
    height: 28px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--zw-text-1-color--default);
  }

  .content {
    padding: 8px 0;
    width: 100%;
    display: flex;
    justify-content: flex-start;
    flex-direction: column;

    .content-item {
      display: flex;
      justify-content: space-between;
      padding: 0 8px;
      align-items: center;
      flex-wrap: nowrap;

      &:not(:last-child) {
        margin-bottom: 4px;
      }

      .tooltip-item-label {
        color: var(--zw-text-2-color--default);
        margin-left: 4px;
      }
    }

    .content-unit {
      width: 40px;
      display: inline-block;
      color: var(--zw-text-3-color--default);
    }
  }
}
