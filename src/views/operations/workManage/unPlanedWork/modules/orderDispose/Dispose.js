export const base = [{
  label: '备注',
  key: 'conditionRemark',
  span: 24
},
{
  slot: 'stepList'
}
];

export const Diagnosis = [{
  label: '实际消除时间',
  key: 'completeTime'
},
{
  label: '现场情况',
  key: 'sceneConditionName',
  func: (params) => {
    return params.orderSource == 4;
  }
},
{
  label: '累计修复时间(h)',
  key: 'totalRepairTime',
  func: (params) => {
    return getStatus(params) || params.handleResult == '1';
  }
},
{
  label: '故障停运容量(MWp)',
  key: 'faultCap'
},
{
  label: '累计损失电量(万KWh)',
  key: 'totalLossPower',
  func: (params) => {
    return getStatus(params) || params.handleResult == '1';
  }
},
{
  label: '逾期原因',
  key: 'overdueReason'
},
{
  label: '备注',
  key: 'conditionRemark'
},
{
  slot: 'stepList'
}
];

export const inspection = [{
  label: '任务状态',
  slot: 'resolveResult'
},
{
  label: '执行时间',
  slot: 'actualTimeResolve'
},
{
  label: '运维经销商',
  slot: 'resolveResult'
},
{
  label: '任务状态',
  slot: 'resolveResult'
}
];

function getStatus (params) {
  let order = params;
  return (order.step > 5 && order.processDefKey == 'activiti4273Workflow') || (order.step > 5 && order.processDefKey == 'activiti4773Workflow') || (order.step > 2 && order.processDefKey ==
    'activiti4873Workflow') || (order.processDefKey == 'activiti5073Workflow' && order.step > 1) || order.flowSts == '4';
}
