import { axios } from '@/utils/request';

// 部门关联电站
export function queryDepartRelationPowerStationArchives (data) {
  return axios({
    url: '/sys/permission/queryDepartRelationPowerStationArchives',
    method: 'post',
    data
  });
}

// 部门未关联电站
export function queryDepartNoRelPowerStationArchives (data) {
  return axios({
    url: '/sys/permission/queryDepartNoRelPowerStationArchives',
    method: 'post',
    data
  });
}

// 保存部门关联电站
export function saveDepartRelPowerStationArchives (data) {
  return axios({
    url: '/sys/permission/saveDepartRelPowerStationArchives',
    method: 'post',
    data
  });
}

// 删除部门关联电站
export function delDepartRelPowerStationArchives (data) {
  return axios({
    url: '/sys/permission/delDepartRelPowerStationArchives',
    method: 'post',
    data
  });
}
// 查看部门可分配电站档案
export function assignablePsaList (data) {
  return axios({
    url: '/sys/psa/assignablePsaList',
    method: 'get',
    params: data
  });
}
export function assignedPsaList (data) {
  return axios({
    url: '/sys/psa/assignedPsaList',
    method: 'get',
    params: data
  });
}
