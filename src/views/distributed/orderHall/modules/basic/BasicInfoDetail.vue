<template>
  <div :class='show && "overflow-hidden"'>
    <detail-layout :labelList="baseList" :form="baseForm" title="基本信息" :toggleBtnShow='toggleBtnShow' @handleToggle='handleToggle'>
    </detail-layout>
    <detail-layout :labelList="labelList" :form="baseForm" title="" :dictMap='dictMap'>
      <template v-slot:planTime>
        <a-col :span="8" class="detail_layout_content" v-if="!['7','8'].includes(baseForm.taskType)">
          <span class="left">计划日期</span>
          <!--    组串标准清洗，系统维护，抢修，物资代采、保险购买、下网电费、站荣站貌计划日期到日      -->
          <!-- <span class="right" v-if="['13','4','15','16','17','14','18'].includes(baseForm.taskType)">
            {{ baseForm.planStartTime }} ~ {{ baseForm.planEndTime }}
          </span> -->
          <!--     其他任务类型到月     -->
          <!-- <span class="right" v-else>
            {{ moment(baseForm.planStartTime).format('YYYY-MM') }} ~ {{ moment(baseForm.planEndTime).format('YYYY-MM') }}
          </span> -->
           <span class="right" v-if="baseForm.taskType === '10'">
            {{ baseForm.findTime }} ~ {{ baseForm.predictRecoverTime }}
          </span>
          <span v-else>
              {{ baseForm.planStartTime }} ~ {{ baseForm.planEndTime }}
          </span>
        </a-col>
      </template>
      <!--   清洗，除草，组串清洗   -->
      <template  v-slot:stationEnvironment>
        <a-col :span="8" class="detail_layout_content" v-if="['1', '2','13',...cleanWeedTaskType].includes(baseForm.taskType)">
          <span class="left">电站环境</span>
          <span class="right">{{ stationEnvironmentName || '--' }}</span>
        </a-col>
      </template>
      <template v-slot:executeCycleRange>
        <a-col :span="8" class="detail_layout_content" v-if="['1',...cleanType].includes(baseForm.taskType) && baseForm.purchasesWay">
          <span class="left">执行周期</span>
          <span class="right">{{ baseForm.executeCycleStartDate }} ~ {{ baseForm.executeCycleEndDate }}</span>
        </a-col>
      </template>
    </detail-layout>
  </div>
</template>

<script>
import moment from 'moment';
import TaskAndOrder, { cleanTypeEnum, weedTypeEnum, testDetectTaskTypeEnum, cleanWeedTaskTypeEnum } from '@/mixins/TaskAndOrder';

export default {
  name: 'BasicInfoDetail',
  mixins: [TaskAndOrder],
  props: {
    baseForm: {
      type: Object,
      default: () => {
        return {};
      }
    },
    dictMap: {
      type: Object,
      default: () => {
        return {};
      }
    },
    showToggle: {
      type: Boolean,
      default: () => true
    }
  },
  data () {
    return {
      moment,
      baseList: [
        {
          label: '工单分类',
          key: 'taskCategoryName',
          span: 24
        },
        {
          label: '工单类型',
          key: 'taskTypeName'
        },
        {
          label: '工程遗留',
          key: 'isEngLegacyLabel',
          func: (params) => {
            return ['7', '8'].includes(params.taskType);
          }
        },
        {
          label: '工单来源',
          key: 'workOrderSourceLabel'
        },
        {
          label: '工单子类',
          key: 'workSubclassName',
          func: (params) => {
            return ['7'].includes(params.taskType);
          }
        }
      ],
      labelList: [
        {
          label: '项目名称',
          key: 'psaName',
          // 仪表校验、财产一切险、公众责任险、机器损坏险类 需要展示项目名称
          func: (params) => {
            return ['39','44','45','46'].includes(params.taskType);
          }
        },
        {
          label: '电站名称',
          key: 'psName',
          // 非 仪表校验、财产一切险、公众责任险、机器损坏险类 需要展示电站名称
          func: (params) => {
            return !['39','44','45','46'].includes(params.taskType);
          }
        },
        {
          label: '发现时间',
          key: 'findTime',
          func: (params) => {
            return ['7', '8'].includes(params.taskType);
          }
        },
        {
          label: '发现地点',
          key: 'findPlaceName',
          func: (params) => {
            return ['7', '8'].includes(params.taskType) && params.isEngLegacy == 1;
          }
        },
        {
          label: (params) => {
            return params.taskType == '7' ? '故障描述' : '缺陷描述';
          },
          labelFunc: true,
          key: 'taskDescription',
          func: (params) => {
            return ['7', '8'].includes(params.taskType);
          },
          span: '24'
        },
        {
          label: '整改要求',
          key: 'rectifyReq',
          span: 24,
          func: (params) => {
            return ['7', '8'].includes(params.taskType) && params.isEngLegacy === 1;
          }
        },
        {
          label: '发现人',
          key: 'findUserName',
          func: (params) => {
            return ['7', '8'].includes(params.taskType);
          }
        },
        {
          label: '预计消除时间',
          key: 'predictRecoverTime',
          func: (params) => {
            return ['7', '8'].includes(params.taskType);
          }
        },

        { slot: 'planTime' },
        { slot: 'stationEnvironment' },
        {
          label: (params) => {
            return (['1', ...Object.keys(cleanTypeEnum)].includes(params.taskType) ? '清洗' : '除草') + '容量(MW)';
          },
          labelFunc: true,
          key: 'cleanCap',
          func: (params) => {
            return ['1', '2', ...Object.keys(cleanWeedTaskTypeEnum)].includes(params.taskType);
          }
        },
        {
          label: '清洗预算(元)',
          key: 'orderBudget',
          func: (params) => {
            return ['1', ...Object.keys(cleanTypeEnum)].includes(params.taskType) && params.purchasesWay;
          }
        },
        {
          label: '清洗方式',
          key: 'cleanTypeName',
          func: (params) => {
            return ['1', ...Object.keys(cleanTypeEnum)].includes(params.taskType);
          }
        },
        {
          label: '除草方式',
          key: 'grassTypeName',
          func: (params) => {
            return ['2', ...Object.keys(weedTypeEnum)].includes(params.taskType);
          }
        },
        {
          label: '采购方式',
          key: 'purchasesWay',
          func: (params) => {
            return ['1', ...Object.keys(cleanTypeEnum)].includes(params.taskType) && params.purchasesWay;
          },
          dict: 'wash_purchases_way'
        },
        {
          slot: 'executeCycleRange'
        },
        {
          label: '上次试验时间',
          key: 'beforeExperimentDate',
          func: (params) => {
            return ['3', ...Object.keys(testDetectTaskTypeEnum)].includes(params.taskType);
          }
        },
        {
          label: '负责人',
          key: 'liablePersonName'
        },
        {
          label: '任务描述',
          key: 'taskDescription',
          span: '24',
          func: (params) => {
            return !['7', '8'].includes(params.taskType);
          }
        },
        {
          label: '履约类别',
          key: 'settlementTypeName',
          func: (params) => {
            return params.settlementType;
          }
        },
        {
          label: '委托单',
          key: 'uploadFileList',
          type: 'file:picture-card',
          func: (params) => {
            return params.settlementType === 'B';
          }
        },
        {
          labelFunc: true,
          label: (params) => (params.taskType === '7' ? '故障图片' : '缺陷图片'),
          key: 'uploadFileList',
          type: 'file:picture-card',
          span: '24',
          func: (params) => {
            return ['7', '8'].includes(params.taskType);
          }
        }
      ],
      show: false
    };
  },
  methods: {
    getLabels (arr, maps) {
      if (!Array.isArray(arr)) {
        return '--';
      }
      const mapsArr = maps.filter((item) => {
        return arr.includes(item.dataValue);
      });
      return mapsArr.map((item) => item.dataLable).join(',');
    },
    handleToggle () {
      this.show = !this.show;
    }
  },
  computed: {
    stationEnvironmentName () {
      if (!['1', '2', '13', ...this.cleanWeedTaskType].includes(this.baseForm.taskType)) {
        return null;
      }
      const stationEnvironment = this.baseForm.stationEnvironment;

      return this.getLabels(stationEnvironment, this.dictMap.psa_model);
    },
    toggleBtnShow () {
      const { purchasesWay, taskType } = this.baseForm;
      return purchasesWay && ['1', ...this.cleanType].includes(taskType) && this.showToggle;
    }
  }
};
</script>
<style scoped lang='less'>
.overflow-hidden{
  height: 32px;
  overflow: hidden;
}
</style>
