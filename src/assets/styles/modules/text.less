// 文字相关
@font-face {
  font-family: 'NeoGram-DemiBold';
  src: url('../../fonts/NeoGram-DemiBold.ttf');
  font-display: swap;
}

// 字号 (例 .font-14)
.for-font-size(@index) when (@index >=12) {
  .font-@{index} {
    font-size: unit(@index, px);
  }
  .for-font-size(@index - 1);
}

.for-font-size(36);

// 字号加行距，行距=字号+8 (例 .text-14)
.for-loop-text(@index) when (@index >=12) {
  .text-@{index} {
    font-size: unit(@index, px);
    line-height: unit(@index+8, px);
  }
  .for-loop-text(@index - 1);
}

.for-loop-text(36);

// 字重
.font-400 {
  font-weight: 400;
}

.font-500 {
  font-weight: 500;
}

.font-600 {
  font-weight: 600;
}

.font-bold {
  font-weight: bold;
}

.font-normal {
  font-weight: normal;
}

// 文本单行超出... 需要设置宽度
.text-ellipsis {
  text-overflow: ellipsis;
  overflow: hidden;
  white-space: nowrap; // 不换行 控制单行文本
}

// 文字不换行- 显示1行 - 溢出显示...
.text-line-clamp-1 {
  display: -webkit-box;
  overflow: hidden;
  word-break: break-all;
  text-overflow: ellipsis;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 1;
}

// 文字不换行- 显示2行 - 溢出显示...
.text-line-clamp-2 {
  display: -webkit-box;
  overflow: hidden;
  word-break: break-all;
  text-overflow: ellipsis;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
}

.text-decoration-underline {
  text-decoration: underline;
}

.font-10 {
  font-size: 12px;
  transform: scale(.8333333);
  transform-origin: center;
}