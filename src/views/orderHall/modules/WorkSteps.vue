<template>
  <div v-if="workStepsShow">
    <div v-if="['3', '8'].includes(type)">
      <WorkStepsDetail :baseForm="formData" />
    </div>
    <div v-else>
      <a-row :gutter="24">
        <a-col :span="24">
          <div class="order-dispose">
            <div class="title-box">
              <span class="before"></span>
              <span>作业步骤</span>
            </div>
          </div>
        </a-col>
      </a-row>

      <a-steps direction="vertical" class="step-box">
        <a-step :key="step.title" v-for="(step, index) in formData.stepVoList">
          <template slot="title">
            {{ step.stepName }}
            <throttle-button
              v-if="step.uploadExampleFileList && step.uploadExampleFileList.length > 0"
              @click="handlePreview(step.uploadExampleFileList)"
              label="示例图片"
              class="example-btn"
            ></throttle-button>
          </template>
          <template slot="description">
            <a-form-model-item
              label="情况描述"
              :prop="'stepVoList.' + index + '.workDesc'"
              :rules="{
                required: isRequired,
                message: '情况描述不能为空',
                trigger: 'blur',
              }"
              :wrapperCol="{ style: 'width: calc(50% - 150px)' }"
              v-if="step.stepContent && step.stepContent.indexOf('1') > -1"
            >
              <a-textarea
                :max-length="1000"
                :disabled="isDisabled"
                v-model="step.workDesc"
                :title="step.workDesc"
                placeholder="请输入"
                style="width: 100%"
              />
            </a-form-model-item>
            <a-form-model-item
              label="上传图片"
              :prop="'stepVoList.' + index + '.uploadPictureList'"
              :rules="[
                {
                  required: isRequired,
                  message: '请上传图片',
                  trigger: 'blur',
                },
                {
                  validator: validateFileList,
                  trigger: 'change',
                },
              ]"
              v-if="step.stepContent && step.stepContent.indexOf('2') > -1"
            >
              <uploadFileView
                v-model="step.uploadPictureList"
                listType="picture-card"
                :isAllImage="true"
                :disabled="isDisabled"
                :zoom="0.8"
                :multiple="true"
                accept=".jpg,.png,.jpeg,.bmp"
                tip="最多上传5张图片，单张图片不超10MB，支持格式jpg/jpeg/png/bmp！"
              />
            </a-form-model-item>
            <a-form-model-item
              label="上传附件"
              :prop="'stepVoList.' + index + '.uploadFileList'"
              :rules="{
                required: isRequired,
                message: '请上传附件',
                trigger: 'blur',
              }"
              v-if="step.stepContent && step.stepContent.indexOf('3') > -1"
            >
              <uploadFileView
                v-model="step.uploadFileList"
                tip="最多上传5个文件,且上传的附件最大不超过10MB!"
                :multiple="true"
                :disabled="isDisabled"
              />
            </a-form-model-item>
          </template>
        </a-step>
      </a-steps>
    </div>
  </div>
</template>

<script>
import WorkStepsDetail from './WorkStepsDetail';
import uploadFileView from '@/components/com/fileUploadView';
import VueViewer from '@/mixins/VueViewer';
export default {
  name: 'WorkSteps',
  mixins: [VueViewer],
  props: {
    type: {
      type: String,
      default: () => {
        return null;
      }
    },
    formData: {
      type: Object,
      default: () => {
        return {};
      }
    },
    isDisabled: {
      type: Boolean,
      default: false
    }
  },
  components: { WorkStepsDetail, uploadFileView },
  data () {
    let validateFileList = (rule, value, callback) => {
      let arr = [];
      if (value && value.length > 0) {
        value.forEach((item, index) => {
          if (arr.indexOf(item.fileName) == -1) {
            arr.push(item.fileName);
          }
        });
        if (arr.length < value.length) {
          callback(new Error('不允许上传相同得文件'));
        }
      }
      callback();
    };
    return {
      isRequired: true,
      validateFileList: validateFileList
    };
  },
  mounted () {
    console.log(this.formData);
  },
  methods: {
    // 示例图片预览
    handlePreview (fileList) {
      this.stopEvent();
      this.viewerImage({ images: fileList });
    },
    // 阻止冒泡方法
    stopEvent (e) {
      e = e || window.event;
      if (e.stopPropagation) {
        // W3C阻止冒泡方法
        e.stopPropagation();
      } else {
        e.cancelBubble = true; // IE阻止冒泡方法
      }
    }
  },
  computed: {
    workStepsShow () {
      const type = this.type;
      const formData = this.formData;
      if ((type === '3' || type === '8') && formData.stepVoList && formData.stepVoList.length) {
        return true;
      }
      if (['6', '5'].includes(type)) {
        return false;
      }
      if (type === '7') {
        // 无人机不显示作业步骤
        if (formData.workSubclass === '17' && formData.taskType === '3') {
          return false;
        }
        if (['3', '4', '5', '6'].includes(formData.otherSts)) {
          return true;
        }
      }

      return false;
    }
  }
};
</script>

<style lang="less" scoped>
:deep(.ant-steps-vertical .ant-steps-item-description) {
  padding: 24px 0 0;
}

:deep(.step-box) {
  :deep(.ant-form-item-label) {
    width: 134px !important;
  }
}

:deep(.last-form-item ){
  :deep(.ant-form-item-label) {
    width: 62px !important;
  }

  :deep(.ant-form-item-control-wrapper) {
    width: calc(100% - 62px) !important;
  }
}

:deep(.example-btn) {
  font-size: 12px !important;
  padding: 0 8px !important;
  height: 22px !important;
  margin-left: 16px;
}

</style>
