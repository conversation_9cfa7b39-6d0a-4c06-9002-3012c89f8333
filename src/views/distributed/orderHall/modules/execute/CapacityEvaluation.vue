<!-- 应发电量/执行信息 -->
<template>
  <div>
    <!-- 上网电量 -->
    <a-row :gutter="24">
      <a-col :xl="8" :sm="12" :xs="24">
        <a-form-model-item prop="businessData.actualOnElectric" :labelCol="labelCol" :wrapperCol="wrapperCol"
          :rules="{ required: true, message: '此项为必填项' }">
          <template #label>
            <span>上网电量(万kWh)</span>
            <a-popover>
              <template slot="content">
                <div style="max-width: 200px">
                  来源于日报填写数据，修正后会同步更新日报数据；
                </div>
              </template>
              <span class="m-l-4"><svg-icon class="info-icon" iconClass="health-info"></svg-icon></span>
            </a-popover>
          </template>
          <a-input-number v-model="businessData.actualOnElectric" placeholder="请输入" :disabled="isDisabled" :max="max"
            :min="-max" style="width: 100%" :precision="4" />
        </a-form-model-item>
      </a-col>
    </a-row>
    <!-- 故障损失电量 -->
    <a-row :gutter="24">
      <a-col :xl="8" :sm="12" :xs="24">
        <a-form-model-item prop="businessData.actualFaultLossElectric"
          :labelCol="labelCol" :wrapperCol="wrapperCol">
          <template #label>
            <span class="custom-fake-required">参考故障损失电量(万kWh)</span>
            <a-popover>
              <template slot="content">
                <div style="max-width: 200px">
                  来源于当日设备故障诊断结果，通过故障设备与相似发电设备之间的发电量对比计算出的损失和。
                </div>
              </template>
              <span class="m-l-4"><svg-icon class="info-icon" iconClass="health-info"></svg-icon></span>
            </a-popover>
          </template>
          <a-input disabled :value="getLabel(businessData.actualFaultLossElectric)" style="width: 100%" />
        </a-form-model-item>
      </a-col>
      <a-popover v-model="visible" title="损失电量明细" trigger="click" overlayClassName="generation-popover"
        :getPopupContainer="(node) => node.parentNode" v-if="!$isEmpty(actualFaultLossDetail())">
        <template slot="content">
          <div class="generation-popover-inner-content">
            <span>{{ actualFaultLossDetail() }}</span>
          </div>
          <div class="flex-end m-t-16">
            <throttle-button label="关闭" size="small" class="solar-eye-btn-primary-cancel" @click="visible = false" />
          </div>
        </template>
        <span class="generation-check-detail" style="display: inline-block;height: 40px;line-height: 40px;">
          查看损失原因分析
        </span>
      </a-popover>
    </a-row>
    <a-row :gutter="24">
      <a-col :xl="8" :sm="12" :xs="24">
        <a-form-model-item prop="businessData.insideFaultLossElectric" :rules="{ required: true, message: '此项为必填项' }"
          :labelCol="labelCol" :wrapperCol="wrapperCol">
          <template #label>
            <span>站内故障损失电量(万kWh)</span>
          </template>
          <a-input-number v-model="businessData.insideFaultLossElectric" :disabled="isDisabled"
            :placeholder="businessData.lossElectricDetail && businessData.lossElectricDetail.insideFaultLossElectric || 0"
            :max="max" :min="-max" style="width: 100%" :precision="4" />
        </a-form-model-item>
      </a-col>
      <a-col :xl="8" :sm="12" :xs="24" :push="1">
        <a-form-model-item prop="businessData.actualInsideFaultLossCutElectric"
          :rules="{ required: true, message: '此项为必填项' }" :labelCol="labelCol" :wrapperCol="wrapperCol">
          <template #label>
            <span>站内故障可核减电量(万kWh)</span>
          </template>
          <a-input-number v-model="businessData.actualInsideFaultLossCutElectric" :disabled="isDisabled"
            :placeholder="businessData.lossElectricDetail && businessData.lossElectricDetail.insideFaultLossCutElectric || 0"
            :max="max" :min="-max" style="width: 100%" :precision="4" />
        </a-form-model-item>
      </a-col>
    </a-row>
    <a-row :gutter="24">
      <a-col :span="17">
        <a-form-model-item label="站内故障损失原因" prop="businessData.faultLossReason"
          :rules="{ required: true, message: '此项为必填项' }" :labelCol="labelCol" :wrapperCol="wrapperCol">
          <zw-textarea :maxLength="500" v-model="businessData.faultLossReason" :disabled="isDisabled"
            :placeholder="placeholder" style="width: 100%" />
        </a-form-model-item>
      </a-col>
    </a-row>
    <!-- 限电损失电量 -->
    <a-row :gutter="24">
      <a-col :xl="8" :sm="12" :xs="24">
        <a-form-model-item prop="businessData.actualRationLossElectric"
          :labelCol="labelCol" :wrapperCol="wrapperCol">
          <template #label>
            <span class="custom-fake-required">参考限电损失电量(万kWh)</span>
            <a-popover>
              <template slot="content">
                <div style="max-width: 200px">
                  来源于当日限电诊断结果，通过全站等效发电小时数与标杆逆变器等效发电小时数之间的对比差值计算出的损失电量。
                </div>
              </template>
              <span class="m-l-4"><svg-icon class="info-icon" iconClass="health-info"></svg-icon></span>
            </a-popover>
          </template>
          <a-input disabled :value="getLabel(businessData.actualRationLossElectric)" style="width: 100%" />
        </a-form-model-item>
      </a-col>
    </a-row>
    <a-row :gutter="24">
      <a-col :xl="8" :sm="12" :xs="24">
        <a-form-model-item prop="businessData.rationLossElectric" :rules="{ required: true, message: '此项为必填项' }"
          :labelCol="labelCol" :wrapperCol="wrapperCol">
          <template #label>
            <span>限电损失电量(万kWh)</span>
          </template>
          <a-input-number v-model="businessData.rationLossElectric" :disabled="isDisabled" :max="max" :min="-max"
            :placeholder="businessData.lossElectricDetail && businessData.lossElectricDetail.rationLossElectric || 0"
            style="width: 100%" :precision="4" />
        </a-form-model-item>
      </a-col>
      <a-col :xl="8" :sm="12" :xs="24" :push="1">
        <a-form-model-item prop="businessData.actualRationLossCutElectric"
          :rules="{ required: true, message: '此项为必填项' }" :labelCol="labelCol" :wrapperCol="wrapperCol">
          <template #label>
            <span>限电损失可核减电量(万kWh)</span>
          </template>
          <a-input-number :disabled="isDisabled" v-model="businessData.actualRationLossCutElectric"
            :placeholder="businessData.lossElectricDetail && businessData.lossElectricDetail.rationLossCutElectric || 0"
            :max="max" :min="-max" style="width: 100%" :precision="4" />
        </a-form-model-item>
      </a-col>
    </a-row>
    <a-row :gutter="24">
      <a-col :span="17">
        <a-form-model-item label="限电损失原因" prop="businessData.rationLossReason"
          :rules="{ required: true, message: '此项为必填项' }" :labelCol="labelCol" :wrapperCol="wrapperCol">
          <zw-textarea :maxLength="500" v-model="businessData.rationLossReason" :disabled="isDisabled"
            :placeholder="placeholder" style="width: 100%" />
        </a-form-model-item>
      </a-col>
    </a-row>
    <!-- 阴影损失电量 -->
    <a-row :gutter="24">
      <a-col :xl="8" :sm="12" :xs="24">
        <a-form-model-item prop="businessData.actualShadowLossElectric"
          :labelCol="labelCol" :wrapperCol="wrapperCol">
          <template #label>
            <span class="custom-fake-required">参考阴影损失电量(万kWh)</span>
            <a-popover>
              <template slot="content">
                <div style="max-width: 200px">
                  来源于当日设备低效（非固定阴影遮挡）诊断结果，通过低效（非固定阴影遮挡）设备与相似发电设备之间的发电量对比计算出的损失和。
                </div>
              </template>
              <span class="m-l-4"><svg-icon class="info-icon" iconClass="health-info"></svg-icon></span>
            </a-popover>
          </template>
          <a-input disabled :value="getLabel(businessData.actualShadowLossElectric)" style="width: 100%" />
        </a-form-model-item>
      </a-col>
    </a-row>
    <a-row :gutter="24">
      <a-col :span="17">
        <a-form-model-item label="阴影损失原因" prop="businessData.shadowLossReason"
          :rules="{ required: true, message: '此项为必填项' }" :labelCol="labelCol" :wrapperCol="wrapperCol">
          <zw-textarea :maxLength="500" v-model="businessData.shadowLossReason" :disabled="isDisabled"
            :placeholder="placeholder" style="width: 100%" />
        </a-form-model-item>
      </a-col>
    </a-row>

    <!-- 其他损失电量 -->
    <a-row :gutter="24">
      <a-col :xl="8" :sm="12" :xs="24">
        <a-form-model-item prop="businessData.actualOtherLossElectric"
          :labelCol="labelCol" :wrapperCol="wrapperCol">
          <template #label>
            <span class="custom-fake-required">参考其他损失电量(万kWh)</span>
            <a-popover>
              <template slot="content">
                <div style="max-width: 200px">
                  总的损失电量（不含灰损）-故障损失电量-限电损失电量-阴影损失电量
                </div>
              </template>
              <span class="m-l-4"><svg-icon class="info-icon" iconClass="health-info"></svg-icon></span>
            </a-popover>
          </template>
          <a-input disabled :value="getLabel(businessData.actualOtherLossElectric)" style="width: 100%" />
        </a-form-model-item>
      </a-col>
    </a-row>
    <a-row :gutter="24">
      <a-col :span="17">
        <a-form-model-item label="其他损失原因" prop="businessData.otherLossReason"
          :rules="{ required: true, message: '此项为必填项' }" :labelCol="labelCol" :wrapperCol="wrapperCol">
          <zw-textarea :maxLength="500" v-model="businessData.otherLossReason" :disabled="isDisabled"
            :placeholder="placeholder" style="width: 100%" />
        </a-form-model-item>
      </a-col>
    </a-row>
  </div>
</template>

<script>

export default {
  name: 'CapacityEvaluation',
  inject: ['actualFaultLossDetail'],
  props: {
    businessData: {
      type: Object,
      default: () => ({ actualfaultLossDetail: '' })
    },
    isDisabled: Boolean
  },
  data () {
    return {
      visible: false,
      max: 999999.9999,
      placeholder: '请输入原因，没有填写“无”',
      labelCol: { style: 'width: 240px' },
      wrapperCol: { style: 'width: calc(100% - 240px)' }
    };
  },

  mounted () { },

  methods: {}
};
</script>

<style lang="less" scoped>
.ant-form-item-label-wrap:not(.ant-form-item-with-help) {
  height: 40px;
}

.ant-form-item-label-wrap {
  :deep(.ant-form-item-required) {
    line-height: 22px;
    display: inline-block;
    margin-right: 8px;
    display: flex;
    justify-content: flex-end;
    align-items: center;

    &::before {
      margin-bottom: 20px;
      margin-right: 0;
    }
  }
}

.generation-check-detail {
  cursor: pointer;
  color: #267DCF;
  margin-left: 16px;
  text-decoration: underline;
  text-underline-offset: 5px;
}

.generation-popover {
  .ant-popover-title {
    border-bottom: none;
    font-weight: 600;
  }

  .generation-popover-inner-content {
    max-width: 520px;
    max-height: 180px;
    overflow: auto;
  }
}
.custom-fake-required::before{
    display: inline-block;
    margin-right: 4px;
    color: #f5222d;
    font-size: 14px;
    font-family: SimSun, sans-serif;
    line-height: 1;
    content: '*';
}
</style>
