<template>
  <div class="logo">
    <!--  华能logo -->
    <template v-if="isCustom">
      <img style='margin-left: 30px' src="~@/assets/images/screen/custom_logo.png" alt="logo" />
    </template>
    <div class='logo-area' v-else id='logo-area' :style='{"zIndex":isShow? 99999 : 999 }'>
       <div class='menu-icon' v-show="isTrue()">
        <div class="menu-cursor cursor-pointer">
          <svg-icon class="operation-icon" id='logo-icon' iconClass="logo_expend" ></svg-icon>
          <new-change ref="drawer" class="text" :class="{show: isVisible}"></new-change>
        </div>
        <div class="icon-left"></div>
      </div>
      <template v-if="userInfo.userType==9 && userInfo.companyId==22">
        <img  style='margin-left: 30px; height: auto' src="~@/assets/images/screen/jd_logo_s.png" alt="logo" />
      </template>
      <template v-else>
        <img class="logo-img" src="~@/assets/logo_white.png" alt="logo"/>
        <div class='text-area'>
          <!-- <span class="logo-font-en">iSolarHealth</span> -->
          <svg-icon class="system-icon" iconClass="health-system"></svg-icon>
        </div>
      </template>
    </div>
  </div>
</template>

<script>
import { mixin } from '@/utils/mixin.js';
import NewChange from './NewChange';
import { USER_INFO } from '@/store/mutation-types';
const topList = ['/houseHold', '/smartAnalyze'];
const bottomList = ['/solarCare', '/integratedPlatform'];
export default {
  name: 'Logo',
  mixins: [mixin],
  components: { NewChange },
  data () {
    return {
      isShow: false,
      isVisible: false,
      userInfo: Vue.ls.get(USER_INFO)
    };
  },
  props: {
    title: {
      type: String,
      default: 'SolarEye',
      required: false
    },
    showTitle: {
      type: Boolean,
      default: true,
      required: false
    }
  },
  computed: {
    isCustom () {
      let userInfo = Vue.ls.get(USER_INFO);
      let arr = userInfo.roles.filter(item => {
        return item.roleCode == 'r10055';
      });
      return arr.length > 0;
    }
  },
  mounted () {
    if (!this.isCustom) {
      let logo_dom = document.querySelector('#logo-area');
      document.querySelector('body').appendChild(logo_dom);
    }
  },
  destroyed () {
    if (!this.isCustom) {
      let logo_dom = document.querySelector('#logo-area');
      document.querySelector('body').removeChild(logo_dom);
    }
  },
  methods: {
    /**
     * funcName isExist 判断是否存在
     * params {platform} String|| Array string，返回是否存在，array 返回数组
     */
    isExist (platform) {
      let menu = JSON.parse(sessionStorage.getItem('ALL_PERMISSION_MENU'));
      let isArray = Array.isArray(platform);
      return isArray ? platform.filter((item) => menu.indexOf(item) > -1) : !(platform ? menu.indexOf(platform) == -1 : JSON.parse(sessionStorage.getItem('hasErp')));
    },
    isTrue () {
      let isTrue = this.isExist(topList).length > 0 || this.isExist() || this.isExist(bottomList).length > 0;
      return isTrue;
    }
  }
};
</script>
<style lang="less" scoped>

  .fade-in{
    animation: .4s linear opacity-show;
    opacity: 1;
  }
  .fade-out{
    animation: .4s linear opacity-fade;
    opacity: 0;
  }

  @keyframes opacity-show {
    0%{
      opacity: 0;
      height: 10%;
    }
    50%{
      opacity: 0;
      height: 50%;
    }
    100%{
      opacity: 1;
      height: 100%;
    }
  }
  @keyframes opacity-fade {
    0%{
      opacity: 1;
    }
    50%{
      opacity: 0;
    }
    100%{
      opacity: 0;
    }
  }

  .opacity-0{
    opacity: 0;
  }
  .opacity-100{
    opacity: 1;
  }
  /*缩小首页布 局顶部的高度*/
  @height: 56px;
  .text-area{
    position: relative;
    width: 160px;
    height: 56px;
    .logo-font-en{
      font-family: 'NeoGram-DemiBold';
      font-size: 24px;
      color:white;
      position: absolute;
      left: 12px;
      top: 50%;
      transform: translateY(-50%);
    }
    .system-icon {
      margin-left: 12px;
      height: 50px;
      width: 143px;
    }
  }
  .icon-bg{
    width: 56px;
    height: 56px;
    background: linear-gradient(90deg, #A4622E 0%, #845F44 100%);
    position: absolute;
    left: 0;
    top: 0;
  }

  :root[data-theme='dark']{
    .icon-bg{
      background: linear-gradient(90deg, #234564 0%, #1C2A3C 100%);
    }
  }

  .logo-area{
    position: fixed;
    display: flex;
    align-items: center;
    justify-content: flex-start;
    left: 0;
    top: 0;
    .logo-img{
      width: 36px;
      height: 36px;
      margin-left: 24px;
    }

    .menu-icon{
      width: 72px;
      height: 56px;
      display: flex;
      align-items: center;
      justify-content: center;
      position: relative;
      .menu-cursor {
        width: 60%;
        height: 60%;
        border-radius:4px;
        margin-left:16px;
          &:hover {
            background: rgba(68,36,13,.25);
        .text {
          opacity:1;
          visibility: visible;
          animation: .4s linear opacity-show;
        }
      }
      .text {
        opacity:0;
        visibility: hidden;
         animation: .4s linear opacity-fade;
      }
      }
      .text.show {
        opacity:1;
        visibility: visible;
      }
      .operation-icon{
        width: 32px;
        height: 32px;
        position: absolute;
        left: 47%;
        top: 50%;
        transform: translate(-47%,-50%);
      }
    }
    .icon-left {
      height: 24px;
      align-self: center;
      width: 1px;
      background: rgba(255,255,255,.3);
      margin-left: 18px;
    }
  }
  .sider {
    box-shadow: none !important;
    .logo {
      height: @height !important;
      line-height: @height !important;
      box-shadow: none !important;
      transition: background 300ms;

      .anticon {
        color: white;
        &:hover {
          color: rgba(255, 255, 255, 0.8);
        }
      }
    }

    &.light .logo {
      background-color: @primary-color;
    }

  }
  :deep(.anticon) {
    color: white;
    padding-left: 8px;
  }
:root[data-theme='dark'] {
   .menu-icon{
     .menu-cursor {
          &:hover {
            background: rgba(142,197,255,.18);
      }
}
   }
 }
</style>
