<template>
  <a-tree-select allow-clear :tree-default-expand-all="true" :treeCheckStrictly="true" show-search :maxTagCount="1"
    style="width: 100%" :disabled="disabled" :dropdownStyle="{ maxHeight: '50vh', overflow: 'auto' }"
    placeholder="请选择项目" v-model="selectId" :treeExpandedKeys.sync="treeExpandedKeys" :tree-data="treeData" :allowClear="allowClear" :multiple="multiple"
    :replace-fields="{children:'children', title:'title', key:'key', value: 'value'}" treeNodeFilterProp="title"
    dropdownClassName="pro-tree-select-dropdown" @change="onChange" @click="handleVisible($event)" @search="search">
  </a-tree-select>
</template>
<script>
import { getErpPjPsaTreeByUserId, getAllPjPsaTree } from '@/api/isolarErp/com/funnel';
import { USER_INFO } from '@/store/mutation-types';
export default {
  name: 'proTreeSelect',
  model: {
    prop: 'value',
    event: 'change'
  },
  props: {
    value: {
      type: String,
      required: false
    },
    allowClear: {
      type: Boolean,
      default: true
    },
    disabled: {
      type: Boolean,
      default: false
    },
    // 是否支持多选,没有页面用到
    multiple: {
      type: Boolean,
      default: false
    },
    // 是否需要验证
    check: {
      type: Boolean,
      default: true
    },
    // 只显示项目并且是全部项目
    onlyAllProject: {
      type: Boolean,
      default: false
    },
    // 只能选中项目,如果选中其他则check
    onlySelectProject: {
      type: Boolean,
      default: false
    },
    // 是否有默认项  1：顶级节点 2：默认电站
    default: {
      type: [String, Number],
      default: null
    },
    // 是否显示全部电站档案
    showAllPsa: {
      type: Boolean,
      default: false
    }
  },
  watch: {
    load: {
      immediate: true,
      handler: function (n, o) {
        if (JSON.stringify(n) != JSON.stringify(o)) {
          let selectId = (n.value + '' ? n.value : '');
          this.selectId = (this.multiple ? selectId.split(',') : selectId);
          if (this.disabled && selectId) {
            this.loadNode(true);
            this.old = {};
          } else if (!this.disabled) {
            this.loadNode(false);
          }
        }
      }
    }
  },
  computed: {
    load () {
      return {
        value: this.value,
        disabled: this.disabled
      };
    }
  },
  data () {
    return {
      selectId: null,
      treeData: [],
      treeExpandedKeys: [],
      nodes: [],
      old: {}
    };
  },
  beforeDestroy () {
    this.treeData = null;
    this.nodes = null;
    this.$el.remove();
    this.$el = null;
    this.data = null;
  },
  methods: {
    handleVisible (el) {
      this.setTreeExpandedKeys();
      this.$nextTick(() => {
        let box = document.querySelector('.ant-select-tree li .ant-select-tree-node-content-wrapper.ant-select-tree-node-selected');
        let dropdown = document.querySelector('.pro-tree-select-dropdown');
        if (box && !dropdown.display) {
          box.scrollIntoView(false);
        }
      });
    },
    setTreeExpandedKeys () {
      let _this = this;
      let treeExpandedKeys = this.treeExpandedKeys = [];
      _this.$nextTick(() => {
        if (!this.selectId || (Array.isArray(this.selectId) && this.selectId.length == 0)) {
          return;
        }
        let keyId = (this.multiple ? [...this.selectId][0] : this.selectId);
        function find (selectId) {
          let node = _this.nodes.find(item => item.projectId == selectId);
          if (node) {
            treeExpandedKeys.push(node.key);
            if (node.pId) {
              find(node.pId);
            }
          }
        }
        find(keyId);
        this.treeExpandedKeys = treeExpandedKeys.reverse();
      });
    },
    // 项目树
    async loadNode (flag) {
      const user = Vue.ls.get(USER_INFO);
      let params = {
        'userId': (user && user.id ? user.id : undefined),
        'onlyAllProject': (this.onlyAllProject ? 1 : 0)
      };
      if (flag) {
        params = {
          'initValue': this.value,
          'disabled': this.disabled,
          ...params
        };
      } else {
        /*
            可编辑时请求数据，根据条件判断是否需要重新加载树
          */
        if (JSON.stringify(params) == JSON.stringify(this.old)) {
          return;
        }
        Object.assign(this.old, params);
      }
      this.treeData = this.nodes = [];
      try {
        let res = {};
        if (this.showAllPsa) res = await getAllPjPsaTree(params);
        else res = await getErpPjPsaTreeByUserId(params);
        if (res.result_code == '1') {
          let items = [];
          // 只能选择项目时，剔除电站
          if (this.onlySelectProject) {
            items = this.getAllProject(res.result_data);
          } else if (this.check) {
            items = this.getAllPsa(res.result_data);
          } else {
            items = res.result_data;
          }
          items.forEach((item, index) => {
            item.value = item.projectId.toString();
            item.title = item.name;
            item.key = 'key'.concat(index.toString());
          });
          this.nodes = items;
          this.treeData = this.getNodes(items);
        } else {
          this.treeData = [];
        }
        if (this.treeData.length == 0) {
          return;
        }
        if (this.default == '1') { // 默认顶级节点
          this.selectId = this.treeData[0].value;
          this.$emit('change', this.treeData[0].value);
        } else if (this.default == '2') { // 默认一个电站
          this.dealDataProviced(res.result_data);
          // let node = res.result_data.find(itm=>(itm.isPjPsa == '0' && item.psaStatus != '3'));/* psaStatus == '3' 表示已关闭  */
          // this.$emit('change', node.value);
        }
      } catch (e) {
        this.treeData = [];
      }
    },
    dealDataProviced (arr) { // 展开所有，会默认选中一个节点，发送选中节点的信息
      let self = this;
      for (var i = 0; i < arr.length; i++) {
        if (arr[i].children && arr[i].children.length > 0) {
          this.dealDataProviced(arr[i].children);
        } else {
          self.$emit('change', arr[i].value, arr[i].title, arr[i]);
          self.selectId = arr[i].value;
          return false;
        }
        return false;
      }
    },
    // 树形数据转换
    getNodes (data) {
      let result = [];
      if (!Array.isArray(data)) {
        return result;
      }
      //  空对象
      let map = {};
      data.forEach(item => {
        map[item.projectId] = item;
      });
      data.forEach(item => {
        let parent = map[item.pId];
        if (parent) {
          (parent.children || (parent.children = [])).push(item);
        } else {
          result.push(item);
        }
      });
      return result;
    },
    // 设置可选、不可选
    getAllPsa (items) {
      if (items instanceof Array) {
        items.forEach(item => {
          item.disabled = (item.isPjPsa == '1' || item.psaStatus == '3'); /* psaStatus == '3' 表示已关闭  */
        });
        return items;
      }
      return [];
    },
    // 不显示电站
    getAllProject (items) {
      if (items instanceof Array) {
        items.forEach(item => {
          item.disabled = (item.nodeType != '4' || item.psaStatus == '3'); /* psaStatus == '3' 表示已关闭  */
        });
        return items;
      }
      return [];
    },
    setDisabled (value) {
      value = (value == null ? '' : value.toString());
      // 项目id前5位固定是66666，由此判断是否是电站档案
      const isProject = '66666';
      if (value.length > 5) {
        const str = value.substring(0, 5);
        if (str == isProject) {
          return true;
        } else {
          return false;
        }
      } else {
        return false;
      }
    },
    onChange (value, label, extra) {
      if (!value) {
        this.$emit('change', '');
        this.selectId = (this.multiple ? [] : '');
      } else if (value instanceof Array) {
        this.$emit('change', value.join(','));
        this.selectId = value;
      } else {
        value = value.toString();
        this.selectId = (this.multiple ? [value] : value);
        if (this.check) {
          const falg = this.setDisabled(value);
          if (falg) {
            this.$message.warning('所选节点不是电站档案，请重新选择');
            this.$nextTick(() => {
              this.$emit('change', '');
              this.selectId = '';
            });
          } else {
            this.$emit('change', value.toString());
          }
        } else if (this.onlySelectProject) {
          if (extra.triggerNode.dataRef.nodeType != 4) {
            this.$message.warning('所选节点不是项目，请重新选择');
            this.$nextTick(() => {
              this.$emit('change', this.value ? this.value + '' : undefined);
              this.selectId = this.value ? this.value + '' : undefined;
            });
          } else {
            this.$emit('change', value.toString());
          }
        } else {
          this.$emit('change', value.toString());
        }
      }
    },
    search (val) {
      this.throttle(this.setExpandedKeys(), 500);
    },
    /*
        搜索展开节点
      */
    setExpandedKeys () {
      this.treeExpandedKeys = this.nodes.map(item => { return item.key; });
    },
    /*
        节流throttle
      */
    throttle (func, delay) {
      let timer = null;
      let startTime = Date.now();
      return function () {
        let curTime = Date.now();
        let remaining = delay - (curTime - startTime);
        let context = this;
        let args = arguments;
        clearTimeout(timer);
        if (remaining <= 0) {
          func.apply(context, args);
          startTime = Date.now();
        } else {
          timer = setTimeout(func, remaining);
        }
      };
    }
  }
};
</script>
<style lang="less" scoped>
  :deep(.ant-select) {
    :deep(.ant-select-selection__choice__content) {
      max-width: 90px !important;
    }
  }
</style>
