export const headEnumList = [
  { flowSts: '1,2,3,4,5', flowStsLabel: '全部', count: 0, icon: 'all' },
  { flowSts: '1', flowStsLabel: '待指派', count: 0, icon: 'waitBegin' },
  { flowSts: '2', flowStsLabel: '处理中', count: 0, icon: 'processing' },
  { flowSts: '3', flowStsLabel: '验收中', count: 0, icon: 'check' },
  { flowSts: '4', flowStsLabel: '已完成', count: 0, icon: 'finish' },
  { flowSts: '5', flowStsLabel: '已终止', count: 0, icon: 'stop' }
];

export const taskTypeEnumList = [
  { taskType: '', taskTypeLabel: '全部', count: 0 },
  { taskType: '8', taskTypeLabel: '缺陷', count: 0 },
  { taskType: '7', taskTypeLabel: '故障', count: 0 },
  { taskType: '1', taskTypeLabel: '清洗', count: 0 },
  { taskType: '2', taskTypeLabel: '除草', count: 0 },
  { taskType: '3', taskTypeLabel: '试验/检测', count: 0 },
  { taskType: '4', taskTypeLabel: '系统维护', count: 0 },
  { taskType: '9', taskTypeLabel: '技改', count: 0 },
  { taskType: '11', taskTypeLabel: '陪停', count: 0 },
  { taskType: '12', taskTypeLabel: '其他事项', count: 0 }
];

export const columnFn = () => {
  return [
    {
      name: 'workCode',
      title: '工单编号',
      width: 140,
      sortable: true
    },
    { name: 'areaName', title: '区域', width: 140, sortable: true },
    { name: 'depName', title: '运维组', width: 160, sortable: true },
    { name: 'psaName', title: '电站', width: 180, sortable: true },
    { name: 'taskType', title: '任务类型', width: 120, sortable: true },
    {
      name: 'workSubclass',
      title: '任务子类',
      width: 120,
      sortable: true,
      render: (row) => {
        const workSubclass = row.workSubclass;
        return workSubclass == ' ' || !workSubclass ? '--' : workSubclass;
      } },
    { name: 'flowStsName', title: '工单状态', width: 120, sortable: true },
    { name: 'orderSourceName', title: '工单来源', width: 120, sortable: true },
    { name: 'taskDescription', title: '任务描述/缺陷描述', width: 160, sortable: true },
    { name: 'liablePerson', title: '负责人', width: 120, sortable: true },
    { name: 'planStartTime', title: '计划开始/发现', width: 150, sortable: true },
    { name: 'planEndTime', title: '计划结束/预计消除', width: 150, sortable: true },
    { name: 'overHours', title: '超时时长(h)', width: 120, sortable: true },
    { name: 'planCode', title: '关联任务', sortable: true },
    { name: 'flowUser', title: '当前环节', width: 100, sortable: true },
    { name: 'issuedTime', title: '下发时间', sortable: true },
    { name: 'realStartTime', title: '派发/领取时间', sortable: true },
    { name: 'impCommitTime', title: '执行提交时间', sortable: true },
    { name: 'flowEndTime', title: '闭环时间', sortable: true },
    { name: 'createUser', title: '创建人', sortable: true }
  ];
};
export const checkedColumn = columnFn().map((item) => item.name);
