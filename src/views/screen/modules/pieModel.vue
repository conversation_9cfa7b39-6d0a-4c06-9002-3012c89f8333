<template>
  <!-- 电站运行情况/ -->
  <div class="solar-eye-screen-bar">
    <div class="solar-eye-screen-title" v-show="title">{{title}}</div>
    <div class="pie-cycle-model" ref="cyclePie"></div>
  </div>
</template>
<script>
import { getStationStatus, dataStatistical } from '@/api/screen/screen';
export default {
  props: {
    type: {
      type: String,
      default: ''
    },
    pasId: {
      type: String,
      default: ''
    },
    title: {
      type: String,
      default: ''
    }
  },
  data () {
    return {
      interval: null
    };
  },
  methods: {
    init (echarts) {
      let self = this;
      if (self.type == '1') {
        self.initStationStatus(echarts);
      } else {
        if (!self.pasId) {
          self.initPie([], []);
          return;
        }
        let map = {
          'alarmType': '1',
          'alarmStatus': '01',
          'size': 10,
          'curPage': 1,
          'treePsId': self.pasId
        };
        dataStatistical(map).then(res => {
          let legend = ['待处理', '已派发', '已闭环'];
          let status = [];
          if (Array.isArray(res.result_data)) {
            let data = res.result_data;
            let sum = data.map(item => { return (item.alarmStatus == 'isolation' ? 0 : Number(item.total)); }).reduce((prev, curr, idx, arr) => { return prev + curr; });
            data.forEach(item => {
              item.rate = (sum == 0 ? 0 : (item.total * 100 / sum).toFixed(2));
            });
            let obj = data.reduce((pre, cur) => {
              pre[cur.alarmStatus] = cur.rate;
              return pre; // 第一轮return的值，为第二轮的初始值
            }, {});
            status = [obj.untreated, obj.distributed, obj.closed];
          }
          self.initPie(echarts, legend, status);
        }).catch(() => {
          self.initPie([], []);
        });
      }
    },
    initStationStatus (echarts) {
      let self = this;
      getStationStatus({}).then(res => {
        let data = (res.result_code == '1' ? res.result_data : '');
        let legend = (data.hasOwnProperty('x') ? data.x : []);
        let status = (data.data.value ? data.data.value : []);
        self.initPie(echarts, legend, status, false);
      });
    },
    initPie (echarts, legend, data, show = true) {
      if (!this.$refs.cyclePie) { // dom 不存在时不画
        return;
      }
      // 不重复初始化
      let pieCharts = echarts.getInstanceByDom(this.$refs.cyclePie);
      if (!pieCharts) {
        pieCharts = echarts.init(this.$refs.cyclePie);
        this.$once('hook:beforeDestroy', function () {
          echarts.dispose(pieCharts);
        });
      }
      const color = ['rgba(113, 218, 161, 1)', 'rgba(165, 178, 255, 1)', 'rgba(253, 162, 106, 1)', 'rgba(113, 131, 147, 1)'];
      const start = ['rgba(157, 218, 113, 1)', 'rgba(108, 193, 255, 1)', 'rgba(253, 107, 107, 1)', 'rgba(113, 131, 147, 1)'];
      let items = [];
      legend.forEach((item, index) => {
        items.push({
          name: item,
          value: (isNaN(data[index]) ? 0 : data[index]),
          itemStyle: {
            normal: {
              color: new echarts.graphic.LinearGradient(0, 1, 0, 0, [{
                offset: 0,
                color: start[index] // 0% 处的颜色
              }, {
                offset: 1,
                color: color[index] // 100% 处的颜色
              }], false)
            }
          }
        });
      });
      let option = {
        color: color,
        title: {
          text: '故障统计',
          'show': show,
          top: '0',
          left: '0',
          textStyle: {
            fontSize: 16,
            color: 'rgba(255, 255, 255, 0.8)'
          }
        },
        tooltip: {
          show: true,
          trigger: 'item',
          backgroundColor: 'rgba(55, 71, 89, 0.9)',
          borderColor: '#69C7EB',
          borderWidth: '1',
          textStyle: {
            color: '#ffffff'
          },
          formatter: '{b}: {c}%'
        },
        legend: {
          orient: 'vertical',
          icon: 'circle',
          x: '70%',
          y: 'center',
          align: 'left',
          textStyle: {
            fontSize: 12,
            color: '#FFFFFF'
          }
        },
        series: [{
          type: 'pie',
          radius: ['42%', '65%'],
          center: ['28%', '57%'],
          data: items,
          hoverAnimation: true,
          labelLine: {
            normal: {
              show: false
            }
          },
          label: {
            normal: {
              show: false
            }
          }
        }]
      };
      pieCharts.setOption(option, true);
      var currentIndex = -1;
      if (!this.interval) {
        this.interval = setInterval(function () {
          var len = legend.length;
          // 取消之前高亮的图形
          pieCharts.dispatchAction({
            type: 'downplay',
            seriesIndex: 0,
            dataIndex: currentIndex
          });
          currentIndex = (currentIndex + 1) % len;
          // 高亮当前图形
          pieCharts.dispatchAction({
            type: 'highlight',
            seriesIndex: 0,
            dataIndex: currentIndex
          });
          // 显示 tooltip
          pieCharts.dispatchAction({
            type: 'showTip',
            seriesIndex: 0,
            dataIndex: currentIndex
          });
        }, 1000 * 2);
      }
    }
  },
  beforeDestroy () {
    if (this.interval) {
      window.clearInterval(this.interval);
      this.interval = null;
    }
  }
};
</script>

<style lang="less" scoped>
  .pie-cycle-model {
    width: 100%;
    height: 85%;
  }
</style>
<style>
  .pie-cycle-model div div{
    display: none !important;
  }
</style>
