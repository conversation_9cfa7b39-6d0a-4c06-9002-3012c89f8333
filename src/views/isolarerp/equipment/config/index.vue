<template>
  <a-row class="maker_and_model" id="makerAndModel">
    <a-spin :spinning="loading">
      <!-- 左边厂家维护 -->
      <a-col :xxl="12" :xl="24" :md="24" style="padding-right: 12px">
        <div class="solar-eye-search-model">
          <a-row :gutter="24" class="solar-eye-search-content">
              <div class="search-title">
                厂家
              </div>
            <a-col :xxl="8" :xl="8" :md="12">
              <a-input-search
                v-model="makerIn"
                @blur="makerIn = $trim($event)"
                @search="onSearchMaker"
                placeholder="厂家名称"
              />
            </a-col>
          </a-row>
        </div>
        <div class="solar-eye-gap"></div>
        <a-col class="solar-eye-main-content">
          <!-- 操作按钮 -->
          <div class="operation" style="height: 32px">
            <div class="operation-btn">
              <throttle-button label="新增" :loading="loading" @click="addRow('1')" />
            </div>
          </div>
          <vxe-table
            ref="makerTabel"
            :data="makerTabel"
            :edit-rules="makerRules"
            :seq-config="{ startIndex: (current0 - 1) * size0 }"
            resizable
            border
            align="center"
            :height="tableHeight - 24"
            @current-change="rowClick"
            highlight-current-row
            highlight-hover-row
            size="small"
          >
            <vxe-table-column
              type="seq"
              title="序号"
              show-overflow="title"
              :formatter="tabFormatter"
              width="60"
            ></vxe-table-column>
            <vxe-table-column field="maker" title="厂家名称" show-overflow="title" min-width="120">
              <template v-slot:default="{ row }">
                <a-input v-if="row.edit" v-model="row.maker" :maxLength="50" />
                <span v-else>{{ getLabel(row.maker, null) }}</span>
              </template>
            </vxe-table-column>
            <vxe-table-column
              field="createTime"
              title="创建时间"
              show-overflow="title"
              :formatter="tabFormatter"
              min-width="120"
            ></vxe-table-column>
            <vxe-table-column
              field="updateTime"
              title="修改时间"
              show-overflow="title"
              :formatter="tabFormatter"
              min-width="120"
            ></vxe-table-column>
            <vxe-table-column title="操作" fixed="right" width="140" align="center">
              <template v-slot:default="{ row, rowIndex }">
                <template v-if="row.edit">
                  <throttle-button title="保存" icon="save" @click="saveRow('1', row)" />
                  <throttle-button title="取消" icon="reload" @click="cancelRow('1', rowIndex)" />
                </template>
                <template v-else>
                  <throttle-button title="编辑" icon="edit" @click="editRow('1', rowIndex)" />
                  <throttle-button title="删除" icon="delete" @click="deleteRow('1', row.id)" />
                </template>
              </template>
            </vxe-table-column>
          </vxe-table>
          <!--分页组件-->
          <page-pagination :pageSize="size0" :current="current0" :total="total0" @size-change="makerPageEvent" />
        </a-col>
      </a-col>
      <!-- 右边型号维护 -->
      <a-col :xxl="12" :xl="24" :md="24">
        <div class="solar-eye-search-model">
          <a-row :gutter="24" class="solar-eye-search-content">
            <div class="search-title">
              型号
            </div>
            <a-col :xxl="8" :xl="8" :md="12">
              <a-input-search
                v-model="modalIn"
                @blur="modalIn = $trim($event)"
                @search="onSearchModal"
                placeholder="型号名称"
              />
            </a-col>
          </a-row>
        </div>
        <div class="solar-eye-gap"></div>
        <a-col class="solar-eye-main-content">
          <!-- 操作按钮 -->
          <div class="operation" style="height: 32px">
            <div class="operation-btn">
              <throttle-button label="新增" :loading="loading" @click="addRow('2')" />
            </div>
          </div>
          <vxe-table
            ref="modelTabel"
            :data="modelTabel"
            :edit-rules="modelRules"
            :seq-config="{ startIndex: (current1 - 1) * size1 }"
            resizable
            border
            align="center"
            :height="tableHeight - 24"
            highlight-current-row
            highlight-hover-row
          >
            <vxe-table-column
              type="seq"
              title="序号"
              show-overflow="title"
              :formatter="tabFormatter"
              width="60"
            ></vxe-table-column>
            <vxe-table-column
              field="maker"
              title="厂家名称"
              :formatter="tabFormatter"
              show-overflow="title"
              min-width="120"
            ></vxe-table-column>
            <vxe-table-column field="deviceModel" title="型号名称" show-overflow="title" min-width="120">
              <template v-slot:default="{ row }">
                <a-input v-if="row.edit" v-model="row.deviceModel" :maxLength="50" />
                <span v-else>{{ getLabel(row.deviceModel, null) }}</span>
              </template>
            </vxe-table-column>
            <vxe-table-column
              field="createTime"
              title="创建时间"
              show-overflow="title"
              :formatter="tabFormatter"
              min-width="120"
            ></vxe-table-column>
            <vxe-table-column
              field="updateTime"
              title="修改时间"
              show-overflow="title"
              :formatter="tabFormatter"
              min-width="120"
            ></vxe-table-column>
            <vxe-table-column title="操作" fixed="right" width="140" align="center">
              <template v-slot:default="{ row, rowIndex }">
                <template v-if="row.edit">
                  <throttle-button title="保存" icon="save" @click="saveRow('2', row)" />
                  <throttle-button title="取消" icon="reload" @click="cancelRow('2', rowIndex)" />
                </template>
                <template v-else>
                  <throttle-button title="编辑" icon="edit" @click="editRow('2', rowIndex)" />
                  <throttle-button title="参数" icon="tool" @click="addParameter(row)" />
                  <throttle-button title="删除" icon="delete" @click="deleteRow('2', row.id)" />
                </template>
              </template>
            </vxe-table-column>
          </vxe-table>
          <!--分页组件-->
          <page-pagination :pageSize="size1" :current="current1" :total="total1" @size-change="modelPageEvent" />
        </a-col>
      </a-col>
    </a-spin>
    <drawer-view ref="makerAndModelForm" @cancel="queryModel" parentId="makerAndModel" />
  </a-row>
</template>
<script>
import {
  deleteMaker,
  insertMaker,
  selectMaker,
  updateMaker,
  deleteModel,
  insertModel,
  selectModel,
  updateModel
} from '@/api/isolarErp/equipment/config';
import throttleButton from '@/components/com/throttle-button';
import tableHeight from '@/mixins/tableHeightAndSearchModel';
export default {
  components: {
    throttleButton
  },
  mixins: [tableHeight],
  name: 'index',
  data () {
    return {
      loading: false,
      maxHeight: 457,
      makerIn: '',
      size0: 10,
      current0: 1,
      total0: 0,
      editMakerRow: null,
      makerTabel: [],
      makerRules: {
        maker: [{ required: true, message: '请填写厂家名称' }]
      },
      makerId: undefined,
      selectMakerName: '',
      modalIn: '',
      size1: 10,
      current1: 1,
      total1: 0,
      editModelRow: null,
      modelRules: {
        deviceModel: [{ required: true, message: '请填写型号' }]
      },
      modelTabel: []
    };
  },
  computed: {
    navTheme () {
      return this.$store.state.app.theme;
    }
  },
  // 表格高度计算
  updated () {},
  created () {
    this.queryMaker();
  },
  methods: {
    // 查询-厂家
    queryMaker () {
      this.loading = true;
      this.makerTabel = [];
      let map = {
        maker: this.makerIn,
        curPage: this.current0,
        size: this.size0
      };
      selectMaker(map)
        .then((res) => {
          this.loading = false;
          if (res.result_code == '1') {
            this.makerTabel = res.result_data.rows;
            this.total0 = res.result_data.total;
          } else {
            this.makerTabel = [];
            this.total0 = 0;
            this.$notification.error({
              message: '系统提示',
              description: res.result_msg ? res.result_msg : '查询失败',
              duration: 2.5
            });
          }
          this.modelTabel = [];
          this.makerId = undefined;
          this.total1 = 0;
        })
        .catch((e) => {
          this.loading = false;
          this.makerTabel = [];
          this.total0 = 0;
          this.modelTabel = [];
          this.makerId = undefined;
          this.total1 = 0;
        });
    },
    // 查询-型号
    queryModel () {
      if (!this.makerId) {
        this.$message.warning('请先选择厂家');
        return;
      }
      this.loading = true;
      this.modelTabel = [];
      let map = {
        makerId: this.makerId,
        deviceModel: this.modalIn,
        curPage: this.current1,
        size: this.size1
      };
      selectModel(map)
        .then((res) => {
          this.loading = false;
          if (res.result_code == '1') {
            this.modelTabel = res.result_data.rows;
            this.total1 = res.result_data.total;
          } else {
            this.modelTabel = [];
            this.total1 = 0;
            this.$notification.error({
              message: '系统提示',
              description: res.result_msg ? res.result_msg : '查询失败',
              duration: 2.5
            });
          }
        })
        .catch((e) => {
          this.loading = false;
          this.modelTabel = [];
          this.total1 = 0;
        });
    },
    // 添加参数
    addParameter (row) {
      this.$refs.makerAndModelForm.init('2', row, '/isolarerp/equipment/config/form');
    },
    // 搜索事件-厂家
    onSearchMaker () {
      this.modalIn = '';
      this.current0 = 1;
      this.queryMaker();
    },
    // 搜索事件-型号
    onSearchModal () {
      this.current1 = 1;
      this.queryModel();
    },
    // 表格行点击事件
    rowClick ({ newValue, oldValue, row, rowIndex, $rowIndex, column, columnIndex, $columnIndex, $event }) {
      if (row.id) {
        this.makerId = JSON.parse(JSON.stringify(row.id));
        this.selectMakerName = JSON.parse(JSON.stringify(row.maker));
        this.onSearchModal();
      }
    },
    /*
      验证是否已有编辑中的数据
      type: 1、厂家  2、型号
      */
    isEdit (type) {
      let items = [];
      let itemss = [];
      items = this.makerTabel.filter((item) => item.edit);
      itemss = this.modelTabel.filter((item) => item.edit);
      return items.length > 0 || itemss.length > 0;
    },
    /*
      厂家、型号新增
      type: 1、厂家  2、型号
      */
    addRow (type) {
      let edit = this.isEdit();
      if (edit) {
        this.$message.warning('请先保存当前编辑中的数据');
        return;
      }
      if (type == '1') {
        this.editMakerRow = null;
        let obj = {
          makerName: '',
          createTime: null,
          updateTime: null,
          edit: true
        };
        this.makerTabel.unshift(obj);
        this.$nextTick(() => {
          this.$refs.makerTabel.reloadData(this.makerTabel);
        });
      } else if (type == '2') {
        if (!this.makerId) {
          this.$message.warning('请先选择厂家');
          return;
        }
        this.editModelRow = null;
        let row = {
          makerId: this.makerId,
          maker: this.selectMakerName,
          deviceModel: '',
          createTime: null,
          updateTime: null,
          edit: true
        };
        this.modelTabel.unshift(row);
        this.$nextTick(() => {
          this.$refs.modelTabel.reloadData(this.modelTabel);
        });
      }
    },
    /*
      厂家、型号保存
      type: 1、厂家  2、型号
      index: 行号
      */
    async saveRow (type, row) {
      this.loading = true;
      if (type == '1') {
        const errMap = await this.$refs['makerTabel'].validate(row).catch((errMap) => errMap);
        if (errMap) {
          this.loading = false;
          return;
        }
        if (row.id) {
          updateMaker({ id: row.id, maker: row.maker })
            .then((res) => {
              if (res.result_code == '1') {
                this.queryMaker();
              } else {
                this.loading = false;
                this.$notification.error({
                  message: '系统提示',
                  description: res.result_msg ? res.result_msg : '操作失败',
                  duration: 2.5
                });
              }
            })
            .catch((e) => {
              this.loading = false;
            });
        } else {
          insertMaker({ maker: row.maker })
            .then((res) => {
              if (res.result_code == '1') {
                this.queryMaker();
              } else {
                this.loading = false;
                this.$notification.error({
                  message: '系统提示',
                  description: res.result_msg ? res.result_msg : '操作失败',
                  duration: 2.5
                });
              }
            })
            .catch((e) => {
              this.loading = false;
            });
        }
      } else if (type == '2') {
        const errMap = await this.$refs['modelTabel'].validate(row).catch((errMap) => errMap);
        if (errMap) {
          this.loading = false;
          return;
        }
        if (row.id) {
          let map = {
            id: row.id,
            makerId: row.makerId,
            deviceModel: row.deviceModel
          };
          updateModel(map)
            .then((res) => {
              if (res.result_code == '1') {
                this.queryModel();
              } else {
                this.loading = false;
                this.$notification.error({
                  message: '系统提示',
                  description: res.result_msg ? res.result_msg : '操作失败',
                  duration: 2.5
                });
              }
            })
            .catch((e) => {
              this.loading = false;
            });
        } else {
          let map = {
            makerId: this.makerId,
            deviceModel: row.deviceModel
          };
          insertModel(map)
            .then((res) => {
              if (res.result_code == '1') {
                this.queryModel();
              } else {
                this.loading = false;
                this.$notification.error({
                  message: '系统提示',
                  description: res.result_msg ? res.result_msg : '操作失败',
                  duration: 2.5
                });
              }
            })
            .catch((e) => {
              this.loading = false;
            });
        }
      }
    },
    /*
      厂家、型号取消
      type: 1、厂家  2、型号
      */
    cancelRow (type, index) {
      let self = this;
      self.$confirm({
        title: '确定取消保存当前编辑的数据吗?',
        okText: '确定',
        cancelText: '取消',
        onOk () {
          if (type == '1') {
            if (self.editMakerRow) {
              self.makerTabel[index] = self.editMakerRow;
              self.$nextTick(() => {
                self.$refs.makerTabel.reloadData(self.makerTabel);
              });
            } else {
              self.makerTabel.splice(0, 1);
            }
            self.makerId = undefined;
          } else if (type == '2') {
            if (self.editModelRow) {
              self.modelTabel[index] = self.editModelRow;
              self.$nextTick(() => {
                self.$refs.modelTabel.reloadData(self.modelTabel);
              });
            } else {
              self.modelTabel.splice(0, 1);
            }
          }
        }
      });
    },
    /*
      厂家、型号编辑
      type: 1、厂家  2、型号
      index: 行号
      */
    editRow (type, index) {
      let edit = this.isEdit();
      if (edit) {
        this.$message.warning('请先保存当前编辑中的数据');
        return;
      }
      if (type == '1') {
        this.editMakerRow = Object.assign({}, this.makerTabel[index]);
        this.makerTabel[index].edit = true;
        this.$nextTick(() => {
          this.$refs.makerTabel.reloadData(this.makerTabel);
        });
      } else if (type == '2') {
        this.editModelRow = Object.assign({}, this.modelTabel[index]);
        this.modelTabel[index].edit = true;
        this.$nextTick(() => {
          this.$refs.modelTabel.reloadData(this.modelTabel);
        });
      }
    },
    /*
      厂家、型号删除
      type: 1、厂家  2、型号
      id: id
      */
    deleteRow (type, id) {
      let self = this;
      self.$confirm({
        title: '确定删除当前数据吗?',
        okText: '确定',
        cancelText: '取消',
        onOk () {
          self.loading = true;
          if (type == '1') {
            deleteMaker({ id: id })
              .then((res) => {
                if (res.result_code == '1') {
                  self.onSearchMaker();
                } else {
                  self.loading = false;
                  self.$notification.error({
                    message: '系统提示',
                    description: res.result_msg ? res.result_msg : '操作失败',
                    duration: 2.5
                  });
                }
              })
              .catch((e) => {
                self.loading = false;
              });
          } else if (type == '2') {
            deleteModel({ id: id })
              .then((res) => {
                if (res.result_code == '1') {
                  self.onSearchModal();
                } else {
                  self.loading = false;
                  self.$notification.error({
                    message: '系统提示',
                    description: res.result_msg ? res.result_msg : '操作失败',
                    duration: 2.5
                  });
                }
              })
              .catch((e) => {
                self.loading = false;
              });
          }
        }
      });
    },
    // 分页事件-厂家
    makerPageEvent (current, size) {
      this.current0 = current;
      this.size0 = size;
      this.queryMaker();
    },
    // 分页事件-型号
    modelPageEvent (current, size) {
      this.current1 = current;
      this.size1 = size;
      this.queryModel();
    }
  }
};
</script>

<style lang="less" scoped="scoped">
.maker_and_model {
  :deep(.vxe-cell--valid) {
    display: unset !important;
  }
}
</style>
