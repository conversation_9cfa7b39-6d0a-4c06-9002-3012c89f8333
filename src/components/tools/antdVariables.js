const antdLess<PERSON>son = {
  '@blue-1': 'color(~`colorPalette(\'@{blue-6}\', 1) `)',
  '@blue-2': 'color(~`colorPalette(\'@{blue-6}\', 2) `)',
  '@blue-3': 'color(~`colorPalette(\'@{blue-6}\', 3) `)',
  '@blue-4': 'color(~`colorPalette(\'@{blue-6}\', 4) `)',
  '@blue-5': 'color(~`colorPalette(\'@{blue-6}\', 5) `)',
  '@blue-6': '#1890ff',
  '@blue-7': 'color(~`colorPalette(\'@{blue-6}\', 7) `)',
  '@blue-8': 'color(~`colorPalette(\'@{blue-6}\', 8) `)',
  '@blue-9': 'color(~`colorPalette(\'@{blue-6}\', 9) `)',
  '@blue-10': 'color(~`colorPalette(\'@{blue-6}\', 10) `)',
  '@purple-1': 'color(~`colorPalette(\'@{purple-6}\', 1) `)',
  '@purple-2': 'color(~`colorPalette(\'@{purple-6}\', 2) `)',
  '@purple-3': 'color(~`colorPalette(\'@{purple-6}\', 3) `)',
  '@purple-4': 'color(~`colorPalette(\'@{purple-6}\', 4) `)',
  '@purple-5': 'color(~`colorPalette(\'@{purple-6}\', 5) `)',
  '@purple-6': '#722ed1',
  '@purple-7': 'color(~`colorPalette(\'@{purple-6}\', 7) `)',
  '@purple-8': 'color(~`colorPalette(\'@{purple-6}\', 8) `)',
  '@purple-9': 'color(~`colorPalette(\'@{purple-6}\', 9) `)',
  '@purple-10': 'color(~`colorPalette(\'@{purple-6}\', 10) `)',
  '@cyan-1': 'color(~`colorPalette(\'@{cyan-6}\', 1) `)',
  '@cyan-2': 'color(~`colorPalette(\'@{cyan-6}\', 2) `)',
  '@cyan-3': 'color(~`colorPalette(\'@{cyan-6}\', 3) `)',
  '@cyan-4': 'color(~`colorPalette(\'@{cyan-6}\', 4) `)',
  '@cyan-5': 'color(~`colorPalette(\'@{cyan-6}\', 5) `)',
  '@cyan-6': '#13c2c2',
  '@cyan-7': 'color(~`colorPalette(\'@{cyan-6}\', 7) `)',
  '@cyan-8': 'color(~`colorPalette(\'@{cyan-6}\', 8) `)',
  '@cyan-9': 'color(~`colorPalette(\'@{cyan-6}\', 9) `)',
  '@cyan-10': 'color(~`colorPalette(\'@{cyan-6}\', 10) `)',
  '@green-1': 'color(~`colorPalette(\'@{green-6}\', 1) `)',
  '@green-2': 'color(~`colorPalette(\'@{green-6}\', 2) `)',
  '@green-3': 'color(~`colorPalette(\'@{green-6}\', 3) `)',
  '@green-4': 'color(~`colorPalette(\'@{green-6}\', 4) `)',
  '@green-5': 'color(~`colorPalette(\'@{green-6}\', 5) `)',
  '@green-6': '#52c41a',
  '@green-7': 'color(~`colorPalette(\'@{green-6}\', 7) `)',
  '@green-8': 'color(~`colorPalette(\'@{green-6}\', 8) `)',
  '@green-9': 'color(~`colorPalette(\'@{green-6}\', 9) `)',
  '@green-10': 'color(~`colorPalette(\'@{green-6}\', 10) `)',
  '@magenta-1': 'color(~`colorPalette(\'@{magenta-6}\', 1) `)',
  '@magenta-2': 'color(~`colorPalette(\'@{magenta-6}\', 2) `)',
  '@magenta-3': 'color(~`colorPalette(\'@{magenta-6}\', 3) `)',
  '@magenta-4': 'color(~`colorPalette(\'@{magenta-6}\', 4) `)',
  '@magenta-5': 'color(~`colorPalette(\'@{magenta-6}\', 5) `)',
  '@magenta-6': '#eb2f96',
  '@magenta-7': 'color(~`colorPalette(\'@{magenta-6}\', 7) `)',
  '@magenta-8': 'color(~`colorPalette(\'@{magenta-6}\', 8) `)',
  '@magenta-9': 'color(~`colorPalette(\'@{magenta-6}\', 9) `)',
  '@magenta-10': 'color(~`colorPalette(\'@{magenta-6}\', 10) `)',
  '@pink-1': 'color(~`colorPalette(\'@{pink-6}\', 1) `)',
  '@pink-2': 'color(~`colorPalette(\'@{pink-6}\', 2) `)',
  '@pink-3': 'color(~`colorPalette(\'@{pink-6}\', 3) `)',
  '@pink-4': 'color(~`colorPalette(\'@{pink-6}\', 4) `)',
  '@pink-5': 'color(~`colorPalette(\'@{pink-6}\', 5) `)',
  '@pink-6': '#eb2f96',
  '@pink-7': 'color(~`colorPalette(\'@{pink-6}\', 7) `)',
  '@pink-8': 'color(~`colorPalette(\'@{pink-6}\', 8) `)',
  '@pink-9': 'color(~`colorPalette(\'@{pink-6}\', 9) `)',
  '@pink-10': 'color(~`colorPalette(\'@{pink-6}\', 10) `)',
  '@red-1': 'color(~`colorPalette(\'@{red-6}\', 1) `)',
  '@red-2': 'color(~`colorPalette(\'@{red-6}\', 2) `)',
  '@red-3': 'color(~`colorPalette(\'@{red-6}\', 3) `)',
  '@red-4': 'color(~`colorPalette(\'@{red-6}\', 4) `)',
  '@red-5': 'color(~`colorPalette(\'@{red-6}\', 5) `)',
  '@red-6': '#f5222d',
  '@red-7': 'color(~`colorPalette(\'@{red-6}\', 7) `)',
  '@red-8': 'color(~`colorPalette(\'@{red-6}\', 8) `)',
  '@red-9': 'color(~`colorPalette(\'@{red-6}\', 9) `)',
  '@red-10': 'color(~`colorPalette(\'@{red-6}\', 10) `)',
  '@orange-1': 'color(~`colorPalette(\'@{orange-6}\', 1) `)',
  '@orange-2': 'color(~`colorPalette(\'@{orange-6}\', 2) `)',
  '@orange-3': 'color(~`colorPalette(\'@{orange-6}\', 3) `)',
  '@orange-4': 'color(~`colorPalette(\'@{orange-6}\', 4) `)',
  '@orange-5': 'color(~`colorPalette(\'@{orange-6}\', 5) `)',
  '@orange-6': '#fa8c16',
  '@orange-7': 'color(~`colorPalette(\'@{orange-6}\', 7) `)',
  '@orange-8': 'color(~`colorPalette(\'@{orange-6}\', 8) `)',
  '@orange-9': 'color(~`colorPalette(\'@{orange-6}\', 9) `)',
  '@orange-10': 'color(~`colorPalette(\'@{orange-6}\', 10) `)',
  '@yellow-1': 'color(~`colorPalette(\'@{yellow-6}\', 1) `)',
  '@yellow-2': 'color(~`colorPalette(\'@{yellow-6}\', 2) `)',
  '@yellow-3': 'color(~`colorPalette(\'@{yellow-6}\', 3) `)',
  '@yellow-4': 'color(~`colorPalette(\'@{yellow-6}\', 4) `)',
  '@yellow-5': 'color(~`colorPalette(\'@{yellow-6}\', 5) `)',
  '@yellow-6': '#fadb14',
  '@yellow-7': 'color(~`colorPalette(\'@{yellow-6}\', 7) `)',
  '@yellow-8': 'color(~`colorPalette(\'@{yellow-6}\', 8) `)',
  '@yellow-9': 'color(~`colorPalette(\'@{yellow-6}\', 9) `)',
  '@yellow-10': 'color(~`colorPalette(\'@{yellow-6}\', 10) `)',
  '@volcano-1': 'color(~`colorPalette(\'@{volcano-6}\', 1) `)',
  '@volcano-2': 'color(~`colorPalette(\'@{volcano-6}\', 2) `)',
  '@volcano-3': 'color(~`colorPalette(\'@{volcano-6}\', 3) `)',
  '@volcano-4': 'color(~`colorPalette(\'@{volcano-6}\', 4) `)',
  '@volcano-5': 'color(~`colorPalette(\'@{volcano-6}\', 5) `)',
  '@volcano-6': '#fa541c',
  '@volcano-7': 'color(~`colorPalette(\'@{volcano-6}\', 7) `)',
  '@volcano-8': 'color(~`colorPalette(\'@{volcano-6}\', 8) `)',
  '@volcano-9': 'color(~`colorPalette(\'@{volcano-6}\', 9) `)',
  '@volcano-10': 'color(~`colorPalette(\'@{volcano-6}\', 10) `)',
  '@geekblue-1': 'color(~`colorPalette(\'@{geekblue-6}\', 1) `)',
  '@geekblue-2': 'color(~`colorPalette(\'@{geekblue-6}\', 2) `)',
  '@geekblue-3': 'color(~`colorPalette(\'@{geekblue-6}\', 3) `)',
  '@geekblue-4': 'color(~`colorPalette(\'@{geekblue-6}\', 4) `)',
  '@geekblue-5': 'color(~`colorPalette(\'@{geekblue-6}\', 5) `)',
  '@geekblue-6': '#2f54eb',
  '@geekblue-7': 'color(~`colorPalette(\'@{geekblue-6}\', 7) `)',
  '@geekblue-8': 'color(~`colorPalette(\'@{geekblue-6}\', 8) `)',
  '@geekblue-9': 'color(~`colorPalette(\'@{geekblue-6}\', 9) `)',
  '@geekblue-10': 'color(~`colorPalette(\'@{geekblue-6}\', 10) `)',
  '@lime-1': 'color(~`colorPalette(\'@{lime-6}\', 1) `)',
  '@lime-2': 'color(~`colorPalette(\'@{lime-6}\', 2) `)',
  '@lime-3': 'color(~`colorPalette(\'@{lime-6}\', 3) `)',
  '@lime-4': 'color(~`colorPalette(\'@{lime-6}\', 4) `)',
  '@lime-5': 'color(~`colorPalette(\'@{lime-6}\', 5) `)',
  '@lime-6': '#a0d911',
  '@lime-7': 'color(~`colorPalette(\'@{lime-6}\', 7) `)',
  '@lime-8': 'color(~`colorPalette(\'@{lime-6}\', 8) `)',
  '@lime-9': 'color(~`colorPalette(\'@{lime-6}\', 9) `)',
  '@lime-10': 'color(~`colorPalette(\'@{lime-6}\', 10) `)',
  '@gold-1': 'color(~`colorPalette(\'@{gold-6}\', 1) `)',
  '@gold-2': 'color(~`colorPalette(\'@{gold-6}\', 2) `)',
  '@gold-3': 'color(~`colorPalette(\'@{gold-6}\', 3) `)',
  '@gold-4': 'color(~`colorPalette(\'@{gold-6}\', 4) `)',
  '@gold-5': 'color(~`colorPalette(\'@{gold-6}\', 5) `)',
  '@gold-6': '#faad14',
  '@gold-7': 'color(~`colorPalette(\'@{gold-6}\', 7) `)',
  '@gold-8': 'color(~`colorPalette(\'@{gold-6}\', 8) `)',
  '@gold-9': 'color(~`colorPalette(\'@{gold-6}\', 9) `)',
  '@gold-10': 'color(~`colorPalette(\'@{gold-6}\', 10) `)',
  '@ant-prefix': 'ant',
  '@html-selector': 'html',
  '@primary-color': '@blue-6',
  '@info-color': '@blue-6',
  '@success-color': '@green-6',
  '@processing-color': '@blue-6',
  '@error-color': '@red-6',
  '@highlight-color': '@red-6',
  '@warning-color': '@gold-6',
  '@normal-color': '#d9d9d9',
  '@white': '#fff',
  '@black': '#000',
  '@primary-1': 'color(~`colorPalette(\'@{primary-color}\', 1) `)',
  '@primary-2': 'color(~`colorPalette(\'@{primary-color}\', 2) `)',
  '@primary-3': 'color(~`colorPalette(\'@{primary-color}\', 3) `)',
  '@primary-4': 'color(~`colorPalette(\'@{primary-color}\', 4) `)',
  '@primary-5': 'color(~`colorPalette(\'@{primary-color}\', 5) `)',
  '@primary-6': '@primary-color',
  '@primary-7': 'color(~`colorPalette(\'@{primary-color}\', 7) `)',
  '@primary-8': 'color(~`colorPalette(\'@{primary-color}\', 8) `)',
  '@primary-9': 'color(~`colorPalette(\'@{primary-color}\', 9) `)',
  '@primary-10': 'color(~`colorPalette(\'@{primary-color}\', 10) `)',
  '@body-background': '#fff',
  '@component-background': '#fff',
  '@font-family': '-apple-system, BlinkMacSystemFont, \'Segoe UI\', \'PingFang SC\', \'Hiragino Sans GB\',' + '  \'Microsoft YaHei\', \'Helvetica Neue\', Helvetica, Arial, sans-serif, \'Apple Color Emoji\',' + '  \'Segoe UI Emoji\', \'Segoe UI Symbol\'',
  '@code-family': '\'SFMono-Regular\', Consolas, \'Liberation Mono\', Menlo, Courier, monospace',
  '@text-color': 'fade(@black, 65%)',
  '@text-color-secondary': 'fade(@black, 45%)',
  '@text-color-inverse': '@white',
  '@icon-color': 'inherit',
  '@icon-color-hover': 'fade(@black, 75%)',
  '@heading-color': 'fade(#000, 85%)',
  '@heading-color-dark': 'fade(@white, 100%)',
  '@text-color-dark': 'fade(@white, 85%)',
  '@text-color-secondary-dark': 'fade(@white, 65%)',
  '@text-selection-bg': '@primary-color',
  '@font-variant-base': 'tabular-nums',
  '@font-feature-settings-base': 'tnum',
  '@font-size-base': '14px',
  '@font-size-lg': '@font-size-base + 2px',
  '@font-size-sm': '12px',
  '@heading-1-size': 'ceil(@font-size-base * 2.71)',
  '@heading-2-size': 'ceil(@font-size-base * 2.14)',
  '@heading-3-size': 'ceil(@font-size-base * 1.71)',
  '@heading-4-size': 'ceil(@font-size-base * 1.42)',
  '@line-height-base': '1.5',
  '@border-radius-base': '4px',
  '@border-radius-sm': '2px',
  '@padding-lg': '24px',
  '@padding-md': '16px',
  '@padding-sm': '12px',
  '@padding-xs': '8px',
  '@control-padding-horizontal': '@padding-sm',
  '@control-padding-horizontal-sm': '@padding-xs',
  '@item-active-bg': '@primary-1',
  '@item-hover-bg': '@primary-1',
  '@iconfont-css-prefix': 'anticon',
  '@link-color': '@primary-color',
  '@link-hover-color': 'color(~`colorPalette(\'@{link-color}\', 5) `)',
  '@link-active-color': 'color(~`colorPalette(\'@{link-color}\', 7) `)',
  '@link-decoration': 'none',
  '@link-hover-decoration': 'none',
  '@ease-base-out': 'cubic-bezier(.7, .3, .1, 1)',
  '@ease-base-in': 'cubic-bezier(.9, 0, .3, .7)',
  '@ease-out': 'cubic-bezier(.215, .61, .355, 1)',
  '@ease-in': 'cubic-bezier(.55, .055, .675, .19)',
  '@ease-in-out': 'cubic-bezier(.645, .045, .355, 1)',
  '@ease-out-back': 'cubic-bezier(.12, .4, .29, 1.46)',
  '@ease-in-back': 'cubic-bezier(.71, -.46, .88, .6)',
  '@ease-in-out-back': 'cubic-bezier(.71, -.46, .29, 1.46)',
  '@ease-out-circ': 'cubic-bezier(.08, .82, .17, 1)',
  '@ease-in-circ': 'cubic-bezier(.6, .04, .98, .34)',
  '@ease-in-out-circ': 'cubic-bezier(.78, .14, .15, .86)',
  '@ease-out-quint': 'cubic-bezier(.23, 1, .32, 1)',
  '@ease-in-quint': 'cubic-bezier(.755, .05, .855, .06)',
  '@ease-in-out-quint': 'cubic-bezier(.86, 0, .07, 1)',
  '@border-color-base': 'hsv(0, 0, 85%)',
  '@border-color-split': 'hsv(0, 0, 91%)',
  '@border-color-inverse': '@white',
  '@border-width-base': '1px',
  '@border-style-base': 'solid',
  '@outline-blur-size': '0',
  '@outline-width': '2px',
  '@outline-color': '@primary-color',
  '@background-color-light': 'hsv(0, 0, 98%)',
  '@background-color-base': 'hsv(0, 0, 96%)',
  '@disabled-color': 'fade(#000, 25%)',
  '@disabled-bg': '@background-color-base',
  '@disabled-color-dark': 'fade(#fff, 35%)',
  '@shadow-color': 'rgba(0, 0, 0, .15)',
  '@shadow-color-inverse': '@component-background',
  '@box-shadow-base': '@shadow-1-down',
  '@shadow-1-up': '0 -2px 8px @shadow-color',
  '@shadow-1-down': '0 2px 8px @shadow-color',
  '@shadow-1-left': '-2px 0 8px @shadow-color',
  '@shadow-1-right': '2px 0 8px @shadow-color',
  '@shadow-2': '0 4px 12px @shadow-color',
  '@btn-font-weight': '400',
  '@btn-border-radius-base': '@border-radius-base',
  '@btn-border-radius-sm': '@border-radius-base',
  '@btn-border-width': '@border-width-base',
  '@btn-border-style': '@border-style-base',
  '@btn-shadow': '0 2px 0 rgba(0, 0, 0, .015)',
  '@btn-primary-shadow': '0 2px 0 rgba(0, 0, 0, .045)',
  '@btn-text-shadow': '0 -1px 0 rgba(0, 0, 0, .12)',
  '@btn-primary-color': '#fff',
  '@btn-primary-bg': '@primary-color',
  '@btn-default-color': '@text-color',
  '@btn-default-bg': '@component-background',
  '@btn-default-border': '@border-color-base',
  '@btn-danger-color': '#fff',
  '@btn-danger-bg': 'color(~`colorPalette(\'@{error-color}\', 5) `)',
  '@btn-danger-border': 'color(~`colorPalette(\'@{error-color}\', 5) `)',
  '@btn-disable-color': '@disabled-color',
  '@btn-disable-bg': '@disabled-bg',
  '@btn-disable-border': '@border-color-base',
  '@btn-padding-base': '0 @padding-md - 1px',
  '@btn-font-size-lg': '@font-size-lg',
  '@btn-font-size-sm': '@font-size-base',
  '@btn-padding-lg': '@btn-padding-base',
  '@btn-padding-sm': '0 @padding-xs - 1px',
  '@btn-height-base': '32px',
  '@btn-height-lg': '40px',
  '@btn-height-sm': '24px',
  '@btn-circle-size': '@btn-height-base',
  '@btn-circle-size-lg': '@btn-height-lg',
  '@btn-circle-size-sm': '@btn-height-sm',
  '@btn-square-size': '@btn-height-base',
  '@btn-square-size-lg': '@btn-height-lg',
  '@btn-square-size-sm': '@btn-height-sm',
  '@btn-group-border': '@primary-5',
  '@checkbox-size': '16px',
  '@checkbox-color': '@primary-color',
  '@checkbox-check-color': '#fff',
  '@checkbox-border-width': '@border-width-base',
  '@descriptions-bg': '#fafafa',
  '@dropdown-selected-color': '@primary-color',
  '@empty-font-size': '@font-size-base',
  '@radio-size': '16px',
  '@radio-dot-color': '@primary-color',
  '@radio-button-bg': '@btn-default-bg',
  '@radio-button-checked-bg': '@btn-default-bg',
  '@radio-button-color': '@btn-default-color',
  '@radio-button-hover-color': '@primary-5',
  '@radio-button-active-color': '@primary-7',
  '@screen-xs': '480px',
  '@screen-xs-min': '@screen-xs',
  '@screen-sm': '576px',
  '@screen-sm-min': '@screen-sm',
  '@screen-md': '768px',
  '@screen-md-min': '@screen-md',
  '@screen-lg': '992px',
  '@screen-lg-min': '@screen-lg',
  '@screen-xl': '1200px',
  '@screen-xl-min': '@screen-xl',
  '@screen-xxl': '1600px',
  '@screen-xxl-min': '@screen-xxl',
  '@screen-xs-max': '(@screen-sm-min - 1px)',
  '@screen-sm-max': '(@screen-md-min - 1px)',
  '@screen-md-max': '(@screen-lg-min - 1px)',
  '@screen-lg-max': '(@screen-xl-min - 1px)',
  '@screen-xl-max': '(@screen-xxl-min - 1px)',
  '@grid-columns': '24',
  '@grid-gutter-width': '0',
  '@layout-body-background': '#f0f2f5',
  '@layout-header-background': '#001529',
  '@layout-footer-background': '@layout-body-background',
  '@layout-header-height': '64px',
  '@layout-header-padding': '0 50px',
  '@layout-footer-padding': '24px 50px',
  '@layout-sider-background': '@layout-header-background',
  '@layout-trigger-height': '48px',
  '@layout-trigger-background': '#002140',
  '@layout-trigger-color': '#fff',
  '@layout-zero-trigger-width': '36px',
  '@layout-zero-trigger-height': '42px',
  '@layout-sider-background-light': '#fff',
  '@layout-trigger-background-light': '#fff',
  '@layout-trigger-color-light': '@text-color',
  '@zindex-badge': '1',
  '@zindex-table-fixed': '1',
  '@zindex-affix': '10',
  '@zindex-back-top': '10',
  '@zindex-picker-panel': '10',
  '@zindex-popup-close': '10',
  '@zindex-modal': '1000',
  '@zindex-modal-mask': '1000',
  '@zindex-message': '1010',
  '@zindex-notification': '1010',
  '@zindex-popover': '1030',
  '@zindex-dropdown': '1050',
  '@zindex-picker': '1050',
  '@zindex-tooltip': '1060',
  '@animation-duration-slow': '.3s',
  '@animation-duration-base': '.2s',
  '@animation-duration-fast': '.1s',
  '@collapse-panel-border-radius': '@border-radius-base',
  '@dropdown-vertical-padding': '5px',
  '@dropdown-edge-child-vertical-padding': '4px',
  '@dropdown-font-size': '@font-size-base',
  '@dropdown-line-height': '22px',
  '@label-required-color': '@highlight-color',
  '@label-color': '@heading-color',
  '@form-warning-input-bg': '@input-bg',
  '@form-item-margin-bottom': '24px',
  '@form-item-trailing-colon': 'true',
  '@form-vertical-label-padding': '0 0 8px',
  '@form-vertical-label-margin': '0',
  '@form-item-label-colon-margin-right': '8px',
  '@form-item-label-colon-margin-left': '2px',
  '@form-error-input-bg': '@input-bg',
  '@input-height-base': '32px',
  '@input-height-lg': '40px',
  '@input-height-sm': '24px',
  '@input-padding-horizontal': '@control-padding-horizontal - 1px',
  '@input-padding-horizontal-base': '@input-padding-horizontal',
  '@input-padding-horizontal-sm': '@control-padding-horizontal-sm - 1px',
  '@input-padding-horizontal-lg': '@input-padding-horizontal',
  '@input-padding-vertical-base': '4px',
  '@input-padding-vertical-sm': '1px',
  '@input-padding-vertical-lg': '6px',
  '@input-placeholder-color': 'hsv(0, 0, 75%)',
  '@input-color': '@text-color',
  '@input-border-color': '@border-color-base',
  '@input-bg': '@component-background',
  '@input-number-hover-border-color': '@input-hover-border-color',
  '@input-number-handler-active-bg': '#f4f4f4',
  '@input-number-handler-hover-bg': '@primary-5',
  '@input-number-handler-bg': '@component-background',
  '@input-number-handler-border-color': '@border-color-base',
  '@input-addon-bg': '@background-color-light',
  '@input-hover-border-color': '@primary-5',
  '@input-disabled-bg': '@disabled-bg',
  '@input-outline-offset': '0 0',
  '@select-border-color': '@border-color-base',
  '@select-item-selected-color': '@text-color',
  '@select-item-selected-font-weight': '600',
  '@select-dropdown-bg': '@component-background',
  '@select-dropdown-vertical-padding': '@dropdown-vertical-padding',
  '@select-dropdown-edge-child-vertical-padding': '@dropdown-edge-child-vertical-padding',
  '@select-dropdown-font-size': '@dropdown-font-size',
  '@select-dropdown-line-height': '@dropdown-line-height',
  '@select-item-selected-bg': '@background-color-light',
  '@select-item-active-bg': '@item-active-bg',
  '@select-background': '@component-background',
  '@cascader-dropdown-vertical-padding': '@dropdown-vertical-padding',
  '@cascader-dropdown-edge-child-vertical-padding': '@dropdown-edge-child-vertical-padding',
  '@cascader-dropdown-font-size': '@dropdown-font-size',
  '@cascader-dropdown-line-height': '@dropdown-line-height',
  '@anchor-border-color': '@border-color-split',
  '@tooltip-max-width': '250px',
  '@tooltip-color': '#fff',
  '@tooltip-bg': 'rgba(0, 0, 0, .75)',
  '@tooltip-arrow-width': '5px',
  '@tooltip-distance': '@tooltip-arrow-width - 1px + 4px',
  '@tooltip-arrow-color': '@tooltip-bg',
  '@popover-bg': '@component-background',
  '@popover-color': '@text-color',
  '@popover-min-width': '177px',
  '@popover-arrow-width': '6px',
  '@popover-arrow-color': '@popover-bg',
  '@popover-arrow-outer-color': '@popover-bg',
  '@popover-distance': '@popover-arrow-width + 4px',
  '@modal-body-padding': '24px',
  '@modal-header-bg': '@component-background',
  '@modal-header-border-color-split': '@border-color-split',
  '@modal-heading-color': '@heading-color',
  '@modal-footer-bg': 'transparent',
  '@modal-footer-border-color-split': '@border-color-split',
  '@modal-mask-bg': 'fade(@black, 45%)',
  '@progress-default-color': '@processing-color',
  '@progress-remaining-color': '@background-color-base',
  '@progress-text-color': '@text-color',
  '@progress-radius': '100px',
  '@menu-inline-toplevel-item-height': '40px',
  '@menu-item-height': '40px',
  '@menu-collapsed-width': '80px',
  '@menu-bg': '@component-background',
  '@menu-popup-bg': '@component-background',
  '@menu-item-color': '@text-color',
  '@menu-highlight-color': '@primary-color',
  '@menu-item-active-bg': '@item-active-bg',
  '@menu-item-active-border-width': '3px',
  '@menu-item-group-title-color': '@text-color-secondary',
  '@menu-icon-size': '@font-size-base',
  '@menu-icon-size-lg': '@font-size-lg',
  '@menu-item-vertical-margin': '4px',
  '@menu-item-font-size': '@font-size-base',
  '@menu-item-boundary-margin': '8px',
  '@menu-dark-color': '@text-color-secondary-dark',
  '@menu-dark-bg': '@layout-header-background',
  '@menu-dark-arrow-color': '#fff',
  '@menu-dark-submenu-bg': '#000c17',
  '@menu-dark-highlight-color': '#fff',
  '@menu-dark-item-active-bg': '@primary-color',
  '@menu-dark-selected-item-icon-color': '@white',
  '@menu-dark-selected-item-text-color': '@white',
  '@menu-dark-item-hover-bg': 'transparent',
  '@spin-dot-size-sm': '14px',
  '@spin-dot-size': '20px',
  '@spin-dot-size-lg': '32px',
  '@table-header-bg': '@background-color-light',
  '@table-header-color': '@heading-color',
  '@table-header-sort-bg': '@background-color-base',
  '@table-body-sort-bg': 'rgba(0, 0, 0, .01)',
  '@table-row-hover-bg': '@primary-1',
  '@table-selected-row-color': 'inherit',
  '@table-selected-row-bg': '#fafafa',
  '@table-body-selected-sort-bg': '@table-selected-row-bg',
  '@table-selected-row-hover-bg': '@table-selected-row-bg',
  '@table-expanded-row-bg': '#fbfbfb',
  '@table-padding-vertical': '16px',
  '@table-padding-horizontal': '16px',
  '@table-border-radius-base': '@border-radius-base',
  '@table-footer-bg': '@background-color-light',
  '@table-footer-color': '@heading-color',
  '@table-header-bg-sm': 'transparent',
  '@tag-default-bg': '@background-color-light',
  '@tag-default-color': '@text-color',
  '@tag-font-size': '@font-size-sm',
  '@time-picker-panel-column-width': '56px',
  '@time-picker-panel-width': '@time-picker-panel-column-width * 3',
  '@time-picker-selected-bg': '@background-color-base',
  '@carousel-dot-width': '16px',
  '@carousel-dot-height': '3px',
  '@carousel-dot-active-width': '24px',
  '@badge-height': '20px',
  '@badge-dot-size': '6px',
  '@badge-font-size': '@font-size-sm',
  '@badge-font-weight': 'normal',
  '@badge-status-size': '6px',
  '@badge-text-color': '@component-background',
  '@rate-star-color': '@yellow-6',
  '@rate-star-bg': '@border-color-split',
  '@card-head-color': '@heading-color',
  '@card-head-background': 'transparent',
  '@card-head-padding': '16px',
  '@card-inner-head-padding': '12px',
  '@card-padding-base': '24px',
  '@card-actions-background': '@background-color-light',
  '@card-skeleton-bg': '#cfd8dc',
  '@card-background': '@component-background',
  '@card-shadow': '0 2px 8px rgba(0, 0, 0, .09)',
  '@card-radius': '@border-radius-sm',
  '@comment-padding-base': '16px 0',
  '@comment-nest-indent': '44px',
  '@comment-font-size-base': '@font-size-base',
  '@comment-font-size-sm': '@font-size-sm',
  '@comment-author-name-color': '@text-color-secondary',
  '@comment-author-time-color': '#ccc',
  '@comment-action-color': '@text-color-secondary',
  '@comment-action-hover-color': '#595959',
  '@tabs-card-head-background': '@background-color-light',
  '@tabs-card-height': '40px',
  '@tabs-card-active-color': '@primary-color',
  '@tabs-title-font-size': '@font-size-base',
  '@tabs-title-font-size-lg': '@font-size-lg',
  '@tabs-title-font-size-sm': '@font-size-base',
  '@tabs-ink-bar-color': '@primary-color',
  '@tabs-bar-margin': '0 0 16px 0',
  '@tabs-horizontal-margin': '0 32px 0 0',
  '@tabs-horizontal-padding': '12px 16px',
  '@tabs-horizontal-padding-lg': '16px',
  '@tabs-horizontal-padding-sm': '8px 16px',
  '@tabs-vertical-padding': '8px 24px',
  '@tabs-vertical-margin': '0 0 16px 0',
  '@tabs-scrolling-size': '32px',
  '@tabs-highlight-color': '@primary-color',
  '@tabs-hover-color': '@primary-5',
  '@tabs-active-color': '@primary-7',
  '@tabs-card-gutter': '2px',
  '@tabs-card-tab-active-border-top': '2px solid transparent',
  '@back-top-color': '#fff',
  '@back-top-bg': '@text-color-secondary',
  '@back-top-hover-bg': '@text-color',
  '@avatar-size-base': '32px',
  '@avatar-size-lg': '40px',
  '@avatar-size-sm': '24px',
  '@avatar-font-size-base': '18px',
  '@avatar-font-size-lg': '24px',
  '@avatar-font-size-sm': '14px',
  '@avatar-bg': '#ccc',
  '@avatar-color': '#fff',
  '@avatar-border-radius': '@border-radius-base',
  '@switch-height': '22px',
  '@switch-sm-height': '16px',
  '@switch-sm-checked-margin-left': '-(@switch-sm-height - 3px)',
  '@switch-disabled-opacity': '.4',
  '@switch-color': '@primary-color',
  '@switch-shadow-color': 'fade(#00230b, 20%)',
  '@pagination-item-size': '32px',
  '@pagination-item-size-sm': '24px',
  '@pagination-font-family': 'Arial',
  '@pagination-font-weight-active': '500',
  '@pagination-item-bg-active': '@component-background',
  '@page-header-padding': '24px',
  '@page-header-padding-vertical': '16px',
  '@page-header-padding-breadcrumb': '12px',
  '@page-header-back-color': '#000',
  '@breadcrumb-base-color': '@text-color-secondary',
  '@breadcrumb-last-item-color': '@text-color',
  '@breadcrumb-font-size': '@font-size-base',
  '@breadcrumb-icon-font-size': '@font-size-base',
  '@breadcrumb-link-color': '@text-color-secondary',
  '@breadcrumb-link-color-hover': '@primary-5',
  '@breadcrumb-separator-color': '@text-color-secondary',
  '@breadcrumb-separator-margin': '0 @padding-xs',
  '@slider-margin': '14px 6px 10px',
  '@slider-rail-background-color': '@background-color-base',
  '@slider-rail-background-color-hover': '#e1e1e1',
  '@slider-track-background-color': '@primary-3',
  '@slider-track-background-color-hover': '@primary-4',
  '@slider-handle-border-width': '2px',
  '@slider-handle-background-color': '@component-background',
  '@slider-handle-color': '@primary-3',
  '@slider-handle-color-hover': '@primary-4',
  '@slider-handle-color-focus': 'tint(@primary-color, 20%)',
  '@slider-handle-color-focus-shadow': 'fade(@primary-color, 20%)',
  '@slider-handle-color-tooltip-open': '@primary-color',
  '@slider-handle-shadow': '0',
  '@slider-dot-border-color': '@border-color-split',
  '@slider-dot-border-color-active': 'tint(@primary-color, 50%)',
  '@slider-disabled-color': '@disabled-color',
  '@slider-disabled-background-color': '@component-background',
  '@tree-title-height': '24px',
  '@tree-child-padding': '18px',
  '@tree-directory-selected-color': '#fff',
  '@tree-directory-selected-bg': '@primary-color',
  '@tree-node-hover-bg': '@item-hover-bg',
  '@tree-node-selected-bg': '@primary-2',
  '@collapse-header-padding': '12px 16px',
  '@collapse-header-padding-extra': '40px',
  '@collapse-header-bg': '@background-color-light',
  '@collapse-content-padding': '@padding-md',
  '@collapse-content-bg': '@component-background',
  '@skeleton-color': '#f2f2f2',
  '@transfer-header-height': '40px',
  '@transfer-disabled-bg': '@disabled-bg',
  '@transfer-list-height': '200px',
  '@message-notice-content-padding': '10px 16px',
  '@wave-animation-width': '6px',
  '@alert-success-border-color': '~`colorPalette(\'@{success-color}\', 3) `',
  '@alert-success-bg-color': '~`colorPalette(\'@{success-color}\', 1) `',
  '@alert-success-icon-color': '@success-color',
  '@alert-info-border-color': '~`colorPalette(\'@{info-color}\', 3) `',
  '@alert-info-bg-color': '~`colorPalette(\'@{info-color}\', 1) `',
  '@alert-info-icon-color': '@info-color',
  '@alert-warning-border-color': '~`colorPalette(\'@{warning-color}\', 3) `',
  '@alert-warning-bg-color': '~`colorPalette(\'@{warning-color}\', 1) `',
  '@alert-warning-icon-color': '@warning-color',
  '@alert-error-border-color': '~`colorPalette(\'@{error-color}\', 3) `',
  '@alert-error-bg-color': '~`colorPalette(\'@{error-color}\', 1) `',
  '@alert-error-icon-color': '@error-color',
  '@list-header-background': 'transparent',
  '@list-footer-background': 'transparent',
  '@list-empty-text-padding': '@padding-md',
  '@list-item-padding': '@padding-sm 0',
  '@list-item-meta-margin-bottom': '@padding-md',
  '@list-item-meta-avatar-margin-right': '@padding-md',
  '@list-item-meta-title-margin-bottom': '@padding-sm',
  '@statistic-title-font-size': '@font-size-base',
  '@statistic-content-font-size': '24px',
  '@statistic-unit-font-size': '16px',
  '@statistic-font-family': '@font-family',
  '@drawer-header-padding': '16px 24px',
  '@drawer-body-padding': '24px',
  '@timeline-width': '2px',
  '@timeline-color': '@border-color-split',
  '@timeline-dot-border-width': '2px',
  '@timeline-dot-color': '@primary-color',
  '@timeline-dot-bg': '@component-background',
  '@typography-title-font-weight': '600',
  '@typography-title-margin-top': '1.2em',
  '@typography-title-margin-bottom': '.5em'
};

export const formatDependency = (lessJson) => {
  const result = {};
  Object.keys(lessJson).forEach((lessKey) => {
    const keyValue = antdLessJson[lessKey];
    if (!keyValue || keyValue.indexOf('@') === -1) {
      // 当前变量不存在依赖项
      result[lessKey] = [];
      return;
    }
    const dependencies = keyValue.match(/@[a-z|-]+/g);
    if (dependencies && Array.isArray(dependencies)) {
      result[lessKey] = result[lessKey] ? [...result[lessKey], ...dependencies] : dependencies;
      return;
    }
    // `` 存在js变量进行替换
    const varDependencies = (keyValue.match(/@\{[a-z|-]+?\}/g) || []).map(item => item.replace(/[{|}]/g, ''));
    if (dependencies && Array.isArray(dependencies)) {
      result[lessKey] = result[lessKey] ? [...result[lessKey], ...varDependencies] : varDependencies;
    }
  });
  // 开始进行合并
  const mergeResult = {};
  const findDeps = (key, depsMap) => {
    let currKey = key;
    let deps = [];
    while (depsMap[currKey] && depsMap[currKey].length) {
      // 默认单链合并
      deps = [...deps, ...depsMap[currKey]];
      if (depsMap[currKey] && depsMap[currKey][0]) {
        currKey = depsMap[currKey][0];
      } else {
        break;
      }
    }
    return deps;
  };
  Object.keys(result).forEach((key) => {
    mergeResult[key] = findDeps(key, result);
  });
  return mergeResult;
};

export const antdLessDependency = formatDependency(antdLessJson);

export const flatDependencyKeys = (arr = [], depsMap) => arr.sort((a, b) => {
  if (depsMap[a] && depsMap[a].includes(b)) return -1;
  if (depsMap[b] && depsMap[b].includes(a)) return 1;
  return 0;
});

export const getAllDependencyVariables = (key, value) => {
  const result = { [key]: value };
  const dependencyMap = [{ targetKey: key, targetValue: value }];
  while (dependencyMap.length) {
    // 构造可能得依赖项
    // 变量依赖
    const { targetKey, targetValue } = dependencyMap.pop();
    Object.keys(antdLessJson).forEach((lessKey) => {
      const keyValue = antdLessJson[lessKey];
      if (!keyValue || keyValue.indexOf('@') === -1) {
        // 当前变量不存在依赖项
        return;
      }
      const keyIndex = keyValue.indexOf(targetKey);
      if (keyIndex > -1 && ((keyIndex + targetKey.length) === (keyValue.length - 1) || keyValue[keyIndex + targetKey.length] !== '-')) {
        // 确认包含变量，进行替换
        result[lessKey] = keyValue.replace(targetKey, targetValue);
        // 进入下一循环迭代
        dependencyMap.push({ targetKey: lessKey, targetValue: result[lessKey] });
        return;
      }
      // `` 存在js变量进行替换
      const variableKey = `@{${targetKey.slice(1)}}`;
      if (keyValue.indexOf(variableKey) > -1) {
        // 确认包含变量，进行替换
        result[lessKey] = keyValue.replace(variableKey, targetValue);
        // 进入下一循环迭代
        dependencyMap.push({ targetKey: lessKey, targetValue: result[lessKey] });
      }
    });
  }
  return result;
};

export const formatThemeConfig = (themesConfig = {}) => {
  let result = {};
  const flatKeys = flatDependencyKeys(Object.keys(themesConfig), antdLessDependency);
  flatKeys.forEach((key) => {
    result = { ...result, ...getAllDependencyVariables(key, themesConfig[key]) };
  });
  return result;
};

export default antdLessJson;
