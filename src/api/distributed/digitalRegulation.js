import { postAction } from '@/api/manage';
const baseUrl = process.env.VUE_APP_API_ERP_URL;
// 作业指导书API
// 作业指导书列表
const listInstruction = (params) => postAction(baseUrl + '/industry/pdca/sopConfig/list', params);
// 新增作业指导书
const addInstruction = (params) => postAction(baseUrl + '/industry/pdca/sopConfig/addSopConfig', params);
// 作业指导书删除（含批量删除）
const deleteInstruction = (params) => postAction(baseUrl + '/industry/pdca/sopConfig/sopConfigDel', params);
// 作业指导书编辑
const editInstruction = (params) => postAction(baseUrl + '/industry/pdca/sopConfig/sopConfigEdit', params);
// 作业指导书详情
const getInstructionDetail = (params) => postAction(baseUrl + '/industry/pdca/sopConfig/sopConfigDetail', params);

// 工单超时配置API
// 工单超时配置列表
const listOrderOut = (params) => postAction(baseUrl + '/industry/pdca/wotConfig/list', params);
// 工单超时配置删除
const deleteOrderOut = (params) => postAction(baseUrl + '/industry/pdca/wotConfig/wotDel', params);
// 新增工单超时配置
const addOrderOut = (params) => postAction(baseUrl + '/industry/pdca/wotConfig/wotAdd', params);
// 工单超时配置编辑
const editOrderOut = (params) => postAction(baseUrl + '/industry/pdca/wotConfig/wotEdit', params);
// 工单超时配置详情
const getOrderOutDetail = (params) => postAction(baseUrl + '/industry/pdca/wotConfig/wotDetail', params);

// 根据任务类型，设备类型 查询指导书配置
const getSopDetailForBus = (params) => postAction(baseUrl + '/industry/pdca/sopConfig/getSopDetailForBus', params);

export {
  listInstruction,
  addInstruction,
  deleteInstruction,
  editInstruction,
  getInstructionDetail,
  listOrderOut,
  deleteOrderOut,
  addOrderOut,
  editOrderOut,
  getOrderOutDetail,
  getSopDetailForBus
};
