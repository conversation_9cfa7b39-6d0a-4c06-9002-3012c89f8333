import { postAction } from '@/api/manage';
const url = process.env.VUE_APP_API_ERP_URL;
// 厂家-删除
const deleteMaker = (params) => postAction(url + '/equipment/v1/devicemaker/delete', params);
// 厂家-新增
const insertMaker = (params) => postAction(url + '/equipment/v1/devicemaker/insert', params);
// 厂家-查询
const selectMaker = (params) => postAction(url + '/equipment/v1/devicemaker/select', params);
// 厂家-更新
const updateMaker = (params) => postAction(url + '/equipment/v1/devicemaker/update', params);

// 型号-删除
const deleteModel = (params) => postAction(url + '/equipment/v1/devicemakermodel/delete', params);
// 型号-新增
const insertModel = (params) => postAction(url + '/equipment/v1/devicemakermodel/insert', params);
// 型号-查询
const selectModel = (params) => postAction(url + '/equipment/v1/devicemakermodel/select', params);
// 型号-更新
const updateModel = (params) => postAction(url + '/equipment/v1/devicemakermodel/update', params);
// 型号参数-编辑
const updateParameter = (params) => postAction(url + '/equipment/v1/devicemakermodel/updateParameter', params);
// 型号参数-查询
const selectParameter = (params) => postAction(url + '/equipment/v1/devicemakermodel/selectParameter', params);
export {
  deleteMaker,
  insertMaker,
  selectMaker,
  updateMaker,
  deleteModel,
  insertModel,
  selectModel,
  updateModel,
  updateParameter,
  selectParameter
};
