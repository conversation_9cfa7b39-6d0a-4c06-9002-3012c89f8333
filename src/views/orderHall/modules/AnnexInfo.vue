<template>
  <div v-if="annexInfoShow">
    <div v-if="['3', '7', '8'].includes(type)">
      <AnnexInfoDetail :baseForm="formData" />
    </div>
    <div v-else>
      <a-row :gutter="24">
        <a-col :span="24">
          <div class="order-dispose">
            <div class="title-box">
              <span class="before"></span>
              <span>附件信息</span>
            </div>
          </div>
        </a-col>
      </a-row>
      <a-row :gutter="24">
        <a-col :span="24">
          <!-- 缺陷：缺陷图片，故障：故障图片 -->
          <a-form-model-item :label="formData.taskType === '7' ? '故障图片' : '缺陷图片'" prop="uploadFileList">
            <uploadFileView
              v-model="formData.uploadFileList"
              :maxNum="5"
              :maxSize="10"
              tip="最多上传5张图片，单张图片不超10MB，支持格式jpg/jpeg/png/bmp！"
              :multiple="true"
              accept=".jpg,.png,.jpeg,.bmp"
              >上传</uploadFileView
            >
          </a-form-model-item>
        </a-col>
      </a-row>
    </div>
  </div>
</template>

<script>
import uploadFileView from '@/components/com/fileUploadView';
import AnnexInfoDetail from './AnnexInfoDetail';
export default {
  name: 'annex-info',
  props: {
    type: {
      type: String,
      default: () => {
        return null;
      }
    },
    formData: {
      type: Object,
      default: () => {
        return {};
      }
    }
  },
  components: { uploadFileView, AnnexInfoDetail },
  data () {
    return {};
  },
  methods: {
    // 附件变化触发附件校验
    uploadFileListChange () {}
  },
  computed: {
    annexInfoShow () {
      const type = this.type;
      const formData = this.formData;
      if (['3', '6', '5', '8'].includes(type)) {
        const flag = ['7', '8'].includes(formData.taskType);
        return flag;
      }
      if (['7'].includes(type)) {
        if (['7', '8'].includes(formData.taskType)) {
          return true;
        }
        return false;
      }
      return false;
    }
  }
};
</script>
