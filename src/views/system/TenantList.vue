<template>
  <div >
    <!-- 查询区域 -->
    <div class="solar-eye-search-model">
        <a-form labelAlign="left" class="solar-eye-search-content" @keyup.enter.native="searchQuery">
        <a-row :gutter="24">
        </a-row>
      </a-form>
    </div>
    <!-- 查询区域-END -->
<div class="solar-eye-main-content" :style="{height: tableHeight + 120 +'px'}">
    <!-- 操作按钮区域 -->
    <div class="operation" style="height: 32px" v-show="isAdmin">
        <div class="operation-btn">
    <!-- 操作按钮区域 -->
      <a-button @click="handleAdd" class="solar-eye-btn-primary">新增</a-button>
      <a-dropdown v-if="selectedRowKeys.length > 0">
        <a-menu slot="overlay">
          <a-menu-item key="1" @click="batchDel"><a-icon type="delete"/>删除</a-menu-item>
        </a-menu>
        <a-button style="margin-left: 8px"> 批量操作 <a-icon type="down" /></a-button>
      </a-dropdown>
    </div>
    </div>
    <!-- table区域-begin -->
      <div class="ant-alert ant-alert-info" style="margin-bottom: 16px;">
        <i class="anticon anticon-info-circle ant-alert-icon"></i> 已选择 <a style="font-weight: 600">{{ selectedRowKeys.length }}</a>项
        <a style="margin-left: 24px" @click="onClearSelected">清空</a>
      </div>

      <a-table
        ref="table"
        size="small"
        :scroll="{y: tableHeight - 77 -44 }"
        bordered
        rowKey="id"
        :columns="columns"
        :dataSource="dataSource"
        :pagination="ipagination"
        :loading="loading"
        :rowSelection="{selectedRowKeys: selectedRowKeys, onChange: onSelectChange}"

        @change="handleTableChange">

        <template slot="htmlSlot" slot-scope="text">
          <div v-html="text"></div>
        </template>
        <template slot="imgSlot" slot-scope="text">
          <span v-if="!text" style="font-size: 12px;font-style: italic;">无图片</span>
          <img v-else :src="getImgView(text)" height="25px" alt="" style="max-width:80px;font-size: 12px;font-style: italic;"/>
        </template>
        <template slot="fileSlot" slot-scope="text">
          <span v-if="!text" style="font-size: 12px;font-style: italic;">无文件</span>
          <a-button
            v-else
            :ghost="true"
            type="primary"

            size="small"
            @click="uploadFile(text)">
            下载
          </a-button>
        </template>
        <span slot="action" slot-scope="text, record">
          <template v-if="isAdmin">
            <a @click="handleEdit(record)">编辑</a>
            <a-divider type="vertical" />
          </template>
          <a-dropdown>
            <a class="ant-dropdown-link">更多 <a-icon type="down" /></a>
            <a-menu slot="overlay">
              <a-menu-item>
                <a @click="handleDetail(record)">详情</a>
              </a-menu-item>
              <a-menu-item v-show="isAdmin">
                <a-popconfirm title="确定删除吗?" @confirm="() => handleDelete(record.id)" okText="确定" cancelText="取消">
                  <a>删除</a>
                </a-popconfirm>
              </a-menu-item>
            </a-menu>
          </a-dropdown>
        </span>

      </a-table>
    </div>

    <tenant-modal ref="modalForm" @ok="modalFormOk"></tenant-modal>
  </div>
</template>

<script>

import '@/assets/less/TableExpand.less';
import { mixinDevice } from '@/utils/mixin';
import { SolarEyeListMixin } from '@/mixins/SolarEyeListMixin';
import TenantModal from './modules/TenantModal';
import { USER_NAME } from '@/store/mutation-types';
import tableHeight from '@/mixins/tableHeightAndSearchModel';
import { getTableHeight } from '@/utils/util.js';
import debounce from 'lodash/debounce';
export default {
  name: 'TenantList',
  mixins: [SolarEyeListMixin, mixinDevice, tableHeight],
  components: {
    TenantModal
  },
  data () {
    return {
      isAdmin: false,
      description: 'adad管理页面',
      // 表头
      columns: [
        {
          title: '租户名称',
          dataIndex: 'name'
        }, {
          title: '租户编号',
          dataIndex: 'id'
        },
        {
          title: '开始时间',
          dataIndex: 'beginDate'

        },
        {
          title: '结束时间',
          dataIndex: 'endDate'
        },
        {
          title: '状态',
          dataIndex: 'status_dictText',
          width: 160
        },
        {
          title: '操作',
          dataIndex: 'action',
          fixed: 'right',
          width: 147,
          scopedSlots: { customRender: 'action' }
        }
      ],
      url: {
        list: '/sys/tenant/list',
        delete: '/sys/tenant/delete',
        deleteBatch: '/sys/tenant/deleteBatch'
      },
      dictOptions: {}
    };
  },
  created () {
    let userName = Vue.ls.get(USER_NAME);
    this.isAdmin = (userName == 'admin');
  },
  watch: {
    'dataSource' () {
      this.setBodyMinHeight(this.dataSource.length, 120);
    },
    'tableHeight' () {
      this.setBodyMinHeight(this.dataSource.length, 120);
    }
  },
  mounted () {
    // 设置表格高度
    this.$nextTick(() => {
      this.tableHeight = getTableHeight(this.$el) + (!this.isAdmin ? 55 : 0);
    });
  },
  computed: {
    importExcelUrl: function () {
      return `${window._CONFIG['domianURL']}/${this.url.importExcelUrl}`;
    }
  },
  methods: {
    initDictConfig () {
    },
    heightResize: debounce(function () {
      this.tableHeight = getTableHeight(this.$el) + (!this.isAdmin ? 55 : 0);
    }, 400)
  }
};
</script>
<style scoped>
 ;
</style>
