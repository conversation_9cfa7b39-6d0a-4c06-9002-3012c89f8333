<template>
  <div class="materials-management-page" id="drawerViewDetail">
    <left-menu ref="leftMenu" v-model="show_menu" @menu-change="menuChange" :isNeedClose="isNeedClose" />
    <div class="right-content" v-bind:style="{'width': show_menu ? 'calc(100%)' : 'calc(100% - 200px)',marginLeft: show_menu ?0:'16px'}">
        <div v-if="!iframe_path">
            <template v-if="page_component">
            <component ref="pageComponent" v-bind:is="page_component"></component>
          </template>
        </div>
          <template v-else>
            <iframe-page-view v-if="!iframeView" :path="iframe_path" @close="closeCurrentMenu"/>
            <iframe-view v-if="iframeView" :path="iframe_path" @close="closeCurrentMenu" />
        </template>
    </div>
  </div>
</template>

<script>
import leftMenu from './modules/leftMenu';
import iframePageView from '@/components/layouts/IframePageView';
import iframeView from '@/components/layouts/IframeView';
export default {
  name: 'menuManage',
  components: {
    leftMenu,
    iframePageView,
    iframeView
  },
  watch: {
    show_menu () {
      this.$refs.pageComponent.refreshColumn();
    }
  },
  provide () {
    return {
      closeCurrentMenu: this.closeCurrentMenu
    };
  },
  data () {
    return {
      show_menu: false,
      page_component: null, // 台账页面路径
      form_component: null, // 表单页面路径
      iframe_path: null,
      isNeedClose: false,
      iframeView: false
    };
  },
  computed: {
  },
  methods: {
    /*
        菜单变化事件
      */
    menuChange (menu) {
      this.isNeedClose = false;
      this.iframeView = !this.iframeView;
      if (menu) {
        this.iframe_path = null;
        if (menu.component === 'layouts/IframePageView' || menu.component === 'layouts/IframeView') {
          this.page_component = this.form_component = null;
          this.iframe_path = menu;
          if (menu.component === 'layouts/IframeView') {
            this.iframeView = true;
          } else {
            this.iframeView = false;
          }
        } else {
          this.page_component = () => import('@/views/' + menu.component);
          setTimeout(() => this.getPageComponent(menu), 100);
        }
      }
    },
    getPageComponent (menu) {
      if (!this.$refs.pageComponent) {
        this.page_component = () => import('@/views/' + menu.component);
        setTimeout(() => this.getPageComponent(menu), 100);
      }
    },
    closeCurrentMenu () { // 打开新的标签，菜单默认选中上一个
      this.isNeedClose = true;
    }
  }
};
</script>

<style lang="less" scoped>
  .materials-management-page{
    width: 100%;
    height: 100%;
    display: flex;
    .right-content{
      height: 100%;
      overflow: hidden;
    }
  }
</style>
