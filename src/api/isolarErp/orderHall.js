import { axios } from '@/utils/erp-request';

export function gatPageList (data) {
  return axios({
    url: '/ops/order/page',
    method: 'POST',
    data
  });
}

// 工单详情
export function gatOrderDetail (data) {
  return axios({
    url: '/ops/order/detail',
    method: 'POST',
    data
  });
}

// 求和
export function getOrderCount (data) {
  return axios({
    url: '/ops/order/count',
    method: 'POST',
    data
  });
}
// 工单领取
export function orderReceive (data) {
  return axios({
    url: '/ops/order/assign',
    method: 'POST',
    data
  });
}

// 查询工单版本：1：new，反之 resolveOrderPath
export function getOrderVersion (data) {
  return axios({
    url: '/ops/order/version',
    method: 'POST',
    data
  });
}

// 工单执行
export function executeOrder (data) {
  return axios({
    url: '/ops/order/execute',
    method: 'POST',
    data
  });
}

// 工单验收
export function checkOrder (data) {
  return axios({
    url: '/ops/order/check',
    method: 'POST',
    data
  });
}

// 导出
export function exportOrderFile (data) {
  return axios({
    url: '/ops/order/exportDetail',
    method: 'POST',
    data
  });
}

// 获取缺陷/故障 等级
export function getDefectType (parameter) {
  return axios({
    url: '/defect/level/defectType',
    method: 'post',
    data: parameter
  });
}
