<template>
  <div class="alarmAnalysis">
    <a-drawer
      width="100%"
      :header-style="{ height: '55px', background: 'transparent' }"
      :visible="alarmAnalysisVisable"
      @close="onClose"
      :destroyOnClose="true"
      :get-container="getDom"
      :maskClosable="false"
      :wrap-style="{ position: 'absolute' }"
      :afterVisibleChange="afterVisibleChange"
      class="drawer-box"
      :class="{ 'no-parentId': !parentId }"
      title="详情"
    >
      <a-spin :spinning="loading">
        <a-icon class="health-clean-icon" title="清除日期" type="close" />
        <div class="content-div">
          <template v-if="!isSpecialId && !hasDetail">
            <detail-layout class="m-t-12" :labelList="baseInfoList" :form="detailData" title="基本信息" />
            <detail-layout class="m-t-12" :labelList="alarmInfoList" :form="detailData" title="诊断信息" />
            <detail-layout class="m-t-12" :labelList="[]" :form="{}" title="时序分析" />
          </template>
          <div class="search-head flex-start">
            <template v-if="isSpecialId || hasDetail">
              <span class="device-name">{{ defaultInfo.deviceNameShow }}</span>
              <span class="device-name">电站：{{ defaultInfo.psName }}</span>
            </template>
            <div class="search-div">
              <span class="search-label">日期：</span>
              <a-range-picker
                class="search-date"
                :disabledDate="disabledDate"
                @openChange="openChange"
                @change="timeDateChange"
                @calendarChange="calendarChange"
                v-model="sequenceTime"
                :allowClear="!isOpticalPowerPredictionRemark"
                format="YYYY-MM-DD"
                :disabled="isSpecialId"
              >
                <template slot="dateRender" slot-scope="current">
                  <div class="my-date">
                    <div :class="getTopicalClassName(current)"></div>
                    <div :class="`ant-calendar-date ${getCurrentClassName(current)}`">
                      {{ current.date() }}
                    </div>
                  </div>
                </template>
              </a-range-picker>
            </div>
            <div class="search-div">
              <span class="search-label">时间间隔：</span>
              <a-select
                class="search-time"
                v-model="timeQueryData.timeInterval"
                show-search
                @change="getTimeAnalysisData(timeQueryData)"
                :options="timeIntervalOptions"
                placeholder="请选择"
                :disabled="isSpecialId || isOpticalPowerPredictionRemark"
              >
              </a-select>
            </div>
            <!-- 天气信息 -->
            <div
              class="search-div"
              v-if="Object.keys(weatherInfo).length > 0 && weatherData.length > 0"
            >
              <div class="weather-info">
                <a-popover placement="rightTop" trigger="click">
                  <a-space>
                    <a-col style="height: 30px">
                      <img v-if="weatherInfo.iconDir" :src="weatherInfo.iconDir" class="weather-icon" />
                    </a-col>
                    <span v-if="weatherInfo.tempNight + '' || weatherInfo.tempDay + ''"
                      >{{ weatherInfo.tempNight }}℃~{{ weatherInfo.tempDay }}℃</span
                    >
                    <span>|</span><span>{{ weatherInfo.city.name }}</span>
                  </a-space>
                  <template slot="content">
                    <div class="weather-popper">
                      <vxe-table
                        :data="weatherData"
                        :maxHeight="260"
                        ref="weatherTable"
                        align="left"
                        resizable
                        border="none"
                        show-overflow
                        highlight-hover-row
                        size="small"
                        class="my-table"
                      >
                        <vxe-table-column show-overflow="title" field="predictDate" title="日期" width="150">
                        </vxe-table-column>
                        <vxe-table-column show-overflow="title" field="conditionDay" title="天气" width="150">
                          <template v-slot="{ row }">
                            <div class="weather-row">
                              <img :src="mapWeatherIconById(row.conditionIdDay)" class="weather-icon" />
                              <span>{{ row.conditionDay }}</span>
                            </div>
                          </template>
                        </vxe-table-column>
                        <vxe-table-column show-overflow="title" field="tempDay" title="气温[℃]" width="150">
                          <template v-slot="{ row }">
                            <div class="weather-row">
                              <span v-if="row.tempNight + '' || row.tempDay + ''"
                                >{{ row.tempNight }}℃~{{ row.tempDay }}℃</span
                              >
                            </div>
                          </template>
                        </vxe-table-column>
                      </vxe-table>
                    </div>
                  </template>
                </a-popover>
              </div>
            </div>
            <throttle-button
              class="solar-eye-btn-primary-cancel"
              v-if="isShowMenu && !isSpecialId"
              label="自定义分析"
              @click="gotoDeepAnalysis"
            />
          </div>
          <div v-if="hasDetail && !isSpecialId" class="detail-info">
            <div v-for="item in detailItems" class="detail-item" :key="item.prop">
              <div class="lable">{{ item.label }}</div>
              <div class="value">{{ detailInfo[item.prop] || '--' }} {{ item.unit || '' }}</div>
            </div>
          </div>
          <!-- 时序分析页面 -->
          <div
            v-if="!isSpecialId"
            class="chart-content"
            :style="{ height: hasDetail ? 'calc(100vh - 415px)' : '544px' }"
          >
            <!-- 时序分析图表 -->
            <SevenDayChart
              v-if="isOpticalPowerPredictionRemark"
              :tipsData="opticalPowerPredictionList"
              :dataResult="opticalPowerPredictionData"
              height="100%"
            />
            <time-analysis-chart v-else ref="timeAnalysisChart" :hasDetail="hasDetail"></time-analysis-chart>
          </div>
          <div class="img-div" v-else>
            <img src="../../../../assets/images/health/alarmEvents/special-detail.png" />
          </div>
          <template v-if="!isSpecialId && !hasDetail">
            <detail-layout class="m-t-12" :labelList="conclusionList" :form="detailData" title="诊断结论及建议" >
              <template v-slot:alarmOpinion>
                <a-col :span="16" class="detail_layout_content" style="line-height: 23px">
                  <span class="left">处理建议</span>
                  <span class="right" style="white-space: pre-line">{{ detailData.alarmOpinion || '--' }}</span>
                </a-col>
              </template>
            </detail-layout>
            <detail-layout
              v-if="alarmStatus == '04'"
              class="m-t-12"
              :labelList="dealList"
              :form="dealInfo"
              title="现场处理"
            >
              <template v-slot:workCode>
                <a-col :span="8" class="detail_layout_content">
                  <span class="left">工单编号</span>
                  <span
                    v-if="dealInfo.workCode"
                    class="right blue cursor-pointer"
                    @click="workOrderDetail(defaultInfo, 'alarm')"
                    >{{ dealInfo.workCode }}</span
                  >
                  <span class="right" v-else>--</span>
                </a-col>
              </template>
            </detail-layout>
            <template v-if="alarmStatus == '04' || defaultInfo.faultStatus == '2'">
              <detail-layout class="m-t-12" :labelList="resultList" :form="detailData" title="处理结果验证">
                <template v-slot:disappearTime>
                  <a-col :span="8" class="detail_layout_content">
                    <span class="left">恢复时间</span>
                    <span class="right">{{ dealInfo.faultStatus == '2' ? dealInfo.disappearTime : '--' }}</span>
                  </a-col>
                </template>
              </detail-layout>
                <div class="chart-content" :style="'calc(100vh - 233px)'" v-if="!isOpticalPowerPredictionRemark">
                <!-- 时序分析图表 -->
                <time-analysis-chart
                  ref="resultAnalysisChart"
                  id="resultAnalysisChart"
                  :hasDetail="hasDetail"
                ></time-analysis-chart>
              </div>
            </template>
          </template>
        </div>
        <div class="footer-div">
          <throttle-button class="solar-eye-btn-primary-cancel" label="返回" @click="onClose" />
          <throttle-button
            v-if="['01', '02'].includes(alarmStatus) && showHandle('910108101106') && showOperationButton"
            class="solar-eye-btn-primary-cancel"
            label="删除"
            @click="doDelete(defaultInfo, '2')"
          />
          <throttle-button
            v-if="alarmStatus == '01' && showOperationButton && showHandle('910108101102') && showOperationButton"
            :disabled="defaultInfo.psCategory && defaultInfo.psCategory.includes('Storage')"
            label="指派"
            @click="batchDistributeEvent(defaultInfo, '2', '4')"
          />
        </div>
      </a-spin>
    </a-drawer>
    <order-detail ref="orderForm" parent-id="deviceModule"/>
    <!--  户用工单详情  -->
    <drawer-view ref="farmOrderDrawer" parentId="deviceModule" />
  </div>
</template>
<script>
import moment from 'moment';
import { mapGetters } from 'vuex';
import { cloneDeep } from 'lodash';
import {
  faultCurve,
  getDeviceAlarmCalendar,
  getFaultTimeRange,
  getWeatherByTimeRange,
  diagnosisDetail
} from '@/api/health/AlarmEvents.js';
import { getPowerStationTopicalDay, getModelData } from '@/api/health/healthapi.js';
import { LeftMixin } from '@/mixins/LeftMixin';
import initDict from '@/mixins/initDict';
import { AlarmEventMixins } from '../../mixins/alarmEventMixins';
import {
  baseInfoList,
  alarmInfoList,
  conclusionList,
  dealList,
  resultList,
  tempPredictionPoints,
  demonstrateIds
} from '../../config';
import TimeAnalysisChart from './TimeAnalysisChart';
import OrderDetail from './OrderDetail';
import SevenDayChart from './SevenDayChart';

export default {
  name: 'AlarmAnalysis',
  mixins: [initDict, LeftMixin, AlarmEventMixins],
  components: { SevenDayChart, TimeAnalysisChart, OrderDetail },
  props: {
    type: {
      type: String,
      default: '1'
    },
    deviceType: {
      type: String,
      default: ''
    },
    parentId: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      baseInfoList,
      alarmInfoList,
      conclusionList,
      dealList,
      resultList,
      detailData: {},
      dealInfo: {},
      orderSteps: [],
      alarmAnalysisVisable: false,
      loading: false,
      weatherInfo: {},
      weatherData: [],
      showOperationButton: true,
      // 工作台列表数据进入的默认信息
      defaultInfo: '',
      alarmStatus: '',
      // 时序分析 - 查询条件变量
      timeOptionRange: [],
      alarmDays: [],
      topicalDays: [],
      timeIntervalOptions: [],
      timeIntervalOptionsAll: {
        day: [
          {
            label: '1min',
            value: '1'
          },
          {
            label: '5min',
            value: '5'
          },
          {
            label: '15min',
            value: '15'
          },
          {
            label: '30min',
            value: '30'
          },
          {
            label: '60min',
            value: '60'
          }
        ],
        week: [
          {
            label: '15min',
            value: '15'
          },
          {
            label: '30min',
            value: '30'
          },
          {
            label: '60min',
            value: '60'
          }
        ]
      },
      sequenceTime: [],
      timeQueryData: {},
      // 时序分析-分析图表变量
      remoteCount: 0,
      unitArr: [],
      stopPoint: undefined,
      detailInfo: {},
      hasDetail: false,
      detailItems: [
        { label: '局放峰值：', prop: 'p53036', unit: 'dBmV' },
        { label: '局放有效值：', prop: 'p53037', unit: 'dBmV' },
        { label: '放电脉冲数：', prop: 'p53038' },
        { label: '局放峰值相位：', prop: 'p53039', unit: '°' },
        { label: '局放噪声水平：', prop: 'p53040', unit: 'dBmV' },
        { label: '局放严重程度：', prop: 'p53042' },
        { label: '放电概率：', prop: 'p53043', unit: '%' },
        { label: '局放类型：', prop: 'p53044' }
      ],
      isSpecialId: false,
      loadingNum: 0,
      opticalPowerPredictionList: [
        {
          key: 'shortSevenPrecision',
          title: '短期准确率',
          multiple: 1,
          toFixed: 1,
          gradient: ['rgba(87, 216, 211, 0.66)', 'rgba(51, 159, 154, 1)']
        },
        {
          key: 'superShortPrecision',
          title: '超短期准确率',
          multiple: 1,
          toFixed: 1,
          gradient: ['rgba(249, 116, 117, 0.66)', 'rgba(184, 80, 80, 1)']
        },
        {
          key: 'middleSevenPrecision',
          title: '中期7点准确率',
          multiple: 1,
          toFixed: 1,
          gradient: ['rgba(162, 183, 202, 0.66)', 'rgba(97, 131, 182, 1)']
        },
        {
          key: 'middleTwelvePrecision',
          title: '中期13点准确率',
          multiple: 1,
          toFixed: 1,
          gradient: ['rgba(182, 173, 252, 0.66)', 'rgba(125, 141, 247, 1)']
        },
        {
          key: 'shortUploadRate',
          title: '短期上报率',
          multiple: 1,
          toFixed: 1,
          gradient: ['rgba(255, 143, 82, 0.66)', 'rgba(214, 124, 68, 1)']
        },
        {
          key: 'superShortUploadRate',
          title: '超短期上报率',
          multiple: 1,
          toFixed: 1,
          gradient: ['rgba(255, 176, 59, 0.66)', 'rgba(185, 128, 52, 1)']
        },
        {
          key: 'middleUploadRate',
          title: '中期上报率',
          multiple: 1,
          toFixed: 1,
          gradient: ['rgba(64, 170, 255, 0.66)', 'rgba(46, 129, 212, 1)']
        }
      ],
      opticalPowerPredictionData: []
    };
  },
  computed: {
    // 监听换肤颜色变化
    navTheme() {
      return this.$store.state.app.theme;
    },
    isShowMenu() {
      return this.$store.state.user.isShowMenu;
    },
    // 判断是否低效缺陷的电站低效
    isLowEfficiencyPs() {
      let { defaultInfo } = this;
      return defaultInfo.alarmGrade == '4' && defaultInfo.deviceType == '11';
    },
    // 判断是否为故障停机的组串故障
    isFaultStopByZc() {
      let { defaultInfo } = this;
      return defaultInfo.alarmGrade == '1' && defaultInfo.deviceType == '10';
    },
    isOpticalPowerPredictionRemark() {
      return this.defaultInfo.alarmRemark === '104';
    },
    computedTimeInterval() {
      const setValue = this.configDataFrequency || '5';
      // 功率因数特殊处理
      return this.defaultInfo.alarmRemark === '106' ? '5' : setValue;
    },
    ...mapGetters(['configDataFrequency'])
  },

  methods: {
    moment,
    // 将抽屉组件挂载到对应的父组件上
    getDom() {
      let parentId = this.parentId;
      if (!parentId) return document.querySelector('.main-parent');
      let mains = document.querySelectorAll('.main');
      return (
        document.getElementById(parentId) ||
        (mains.length ? mains[mains.length - 1] : document.querySelector('.main-parent'))
      );
    },
    // 页面初始化变量， alarmStatus: 1 待处理  2 已隔离  3 已派发  4 已闭环
    init(row, alarmStatus) {
      this.alarmAnalysisVisable = true;
      this.loadingNum = 0;
      this.isSpecialId = row.id == 231432912;
      this.defaultInfo = row;
      this.alarmStatus = alarmStatus || row.alarmStatus;
      // 时序分析-初始化查询条件
      this.timeIntervalOptions = cloneDeep(this.timeIntervalOptionsAll.day);
      const setValue = this.configDataFrequency;
      if (setValue != '1') this.timeIntervalOptions.splice(0, 1);
      this.timeQueryData.timeInterval = this.computedTimeInterval;
      this.timeQueryData = {
        id: this.defaultInfo.id,
        psKey: this.defaultInfo.psKey,
        timeInterval: this.computedTimeInterval,
        startTime: '',
        endTime: '',
        pointList: [],
        isInitialization: true,
        psId: this.defaultInfo.psId
      };
      this.sequenceTime = [moment(row.lastHappenTime || row.happenTime), moment(new Date())];
      this.timeOptionRange = [];
      if (demonstrateIds.includes(row.id)) {
        let dayIntervel = moment(row.disappearTime).diff(this.sequenceTime[0], 'day');
        if (dayIntervel >= 0) {
          this.sequenceTime[1] = moment(row.disappearTime);
        } else {
          this.setSequenceTime(row);
        }
      } else {
        this.setSequenceTime(row);
      }
      this.timeQueryData.startTime = this.sequenceTime[0].format('YYYY-MM-DD');
      this.timeQueryData.endTime = this.sequenceTime[1].format('YYYY-MM-DD');
    },
    // 时序分析，设置查询条件日期值
    setSequenceTime(row) {
      let dayIntervel = this.sequenceTime[1].diff(this.sequenceTime[0], 'day');
      if (dayIntervel > 7) {
        let temp = this.sequenceTime[0].format('YYYY-MM-DD');
        this.sequenceTime = [moment(temp), moment(temp).add(7, 'days')];
      }
      // 低效，电站下日期区间是前7天 (有更新时间用更新时间往前推7天，没有更新时间用发生时间往前推7天)
      if (this.isLowEfficiencyPs) {
        let time = row.lastHappenTime || row.happenTime;
        this.sequenceTime = [moment(time).subtract(6, 'days'), moment(time)];
      }
      if (dayIntervel > 0) {
        this.timeIntervalOptions = cloneDeep(this.timeIntervalOptionsAll.day);
        const setValue = this.configDataFrequency;
        if (setValue != '1') this.timeIntervalOptions.splice(0, 1);
        this.timeQueryData.timeInterval = this.computedTimeInterval;
      }
    },
    // 抽屉组件切换动画结束后事件，异步请求事件须放在这里执行，否则可能会导致页面滑动卡死
    async afterVisibleChange() {
      if (this.alarmAnalysisVisable) {
        this.loading = true;
        // 时序分析-获取天气信息
        this.getWeatherInfo(this.defaultInfo.psId);
        if (this.isSpecialId) {
          return;
        }
        // 时序分析-获取故障日期
        this.getAlarmDay(this.defaultInfo);
        // 时序分析-获取典型日
        this.getTopicalDay(this.defaultInfo);
        // 故障停机 组串
        let setEventFlag = this.isFaultStopByZc;
        if (this.isOpticalPowerPredictionRemark) {
          const { lastHappenTime, happenTime } = this.defaultInfo;
          let endDate = moment(lastHappenTime || happenTime).add(7, 'days').format('YYYY-MM-DD');
          let isOverToday = moment(endDate).diff(moment(), 'days');
          if (isOverToday > 0) {
            endDate = moment().format('YYYY-MM-DD');
          }
          const diffDays = moment().diff(this.sequenceTime[0], 'days');
          this.getOpticalPowerPredictionData(endDate, diffDays + 1);
        } else {
          this.getTimeAnalysisData(this.timeQueryData);
          // 时序分析、联动分析图表初始化
          this.$refs.timeAnalysisChart.drawLine(setEventFlag);
        }
        let hasResultAnalysis = this.alarmStatus == '04' || this.defaultInfo.faultStatus == '2';
        if (hasResultAnalysis) {
          this.$nextTick(() => {
            this.$refs.resultAnalysisChart.drawLine(setEventFlag);
          });
        }
        // 获取诊断详情
        this.getDiagnosisDetail(hasResultAnalysis);
      }
    },
    // 获取光功率考核预警数据
    getOpticalPowerPredictionData(endDate, distance) {
      this.loading = true;
      getModelData({
        psId: this.defaultInfo.psId,
        modelName: 'opticalPowerPredictionSevenDay',
        psKey: '107353_60_1_1',
        recordDate: endDate,
        luminousDurationDay: distance,
        deviceType: '60',
        points: 'time_stamp,fileType,fileName,stopCap,cap,rov,directRadiation'
      })
        .then((res) => {
          this.opticalPowerPredictionData = res.result_data;
        })
        .finally(() => {
          this.loading = false;
        });
    },
    // 获取诊断详情
    getDiagnosisDetail(hasResultAnalysis) {
      diagnosisDetail({ id: this.defaultInfo.id })
        .then((res) => {
          this.alarmStatus = res.alarmStatus;
          this.defaultInfo.faultStatus = res.faultStatus;
          this.detailData = res;
          let arr = res.workOrderStepList;
          let hasWorkStep = arr && arr.length > 0;
          let len = hasWorkStep ? arr.length : 0;
          this.dealInfo = {
            ...res.execute,
            workCode: res.workOrderBaseInfo ? res.workOrderBaseInfo.workCode : null,
            workDesc: hasWorkStep ? arr[len - 1].workDesc : null,
            // stepPictureList: hasWorkStep ? [ {path: arr[len - 1].stepPictureList[0]}] : [],
            stepPictureList: hasWorkStep ? arr[len - 1].stepPictureList.map((o) => ({ path: o })) : [],
            finishedTime: res.finishedTime,
            faultStatus: res.faultStatus,
            disappearTime: res.disappearTime
          };
          if (hasResultAnalysis && !this.isOpticalPowerPredictionRemark) {
            // faultStatus 2 恢复 1 持续
            const time = res.faultStatus == 2 ? res.disappearTime : res.finishedTime || new Date();
            let obj = Object.assign({}, this.timeQueryData, {
              startTime: moment(time).format('YYYY-MM-DD'),
              endTime: moment(time).format('YYYY-MM-DD')
            });
            this.getTimeAnalysisData(obj, hasResultAnalysis);
          } else {
            this.loadingNum++;
            if (this.loadingNum > 1) {
              this.loading = false;
            }
          }
        })
        .catch(() => {
          this.loading = false;
        });
    },
    // 关闭取消功能、重置数据
    onClose(flag) {
      this.alarmAnalysisVisable = false;
      this.showOperationButton = true;
      // 测点树、设备树、天气信息-重置变量
      this.pointArr = [];
      this.weatherInfo = {};
      this.weatherData = [];
      // 时序分析-重置变量
      this.remoteCount = 0;
      this.remotePoint = {};
      this.hasDetail = false;
      this.detailInfo = {};
      this.detailData = {};
      // 销毁时序分析图表实例
      if (this.$refs.timeAnalysisChart) {
        this.$refs.timeAnalysisChart.destroyChart();
      }
      this.$emit('alarmAnalysisClose', flag);
    },
    // 时序分析-获取故障日期
    getAlarmDay(row) {
      // 请求查询接口
      this.alarmDays = [];
      getDeviceAlarmCalendar({ psKey: row.psKey }).then((res) => {
        if (res.result_code === '1') {
          if (res.result_data) {
            res.result_data.forEach((item) => {
              this.alarmDays.push(moment(item).format('YYYY/MM/DD'));
            });
          }
        }
      });
    },
    // 时序分析-获取典型日
    getTopicalDay(row) {
      // 请求查询接口
      this.topicalDays = [];
      let paramsObj = {
        psId: row.psId, // 电站id
        startTopicalDay: '', // 典型日开始时间
        endTopicalDay: '' // 典型日结束时间
      };
      getPowerStationTopicalDay(paramsObj).then((res) => {
        if (res.result_code === '1') {
          if (res.result_data) {
            res.result_data.forEach((item) => {
              this.topicalDays.push(moment(item).format('YYYY/MM/DD'));
            });
          }
        }
      });
    },
    // 时序分析-获取天气信息
    getWeatherInfo(psId) {
      let obj = {
        psId: psId,
        startDate: this.timeQueryData.startTime || this.defaultInfo.happenTime.substring(0, 10),
        endDate: this.timeQueryData.endTime || this.defaultInfo.happenTime.substring(0, 10)
      };
      getWeatherByTimeRange(obj)
        .then((res) => {
          if (Object.keys(res.result_data).length > 0) {
            this.weatherInfo.city = res.result_data.city;
            this.weatherData = res.result_data.weather.map((item) => {
              if (
                (this.timeQueryData.endTime || this.defaultInfo.happenTime.substring(0, 10)) ==
                item.timeDate.substring(0, 10)
              ) {
                this.weatherInfo.tempDay = item.tempDay;
                this.weatherInfo.tempNight = item.tempNight;
                this.weatherInfo.iconDir = this.mapWeatherIconById(item.conditionIdDay);
              }
              return {
                predictDate: item.timeDate.substring(0, 10),
                conditionDay: item.conditionDay,
                conditionIdDay: item.conditionIdDay,
                tempDay: item.tempDay,
                tempNight: item.tempNight
              };
            });
          } else {
            this.weatherData = [];
          }
          this.$forceUpdate();
          if (this.$refs.weatherTable) {
            this.$refs.weatherTable.refreshColumn();
          }
          if (this.isSpecialId) {
            this.loading = false;
          }
        })
        .catch(() => {
          if (this.isSpecialId) {
            this.loading = false;
          }
        });
    },
    // 时序分析-获取天气图标
    mapWeatherIconById(conditionId) {
      if (conditionId >= 0) {
        return require(`@/assets/images/weather/W${conditionId}.png`);
      }
      return null;
    },
    // 时序分析-获取分析测点数据
    async getTimeAnalysisData(pramas, isResultAnalysis) {
      if (!this.loading) {
        this.loading = true;
      }
      pramas.psId = this.timeQueryData.psId;
      pramas.source = this.defaultInfo.source;
      this.hasDetail = ['77', '78'].includes(this.defaultInfo.alarmRemark);
      // 分析数据
      let timeDataResult = await faultCurve(pramas).catch(() => {
        this.loading = false;
      });
      if (this.hasDetail) {
        this.detailInfo = timeDataResult.result_data.detail;
      }
      // 故障区间数据
      let faultTimeRangeResult = await getFaultTimeRange({
        ...pramas,
        alarmReason: this.defaultInfo.alarmReason
      }).catch(() => {
        this.loading = false;
      });
      if (!timeDataResult) {
        this.loading = false;
        return;
      }
      let xAxisData = timeDataResult.result_data.dataX.map((item) => {
        return item.split(' ')[0] + '\n' + item.split(' ')[1];
      });
      let dataY = timeDataResult.result_data.dataY;
      // 初始进入界面时，取后台返回的默认测点
      if (this.timeQueryData.isInitialization || isResultAnalysis) {
        this.timeQueryData.pointList = timeDataResult.result_data.pointList;
        // 遥信数据需单独记录返回的遥信测点信息（1是遥信 2是遥测）
        if (this.defaultInfo.pointType == '1') {
          this.timeQueryData.pointList.forEach((item) => {
            if (item.pointType == '1') {
              this.remoteCount = 1;
              this.remotePoint = item;
            }
          });
          timeDataResult.result_data.dataY.forEach((item) => {
            if (item.point == this.remotePoint.point) {
              this.remotePoint.pointName = item.pointName;
              this.remotePoint.unit = item.unit;
              this.remotePoint.deviceName = item.deviceName;
            }
          });
        }
        this.timeQueryData.pointList.forEach((item) => {
          if (item.pointType == '3') {
            this.stopPoint = item;
          }
        });
        if (this.stopPoint) {
          timeDataResult.result_data.dataY.forEach((item) => {
            if (item.point == this.stopPoint.point) {
              this.stopPoint.pointName = item.pointName;
              this.stopPoint.unit = item.unit;
              this.stopPoint.deviceName = item.deviceName;
            }
          });
        }
        this.timeQueryData.isInitialization = false;
      }
      const { legendData, irradiateCount, faultPointCount, enableCount } = this.getLegendData(dataY);
      let legend = this.getLegend(legendData);
      this.legendData = legendData;
      // 清除上个图表内容，重新绘制图表
      if (isResultAnalysis) {
        if (this.$refs.resultAnalysisChart) {
          this.$refs.resultAnalysisChart.destroyChart();
        }
      } else {
        if (this.$refs.timeAnalysisChart) {
          this.$refs.timeAnalysisChart.destroyChart();
        }
      }
      let setEventFlag = this.isFaultStopByZc;
      let isExistChart = isResultAnalysis ? this.$refs.resultAnalysisChart : this.$refs.timeAnalysisChart;
      isExistChart && isExistChart.drawLine(setEventFlag);
      if (legendData.length > 0 && dataY.length > 0) {
        const { series, faultArr } = this.getSeries(legendData, dataY, xAxisData, faultTimeRangeResult);
        let chartData = {
          xAxisData: xAxisData,
          legend: legend,
          series: series,
          faultArr: faultArr,
          alarmType: this.defaultInfo.alarmType,
          irradiateCount: irradiateCount,
          faultPointCount: faultPointCount,
          enableCount: enableCount,
          unitArr: this.unitArr,
          remoteCount: this.remoteCount,
          legendData: legendData[0].data,
          deviceType: this.defaultInfo.deviceTypeShow,
          type: this.type,
          id: this.defaultInfo.id
        };
        isExistChart && isExistChart.refreshChart(chartData);
      }
      // 等详情接口或者恢复的时序分析图接口返回数据后，再取消loading
      this.loadingNum++;
      if (this.loadingNum > 1) {
        this.loading = false;
      }
    },
    // 时序分析-图表-获取图标legend对象数组
    getLegendData(dataY) {
      let data = [];
      let irradiateCount = 0;
      let faultPointCount = 0;
      let enableCount = 0;
      // 斜面瞬时辐照测点信息
      let irradiateInfo = {};
      let irradiateFlag = false;
      // Y轴单位数组
      this.unitArr = [];
      let defaultDevicePointArr = []; // 默认设备测点数据
      let faultPointArr = []; // 故障测点数据
      let enableArr = []; // 组串未接测点数据
      let otherDevicePointArr = []; // 其他设备测点数据
      let { alarmRemark, psKey, deviceType } = this.defaultInfo;
      dataY.forEach((item) => {
        if (
          (item.psKey.split('_')[1] == '1' ||
            ['36', '37', '39', '42', '43'].includes(alarmRemark) ||
            ['56', '11'].includes(deviceType)) &&
          psKey != item.psKey
        ) {
          otherDevicePointArr.push({
            psKey: item.psKey,
            points: []
          });
        }
      });
      otherDevicePointArr = this.arrUnique(otherDevicePointArr, 'psKey') || [];
      this.timeQueryData.pointList.forEach((ele) => {
        dataY.forEach((v) => {
          if (v.point == ele.point && v.psKey == ele.psKey) {
            ele.pointName = v.pointName;
            ele.unit = v.unit;
            if (ele.point == 'p2007') {
              irradiateFlag = true;
              irradiateInfo = {
                deviceName: v.deviceName,
                pointName: v.pointName,
                psKey: v.psKey,
                unit: v.unit,
                point: ele.point
              };
              irradiateCount = 1;
              // 组串故障时，故障测点排在前面，并记录个数
            } else if ((alarmRemark == '2' || alarmRemark == '3' || alarmRemark == '4') && v.isFaultPoint) {
              faultPointArr.push({
                deviceName: v.deviceName,
                pointName: v.pointName,
                psKey: v.psKey,
                unit: v.unit,
                point: ele.point
              });
              faultPointCount++;
            } else if (ele.enable === '0') {
              // 组串未接
              enableArr.push({
                deviceName: v.deviceName,
                pointName: v.pointName,
                psKey: v.psKey,
                unit: v.unit,
                point: ele.point
              });
              enableCount++;
            } else if (
              v.psKey == psKey &&
              ['1', '4', '6', '7', '301', '5', '26', '23', '24', '37', '56'].includes(deviceType) &&
              v.point != 'stop'
            ) {
              defaultDevicePointArr.push({
                deviceName: v.deviceName,
                pointName: v.pointName,
                psKey: v.psKey,
                unit: v.unit,
                point: ele.point
              });
            } else {
              otherDevicePointArr.forEach((item) => {
                if (item.psKey == v.psKey) {
                  item.points.push({
                    deviceName: v.deviceName,
                    pointName: v.pointName,
                    psKey: v.psKey,
                    unit: v.unit,
                    point: ele.point
                  });
                }
              });
            }
            if (this.unitArr.indexOf(v.unit) == -1) {
              this.unitArr.push(v.unit);
            }
          }
        });
      });
      let arr = [];

      otherDevicePointArr.forEach((item) => {
        arr = [...arr, ...item.points];
      });
      // 按故障测点数据、默认设备测点数据、其他设备测点数据排序、未接组串测点数据
      data = [...faultPointArr, ...defaultDevicePointArr, ...arr, ...enableArr];
      // 遥信测点排在第二位
      if (this.defaultInfo.pointType == '1') {
        data.unshift(this.remotePoint);
        if (this.unitArr.indexOf(this.remotePoint.unit) == -1) {
          this.unitArr.push(this.remotePoint.unit);
        }
      }

      // 斜面瞬时辐照排在第一位
      if (irradiateFlag) {
        data.unshift(irradiateInfo);
      }
      if (this.stopPoint && data.length == 0) {
        data.push(this.stopPoint);
        if (this.unitArr.indexOf(this.stopPoint.unit) == -1) {
          this.unitArr.push(this.stopPoint.unit);
        }
      }
      return {
        legendData: [{ data: data }],
        irradiateCount: irradiateCount,
        faultPointCount: faultPointCount,
        enableCount: enableCount
      };
    },
    // 时序分析-图表-获取图标legend数组
    getLegend(legendData) {
      let data = [];
      legendData.forEach((item, index) => {
        item.data.forEach((ele) => {
          if (ele.point != 'stop') {
            data.push(ele.deviceName + ele.pointName);
          }
        });
      });
      let obj = { data: data, type: 'scroll', y: 10 };
      let isDark = this.navTheme == 'dark';
      let isDarkColor = isDark ? '#ffffff' : '#9b9b9b';
      let isNeedObj = {
        textStyle: {
          color: isDarkColor
        },
        pageIconColor: isDarkColor,
        pageTextStyle: {
          color: isDarkColor
        }
      };
      let realObj = Object.assign({}, obj, isNeedObj);

      return [realObj];
    },
    // 时序分析-图表-获取series数组、故障区间组
    getSeries(legend, dataY, dataX, faultTimeRangeResult) {
      let series = [];
      let faultArr = [];
      // 根据legend中data个数，设置series数组
      let isLowEfficiencyPs = this.isLowEfficiencyPs;

      legend.length > 0 &&
        legend[0].data.forEach((ele, key) => {
          let data = [];
          let statusSignArr = [];
          // 处理接口返回的y轴数据
          dataY.forEach((val) => {
            if (ele.pointName == val.pointName && ele.deviceName == val.deviceName) {
              let list = [];
              val.pointValue.forEach((element, index) => {
                // 获取故障持续时间、时间段，低效缺陷-电站 不展示故障
                let result = isLowEfficiencyPs
                  ? {
                    alarmArr: [],
                    faultArr: []
                  }
                  : this.getDurationOrMarkAreaArr(faultTimeRangeResult, index, dataX, true, faultArr);
                let alarmArr = result.alarmArr;
                faultArr = result.faultArr;
                list.push({
                  value: element,
                  unit: val.unit,
                  point: val.point,
                  alarmArr: alarmArr,
                  pointName: val.pointName,
                  deviceName: val.deviceName,
                  isFaultPoint: val.isFaultPoint,
                  pointType: val.pointType
                });
              });
              data = list;
            }
            // 电站状态信息数组
            if (val.point == 'statusSign') {
              statusSignArr = val.pointValue;
            }
          });
          data.forEach((v, i) => {
            v.statusSign = statusSignArr[i];
          });
          // 获取故障区间遮罩层区域
          // 故障停机-组串，低效缺陷-电站 不展示区间
          let markAreaArr = [];
          if (!(this.isFaultStopByZc || isLowEfficiencyPs)) {
            markAreaArr = this.getDurationOrMarkAreaArr(faultTimeRangeResult, null, dataX, false, faultArr);
          }
          series.push({
            type: 'line',
            large: true,
            name: ele.deviceName + ele.pointName,
            data: data,
            smooth: true,
            showSymbol: false,
            lineStyle: {
              width: 1
            },
            itemStyle: {
              color: tempPredictionPoints.includes(ele.point) ? '#f00' : null
            },
            emphasis: {
              focus: 'series'
            },
            z: legend[0].data.length - key,
            markArea: {
              itemStyle: {
                color: {
                  type: 'linear',
                  x: 0,
                  y: 0,
                  x2: 0,
                  y2: 1,
                  colorStops: [
                    {
                      offset: 0,
                      color: 'rgba(211, 0, 0, 0)' // 0% 处的颜色
                    },
                    {
                      offset: 1,
                      color: 'rgba(206, 0, 0, 0.26)' // 100% 处的颜色
                    }
                  ],
                  global: false // 缺省为 false
                },
                opacity: key == 0 ? 1 : 0
              },
              data: markAreaArr,
              emphasis: {
                itemStyle: {
                  color: {
                    type: 'linear',
                    x: 0,
                    y: 0,
                    x2: 0,
                    y2: 1,
                    colorStops: [
                      {
                        offset: 0,
                        color: 'rgba(245, 108, 108, 0)' // 0% 处的颜色
                      },
                      {
                        offset: 1,
                        color: '#F56C6C' // 100% 处的颜色
                      }
                    ],
                    global: false // 缺省为 false
                  },
                  opacity: key == 0 ? 0.2 : 0
                }
              }
            },
            yAxisIndex: this.unitArr.indexOf(ele.unit)
          });
        });
      return {
        series: series,
        faultArr: faultArr
      };
    },
    // 时序分析-图表-获取故障区间段信息或者故障区间遮罩层区域，flag为ture时为获取故障时间段信息，false时为获取遮罩区域
    getDurationOrMarkAreaArr(res, index, dataX, flag, faultArr) {
      let alarmArr = [];
      let markAreaArr = [];
      res.result_data.forEach((ele) => {
        let xTime = flag ? new Date(dataX[index]) : '';
        let happenTime = new Date(ele.happenTime);
        let endTime = ele.endTime ? new Date(ele.endTime) : new Date();
        let arr = [];
        if (flag) {
          let duration = '';
          if (xTime >= happenTime && xTime <= endTime) {
            if (ele.endTime) {
              duration = ele.duration + 'h';
            } else {
              duration = ((endTime - happenTime) / 3600000).toFixed(2) + 'h';
            }
            // obj.powerLoss = ele.hasOwnProperty('powerLoss') ?ele.powerLoss : '--'
            faultArr.push(dataX[index]);
            alarmArr.push({
              alarmType: ele.alarmType,
              alarmLevel: ele.alarmLevel || '--',
              alarmRemark: ele.alarmRemark,
              alarmReason: ele.alarmReason,
              duration: duration
            });
          }
        } else {
          faultArr.forEach((val) => {
            xTime = new Date(val);
            if (xTime >= happenTime && xTime <= endTime) {
              arr.push(val);
            }
          });
        }
        if (arr.length > 0) {
          markAreaArr.push([{ xAxis: arr[0] }, { xAxis: arr[arr.length - 1] }]);
        }
      });
      if (flag) {
        return {
          alarmArr: alarmArr,
          faultArr: faultArr
        };
      } else {
        return markAreaArr;
      }
    },
    cleanDate() {
      this.sequenceTime = [];
      this.timeDateChange([], false);
    },
    // 阻止冒泡方法
    stopEvenet(e) {
      e = e || window.event;
      if (e.stopPropagation) {
        // W3C阻止冒泡方法
        e.stopPropagation();
      } else {
        e.cancelBubble = true; // IE阻止冒泡方法
      }
    },
    // 时序分析-日期面板打开、关闭
    openChange(status) {
      if (status) {
        this.timeOptionRange = [];
      }
      setTimeout(() => {
        let isExist = document.getElementsByClassName('health-clean-icon').length == 0;
        if (status && !isExist) {
          if (!document.getElementsByClassName('health-clean-icon')[0].style.top) {
            document.body.appendChild(document.getElementsByClassName('health-clean-icon')[0]);
            document.getElementsByClassName('health-clean-icon')[0].addEventListener('click', this.cleanDate);
          }
          document.getElementsByClassName('health-clean-icon')[0].style.display = 'block';
          let top = document.getElementsByClassName('ant-calendar-picker-container')[0].style.top;
          let left = document.getElementsByClassName('ant-calendar-picker-container')[0].style.left;
          document.getElementsByClassName('health-clean-icon')[0].style.top =
            top.substring(0, top.length - 2) * 1 + 10 + 'px';
          document.getElementsByClassName('health-clean-icon')[0].style.left =
            left.substring(0, left.length - 2) * 1 + 510 + 'px';
        } else {
          if (!isExist) document.getElementsByClassName('health-clean-icon')[0].style.display = 'none';
        }
      }, 200);
    },

    // 时序分析-查询条件-日期变化事件
    timeDateChange(val, flag) {
      if (this.sequenceTime.length > 0) {
        this.timeQueryData.startTime = this.sequenceTime[0].format('YYYY-MM-DD');
        this.timeQueryData.endTime = this.sequenceTime[1].format('YYYY-MM-DD');
        // let dayIntervel = val[1].diff(val[0], 'day');
        // if (dayIntervel > 0) {
        //   // this.timeIntervalOptions = this.timeIntervalOptionsAll.week;
        //   this.timeIntervalOptions = this.timeIntervalOptionsAll.day;
        // } else {
        //   this.timeIntervalOptions = this.timeIntervalOptionsAll.day;
        // }
      } else {
        this.timeOptionRange = [];
        this.timeQueryData.startTime = '';
        this.timeQueryData.endTime = '';
        // this.timeIntervalOptions = this.timeIntervalOptionsAll.day;
      }
      this.timeQueryData.timeInterval = this.computedTimeInterval;
      if (flag) {
        this.getWeatherInfo(this.defaultInfo.psId);
        if (this.isOpticalPowerPredictionRemark) {
          this.getOpticalPowerPredictionData(flag[1], moment(flag[1]).diff(flag[0], 'days', true) + 1);
        } else {
          this.getTimeAnalysisData(this.timeQueryData);
        }
      }
    },
    // 时序分析-查询条件-不可选择日期限制
    disabledDate(current) {
      let timeOptionRange = this.timeOptionRange;
      if (timeOptionRange && timeOptionRange.length > 0) {
        return (
          current.diff(timeOptionRange[0], 'days') > 29 ||
          current.diff(timeOptionRange[0], 'days') < -29 ||
          current > moment().endOf('day')
        );
      } else {
        return current > moment().endOf('day');
      }
    },
    // 时序分析-查询条件-日期面板变化
    calendarChange(time) {
      // 当第一时间选中才设置禁用
      this.timeOptionRange = time;
    },
    // 时序分析-查询条件-故障日期样式
    getCurrentClassName(current) {
      if (this.alarmDays.includes(current.format('YYYY/MM/DD'))) {
        return 'topic-date';
      }
      return '';
    },
    // 时序分析-查询条件-典型日期样式
    getTopicalClassName(current) {
      if (this.topicalDays.includes(current.format('YYYY/MM/DD'))) {
        return 'topical-day';
      }
      return '';
    },
    gotoDeepAnalysis() {
      let pointList = this.timeQueryData.pointList.filter((item) => {
        return (
          ['1', '4', '5', '7', '26', '23', '24', '37'].includes(item.deviceType) &&
          item.point != 'stop' &&
          item.pointType == '2' &&
          item.pointName
        );
      });
      let points = pointList.map((item) => {
        return {
          deviceType: item.deviceType,
          key: item.point,
          point: item.point,
          pointName: item.pointName,
          unit: item.unit
        };
      });
      points = this.arrUnique(points, 'key') || [];
      let devices = pointList.map((item) => {
        return {
          deviceType: item.deviceType,
          psKey: item.psKey
        };
      });
      devices = this.arrUnique(devices, 'psKey');
      if (!devices) {
        devices = [
          {
            deviceType: this.defaultInfo.deviceType,
            psKey: this.defaultInfo.psKey
          }
        ];
      }
      let types = pointList.map((item) => {
        return item.deviceType;
      });
      types = Array.from(new Set(types));
      let list = {
        points: points,
        devices: devices,
        types: types
      };
      let time =
        this.sequenceTime.length > 0
          ? this.sequenceTime
          : [moment(this.defaultInfo.happenTime), moment(this.defaultInfo.happenTime)];
      // console.log('params', { psId: this.defaultInfo.psId, psName: this.defaultInfo.psName, list: list, time: time, source: 2 });
      // window.comefromAlarmCenter = true;
      // localStorage.setItem('comefromAlarmCenter', true)
      this.$router.push({
        // name: 'DeepAnalysisTool',
        name: 'dataCenter-insightTool',
        params: { psId: this.defaultInfo.psId, psName: this.defaultInfo.psName, list: list, time: time, source: 2 }
      });
      window.comefromAlarmCenter = true;
    }
  }
};
</script>
<style lang="less" scoped>
.content-div {
  height: calc(100% - 66px);
  overflow-y: auto;
  overflow-x: hidden;
}

.footer-div {
  margin-top: 8px;
  padding: 12px 0px;
  border: 1px solid var(--zw-border-color--default);
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;

  button + button {
    margin-left: 16px;
  }
}

:deep(.ant-spin-nested-loading) {
  height: 100%;

  .ant-spin-container {
    width: 100%;
    height: 100%;
  }
}

.no-parentId {
  position: fixed !important;

  :deep(.ant-drawer-content-wrapper) {
    width: calc(100vw - 32px) !important;
    height: calc(100% - 112px);
    margin-top: 74px;
    padding-right: 32px;
  }
}

:deep(.ant-drawer-body) {
  padding: 0;
}

:deep(.ant-drawer-wrapper-body) {
  overflow: hidden;
}

:deep(.ant-calendar-picker-container) {
  top: 200px !important;
}

.search-head {
  padding: 16px 24px;

  .device-name {
    color: var(--zw-text-1-color--default);
    margin-right: 40px;
  }
}

.search-div {
  margin-right: 40px;

  .search-label {
    margin-right: 4px;
    color: var(--zw-text-2-color--default);
  }

  .search-date {
    width: 240px;
  }

  .search-time {
    width: 166px;
  }

  .weather-info {
    padding: 1px 20px;
    border-radius: 16px;
    background: var(--zw-table-header-bg-color--default);
    color: var(--zw-text-1-color--default);
    width: auto;

    .ant-space {
      cursor: pointer;
    }
  }
}

.right {
  margin-left: 0;
  width: 100%;
}

.health-clean-icon {
  display: none;
  position: absolute;
  cursor: pointer;
  z-index: 9999;
  color: var(--zw-text-1-color--default);
}

.chart-content {
  border-radius: 0 0 4px 4px;
  overflow: auto;
  width: 100%;
}

.topical-day {
  position: absolute;
  width: 4px;
  height: 4px;
  top: -5px;
  left: 16px;
  background: var(--zw-primary-color--default);
  border-radius: 100%;
}

.weather-popper {
  max-height: 300px;

  .weather-row {
    height: 30px;

    img {
      margin-right: 5px;
    }
  }
}

.weather-icon {
  height: 24px;
  width: 24px;
  object-fit: contain;
}

.my-date {
  position: relative;
}

.detail-btn {
  position: relative;
  top: -2px;
  border: none;
  color: var(--zw-conduct-color--normal);
  font-size: 18px;
  background: none;
}

.health-icon {
  color: var(--zw-conduct-color--normal);
  font-size: 20px;
}

.health-icon:hover {
  color: var(--zw-primary-color--default) !important;
}

.disabled {
  cursor: not-allowed;
  pointer-events: none;
  opacity: 0.5;
}

.detail-info {
  margin: 8px auto 24px;
  background: var(--zw-small-area-bg-color--default);
  padding: 16px 0 8px;
  line-height: 22px;
  width: 1248px;

  .lable {
    color: var(--zw-text-2-color--default);
    text-align: right;
    display: inline-block;
    width: 176px;
    margin-bottom: 8px;
  }

  .value {
    color: var(--zw-text-1-color--default);
    opacity: 0.7;
    text-align: left;
    display: inline-block;
    width: 136px;
    margin-bottom: 8px;
  }

  .detail-item {
    display: inline-block;
  }
}

.img-div {
  margin-top: 16px;
  height: calc(100vh - 262px);
  text-align: center;

  img {
    height: 100%;
  }
}

</style>
