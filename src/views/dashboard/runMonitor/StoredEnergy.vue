<template>
  <div id="storedDom" class="run-monitor" :class="{isThird: !isShowMenu }">
    <!--展示功率等数据头部-->
    <stored-head-data :headData="headData" show-type="powerStation"></stored-head-data>
    <!--展示筛选查询头部-->
    <a-spin size="large" @click="e => e.preventDefault()" :spinning="wholePageLoading">
      <div class="search-header flex-between">
        <div class="search-header-left flex-start">
        <span class="search-label">电站</span>
          <div class="search-select">
            <ps-tree-select @change="getChild" :isQueryPs="1" :isPsaDisable="false" :checked="true" @treeSearchEnd="listLoading = false"
              @checkStart="wholePageLoading = true" @treeSearchStart="listLoading = true" style="width: 100%;" psCateGory="'Storage'" 
              :hasMaintenanceStatusParams="true"/>
          </div>
          <a-button class="solar-eye-btn-primary margin-left-24" :loading="listLoading" @click="refreshScrollLoading">
            查询
          </a-button>
        </div>
        <div class="desc">为您筛选到符合条件的电站&nbsp;<span
          class="desc-color">{{ ipagination.total }}</span>&nbsp;/{{ selectLength }}座
        </div>
      </div>
      <div style="height: 16px;width:100%"></div>
      <div class="main-content-chart" :style="contentHeight">
        <div class="chart-card" v-for="(item,index) in listData" :key="`StoredEnergy_${index}`" :data-psId="item.psId" :style="{height: isCustom ?'560px' :'' }">
          <div class="chart-card-header">
            <div class="chart-card-header-title"
                 :class="{'chart-card-header-title-read':haveReadPsIds.includes(item.psId)}" @click="goInverter(item)">
              <a-tooltip placement="topLeft">
                <template slot="title">
                  <span >{{ item.psName }}</span>
                </template>
                <span  class="chart-card-header-title"
                      :class="{'chart-card-header-title-read':haveReadPsIds.includes(item.psId)}">
              {{ item.psName }}
            </span>
              </a-tooltip>
            </div>
            <a-dropdown :trigger="['click']" :getPopupContainer="(node) => node.parentNode">
              <a class="ant-dropdown-link" @click="e => e.preventDefault()" style="position: absolute;"
                :style="{ top: item.statusSignName ? '0' : '14px', right: item.statusSignName ? 0 : '10px' }">
                <div v-if="item.statusSignName" style="position: relative" class="solar-eye-seal-div">
                  <img :src="navTheme == 'dark' ? darkSealBg : lightSealBg" />
                  <span class="solar-eye-seal-name">{{ item.statusSignName }}</span>
                </div>
                <span v-else>
                  <svg-icon iconClass="sign" :style="{ color: navTheme == 'dark' ? '#AB4C2D' : '#FF8787' }"></svg-icon>
                  <span class="solar-eye-seal">&nbsp;&nbsp;状态标牌</span>
                </span>
              </a>
              <a-menu slot="overlay" @click="handleMenuClick($event, index, item)" :theme="navTheme">
                <a-menu-item v-for="item in menuItemList" :key="item.secondTypeCode"
                  :style="{ background: getSelectColor(item.secondTypeCode, index) }">
                  <a-tooltip placement="right">
                    <template slot="title">
                      <span>{{ item.remark }}</span>
                    </template>
                    <span>{{ item.secondName }}</span>
                  </a-tooltip>
                </a-menu-item>
              </a-menu>
            </a-dropdown>
          </div>
          <div class="chart-card-data" style="padding-top: 12px" >
            <div class="chart-card-data-block">
              <div class="chart-data-text">
                <div class="left">
                  <svg-icon iconClass="real_time_power"></svg-icon>
                </div>
                <div class="right">
                  <p class="">{{ item.p370009 }}</p>
                  <p class=""> 有功功率({{ item.p370009Unit }})</p>
                </div>
              </div>
              <div class="chart-data-text">
                <div class="left">
                  <svg-icon iconClass="charge"></svg-icon>
                </div>
                <div class="right">
                  <p class="">{{ item.p370016 }}</p>
                  <p class=""> 日充电量({{ item.p370016Unit }})</p>
                </div>
              </div>
            </div>
            <div class="chart-card-data-block">
              <div class="chart-data-text">
                <div class="left">
                  <svg-icon iconClass="soc"></svg-icon>
                </div>
                <div class="right">
                  <p class="">{{ item.p230004 }}</p>
                  <p class="">SOC({{ item.p230004Unit }})</p>
                </div>
              </div>
              <div class="chart-data-text">
                <div class="left">
                  <svg-icon iconClass="charge_out" ></svg-icon>
                </div>
                <div class="right">
                  <p class="">{{ item.p370017 }} </p>
                  <p class="">日放电量({{ item.p370017Unit }})</p>
                </div>
              </div>
            </div>
          </div>
          <div class="warning-info">
            <div class="warning-info-item warning-info-item-store" @click="gotoDiagnosisCenter(item.psId, '1', item.psName)">
              <span>I 类告警</span>
              <span style="margin-left:14px">{{ item.alarm && item.alarm.ALARM1 }}</span></div>
            <div class="warning-info-item warning-info-item-store" @click="gotoDiagnosisCenter(item.psId, '2', item.psName)">
             <span>II 类告警</span>
             <span style="margin-left:14px">{{ item.alarm && item.alarm.ALARM2 }}</span></div>
            <div class="warning-info-item warning-info-item-store" @click="gotoDiagnosisCenter(item.psId, '3', item.psName)">
              <span>III 类告警</span><span
              style="margin-left:14px">{{ item.alarm && item.alarm.ALARM3 }}</span></div>
          </div>
          <div class="chart-card-chart" style="height: 270px;padding: 24px 8px 6px 16px">
            <power-station-chart @getCardData="getCardData" :isCn="true"  :psId="item.psId"
                                 :refreshFlag="item.refreshFlag"
                                 :chartIndex="index" />
          </div>

        </div>
        <infinite-loading :identifier="infiniteId" v-if="listData.length!== 1 && dataReady" @infinite="refreshListData">
          <div slot="spinner">
            <a-spin size="large" v-if="!wholePageLoading" class="card-spin"></a-spin>
          </div>
          <div slot="no-more">
            <p class="no-more-data">无更多数据</p>
          </div>
          <div slot="no-results" v-if="listData.length == 0">
            <img class="no-data-img" :src="noDataImg" />
            <p class="no-data-text">暂无数据</p>
          </div>
        </infinite-loading>
      </div>
    </a-spin>

  </div>
</template>

<script>
import PowerStationChart from './modules/PowerStationChart'; // 折线图组件
import {
  getMonitorHeadCn, // 头部数据
  monitorPowerStationCnList, // 列表数据
  getStationRefreshCn
} from '@/api/monitor/runMonitor';
import InfiniteLoading from 'vue-infinite-loading'; // 滚动加载插件
import initDict from '@/mixins/initDict';
import StoredHeadData from './modules/StoredHeadData';
import SvgIcon from '../../../components/SvgIcon.vue';
import { mixin } from '@/utils/mixin.js';
import { USER_INFO } from '@/store/mutation-types';
import setStatus from './mixins/setStatus';

export default {
  name: 'StoredEnergy',
  mixins: [initDict, mixin, setStatus],
  components: { StoredHeadData, PowerStationChart, InfiniteLoading, SvgIcon },
  watch: {
    'ipagination.current': {
      handler (val) {
        console.log(val);
      }
    }
  },
  data () {
    return {
      listLoading: false, // 列表加载
      wholePageLoading: true, // 页面全局loading
      firstLoading: true, // 是否是第一次加载
      psNameHide: false, // 是否显示电站真实名称
      dataReady: false, // 列表查询查询前置条件数据是否具备
      treeLoad: false, // 树加载
      infiniteId: +new Date(), // 滚动加载标识
      listData: [], // 列表数据
      listRefreshTimeStart: '', // 记录最新一次数据刷新开始请求的时间
      noDataImg: require('@/assets/images/public/no-data.png'), // 暂无数据图片
      headData: { // 头部数据
        deviceCount: '0,台', // 变流器数量
        p370014: '0,Wh', // 总充电量
        p370015: '0,Wh', // 总放电量
        p370016: '0,Wh', // 日充电量
        p370017: '0,Wh', // 日放电量
        psCount: 0, // 电站数量
        ratedPowerCn: '0,MW', // 有功功率
        totalCapcityCn: '0,W', // 装机容量
        p370009: '0,W'
      },
      ipagination: { // 分页
        current: 1,
        pageSize: 10,
        pageSizeOptions: ['10', '20', '30'],
        defaultPageSize: 10,
        defaultCurrent: 1,
        size: 'small',
        total: 0,
        showTotal: (total) => `共 ${total} 条`
      },
      queryParams: { // 搜索筛选数据对象
        min: null, // 装机容量min
        max: null, // 装机容量max
        unit: 'kW', // 装机容量单位
        isSelectAll: true, // 是否全选
        psIds: '', // 已选择的电站
        alarmType: 'ALL', // 告警类型
        statusSign: 'ALL', // 电站挂牌
        // psTypeList: [], // 电站类型
        sort: 'tmp.ps_id desc', // 默认排序
        elecEnsureType: 'ALL',
        psaIds: '',
        depCodes: '',
        queryName: ''
      },
      haveReadPsIds: [], // 已读过的电站
      value: undefined, // 输入选择框值
      scrollTop: 0, // 当前页面滚动高度
      readPage: 0, // 局部刷新的页码
      innerWidth: window.innerWidth, // 浏览器宽度
      timer: null,
      menuItemList: [],
      status: {
        name: '',
        value: ''
      },
      selectLength: 0,
      refreshWholeFlag: false

    };
  },
  async created () {
    this.getSystemCodeList();
    this.timer = setInterval(this.refreshInterval, 1000 * 5 * 60);
    setTimeout(() => {
      this.firstLoading = false;
    }, 1500);
    this.$once('hook:beforeDestroy', () => {
      clearInterval(this.timer);
      this.timer = null;
    });
  },
  mounted () {
    let storedDom = document.getElementById('storedDom');
    if (storedDom) {
      storedDom.scrollTop = this.scrollTop;
    }
    this.getInnerWidth(true);
    // this.refreshListData(false,true);
  },
  activated () {
    let storedDom = document.getElementById('storedDom');
    if (storedDom) {
      storedDom.scrollTop = this.scrollTop;
    }
    if (!this.timer && !this.firstLoading) {
      this.refreshInterval();
      this.timer = setInterval(this.refreshInterval, 1000 * 5 * 60);
    }
  },
  beforeDestroy () {
    if (this.timer) {
      window.clearInterval(this.timer);
      this.timer = null;
    }
    window.removeEventListener('resize', this.getInnerWidthResize);
  },
  deactivated () {
    if (this.timer) {
      window.clearInterval(this.timer);
      this.timer = null;
    }
  },
  computed: {
    // 卡片区域高度
    contentHeight () {
      return `height:calc(100vh - ${
        368 + // 头部各种边距
        59 + // 导航栏
        24 + // 多页签
        (this.isCustom ? 44 : 0)
      }px)`;
    },
    isCustom () {
      let userInfo = Vue.ls.get(USER_INFO);
      let arr = userInfo.roles.filter(item => {
        return item.roleCode == 'r10055';
      });
      return arr.length > 0;
    },
    isShowMenu () {
      return this.$store.state.user.isShowMenu;
    }
  },
  methods: {
    getInnerWidth (iswidth) {
      if (iswidth) {
        window.addEventListener('resize', this.getInnerWidthResize);
      } else {
        window.removeEventListener('resize', this.getInnerWidthResize);
      }
    },
    getInnerWidthResize () {
      this.innerWidth = window.innerWidth;
    },
    /**
     * 获取选中电站的数据
     * params {参数is_all 是否选中根节点，参数 ids 选中实体电站的id}
     */
    getChild (params, node) {
      if (this.$route.path != '/dashboard/analysis' && this.$route.path != '/houseHold/dashboard') {
        return;
      }
      this.queryParams.depCodes = node.depCodes;
      this.queryParams.psaIds = node.psaIds;
      this.queryParams.psIds = node.psIds;
      this.selectLength = node.psNum;
      if (!node.isFirstReq) {
        this.refreshScrollLoading();
      }
      this.refreshWholeFlag = true;
      this.dataReady = true;
    },
    goInverter (item) {
      let storedDom = document.getElementById('storedDom');
      this.scrollTop = storedDom.scrollTop;
      if (!this.haveReadPsIds.includes(item.psId)) {
        this.haveReadPsIds.push(item.psId);
      }

      this.$emit('changePageParams', {
        psId: item.psId,
        psName: item.psName,
        source: item.source || item.source == 0 ? item.source : 1
      });
      setTimeout(() => {
        this.$emit('changePage', 2);
      }, 10);
    },
    // 获取头部总览数据
    async getOverviewData () {
      let parms = this.queryParams;
      let res = await getMonitorHeadCn({
        // isParNode: this.queryParams.isSelectAll ? '1' : '0',

        // treePsId: this.queryParams.isSelectAll ? undefined : this.queryParams.psIds.join(','),
        depCodes: parms.depCodes,
        psaIds: parms.psaIds,
        treePsId: parms.psIds,
        // queryName:parms.queryName,
        alarmType: this.queryParams.alarmType === 'ALL' ? undefined : this.queryParams.alarmType,
        statusSign: this.queryParams.statusSign === 'ALL' ? undefined : this.queryParams.statusSign,
        // psTypeList: this.queryParams.psTypeList,
        elecEnsureType: this.queryParams.elecEnsureType == 'ALL' ? undefined : this.queryParams.elecEnsureType,
        totalCapcityBegin: this.exchangeKw(this.queryParams.min) === null ? undefined : this.exchangeKw(this.queryParams.min),
        totalCapcityEnd: this.exchangeKw(this.queryParams.max) === null ? undefined : this.exchangeKw(this.queryParams.max),
        psCateGory: "'Storage'"
      });
      // if(!this.listLoading){
      setTimeout(() => {
        this.treeLoad = true;
      }, 500);
      // }

      if (Object.keys(res.result_data).length) {
        this.headData = Object.assign(this.headData, res.result_data);
      }
    },
    // 容量单位换算
    exchangeKw (val) {
      if (val === null) {
        return null;
      }
      if (this.queryParams.unit === 'kW') {
        return val;
      }

      if (this.queryParams.unit === 'MW') {
        return val * 1000;
      }
      if (this.queryParams.unit === 'GW') {
        return val * 1000000;
      }
    },
    // 重置滚动刷新
    refreshScrollLoading (isResetSort) {
      this.refreshWholeFlag = true;
      this.haveReadPsIds = [];
      this.scrollTop = 0;
      this.listData = [];
      this.ipagination.current = 1;
      this.infiniteId += 1;
    },
    // 刷新列表数据 loadMore 是否是滚动刷新
    async refreshListData (loadMore, isPartRefresh) {
      if (this.refreshWholeFlag) {
        this.wholePageLoading = true;
        this.refreshWholeFlag = false;
      }
      this.listLoading = true;
      if (this.ipagination.current === 1 || isPartRefresh) {
        this.getOverviewData();
      }
      let params = this.queryParams;
      // todo 在不加遮罩的情况下 优化多选多次点击接口返回结果顺序和点击顺序不一的数据错乱bug
      // let requestTimeStart = (new Date()).getTime();
      // this.listRefreshTimeStart = requestTimeStart;
      let res = await monitorPowerStationCnList({
        pageSize: 8,
        currentPage: isPartRefresh ? this.readPage : this.ipagination.current,
        // isParNode: this.queryParams.isSelectAll ? '1' : '0',
        // treePsId: this.queryParams.isSelectAll ? undefined : this.queryParams.psIds.join(','),
        depCodes: params.depCodes,
        psaIds: params.psaIds,
        treePsId: params.psIds,
        // queryName:params.queryName,
        alarmType: this.queryParams.alarmType === 'ALL' ? undefined : this.queryParams.alarmType,
        // psTypeList: this.queryParams.psTypeList,
        statusSign: this.queryParams.statusSign === 'ALL' ? undefined : this.queryParams.statusSign,
        order: this.queryParams.sort,
        totalCapcityBegin: this.exchangeKw(this.queryParams.min) === null ? undefined : this.exchangeKw(this.queryParams.min),
        totalCapcityEnd: this.exchangeKw(this.queryParams.max) === null ? undefined : this.exchangeKw(this.queryParams.max),
        elecEnsureType: 0,
        psCateGory: "'Storage'"
      });
      // if (this.listRefreshTimeStart != requestTimeStart) {
      //   return;
      // }
      this.listLoading = false;

      if (res.result_code !== '1') {
        this.$message.error(res.result_msg);
        return;
      }
      let result = res.result_data || {};
      this.ipagination.total = result.rowCount || 0;
      let pageList = result.pageList || [];

      if (this.listData.length === 0) {
        this.listData = pageList.map(item => {
          return {
            ...item,
            alarm: {},
            radiation: '0,MJ/m²',
            signName: '',
            refreshFlag: Date.parse(new Date())
          };
        });
      } else {
        const arr = pageList.map(item => {
          return {
            ...item,
            alarm: {},
            radiation: '0,MJ/m²',
            refreshFlag: Date.parse(new Date())
          };
        });
        if (isPartRefresh) {
          this.listData.splice((this.readPage - 1) * 8, this.listData.length - ((this.readPage - 1) * 8));
          this.listData = this.listData.concat(arr.map(item => {
            let obj = JSON.parse(JSON.stringify(item));
            obj.refreshFlag++;
            return obj;
          }));
        } else {
          this.listData = this.listData.concat(arr);
        }
      }

      if (!isPartRefresh) {
        this.readPage = this.ipagination.current;
        this.ipagination.current++;
      }

      if (loadMore) {
        const length = pageList.length;
        if (length < 8) {
          loadMore.complete();
          if (length != 0) {
            loadMore.loaded();
          }
        } else {
          loadMore.loaded();
        }
      }
      this.listLoading = false;
      this.wholePageLoading = false;
    },

    getCardData (cardData, index) {
      this.$set(this.listData[index], 'alarm', cardData.alarm);
      this.listData[index].radiation = cardData.radiation;
    },
    // 根据权限标识和外部指示状态进行权限判断
    show (perms) {
      return this.showHandle(perms);
    },
    isViewArea (dom) {
      // 获取可视窗口的盖度。
      let windowHeight = window.innerHeight;// 可视区域的高
      let position = dom.getBoundingClientRect();
      // 当元素的top偏移量小于页面大小并且大于高度的负数
      // 后面position.top>-position.height主要
      // 是为了防止底边在可视区域的顶部,也就是超出可视区域
      // 这里的判断是重点
      if (position.top < windowHeight && position.top > -(position.height - 103)) {
        return true;
      }
      return false;
    },
    // 获取可视区域内的电站id
    getViewAreaPsId (isPsId) {
      let chartCards = document.getElementsByClassName('chart-card');
      let psIds = [];
      let indexList = [];
      if (chartCards.length > 0) {
        chartCards.forEach((element, index) => {
          if (this.isViewArea(element)) {
            psIds.push(element.dataset.psid);
            indexList.push(index);
          }
        });
      }
      return isPsId ? psIds : indexList;
    },
    refreshInterval () { // 定时刷新
      let arr = this.getViewAreaPsId(false);
      arr.map(item => {
        this.listData[item].refreshFlag = new Date().getTime();
      });
      this.getOverviewData();
      getStationRefreshCn({
        psIds: this.getViewAreaPsId(true),
        psCateGory: "'Storage'"
      }).then(res => {
        if (res.result_code === '1') {
          let data = res.result_data || [];
          if (data && data.length > 0) {
            arr && arr.map((item, index) => {
              this.listData[item] = Object.assign(this.listData[item], data[index]);
            });
          }
        } else {
          this.$message.error(res.result_msg);
        }
      });
    }
  }
};
</script>
<style lang="less" scoped>
.warning-info-item-store {
    display: flex;
    padding: 0 8px;
    justify-content: space-between;
}
.chart-data-text {
 margin-right: 32px;
}
</style>
