<template>
  <detail-layout :labelList="labelList" :form="baseForm" title="附件信息"> </detail-layout>
</template>

<script>
export default {
  name: 'AnnexInfoDetail',
  props: {
    baseForm: {
      type: Object,
      default: () => {
        return {};
      }
    }
  },
  data () {
    return {
      labelList: [
        {
          label: (params) => {
            return params.taskType === '7' ? '故障图片' : '缺陷图片';
          },
          labelFunc: true,
          key: 'uploadFileList',
          type: 'file:text',
          span: 24,
          func: (params) => {
            return params.taskType != 9;
          }
        }
      ]
    };
  }
};
</script>
