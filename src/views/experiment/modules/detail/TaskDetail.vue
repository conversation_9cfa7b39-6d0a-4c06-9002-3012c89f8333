<template>
  <div class="drawer-form-com">
    <div class="drawer-form-content">
      <a-spin :spinning="loading" style="height: 100%; width: 100%">
        <div v-if="baseForm.id">
          <detail-layout
            @handleToggle="(val) => (basicShow = val)"
            :class="basicShow ? 'overflow-hidden' : ''"
            v-if="!baseForm.isTemporarySave" :labelList="labelList1" :form="baseForm" title="基本信息">
            <template v-slot:planTime>
              <a-col :span="8" class="detail_layout_content">
                <span class="left">计划日期</span>
                <span class="right"> {{ baseForm.executePlanStartTime }} ~ {{ baseForm.executePlanEndTime }} </span>
              </a-col>
            </template>
          </detail-layout>
          <detail-layout
            @handleToggle="(val) => (basicShow = val)"
            :class="basicShow ? 'overflow-hidden' : ''"
            :toggleBtnShow='true' v-if="baseForm.isTemporarySave" :labelList="labelList2" :form="baseForm" title="基本信息">
            <template v-slot:planTime>
              <a-col :span="8" class="detail_layout_content">
                <span class="left">计划日期</span>
                <span class="right"> {{ baseForm.executePlanStartTime }} ~ {{ baseForm.executePlanEndTime }} </span>
              </a-col>
            </template>
          </detail-layout>

          <template v-if="baseForm.testInfoList && baseForm.testInfoList.length">
            <TitleItem title-lable="试验信息" style='margin-top: 16px' />
           <template v-if="baseForm.taskDefKey === 'act0'">
             <ReceivingTestInfo
               :testInfoItem="item"
               :testInfoIndex="index"
               v-for="(item, index) in baseForm.testInfoList"
               :key="index"
             />
           </template>
            <template v-else>
              <AfterRecivedTestInfo :testInfoList="baseForm.testInfoList"/>
            </template>
            <div class="detail_layout report-layout" v-if="['act1','act2','act3'].includes(baseForm.taskDefKey) ||( !baseForm.taskDefKey && baseForm.flowSts!=='5')">
              <a-row :gutter="24" style="padding: 12px 16px">
                <a-col :span="24" class="detail_layout_content">
                  <span class="left" style="width: 160px">试验报告</span>
                  <span class="right">
                    <throttle-button icon="download" label="下载报告"  :loading='exportLoading' @click='downloadFile' />
                  </span>
                </a-col>
              </a-row>
              <a-row :gutter="24" style="padding: 0 16px" v-if="['act2','act3'].includes(baseForm.taskDefKey) || (!baseForm.taskDefKey && baseForm.flowSts!=='5')">
                <a-col :span="24" class="detail_layout_content">
                  <span class="left" style="width: 160px">备注</span>
                  <span class="right"> {{ baseForm.preTestRemark || '--' }}</span>
                </a-col>
              </a-row>
            </div>
          </template>
          <template v-if="baseForm.flowSts === '5'">
            <detail-layout :labelList="labelList3" :form="baseForm" title="终止信息"/>
          </template>
        </div>
      </a-spin>
    </div>
    <div class="drawer-form-foot">
      <a-button type="info" :disabled="loading" @click="$emit('cancel')">返回</a-button>
    </div>
    <FlowChart :formData.sync='baseForm'/>
  </div>
</template>

<script>
import { taskDetailApi } from '@api/isolarErp/taskHall';
import { labelList1, labelList2, labelList3 } from '@views/experiment/js/Column';
import initDict from '@/mixins/initDict';
import AfterRecivedTestInfo from '@views/experiment/modules/detail/ReceivedTestInfo.vue';
import TitleItem from '@views/experiment/modules/detail/TitleItem.vue';
import FlowChart from '@views/experiment/modules/detail/FlowChart.vue';
import ReceivingTestInfo from './ReceivingTestInfo.vue';
import downBigExcel from '@/mixins/downBigExcel';
export default {
  name: 'TaskDetail',
  mixins: [initDict, downBigExcel],
  components: { AfterRecivedTestInfo, TitleItem, FlowChart, ReceivingTestInfo },
  inject: ['downloadFileFn'],
  data () {
    return {
      loading: false,
      baseForm: {},
      labelList1,
      labelList2,
      labelList3,
      exportLoading: false,
      basicShow: false
    };
  },
  created () {
    this.getDictMap('psa_model');
  },
  methods: {
    async init (type, row) {
      this.loading = true;
      const { result_data } = await taskDetailApi({ businessesId: row.id });
      this.baseForm = Object.assign({}, row, result_data);
      this.loading = false;
      return `任务-${result_data.taskCode}【${row.flowStsName}】`;
    },
    downloadFile () {
      const { taskDefKey, reportFile } = this.baseForm;
      try {
        if (['act1', 'act2'].includes(taskDefKey)) {
          this.exportExcelEvent('exportLoading', 'poi-tl-100001', { testId: this.baseForm.id }, 'docx');
        } else if (['act3', 'act4'].includes(taskDefKey) || !taskDefKey) {
          this.exportLoading = true;
          const file = reportFile[reportFile.length - 1];
          this.downloadFileFn(file.path, file.fileName);
          setTimeout(() => { this.exportLoading = false; }, 1000);
        }
      } catch (e) {
        this.exportLoading = false;
      }
    }
  },
  computed: {}
};
</script>

<style scoped lang="less">
.drawer-form-com {
  padding: 24px 0 0;
  .overflow-hidden {
    height: 32px;
    overflow: hidden;
  }
  .report-layout{
    margin-top: 20px;
    position: sticky;
    bottom: -12px;
    background-color: #fff;
  }
}
:root[data-theme='dark']{
  .drawer-form-com{
    .report-layout{
      background: #263652 ;
      width: 100vw;
    }
  }
}
</style>
