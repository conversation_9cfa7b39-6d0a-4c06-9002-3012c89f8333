import { axios } from '@/utils/erp-request';
// 检索设备类型列表
export function queryList (data) {
  return axios({
    url: '/equipment/v1/type/list',
    method: 'post',
    data
  });
}
// 新增设备类型
export function insertErpDeviceType (data) {
  return axios({
    url: '/equipment/v1/type/insertErpDeviceType',
    method: 'post',
    data
  });
}
// 获取当前设备的父类设备信息
export function getParentDeviceTypeInfoList (data) {
  return axios({
    url: '/equipment/v1/type/getParentDeviceTypeInfoList',
    method: 'post',
    data
  });
}
// 修改设备类型
export function updateErpDeviceType (data) {
  return axios({
    url: '/equipment/v1/type/updateErpDeviceType',
    method: 'post',
    data
  });
}
// 删除设备类型
export function deleteErpDeviceType (data) {
  return axios({
    url: '/equipment/v1/type/deleteErpDeviceType',
    method: 'post',
    data
  });
}
// 查询设备类型
export function getErpDeviceTypeDetail (data) {
  return axios({
    url: '/equipment/v1/type/getErpDeviceTypeInfo',
    method: 'post',
    data
  });
}
