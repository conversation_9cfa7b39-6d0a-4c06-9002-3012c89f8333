<!--双细则考核配置-->
<template>
  <div id="doubleRule">
    <div class="solar-eye-search-model">
      <a-row :gutter="24" class="solar-eye-search-content">
        <a-col :xxl="6" :xl="8" :md="12">
          <div class="search-item">
            <span class="search-label">省份</span>
            <a-select placeholder="请选择" v-model="searchParams.areaId" allowClear>
              <a-select-option
                v-for="(item, index) in provinceList"
                :key="index"
                :value="item.areaId"
                :data-name="item.areaName"
                >{{ item.areaName }}
              </a-select-option>
            </a-select>
          </div>
        </a-col>
        <a-col :xxl="6" :xl="8" :md="12">
          <div class="search-item">
            <span class="search-label">所在区域</span>
            <a-select placeholder="请选择" v-model="searchParams.areaTypeName" allowClear>
              <a-select-option v-for="(item, index) in areaTypeList" :key="index" :value="item.areaTypeName"
                >{{ item.areaTypeName }}
              </a-select-option>
            </a-select>
          </div>
        </a-col>
        <a-col :xxl="4" :xl="6" :md="8">
          <div class="search-item">
            <throttle-button label="查询" @click="queryData" class="solar-eye-btn-primary" />
            <throttle-button label="重置" @click="resetParams" class="solar-eye-btn-primary-cancel" />
          </div>
        </a-col>
      </a-row>
    </div>
    <div class="solar-eye-gap"></div>
    <div class="solar-eye-main-content">
      <div class="operation" style="height: 32px">
        <div class="operation-btn">
          <a-button class="solar-eye-btn-primary-cancel" @click="rowClick('1')" v-has="'doubleRule:add'">新增</a-button>
          <a-button
            class="solar-eye-btn-primary-cancel"
            v-has="'doubleRule:export'"
            @click="exportDoubleRule"
            :loading="exportLoading"
            >导出
          </a-button>
        </div>
      </div>
      <a-spin :spinning="loading">
        <vxe-table
          :data="tableData"
          :height="tableHeight - 32"
          class="my-table"
          align="left"
          show-overflow
          resizable
          size="small"
          highlight-hover-row
          :seq-config="{ startIndex: (paginationParams.curPage - 1) * paginationParams.size }"
        >
          <vxe-table-column type="seq" :width="80" align="center" title="序号" />
          <vxe-table-column
            v-for="(item, index) in columns"
            :key="index"
            :formatter="tabFormatter"
            :field="item.key"
            :title="item.title"
            :min-width="item.width"
          >
          </vxe-table-column>
          <vxe-table-column
            align="left"
            title="操作"
            fixed="right"
            width="110"
            :resizable="false"
            :visible="showHandle('doubleRule:edit, doubleRule:delete')"
          >
            <template v-slot="{ row }">
              <g-button
                style="justify-content: flex-start"
                @edit="rowClick('2', row)"
                @delete="deleteRow(row)"
                :limit="3"
                :list="[
                  {
                    icon: 'edit',
                    isSvg: true,
                    emit: 'edit',
                    name: '编辑',
                    show: true, // 显示条件
                    has: 'doubleRule:edit' // 未分配权限 则添加 true,  否则传入权限标识即可
                  },
                  {
                    icon: 'delete',
                    isSvg: true,
                    emit: 'delete',
                    name: '删除',
                    has: 'doubleRule:delete', // 未分配权限 则添加 true,  否则传入权限标识即可
                    show: true // 显示条件
                  }
                ]"
              />
            </template>
          </vxe-table-column>
        </vxe-table>
        <page-pagination
          :pageSize="paginationParams.size"
          :current="paginationParams.curPage"
          :total="total"
          @size-change="sizeChange"
        />
      </a-spin>
    </div>
    <drawer-view ref="drawerView" @cancel="handleCancel" parentId="doubleRule" />
  </div>
</template>

<script>
import initDict from '@/mixins/initDict';
import tableHeight from '@/mixins/tableHeightAndSearchModel';
import { LeftMixin } from '@/mixins/LeftMixin';
import { doubleRuleColumns } from './js/columns';
import {
  deleteDoubleRulesApi,
  exportDoubleRulesApi,
  getAreaTypeListApi,
  getDoubleRulesListApi,
  getProvinceListApi
} from '@/api/health/healthapi';
import { uniqBy } from 'lodash';

export default {
  name: 'DoubleRule',
  mixins: [initDict, tableHeight, LeftMixin],
  data () {
    return {
      tableData: [],
      columns: doubleRuleColumns,
      paginationParams: {
        curPage: 1,
        size: 10
      },
      total: 0,
      searchParams: {
        areaId: undefined,
        areaTypeName: undefined
      },
      loading: false,
      provinceList: [],
      areaTypeList: [],
      backAreaTypeList: [],
      exportLoading: false
    };
  },
  created () {
    this.getProvinceList();
    this.getAreaTypeList();
    this.queryData();
  },
  provide () {
    return {
      areaTypeList: () => this.backAreaTypeList,
      provinceList: () => this.provinceList
    };
  },
  methods: {
    getProvinceList () {
      getProvinceListApi()
        .then((res) => {
          this.provinceList = res || [];
        })
        .catch((err) => {
          this.provinceList = [];
          console.error('获取省份列表失败:', err);
        });
    },
    getAreaTypeList (areaName) {
      getAreaTypeListApi({ areaName })
        .then((res) => {
          if (!areaName) {
            this.backAreaTypeList = uniqBy(res || [], 'areaTypeName');
          }
          this.areaTypeList = uniqBy(res || [], 'areaTypeName');
        })
        .catch((err) => {
          this.areaTypeList = [];
          console.error('获取区域类型列表失败:', err);
        });
    },
    handleAreaChange (value, option) {
      this.searchParams.areaTypeName = undefined;
      const areaName = option.data.attrs['data-name'];
      this.getAreaTypeList(areaName);
    },
    queryData () {
      this.loading = true;
      getDoubleRulesListApi({
        ...this.searchParams,
        ...this.paginationParams
      })
        .then((res) => {
          this.tableData = res.data || [];
          this.total = res.total || 0;
        })
        .finally(() => {
          this.loading = false;
        });
    },
    resetParams () {
      this.searchParams = this.$options.data().searchParams;
      this.paginationParams = this.$options.data().paginationParams;
      this.queryData();
    },
    sizeChange (curPage, size) {
      Object.assign(this.paginationParams, { curPage, size });
      this.queryData();
    },
    rowClick (type, row = {}) {
      this.$refs.drawerView.init(type, row, '/config/modules/DoubleRuleForm');
    },
    deleteRow (row) {
      this.$confirm({
        title: '确认删除',
        content: '是否删除选中数据?',
        okText: '确定',
        cancelText: '取消',
        onOk: () => {
          deleteDoubleRulesApi({ ids: [row.id] })
            .then(() => {
              this.$message.success('删除成功');
              this.queryData();
            })
            .catch((err) => {
              console.error('删除失败:', err);
              this.$message.error('删除失败，请稍后再试');
            });
        }
      });
    },
    handleCancel (needRefresh = true) {
      if (needRefresh) {
        this.queryData();
      }
    },
    exportDoubleRule () {
      this.exportLoading = true;
      exportDoubleRulesApi({
        ...this.searchParams,
        ...this.paginationParams
      })
        .then((res) => {
          this.$downloadFile({
            fileBase64Code: res.fileBase64Code,
            fileName: res.fileName,
            fileType: res.fileType
          });
          this.exportLoading = false;
          this.$message.success('导出成功');
        })
        .catch(() => {
          this.exportLoading = false;
        });
    }
  }
};
</script>

<style scoped lang="less"></style>
