export const headerList = [
  { id: '0', value: '', name: '全部', num: 0, icon: 'all' },
  { id: '1', value: '2', name: '待指派', num: 0, icon: 'undo' },
  { id: '2', value: '3', name: '处理中', num: 0, icon: 'processing' },
  { id: '3', value: '4', name: '验收中', num: 0, icon: 'check' },
  { id: '4', value: '5', name: '已完成', num: 0, icon: 'finish' },
  { id: '5', value: '6', name: '已终止', num: 0, icon: 'stop' }
];

export const columnFn = () => {
  return [
    { comment: '工单编号', name: 'orderNo' },
    { comment: '业主', name: 'ownerName' },
    { comment: '项目公司', name: 'projectCompany', width: 180 },
    { comment: '所属地区', name: 'area', width: 180 },
    { comment: '电站', name: 'psaName' },
    { comment: '运维商', name: 'maintenance', width: 250 },
    { comment: '工单类型', name: 'orderTypeName' },
    { comment: '工单状态', name: 'flowStsName' },
    { comment: '工单生成时间', name: 'createTime' },
    { comment: '派发时间', name: 'actualStartTime' },
    { comment: '实际结束时间', name: 'actualResolveTime' }
  ];
};

export const orderHallCheck = columnFn().map((item) => item.name);
