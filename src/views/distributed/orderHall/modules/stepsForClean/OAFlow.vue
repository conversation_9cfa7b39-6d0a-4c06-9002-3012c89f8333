<template>
 <div class="drawer-form-com" id="oa-flow">
     <div class='drawer-form-content'>
       <a-spin :spinning='loading' style='width: 100%; min-height: 60px'>
         <template v-if='formData.id'>
           <a-alert :message="`请在${formData.orderDetailVo.taskTypeName || '--'}工单完成后15天内完成OA流程提交操作`" banner closable />
           <BasicInfoDetail style='padding-top: 40px' :baseForm="formData.orderDetailVo" :dictMap="dictMap" :showToggle='false' />
           <div class='gap'></div>
           <template v-if='isDisabled'>
             <detail-layout :labelList="labelList" :form="formData" title="处理信息">
               <template v-slot:oaFlowId>
                 <a-col :span="8" class="detail_layout_content">
               <span class="left">
                 OA流程ID
                 <a-popover>
                    <template slot="content">
                      <div style="max-width: 268px">
                      填写对应的OA操作流程，例如报销，填写报销流程ID
                      </div>
                    </template>
                    <span>
                      <svg-icon style="margin-left: 2px" iconClass="health-info"></svg-icon>
                    </span>
                  </a-popover>
               </span>
                   <span class="right">{{formData.oaFlowId}}</span>
                 </a-col>
               </template>
             </detail-layout>
           </template>
           <template v-else>
             <a-row :gutter="24" >
               <a-col :span="24">
                 <div class="order-dispose">
                   <div class="title-box">
                     <span class="before"></span>
                     <span>处理信息</span>
                   </div>
                 </div>
               </a-col>
             </a-row>
             <a-row :gutter="24" >
               <a-form-model
                 :model="formData"
                 ref="oaFlow"
                 :labelCol="{ style: 'width: 150px' }"
                 :wrapperCol="{ style: 'width: calc(100% - 150px)' }"
               >
                 <a-col :xl="8" :sm="12" :xs="24">
                   <a-form-model-item  prop="oaFlowId" :rules="{ required: true, message: '此项为必填项，7位数字',pattern: /^([0-9]\d{6})$/,trigger:['change','blur'] }">
                     <template slot='label'>
                     <span>
                       OA流程ID
                       <a-popover>
                          <template slot="content">
                            <div style="max-width: 268px">
                            填写对应的OA操作流程，例如报销，填写报销流程ID
                            </div>
                          </template>
                          <span>
                            <svg-icon style="margin-left: 2px" iconClass="health-info"></svg-icon>
                          </span>
                        </a-popover>
                       </span>
                     </template>
                     <a-input :maxLength="7"  placeholder='请填写OA流程ID' v-model="formData.oaFlowId" style="width: 100%" allowClear />
                   </a-form-model-item>
                 </a-col>
                 <a-col :xl="8" :sm="12" :xs="24">
                   <a-form-model-item label="OA流程类型" prop="oaFlowType" :rules="{ required: true, message: '此项为必填项', }">
                     <a-select placeholder='请选择OA流程类型' v-model="formData.oaFlowType" allowClear style="width: 100%">
                       <a-select-option v-for="item in dictMap.oa_flow_type" :key="item.dataValue" :value="item.dataValue">
                         {{ item.dataLable }}
                       </a-select-option>
                     </a-select>
                   </a-form-model-item>
                 </a-col>
               </a-form-model>
             </a-row>
           </template>
         </template>
       </a-spin>
     </div>
   <div class="drawer-form-foot">
     <template  v-if='!isDisabled'>
       <throttle-button :loading="loading" label="暂存" @click="confirmOrderResolve(false)" />
       <a-button :loading="loading" @click="confirmOrderResolve(true)" class="ant-btn-primary">提交</a-button>
     </template>
     <throttle-button v-else :disabled="loading" label="返回" @click="$emit('cancel')" />
   </div>
   <div @click="openFlowChart" class="flow-chart-btn">
     <svg-icon iconClass="flow"></svg-icon>
     流程图
   </div>
   <flow-chart-drawer
     v-if="showDiagram"
     ref="flowChartDrawer"
     :parentId="parentId"
     :processInstanceId="formData.processInstanceId"
     :flowUser="formData.flowUser"
   />
 </div>
</template>

<script>
import { executeOaApi, getOaDetailApi } from '@api/distributed/orderHall';
import BasicInfoDetail from '@views/distributed/orderHall/modules/basic/BasicInfoDetail.vue';
import initDict from '@/mixins/initDict';
export default {
  components: { BasicInfoDetail },
  mixins: [initDict],
  props: {
    parentId: {
      type: String,
      required: true,
      default: ''
    }
  },
  data () {
    return {
      model: {
        type: null,
        rowInfo: {}
      },
      formData: { orderDetailVo: {}, oaFlowId: null, oaFlowType: null },
      loading: false,
      showDiagram: false,
      labelList: [
        {
          slot: 'oaFlowId'
        },
        {
          label: 'OA流程类型',
          key: 'oaFlowTypeName'
        }
      ]
    };
  },
  created () {
    this.getDictMap(`industry_task_type,psa_model,wash_purchases_way,oa_flow_type`);
  },
  methods: {
    reset () {},
    async init (type, rowInfo) {
      this.model = { type, rowInfo };
      console.log(type);
      this.loading = true;
      let result = {};
      try {
        result = await getOaDetailApi({ businessesId: this.model.rowInfo.id });
        this.loading = false;
      } catch (err) {
        this.loading = false;
      }
      this.formData = result.result_data;
      const stationEnvironment = this.formData.orderDetailVo.stationEnvironment;
      this.formData.orderDetailVo.stationEnvironment = stationEnvironment ? Array.from(new Set(stationEnvironment.split(','))) : [];
      return `OA处理提醒-${result.result_data.oaNum}`;
    },
    confirmOrderResolve (isCommit) {
      if (isCommit) {
        this.$refs.oaFlow.validate(valid => {
          if (valid) {
            this.doExecute(isCommit);
          }
        });
      } else {
        this.doExecute(isCommit);
      }
    },
    doExecute (isCommit) {
      this.loading = true;
      executeOaApi({
        isCommit,
        oaFlowId: this.formData.oaFlowId,
        id: this.formData.id,
        oaFlowType: this.formData.oaFlowType
      }).then(res => {
        if (res.result_code == 1) {
          // const result = res.result_data;
          this.$message.success(isCommit ? '提交成功' : '保存成功');
          if (isCommit) this.$emit('cancel');
          this.loading = false;
        }
      }).catch(() => {
        this.loading = false;
      });
    },
    openFlowChart () {
      this.showDiagram = true;
      const _self = this;
      this.$nextTick(() => {
        _self.$refs.flowChartDrawer.openView();
      });
    }
  },
  computed: {
    isDisabled () {
      const { flowSts } = this.formData;
      if (flowSts) {
        return flowSts === '4';
      }
      return true;
    }
  }
};
</script>

<style scoped lang='less'>
#oa-flow{
  padding:0;
  .drawer-form-content{
    padding-right: 0;
    .gap{
      height: 16px;
    }
    .ant-alert.ant-alert-warning{
      background-color: #F9E0C7;
      .ant-alert-message{
        font-size: 14px;
        font-weight: 400;
        color: rgba(0,0,0,0.9);
      }
     :deep(.ant-alert-icon){
        color: #ED7B2F;
      }
      :deep(.ant-alert-close-icon){
        right: 22px;
      }
    }
  }
  :deep(.ant-form-item) {
    // width: 100%;
    display: flex;
  }
}
:root[data-theme='dark'] {
  #oa-flow{
    :deep(.ant-alert.ant-alert-warning){
      .ant-alert-message{
        color: rgba(0,0,0,0.9);
      }
      .ant-alert-close-icon{
        .anticon.anticon-close{
          color: rgba(0,0,0,0.6)
        }
      }
    }
  }
}
</style>
