<!--算法模型配置-->
<template>
  <a-spin :spinning="loading">
    <a-row :gutter="24" type="flex" id="algorithmModel">
      <a-col :span="4">
        <div class="solar-eye-main-content left">
          <div class="margin-b-12">节点树
            <span class="pointer" style="float: right; font-size: 14px" @click="handleToggle">
              {{ toggleBtnShow ? '展开' : '收起' }}
              <svg-icon icon-class="up" :style="`transform: rotate(${toggleBtnShow ? 180 : 0}deg)`" />
            </span></div>
          <a-tree
            :tree-data="treeData"
            :block-node="true"
            :selectable="false"
            :expandedKeys="expandedKeys"
            @expand="onExpandedKeysChange"
          >
            <template #title="$slots">
              <div class="tree-node-edit flex-between width-100" v-if="treeNodeActiveData.editKey === $slots.key">
                <a-input
                  size="small"
                  style="width: 60%"
                  v-model="treeNodeActiveData.title"
                  placeholder="请输入"
                  autofocus
                  :max-length="50"
                />
                <div class="flex-end flex-gap-16">
                  <svg-icon icon-class="camera-delete" class-name="font-10 pointer" @click="handleEditCancel($slots)" />
                  <svg-icon icon-class="camera-right" class-name="pointer" @click="treeNodeEditConfirm($slots)" />
                </div>
              </div>
              <template v-else>
                <span
                  @click="treeNodeTitleClick($slots)"
                  class="node-title"
                  :class="{ 'active-title': treeNodeActiveData.key === $slots.key }"
                  :title="$slots.title"
                >
                  {{ $slots.title }}
                </span>
                <svg-icon
                  icon-class="camera-add"
                  class-name="pointer tree-node-hoverable"
                  v-if="$slots.level === 2"
                  @click="addTreeNodeClick($slots)"
                />
                <div
                  class="flex-end flex-gap-16 tree-node-hoverable"
                  v-if="$slots.level === 3"
                  :style="{
                    opacity: treeNodeActiveData.deleteKey && treeNodeActiveData.deleteKey === $slots.key ? 1 : null,
                  }"
                >
                  <a-popconfirm
                    placement="topLeft"
                    title="是否删除该节点及节点下所有设备"
                    ok-text="确认"
                    cancel-text="取消"
                    @cancel="treeNodeActiveData.deleteKey = null"
                    @confirm="handleNodeDelete($slots)"
                    @visibleChange="handleVisibleChange"
                  >
                    <svg-icon
                      icon-class="camera-wrong"
                      class-name="pointer"
                      style="outline: none"
                      @click="handleDeleteConfirm($slots)"
                    />
                  </a-popconfirm>
                  <svg-icon icon-class="camera-edit" class-name="pointer" @click="treeNodeEditClick($slots)" />
                </div>
              </template>
            </template>
          </a-tree>
        </div>
      </a-col>
      <a-col :span="20">
        <div class="solar-eye-search-model">
          <a-row :gutter="24" class="solar-eye-search-content">
            <a-col :xxl="6" :xl="8" :md="12">
              <div class="search-item">
                <span class="search-label">诊断原因</span>
                <a-input placeholder="请输入关键字搜索" v-model="searchParams.alarmReasonName" allowClear />
              </div>
            </a-col>
            <a-col :xxl="6" :xl="8" :md="12">
              <div class="search-item">
                <span class="search-label">是否启用</span>
                <a-select v-model="searchParams.isEnable" placeholder="请选择" allowClear>
                  <a-select-option :value="1">启用</a-select-option>
                  <a-select-option :value="0">禁用</a-select-option>
                </a-select>
              </div>
            </a-col>
            <a-col :xxl="4" :xl="6" :md="8">
              <div class="search-item">
                <throttle-button label="查询" @click="queryData()" class="solar-eye-btn-primary" />
                <throttle-button label="重置" @click="resetParams" class="solar-eye-btn-primary-cancel" />
              </div>
            </a-col>
          </a-row>
        </div>
        <div class="solar-eye-gap"></div>
        <div class="solar-eye-main-content">
          <div class="operation" style="height: 32px">
            <div class="operation-btn">
              <a-button class="solar-eye-btn-primary-cancel" @click="rowClick('1')" v-has="'algorithmModel:add'"
                >新增
              </a-button>
              <a-button
                class="solar-eye-btn-primary-cancel"
                :disabled="!selectedData.length"
                @click="handleModelDelete(selectedData)"
                v-has="'algorithmModel:patchDelete'"
                >批量删除
              </a-button>
              <a-button
                class="solar-eye-btn-primary-cancel"
                @click="handleExport"
                :loading="exportLoading"
                v-has="'algorithmModel:export'"
                >导出
              </a-button>
            </div>
          </div>
          <vxe-table
            :data="tableData"
            :height="tableHeight - 32"
            class="my-table"
            align="left"
            show-overflow
            resizable
            size="small"
            highlight-hover-row
            :seq-config="{ startIndex: (paginationParams.curPage - 1) * paginationParams.size }"
            @checkbox-all="handleCheckboxChange"
            @checkbox-change="handleCheckboxChange"
          >
            <vxe-table-column type="checkbox" width="50" fixed="left"></vxe-table-column>
            <vxe-table-column type="seq" :width="80" align="center" title="序号" />
            <vxe-table-column
              v-for="(item, index) in columns"
              :key="index"
              :field="item.key"
              :title="item.title"
              :min-width="item.width"
              :fixed="item.fixed"
            >
              <template #default="{ row }">
                <div v-if="item.key === 'isEnable'" class="flex-start flex-gap-8">
                  <a-switch v-model="row.isEnable" @change="updateEnable(row)" />
                  <span>{{ row.isEnable ? '启用' : '禁用' }}</span>
                </div>
                <span v-else> {{ row[item.key] || '--' }}</span>
              </template>
            </vxe-table-column>
            <vxe-table-column
              align="left"
              title="操作"
              fixed="right"
              width="140"
              :resizable="false"
              :visible="showHandle('algorithmModel:edit, algorithmModel:copy, algorithmModel:delete')"
            >
              <template v-slot="{ row }">
                <g-button
                  style="justify-content: flex-start"
                  @edit="rowClick('2', row)"
                  @copy="rowClick('4', row)"
                  @delete="handleModelDelete([row])"
                  :limit="3"
                  :list="[
                    {
                      icon: 'edit',
                      isSvg: true,
                      emit: 'edit',
                      name: '编辑',
                      has: 'algorithmModel:edit',
                      show: true,
                    },
                    {
                      icon: 'copy',
                      isSvg: true,
                      emit: 'copy',
                      name: '复制',
                      has: 'algorithmModel:copy',
                      show: true,
                    },
                    {
                      icon: 'delete',
                      isSvg: true,
                      emit: 'delete',
                      name: '删除',
                      has: 'algorithmModel:delete',
                      show: true,
                    },
                  ]"
                />
              </template>
            </vxe-table-column>
          </vxe-table>
          <page-pagination
            :page-size="paginationParams.size"
            :current="paginationParams.curPage"
            :total="total"
            @size-change="sizeChange"
          />
        </div>
        <drawer-view ref="drawerView" @cancel="queryData" parentId="algorithmModel" />
      </a-col>
    </a-row>
  </a-spin>
</template>
<script>
import initDict from '@/mixins/initDict';
import tableHeight from '@/mixins/tableHeightAndSearchModel';
import { LeftMixin } from '@/mixins/LeftMixin';
import { algorithmColumns } from './js/columns';
import { searchTree, eachTree } from 'xe-utils';
import uuid from 'uuid/v4';
import { uuidValidate } from '@/utils/util';
import {
  diagnosisModelDeleteApi,
  diagnosisModelNodeCreateApi,
  diagnosisModelNodeDeleteApi,
  diagnosisModelNodeUpdateApi,
  diagnosisModelUpdateApi,
  exportDiagnosisModelApi,
  getDiagnosisModelListApi,
  getDiagnosisModelNodeListApi
} from '@/api/health/healthapi';
export default {
  name: 'AlgorithmModel',
  mixins: [initDict, tableHeight, LeftMixin],
  data () {
    return {
      tableData: [],
      toggleBtnShow: true,
      // autoExpandParent: false,
      columns: algorithmColumns,
      paginationParams: {
        curPage: 1,
        size: 10
      },
      total: 0,
      treeData: [],
      treeNodeActiveData: {
        key: null,
        editKey: null,
        title: undefined,
        deleteKey: null
      },
      expandedKeys: [],
      searchParams: {
        isEnable: undefined,
        alarmReasonName: undefined,
        psType: undefined,
        deviceType: undefined,
        alarmRemark: undefined
      },
      selectedData: [],
      loading: false,
      exportLoading: false
    };
  },
  created () {
    this.init();
  },
  methods: {
    async init () {
      this.loading = true;
      try {
        await this.getModelNodeTree();
        await this.getDiagnosisModelList();
      } catch (e) {
        this.loading = false;
      }
    },
    resetTreeToFirstNode () {
      if (this.$isEmpty(this.treeData)) {
        throw new Error('树节点数据为空，无法初始化搜索参数');
      }
      const [firstLeafData = {}] = this.treeData;
      Object.assign(this.searchParams, {
        deviceType: undefined,
        alarmRemark: undefined,
        psType: firstLeafData.secondCode
      });
      Object.assign(this.treeNodeActiveData, { key: firstLeafData.key });
    },
    // 展开收起树节点
    handleToggle () {
      this.toggleBtnShow = !this.toggleBtnShow;
      if (!this.toggleBtnShow) {
        const keys = [];
        const loop = (data) => {
          data.forEach((item) => {
            keys.push(item.key);
            if (item.children) {
              loop(item.children);
            }
          });
        };
        loop(this.treeData);
        this.expandedKeys = keys;
      } else {
        this.expandedKeys = [];
      }
    },
    rowClick (type, row = {}) {
      const newRow = Object.assign({}, row, { treeData: this.treeData });
      this.$refs.drawerView.init(type, newRow, '/config/modules/AlgorithmModelForm');
    },
    async getDiagnosisModelList () {
      this.loading = true;
      const res = await getDiagnosisModelListApi(Object.assign({}, this.searchParams, this.paginationParams)).catch(
        () => {
          this.loading = false;
        }
      );
      this.tableData = res.data;
      this.total = res.total;
      this.loading = false;
    },
    async getModelNodeTree () {
      const res = await getDiagnosisModelNodeListApi();
      this.treeData = this.addLevelToTreeData(res);
    },
    handleNodeDelete (node) {
      const [firstCode, secondCode] = node.key.split('-');
      diagnosisModelNodeDeleteApi({ firstCode, secondCode }).then((res) => {
        this.$message.success('操作成功');
        if (this.treeNodeActiveData.key === node.key) {
          this.getModelNodeTree();
          this.resetTreeToFirstNode();
          this.getDiagnosisModelList();
        } else {
          this.getModelNodeTree();
        }
      });
      this.treeNodeActiveData.deleteKey = null;
    },
    handleModelDelete (list) {
      this.$confirm({
        title: '提示',
        content: '是否确认删除选中数据？',
        onOk: () => {
          diagnosisModelDeleteApi({
            list: list.map(({ firstCode, secondCode, alarmRemark, alarmReason, deviceType, inverterUnitCode }) => ({
              firstCode,
              secondCode,
              alarmRemark,
              alarmReason,
              deviceType,
              inverterUnitCode
            }))
          })
            .then((res) => {
              this.$message.success('操作成功');
              this.loading = false;
              this.getDiagnosisModelList();
            })
            .catch((e) => {
              this.loading = false;
            });
        },
        onCancel: () => {}
      });
    },
    sizeChange (curPage, size) {
      Object.assign(this.paginationParams, { curPage, size });
      this.getDiagnosisModelList();
    },

    queryData () {
      this.paginationParams = this.$options.data().paginationParams;
      this.getDiagnosisModelList();
    },
    resetParams () {
      const { paginationParams } = this.$options.data();
      Object.assign(this.searchParams, {
        isEnable: undefined,
        alarmReasonName: undefined
      });
      this.paginationParams = paginationParams;
      this.getDiagnosisModelList();
    },
    onExpandedKeysChange (keys) {
      this.expandedKeys = keys;
    },
    treeNodeTitleClick (node) {
      Object.assign(this.treeNodeActiveData, { key: node.key });
      switch (node.level) {
        case 1:
          Object.assign(this.searchParams, {
            psType: node.secondCode,
            deviceType: undefined,
            alarmRemark: undefined
          });
          break;
        case 2:
          Object.assign(this.searchParams, {
            psType: node.parentKey.split('-')[1],
            deviceType: node.secondCode,
            alarmRemark: undefined
          });
          break;
        case 3:
          Object.assign(this.searchParams, {
            psType: undefined,
            deviceType: node.parentKey.split('-')[1],
            alarmRemark: node.secondCode
          });
          break;
      }
      Object.assign(this.paginationParams, { curPage: 1, size: 10 });
      this.getDiagnosisModelList();
    },
    treeNodeEditClick (node) {
      if (searchTree(this.treeData, (item) => uuidValidate(item.key)).length) {
        this.$message.warning('请先完成当前新增节点后再继续');
        return;
      }
      Object.assign(this.treeNodeActiveData, { title: node.title, editKey: node.key });
    },
    handleEditCancel (node) {
      if (uuidValidate(node.key)) {
        eachTree(this.treeData, (item) => {
          if (item.key === node.parentKey) item.children = item.children.filter((item) => item.key !== node.key);
        });
      }
      Object.assign(this.treeNodeActiveData, { title: undefined, editKey: null });
    },
    handleDeleteConfirm (node) {
      const { deleteKey } = this.treeNodeActiveData;
      if (deleteKey && deleteKey === node.key) this.treeNodeActiveData.deleteKey = null;
      else this.treeNodeActiveData.deleteKey = node.key;
    },
    handleVisibleChange (visible) {
      if (!visible) this.treeNodeActiveData.deleteKey = null;
    },
    treeNodeEditConfirm (node) {
      const { title } = this.treeNodeActiveData;
      if (!title || !title.trim()) {
        this.$message.warning('请输入节点名称');
        return;
      }
      if (uuidValidate(node.key)) {
        const [firstCode, secondCode] = node.parentKey.split('-');
        diagnosisModelNodeCreateApi({
          parentFirstCode: firstCode,
          parentSecondCode: secondCode,
          nodeDesc: title
        }).then((res) => {
          this.$message.success('操作成功');
          this.getModelNodeTree();
        });
      } else {
        const [firstCode, secondCode] = node.key.split('-');
        diagnosisModelNodeUpdateApi({ firstCode, secondCode, nodeDesc: title }).then((res) => {
          this.$message.success('操作成功');
          this.getModelNodeTree()
            .then(() => {
              Object.assign(this.treeNodeActiveData, { editKey: null });
            })
            .catch((e) => {});
        });
      }
    },
    updateEnable (row) {
      this.loading = true;
      diagnosisModelUpdateApi({ ...row, isEnable: row.isEnable ? 1 : 0 })
        .then((res) => {
          this.loading = false;
          this.$message.success('操作成功');
        })
        .catch((e) => {
          row.isEnable = !row.isEnable;
          this.loading = false;
        });
    },
    handleCheckboxChange ({ records }) {
      this.selectedData = records;
    },
    addTreeNodeClick (node) {
      if (searchTree(this.treeData, (item) => uuidValidate(item.key)).length) {
        this.$message.warning('请先完成当前新增节点后再继续');
        return;
      }
      if (!this.expandedKeys.includes(node.key)) {
        this.expandedKeys.push(node.key);
      }
      const key = uuid();
      node.children.push({ key: key, title: null, parentKey: node.key });
      Object.assign(this.treeNodeActiveData, { title: undefined, editKey: key });
      this.$nextTick(() => {
        document.querySelector('.tree-node-edit').scrollIntoView({
          behavior: 'smooth',
          block: 'center'
        });
      });
    },
    addLevelToTreeData (treeData, level = 1, key, ...rest) {
      return treeData.map((node) => {
        const newNode = {
          ...node,
          key: node.firstCode + '-' + node.secondCode,
          level: level,
          parentKey: key,
          children: node.child,
          title: node.nodeDesc
        };
        delete newNode.child;
        if (node.child && node.child.length) {
          newNode.children = this.addLevelToTreeData(node.child, level + 1, newNode.key); // 传递当前节点的 key 作为 parentKey
        } else {
          newNode.children = [];
        }

        return newNode;
      });
    },
    handleExport () {
      this.exportLoading = true;
      const params = {
        ...this.searchParams,
        ...this.paginationParams
      };
      exportDiagnosisModelApi(params)
        .then((res) => {
          this.$downloadFile({
            fileBase64Code: res.fileBase64Code,
            fileName: res.fileName,
            fileType: res.fileType
          });
          this.exportLoading = false;
        })
        .catch((e) => {
          this.exportLoading = false;
        });
    }
  }
};
</script>

<style scoped lang="less">
.left {
  padding: 16px 12px 16px 24px;
  height: calc(100vh - 119px);

  :deep(.ant-tree) {
    height: calc(100% - 24px);
    overflow: auto;

    .ant-tree-title {
      display: flex;
      justify-content: space-between;
      align-items: center;

      & .active-title {
        color: var(--zw-primary-color--default);
      }

      .node-title {
        max-width: 70%;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }

      .tree-node-hoverable {
        opacity: 0;
        pointer-events: none;
      }
    }

    .ant-tree-treenode-switcher-close:hover,
    .ant-tree-node-content-wrapper:hover {
      background: var(--zw-primary-bg-color--hover) !important;

      .tree-node-hoverable {
        opacity: 1;
        pointer-events: auto;
      }
    }
  }
}
</style>
