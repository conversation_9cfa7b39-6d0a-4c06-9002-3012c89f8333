<template>
  <div class="detail_layout">
    <a-row style="padding: 0 16px">
      <a-col :span="7" class="detail_layout_content">
        <span class="left" style="width: 160px">试验对象{{testInfoIndex+1}}</span>
        <div class="right">
          {{ testInfoItem.testContent || '--' }}
        </div>
      </a-col>
    </a-row>
    <ReceivingTestInfoItem
      v-for='(deviceItem,index) in testInfoItem.deviceDtoList'
      :key='index'
      :testInfoIndex='testInfoIndex'
      :deviceIndex='index'
      :deviceItem='deviceItem'/>
  </div>
</template>

<script>
import ReceivingTestInfoItem from './ReceivingTestInfoItem.vue';
export default {
  name: 'ReceivedTestInfo',
  components: { ReceivingTestInfoItem },
  props: {
    testInfoItem: {
      type: Object
    },
    testInfoIndex: {
      type: Number
    }
  }
};
</script>

<style scoped lang='less'>

:deep(.table-config-box){
  margin-left: 24px;
  background: #F7F7F7;
  padding: 12px 12px 12px 36px;
  border-radius: 3px;
  .project-title{
    margin-bottom: 12px;
    .left{
      color: #666;
      font-weight: 550;
      -webkit-box-flex: 0;
      -ms-flex: none;
      flex: none;
    }
    .left::after{
      content: "\FF1A";
    }
  }

}
:deep(.detail_layout_content){
  line-height:36px;
}

:root[data-theme='dark'] {
  .device-box{
    .table-config-box{
      background: #162133;
    }
  }
}
</style>
