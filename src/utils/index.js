import { ALL_PERMISSION_MENU, USER_INFO } from '@/store/mutation-types';

// 退出登录跳转主域名登录页
export function logoutToSolareye(next = null, to = null){
  const env = process.env.VUE_APP_ENV;
  if(!env || env == 'dev'){
    if(next){
      next({
        path: '/user/login',
        query: {
          redirect: to ? to.fullPath : ''
        }
      });
      return;
    }
    try {
      let href = window.location.href;
      let path = href.split('/#')[1];
      if (path != '/' && path.indexOf('/user/login') == -1) {
        window.location.reload();
      }
    } catch (e) {
      window.location.reload();
    }
  } else {
    let work = env === 'pro' ? 'www' : env;
    // window.location.href = `http://10.5.4.115:3000/#/user/login?clear=1`;
    let navTheme = JSON.parse(localStorage.getItem("pro__DEFAULT_THEME")).value;
    window.location.href = `https://${work}.isolareye.com/#/user/login?clear=1&navTheme=${navTheme}`;
    document.title = 'SolarEye登录页';
  }
}

export function parseTime (time) {
  if (time) {
    var date = new Date(time);
    var year = date.getFullYear();
    /* 在日期格式中，月份是从0开始的，因此要加0
             * 使用三元表达式在小于10的前面加0，以达到格式统一  如 09:11:05
             * */
    var month = date.getMonth() + 1 < 10 ? '0' + (date.getMonth() + 1) : date.getMonth() + 1;
    var day = date.getDate() < 10 ? '0' + date.getDate() : date.getDate();
    // 拼接
    return year + '-' + month + '-' + day;
  } else {
    return '';
  }
}

export function addDayToDate (Time, days) {
  var a = new Date(Time);
  a = a.valueOf();
  a = a - days * 24 * 60 * 60 * 1000;
  a = new Date(a);
  return a;
}

export function nowTime () {
  var date = new Date();
  var year = date.getFullYear();
  /* 在日期格式中，月份是从0开始的，因此要加0
         * 使用三元表达式在小于10的前面加0，以达到格式统一  如 09:11:05
         * */
  var month = date.getMonth() + 1 < 10 ? '0' + (date.getMonth() + 1) : date.getMonth() + 1;
  var day = date.getDate() < 10 ? '0' + date.getDate() : date.getDate();
  var hours = date.getHours() < 10 ? '0' + date.getHours() : date.getHours();
  var minutes = date.getMinutes() < 10 ? '0' + date.getMinutes() : date.getMinutes();
  var seconds = date.getSeconds() < 10 ? '0' + date.getSeconds() : date.getSeconds();
  // 拼接
  return year + month + day + hours + minutes + seconds;
}

export function parseTimeYmdhm (time) {
  if (time) {
    var date = new Date(time);
    var year = date.getFullYear();
    /* 在日期格式中，月份是从0开始的，因此要加0
             * 使用三元表达式在小于10的前面加0，以达到格式统一  如 09:11:05
             * */
    var month = date.getMonth() + 1 < 10 ? '0' + (date.getMonth() + 1) : date.getMonth() + 1;
    var day = date.getDate() < 10 ? '0' + date.getDate() : date.getDate();
    var hours = date.getHours() < 10 ? '0' + date.getHours() : date.getHours();
    var minutes = date.getMinutes() < 10 ? '0' + date.getMinutes() : date.getMinutes();
    // 拼接
    return year + '-' + month + '-' + day + ' ' + hours + ':' + minutes;
  } else {
    return '';
  }
}

// 获得本周的开始时间
export function getStartDayOfWeek () {
  var now = new Date(); // 当前日期
  var nowDayOfWeek = now.getDay(); // 今天本周的第几天
  var day = nowDayOfWeek || 7;
  return formatDate(new Date(now.getFullYear(), now.getMonth(), now.getDate() + 1 - day));
}

// 获得本周的结束时间
export function getEndDayOfWeek () {
  var now = new Date(); // 当前日期
  var nowDayOfWeek = now.getDay(); // 今天本周的第几天
  var day = nowDayOfWeek || 7;
  return formatDate(new Date(now.getFullYear(), now.getMonth(), now.getDate() + 7 - day));
}

export function formatDate (date) {
  var myyear = date.getFullYear();
  var mymonth = date.getMonth() + 1;
  var myweekday = date.getDate();
  if (mymonth < 10) {
    mymonth = '0' + mymonth;
  }
  if (myweekday < 10) {
    myweekday = '0' + myweekday;
  }
  return (myyear + '-' + mymonth + '-' + myweekday);
}

// 获取本周的第一天
export function getFirstDayOfCurrWeek () {
  // 获取星期几,getDay()返回值是 0（周日） 到 6（周六） 之间的一个整数。0||7为7，即weekday的值为1-7
  var date = new Date();
  var weekday = date.getDay() || 7;
  // 往前算（weekday-1）天，年份、月份会自动变化
  date.setDate(date.getDate() - weekday + 1);
  return parseTimeYmd(date);
}

// 判断字符串是否为空
export function isEmpty (str) {
  if (!str || str === undefined || str === null || str === '' || str === '-') {
    return '-';
  }
  return str;
}

export function parseTime2 (time) {
  if (time) {
    var date = new Date(time);
    var year = date.getFullYear();
    /* 在日期格式中，月份是从0开始的，因此要加0
             * 使用三元表达式在小于10的前面加0，以达到格式统一  如 09:11:05
             * */
    var month = date.getMonth() + 1 < 10 ? '0' + (date.getMonth() + 1) : date.getMonth() + 1;
    var day = date.getDate() < 10 ? '0' + date.getDate() : date.getDate();
    var hours = date.getHours() < 10 ? '0' + date.getHours() : date.getHours();
    var minutes = date.getMinutes() < 10 ? '0' + date.getMinutes() : date.getMinutes();
    var seconds = date.getSeconds() < 10 ? '0' + date.getSeconds() : date.getSeconds();
    // 拼接
    return '' + year + month + day + hours + minutes + seconds;
  } else {
    return '';
  }
}

export function parseTimeYmd (time) {
  if (time) {
    var date = new Date(time);
    var year = date.getFullYear();
    /* 在日期格式中，月份是从0开始的，因此要加0
             * 使用三元表达式在小于10的前面加0，以达到格式统一  如 09:11:05
             * */
    var month = date.getMonth() + 1 < 10 ? '0' + (date.getMonth() + 1) : date.getMonth() + 1;
    var day = date.getDate() < 10 ? '0' + date.getDate() : date.getDate();
    // 拼接
    return year + '-' + month + '-' + day;
  } else {
    return '';
  }
}

export function parseTimeYm (time) {
  if (time) {
    var date = new Date(time);
    var year = date.getFullYear();
    /* 在日期格式中，月份是从0开始的，因此要加0
             * 使用三元表达式在小于10的前面加0，以达到格式统一  如 09:11:05
             * */
    var month = date.getMonth() + 1 < 10 ? '0' + (date.getMonth() + 1) : date.getMonth() + 1;
    // 拼接
    return year + '-' + month;
  } else {
    return '';
  }
}

export function formatTime (time, option) {
  time = +time * 1000;
  const d = new Date(time);
  const now = Date.now();
  const diff = (now - d) / 1000;
  if (diff < 30) {
    return '刚刚';
  } else if (diff < 3600) {
    // less 1 hour
    return Math.ceil(diff / 60) + '分钟前';
  } else if (diff < 3600 * 24) {
    return Math.ceil(diff / 3600) + '小时前';
  } else if (diff < 3600 * 24 * 2) {
    return '1天前';
  }
  if (option) {
    return parseTime(time, option);
  } else {
    return (
      d.getMonth() +
            1 +
            '月' +
            d.getDate() +
            '日' +
            d.getHours() +
            '时' +
            d.getMinutes() +
            '分'
    );
  }
}

export function getCurrentMonthFirst () {
  var date = new Date();
  date.setDate(1);
  var month = parseInt(date.getMonth() + 1);
  var day = date.getDate();
  if (month < 10) {
    month = '0' + month;
  }
  if (day < 10) {
    day = '0' + day;
  }
  return date.getFullYear() + '-' + month + '-' + day;
}

export function getCurrentMonthLast () {
  var date = new Date();
  var currentMonth = date.getMonth();
  var nextMonth = ++currentMonth;
  var nextMonthFirstDay = new Date(date.getFullYear(), nextMonth, 1);
  var oneDay = 1000 * 60 * 60 * 24;
  var lastTime = new Date(nextMonthFirstDay - oneDay);
  var month = parseInt(lastTime.getMonth() + 1);
  var day = lastTime.getDate();
  if (month < 10) {
    month = '0' + month;
  }
  if (day < 10) {
    day = '0' + day;
  }
  return date.getFullYear() + '-' + month + '-' + day;
}

export function debounce (func, wait, immediate) {
  let timeout, args, context, timestamp, result;

  const later = function () {
    // 据上一次触发时间间隔
    const last = +new Date() - timestamp;

    // 上次被包装函数被调用时间间隔last小于设定时间间隔wait
    if (last < wait && last > 0) {
      timeout = setTimeout(later, wait - last);
    } else {
      timeout = null;
      // 如果设定为immediate===true，因为开始边界已经调用过了此处无需调用
      if (!immediate) {
        result = func.apply(context, args);
        if (!timeout) context = args = null;
      }
    }
  };
  return function (...args) {
    context = this;
    timestamp = +new Date();
    const callNow = immediate && !timeout;
    // 如果延时不存在，重新设定延时
    if (!timeout) timeout = setTimeout(later, wait);
    if (callNow) {
      result = func.apply(context, args);
      context = args = null;
    }
    return result;
  };
}

export function isExternal (path) {
  return /^(https?:|mailto:|tel:)/.test(path);
}

// 替换邮箱字符
export function regEmail (email) {
  if (String(email).indexOf('@') > 0) {
    const str = email.split('@');
    let _s = '';
    if (str[0].length > 3) {
      for (var i = 0; i < str[0].length - 3; i++) {
        _s += '*';
      }
    }
    var new_email = str[0].substr(0, 3) + _s + '@' + str[1];
  }
  return new_email;
}

// 替换手机字符
export function regMobile (mobile) {
  if (mobile.length > 7) {
    var new_mobile = mobile.substr(0, 3) + '****' + mobile.substr(7);
  }
  return new_mobile;
}

// 转换为驼峰命名
export function toHump (name) {
  return name.replace(/\_(\w)/g, function (all, letter) {
    return letter.toUpperCase();
  });
}

/**
 * Check if an element has a class
 * @param {HTMLElement} elm
 * @param {string} cls
 * @returns {boolean}
 */
export function hasClass (ele, cls) {
  return !!ele.className.match(new RegExp('(\\s|^)' + cls + '(\\s|$)'));
}

/**
 * Add class to element
 * @param {HTMLElement} elm
 * @param {string} cls
 */
export function addClass (ele, cls) {
  if (!hasClass(ele, cls)) ele.className += ' ' + cls;
}

/**
 * Remove class from element
 * @param {HTMLElement} elm
 * @param {string} cls
 */
export function removeClass (ele, cls) {
  if (hasClass(ele, cls)) {
    const reg = new RegExp('(\\s|^)' + cls + '(\\s|$)');
    ele.className = ele.className.replace(reg, ' ');
  }
}

// import { delFile } from "@/api/common_gy/common";
export function downloadFile (sendUrl) {
  window.location.href = sendUrl;
}

export function generateUuid () {
  var s = [];
  var random = Math.random();
  var hexDigits = random.toString().split('0.')[1];
  for (var i = 0; i < 32; i++) {
    s[i] = hexDigits.substr(Math.floor(Math.random() * 0x10), 1);
  }
  s[14] = '4'; // bits 12-15 of the time_hi_and_version field to 0010
  s[19] = hexDigits.substr((s[19] & 0x3) | 0x8, 1); // bits 6-7 of the clock_seq_hi_and_reserved to 01
  s[8] = s[13] = s[18] = s[23];
  var uuid = s.join('');
  return uuid;
}

/*
 * 带权限判断的路由跳转
 * @param {url} 地址
 * @param {that} this
 * @return 跳转成功或失败
 * */
export function permissionRouterGo (url, that) {
  let menuList = JSON.parse(sessionStorage.getItem(ALL_PERMISSION_MENU));
  console.log(menuList);
  if (menuList.find(item => (item == url))) {
    that.$router.push(url);
    return true;
  } else {
    that.$message.warning('您当前暂无权限，请联系管理员！');
    return false;
  }
}
export function getRolesList () {
  let roleList = Vue.ls.get(USER_INFO);
  let arr = [];
  roleList.roles && roleList.roles.forEach(item => {
    arr.push(item.roleCode);
  });
  return arr;
}

export function isLoadedJs (jsName, callback) {
  let isNeed = process.env.NODE_ENV == 'production' ? '' : '/';
  if ($(`script[src='${isNeed}${jsName}']`).length > 0) {
    // if (jsName.indexOf('aliyun-oss-sdk-6.17.1.min.js') > -1 || jsName.indexOf('')) {
    callback && callback();
    // }
  } else {
    const scriptNode = document.createElement('script');
    scriptNode.src = jsName.indexOf('https://staticres') > -1 ? jsName : __webpack_public_path__ + jsName;
    scriptNode.async = true;
    scriptNode.addEventListener('load', function () {
      callback && callback();
    }, false);
    document.body.appendChild(scriptNode);
  }
}
