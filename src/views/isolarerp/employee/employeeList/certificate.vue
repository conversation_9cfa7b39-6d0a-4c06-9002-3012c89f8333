<template>
  <!-- 资格证书 -->
  <a-row>
    <a-spin :spinning="loading">
      <a-col v-show="show || type" :span="24" class="top-btn-group">
        <a-button size="default" class="solar-eye-btn-primary" @click="addCertificate" style="margin-right:10px;">新增</a-button>
        <a-button size="default" @click="delCertificate">删除</a-button>
      </a-col>
      <a-col :span="24" class='margin-bottom'>*低压电工证、高压电工证和高处作业证上传清晰附件照片后系统可自动识别信息</a-col>
      <a-col :span="24">
        <vxe-table class="emp-table" ref="certificateTable" :data="certificateTable" :edit-rules="rules" resizable align="center" max-height="457"
          :edit-config="{mode:'row', showStatus: true}" border show-overflow highlight-hover-row size="small">
          <vxe-table-column type="checkbox" fixed="left" width="60"></vxe-table-column>
          <vxe-table-column type="seq" title="序号" width="80"></vxe-table-column>
          <vxe-table-column show-overflow="title" field="certificateName" title="证书名称" width="180">

            <template v-slot="{ row }">
              <a-select v-if="show || row.edit" v-model="row.certificateName" @change="certificateNameChange(row)" size="default" style="width: 95%;" :disabled="pageFrom=='individual'&& row.autoRecognized==1">
                <a-select-option v-for="(option,index) in dictMap.certificate" :key="index" :value="option.dataValue">{{option.dataLable}}</a-select-option>
              </a-select>
              <span v-else>{{getLabel(row.certificateName,dictMap['certificate'])}}</span>
            </template>
          </vxe-table-column>
          <vxe-table-column show-overflow="title" field="certAwardDep" title="颁发机构" min-width="200">
            <template v-slot="{ row }">
              <a-input v-model="row.certAwardDep" v-if="show || row.edit" :maxLength='100' placeholder="请输入内容" size="default" @blur="row.certAwardDep = $trim($event)"
                style="width: 95%;" :disabled="pageFrom=='individual'&& row.autoRecognized==1"></a-input>
              <span v-else>{{getLabel(row.certAwardDep,null)}}</span>
            </template>
          </vxe-table-column>
          <vxe-table-column show-overflow="title" field="certFirstDate" title="初领日期" width="150">
            <template v-slot="{ row }">
              <a-date-picker v-model="row.certFirstDate" v-if="show || row.edit || (hasExcu && pageFrom == 'individual') " format="YYYY-MM-DD" valueFormat="YYYY-MM-DD" size="default"
                style="width: 95%;" :disabled="( pageFrom=='individual'&& row.sysTenantId) || (pageFrom=='individual'&& row.autoRecognized==1 )" />
              <span v-else>{{getLabel(row.certFirstDate,null)}}</span>
            </template>
          </vxe-table-column>
          <vxe-table-column show-overflow="title" field="certStartDate" title="开始日期" width="150">
            <template slot-scope="scope">
              <a-date-picker v-model="scope.row.certStartDate" v-if="show || scope.row.edit || (hasExcu && pageFrom == 'individual')" format="YYYY-MM-DD" valueFormat="YYYY-MM-DD" size="default"
                style="width: 95%;" :disabled="( pageFrom=='individual'&& scope.row.sysTenantId) || (pageFrom=='individual'&& scope.row.autoRecognized==1)"
                @change="(date, dateStrings)=> setCetificateTime(scope.row.certificateName,date,dateStrings,scope.$rowIndex)" />
              <span v-else>{{getLabel(scope.row.certStartDate,null)}}</span>
            </template>
          </vxe-table-column>
          <vxe-table-column show-overflow="title" field="certEndDate" title="结束日期" width="150">
            <template v-slot="{ row }">
              <a-date-picker v-model="row.certEndDate" v-if="show || row.edit ||(hasExcu && pageFrom == 'individual' )" format="YYYY-MM-DD" valueFormat="YYYY-MM-DD" size="default"
                style="min-width: 90%;" :disabled=" pageFrom=='individual'&& row.sysTenantId || (pageFrom=='individual'&& row.autoRecognized==1)" />
              <span v-else>{{getLabel(row.certEndDate,null)}}</span>
            </template>
          </vxe-table-column>
          <vxe-table-column show-overflow="title" field="certLvl" title="等级" width="120">
            <template v-slot="{ row }">
              <a-select v-if="show || row.edit" :disabled="row.certificateName != '8'&&row.certificateName != '9'&&row.certificateName !='4' || (pageFrom=='individual'&& row.autoRecognized==1)" v-model="row.certLvl" size="default" style="width: 95%;"
                @change="changeLevel(row)">
                <a-select-option v-for="(option,index) in row.certificateName == '4'? dictMap.safety_cert_lvl:dictMap.cert_lvl" :key="index" :value="option.dataValue">{{option.dataLable}}
                </a-select-option>
              </a-select>
              <span v-else>
                <span v-if="row.certificateName == '4'">{{getLabel(row.certLvl,dictMap['safety_cert_lvl'])}}</span>
                <span v-else>{{getLabel(row.certLvl,dictMap['cert_lvl'])}}</span>
              </span>
            </template>
          </vxe-table-column>
          <vxe-table-column field="dispatching" title="调度地区" width="200">
            <template v-slot="{ row }">
              <a-cascader v-model="row.dispatching" v-if="(show || row.edit) && row.certificateName == '3'" change-on-select
                :options="options" :fieldNames="{label: 'AREA_NAME', value: 'AREA_ID', children: 'childCity'}"
                placeholder="请选择" style="width: 100%;" :disabled="pageFrom=='individual'&& row.autoRecognized==1" />
              <span v-else>{{getLabel(row.dispatchingAreaName,null)}}</span>
            </template>
          </vxe-table-column>

          <!-- <vxe-table-column field="isInCompany" title="是否在公司考取" width="120">
            <template v-slot="{ row }">
              <a-select v-model="row.isInCompany" size="default" v-if="(show || row.edit) && !type" @change="isInCompanyChange(row)">
                <a-select-option value="1">是</a-select-option>
                <a-select-option value="0">否</a-select-option>
              </a-select>
              <span v-else>{{getLabel(row.isInCompany,dictMap['yn'])}}</span>
            </template>
          </vxe-table-column>
          <vxe-table-column field="isReimb" title="是否报销费用" width="120">
            <template v-slot="{ row }">
              <a-select v-model="row.isReimb" size="default" v-if="(show || row.edit) && row.isInCompany == '1' && !type" @change="isReimbChange(row)">
                <a-select-option value="1">是</a-select-option>
                <a-select-option value="0">否</a-select-option>
              </a-select>
              <span v-else>{{getLabel(row.isReimb,dictMap['yn'])}}</span>
            </template>
          </vxe-table-column>
          <vxe-table-column show-overflow="title" field="reimbCost" title="报销金额" width="120">
            <template v-slot="{ row }">
              <a-input-number v-model="row.reimbCost" :min='0' :max="9999.99" :precision='2' v-if="(show || row.edit) && row.isReimb == '1' && !type" size="default" style="width: 95%;">
              </a-input-number>
              <span v-else>{{getLabel(row.reimbCost,null)}}</span>
            </template>
          </vxe-table-column> -->
          <vxe-table-column show-overflow="title" field="approveDate" title="复审日期" width="150">
            <template v-slot="{ row }">
              <!-- show表示新增和编辑状态 ，row.edit表示行新增 -->
              <a-date-picker v-model="row.approveDate" v-if="show || row.edit ||(hasExcu && pageFrom == 'individual')" format="YYYY-MM-DD" valueFormat="YYYY-MM-DD" size="default"
                style="min-width: 90%;" :disabled="(pageFrom=='individual'&& row.sysTenantId)|| (pageFrom=='individual'&& row.autoRecognized==1)" />
              <span v-else>{{getLabel(row.approveDate, null)}}</span>
            </template>
          </vxe-table-column>
          <vxe-table-column title="附件" width="200" fixed="right" align="center">
            <template slot-scope="scope">
            <a-spin :spinning="spinning" v-show="scope.row.spinning"></a-spin>
              <upload-vue v-show="(show || scope.row.edit) && !scope.row.spinning" v-model="scope.$rowIndex" :accept="accept" @change="fileChange" @loading="loadEvent" />
              <a-button v-show="scope.row.annexName || scope.row.fileName" size="default" icon="read" :loading="loading" title="照片预览" @click="lookOrDown(scope.row,'look')"></a-button>
              <a-button v-show="scope.row.annexName" size="default" icon="arrow-down" :loading="loading" title="照片下载" @click="lookOrDown(scope.row,'down')"></a-button>
              <a-button v-show="(show || scope.row.edit) && scope.row.annexName" size="default" icon="delete" :loading="loading" title="附件删除" @click="deleteAnnex(scope.$rowIndex)"></a-button>
            </template>
          </vxe-table-column>
          <vxe-table-column :visible="type" title="操作" fixed="right" width="120" :resizable="false">
            <template slot-scope="scope">
              <template v-if="scope.row.edit">
                <a-button size="default" icon="save" :loading="loading" title="保存" @click="saveRow(scope.$rowIndex)"></a-button>
                <a-button size="default" icon="arrow-left" title="取消" @click="cacelRow(scope.$rowIndex)"></a-button>
              </template>
              <template v-else>
                <a-button size="default" type="primary" icon="form" title="编辑" @click="editRow(scope.$rowIndex)"></a-button>
              </template>
            </template>
          </vxe-table-column>
        </vxe-table>
      </a-col>
    </a-spin>
    <pic-view v-model="visible" :open="visible" :path="picPath" :isImg="isImg" @change="close" />
  </a-row>
</template>

<script>
import moment from 'moment';
import picView from './picView';
import initDict from '@/mixins/initDict';
import uploadVue from './uploadViewImg';
import { TENANT_ID, USER_NAME } from '@/store/mutation-types';
// import { getSystemDate } from '@/api/isolarErp/dutymanage/dutymanage';
import { saveCertificate, downloadBizAnnex, delCertificateById, queryProvinceAndCity } from '@/api/isolarErp/employee/employee';
export default {
  components: {
    uploadVue,
    picView
  },
  mixins: [initDict],
  props: {
    type: {
      type: Boolean,
      default: false
    },
    empId: {
      type: [String, Number],
      default: ''
    },
    empName: {
      type: String,
      default: undefined
    },
    // individual: 个人主页  individualList: 人员档案
    pageFrom: {
      type: String,
      default: undefined
    }
  },

  data () {
    const certEndDateValid = ({ row }) => {
      if (!row.certEndDate && (row.certificateName == '4' || row.certificateName == '1' || row.certificateName == '2' || row.certificateName == '14')) {
        return new Error('请选择结束日期');
      }
    };
    const approveDateValid = ({ row }) => {
      if (!row.approveDate && (row.certificateName == '1' || row.certificateName == '2' || row.certificateName == '14')) {
        return new Error('请选择复审日期');
      }
    };
    return {
      show: false,
      showType: '', // 1新增 2编辑
      hasExcu: false,
      options: [], // 省市数据下拉
      certificateTable: [],
      uploadViewImgDialog: false,
      upload: null,
      accept: '.jpg, .png, .jpeg, .bmp, .pdf',
      loading: false,
      rules: {
        certificateName: [
          { required: true, message: '请选择证书名称', trigger: 'change' }
        ],
        certStartDate: [
          { required: true, message: '请选择开始日期', trigger: 'change' }
        ],
        certFirstDate: [
          { required: true, message: '请选择初领日期', trigger: 'change' }
        ],
        certEndDate: [
          { validator: certEndDateValid, trigger: 'blur' }
        ],
        approveDate: [
          { validator: approveDateValid, trigger: 'blur' }
        ]
      },
      visible: false,
      isImg: true,
      picPath: '',
      oldRow: null,
      pdfViewer: ''
    };
  },
  created () {
    this.getDictMap('certificate,cert_lvl,safety_cert_lvl,yn');
    this.getOptions();
    // 本地调试需要public目录
    let path = process.env.NODE_ENV == 'development' ? '/static/' : './static/';
    this.pdfViewer = path + 'pdfjs-dist/web/viewer.html?file=';
  },
  methods: {
    moment,
    // 初始化数据
    init (data, type) {
      this.show = (type && type != '3');
      this.showType = type;
      data = (data || []);
      data.forEach(item => {
        item.edit = false;
        item.dispatching = (item.dispatchingArea == '--' ? [] : (item.dispatchingArea).split('/'));
      });
      this.certificateTable = data;
      this.hasExcu = this.$store.getters.userInfo.roles.some((item) =>
        item.roleCode == 'r10009'
      );
    },
    loadEvent (index, isTrue) {
      this.$set(this.certificateTable[index], 'spinning', true);
    },
    setCetificateTime (certificateName, certStartDate, dateStrings, rowId) {
      if (certificateName == '1' || certificateName == '2' || certificateName == '14') { // 低压、高压、高处
        if (!certStartDate) {
          this.$set(this.certificateTable[rowId], 'certEndDate', null);
          this.$set(this.certificateTable[rowId], 'approveDate', null);
        } else {
          let certEndDateString = moment(certStartDate).add(6, 'years').subtract(1, 'days').format('YYYY-MM-DD');
          let approveDateString = moment(certStartDate).add(3, 'years').subtract(1, 'days').format('YYYY-MM-DD');
          this.$set(this.certificateTable[rowId], 'certEndDate', certEndDateString);
          this.$set(this.certificateTable[rowId], 'approveDate', approveDateString);
        }
      } else if (certificateName == '4') { // 安全证
        if (!certStartDate) {
          this.$set(this.certificateTable[rowId], 'certEndDate', null);
        } else {
          let certEndDateString = moment(certStartDate).add(3, 'years').subtract(1, 'days').format('YYYY-MM-DD');
          this.$set(this.certificateTable[rowId], 'certEndDate', certEndDateString);
        }
      }
    },
    /*
          获取省市下拉数据
        */
    getOptions () {
      queryProvinceAndCity({}).then(res => {
        this.options = res.result_data;
      }).catch(() => {
        this.options = [];
      });
    },
    // 主页-获取数据
    getData () {
      let data = JSON.parse(JSON.stringify(this.certificateTable));
      data.forEach(item => {
        item.dispatchingArea = (Array.isArray(item.dispatching) ? (item.dispatching).join('/') : '');
      });
      return data;
    },
    // 数据校验
    async validate () {
      const errMap = await this.$refs['certificateTable'].validate(true).catch(errMap => errMap);
      if (errMap) {
        return errMap;
      }
      let certificates = this.certificateTable;
      return this.checkData(certificates);
    },
    checkData (certificates) {
      try {
        // let types = [];
        certificates.forEach((item, index) => {
          if (item.certStartDate > item.certEndDate) {
            throw new Error('资格证书第' + (index + 1) + '行结束日期需大于开始日期');
          }
          if (item.certFirstDate > item.certStartDate) {
            throw new Error('资格证书第' + (index + 1 + '行开始日期需大于初领日期'));
          }
          if (item.certStartDate > item.approveDate) {
            throw new Error('资格证书第' + (index + 1) + '行复审日期需大于开始日期');
          }
          // if (item.isInCompany == '1' && !['1', '0'].includes(item.isReimb)) {
          //   throw new Error('资格证书第' + (index + 1) + '行请选择是否报销费用');
          // }
          // if (item.isReimb == '1' && (typeof item.reimbCost !== 'number')) {
          //   throw new Error('资格证书第' + (index + 1) + '行请填写报销金额');
          // }
          if (!item.fileName && !item.annexName) {
            throw new Error('资格证书第' + (index + 1) + '行需上传附件');
          }
        });
        // types.forEach(type=>{
        //   let certificateName = this.getLabel(type,this.dictMap['certificate']);
        //   let start = [];
        //   let end = [];
        //   this.certificateTable.forEach(item => {
        //     if(type == item.certificateName){
        //       start.push(item.certStartDate);
        //       end.push(item.certEndDate);
        //     }
        //   });
        //   start = start.sort();
        //   end = end.sort();
        //   // 判断时间区间交叉
        //   start.forEach((item,index)=>{
        //     if (index > 0 && item <= end[index-1]){
        //       throw new Error(certificateName+'时间区间有重叠');
        //     }
        //   });
        // })
        return null;
      } catch (e) {
        this.$message.warning(e.message);
        return true;
      }
    },
    // 证书名称change事件 职称证书时 等级可选
    certificateNameChange (row) {
      if (row.certificateName != '8' && row.certificateName != '9' && row.certificateName != '4') {
        row.certLvl = undefined;
      }
      if (row.certificateName != '3') {
        row.dispatching = undefined;
      }
      this.$refs['certificateTable'].reloadRow(row);
    },
    // 是否公司考取change事件
    isInCompanyChange (row) {
      if (row.isInCompany != '1') {
        row.isReimb = undefined;
        row.reimbCost = undefined;
      }
      this.$refs['certificateTable'].reloadRow(row);
    },
    changeLevel (row) {
      this.$forceUpdate();
    },
    // 是否报销费用change事件
    isReimbChange (row) {
      if (row.isReimb != '1') {
        row.reimbCost = undefined;
      }
      this.$refs['certificateTable'].reloadRow(row);
    },
    // 添加
    addCertificate () {
      let row = {
        certificateName: '',
        certAwardDep: '',
        certStartDate: '',
        certEndDate: null,
        certPic: '',
        isInCompany: '0',
        isReimb: '0',
        reimbCost: 0,
        dispatching: undefined,
        approveDate: null,
        autoRecognized: 0, // 0 用户填写，1 自动识别
        spinning: false
      };
      if (this.type) {
        for (let item of this.certificateTable) {
          if (item.edit) {
            this.$message.warning('请先保存当前编辑的数据!');
            return;
          }
        }
        row.edit = true;
        this.oldRow = null;
      }
      this.certificateTable.unshift(row);
    },
    // 行编辑
    editRow (index) {
      for (let item of this.certificateTable) {
        if (item.edit) {
          this.$message.warning('请先保存当前编辑的数据!');
          return;
        }
      }
      this.oldRow = Object.assign({}, this.certificateTable[index]);
      this.certificateTable[index].edit = true;
    },
    // 行保存
    async saveRow (index) {
      let self = this;
      self.loading = true;
      const errMap = await self.$refs['certificateTable'].validate(self.certificateTable[index]).catch(errMap => errMap);
      if (errMap) {
        self.loading = false;
        return errMap;
      }
      let row = self.certificateTable[index];
      let check = self.checkData([row]);
      if (check) {
        self.loading = false;
        return errMap;
      }
      self.$confirm({
        title: '确定保存修改吗?',
        okText: '确定',
        cancelText: '取消',
        onOk () {
          row.sysTenantId = Vue.ls.get(TENANT_ID);
          row.userAccount = Vue.ls.get(USER_NAME);
          row.empId = self.empId;
          row.dispatchingArea = (Array.isArray(row.dispatching) ? (row.dispatching).join('/') : '');
          saveCertificate({ 'empName': self.empName, 'certificate': [row] }).then(res => {
            self.loading = false;
            if (res.result_code == '1') {
              self.$message.success('操作成功');
              self.certificateTable[index].edit = false;
              self.$emit('refresh');
            }
          }).catch(() => {
            self.certificateTable[index].sysTenantId = undefined;
            self.$nextTick(() => {
              self.$refs.certificateTable.updateData();
            });
            self.loading = false;
          });
        },
        onCancel () {
          self.loading = false;
        }
      });
    },
    // 行-取消保存
    cacelRow (index) {
      let self = this;
      self.$confirm({
        title: '确定放弃修改吗?',
        okText: '确定',
        cancelText: '取消',
        onOk () {
          // 用于区分修改的是已有数据还是新加的数据
          let oldRow = self.oldRow;
          if (oldRow) {
            self.certificateTable[index] = Object.assign({}, oldRow);
          } else {
            self.certificateTable.splice(index, 1);
          }
          self.oldRow = null;
          self.$refs.certificateTable.reloadData(self.certificateTable);
          self.$forceUpdate();
        }
      });
    },
    // 删除
    delCertificate () {
      this.loading = true;
      let select = this.$refs.certificateTable.getCheckboxRecords();
      if (select.length < 1) {
        this.$message.warning('请至少勾选一条数据');
        this.loading = false;
        return;
      }
      this.loading = false;
      let self = this;
      self.$confirm({
        title: '确定要删除吗?',
        okText: '确定',
        cancelText: '取消',
        onOk () {
          if (self.type) {
            let ids = [];
            select.forEach(item => {
              if (item.id) {
                ids.push(item.id + '');
              }
            });
            if (ids.length == 0) {
              self.remove(select);
            } else {
              self.loading = true;
              delCertificateById({ 'ids': ids, 'isIndividual': '1' }).then(res => {
                self.loading = false;
                self.$message.info('数据删除成功');
                self.remove(select);
              }).catch(() => {
                self.loading = false;
              });
            }
          } else {
            self.remove(select);
            /* 下面的逻辑暂时不要 */
            // self.loading = true;
            // getSystemDate({}).then(res => {
            //   let now = moment(res.systemDate).valueOf();
            //   let row = select.find(item => item.id && ((!item.certEndDate && !item.approveDate) || !((item.certEndDate && now >= moment(item.certEndDate).subtract(1, 'months').valueOf()) || (item.approveDate && now >= moment(item.approveDate).subtract(1, 'months').valueOf()))));
            //   if (row) {
            //     self.$notification.error({
            //       message: '系统提示',
            //       description: '只能删除到期前一个月的证书！'
            //     });
            //   } else {
            //     self.remove(select);
            //   }
            //   self.loading = false;
            // }).catch(() => {
            //   self.loading = false;
            // });
          }
        }
      });
    },
    // 页面移除数据
    remove (data) {
      let _XIDS = [];
      data.forEach(item => {
        _XIDS.push(item._XID);
      });
      this.certificateTable = this.certificateTable.filter(item => !_XIDS.includes(item._XID));
    },
    // 照片更新
    fileChange (index, obj, data) {
      Object.assign(this.certificateTable[index], obj, { 'file': obj.file, 'path': 'data:image/jpg;base64,'.concat(obj.file), 'fileName': obj.fileName, 'annexName': obj.fileName, 'spinning': false });
      this.$nextTick(() => {
        this.$refs.certificateTable.updateData();
      });
    },
    // 图片删除
    deleteAnnex (index) {
      let self = this;
      self.$confirm({
        title: '确定删除此附件吗?',
        okText: '确定',
        cancelText: '取消',
        onOk () {
          Object.assign(self.certificateTable[index], { 'file': '', 'path': '', 'fileName': '', 'annexName': '', 'pathName': '', autoRecognized: 0 });
          self.$nextTick(() => {
            self.$refs.certificateTable.updateData();
          });
        }
      });
    },
    // 图片预览\下载
    lookOrDown (row, type) {
      const self = this;
      self.loading = true;
      let fileId = (row.hasOwnProperty('fileId') ? row.fileId : '');
      let annexName = row.annexName || row.fileName;
      let path = (row.hasOwnProperty('path') ? row.path : '');
      // path有值表示图片是本地上传的，不需要请求后台加载图片
      if (path) {
        self.loading = false;
        if (type == 'look') {
          let data = {
            fileBase64Code: path.split(',')[1],
            fileType: (annexName.substring(annexName.length - 3, annexName.length + 1) == 'pdf' ? 'application/pdf' : '')
          };
          self.picPath = self.getFilePath(data);
          self.visible = true;
        } else {
          const resultData = {
            fileBase64Code: path,
            fileType: '',
            fileName: annexName
          };
          self.$downloadFile(resultData);
        }
      } else {
        downloadBizAnnex({ 'fileId': fileId }).then(res => {
          self.loading = false;
          if (res.result_code == '1') {
            if (type == 'look') {
              self.picPath = self.getFilePath(res.result_data);
              self.visible = true;
            } else {
              self.$downloadFile(res.result_data);
            }
          } else {
            self.$message.warning(res.result_msg);
          }
        }).catch(() => {
          self.loading = false;
        });
      }
    },
    getFilePath (data) {
      if (!data) {
        return '';
      }
      if (data.fileType == 'application/pdf') {
        this.isImg = false;
        let atob = window.atob(data.fileBase64Code);
        let l = atob.length;
        let u8Arr = new Uint8Array(l);
        while (l--) {
          u8Arr[l] = atob.charCodeAt(l);
        }
        if (window.createObjectURL != undefined) { // basic
          return window.createObjectURL(new Blob([u8Arr], {
            type: 'application/pdf'
          })) + '#toolbar=1';
        } else if (window.webkitURL != undefined) { // webkit or chrome
          return window.webkitURL.createObjectURL(new Blob([u8Arr], {
            type: 'application/pdf'
          })) + '#toolbar=1';
        } else if (window.URL != undefined) { // mozilla(firefox)
          return window.URL.createObjectURL(new Blob([u8Arr], {
            type: 'application/pdf'
          })) + '#toolbar=1';
        }
        completion();
      } else {
        this.isImg = true;
        return 'data:image/jpg;base64,'.concat(data.fileBase64Code);
      }
    },
    // 关闭预览窗口
    close () {
      this.visible = false;
      this.$nextTick(() => {
        this.picPath = '';
        this.isImg = true;
      });
    }
  }
};
</script>

<style lang="less" scoped>
</style>
