<template>
  <div class="order-dispose">
    <template v-if="getStatus() || type()==3">
      <!--工单来源 诊断-->
      <diagnosis-detail v-if="order().taskType=='8' || order().taskType=='7' ||  order().orderSource==4 ||  order().orderSource==5" ref='disposeForm' />
      <!--工单来源 巡检-->
      <inspection v-else-if="order().orderSource=='10'" ref="disposeForm" />
      <!--工单来源 无人机巡检任务-->
      <div v-else-if="order().taskType=='10'">
        <div class="view_detail_box">
          <div class="title-box">
            <span class="before"></span>
            <span>执行信息</span>
          </div>
        </div>
        <tango-task ref="disposeForm" :catalog="catalog" @setUploadStatus="setUploadStatus" @setLoading="setLoading"></tango-task>
      </div>
      <!--工单来源 其他-->
      <base-dispose-detail v-else ref="disposeForm" />
    </template>

    <div class="view_detail_box" v-else>
      <div class="title-box">
        <span class="before"></span>
        <span>执行信息</span>
      </div>
      <!--工单来源 诊断-->
      <diagnosis v-if="order().taskType=='8' || order().taskType=='7' ||  order().orderSource==4 ||  order().orderSource==5" ref='disposeForm'></diagnosis>
      <!--工单来源 巡检-->
      <inspection v-else-if="order().orderSource=='10'" ref="disposeForm"></inspection>
      <!--工单来源 无人机巡检任务-->
      <!-- <tango-task v-else-if="order().taskType=='10'" ref="disposeForm" :catalog="catalog" @setUploadStatus="setUploadStatus" @setLoading="setLoading"></tango-task> -->
      <!--工单来源 其他-->
      <base-dispose v-else ref="disposeForm" />
    </div>
  </div>
</template>

<script>
import initDict from '@/mixins/initDict';
import diagnosis from './orderDispose/diagnosis';
import inspection from './orderDispose/inspection';
import baseDispose from './orderDispose/base';
import BaseDisposeDetail from './orderDispose/BaseDetail';
import DiagnosisDetail from './orderDispose/DiagnosisDetail';
export default {
  name: 'orderDispose',
  components: {
    diagnosis,
    inspection,
    baseDispose,
    DiagnosisDetail,
    BaseDisposeDetail
  },
  mixins: [initDict],
  props: {
    disabled: {
      type: Boolean,
      default: false
    },
    isDiagnosis: {
      default: false,
      type: [Boolean, String]
    },
    isFarm: {
      default: '',
      type: [String, Number]
    },
    catalog: {
      type: String,
      default: ''
    }
  },
  inject: ['order', 'orderSource', 'type'],
  watch: {},
  data () {
    return {};
  },
  created () {},
  computed: {},
  methods: {
    // 保存校验
    saveOrderDispose (type) { // 0 保存 1提交
      return new Promise((resolve, reject) => {
        this.$refs.disposeForm.setValidate(type).then(res => {
          resolve(res);
        }).catch(err => {
          reject(err);
        });
      });
    },
    getStatus () {
      let order = this.order();
      return (order.step > 5 && order.processDefKey == 'activiti4273Workflow') || (order.step > 5 && order.processDefKey == 'activiti4773Workflow') || (order.step > 2 && order.processDefKey ==
          'activiti4873Workflow') || (order.processDefKey == 'activiti5073Workflow' && order.step > 1) || order.flowSts == '4' || order.otherSts == '4';
    },
    // 取消检验信息
    clearValidate () {
      this.$refs.disposeForm.clearValidate();
    },
    updataDispose (order) {
      this.$refs.disposeForm.updataDispose(order);
    },
    initData (order) {
      this.$refs.disposeForm.initData(order);
    },
    // 无人机巡检任务-获取巡检图数据
    getImageData (type) {
      return this.$refs.disposeForm.getImageData(type);
    },
    // 无人机巡检任务-取消巡检图文件上传
    abortUpload () {
      this.$refs.disposeForm.abortUpload();
    },
    // 无人机巡检任务-获取巡检图文件上传状态
    setUploadStatus (uploadStatus) {
      this.$emit('setUploadStatus', uploadStatus);
    },
    // 设置加载
    setLoading (loading) {
      this.$emit('setLoading', loading);
    }
  }
};
</script>

<style lang="less" scoped>
  .view_detail_box {
    padding: 8px 0 0;

    .title_box_top {
      font-size: 18px;
      font-weight: 550;
      line-height: 25px;
      padding-bottom: 20px;
      display: flex;
      flex-flow: row nowrap;
      justify-content: space-between;
    }
  }

  :deep(.ant-form-item) {
    width: 100%;
    display: inline-flex;
  }

  :deep(.ant-steps-icon) {
    display: none;
  }

  :deep(.ant-steps-item-icon) {
    border: 4px solid #FB8E46 !important;
    height: 18px;
    width: 18px;
    position: relative;
    top: 18px;
    left: 18px;

  }

  :deep(.ant-steps-item-title) {
    position: relative;
    top: 10px;
    left: 10px;
    color: #5B5B5B !important;
    font-weight: 500;
  }

  :deep(.ant-steps-item) {
    margin-top: -9px;
  }

  :deep(.ant-steps-item-process .ant-steps-item-icon) {
    background: none !important;
  }

  :deep(.ant-steps-item-wait > .ant-steps-item-container > .ant-steps-item-tail::after) {
    background: none !important;
  }

  :deep(.ant-steps-item-process > .ant-steps-item-container > .ant-steps-item-tail::after) {
    background: none !important;
  }

  :deep(.ant-steps-item-tail::after) {
    border-left: 1px dashed #D6D6D6;
    position: relative;
    left: 10px;
  }

  :deep(.ant-steps-item-finish > .ant-steps-item-container > .ant-steps-item-tail::after) {
    background: none !important;
    border-left-color: #FF8F33;
  }

  :deep(.ant-steps-item-finish .ant-steps-item-icon),
  :deep(.ant-steps-item-wait .ant-steps-item-icon) {
    background-color: transparent;
  }

  .title-box {
    padding-bottom: 8px;
  }

  :root[data-theme='dark'] {
    .line_height .left {
      color: #fff !important;
    }
  }
</style>
