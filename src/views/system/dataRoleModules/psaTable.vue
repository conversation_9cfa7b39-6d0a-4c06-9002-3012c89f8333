<template>
  <a-card :bordered="false">
    <template v-if="selectedKey">
      <a-row :gutter="24" class="solar-eye-search-content">
        <a-col :xxl="8" :xl="12">
          <div class="search-item">
            <span class="search-label">电站名称</span>
            <a-input v-model="params.psaName" @blur="params.psaName = $trim($event)" size="default" allowClear></a-input>
          </div>
        </a-col>
        <a-col :xxl="4" :xl="8" :md="12">
          <div class="search-item">
            <throttle-button :disabled="loading" :loading="loading" label="查询" @click="searchClick"/>
          </div>
        </a-col>
      </a-row>
      <!-- 非自定义数据角色不可添加删除 -->
      <div class="operation-btn" v-show="record.type != '0'">
        <throttle-button :disabled="addDisabled" label="添加电站" @click="departAdd"></throttle-button>
        <throttle-button label="移除" :disabled="!checkedData.length" @click="deletePsa" style="margin-left: 12px;"></throttle-button>
      </div>
      <a-spin :spinning="loading">
        <!--表格渲染-->
        <vxe-table :data="data" :height="tableHeight" ref="multipleTable" align="center" border show-header-overflow show-overflow highlight-hover-row size="small"
         resizable @checkbox-all="handleSelectionChange" @checkbox-change="handleSelectionChange" :seq-config="{startIndex: (params.pageNo - 1) * params.pageSize }">
          <vxe-table-column type="checkbox" width="50" fixed="left"></vxe-table-column>
          <vxe-table-column type="seq" width="80" title="序号"></vxe-table-column>
          <vxe-table-column show-overflow="title" v-for="item in columnList" :key="item.name" min-width="150" :title="item.comment">
            <template v-slot="{ row }">
              <template v-if="item.name == 'psModel'">
                <span>{{getLabel(row.psModelOne, dictMap.psa_model)+"，"+ getLabel(row.psModelTwo, dictMap.psa_model) + '，' + getLabel(row.psModelThree, dictMap.psa_model)}}</span>
              </template>
              <template v-else-if ="item.name == 'psaStatus'">
                <span>{{getLabel(row.psaStatus, dictMap.psa_status)}}</span>
              </template>
              <template v-else>
                <span>{{getLabel(row[item.name], null)}}</span>
              </template>
            </template>
          </vxe-table-column>
          <template v-slot:empty>
            <span>查询无数据</span>
          </template>
        </vxe-table>
        <!--分页组件-->
        <page-pagination :pageSize="params.pageSize" :current="params.pageNo" :total="total" @size-change="sizeChange"/>
      </a-spin>
    </template>
    <a-card v-else :bordered="false" style="height:200px">
      <a-empty>
        <span slot="description"> 请先选择一个部门! </span>
      </a-empty>
    </a-card>
    <!--表单组件-->
    <departForm v-model="departForm.open" :departForm="departForm" @closeDepartForm="closeDepartForm"></departForm>
  </a-card>
</template>

<script>
import departForm from '../modules/DepartPowerStationForm';
import { queryDepartRelationPowerStationArchives, delDepartRelPowerStationArchives } from '@/api/isolarErp/depart/departRelationPsa';

export default {
  props: {
    selectedKey: {
      type: Object,
      default: null
    },
    dictMap: {
      type: Object,
      default: () => {
        return {};
      }
    }
  },
  components: {
    departForm
  },
  data () {
    return {
      tableHeight: 460,
      columnList: [{ name: 'departName', comment: '所属部门' }, { name: 'psaName', comment: '电站名称' },
        { name: 'gridConnectedScale', comment: '并网容量（MW）' }, { name: 'psModel', comment: '电站类型' },
        { name: 'psaStatus', comment: '电站状态' },
        { name: 'placeProvinceCity', comment: '所属省市' }, { name: 'placeAddress', comment: '详细地址' }],
      data: [],
      // 选中的数据
      checkedData: [],
      loading: false,
      delDisabled: true,
      addDisabled: false,
      params: {
        referer: 1,
        psaName: undefined,
        code: undefined,
        pageSize: 10,
        pageNo: 1
      },
      total: 0,
      departForm: {
        referer: 1,
        departId: '',
        open: false,
        title: '电站选择'
      },
      record: {}
    };
  },

  methods: {
    /*
        初始化
      */
    init (record) {
      this.record = Object.assign({}, record);
      this.params.code = record.code;
      this.params.type = record.type;
      this.departForm.departId = record.id;
      this.query();
    },
    /*
        查询点击事件
      */
    searchClick () {
      this.params.pageNo = 1;
      this.query();
    },
    // 查询已经关联的电站
    async query () {
      let self = this;
      self.loading = true;
      let $table = this.$refs.multipleTable;
      $table && await $table.clearScroll();
      self.checkedData = [];
      self.delDisabled = true;
      queryDepartRelationPowerStationArchives(this.params).then(res => {
        self.data = res.result.records;
        self.total = res.result.total;
        self.loading = false;
      }).catch(() => {
        self.data = [];
        self.total = 0;
        self.loading = false;
      });
    },

    // 添加电站
    departAdd () {
      this.departForm.open = true;
    },
    // 关闭弹窗
    closeDepartForm () {
      this.departForm.open = false;
      this.query();
    },
    // 行选中事件
    handleSelectionChange ({ records }) {
      this.checkedData = records;
    },
    // 移除
    deletePsa () {
      const self = this;
      self.loading = true;
      self.$confirm({
        title: '确定要删除吗?',
        okText: '确定',
        cancelText: '取消',
        onOk () {
          let map = {
            'depId': self.departForm.departId,
            'psaIds': self.checkedData.map(psa => { return psa.psaId; })
          };
          delDepartRelPowerStationArchives(map).then(res => {
            self.$message.success(res.message);
            self.searchClick();
            self.loading = false;
          }).catch(() => {
            self.$message.error(err.message);
            self.loading = false;
          });
        },
        onCancel () {
          self.loading = false;
          self.searchClick();
        }
      });
    },
    // 分页事件
    sizeChange (p, e) {
      Object.assign(this.params, { pageSize: e, pageNo: p });
      this.query();
    }
  }
};
</script>

<style lang="less" scoped>
  .operation-btn{
    text-align: right;
    margin-bottom: 12px;
  }
  .search-label{
    width: 80px;
  }
</style>
