import { getHealthDictApi } from '@/api/health/healthapi';

export default {
  data () {
    return {
      dictMap: {}
    };
  },
  methods: {
    getHealthDict (names = '') {
      return new Promise((resolve, reject) => {
        getHealthDictApi({ firstTypeCodeList: names.trim().split(',') })
          .then((res) => {
            let resultData = res.result_data;
            for (let key in resultData) {
              resultData[key].forEach((item) => {
                item.dispName = item.secondName;
                item.codeValue = item.secondTypeCode;
              });
            }
            this.dictMap = resultData;
            resolve(res);
          })
          .catch((err) => {
            reject(err);
          });
      }).catch((e) => {});
    },
    getHealthDictName (codeValue = '', dict) {
      const list = this.dictMap[dict] || [];
      const findItem = list.find((item) => item.codeValue === codeValue) || {};
      return findItem.dispName || '--';
    }
  }
};
