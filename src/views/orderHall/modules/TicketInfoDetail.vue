<template>
  <detail-layout :labelList="labelList" :form="baseForm" title="两票信息">
    <template v-slot:ticketDtoList>
      <a-col :span="24" class="detail_layout_content">
        <span class="left">关联两票</span>
        <span class="right">{{ ticketDtoListLabel  }}</span>
      </a-col>
    </template>
  </detail-layout>
</template>

<script>

export default {
  name: 'TicketInfoDetail',
  props: {
    baseForm: {
      type: Object,
      default: () => ({})
    }
  },
  data () {
    return {
      labelList: [
        { slot: 'ticketDtoList' }
      ]
    };
  },
  computed: {
    ticketDtoListLabel () {
      const { ticketDtoList } = this.baseForm;
      if (ticketDtoList && ticketDtoList.length > 0) {
        return ticketDtoList.map(item => item.ticketNo).join('、');
      }
      return '--';
    }
  }
};
</script>

<style lang='less' scoped>
</style>
