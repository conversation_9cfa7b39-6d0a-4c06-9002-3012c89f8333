<template>
  <div class="iframe-view">
    <div v-if="loading">数据加载中 </div>
    <iframe :id="id" :src="url" ref="testPage" frameborder="0" width="100%" height="800px" scrolling="auto"></iframe>
  </div>
</template>

<script>
import { postAction } from '@/api/manage';
import { mixin, mixinDevice } from '@/utils/mixin.js';
export default {
  name: 'iframeView',
  props: {
    path: {
      type: String,
      required: true,
      default: null
    }
  },
  // inject: ['closeCurrent'],
  data () {
    return {
      url: '',
      id: '',
      loading: true,
      iframeWin: {},
      user: {},
      perms: []
    };
  },
  mixins: [mixin, mixinDevice],
  created () {
    this.goUrl();
  },
  updated () {
    this.goUrl();
  },
  methods: {
    goUrl () {
      postAction('https://gateway.isolarcloud.com/v1/userService/login', {
        user_account: 'gaochao',
        user_password: 'pw1111',
        login_type: 1,
        appkey: '7D233083E6C376543C36D329100BB473',
        sys_code: '901'
      }).then(res => {
        let token = res.result_data.token;
        let params = '&lang=zh_CN&microGateWayUrl=https://gateway.isolarcloud.com&noMenu=1&apiUrl=https://api.isolarcloud.com&baseUrl=https://www.isolarcloud.com&cloudId=1';
        this.url = this.path + '?token=' + token + params;
        this.id = new Date().getTime();
      });
    }
  }
};
</script>
<style lang="less" scoped="scoped">
.iframe-view{
  width: 100%;
  height: 100%;
}
</style>
