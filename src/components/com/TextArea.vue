<template>
  <div class="zw-textarea" :class="{ 'zw-textarea-red': number > maxLength }">
    <a-textarea
     v-model="remark"
      :auto-size="autoSize"
      :disabled="disabled"
      :maxLength="!allow && maxLength"
      @input="inputEvent"
      @blur="blurEvent"
      @change="inputEvent"
      style="width: 100%"
      :placeholder="placeholder"
      :size="size"
      :allow-clear="allowClear"
    >
    </a-textarea>
    <div class="number" v-show="!disabled" :style="{bottom: allowClear || allowClear==''?'7px':'-2px'}">{{ number }}/{{maxLength}}</div>
  </div>
</template>
<script>
export default {
  name: 'ZwTextarea',
  model: {
    prop: 'value',
    event: 'blur'
  },
  props: {
    disabled: {
      default: false,
      type: Boolean
    },
    value: {
      type: String,
      required: false
    },
    size: {
      type: String,
      default: 'default'
    },
    autoSize: {
      default: () => {
        return { minRows: 2, maxRows: 2 };
      }
    },
    maxLength: {
      type: String || Number,
      default: 200
    },
    allow: { // 超过maxlength 是否允许继续输入，默认true，允许输入，false 不允许输入
      type: Boolean,
      default: true
    },
    placeholder: String,
    allowClear: {
      default: undefined
    }
  },
  watch: {
    value: {
      handler (val) {
        this.remark = val;
        this.number = this.value ? this.value.length : 0;
      },
      immediate: true
    }
  },
  created () {
    this.remark = this.value;
  },
  data () {
    return {
      number: 0,
      remark: null
    };
  },
  methods: {
    blurEvent (e) {
      this.$trim(e);
      this.$emit('blur', this.remark);
    },
    inputEvent (e) {
      // if (e.target.value.length >= this.maxLength && !this.allow) {
      //   this.number = this.maxLength;
      //   this.remark = e.target.value.slice(0, this.maxLength);
      //   this.$emit('input', this.remark);
      //   return;
      // }
      this.number = e.target.value.length;
      this.$emit('input', e.target.value);
    }
  }
};
</script>
<style lang="less">
.zw-textarea {
  position: relative;
  .number {
    font-size: 12px;
    font-family: MicrosoftYaHei;
    color: var(--zw-text-3-color--default);
    position: absolute;
    right: 8px;
    bottom: -2px;
  }
}
.zw-textarea-red {
  .ant-input {
    &:hover,&:focus {
      border-color: var(--zw-warning-color--normal);
      box-shadow: 0px 0px 0px 2px #fddddd;
    }
  }
}
.zw-textarea-red {
  .number {
    color: var(--zw-warning-color--normal);
  }
}
</style>
