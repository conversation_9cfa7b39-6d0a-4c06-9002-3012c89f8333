<template>
  <div class="recieve-content">
    <div class="table-content">
      <div class="flex-between div-title">
        <div>电站信息</div>
        <a-input-search v-model="stationSearchData.psName" class="input" placeholder="请输入关键字搜索" allowClear @search="getPsList(1)"/>
      </div>
      <vxe-table
        :data="stationData"
        ref="stationTable"
        resizable
        show-overflow
        highlight-hover-row
        size="small"
        :height="440"
        :seq-config="{ startIndex: (stationSearchData.currentPage - 1) * stationSearchData.pageSize }"
        class="my-table"
        @checkbox-all="handleSelectionChange"
        @checkbox-change="handleSelectionChange"
        @cell-click="cellClick"
        :row-class-name="rowClassName"
      >
        <vxe-table-column type="checkbox" :width="60"></vxe-table-column>
        <vxe-table-column type="seq" :width="80" title="序号"></vxe-table-column>
        <vxe-table-column v-for="item in stationColumns" :key="item.field" show-overflow="title" :formatter="tabFormatter" :title="item.title" :min-width="item.width" :field="item.field">
        </vxe-table-column>
        <template v-slot:empty>
          <span>查询无数据</span>
        </template>
      </vxe-table>
      <page-pagination
        :pageSize="stationSearchData.pageSize"
        :current="stationSearchData.currentPage"
        :total="stationTotal"
        @size-change="getPsList"
      />
    </div>
    <div class="icon flex-center">
      <svg-icon class="polygon-icon" iconClass="polygon"/>
    </div>
    <div class="table-content">
      <div class="flex-between div-title">
        <div>设备信息</div>
        <div class="buttons">
          <a-dropdown >
            <a-menu slot="overlay" @click="handleImportMenuClick">
              <a-menu-item key="1">导出电站列表</a-menu-item>
              <a-menu-item key="2">导出电站详情</a-menu-item>
              <a-menu-item key="3">导出设备信息</a-menu-item>
            </a-menu>
            <a-button v-if="type == '1'" class='solar-eye-btn-primary-cancel'>导出<a-icon type="down" /></a-button>
          </a-dropdown>
        </div>
      </div>
      <vxe-table
        :data="deveiceData"
        ref="deviceTable"
        resizable
        show-overflow
        highlight-hover-row
        size="small"
        :height="440"
        :seq-config="{ startIndex: (deviceSearchData.currentPage - 1) * deviceSearchData.pageSize }"
        class="my-table"
      >
        <vxe-table-column type="seq" :width="80" title="序号"></vxe-table-column>
        <vxe-table-column v-for="(item,index) in deviceColumns" show-overflow="title" :formatter="tabFormatter"
          :title="item.title" :field="item.field" :min-width="item.width" :key="index">
        </vxe-table-column>
        <template v-slot:empty>
          <span>查询无数据</span>
        </template>
      </vxe-table>
      <page-pagination :pageSize="deviceSearchData.pageSize" :current="deviceSearchData.currentPage" :total="deviceTotal"
        @size-change="getDeviceList"/>
    </div>
  </div>
</template>
<script>
import {
  getPsList,
  getDeviceList,
  exportPsList,
  exportPsDetailList,
  exportDeviceList
} from '@/api/model/abutmentConfig.js';
export default {
  props: {
    sourceId: {
      type: String,
      default: ''
    },
    type: {
      type: String,
      default: '1'
    }
  },
  data () {
    return {
      stationData: [],
      deveiceData: [],
      stationTotal: 0,
      deviceTotal: 0,
      stationColumns: [
        { title: '电站ID', field: 'originalPsId' },
        { title: '电站名称', field: 'originalPsName', width: 200 },
        { title: 'PSID ', field: 'psId' }
      ],
      deviceColumns: [
        { title: '设备ID', field: 'originalDeviceCode' },
        { title: '设备名称', field: 'originalDeviceName', width: 200 },
        { title: 'PSK', field: 'psKey' }
      ],
      stationSearchData: {
        currentPage: 1,
        pageSize: 10,
        psName: undefined
      },
      deviceSearchData: {
        currentPage: 1,
        pageSize: 10
      },
      row: {},
      selectPsIds: [],
      psName: undefined
    };
  },
  methods: {
    getPsList (currentPage, pageSize) {
      if (currentPage) {
        this.stationSearchData.currentPage = currentPage;
      }
      if (pageSize) {
        this.stationSearchData.pageSize = pageSize;
      }
      let params = {
        sourceId: this.sourceId,
        currentPage: currentPage || this.stationSearchData.currentPage,
        pageSize: pageSize || this.stationSearchData.pageSize,
        psName: this.stationSearchData.psName
      };
      this.psName = this.stationSearchData.psName;
      this.$emit('setLoading', true);
      this.deviceTotal = 0;
      this.deveiceData = [];
      this.selectPsIds = [];
      this.row = {};
      getPsList(params).then(res => {
        if (res.result_data.pageList && res.result_data.pageList.length > 0) {
          this.stationData = res.result_data.pageList;
          this.stationTotal = res.result_data.rowCount;
        } else {
          this.stationData = [];
          this.stationTotal = 0;
        }
        this.$emit('setLoading', false);
      }).catch(() => {
        this.$emit('setLoading', false);
        this.stationData = [];
        this.stationTotal = 0;
      });
    },
    getDeviceList (currentPage, pageSize) {
      if (currentPage) {
        this.deviceSearchData.currentPage = currentPage;
      }
      if (pageSize) {
        this.deviceSearchData.pageSize = pageSize;
      }
      let params = {
        sourceId: this.sourceId,
        currentPage: currentPage || this.deviceSearchData.currentPage,
        pageSize: pageSize || this.deviceSearchData.pageSize,
        originalPsIdList: [this.row.originalPsId]
      };
      this.$emit('setLoading', true);
      getDeviceList(params).then(res => {
        if (res.result_data.pageList && res.result_data.pageList.length > 0) {
          this.deveiceData = res.result_data.pageList;
          this.deviceTotal = res.result_data.rowCount;
        } else {
          this.deveiceData = [];
          this.deviceTotal = 0;
        }
        this.$emit('setLoading', false);
      }).catch(() => {
        this.$emit('setLoading', false);
        this.deveiceData = [];
        this.deviceTotal = 0;
      });
    },
    handleSelectionChange ({ records }) {
      this.selectPsIds = records.map(item => {
        return item.originalPsId;
      });
    },
    handleImportMenuClick (e) {
      const { key } = e;
      let exportMethod = key == '1' ? exportPsList : (key == '2' ? exportPsDetailList : exportDeviceList);
      this.$emit('setLoading', true);
      exportMethod({ sourceId: this.sourceId, originalPsIdList: this.selectPsIds, psName: this.psName }).then(res => {
        this.$emit('setLoading', false);
        let obj = {
          fileName: res.result_data.fileName,
          fileBase64Code: res.result_data.strBase64
        };
        this.$downloadFile(obj);
      }).catch(() => { this.$emit('setLoading', false); });
    },
    cellClick ({ row }) {
      if (!row.isCurrent) {
        this.row = row;
        this.getDeviceList(1);
        this.stationData.forEach(item => {
          item.isCurrent = false;
        });
        row.isCurrent = true;
      }
    },
    rowClassName ({ row }) {
      if (row.isCurrent) {
        return 'row-tree-children';
      }
    }
  }
};
</script>
<style lang="less" scoped>
.recieve-content {
  height: 100%;
  display: flex;
  .table-content {
    width: calc((100% - 44px)/2);
  }
  .icon {
    width: 44px;
    height: 500px;
    padding-top: 60px;
    .polygon-icon {
      font-size: 20px;
    }
  }
  .div-title {
    height: 32px;
    margin: 12px 0 16px;
    .buttons {
      button + button {
        margin-left: 12px;
      }
    }
    .input {
      width: 320px;
    }
  }
}
</style>
