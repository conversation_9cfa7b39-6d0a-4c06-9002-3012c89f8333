<template>
<a-spin size="large" :spinning="wholePageLoading" class="spin-content">
  <div class="device">
    <section>
      <a-radio-group class="radio-div" v-model="alarmGrade" @change="alarmGradeChange" size="default">
        <a-radio-button value="">全部({{ activeTotal }})</a-radio-button>
        <a-radio-button v-for="item in alarmNumList" :key="item.grade" :value="item.grade"
          >{{ item.gradeName }}({{ item.count }})</a-radio-button
        >
      </a-radio-group>
    </section>
    <section  class="width_100">
      <div class="content" :style="{height: isShowMenu ? 'calc(100vh - 435px)' : 'calc(100vh - 311px)'}">
        <template v-for="item in list">
          <div class="circuit" :key="item.ps_key">
            <div class="b-box">
              <header>
                <div class="device_name" :title="item.device_name">{{ item.device_name }}</div>
                <img :src="require(`@/assets/images/monitor/device/alarm_type_${item.grade}.png`)" class="alarm-type-img" />
                <div class="alarm-type" :class="['alarm_type_' + item.grade]">{{ item.gradeName }}</div>
                <div class="next_device" v-show="isShowLink(item)" @click="changeDevice({ value: 'Inverter', name: '逆变器', type: 1 },item)">
                </div>
              </header>
              <section>
                <div class="left">
                  <img src="@/assets/images/monitor/device/box_s.png" alt="" srcset="" />
                </div>
                <div class="right-box">
                  <div class="reason_title" v-if="item.allReason">
                    <img class="grade-image" v-show="item.allReason" :src="getGradePng(item.alarmGrade)" />
                    <div class="alarm_name flex-center" :title="item.allReason">
                      {{ item.allReason }}
                      <div :class="{'alarm_name_bg': item.allReason}"></div>
                    </div>
                  </div>
                  <div class="right-content">
                    <div>
                      <div class="head">测点名称</div>
                      <div class="text">P(kW)：</div>
                      <div class="text">Q(kVar)：</div>
                      <div class="text">la(A)：</div>
                      <div class="text">lb(A)：</div>
                      <div class="text">lc(A)：</div>
                    </div>
                    <div class="value-div">
                      <div class="values">
                        <div class="head">绕组1</div>
                        <div class="value" :title="isExistValue(item.p6009)">{{isExistValue(item.p6009)}}</div>
                        <div class="value" :title="isExistValue(item.p6010)">{{isExistValue(item.p6010)}}</div>
                        <div class="value" :title="isExistValue(item.p6006)">{{isExistValue(item.p6006)}}</div>
                        <div class="value" :title="isExistValue(item.p6007)">{{isExistValue(item.p6007)}}</div>
                        <div class="value" :title="isExistValue(item.p6008)">{{isExistValue(item.p6008)}}</div>
                      </div>
                      <div class="values" v-if="isExist(item)">
                        <div class="head">绕组2</div>
                        <div class="value" :title="isExistValue(item.p6039)">{{isExistValue(item.p6039)}}</div>
                        <div class="value" :title="isExistValue(item.p6040)">{{isExistValue(item.p6040)}}</div>
                        <div class="value" :title="isExistValue(item.p6036)">{{isExistValue(item.p6036)}}</div>
                        <div class="value" :title="isExistValue(item.p6037)">{{isExistValue(item.p6037)}}</div>
                        <div class="value" :title="isExistValue(item.p6038)">{{isExistValue(item.p6038)}}</div>
                      </div>
                    </div>
                  </div>
                  <div class="device_bottom">
                    <span class="anomaly-title">下级状态：</span>
                    <div class="anomaly"></div>
                      异常
                    <span class="anomaly_number">{{ item.downAlarmDeviceCount }}</span>
                    <div class="unconnected"></div>
                      正常
                    <span class="unconnected_number">{{ item.downDisConnectDeviceCount }}</span>
                  </div>
                </div>
              </section>
            </div>
          </div>
        </template>
        <infinite-loading :identifier="infiniteId" v-if="list.length >= pageSize" @infinite="getList">
          <div slot="spinner">
            <a-spin size="large" v-if="!wholePageLoading" class="card-spin"></a-spin>
          </div>
          <div slot="no-more">
            <p class="no-more-data">无更多数据</p>
          </div>
        </infinite-loading>
         <div class="infinite-loading-container" v-if="list.length == 0 && !wholePageLoading">
            <img class="no-data-img" :src="noDataImg" />
            <p class="no-data-text">暂无数据</p>
          </div>
      </div>
    </section>
  </div>
  </a-spin>
</template>
<script>
import deviceType from '../mixins/deviceType';
export default {
  mixins: [deviceType],
  data () {
    return {
      url: {
        count: '/monitorNew/getUnitAlarmCount',
        alarmList: '/monitorNew/getUnitAlarmList'
      }
    };
  },
  computed: {
    isShowMenu () {
      return this.$store.state.user.isShowMenu;
    }
  },
  methods: {
    changeDevice (obj, item) {
      this.$emit('select', obj, item);
    },
    isExist (item) {
      let total = 0;
      for (let i in item) {
        if (['p6039', 'p6040', 'p6036', 'p6037', 'p6038'].includes(i)) {
          total += item[i] ? Number(item[i]) : 0;
        }
      }
      return total != 0;
    },
    isShowLink (item) {
      return Number(item.downAlarmDeviceCount) + Number(item.downDisConnectDeviceCount);
    }
  }
};
</script>
<style lang="less" scoped>
@import url('../less/base.less');
.device {
  align-items: flex-start;
  .b-box {
    width: 380px;
    height: 306px;
    margin: 6px 0 0 6px;
    header {
      .device_name {
        width: 184px;
      }
    }
    section {
      .left {
        width: 150px;
        padding-left: 6px;
      }
    }
  }
  .content {
    flex-wrap: wrap;
    height: calc(100vh - 435px);
    overflow: auto;
    align-content: flex-start;
    .circuit {
      margin-bottom: 16px;
      position: relative;
      z-index: 1;
    }
  }
}
</style>
