// vxeTable组件样式
.vxe-table {
  color: var(--zw-text-1-color--default);

  .vxe-table--body-wrapper,
  .vxe-table--footer-wrapper {
    background: transparent !important;
    border-top-color: var(--zw-border-color--default);
  }

  .vxe-table--border-line,
  .vxe-table--header-border-line {
    border-color: transparent !important;
  }

  .vxe-table--fixed-left-wrapper,
  .vxe-table--fixed-right-wrapper {
    background: var(--zw-card-bg-color--default) !important;
    border-right-color: var(--zw-border-color--default) !important;
  }

  .vxe-table--fixed-left-wrapper {
    box-shadow: 4px 3px 4px 0 rgba(0, 0, 0, 0.12) !important
  }

  .vxe-table--fixed-right-wrapper {
    box-shadow: -4px 3px 4px 0 rgba(0, 0, 0, 0.12) !important;
  }

  .vxe-body--row.row--checked,
  .vxe-body--row.row--radio {
    background-color: transparent;
  }

  .vxe-table--footer-wrapper {
    border-top-color: var(--zw-border-color--default);
  }
  // .vxe-body--row:nth-child(2n),
  .vxe-body--row:hover,.vxe-body--row.row--hover,.vxe-body--row.row--hover.row--stripe {
    background: var(--zw-table-bg-color--hover);
  }
  .vxe-table--header-wrapper {
    background: var(--zw-table-header-bg-color--default);
  }
  &.border--full {
    .vxe-header--column  {
      background: var(--zw-table-header-bg-color--default);
    }
    .vxe-body--column {
      border-bottom: 1px solid var(--zw-border-color--default);
    }
  }
}

.vxe-table.border--default .vxe-body--column,
.vxe-table.border--default .vxe-footer--column,
.vxe-table.border--default .vxe-header--column,
.vxe-table.border--inner .vxe-body--column,
.vxe-table.border--inner .vxe-footer--column,
.vxe-table.border--inner .vxe-header--column {
  background: transparent !important;
}

.vxe-table.border--full .vxe-header--column {
  background-size: 1px 26px !important;
  background-position: right !important;
}

.vxe-table.border--default .vxe-table--header-wrapper,
.vxe-table.border--full .vxe-table--header-wrapper,
.vxe-table.border--outer .vxe-table--header-wrapper {
  background-color: none !important;
}

.vxe-table.border--default .vxe-body--column,
.vxe-table.border--default .vxe-footer--column,
.vxe-table.border--inner .vxe-body--column,
.vxe-table.border--inner .vxe-footer--column,
.vxe-table.border--full .vxe-body--column,
.vxe-table.border--full .vxe-footer--column,
.vxe-table.border--default .vxe-table--header-wrapper .vxe-header--row:last-child .vxe-header--gutter,
.vxe-table.border--full .vxe-table--header-wrapper .vxe-header--row:last-child .vxe-header--gutter,
.vxe-table.border--inner .vxe-table--header-wrapper .vxe-header--row:last-child .vxe-header--gutter,
.vxe-table.border--outer .vxe-table--header-wrapper .vxe-header--row:last-child .vxe-header--gutter {
  background-image: none !important;
}

.is--checked.vxe-custom--option .vxe-checkbox--icon:before,
.is--checked.vxe-export--panel-column-option .vxe-checkbox--icon:before,
.is--checked.vxe-table--filter-option .vxe-checkbox--icon:before,
.is--indeterminate.vxe-custom--option .vxe-checkbox--icon:before,
.is--indeterminate.vxe-export--panel-column-option .vxe-checkbox--icon:before,
.is--indeterminate.vxe-table--filter-option .vxe-checkbox--icon:before,
.vxe-table .is--checked.vxe-cell--checkbox .vxe-checkbox--icon:before,
.vxe-table .is--indeterminate.vxe-cell--checkbox .vxe-checkbox--icon:before,
.vxe-table .is--checked.vxe-cell--checkbox:hover .vxe-checkbox--icon:before,
.vxe-table .is--indeterminate.vxe-cell--checkbox:hover .vxe-checkbox--icon:before,
.vxe-table .is--checked.vxe-cell--radio .vxe-radio--icon:before,
.vxe-table .is--indeterminate.vxe-cell--radio .vxe-radio--icon:before,
.vxe-table .is--checked.vxe-cell--radio:hover .vxe-radio--icon:before,
.vxe-table .is--indeterminate.vxe-cell--radio:hover .vxe-radio--icon:before {
  border-color: var(--zw-primary-color--default);
  background-color: var(--zw-primary-color--default);
}

.vxe-custom--option:hover .vxe-checkbox--icon:before,
.vxe-export--panel-column-option:hover .vxe-checkbox--icon:before,
.vxe-table--filter-option:hover .vxe-checkbox--icon:before,
.vxe-table .vxe-cell--checkbox:hover .vxe-checkbox--icon:before,
.vxe-table .vxe-cell--radio:hover .vxe-radio--unchecked-icon:before {
  border-color: var(--zw-primary-color--default) !important;
}
.vxe-table.border--default .vxe-table--header-wrapper, .vxe-table.border--full .vxe-table--header-wrapper, .vxe-table.border--outer .vxe-table--header-wrapper {
  background-color: transparent;
}
.vxe-table .vxe-body--row.row--current {
  background-color: var(--zw-table-bg-color--hover) !important;
}

// vxeTable表格
.my-table {
  .sort-head {
    .sort-title {
      margin-left: unset;
    }
  }

  .vxe-table--header-wrapper {
    background: var(--zw-table-header-bg-color--default) !important;
  }

  .vxe-header--column {
    font-weight: normal;
  }

  .vxe-header--column .vxe-resizable.is--line:before {
    background-color: transparent;
  }

  .vxe-body--row:hover,
  .vxe-body--row:nth-child(even) {
    background: transparent;
  }

  .vxe-body--row.row--hover {
    background: var(--zw-table-bg-color--hover) !important;
  }

  .vxe-body--column {
    border-bottom: 1px solid var(--zw-border-color--default) !important;
  }

  .vxe-body--row.row--checked {
    background: var(--zw-table-bg-color--hover) !important;
  }

  .vxe-body--row.row--checked,
  .vxe-body--row.row--radio {
    background-color: var(--zw-table-bg-color--hover);
  }
}

// 弹窗内-表格tip不显示问题
.vxe-table--tooltip-wrapper {
  z-index: 1001 !important;
}

.vxe-cell .ant-btn {
  border: 0;
  background-color: unset !important;
  min-width: 28px;
  height: 28px;
  box-shadow: 0 0 var(--black);
}

.vxe-cell {
  .ant-btn,
  .ant-btn.ant-btn-primary:hover,
  .ant-btn:focus {
    color: var(--zw-conduct-color--normal) !important;
  }
}

// vxe 筛选下拉框 start
.vxe-table--filter-wrapper {
  background: var(--zw-card-bg-color--default);
  border: 1px solid var(--zw-border-color--default);
}

.vxe-table--filter-wrapper .vxe-table--filter-body > li:hover,
.vxe-table--filter-wrapper .vxe-table--filter-header > li:hover {
  background: var(--zw-table-bg-color--hover);
}

.vxe-table--filter-wrapper .vxe-table--filter-footer {
  border-top-color: var(--zw-border-color--default);
}
// end

// vxe-table checkbox边框 start
.vxe-custom--option .vxe-checkbox--icon:before,
.vxe-export--panel-column-option .vxe-checkbox--icon:before,
.vxe-table--filter-option .vxe-checkbox--icon:before,
.vxe-table .vxe-cell--checkbox .vxe-checkbox--icon:before {
  border-width: 1px;
  border-color: var(--zw-border-color--default);
}
// end

// 深色背景
[data-theme='dark'] {
  .vxe-custom--option .vxe-checkbox--icon:not(.vxe-checkbox--checked-icon),
  .vxe-export--panel-column-option .vxe-checkbox--icon:not(.vxe-checkbox--checked-icon):before,
  .vxe-table--filter-option .vxe-checkbox--icon:not(.vxe-checkbox--checked-icon):before,
  .vxe-table .vxe-cell--checkbox .vxe-checkbox--icon:not(.vxe-checkbox--checked-icon):before {
    background-color: transparent;
  }

  .vxe-table .vxe-cell--radio .vxe-radio--icon:before {
    border-width: 1px;
    background-color: transparent;
  }

  .vxe-table .is--checked.vxe-cell--radio .vxe-radio--checked-icon:before {
    background-color: var(--zw-primary-color--default);
  }
}
// 像电站等级及电站评价需要broder的页面
.border-table.vxe-table.border--full {
    .vxe-body--column, .vxe-footer--column, .vxe-header--column {
      background-image:linear-gradient(var(--zw-table-header-bg-color--default),var(--zw-table-header-bg-color--default)),linear-gradient(var(--zw-table-header-bg-color--default),var(--zw-table-header-bg-color--default)) !important
    }
    .vxe-body--column {
      border-bottom-color: transparent;
    }
}