<template>
  <div class="alarmEvents" id="safetyEvents">
    <a-spin :spinning="pageloading">
      <div class="solar-eye-search-model">
        <div class="solar-eye-search-content">
          <a-row :gutter="24" class="solar-search-bg" style="margin: 0">
            <a-col :xxl="4" :xl="6" :md="12">
              <div class="search-item">
                <span class="search-label">电站名称</span>
                <ps-tree-select ref="tree" @change="getStationChange" v-model="psId" :isPsName="psName"
                  :isPsaDisable="false" style="width: 100%;" :hasMaintenanceStatusParams="true"/>
              </div>
            </a-col>
            <a-col :xxl="4" :xl="6" :md="10">
              <div class="search-item">
                <span class="search-label">电量保证类型</span>
                <a-select :maxTagCount="1" mode="multiple" class="electric-input" v-model="elecEnsureType"
                  @change="paramsChange($event, 'elecEnsureType')">
                  <a-select-option value="0">全部</a-select-option>
                  <a-select-option v-for="data in dictMap.elec_ensure_type" :key="data.dataValue" :value="data.dataValue">
                    {{ data.dataLable }}
                  </a-select-option>
                </a-select>
              </div>
            </a-col>
            <a-col :xxl="4" :xl="6" :md="12">
              <div class="search-item">
                <span class="search-label">发生时间</span>
                <a-range-picker v-model="searchTime" @change="paramsChange($event, 'searchTime')"
                  format="YYYY-MM-DD"></a-range-picker>
              </div>
            </a-col>
            <a-col :xxl="3" :xl="6" :md="10">
              <div class="search-item">
                <span class="search-label">处理状态</span>
                <a-select v-model="alarmStatus" @change="paramsChange($event, 'alarmStatus')">
                  <a-select-option value="01">待处理</a-select-option>
                  <a-select-option value="03">已派发</a-select-option>
                  <a-select-option value="04">已闭环</a-select-option>
                </a-select>
              </div>
            </a-col>
            <a-col :xxl="3" :xl="5" :md="10">
              <div class="search-item">
                <span class="search-label">隐患类型</span>
                <a-cascader :options="alarmTypes" placeholder="请选择" change-on-select :allowClear="false"
                  v-model="alarmType" />
              </div>
            </a-col>
            <a-col :xxl="3" :xl="6" :md="10" v-show="alarmStatus == '04'">
              <div class="search-item">
                <span class="search-label">闭环方式</span>
                <a-select v-model="closedType" :getPopupContainer="(node) => node.parentNode"
                  @change="paramsChange($event, 'closedType')">
                  <a-select-option value="">全部</a-select-option>
                  <a-select-option v-for="item in dictMap.farm_scene_condition_query" :key="item.key"
                    :value="item.codeValue" :title="item.dispName">
                    {{ item.dispName }}
                  </a-select-option>
                </a-select>
              </div>
            </a-col>
            <!-- <a-col :xxl="5" :xl="5" :md="10" >
              <div class="search-item">
                <span class="search-label">设备名称</span>
                  <a-select mode="multiple" allowClear showSearch :maxTagCount="1" :value="deviceName"
                    style="height:32px" placeholder="请选择" :disabled="!isChooseful" @change='handleAsyncChange'>
                    <a-select-option v-for="item in devices" :key="item.psKey" :value='item.deviceName'>
                      {{ item.deviceName }}
                    </a-select-option>
                  </a-select>
              </div>
            </a-col> -->
            <a-col :xxl="3" :xl="3" :md="4">
              <div class="search-item">
                <throttle-button label="重置" class="solar-eye-btn-primary-cancel"
                  @click="resetSearchData"></throttle-button>
                <throttle-button label="查询" class="solar-eye-btn-primary" size="default"
                  @click="pageChange(1)"></throttle-button>
              </div>
            </a-col>
          </a-row>
        </div>
        <div class="split-line"></div>
      </div>
      <a-col class="solar-eye-main-content">
        <div class="operation-btn">
          <erp-button label="导出" perms="safetyEvents:export" @click="doExport" class="solar-eye-btn-primary-cancel"
            size="default"></erp-button>
        </div>
        <vxe-table :data="data" :height="isShowMenu ? tableHeight - 170 : tableHeight - 167" ref="multipleTable" align="center" resizable border
          show-overflow highlight-hover-row size="small" :seq-config="{ startIndex: (page - 1) * size }">
          <vxe-table-column type="seq" :width="80" align="center" title="序号"></vxe-table-column>
          <vxe-table-column show-overflow="title" field="alarmRemarkName" title="隐患类型" min-width="120">
          </vxe-table-column>
          <vxe-table-column show-overflow="title" title="隐患原因" min-width="120">
            <template v-slot="{ row }">
              <span> {{ row.alarmReason ? row.alarmReason : "--" }}</span>
            </template>
          </vxe-table-column>
          <vxe-table-column show-overflow="title" field="deviceName" title="设备名称" min-width="120">
            <template #header="{ column }">
              <table-sort :column="column" filed="deviceName" @sortChange="mySortChange" :downSort="downSort"
                :upSort="upSort" :sortFiled="sortFiled" />
            </template>
          </vxe-table-column>
          <vxe-table-column show-overflow="title" title="设备类型" min-width="100">
            <template v-slot="{ row }">
              <span> {{ row.deviceTypeName ? row.deviceTypeName : "--" }}</span>
            </template>
          </vxe-table-column>
          <vxe-table-column show-overflow="title" field="psName" title="电站名称" min-width="200">
            <template #header="{ column }">
              <table-sort :column="column" filed="psName" @sortChange="mySortChange" :downSort="downSort" :upSort="upSort"
                :sortFiled="sortFiled" />
            </template>
          </vxe-table-column>
          <vxe-table-column show-overflow="title" field="eventStartTime" title="发生时间" min-width="120">
            <template #header="{ column }">
              <table-sort :column="column" filed="eventStartTime" @sortChange="mySortChange" :downSort="downSort"
                :upSort="upSort" :sortFiled="sortFiled" />
            </template>
          </vxe-table-column>
          <vxe-table-column show-overflow="title" field="lastHappenTime" title=" 更新时间 " min-width="120">
            <template #header="{ column }">
              <table-sort :column="column" filed="lastHappenTime" @sortChange="mySortChange" :downSort="downSort"
                :upSort="upSort" :sortFiled="sortFiled" />
            </template>
            <template v-slot="{ row }">
              <span>{{ row.lastHappenTime ? row.lastHappenTime : '--' }}</span>
            </template>
          </vxe-table-column>
          <vxe-table-column :visible="dealStatus != '04'" show-overflow="title" field="powerLoss" title="电量损失值(kWh)"
            min-width="140">
            <template #header="{ column }">
              <table-sort :column="column" filed="powerLoss" @sortChange="mySortChange" :downSort="downSort"
                :upSort="upSort" :sortFiled="sortFiled" />
            </template>
          </vxe-table-column>
          <vxe-table-column :visible="dealStatus == '03'" show-overflow="title" field="orderStatus" title="工单状态"
            min-width="120">
            <template v-slot="{ row }">
              <span @click="workOrderDetail(row, 'safety')" style="cursor: pointer;" class="blue">{{
                row.orderStatus }}</span>
            </template>
          </vxe-table-column>
          <vxe-table-column :visible="dealStatus == '03'" show-overflow="title" field="declareUser" title="派发人"
            min-width="120">
            <template #header="{ column }">
              <table-sort :column="column" filed="declareUser" @sortChange="mySortChange" :downSort="downSort"
                :upSort="upSort" :sortFiled="sortFiled" />
            </template>
          </vxe-table-column>
          <vxe-table-column :visible="dealStatus != '01'" show-overflow="title" field="distributeTime" title="派发时间"
            min-width="120">
            <template #header="{ column }">
              <table-sort :column="column" filed="distributeTime" @sortChange="mySortChange" :downSort="downSort"
                :upSort="upSort" :sortFiled="sortFiled" />
            </template>
          </vxe-table-column>
          <vxe-table-column :visible="dealStatus == '04'" show-overflow="title" field="finishedTime" title="闭环时间"
            min-width="120">
            <template #header="{ column }">
              <table-sort :column="column" filed="finishedTime" @sortChange="mySortChange" :downSort="downSort"
                :upSort="upSort" :sortFiled="sortFiled" />
            </template>
          </vxe-table-column>
          <vxe-table-column :visible="dealStatus == '04'" show-overflow="title" field="closedType" title="闭环方式"
            min-width="120">
            <template #header="{ column }">
              <table-sort :column="column" filed="closedType" @sortChange="mySortChange" :downSort="downSort"
                :upSort="upSort" :sortFiled="sortFiled" />
            </template>
            <template v-slot="{ row }">
              <span :title="row.closedTypeShow">{{ row.closedTypeShow }}</span>
            </template>
          </vxe-table-column>
          <vxe-table-column fixed="right" title="操作" min-width="180">
            <template v-slot="{ row }">
              <span title="查看" v-if="show('safetyEvents:analysis')">
                <svg-icon class="health-icon" @click="goDetail(row)" iconClass="health-check"></svg-icon>
              </span>
              <span title="派发" v-if="show('safetyEvents:dispatch')">
                <svg-icon v-show="dealStatus == '01'" class="health-icon " @click="batchDistributeEvent(row, '1', '5')"
                  iconClass="health-flaw"></svg-icon>
              </span>
            </template>
          </vxe-table-column>
          <template v-slot:empty>
            <span>查询无数据</span>
          </template>
        </vxe-table>
        <!--分页组件-->
        <page-pagination :pageSize="size" :current="page" :total="total" @size-change="sizeChange" />
      </a-col>
    </a-spin>
    <alarm-detail ref="alarmDetail" @alarmDetailClose="alarmDetailClose"></alarm-detail>
    <drawer-view ref="orderForm" parentId="AlarmCenter" />
  </div>
</template>
<script>
import {
  getAlarmEvents,
  exportAlarmEvents,
  getDeviceByPsIds
} from '@/api/health/safetyEvents.js';
import { AlarmEventMixins } from './mixins/alarmEventMixins';
import initDict from '@/mixins/initDict';
import alarmDetail from './modules/AlarmDetail';
import { mixin } from '@/utils/mixin.js';
import tableHeight from '@/mixins/tableHeightAndSearchModel';
export default {
  name: 'safetyEvents',
  mixins: [initDict, AlarmEventMixins, mixin, tableHeight],
  components: {
    alarmDetail
  },
  props: {
    stationParams: {
      type: Object,
      default: () => {
        return {};
      }
    }
  },
  data () {
    return {
      pageloading: false,
      psId: '',
      psName: '',
      alarmType: ['1'],
      alarmTypes: [],
      searchTime: [],
      searchData: {},
      data: [],
      devices: [], // 搜索电站下的设备
      isChooseful: false, // 设备不能选择
      psKey: '', // 设备
      deviceName: [],
      elecEnsureType: ['0'],
      alarmStatus: '01',
      dealStatus: '01',
      closedType: '',
      depCodes: '',
      nodeType: '',
      faultStatus: '1'
    };
  },
  computed: {
    isShowMenu () {
      return this.$store.state.user.isShowMenu;
    }
  },
  created () {
    this.isLoadedJs('https://staticres.isolareye.com/js/hkplayer/h5player.min.js');
    this.getDictMap('alarm_type,device_type,fault_status,alarm_stauts,elec_ensure_type,farm_scene_condition_query');
    this.getAlarmTypes();
    this.psName = this.stationParams.psName;
    this.depCodes = this.stationParams.orgCode;
  },
  methods: {
    resetSearchData () {
      this.alarmStatus = '01';
      this.elecEnsureType = ['0'];
      this.closedType = '';
      this.alarmType = ['1'];
      this.searchTime = [];
      if (this.$refs.tree) {
        let node = this.$refs.tree.rootNode;
        this.psName = node.name;
        this.psId = node.id + '';
        this.depCodes = node.orgCode;
        if (node.isPsa == '0' && !node.isPs) {
          this.nodeType = 1;
        } else if (node.isPsa == '1') {
          this.nodeType = 2;
        } else if (node.isPs == 1) {
          this.nodeType = 3;
        } else {
          this.nodeType = '';
        }
        this.$refs.tree.refresh = false;
        let stationParams = {
          psId: this.psId,
          psName: this.psName,
          nodeType: this.nodeType,
          orgCode: this.depCodes,
          parentId: node.parentId,
          parentType: node.parentType,
          parentName: node.parentName,
          parentOrgCode: node.parentOrgCode
        };
        this.$emit('stationChange', stationParams);
        setTimeout(() => {
          this.$refs.tree.refresh = true;
        }, 300);
        this.$emit('resetParams');
        this.pageChange(1);
      }
    },
    // 自定义表格排序事件
    mySortChange (sortKind, sortFiled, downSort, upSort) {
      this.sortKind = sortKind;
      this.sortFiled = sortFiled;
      this.downSort = downSort;
      this.upSort = upSort;
      this.page = 1;
      this.getList();
    },
    // 左侧树交互
    showOrHide (val) {
      this.doubleRight = val;
    },
    /*
      表格列刷新
    */
    refreshColumn () {
      if (this.$refs.multipleTable) {
        this.$refs.multipleTable.refreshColumn();
      }
    },
    // 电站树变化事件
    getStationChange (val, node) {
      this.psId = node.id;
      this.psName = node.name;
      this.isChooseful = false;
      this.depCodes = node.orgCode;
      this.deviceName = [];
      if (node.isPsa == '1') {
        this.isChooseful = true; // 电站节点设备可以选择
        getDeviceByPsIds({ psId: node.id }).then(res => {
          this.devices = res.result_data;
        });
      }
      if (node.isPsa == '0' && !node.isPs) {
        this.nodeType = 1;
      } else if (node.isPsa == '1') {
        this.nodeType = 2;
      } else if (node.isPs == 1) {
        this.nodeType = 3;
      } else {
        this.nodeType = '';
      }
      let stationParams = {
        psId: this.psId,
        psName: this.psName,
        nodeType: this.nodeType,
        orgCode: this.depCodes,
        parentId: node.parentId,
        parentType: node.parentType,
        parentName: node.parentName,
        parentOrgCode: node.parentOrgCode
      };
      this.$emit('stationChange', stationParams);
      this.$nextTick(() => {
        this.pageChange(1);
      });
    },

    handleAsyncChange (val, option) {
      this.deviceName = val;
      let psKey = option.map((el) => {
        return el.key;
      });
      this.psKey = psKey.toString();
      this.pageChange(1);
    },
    // 表格页签变化事件
    currentChange () {
      this.getList();
    },
    onChange (current, pageSize) {
      this.page = 1;
      this.size = pageSize;
      this.getList();
    },
    // 表格数据变化事件
    pageChange (val) {
      this.page = val;
      this.getList();
    },
    // 格式化查询、导出条件数据
    fomartSearchData () {
      let obj = {};
      obj.alarmStatus = this.alarmStatus;
      if (this.psId) {
        if (this.nodeType == '1') {
          obj.treeOrgId = this.psId;
        } else if (this.nodeType == '2') {
          obj.treePsaId = this.psId;
        } else {
          obj.treePsId = this.psId;
        }
      }
      obj.treeId = this.psId;
      obj.alarmType = this.alarmType[0];
      obj.alarmRemark = this.alarmType.length == 2 ? this.alarmType[1] : '';
      obj.deviceType = '33';
      obj.psKey = this.psKey.toString();
      if (this.searchTime.length > 0) {
        obj.startTime = this.searchTime[0].format('YYYY-MM-DD');
        obj.endTime = this.searchTime[1].format('YYYY-MM-DD');
      } else {
        obj.startTime = '';
        obj.endTime = '';
      }
      obj.sortFiled = this.sortFiled;
      obj.sortKind = this.sortKind;
      obj.size = this.size;
      obj.curPage = this.page;
      obj.elecEnsureType = this.elecEnsureType.join();
      obj.closedType = this.closedType;
      obj.depCodes = this.depCodes;
      obj.faultStatus = this.faultStatus;
      obj.tabCode = '5';
      this.searchData = obj;
    },
    // 获取告警列表
    getList () {
      this.fomartSearchData();
      this.$emit('searchDataChange', this.searchData);
      this.dealStatus = this.alarmStatus;
      if (this.$refs.multipleTable) {
        this.$refs.multipleTable.clearScroll();
      }
      getAlarmEvents(this.searchData).then(res => {
        if (res.result_code == '1') {
          this.data = res.result_data.pageList;
          if (this.tabKind == '01') {
            this.data = this.data.map(item => {
              let disappearTimeCopy = item.disappearTime;
              item.disappearTime = item.lastHappenTime;
              return {
                ...item,
                disappearTimeCopy
              };
            });
          }
          this.total = res.result_data.rowCount;
          this.dealData();
          this.$refs.multipleTable.loadData(this.data);
          this.$refs.multipleTable.refreshColumn();
        }
        this.$emit('loadingEnd');
      }).catch(() => {
        this.$emit('loadingEnd');
      });
    },
    // 处理列表电量损失、更新时间数据
    dealData () {
      this.data.forEach(item => {
        if (!item.powerLoss && typeof (item.powerLoss) != 'number') {
          item.powerLoss = '--';
        }
        if (!item.disappearTime && typeof (item.disappearTime) != 'number') {
          item.disappearTime = '--';
        }
        if (!item.lastHappenTime && typeof (item.lastHappenTime) != 'number') {
          item.lastHappenTime = '--';
        }
      });
    },
    // 导出告警列表
    doExport () {
      this.fomartSearchData();
      this.pageloading = true;
      exportAlarmEvents(this.searchData).then(res => {
        this.$downloadFile({ fileBase64Code: res.result_data.strBase64, fileName: res.result_data.fileName });
        this.pageloading = false;
      }).catch(() => {
        this.pageloading = false;
      });
    },
    // 分析
    goDetail (row) {
      this.$refs.alarmDetail.init(row, this.dealStatus);
    },
    // 分析页面关闭
    alarmDetailClose (flag) {
      if (flag) {
        this.pageChange(1);
      }
    },
    paramsChange (val, prop) {
      if (prop == 'alarmStatus') {
        this.closedType = '';
        this.$emit('paramsChange', 'closedType', this.closedType);
      }
      this.$emit('paramsChange', prop, val);
      // this.pageChange(1);
    }
  }
};
</script>
<style lang="less" scoped>
.split-line {
  height: 1px;
  margin: 0 24px;
  background: var(--zw-divider-color--default)
}

.solar-eye-main-content {
  padding: 16px 24px;
  border-radius: 0 0 4px 4px;
}

.solar-eye-search-model {
  border-radius: 4px 4px 0 0;
}

.health-icon {
  color: var(--zw-conduct-color--normal);
  font-size: 28px;
}

.health-icon:hover {
  color: var(--zw-primary-color--default) !important;
}

:deep(.vxe-table.size--small .vxe-header--column:not(.col--ellipsis)) {
  padding: 0;
}

:deep(.vxe-table .vxe-header--row .vxe-cell) {
  padding: 0;
}

:deep(.vxe-table .vxe-header--column) {
  line-height: 20px;
}

.sort-head {
  cursor: pointer;
  padding: 2px 0 10px;
}

:deep(.sort-head span) {
  display: inline-block;
  margin-top: 10px;
}

:deep(.up-icon) {
  top: 12px;
}

:deep(.down-icon) {
  top: 21px;
}

.sort-head:hover {
  background: var(--zw-primary-bg-color--hover);
}
</style>
