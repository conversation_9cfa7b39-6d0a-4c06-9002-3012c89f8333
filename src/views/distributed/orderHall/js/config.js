import { endsWith } from 'lodash';

export const headEnumList = [
  { flowSts: '1,2,3,4,5', flowStsLabel: '全部', count: 0, icon: 'all' },
  { flowSts: '1', flowStsLabel: '待指派', count: 0, icon: 'waitBegin' },
  { flowSts: '2', flowStsLabel: '处理中', count: 0, icon: 'processing' },
  { flowSts: '3', flowStsLabel: '验收中', count: 0, icon: 'check' },
  { flowSts: '4', flowStsLabel: '已完成', count: 0, icon: 'finish' },
  { flowSts: '5', flowStsLabel: '已终止', count: 0, icon: 'stop' }
];

export const taskTypeEnumList = [
  { taskType: '', taskTypeLabel: '全部', count: 0 },
  { taskType: '8', taskTypeLabel: '缺陷', count: 0 },
  { taskType: '7', taskTypeLabel: '故障', count: 0 },
  { taskType: '1', taskTypeLabel: '清洗', count: 0 },
  { taskType: '2', taskTypeLabel: '除草', count: 0 },
  { taskType: '3', taskTypeLabel: '试验/检测', count: 0 },
  { taskType: '4', taskTypeLabel: '系统维护', count: 0 },
  { taskType: '9', taskTypeLabel: '技改', count: 0 },
  { taskType: '11', taskTypeLabel: '陪停', count: 0 },
  { taskType: '13', taskTypeLabel: '标准组串清洗', count: 0 },
  { taskType: '14', taskTypeLabel: '下网电费', count: 0 },
  { taskType: '15', taskTypeLabel: '抢修', count: 0 },
  { taskType: '16', taskTypeLabel: '物资代采', count: 0 },
  { taskType: '12', taskTypeLabel: '其他事项', count: 0 }
];

export const columnFn = () => {
  return [
    { name: 'workCode', title: '工单编号', width: 160 },
    { name: 'ownerName', title: '业主', width: 140 },
    { name: 'projectCompany', title: '项目公司', width: 180 },
    { name: 'psaName', title: '所属项目', width: 150 },
    { name: 'psName', title: '电站名称', width: 150 },
    { name: 'taskTypeLabel', title: '工单类型', width: 120 },
    { name: 'workSubclass',
      title: '工单子类',
      width: 120,
      render: (row) => {
        const workSubclass = row.workSubclass;
        return workSubclass == ' ' || !workSubclass ? '--' : workSubclass;
      }
    },
    { name: 'taskDescription', title: '工单描述', width: 160 },
    { name: 'workOrderSourceLabel', title: '工单来源', width: 120 },
    { name: 'createTime', title: '工单生成时间', width: 160 },
    { name: 'realStartTime', title: '工单派发/领取时间', width: 160 },
    { name: 'personnelArrivalTime', title: '人员到场时间', width: 160 },
    { name: 'flowEndTime', title: '工单闭环时间', width: 160 },
    // { name: 'overHours', title: '超时时长', width: 120 },
    { name: 'ticketNo', title: '关联两票', width: 160 },
    { name: 'liablePerson', title: '负责人', width: 120 },
    { name: 'serviceDepartmentName', title: '服务单位', width: 160 },
    { name: 'flowStsName', title: '工单状态', width: 140, fixed: 'right' }
  ];
};
export const checkedColumn = columnFn().map((item) => item.name);

// 站荣站貌执行时的表头映射
export const psaAppearanceColumnFn = (otherSts, appearanceVoList) => {
  return [
    {
      title: '位置',
      field: 'project',
      width: '7%',
      visible: true
    },
    {
      title: '检查内容',
      field: 'contentList',
      width: '20%',
      visible: true
    },
    {
      title: '分值',
      field: 'mark',
      width: '5%',
      visible: true
    },
    {
      title: '是否涉及',
      field: 'isNotInvolve',
      width: '7%',
      visible: true
    },
    {
      title: '示例图片',
      field: 'exampleFileList',
      width: '7%',
      visible: true
    },
    {
      title: '上传水印照片',
      field: 'uploadFileList',
      visible: true,
      width: '40%'
    },
    {
      title: '评分',
      field: 'score',
      width: '5%',
      fixed: 'right',
      visible: ['5', '9', '10'].includes(otherSts)
    },
    {
      title: '改进建议',
      field: 'improvementSuggest',
      width: '15%',
      fixed: 'right',
      visible: ['5', '9', '10'].includes(otherSts)

    },
    {
      title: '审核建议',
      field: 'examineSuggest',
      width: '15%',
      fixed: 'right',
      visible: ['5', '10'].includes(otherSts) || (otherSts === '9' && appearanceVoList.some(item => item.examineSuggest))
    }
  ];
};
// 根据任务类型获取相应参数，避免大量参数冗余
export const REFORM_TYPE = {
  '18': { dataLabel: '站容站貌', dataValue: '18', name: 'appearance' },
  '10': { dataLabel: '应发电量', dataValue: '10', name: 'expectedPower' }
};
// 工单关联两票的表头映射
export const ticketColumn = [
  {
    title: '票号',
    field: 'ticketNo',
    width: 120
  },
  {
    title: '两票类型',
    field: 'ticketTypeLabel'
  },
  {
    title: '具体类别',
    field: 'ticketTypeDtlLabel',
    width: 120
  },
  {
    title: '工作内容',
    field: 'workContent',
    width: 160
  },
  {
    title: '当前环节',
    field: 'flowUser'
  },
  {
    title: '填报人',
    field: 'createUserName'
  }, {
    title: '填报时间',
    field: 'ticketCreateTime',
    width: 120
  }
];

export const resolvePrecion = (obj = {}, type = 'divide') => {
  Object.keys(obj).forEach(item => {
    if ((endsWith(item, 'Electric') || item === 'powerLossDeviation') && obj[item]) {
      if (type === 'divide') {
        obj[item] = Number.parseFloat((obj[item] / 10000)).toFixed(4);
      } else if (type === 'multiply') {
        obj[item] = Number.parseFloat((obj[item] * 10000)).toFixed(4);
      }
    } else if (item === 'lossElectricDetail' && obj.lossElectricDetail) {
      resolvePrecion(obj.lossElectricDetail, type);
    }
  });
  return obj;
};
