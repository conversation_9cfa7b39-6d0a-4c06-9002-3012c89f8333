<template>
  <a-drawer
    width="100%"
    :visible="drawerInfo.visible"
    :title="drawerInfo.title"
    :body-style="{ padding: '24px' }"
    :drawerStyle="{ padding: 0, 'overflow-x': 'hidden' }"
    @close="onClose(false)"
    :get-container="dom"
    :wrap-style="{ position: 'absolute' }"
  >
    <a-row class="readonly-disable">
      <a-form-model
        :model="form"
        :rules="rules"
        layout="horizontal"
        v-bind="{
          labelCol: { span: 7 },
          wrapperCol: { span: 17 },
        }"
        ref="ruleForm"
      >
        <a-col :span="5" :style="{ height: height - 110 + 'px' }" class="solareye-drawer-left">
          <div :style="{ height: height - 180 + 'px', overflow: 'auto' }">
            <a-form-model-item label="设备类型" prop="deviceTypeCode"

            >
              <a-select
                v-model="form.deviceTypeCode"
                @change="(value) => getPointList(value, false)"
                option-filter-prop="children"
                :disabled="isReadonly"
              >
                <a-select-option v-for="item in deviceTypeList" :key="item.code" :value="item.code">{{
                  item.name
                }}</a-select-option>
              </a-select>
            </a-form-model-item>
            <a-form-model-item label="测点名称" prop="pointCode">
              <a-select v-model="form.pointCode" @change="changePointEvent" showSearch :filter-option="filterOption">
                <a-select-option v-for="item in pointList" :key="item.id" :value="item.pointCode" :id="item.id">
                  {{ item.pointName }}</a-select-option
                >
              </a-select>
            </a-form-model-item>
            <a-form-model-item label="是否必接" prop="accessFlag">
              <a-select v-model="form.accessFlag">
                <a-select-option :value="0">否</a-select-option>
                <a-select-option :value="1">是</a-select-option>
              </a-select>
            </a-form-model-item>
            <a-form-model-item label="测点类型">
              <a-input v-model="form.pointType" readOnly="true">{{ form.pointType === 2 ? '遥测' : '遥信' }}</a-input>
            </a-form-model-item>
            <a-form-model-item label="测点编码">
              <a-input v-model="form.pointCode" readOnly="true"></a-input>
            </a-form-model-item>
            <a-form-model-item label="存储单位">
              <a-input v-model="form.unit" readOnly="true"></a-input>
            </a-form-model-item>
            <a-form-model-item label="精度">
              <a-input v-model="form.reservedBit" readOnly></a-input>
            </a-form-model-item>
            <a-form-model-item label="上限值">
              <a-input
                v-model="form.upperLimitCnValue"
                @focus="focusEvent(true)"
                readOnly
                placeholder="请手动选择右侧的变量"
              ></a-input>
            </a-form-model-item>
            <a-form-model-item label="下限值">
              <a-input
                v-model="form.lowerLimitCnValue"
                @focus="focusEvent(false)"
                readOnly
                placeholder="请手动选择右侧的变量"
              ></a-input>
            </a-form-model-item>
            <a-form-model-item label="1级转换单位" prop="unitOne"
              :rules="{
              required: form.rateOne ||form.unitOne ? true : false,
              message: '请输入1级装换单位',
              trigger: 'blur',
            }">
              <a-input v-model="form.unitOne" placeholder="请输入1级装换单位" maxLength="6" ></a-input>
            </a-form-model-item>
            <a-form-model-item label="1级转换倍率" prop="rateOne"
              :rules="{
              required: form.rateOne ||form.unitOne ? true : false,
              message: '请输入1级装换单位',
              trigger: 'blur',
            }">
              <a-input-number
                v-model="form.rateOne"
                min="0.0001"
                max="1000000000"
                :step="0.0001"
                style="width: 100%"
                placeholder="请手动1级转换倍率"
              ></a-input-number>
            </a-form-model-item>
            <a-form-model-item label="2级转换单位" prop="unitTwo"
              :rules="{
              required: form.rateTwo ||form.unitTwo ? true : false,
              message: '请输入2级装换单位',
              trigger: 'blur',
            }">
              <a-input v-model="form.unitTwo" placeholder="请输入2级装换单位" maxLength="6" :disabled="!(form.unitOne||form.rateOne)"></a-input>
            </a-form-model-item>
            <a-form-model-item label="2级转换倍率" prop="rateTwo"
              :rules="{
              required: form.rateTwo ||form.unitTwo ? true : false,
              message: '请输入1级装换单位',
              trigger: 'blur',
            }">
              <a-input-number
                style="width: 100%"
                v-model="form.rateTwo"
                placeholder="请手动2级转换倍率"
                min="0.0001"
                max="1000000000"
                :step="0.0001"
                :disabled="!(form.unitOne||form.rateOne)"
              ></a-input-number>
            </a-form-model-item>
            <a-form-model-item label="3级转换单位" prop="unitThree"
              :rules="{
              required: form.rateThree ||form.unitThree ? true : false,
              message: '请输入3级装换单位',
              trigger: 'blur',
            }">
              <a-input v-model="form.unitThree" placeholder="请输入3级装换单位" maxLength="6" :disabled="!(form.unitTwo||form.rateTwo)"></a-input>
            </a-form-model-item>
            <a-form-model-item label="3级转换倍率" prop="rateThree"
              :rules="{
              required: form.rateThree ||form.unitThree ? true : false,
              message: '请输入3级转换倍率',
              trigger: 'blur',
            }">
              <a-input-number
                min="0.0001"
                max="1000000000"
                :step="0.0001"
                style="width: 100%"
                v-model="form.rateThree"
                placeholder="请手动3级转换倍率"
                :disabled="!(form.unitTwo||form.rateTwo)"
              ></a-input-number>
            </a-form-model-item>
            <a-form-model-item label="恒值判定">
              <a-switch v-model="form.mutationFlag" />
              <span class="solareye-color-off-line" style="margin-left: 10px; font-size: 12px"
                >开启后测点将参与恒值判定</span
              >
            </a-form-model-item>
            <a-form-model-item label="突变判定">
              <a-switch v-model="form.constantFlag" />
              <span class="solareye-color-off-line" style="margin-left: 10px; font-size: 12px"
                >开启后测点将参与突变判定</span
              >
            </a-form-model-item>
            <a-form-model-item label="测点排序">
              <a-input-number min="0" :precision="0" style="width: 100%" v-model="form.dataSort" placeholder="请填写测点排序"/>
            </a-form-model-item>
            <a-form-model-item label="更新时间">
              <a-input v-model="form.updateTime" readOnly></a-input>
            </a-form-model-item>
            <a-form-model-item label="更新人">
              <a-input v-model="form.updateUser" readOnly></a-input>
            </a-form-model-item>
          </div>

          <div
            :style="{
              width: '100%',
              textAlign: 'center',
            }"
          >
            <a-button :style="{ marginRight: '8px' }" @click="onClose(false)"
              >{{ !isReadonly ? '取消' : '关闭' }}
            </a-button>
            <a-button class="solar-eye-btn-primary" v-show="!isReadonly" @click="onSubmit" :loading="loading">
              保存
            </a-button>
          </div>
        </a-col>
        <a-col :span="19" :style="{ height: height - 120 + 'px' }">
          <div class="drawer-right-content" :style="{ height: height - 106 + 'px' }">
            <h2 class="" style="text-align: center; margin-bottom: 24px">
              {{ isUpperLimit ? '上限' : '下限' }}公式编辑器
            </h2>
            <a-form-model-item label="变量来源" class="drawer-clo-18">
              <a-select v-model="form.source" @change="getVariableSourceList">
                <a-select-option v-for="item in variableSourceList" :key="item.code" :value="item.code">{{
                  item.name
                }}</a-select-option>
                <!-- <a-select-option
                v-for="item in variableSourceList"
                :key="item.code"
                :value="item.code"
                :label="item.name"
              ></a-select-option> -->
              </a-select>
            </a-form-model-item>
            <a-form-model-item label="变量选择" class="drawer-clo-18">
              <a-input-search
                placeholder="请输入关键字输入"
                v-model="variableName"
                @search="searchVariable"
                @pressEnter="searchVariable"
              ></a-input-search>
              <div class="variable-content">
                <a-spin :spinning="spinning">
                  <a-button
                    v-for="item in variableSelectList"
                    :key="item.data_value"
                    @click="selectVariable(item)"
                    class="solar-eye-btn-grey"
                    >{{ item.data_label }}</a-button
                  >
                </a-spin>
                <img src="@/assets/images/public/no-data.png" v-if="variableSelectList.length == 0 && !spinning" />
              </div>
              <div class="">
                <a-button @click="selectConst(item)" v-for="item in variabelList" :key="item" style="margin-left: 16px">
                  {{ item }}
                </a-button>
                <a-button @click="clearVariable" style="margin-left: 16px">清除</a-button>
              </div>
            </a-form-model-item>
            <a-form-model-item label="表达公式" class="drawer-clo-18">
              <a-textarea
                placeholder="请手动选择右侧的变量"
                v-model="form.upperLimitCnValue"
                @focus="focusEvent(true)"
                v-if="isUpperLimit"
                readOnly
                :rows="4"
              ></a-textarea>
              <a-textarea
                v-model="form.lowerLimitCnValue"
                :rows="4"
                readOnly
                @focus="focusEvent(false)"
                v-else
                palceholder="请手动选择右侧的变量"
              ></a-textarea>
            </a-form-model-item>
          </div>
        </a-col>
      </a-form-model>
    </a-row>
  </a-drawer>
</template>
<script>
import { postAction } from '@/api/manage';
const baseUrl = process.env.VUE_APP_Health_BASE_URL;
// const baseUrl = 'http://***************:8081';
let innerHeight = window.innerHeight - 60 - 40 - 24;
export default {
  name: 'PointMageDrawer',
  props: {
    deviceTypeList: Array,
    default: []
  },
  data () {
    const validateLimitValue = (rule, value, callback) => {
      if (value && !/^[\u4e00-\u9fa5\w\_\+\-\/\(\).\*\[\]\（\）]+$/.test(value)) {
        callback(new Error('只允许中英文、数字、- + / * ( ) .'));
      }
      callback();
    };
    return {
      drawerInfo: {
        title: '',
        visible: false,
        type: ''
      },
      pointList: [],
      loading: false,
      form: {
        id: '', // 测点主键名称
        accessFlag: 0,
        deviceType: '',
        reservedBit: null,
        unit: '',
        pointCode: '',
        createTime: null,
        pointName: '',
        updateUser: null,
        updateTime: null,
        pointType: '',
        source: 1,
        upperLimitCnValue: '',
        upperLimitEnValue: '',
        lowerLimitCnValue: '',
        lowerLimitEnValue: '',
        deviceTypeCode: '',
        unitOne: '',
        rateOne: '',
        unitTwo: '',
        rateTwo: '',
        unitThree: '',
        rateThree: '',
        dataSort: '' // 测点排序
      },
      hideType: '',
      height: innerHeight > 700 ? innerHeight : 700,
      variableName: '',
      variabelList: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 0, '+', '-', '*', '/', '.', '(', ')'],
      rules: {
        deviceTypeCode: [
          {
            required: true,
            message: '请选择设备类型',
            trigger: 'change'
          }
        ],
        pointCode: [
          {
            required: true,
            message: '请选择测点名称',
            trigger: 'change'
          }
        ],
        upperLimitCnValue: [
          {
            validator: validateLimitValue,
            trigger: 'blur'
          }
        ],
        lowerLimitCnValue: [
          {
            validator: validateLimitValue,
            trigger: 'blur'
          }
        ]
      },
      isUpperLimit: true,

      variableSourceList: [
        {
          name: '设备技术参数',
          code: 1
        }
      ],
      variableSelectList: [],
      variableSelectListCopy: [],
      url: {
        sourceList: '/pointManagement/deviceParameter',
        detail: '/pointManagement/detail',
        pointList: '/pointManagement/pointsByDeviceType',
        update: '/pointManagement/update',
        add: '/pointManagement/add'
      },
      dom: document.getElementsByClassName('point-mage')[0],
      spinning: true
    };
  },
  created () {},
  watch: {
    visible () {}
  },
  computed: {
    isReadonly () {
      return this.drawerInfo.type == 'view';
    }
  },
  mounted () {
    this.dom = document.getElementsByClassName('point-mage')[0];
  },
  methods: {
    /**
     * 初始化抽屉
     * params {Object} column 点击列的对象
     * params {Object} title ，type 等基本信息
     * */
    initDrawer (column, obj) {
      this.loading = false;
      this.drawerInfo = Object.assign({}, this.drawerInfo, obj);
      this.$nextTick(() => {
        this.dom = document.getElementsByClassName('point-mage')[0];
      });
      this.isUpperLimit = true;
      for (let item in this.form) {
        if (item != 'id') {
          this.form[item] = '';
        }
      }
      if (obj.type != 'add') {
        this.form = Object.assign({}, this.form, column);
        this.form.accessFlag = column.accessFlag ? 1 : 0;
        this.getPointList(column.deviceTypeCode, true);
        // this.getVariableSourceList();
      }
    },
    /**
     *  上下线输入框获取焦点事件
     * params {Boolean} isUpperLimt 是否是上限值输入框
     */
    focusEvent (isUpperLimit) {
      this.isUpperLimit = !!isUpperLimit;
      let str = isUpperLimit ? this.form.upperLimitCnValue : this.form.lowerLimitCnValue;
      this.hideType = JSON.stringify(str);
    },
    clearVariable () {
      if (this.isUpperLimit) {
        this.form.upperLimitCnValue = '';
        this.form.upperLimitEnValue = '';
      } else {
        this.form.lowerLimitCnValue = '';
        this.form.lowerLimitEnValue = '';
      }
    },

    changePointEvent (e, option) {
      this.form.id = option.key;
      this.getDetail();
    },
    selectConst (item) {
      if (typeof item === 'number') {
        if (this.isUpperLimit) {
          if (
            this.form.upperLimitCnValue != '' &&
            !this.form.upperLimitCnValue.charAt(this.form.upperLimitCnValue.length - 1) === ']'
          ) {
            this.$message.error('请先选择+、-、*、/');
            return null;
          }
        } else {
          if (
            this.form.lowerLimitCnValue != '' &&
            !this.form.lowerLimitCnValue.charAt(this.form.lowerLimitCnValue.length - 1) === ']'
          ) {
            this.$message.error('请先选择+、-、*、/');
            return null;
          }
        }
      }
      if (this.isUpperLimit) {
        this.form.upperLimitCnValue += `${item}`;
        this.form.upperLimitEnValue += `${item}`;
      } else {
        this.form.lowerLimitCnValue += `${item}`;
        this.form.lowerLimitEnValue += `${item}`;
      }
    },
    /**
     *  变量选择的操作
     * params {Object} 变量对象
     */
    selectVariable (item) {
      if (this.isUpperLimit) {
        if (this.form.upperLimitCnValue != '' && !this.validateIsInclueOperator('upperLimitCnValue')) {
          return null;
        }
        this.form.upperLimitCnValue += `[${item.data_label}]`;
        this.form.upperLimitEnValue += `[${item.data_value}]`;
      } else {
        if (this.form.lowerLimitCnValue != '' && !this.validateIsInclueOperator('lowerLimitCnValue')) {
          return null;
        }
        this.form.lowerLimitCnValue += `[${item.data_label}]`;
        this.form.lowerLimitEnValue += `[${item.data_value}]`;
      }
    },
    /**
     *  选择变量时判断最后一位是否有运算符
     * params {String} keyName 上下限的key
     */
    validateIsInclueOperator (keyName) {
      let str = this.form[keyName];
      let lastStr = str ? str.charAt(str.length - 1) : '';
      if (str && !/^[\+\-\/\(\*]+$/.test(lastStr)) {
        this.$message.warning('请先选择+、-、*、/、()');
        return false;
      }
      return true;
    },
    onClose (flag) {
      this.drawerInfo.visible = false;
      this.$emit('click', flag);
    },
    onSubmit () {
      let type = this.drawerInfo.type;
      let url = type == 'add' ? this.url.add : this.url.update;
      this.$refs.ruleForm.validate((valid) => {
        if (valid) {
          this.loading = true;
          const newForm = Object.assign({}, this.form, { pointType: this.drawerInfo.pointType });
          postAction(baseUrl + url, newForm)
            .then((res) => {
              if (res.result_code == '1') {
                this.$message.success('编辑成功');
                if (this.drawerInfo.type === 'edit') {
                  this.$emit('click', true);
                }
                // this.onClose(true)
              }
              this.loading = false;
            })
            .catch(() => {
              this.loading = false;
            });
        }
      });
    },
    /**
     *  根据设备类型获取测点名称列表
     * params {String} value 设备类型
     * params {Boolean} isFirstInit 是否是第一次进入页面进行请求
     */
    getPointList (value, isFirstInit) {
      this.form.deviceType = value;
      postAction(baseUrl + this.url.pointList, { deviceType: value, pointType: 2 }).then((res) => {
        this.pointList = res.result_data;
        let isHasData = res.result_data && res.result_data.length > 0;
        if (!isFirstInit && isHasData) {
          this.form.id = res.result_data[0].id;
        }
        if (isHasData) {
          this.getDetail();
        }
      });
      this.getVariableSourceList();
    },
    // 根据设备类型及测点名称获取 测点编码等基本信息
    getDetail () {
      postAction(baseUrl + this.url.detail, {
        deviceType: this.form.deviceTypeCode,
        id: this.form.id
      })
        .then((res) => {
          let data = res.result_data;
          if (res.result_data) {
            for (let item in data) {
              if (data[item] === null) {
                data[item] = '';
              }
            }
          }
          this.form = data;
          this.form.accessFlag = data.accessFlag ? 1 : 0;
        })
        .catch(() => {});
    },
    // 根据变量来源获取变量
    getVariableSourceList () {
      this.spinning = true;
      postAction(baseUrl + this.url.sourceList, {
        deviceType: this.form.deviceTypeCode,
        source: this.form.source
      })
        .then((res) => {
          this.spinning = false;
          this.variableSelectListCopy = res.result_data;
          this.variableSelectList = res.result_data;
        })
        .catch(() => {
          this.spinning = false;
        });
    },
    searchVariable () {
      this.variableSelectList = this.variableSelectListCopy.filter((item) => {
        return item.data_label.indexOf(this.variableName) > -1;
      });
    },
    filterOption (input, option) {
      return option.componentOptions.children[0].text.toLowerCase().indexOf(input.toLowerCase()) >= 0;
    }
  }
};
</script>
<style lang="less" scoped>
.solareye-drawer-left {
  border-radius: 4px;

  padding: 24px;
  .ant-form-item {
    margin-bottom: 12px !important;
  }
}
.readonly-disable {
  input:read-only:not(.ant-calendar-range-picker-input),
  textarea:read-only {
    background-color: var(--zw-input-bg-color--disable);
  }
}
.drawer-right-content {
  display: flex;
  flex-direction: column;
  justify-content: center;
  .ant-col-18 {
    width: 52.5% !important;
  }
}
.variable-content {
  height: 200px;
  overflow: auto;

  img {
    text-align: center;
    width: auto;
    height: auto;
    max-height: 100%;
    max-width: 100%;
    margin: 0 auto;
    display: flex;
  }
}
</style>
