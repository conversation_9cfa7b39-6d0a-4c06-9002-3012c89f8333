<template>
  <div class="header-logo-drawer" :class="{ width: isExist(topList).length > 1 || (isExist() && isExist(topList).length == 1) || isExist(bottomList).length > 1 }">
    <div class="header-main">
      <div class="title">SolarEye</div>
      <div class="content">
        <div class="menu" @click="toNewPage('.isolareye.com')" v-if="isExist()">
          <img :src="require(`@/assets/images/changMenu/workD${navTheme ? '' : '_dark'}.png`)" class="left" />
          <div class="right">
            <div class="right-top right-top-r">
              iSolarWork
              <img :src="require(`@/assets/images/changMenu/D${navTheme ? '' : '_dark'}.png`)" />
            </div>
            <div class="right-bottom">数字运维系统</div>
          </div>
        </div>
        <div
          class="menu"
          @click="toNewPage('.isolareye.com/houseHold', '/houseHold')"
          v-if="isExist('/houseHold')"
        >
          <img :src="require(`@/assets/images/changMenu/houseHold${navTheme ? '' : '_dark'}.png`)" class="left" />
          <div class="right">
            <div class="right-top right-top-r">
              iSolarWork <img :src="require(`@/assets/images/changMenu/H${navTheme ? '' : '_dark'}.png`)" />
            </div>
            <div class="right-bottom">数字运维系统</div>
          </div>
        </div>
        <div
          class="menu selected"
          @click="toNewPage()"
        >
          <img :src="require(`@/assets/images/changMenu/health${navTheme ? '' : '_dark'}.png`)" class="left" />
          <div class="right">
            <div class="right-top">iSolarHealth</div>
            <div class="right-bottom">健康评测系统</div>
          </div>
        </div>
        <div
          class="menu"
          @click="toNewPage('.isolareye.com/zx', '/smartAnalyze')"
          v-if="isExist('/smartAnalyze')"
        >
          <img :src="require(`@/assets/images/changMenu/star${navTheme ? '' : '_dark'}.png`)" class="left" />
          <div class="right">
            <div class="right-top">iSolarStar</div>
            <div class="right-bottom">数据分析系统</div>
          </div>
        </div>
      </div>
      <div class="content" v-if="isExist(bottomList).length > 0">
        <div
          class="menu"
          @click="toNewPage('.isolarhealth.com/newCare', '/solarCare')"
          v-if="isExist('/solarCare')"
        >
          <img :src="require(`@/assets/images/changMenu/angel${navTheme ? '' : '_dark'}.png`)" class="left" />
          <div class="right">
            <div class="right-top">iSolarAngel</div>
            <div class="right-bottom">智能巡检系统</div>
          </div>
        </div>
        <div
          class="menu"
          @click="toNewPage('.isolareye.com/allInOne', '/integratedPlatform')"
          v-if="isExist('/integratedPlatform')"
        >
          <img :src="require(`@/assets/images/changMenu/allIn${navTheme ? '' : '_dark'}.png`)" class="left" />
          <div class="right">
            <div class="right-top">SolarEye</div>
            <div class="right-bottom">一体化平台</div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import { message } from 'ant-design-vue';
import { ACCESS_TOKEN, TENANT_ID } from '@/store/mutation-types';
const env = process.env.VUE_APP_ENV;
const topList = ['/houseHold', '/iSolarHealth', '/smartAnalyze'];
const bottomList = ['/solarCare', '/integratedPlatform'];
export default {
  name: 'NewChange',
  data () {
    return {
      env: env ? (env === 'pro' ? 'www' : env) : 'fat',
      topList: topList,
      bottomList: bottomList
    };
  },
  computed: {
    permissionMenuList () {
      return this.$store.state.user.firstMenu;
    },
    token () {
      return this.$store.state.user.token || Vue.ls.get(ACCESS_TOKEN);
    },
    tenantId () {
      return this.$store.state.user.tenantid || Vue.ls.get(TENANT_ID);
    },
    navTheme () {
      return !(this.$store.state.app.theme == 'dark' || this.$route.path == '/dashboard/analysis');
    }
  },
  methods: {
    /**
     * funcName isExist 判断是否存在
     * params {platform} String|| Array string，返回是否存在，array 返回数组
     */
    isExist (platform) {
      let menu = JSON.parse(sessionStorage.getItem('ALL_PERMISSION_MENU'));
      let isArray = Array.isArray(platform);
      return isArray ? platform.filter((item) => menu.indexOf(item) > -1) : !(platform ? menu.indexOf(platform) == -1 : JSON.parse(sessionStorage.getItem('hasErp')));
    },
    toNewPage (path, platform) {
      if (path) {
        let isInclude = this.isExist(platform);
        if (!isInclude) {
          this.$message.error({
            content: (h) =>
              h('span', {}, [
                h('span', '暂无权限，请联系管理员！'),
                h(
                  'span',
                  {
                    style: {
                      paddingLeft: '40px',
                      cursor: 'pointer'
                    },
                    on: {
                      click: () => {
                        message.destroy();
                      }
                    }
                  },
                  'X'
                )
              ]),
            duration: 5,
            class: 'no-platform',
            style: {
              marginTop: '5vh'
            }
          });
          return;
        }
        window.open(`https://${this.env}${path}/#/user/login?tenant_id=${this.tenantId}&token=${this.token}&deviceId=${localStorage.getItem('deviceId')}`);
      } else {
        this.$router.push(this.permissionMenuList.path);
      }
    }
  }
};
</script>
<style lang="less" scoped>
.header-logo-drawer {
  position: absolute;
  top: 40px;
  left: 24px;
  z-index: 9999;
  &.width {
    min-width: 524px;
  }
  .header-main {
    border-radius: 16px;
    background: linear-gradient(180deg, var(--zw-primary-common-light-color--default) 0%, var(--zw-card-bg-color--default) 17%);
    box-sizing: border-box;
    border: 1.22px solid var(--zw-border-color--default);
    box-shadow: 0px 6.12px 24.46px 0px rgba(0, 0, 0, 0.14);
    height: auto;
    margin-top: 30px;
  }
  .title {
    height: 65px;
    line-height: 65px;
    font-family: 'NeoGram-DemiBold';
    font-size: 22px;
    font-weight: 540;
    letter-spacing: 0.02em;
    color: var(--zw-text-1-color--default);
    border-bottom: 1px solid var(--zw-border-color--default);
    padding-left: 32px;
  }
  .content {
    padding: 24px 32px 14px;
    display: flex;
    flex-flow: wrap;
    .menu {
      display: flex;
      flex-direction: row;
      width: 212px;
      height: 73px;
      align-items: center;
      padding: 14px 11px 11px;
      cursor: pointer;
      margin-bottom: 10px;
      border-radius: 8px;
      &:hover {
        background: var(--zw-card-light-bg-color--default);
      }
    }
    .menu.selected {
      background: var(--zw-card-light-bg-color--default);
    }
    .menu:nth-child(odd) {
      margin-right: 32px;
    }
    .menu:only-child {
      margin-right: 0;
    }
    .menu .left {
      width: 48px;
      height: 48px;
      object-fit: cover;
    }
    .menu .right {
      margin-left: 8px;
      .right-top {
        font-family: PingFangSC-Medium;
        font-size: 19px;
        line-height: 24px;
        font-style: oblique;
        font-weight: 540;
        color: var(--zw-text-1-color--default);
      }
      .right-bottom {
        font-size: 15px;
        margin-top: 2px;
        font-weight: 200;
        line-height: 14px;
        color: var(--zw-text-2-color--default);
      }
      .right-top-r {
        position: relative;
        img {
          position: absolute;
          top: -10px;
          right: -22px;
          width: 23px;
          height: 23px;
          object-fit: cover;
        }
      }
    }
  }
  .content + .content {
    border-top: 1px solid var(--zw-divider-color--default);
    padding: 24px 0 14px;
    margin: 0 32px;
  }
}
</style>
