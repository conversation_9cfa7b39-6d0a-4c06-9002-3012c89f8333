<template>
  <div>
    <template v-if="!isForget">
      <div style="margin-bottom: 24px" class="ant-alert-message">您的账号可能存在安全风险，为了确保为您本人操作，请先进行安全验证。</div>
      <div style="fontSize: 30px; font-weight: bold; margin-bottom: 16px" class="ant-alert-message">{{ telephone }}</div>
    </template>
    <a-row :gutter="24">
      <a-form-model ref="ruleForm" :model="form" :rules="rules">
        <a-col class="gutter-row" :span="24" v-show="isForget">
          <a-form-model-item label="" prop="phone">
            <a-input v-model="form.phone" type="text" size="large" placeholder="请输入手机号码"> </a-input>
          </a-form-model-item>
        </a-col>

        <a-col class="gutter-row" :span="15">
          <a-form-model-item label="" prop="captcha">
            <a-input v-model="form.captcha" type="text" size="large" placeholder="请输入验证码"> </a-input>
          </a-form-model-item>
        </a-col>
        <a-col class="gutter-row" :span="8" style="text-align: right">
          <a-form-model-item label="">
            <a-button
              tabindex="-1"
              class="get-code-btn"
              :disabled="state.smsSendBtn"
              @click.stop.prevent="getCaptcha"
              v-text="(!state.smsSendBtn && '获取验证码') || state.time + ' s后重新获取'"
            ></a-button>
          </a-form-model-item>
        </a-col>
      </a-form-model>
    </a-row>
    <Verify
      @success="verifySuccess"
      @error="verifyError"
      @close="verifyClose"
      :mode="'pop'"
      :captchaType="'blockPuzzle'"
      :loginParams="loginParams"
      :imgSize="{ width: '330px', height: '155px' }"
      :modeType="smsMode"
      ref="verify"
    ></Verify>
  </div>
</template>
<script>
import Verify from '@/components/verifition/Verify';
import { postAction } from '@/api/manage';
export default {
  components: {
    Verify
  },
  props: {
    mobile: {
      default: null
    },
    smsMode: {
      default: 0
    },
    isForget: {
      default: false
    }
  },
  data () {
    return {
      form: {
        phone: '',
        captcha: ''
      },
      state: {
        time: 120,
        smsSendBtn: false
      },
      loginParams: {},
      rules: {
        phone: [{
          required: true,
          message: '手机号码不能为空'
        }, { validator: this.validatePhone }],
        captcha: [{
          required: true,
          message: '验证码不能为空'
        }]
      }
    };
  },
  computed: {
    telephone () {
      return this.form.phone.replace(/(\d{3})\d{4}(\d{4})/, '$1****$2');
    }
  },
  created () {
    if (this.isForget) {
      return true;
    }
    if (!this.mobile) {
      this.form.phone = this.$ls.get('Login_Userinfo').phone;
    } else {
      this.form.phone = this.mobile;
    }
  },
  methods: {
    validatePhone (rule, value, callback) {
      if (value) {
        var myreg = /^[1][3|4|5|6|7|8|9][0-9]{9}$/;
        if (!myreg.test(value)) {
          callback(new Error('请输入正确的手机号'));
        } else {
          callback();
        }
      } else {
        callback();
      }
    },
    showModal (res) {
      this.visible = true;
      this.form.phone = res ? res.phone : '';
    },
    handleOk () {
      let params = {
        mobile: this.form.phone,
        code: this.form.captcha
      };
      if (this.smsMode == 0) {
        this.$store
          .dispatch('TelePhoneLogin', params)
          .then((res) => {
            if (!res.success || res.code == 0) {
              this.cmsFailed(res.message);
            } else {
              this.visible = false;
            }
            this.$emit('ok', res.result);
          })
          .catch(() => {
            this.$emit('ok', '');
          });
      } else {
        let url = this.isForget ? '/sys/phoneVerification' : '/sys/user/phoneVerification';
        postAction(url, params).then((res) => {
          if (res.success) {
            var userList = {
              username: res.result.username,
              mobile: this.form.phone,
              code: res.result.smscode
            };
            setTimeout(() => {
              this.$emit('ok', userList);
            }, 0);
          } else {
            this.$emit('ok', '');
            this.cmsFailed(res.message);
          }
        }).catch(() => {
          this.$emit('ok', '');
        });
      }
    },
    showVerify () {
      // 当mode="pop"时,调用组件实例的show方法显示组件
      this.$refs.verify.show();
    },
    verifySuccess (params) {
      // params 返回的二次验证参数
      this.state.smsSendBtn = true;

      const hide = this.$message.loading('验证码发送中..', 0);
      let interval = window.setInterval(() => {
        if (this.state.time-- <= 0) {
          this.state.time = 120;
          this.state.smsSendBtn = false;
          window.clearInterval(interval);
        }
      }, 1000);
      setTimeout(hide, 500);
    },
    verifyError () {},
    verifyClose (params) {
      this.loginBtn = false;
      if (params) {
        // this.$notification.error({
        //   message: '登录失败',
        //   description: params.message
        // });
        if (params.code === 600) {
          this.$refs.security.showModal(params.result);
          return false;
        }
      }
    },
    // 获取验证码
    async getCaptcha (e) {
      e.preventDefault();
      if (this.isForget) {
        let arr = [];
        await this.$refs.ruleForm.validateField(['phone'], error => {
          if (error) arr.push(error);
        });
        if (arr.length > 0) {
          return;
        }
      }
      this.loginParams = {
        mobile: this.form.phone,
        smsMode: this.smsMode,
        checkKey: new Date().getTime()
      };
      this.$refs.verify.show();
    },
    cmsFailed (err) {
      this.$notification['error']({
        message: '验证错误',
        description: err,
        duration: 4
      });
    },
    validateForm () {
      this.$refs.ruleForm.validate((valid) => {
        if (valid) {
          this.handleOk();
        } else {
          this.$emit('ok', '');
        }
      });
    }
  }
};
</script>
<style scoped>
:deep(.ant-form-explain){
    text-align: left;
}
.get-code-btn {
  width: 120px;
  height: 44px;
  border-radius: 3px;
  opacity: 1;
  color: var(--zw-primary-color--default);
  background: var(--zw-primary-partial-areas-color--hover);
  box-sizing: border-box;
  border: 1px solid var(--zw-primary-color--default);
}
.get-code-btn:hover {
  color: var(--zw-primary-color--active);
  border-color: var(--zw-primary-color--active);
}
:deep(.ant-input-lg) {
  height: 44px;
}
</style>
