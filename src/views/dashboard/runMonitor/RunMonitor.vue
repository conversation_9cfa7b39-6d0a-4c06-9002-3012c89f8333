<template>
  <div class="run-monitor" id="monitorDom" :class="{isThird: !isShowMenu }">
    <!--展示功率等数据头部-->
    <head-data :headData="headData" show-type="powerStation"></head-data>
    <!--展示筛选查询头部-->
    <a-spin size="large" @click="e => e.preventDefault()" :spinning="wholePageLoading">
      <div class="search-header flex-between">
        <div class="search-header-left flex-start">
         <span class="search-label">电站</span>
          <div class="search-select">
            <ps-tree-select @change="getChild" :isQueryPs="1" :isPsaDisable="false" :checked="true" :newCount="true"
              @treeSearchEnd="listLoading = false" @checkStart="wholePageLoading = true" :hasMaintenanceStatusParams="true"
              @treeSearchStart="listLoading = true" style="width: 100%;" psCateGory="'CentralizedPV','DistributedPV','CIPV'"
              :key="psTreeKey"
            />
          </div>

          <div class="rang-input flex-start">
            <span class="rang-input-label">装机容量范围</span>
            <div class="double-input flex-start">
              <a-input-number class="rang-input-left" :min="0" :max="999999999" placeholder="请输入"
                v-model="queryParams.min" @change="rangChange('min')" />
              <a-icon type="swap-right" />
              <a-input-number class="rang-input-right" :min="0" :max="999999999" placeholder="请输入"
                v-model="queryParams.max" @change="rangChange('max')">
              </a-input-number>
            </div>
            <a-select class="unit-select" :getPopupContainer="(node) => node.parentNode" @change="unitChange"
              default-value="kW">
              <a-select-option v-for="item in unitOptions" :value="item.value" :key="item.value">
                {{ item.text }}
              </a-select-option>
            </a-select>
          </div>

          <span class="search-label">电量保证类型</span>
          <a-select :maxTagCount="1" class="type-select electric-input" mode="multiple" @change="ensureTypeChange"
            :getPopupContainer="(node) => node.parentNode" default-value="0">
            <a-select-option value="0" key="">全部</a-select-option>
            <a-select-option :value="item.dataValue" v-for="(item, index) in dictMap.elec_ensure_type" :key="index">
              {{ item.dataLable }}
            </a-select-option>
          </a-select>
          <!-- <span class="search-label">电站类型</span> -->
          <!-- <a-select class="type-select-multiple"
                    :getPopupContainer="(node) => node.parentNode"
                    v-model="queryParams.psTypeList"
                    mode="multiple"
                    :maxTagCount="1"
                    :maxTagPlaceholder="queryParams.psTypeList.length - 1 + '+...'"
                    allowClear
                    showArrow>
            <a-select-opt-group>
              <span slot="label" class="cursor-pointer" @click="psaTypeSelectAll">全部</span>
              <a-select-option
                :value="item.dataValue" v-for="(item,index) in dictMap.psa_model" :key="index">
                {{item.dataLable}}
              </a-select-option>
            </a-select-opt-group>
          </a-select> -->
          <span class="search-label">报警等级</span>
          <a-select class="type-select" :getPopupContainer="(node) => node.parentNode" :options="warningTypeOptions"
            @change="warningTypeChange" default-value="全部">
          </a-select>
          <span class="search-label">电站挂牌</span>
          <a-select class="type-select" :getPopupContainer="(node) => node.parentNode" :options="stationStatusOptions"
            @change="statusSignChange" default-value="全部">
          </a-select>
          <a-button class="solar-eye-btn-primary margin-left-24" :loading="listLoading" @click="refreshScrollLoading">
            查询
          </a-button>
        </div>
      </div>
      <!--排序头部-->
      <div class="sort-header">
        <div class="desc">为您筛选到符合条件的电站&nbsp;<span class="desc-color">{{ ipagination.total }}</span>&nbsp;/{{ selectLength
        }}座
        </div>
        <div class="sort-header-item" v-for="(item, index) in sortOptions"
          :class="item.state ? 'sort-header-item-active' : ''" @click="handleSort(index)" :key="item.psId">
          <span class="sort-header-item-text">{{ item.label }}</span>
          <svg-icon class="sort-header-item-icon"
            :iconClass="!item.isReverse ? 'sortMaxToMin' : 'sortMinToMax'"></svg-icon>
        </div>
      </div>
      <div class="main-content-chart" :style="contentHeight">
        <div class="chart-card" v-for="(item, index) in listData" :key="`RunMonitor_${index}`" :data-psId="item.psId"
          :style="{ height: isCustom ? '560px' : '' }">
          <div class="chart-card-header">
            <div class="chart-card-header-title"
              :class="{ 'chart-card-header-title-read': haveReadPsIds.includes(item.psId) }" @click="goInverter(item)">
              <a-tooltip placement="topLeft">
                <template slot="title">
                  <span v-if="psNameHide">xxxxx电站</span>
                  <span v-else>{{ item.psName }}</span>
                </template>
                <span v-if="psNameHide">xxxxx电站</span>
                <span v-else class="chart-card-header-title"
                  :class="{ 'chart-card-header-title-read': haveReadPsIds.includes(item.psId) }">
                  {{ item.psName }}
                </span>
              </a-tooltip>
            </div>
            <a-dropdown :trigger="['click']" :getPopupContainer="(node) => node.parentNode">
              <a class="ant-dropdown-link" @click="e => e.preventDefault()" style="position: absolute;"
                :style="{ top: item.statusSignName ? '0' : '14px', right: item.statusSignName ? 0 : '10px' }">
                <div v-if="item.statusSignName" style="position: relative" class="solar-eye-seal-div">
                  <img :src="navTheme == 'dark' ? darkSealBg : lightSealBg" />
                  <span class="solar-eye-seal-name">{{ item.statusSignName }}</span>
                </div>
                <span v-else>
                  <svg-icon iconClass="sign" :style="{ color: 'var(--zw-primary-color--default)' }"></svg-icon>
                  <span class="solar-eye-seal">&nbsp;&nbsp;状态标牌</span>
                </span>
              </a>
              <a-menu slot="overlay" @click="handleMenuClick($event, index, item)" :theme="navTheme">
                <a-menu-item v-for="item in menuItemList" :key="item.secondTypeCode"
                  :style="{ background: getSelectColor(item.secondTypeCode, index) }">
                  <a-tooltip placement="right">
                    <template slot="title">
                      <span>{{ item.remark }}</span>
                    </template>
                    <span>{{ item.secondName }}</span>
                  </a-tooltip>
                </a-menu-item>
              </a-menu>
            </a-dropdown>
          </div>
          <div style="display:flex;padding:0 16px">
            <div class="point5-line4 flex-start" v-for="(device, deviceIndex) in device_list"
              :key="device.iconClass + index">
              <div class="point-out" :class="device.status === 0 ? '' : 'point-out-active'">
                <svg-icon :iconClass="device.iconClass"></svg-icon>
              </div>
              <div class="point5-line4-line" v-show="deviceIndex < device_list.length - 1"></div>
            </div>

          </div>

          <div class="chart-card-data">
            <div class="chart-card-data-block">
              <div class="chart-data-text">
                <div class="left">
                  <svg-icon iconClass="real_time_power_p"></svg-icon>
                </div>
                <div class="right">
                  <p class="">{{ item.realTimePower.split(',')[0] }}</p>
                  <p class=""> 实时功率({{ item.realTimePower.split(',')[1] }})</p>
                </div>
              </div>
              <div class="chart-data-text">
                <div class="left">
                  <svg-icon iconClass="plane_radiation"></svg-icon>
                </div>
                <div class="right">
                  <p class="">{{ item.radiation.split(',')[0] }}</p>
                  <p class=""> 倾斜面辐射({{ item.radiation.split(',')[1] }})</p>
                </div>
              </div>
              <div class="chart-data-text" v-if="isCustom">
                <div class="left">
                  <svg-icon :iconClass="'year_rate' + (navTheme == 'light' ? '_light' : '')"></svg-icon>
                </div>
                <div class="right">
                  <p class="">{{ item.yearElecRate === null ? '--' : item.yearElecRate }}</p>
                  <p class="">年计划电量完成率(%)</p>
                </div>
              </div>
            </div>
            <div class="chart-card-data-block">
              <div class="chart-data-text">
                <div class="left">
                  <svg-icon iconClass="daily_output"></svg-icon>
                </div>
                <div class="right">
                  <p class="">{{ item.dailyPowerGeneration.split(',')[0] }}</p>
                  <p class="">日发电量({{ item.dailyPowerGeneration.split(',')[1] }})</p>
                </div>
              </div>
              <div class="chart-data-text">
                <div class="left">
                  <svg-icon iconClass="utilization_hours"></svg-icon>
                </div>
                <div class="right">
                  <p class="">{{ item.usingHours.split(',')[0] }} </p>
                  <p class="">日等效利用小时({{ item.usingHours.split(',')[1] }})</p>
                </div>
              </div>
              <div class="chart-data-text" v-if="isCustom">
                <div class="left">
                  <svg-icon :iconClass="'month_rate' + (navTheme == 'light' ? '_light' : '')"></svg-icon>
                </div>
                <div class="right">
                  <p class="">{{ item.monthElecRate === null ? '--' : item.monthElecRate }} </p>
                  <p class="">月计划电量完成率(%)</p>
                </div>
              </div>
            </div>
          </div>
          <div class="warning-info">
            <div class="warning-info-item" @click="gotoDiagnosisCenter(item.psId, '1', item.psName)">
              {{ warningTypeOptions[1].label }}<span style="margin-left:14px">{{ item.alarm && item.alarm.ALARM1 }}</span>
            </div>
            <div class="warning-info-item" @click="gotoDiagnosisCenter(item.psId, '2', item.psName)">
              {{ warningTypeOptions[2].label }}<span style="margin-left:14px">{{ item.alarm && item.alarm.ALARM2 }}</span>
            </div>
            <div class="warning-info-item" @click="gotoDiagnosisCenter(item.psId, '3', item.psName)">
              {{ warningTypeOptions[3].label }}<span style="margin-left:14px">{{ item.alarm && item.alarm.ALARM3 }}</span>
            </div>
          </div>
          <div class="chart-card-chart" style="padding: 24px 8px 24px 16px">
            <power-station-chart @getCardData="getCardData" :unit="item.realTimePower.split(',')[1]" :psId="item.psId"
              :refreshFlag="item.refreshFlag" :chartIndex="index" />
          </div>

        </div>
        <infinite-loading :identifier="infiniteId" v-if="listData.length !== 1 && dataReady" @infinite="refreshListData">
          <div slot="spinner">
            <a-spin size="large" v-if="!wholePageLoading" class="card-spin"></a-spin>
          </div>
          <div slot="no-more">
            <p class="no-more-data">无更多数据</p>
          </div>
          <div slot="no-results" v-if="listData.length == 0">
            <img class="no-data-img" :src="noDataImg" />
            <p class="no-data-text">暂无数据</p>
          </div>
        </infinite-loading>
      </div>
    </a-spin>

  </div>
</template>

<script>
import PowerStationChart from './modules/PowerStationChart'; // 折线图组件
import {
  getHeadData, // 头部数据
  monitorPowerStationList, // 列表数据
  getStationRefresh
} from '@/api/monitor/runMonitor';
import InfiniteLoading from 'vue-infinite-loading'; // 滚动加载插件
import initDict from '@/mixins/initDict';
import HeadData from './modules/HeadData';
import {
  getSystemCodeList
} from '@/api/health/healthapi.js';
import SvgIcon from '../../../components/SvgIcon.vue';
import { mixin } from '@/utils/mixin.js';
import { USER_INFO } from '@/store/mutation-types';
import setStatus from './mixins/setStatus';
const MONITOR_COMPONENT_PATH = '/dashboard/analysis';
export default {
  name: 'RunMonitor',
  mixins: [initDict, mixin, setStatus],
  components: { HeadData, PowerStationChart, InfiniteLoading, SvgIcon },
  watch: {
    $route: {
      immediate: true,
      handler (val, oldVal) {
        // leave some time when data not load, then enter
        const isEnter = val.path === MONITOR_COMPONENT_PATH && (oldVal && oldVal.path !== MONITOR_COMPONENT_PATH);
        if (isEnter && val.meta.keepAlive && !this.dataReady) {
          this.psTreeKey = Math.random();
        }
      }
    }
  },
  data () {
    return {
      listLoading: false, // 列表加载
      wholePageLoading: true, // 页面全局loading
      firstLoading: true, // 是否是第一次加载
      psNameHide: false, // 是否显示电站真实名称
      dataReady: false, // 列表查询查询前置条件数据是否具备
      treeLoad: false, // 树加载
      infiniteId: +new Date(), // 滚动加载标识
      listData: [], // 列表数据
      listRefreshTimeStart: '', // 记录最新一次数据刷新开始请求的时间
      noDataImg: require('@/assets/images/public/no-data.png'), // 暂无数据图片
      headData: { // 头部数据
        accessCapacity: '0,kW', // 接入容量
        dailyPowerGeneration: '0,kW', // 日发电量
        inverterNum: '0,台', // 逆变器数量
        monthCompletionRate: '0.00,%', // 月完成率
        monthPower: '0,万kWh', // 月发电量
        realTimePower: '0,kW', // 实时功率
        stationNum: '0,座', // 电站数量
        usingHours: '0,h', // 日等效小时
        yearCompletionRate: '0.00,%', // 年完成率
        yearPower: '0,万kWh', // 年发电量
        percent: 0 // 百分比
      },
      unitOptions: [ // 装机容量单位
        {
          value: 'kW',
          text: 'kW'
        }, {
          value: 'MW',
          text: 'MW'
        }, {
          value: 'GW',
          text: 'GW'
        }
      ], // 列表数据
      warningTypeOptions: [],
      ipagination: { // 分页
        current: 1,
        pageSize: 10,
        pageSizeOptions: ['10', '20', '30'],
        defaultPageSize: 10,
        defaultCurrent: 1,
        size: 'small',
        total: 0,
        showTotal: (total) => `共 ${total} 条`
      },
      queryParams: { // 搜索筛选数据对象
        min: null, // 装机容量min
        max: null, // 装机容量max
        unit: 'kW', // 装机容量单位
        isSelectAll: true, // 是否全选
        psIds: '', // 已选择的电站
        alarmType: 'ALL', // 告警类型
        statusSign: 'ALL', // 电站挂牌
        // psTypeList: [], // 电站类型
        sort: 'tmp.ps_id desc', // 默认排序
        elecEnsureType: ['0'],
        psaIds: '',
        depCodes: '',
        queryName: ''
      },
      sortOptions: [ // 排序配置项目
        {
          state: true,
          isReverse: false,
          label: '默认排序',
          sortDec: ['tmp.ps_id desc', 'tmp.ps_id asc']
        }, {
          state: false,
          isReverse: false,
          label: '并网容量',
          sortDec: ['accessCapacity desc', 'accessCapacity asc']
        }, {
          state: false,
          isReverse: false,
          label: '日等效利用小时',
          sortDec: ['usingHours desc', 'usingHours asc']
        }
        // {
        //   state: false,
        //   isReverse: false,
        //   label: '告警',
        //   sortDec: ['ALARM1 desc,ALARM2 desc,ALARM3 desc,HAPPEN_TIME desc'
        //     , 'ALARM1 asc,ALARM2 asc,ALARM3 asc,HAPPEN_TIME asc']
        // }
      ],
      haveReadPsIds: [], // 已读过的电站
      value: undefined, // 输入选择框值
      scrollTop: 0, // 当前页面滚动高度
      readPage: 0, // 局部刷新的页码
      innerWidth: window.innerWidth, // 浏览器宽度
      timer: null,
      menuItemList: [],
      stationStatusOptions: [
        {
          value: 'ALL',
          label: '全部'
        }
      ],
      status: {
        name: '',
        value: ''
      },
      device_list: [{
        status: 0,
        iconClass: 'comp_package'
      }, {
        status: 0,
        iconClass: 'junction_box'
      }, {
        status: 0,
        iconClass: 'inverter'
      }, {
        status: 0,
        iconClass: 'box_change'
      }, {
        status: 0,
        iconClass: 'electrical_unit_no'
      }],
      darkSealBg: require('../../../assets/images/public/seal.png'),
      lightSealBg: require('../../../assets/images/public/light_seal.png'),
      selectLength: 0,
      refreshWholeFlag: false,
      psTreeKey: Math.random()
    };
  },
  async created () {
    await this.getDictMap('elec_ensure_type');
    // await this.getDictMap('psa_model')
    // this.queryParams.psTypeList = this.dictMap.psa_model.map(item => item.dataValue)
    this.getSystemCodeList();
    this.getWarningTypes();
    this.timer = setInterval(this.refreshInvertal, 1000 * 5 * 60);
    setTimeout(() => {
      this.firstLoading = false;
    }, 1500);
    this.$once('hook:beforeDestroy', () => {
      clearInterval(this.timer);
      this.timer = null;
    });
  },
  // 轮询脚本 暂不删除
  // mounted() {
  //   let that = this
  //   var container = document.getElementsByClassName('run-monitor')
  //   container[0].scrollTop = 0
  //   this.timer = setInterval(() => {
  //     if (window.innerHeight == window.screen.height && window.innerWidth == window.screen.width) {
  //       container[0].scrollTop = container[0].scrollHeight
  //     }
  //   }, 10000)
  //   this.$once('hook:beforeDestroy', () => {
  //     clearInterval(that.timer)
  //     that.timer = null
  //   })
  // },
  mounted () {
    let monitorDom = document.getElementById('monitorDom');
    if (monitorDom) {
      monitorDom.scrollTop = this.scrollTop;
    }
    this.getInnerWidth(true);
    // this.refreshListData(false,true);
  },
  activated () {
    let monitorDom = document.getElementById('monitorDom');
    if (monitorDom) {
      monitorDom.scrollTop = this.scrollTop;
    }
    if (!this.timer && !this.firstLoading) {
      this.refreshInvertal();
      this.timer = setInterval(this.refreshInvertal, 1000 * 5 * 60);
    }
  },
  beforeDestroy () {
    if (this.timer) {
      window.clearInterval(this.timer);
      this.timer = null;
    }
    window.removeEventListener('resize', this.getInnerWidthResize);
  },
  deactivated () {
    if (this.timer) {
      window.clearInterval(this.timer);
      this.timer = null;
    }
  },
  computed: {
    // 卡片区域高度
    contentHeight () {
      return `height:calc(100vh - ${
        368 + // 头部各种边距
        59 + // 导航栏
        24 + // 多页签
        (this.isCustom ? 44 : 0) +
        (!this.isShowMenu ? -188 : 0)
      }px)`;
    },
    isCustom () {
      let userInfo = Vue.ls.get(USER_INFO);
      let arr = userInfo.roles.filter(item => {
        return item.roleCode == 'r10055';
      });
      return arr.length > 0;
    },
    isShowMenu () {
      return this.$store.state.user.isShowMenu;
    }
  },
  methods: {
    getInnerWidth (iswidth) {
      if (iswidth) {
        window.addEventListener('resize', this.getInnerWidthResize);
      } else {
        window.removeEventListener('resize', this.getInnerWidthResize);
      }
    },
    getInnerWidthResize () {
      this.innerWidth = window.innerWidth;
    },
    /**
     * 获取选中电站的数据
     * params {参数is_all 是否选中根节点，参数 ids 选中实体电站的id}
     */
    getChild (params, node) {
      if (this.$route.path != '/dashboard/analysis' && this.$route.path != '/houseHold/dashboard') {
        return;
      }
      this.queryParams.depCodes = node.depCodes;
      this.queryParams.psaIds = node.psaIds;
      this.queryParams.psIds = node.psIds;
      this.selectLength = node.psNum;
      if (!node.isFirstReq) {
        this.refreshScrollLoading();
      }
      this.refreshWholeFlag = true;
      this.dataReady = true;
    },
    goInverter (item) {
      let monitorDom = document.getElementById('monitorDom');
      this.scrollTop = monitorDom.scrollTop;
      if (!this.haveReadPsIds.includes(item.psId)) {
        this.haveReadPsIds.push(item.psId);
      }

      this.$emit('changePageParams', {
        psId: item.psId,
        psName: item.psName,
        source: item.source || item.source == 0 ? item.source : 1
      });
      setTimeout(() => {
        this.$emit('changePage', 2);
      }, 10);
    },
    // 获取头部总览数据
    async getOverviewData () {
      let parms = this.queryParams;
      let res = await getHeadData({
        // isParNode: this.queryParams.isSelectAll ? '1' : '0',

        // treePsId: this.queryParams.isSelectAll ? undefined : this.queryParams.psIds.join(','),
        depCodes: parms.depCodes,
        psaIds: parms.psaIds,
        treePsId: parms.psIds,
        // queryName:parms.queryName,
        alarmType: this.queryParams.alarmType === 'ALL' ? undefined : this.queryParams.alarmType,
        statusSign: this.queryParams.statusSign === 'ALL' ? undefined : this.queryParams.statusSign,
        // psTypeList: this.queryParams.psTypeList,
        elecEnsureType: this.queryParams.elecEnsureType.join(','),
        totalCapcityBegin: this.exchangeKw(this.queryParams.min) === null ? undefined : this.exchangeKw(this.queryParams.min),
        totalCapcityEnd: this.exchangeKw(this.queryParams.max) === null ? undefined : this.exchangeKw(this.queryParams.max),
        psCateGory: "'CentralizedPV','DistributedPV','CIPV'"
      });
      // if(!this.listLoading){
      setTimeout(() => {
        this.treeLoad = true;
      }, 500);
      // }

      if (Object.keys(res.result_data).length) {
        this.headData = Object.assign(this.headData, res.result_data);
      }
      this.headData.percent = this.headData.percent > 100 ? 100 : this.headData.percent;
    },
    // 排序
    handleSort (index) {
      if (this.sortOptions[index].state) {
        this.sortOptions[index].isReverse = !this.sortOptions[index].isReverse;
      } else {
        this.sortOptions = this.sortOptions.map(item => {
          item.state = false;
          return item;
        });
        this.sortOptions[index].state = true;
      }
      this.queryParams.sort = this.sortOptions[index].sortDec[Number(this.sortOptions[index].isReverse)];
      this.refreshScrollLoading(false);
    },
    // 容量单位换算
    exchangeKw (val) {
      if (val === null) {
        return null;
      }
      if (this.queryParams.unit === 'kW') {
        return val;
      }

      if (this.queryParams.unit === 'MW') {
        return val * 1000;
      }
      if (this.queryParams.unit === 'GW') {
        return val * 1000000;
      }
    },
    warningTypeChange (val) {
      this.queryParams.alarmType = val;
    },
    statusSignChange (val) {
      this.queryParams.statusSign = val;
    },
    ensureTypeChange (val) {
      this.queryParams.elecEnsureType = val;
    },
    unitChange (val) {
      this.queryParams.unit = val;
    },
    // 重置滚动刷新
    refreshScrollLoading (isResetSort) {
      this.refreshWholeFlag = true;
      if (isResetSort) {
        this.sortOptions = [ // 排序配置默认
          {
            state: true,
            isReverse: false,
            label: '默认排序',
            sortDec: ['tmp.ps_id desc', 'tmp.ps_id asc']
          }, {
            state: false,
            isReverse: false,
            label: '并网容量',
            sortDec: ['accessCapacity desc', 'accessCapacity asc']
          }, {
            state: false,
            isReverse: false,
            label: '日等效利用小时',
            sortDec: ['usingHours desc', 'usingHours asc']
          }
        ];
        this.queryParams.sort = 'tmp.ps_id desc';
      }
      this.haveReadPsIds = [];
      this.scrollTop = 0;
      this.listData = [];
      this.ipagination.current = 1;
      this.infiniteId += 1;
    },
    // 刷新列表数据 loadMore 是否是滚动刷新
    async refreshListData (loadMore, isPartRefresh) {
      if (this.refreshWholeFlag) {
        this.wholePageLoading = true;
        this.refreshWholeFlag = false;
      }
      this.listLoading = true;
      if (this.ipagination.current === 1 || isPartRefresh) {
        this.getOverviewData();
      }
      let params = this.queryParams;
      // todo 在不加遮罩的情况下 优化多选多次点击接口返回结果顺序和点击顺序不一的数据错乱bug
      // let requestTimeStart = (new Date()).getTime();
      // this.listRefreshTimeStart = requestTimeStart;
      let res = await monitorPowerStationList({
        pageSize: 8,
        currentPage: isPartRefresh ? this.readPage : this.ipagination.current,
        // isParNode: this.queryParams.isSelectAll ? '1' : '0',
        // treePsId: this.queryParams.isSelectAll ? undefined : this.queryParams.psIds.join(','),
        depCodes: params.depCodes,
        psaIds: params.psaIds,
        treePsId: params.psIds,
        // queryName:params.queryName,
        alarmType: this.queryParams.alarmType === 'ALL' ? undefined : this.queryParams.alarmType,
        // psTypeList: this.queryParams.psTypeList,
        statusSign: this.queryParams.statusSign === 'ALL' ? undefined : this.queryParams.statusSign,
        order: this.queryParams.sort,
        totalCapcityBegin: this.exchangeKw(this.queryParams.min) === null ? undefined : this.exchangeKw(this.queryParams.min),
        totalCapcityEnd: this.exchangeKw(this.queryParams.max) === null ? undefined : this.exchangeKw(this.queryParams.max),
        elecEnsureType: this.queryParams.elecEnsureType.join(','),
        psCateGory: "'CentralizedPV','DistributedPV','CIPV'"
      });
      // if (this.listRefreshTimeStart != requestTimeStart) {
      //   return;
      // }
      this.listLoading = false;

      if (res.result_code !== '1') {
        this.$message.error(res.result_msg);
        return;
      }
      let result = res.result_data || {};
      this.ipagination.total = result.rowCount || 0;
      let pageList = result.pageList || [];

      if (this.listData.length === 0) {
        this.listData = pageList.map(item => {
          return {
            ...item,
            alarm: {},
            radiation: '0,MJ/m²',
            signName: '',
            refreshFlag: Date.parse(new Date())
          };
        });
      } else {
        const arr = pageList.map(item => {
          return {
            ...item,
            alarm: {},
            radiation: '0,MJ/m²',
            refreshFlag: Date.parse(new Date())
          };
        });
        if (isPartRefresh) {
          this.listData.splice((this.readPage - 1) * 8, this.listData.length - ((this.readPage - 1) * 8));
          this.listData = this.listData.concat(arr.map(item => {
            let obj = JSON.parse(JSON.stringify(item));
            obj.refreshFlag++;
            return obj;
          }));
        } else {
          this.listData = this.listData.concat(arr);
        }
      }

      if (!isPartRefresh) {
        this.readPage = this.ipagination.current;
        this.ipagination.current++;
      }

      if (loadMore) {
        const length = pageList.length;
        if (length < 8) {
          loadMore.complete();
          if (length != 0) {
            loadMore.loaded();
          }
        } else {
          loadMore.loaded();
        }
      }
      this.listLoading = false;
      this.wholePageLoading = false;
    },

    getCardData (cardData, index) {
      this.$set(this.listData[index], 'alarm', cardData.alarm);
      this.listData[index].radiation = cardData.radiation;
    },

    // 装机容量范围校验
    rangChange (type) {
      if (this.queryParams.min !== null &&
        this.queryParams.max !== null &&
        this.queryParams.max < this.queryParams.min) {
        if (type === 'max') {
          this.queryParams.max = this.queryParams.min;
        }
        if (type === 'min') {
          this.queryParams.min = this.queryParams.max;
        }
      }
    },
    async getWarningTypes () {
      let res = await getSystemCodeList({ firstTypeCode: '0070' });
      let arr = res.result_data['0070'].filter(item => item.secondTypeCode != '4').map(item => {
        return {
          value: item.secondTypeCode,
          label: item.secondName
        };
      });
      this.warningTypeOptions = [
        {
          label: '全部',
          value: 'ALL'
        },
        ...arr
      ];
      // this.alarmGradeList = this.warningTypeOptions.map(item => item.value)
    },
    // 根据权限标识和外部指示状态进行权限判断
    show (perms) {
      return this.showHandle(perms);
    },
    isViewArea (dom) {
      // 获取可视窗口的盖度。
      let windowHeight = window.innerHeight;// 可视区域的高
      let position = dom.getBoundingClientRect();
      // 当元素的top偏移量小于页面大小并且大于高度的负数
      // 后面position.top>-position.height主要
      // 是为了防止底边在可视区域的顶部,也就是超出可视区域
      // 这里的判断是重点
      if (position.top < windowHeight && position.top > -(position.height - 103)) {
        return true;
      }
      return false;
    },
    // 获取可视区域内的电站id
    getViewAreaPsId (isPsId) {
      let chartCards = document.getElementsByClassName('chart-card');
      let psIds = [];
      let indexList = [];
      if (chartCards.length > 0) {
        chartCards.forEach((element, index) => {
          if (this.isViewArea(element)) {
            psIds.push(element.dataset.psid);
            indexList.push(index);
          }
        });
      }
      return isPsId ? psIds : indexList;
    },
    refreshInvertal () { // 定时刷新
      let arr = this.getViewAreaPsId(false);
      arr.map(item => {
        this.listData[item].refreshFlag = new Date().getTime();
      });
      this.getOverviewData();
      getStationRefresh({
        psIds: this.getViewAreaPsId(true),
        psCateGory: "'CentralizedPV','DistributedPV','CIPV'"
      }).then(res => {
        if (res.result_code === '1') {
          let data = res.result_data || [];
          arr.map((item, index) => {
            this.listData[item] = Object.assign(this.listData[item], data[index]);
          });
        } else {
          this.$message.error(res.result_msg);
        }
      });
    }
  }
};
</script>

<style lang="less">
.point5-line4 {
  .point-out {
    width: 42px;
    height: 40px;
    text-align: center;
    line-height: 36px;

    .svg-icon {
      color: white;
      font-size: 18px;

    }
  }

  .point5-line4-line {
    width: 15px;
    height: 1px;
    margin: 0 2px;
    background: #7BB9FE;
    border: 1px solid #7BB9FE;
  }
}

.run-monitor {
  overflow-y: auto;
  width: 100%;
  height: calc(100vh - 181px);
  font-family: PingFangSC-Regular, PingFang SC;
  &.isview {
     height: calc(100vh - 121px);
  }
  &.isThird {
    height: calc(100vh - 48px);;
  }
  .monitor-list-area {
    margin-top: 16px;
    padding: 24px;
    min-height: 446px;
    background: #ffffff;
    box-shadow: 0 2px 4px 0 rgba(0, 0, 0, 0.06);
    box-sizing: border-box;
    border-radius: 6px;

    .operation-text {
      font-size: 14px;
      font-weight: 400;
      cursor: pointer;
    }

    .operation-icon {
      font-size: 20px;
      cursor: pointer;
    }

    .operation-icon:hover {
      color: var(--zw-primary-color--default);
      stroke: var(--zw-primary-color--default);
    }

    .margin-left-24 {
      margin-left: 24px;
    }
  }

  .cursor-pointer {
    cursor: pointer;
  }

  .margin-left-24 {
    margin-left: 24px;
  }

  .width-100 {
    width: 100%;
  }

  .width-100px {
    width: 80px;
  }

  .flex-wrap {
    flex-wrap: wrap;
    padding: 12px !important;
  }

  .flex-between {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .flex-start {
    display: flex;
    justify-content: flex-start;
    align-items: center;
  }

  .search-header {
    height: 80px;
    background: var(--zw-card-bg-color--default);
    box-shadow: 0 2px 4px 0 rgba(0, 0, 0, 0.06);
    //margin: 16px 0 0;
    padding: 24px;
    box-sizing: border-box;
    border-radius: 6px;

    .search-header-left {
      position: relative;

      .search-select {
        position: relative;
        width: 260px;

        .close-icon {
          position: absolute;
          right: 10px;
          font-size: 12px;
          top: 50%;
          transform: translateY(-50%);
          color: var(--zw-text-1-color--default);
        }

        .close-icon:hover {
          color: var(--zw-text-color--disable);
        }

      }

      .rang-input {
        margin-left: 24px;
        position: relative;

        .rang-input-label {
          font-size: 14px;
          margin-right: 8px;
        }

        .double-input {
          width: 240px;
          border-radius: 4px;
          border: 1px solid var(--zw-border-color--default);
          height: 32px;
          display: flex;
          justify-content: space-around;

          input {
            text-align: center;
          }

          .rang-input-left {
            width: 80px;
            box-sizing: border-box;
            border: none;
            box-shadow: none;
            background-color: transparent;

            .ant-input-number-handler-wrap {
              display: none;
            }
          }

          .rang-input-right {
            width: 80px;
            box-sizing: border-box;
            border: none;
            box-shadow: none;
            background-color: transparent;

            .ant-input-number-handler-wrap {
              display: none;
            }
          }

        }

        .double-input:hover {
          border: 1px solid var(--zw-primary-color--default);
        }

        .unit-select {
          width: 90px;
          margin-left: 8px;
        }

      }

      .search-label {
        font-size: 14px;

        margin: 0 8px 0 24px;
      }

      .type-select {
        width: 166px;
        @media screen and (min-width: 1920px) {
          width: 166px;
        }
      }
      .electric-input {
        width: 260px;
      }

      .type-select-multiple {
        width: 300px;
      }

      .search-input {
        width: 200px;
        height: 36px;
      }
    }
  }

  .main-content-chart {
    display: flex;
    justify-content: flex-start;
    flex-flow: row wrap;

    .no-data-img {
      margin-top: 72px;
    }

    .no-data-text {
      margin-top: 25px;
      font-size: 20px;
      font-weight: 400;
    }

    .no-more-data {
      margin-top: 32px;
      padding-bottom: 60px;

      font-size: 16px;
    }

    .infinite-loading-container {
      width: 100%;
    }

    .card-spin {
      width: 100%;
      height: 100px;
    }

    .chart-card {

      height: 505px;
      width: calc((100% - 3 * 16px) / 4);
      margin-right: 16px;
      margin-bottom: 16px;
      box-sizing: border-box;
      border-radius: 4px;

      box-shadow: 0 4px 6px 0 rgba(21, 60, 104, 0.09);

      .chart-card-chart {
        width: 100%;
        height: 248px;
        /*height: calc(100% - 250px);*/
        padding: 24px;
        box-sizing: border-box;
      }

      .chart-card-header {
        height: 50px;
        border-radius: 4px 4px 0 0;
        //box-shadow: 0 1px 4px 0 rgba(0, 0, 0, 0.07);
        padding: 14px 24px 0;
        display: flex;
        flex-direction: column;
        position: relative;

        .chart-card-header-title {
          font-size: 16px;
          font-weight: 550;
          margin-bottom: 10px;
          width: 75%;
          overflow: hidden;
          white-space: nowrap;
          text-overflow: ellipsis;
          cursor: pointer;
        }

      }

      .chart-card-data {
        width: 100%;
        display: flex;
        min-height: 105px;
        justify-content: space-between;
        box-sizing: border-box;
        //border-bottom: 2px dashed #E1E3E7;
        padding: 24px;

        .chart-card-data-block {
          display: flex;
          justify-content: space-between;
          flex-direction: column;
        }

        .chart-data-text {
          margin-right: 16px;
          display: inline-flex;
          white-space: nowrap;

          p {
            margin: 0;
          }

          .left {
            width: 44px;
            height: 44px;
            text-align: center;
            display: inline-flex;
            align-items: center;
            justify-content: center;

            .svg-icon {
              font-size: 22px;
            }

            margin-right: 16px;
          }

          &-label {
            text-align: right;
            font-size: 12px;

            margin: 0;
          }

          &-num {
            text-align: left;
            font-size: 14px;
            font-weight: 600;
            margin: 0;
          }
        }

        .chart-data-text+.chart-data-text {
          margin-top: 16px;
        }
      }

      .warning-info {
        display: flex;
        justify-content: space-around;
        padding: 0 24px 12px;

        .warning-info-item {
          width: 125px;
          height: 28px;
          box-sizing: border-box;
          font-size: 12px;
          text-align: center;
          line-height: 28px;
        }
      }
    }

    @media screen and (min-width: 1367px) {
      .chart-card:nth-child(4n) {
        margin-right: 0 !important;
      }
    }

    @media screen and (max-width: 1366px) {
      .chart-card:nth-child(3n) {
        margin-right: 0 !important;
      }

      .chart-card {
        width: calc((100% - 2 * 16px) / 3);
      }
    }

    .chart-card:hover {
      /*box-shadow: 0 4px 6px 0 rgba(21, 60, 104, 0.09);*/
    }
  }
}

.mobile {
  .run-monitor {
    .data-header {
      height: auto;
      flex-direction: column;

      .info-data {
        width: 100%;
      }

      .divider {
        width: 90%;
        height: 1px;
        margin: 10px;
      }
    }

    .sort-header {
      flex-wrap: wrap;
      margin: 0;

      .sort-header-item {
        margin: 8px;
      }
    }

    .search-header.flex-between {
      height: auto;
      flex-direction: column;

      .search-header-left {
        flex-direction: column;
      }
    }

    .main-content-chart .chart-card {
      width: 100%;

      .chart-card-data {
        flex-direction: column;
      }
    }

  }
}

.run-monitor .sort-header {
  height: 54px;
}
</style>
