<template>
  <div class='device-tree-container'>
    <a-tree :show-line='true' :showIcon="true" :expandedKeys.sync="expandedKeys" @expand="handleTreeExpand" >
      <a-icon style="font-size: 16px;color:inherit" slot="switcherIcon" type="caret-down" />
      <a-tree-node :key="firstLevel.key" :title="firstLevel.title" v-for="firstLevel in treeData">
        <a-tree-node :key="secondLevel.key" :title="secondLevel.title" v-for="secondLevel in firstLevel.children">
          <a-icon  slot="switcherIcon" :type="expandedKeys.includes(secondLevel.key)?'caret-down':'caret-right'" />
          <a-tree-node class="third-level-item" :key="thirdLevel.key" :title="thirdLevel.title" v-for="thirdLevel in secondLevel.children">
          </a-tree-node>
        </a-tree-node>
      </a-tree-node>
    </a-tree>
  </div>
</template>

<script>
// import { omit, pick } from 'lodash';
// import uuid from 'uuid/v4';
export default {
  name: 'TestInfo',
  props: {
    testInfoList: {
      type: Array,
      default: () => ([])
    }
  },
  data () {
    return {
      treeData: [],
      expandedKeys: []
    };
  },
  methods: {
    loopTreeDataFn (dataSource = []) {
      return dataSource.map((testInfoItem, i) => {
        return {
          ...testInfoItem,
          level: 1,
          key: testInfoItem.testContentId + '-' + testInfoItem.testContent,
          title: `试验对象${i + 1}：${testInfoItem.testContent}`,
          children: (testInfoItem.deviceDtoList || []).map((deviceDtoItem, j) => {
            return {
              ...deviceDtoItem,
              level: 2,
              parentId: testInfoItem.testContentId,
              key: deviceDtoItem.testDeviceId + '-' + deviceDtoItem.deviceTypeName,
              title: `设备类型${i + 1}-${j + 1}：${deviceDtoItem.deviceTypeName}  ${deviceDtoItem.deviceAmount}个`,
              children: (deviceDtoItem.deviceNoVoList || []).map((deviceNoVoItem, k) => {
                return {
                  ...deviceNoVoItem,
                  level: 3,
                  parentId: deviceDtoItem.testContentId,
                  key: deviceNoVoItem.testDeviceNoId + '-' + deviceNoVoItem.deviceName,
                  title: `NO.${k + 1} ${deviceNoVoItem.deviceNo} ${deviceNoVoItem.deviceName}`
                };
              })
            };
          })
        };
      });
    },
    handleTreeExpand (selectedKeys, { expanded, node }) {
      console.log('selectedKeys', selectedKeys);
      this.expandedKeys = selectedKeys;
    },
    handleDefaultExpanded (dataSource) {
      // 默认展开试验对象
      const firstLevelKeys = dataSource.map(o => o.key);
      this.expandedKeys = firstLevelKeys;
    }
  },
  watch: {
    'testInfoList': {
      deep: true,
      immediate: true,
      handler (val, oldVal) {
        console.log('loopTreeDataFn', val);
        const dataSource = this.loopTreeDataFn(val);
        this.handleDefaultExpanded(dataSource);
        this.treeData = dataSource;
      }
    }
  }
};
</script>

  <style lang="less" scoped>
  .device-tree-container {
    margin-left: 60px;
    :deep(.third-level-item){
      margin-left: 24px;
      &:before{
        display: none;
      }

      .ant-tree-switcher.ant-tree-switcher-noop{
        display: none;
      }
    }
  }
  :root[data-theme='dark']{
    .device-tree-container{
      :deep(.ant-tree.ant-tree-show-line li span.ant-tree-switcher){
        background-color: transparent !important;
        color: #fff !important;
      }
    }
  }
  </style>
