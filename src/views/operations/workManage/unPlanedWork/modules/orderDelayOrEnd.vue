<!-- 延期 终止表单 -->
<template>
  <div>
    <div class="view_detail_box" v-if="!disabled">
      <div class="title_box_top">
        <span class="before"></span>
        <span v-if="delayOrEndType == '0'">延期信息</span>
        <span v-else-if="delayOrEndType == '1'">终止信息</span>
        <span v-else>挂起信息</span>
      </div>
      <!-- 提交表单 -->
      <a-form-model ref="delayOrEndForm" :model="delayOrEnd" :rules="rules" :labelCol="{ style: 'width: 160px' }" :wrapperCol="{ style: 'width: calc(100% - 160px)' }">
        <a-row :gutter="24">
          <a-col :xl='8' :sm='12' :xs='24' v-if="delayOrEndType == '0'">
            <a-form-model-item label="延期天数（天）" prop="delayDay">
              <a-input-number v-model="delayOrEnd.delayDay" :min="1" :max="999" :precision="0" placeholder="请输入" style="width:50%" />
            </a-form-model-item>
          </a-col>
          <a-col :xl='8' :sm='12' :xs='24' v-if="delayOrEndType == '1' && ['1', '2'].includes(order().taskType)">
            <a-form-model-item label="完成容量(MW)" prop="completeCap">
              <a-input-number v-model="delayOrEnd.completeCap" :min="0" :max="9999.9999" :precision="4"
                placeholder="请输入" style="width:50%" />
            </a-form-model-item>
          </a-col>
        </a-row>
        <a-row>
          <a-col :span="12">
            <a-form-model-item :label="reasonLable()" prop="reason">
              <a-textarea v-model="delayOrEnd.reason" :max-length="200" :auto-size="{ minRows: 2, maxRows: 4}"
                :title="delayOrEnd.reason" :placeholder="'请输入'" style="width: 100%" />
            </a-form-model-item>
          </a-col>
        </a-row>
      </a-form-model>
    </div>

    <detail-layout v-else :labelList="baseInfo" :form="delayOrEnd" :title="delayOrEndType == '0' ? '延期信息' : '终止信息'"></detail-layout>
  </div>
</template>

<script>
export default {
  props: {
    delayOrEndType: {
      type: String,
      default: ''
    },
    disabled: {
      type: Boolean,
      default: false
    }
  },
  components: {},
  inject: ['order'],
  data () {
    let validateCompleteCap = (rule, value, callback) => {
      if (value && (Number(value) > Number(this.order().cleanCap))) {
        callback(new Error('实际完成容量不能大于计划容量'));
      }
      callback();
    };
    return {
      // 普通表单
      delayOrEnd: {
        delayDay: '',
        completeCap: '',
        reason: ''
      },
      baseInfo: [
        { label: '延期天数(天)',
          key: 'delayDay',
          span: 24,
          func: (params) => {
            return params.delayOrEndType == '0';
          }
        },
        { label: '完成容量(MW)',
          key: 'completeCap',
          span: 24,
          func: (params) => {
            return params.delayOrEndType == '1' && ['1', '2'].includes(params.taskType);
          }
        },
        { label: this.delayOrEndType == '0' ? '延期原因' : '终止原因', key: 'reason', span: 24 }
      ],
      rules: {
        delayDay: [{ required: true, message: '请填写延期天数(天)', trigger: 'blur' }],
        completeCap: [{ required: true, message: '请填写完成容量(MW)', trigger: 'blur' },
          { validator: validateCompleteCap, trigger: 'blur' }
        ],
        reason: [{ required: true, message: `请填写${this.reasonLable()}`, trigger: 'blur' }]
      }
    };
  },
  created () {
    let order = this.order();
    this.delayOrEnd = {
      delayDay: order.delayDay,
      completeCap: order.completeCap,
      reason: order.reason,
      delayOrEndType: this.delayOrEndType,
      taskType: order.taskType
    };
  },
  methods: {
    // 更新数据
    updataDelayOrEnd () {},
    reasonLable () {
      let lable = '';
      switch (this.delayOrEndType) {
        case '0':
          lable = '延期原因';
          break;
        case '1':
          lable = '终止原因';
          break;
        case '2':
          lable = '挂起原因';
          break;
      }
      return lable;
    },
    // 保存验证
    saveDelayOrEnd () {
      return new Promise((resolve, reject) => {
        this.$refs.delayOrEndForm.validate((valid) => {
          if (valid) {
            resolve(this.delayOrEnd);
          } else {
            reject(new Error());
          }
        });
      });
    }
  }
};
</script>

<style lang="less" scoped>
  .view_detail_box {
    .title_box_top {
      font-size: 16px;
      font-weight: 500;
      color: #5b5b5b;
      margin-bottom: 16px;

      .before {
        width: 4px;
        height: 15px;
        border-left: 4px solid #FF8F33;
        margin-right: 16px;
        border-radius: 0px 2px 2px 0px;
      }
    }
  }

  :deep(.ant-form-item) {
    width: 100%;
    display: inline-flex;
  }

  :root[data-theme='dark'] {
    .title_box_top {
      color: #fff;

      .before {
        border-left-color: #60CAFE;
      }
    }
  }
</style>
