import echarts from '@/utils/enquireEchart';

const mixin = {
  data () {
    return {

    };
  },
  methods: {
    makeXAxis (gridIndex, opt, length) {
      let color = this.navTheme == 'dark' ? '#fff' : '#9B9B9B';
      return echarts.util.merge({
        type: 'category',
        axisLine: { onZero: false, lineStyle: { color: 'rgba(51, 51, 51, .14)' } },
        axisTick: { show: false },
        axisLabel: { show: gridIndex == length - 1, color: color }, // 从上往下排列 最下面一个显示x轴label
        splitLine: { show: false, lineStyle: { color: 'rgba(51, 51, 51, .14)' } },
        gridIndex: gridIndex
      }, opt || {}, true);
    },
    makeYAxis (gridIndex, opt, is1n) {
      let isDark = this.navTheme == 'dark'; let color = isDark ? '#fff' : '#9B9B9B';
      return echarts.util.merge({
        type: 'value',
        gridIndex: gridIndex,
        nameLocation: is1n ? 'end' : 'middle',
        nameGap: is1n ? 20 : 80,
        axisPointer: { show: false },
        axisLine: { onZero: false, show: false },
        axisLabel: { show: true, color: color, showMaxLabel: gridIndex == 0 }, // y轴刻度线 最上面一个y轴才显示顶部刻度label
        nameTextStyle: {
          color: isDark ? '#fff' : 'rgba(0, 0, 0, 0.8)'
        },
        splitLine: {
          lineStyle: {
            color: isDark ? ['#354661'] : ['#d6d6d6']
          }
        },
        scale: true
      }, opt || {}, true);
    },
    makeGrid (top, index, length) {
      let isDark = this.navTheme == 'dark';
      let colorBg = isDark ? (index % 2 == 1 ? '#111C2D' : '#152236') : (index % 2 == 1 ? 'rgba(255, 143, 51, 0.06)' : '#fff');
      let borderColor = isDark ? '#3B495C' : '#d6d6d6';
      return echarts.util.merge({
        top: top,
        bottom: length == index ? 60 : undefined,
        show: true,
        right: 100,
        borderColor: borderColor,
        backgroundColor: colorBg, // 隔行背景色设置
        height: length == 1 ? this.height - 160 : this.gridHeight // 根据y轴个数不同计算不同的高度
      }, {}, true);
    },
    makeSeries (index, yIndex, opt, type) {
      return echarts.util.merge({
        type: type == 'stack' ? 'bar' : type,
        stack: type == 'stack' ? 'total' + index : undefined,
        symbol: 'circle',
        symbolSize: type == 'scatter' ? 8 : 4,
        barGap: 0,
        xAxisIndex: index,
        yAxisIndex: yIndex,
        large: true,
        cursor: 'default',
        emphasis: {
          focus: 'series'
        },
        smooth: true
      }, opt || {}, true);
    }

  }
};

export { mixin };
