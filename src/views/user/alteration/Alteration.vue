<template>
 <div style="padding: 68px 40px">
      <a-steps class="steps" :current="currentTab">
      <a-step title="手机验证"/>
      <a-step title="密码"/>
      <a-step title="完成"/>
    </a-steps>
    <div class="content">
      <step2 v-if="currentTab === 0" @nextStep="nextStep"/>
      <step3 v-if="currentTab === 1" @nextStep="nextStep" @prevStep="prevStep" :userList="userList"/>
      <step4 v-if="currentTab === 2" @prevStep="prevStep" @finish="finish" :userList="userList"/>
    </div>
 </div>

</template>

<script>
import Step2 from './Step2';
import Step3 from './Step3';
import Step4 from './Step4';

export default {
  name: 'Alteration',
  components: {
    Step2,
    Step3,
    Step4
  },
  data () {
    return {
      description: '将一个冗长或用户不熟悉的表单任务分成多个步骤，指导用户完成。',
      currentTab: 0,
      userList: {},
      // form
      form: null
    };
  },
  methods: {

    // handler
    nextStep (data) {
      this.userList = data;
      if (this.currentTab < 4) {
        this.currentTab += 1;
      }
    },
    prevStep (data) {
      this.userList = data;
      if (this.currentTab > 0) {
        this.currentTab -= 1;
      }
    },
    finish () {
      this.currentTab = 0;
    }
  }
};
</script>

<style lang="less" scoped>
  .steps {
    max-width: 750px;
    margin: 16px auto;
  }
  :deep(.ant-steps-item-icon) {
    height: 24px;
    width: 24px;
    border-radius: 24px;
   line-height: 24px;
   margin-top: 4px;
   .anticon-check.ant-steps-finish-icon {
    font-size: 12px;
   }
  }
  :deep(.ant-steps-item) {
    font-size: 14px;
    .ant-steps-item-title {
      padding-right: 8px;
      &:after {
        height: 2px;
        background-color: var(--zw-border-color--default) !important;
      }
    }
  }
  :deep(.ant-steps-item-wait) {
    .ant-steps-item-icon {
      color: var(--zw-text-3-color--default);
      border-color: var(--zw-text-3-color--default);
      .ant-steps-icon {
        color: var(--zw-text-3-color--default) !important;
      }
    }
    .ant-steps-item-title {
      color: var(--zw-text-3-color--default) !important;
    }
  }
  :deep(.ant-steps-item-active),:deep(.ant-steps-item-finish) {
    .ant-steps-item-title{
      &::after  {
        background-color: var(--zw-primary-color--default) !important;
      }
    }
  }
  :deep(.ant-steps-item-active) {
    .ant-steps-item-title{
      color: var(--zw-primary-color--default) !important;
    }
  }
</style>
