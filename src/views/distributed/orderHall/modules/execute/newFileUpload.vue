<template>
 <div  class="file-upload" :style="{'--zoom': zoom}">
   <a-upload
     :accept='accept'
     :action="action"
     :headers="headers"
     :custom-request='customRequest'
     :beforeUpload='beforeUpload'
     list-type='picture-card'
     :remove="handleRemove"
     v-bind:class="{'upload-disabled': isDisabled}"
     :disabled='loading'
   >
     <a-icon type="plus" v-bind:style="{'font-size': iconSize + 'px'}"/>
     <div  class="ant-upload-text com-color">上传视频</div>
   </a-upload>
   <template v-if="!disabled && tip">
     <span @click.stop="()=>{}" class="ant-upload-hint picture-card-tip" :style="'color:#999;font-size: 12px;' + $attrs.extraTipSty">提示：{{tip}}</span>
   </template>
 </div>
</template>

<script>
import { apiBaseUrl } from '@/utils/gy-request';
import { ACCESS_TOKEN, TENANT_ID } from '@/store/mutation-types';
import axios from 'axios';
export default {
  model: {
    prop: 'files',
    event: 'set'
  },
  props: {
    // 文件列表
    files: {
      type: Array,
      default: () => {
        return [];
      }
    },
    // 接受上传的文件类型
    accept: {
      type: String,
      default: () => '.mp4'
    },
    // 上传icon的字体大小
    iconSize: {
      type: Number,
      default: 30
    },
    // 接受上传文件大小（M）
    maxSize: {
      type: Number,
      default: 10
    },
    // 允许上传的文件数量
    maxNum: {
      type: Number,
      default: 5
    },
    // 是否为只读属性
    disabled: {
      type: Boolean,
      default: false
    },
    // 照片墙模式下-图片的缩放, 范围 0.5-1，值越小图片越小
    zoom: {
      type: Number,
      default: 1
    },
    tip: {
      type: String,
      default: ''
    }
  },
  watch: {
    watchfiles: {
      immediate: true,
      handler (val, old) {
        let files = Array.isArray(val) ? val.filter(file => !!file) : [];
        files.forEach((item, index) => {
          item.uid = item.uid || index;
          item.name = item.fileName || item.pathName;
          item.url = item.firstFrame;
        });
        this.fileList = files;
      }
    }
  },
  computed: {
    watchfiles () {
      return this.files;
    },
    isDisabled () {
      return this.disabled || (this.maxNum && this.fileList.length >= this.maxNum);
    }
  },
  data () {
    return {
      action: apiBaseUrl + '/sys/oss/file/upload',
      headers: {
        'X-Access-Token': Vue.ls.get(ACCESS_TOKEN),
        'tenant_id': Vue.ls.get(TENANT_ID)
      },
      loading: false,
      fileList: [],
      progress: 0
    };
  },
  methods: {

    handleRemove (file) {
      let self = this;
      if (self.disabled) {
        return;
      }
      let index = self.fileList.indexOf(file);
      self.fileList.splice(index, 1);
      self.$emit('set', self.fileList);
      self.max = self.fileList.length;
    },
    getFileExtension (fileName) {
      let extension = fileName.substring(fileName.lastIndexOf('.') + 1);
      return extension.toLocaleLowerCase();
    },
    beforeUpload (file) {
      console.log('beforeUpload-->', file);
      this.loading = true;
      const valid = this.checkFile(file);
      if (!valid) {
        this.loading = false;
        // eslint-disable-next-line prefer-promise-reject-errors
        return Promise.reject('error');
      }
      return true;
    },
    checkFile (file) {
      let { maxSize } = this;
      if (!file || (file && !file.size)) {
        this.showNotification('emptyError', '禁止上传空文件!');
        return false;
      }
      if (file.size > (maxSize * 1024 * 1024)) {
        this.showNotification('sizeError', `视频过大，请缩小至${maxSize}M内后重试哦~`);
        this.loading = false;
        return false;
      }
      return true;
    },
    /*
       错误提示
     */
    showNotification (key, description) {
      this.$notification.close(key);
      this.$notification.error({
        'key': key,
        'message': '系统提示',
        'description': description,
        'duration': 3
      });
    },
    async customRequest (arg) {
      console.log('customRequest-->', arg);
      const _this = this;
      const { onProgress, onError, onSuccess, headers } = arg;
      const fd = new FormData(); // 表单格式
      fd.append('file', arg.file);
      fd.append('isNeedBase', '1');
      fd.append('isNeedFirstFrame', '1'); // 需要返回视频第一帧
      try {
        const res = await axios.post(arg.action, fd,
          {
            headers,
            onUploadProgress: ({ total, loaded }) => {
              onProgress({ percent: Math.round((loaded / total) * 100).toFixed(2) }, arg);
            }
          });
        let result = Object.assign({}, res.data.result, { 'base64code': undefined, 'url': undefined, 'fileType': _this.getFileExtension(arg.file.name) });
        _this.fileList = _this.fileList.concat([result]);
        // 上传接口回调 触发父组件 change 事件
        _this.$emit('change', result);
        _this.$emit('set', _this.fileList);
        onSuccess(res.data, arg);
      } catch (e) {
        onError('文件上传失败，请重试');
        this.$notification.error({
          message: '系统提示',
          description: `${arg.file.name}上传失败`,
          duration: 3
        });
      } finally {
        this.loading = false;
      }
    }
  }
};
</script>
<style lang="less" scoped>
.file-upload{
  position: relative;
}
.picture-card-tip{
  position: absolute;
  left: 0;
  bottom: -10px;
}
:deep(.ant-upload-list-picture-card){
  zoom: var(--zoom)
}
:deep(.ant-upload-select-picture-card){
  zoom: var(--zoom)
}
:deep(.upload-disabled > .ant-upload-select){
  display: none;
}
:deep(.ant-upload-list-text){
  max-width: 500px;
}
</style>
<style>
:root[data-theme='dark'] .file-upload .ant-upload-list-item-name{
  color: #60CAFE;
}
</style>
