export const algorithmColumns = [
  {
    key: 'psTypeName',
    title: '电站类型',
    width: 120
  },
  {
    key: 'deviceTypeName',
    title: '设备类型',
    width: 120
  },
  {
    key: 'deviceType',
    title: '设备ID',
    width: 160
  },
  {
    key: 'alarmRemarkName',
    title: '诊断类型',
    width: 160
  },
  {
    key: 'alarmRemark',
    title: '诊断类型ID',
    width: 180
  },
  {
    key: 'alarmReasonName',
    title: '诊断原因',
    width: 160
  },
  {
    key: 'alarmReason',
    title: '诊断原因ID',
    width: 160
  },
  {
    key: 'algoPoints',
    title: '对应测点编号',
    width: 160
  },
  {
    key: 'faultCurvePoints',
    title: '时序分析测点编号',
    width: 160
  },
  {
    key: 'alarmLevelName',
    title: '诊断等级',
    width: 160
  },
  {
    key: 'alarmRiskWarning',
    title: '风险提示',
    width: 200
  },
  {
    key: 'alarmOpinionContent',
    title: '处理建议',
    width: 200
  },
  {
    key: 'updateTime',
    title: '更新时间',
    width: 120
  },
  {
    key: 'updateUser',
    title: '更新人',
    width: 120
  },
  {
    key: 'isEnable',
    title: '启用状态',
    width: 120,
    fixed: 'right'
  }
];

export const doubleRuleColumns = [
  {
    key: 'areaName',
    title: '省份',
    width: 120
  },
  {
    key: 'areaTypeName',
    title: '所在区域',
    width: 120
  },
  {
    key: 'agcAdjustRateCoefficient',
    title: 'AGC调节速率系数(%)',
    width: 160
  },
  {
    key: 'agcAdjustRateThreshold',
    title: 'AGC调节速率阈值(MW)',
    width: 160
  },
  {
    key: 'agcAdjustAccuracyCoefficient',
    title: 'AGC调节精度系数(%)',
    width: 180
  },
  {
    key: 'agcAdjustRateDuration',
    title: 'AGC调节速率未达标持续时长阈值(min)',
    width: 250
  },
  {
    key: 'agcOperationRateDuration',
    title: 'AGC投运率持续时长阈值（min）',
    width: 250
  },
  {
    key: 'avcAdjustThreshold',
    title: 'AVC调节阈值（kar）',
    width: 160
  },
  {
    key: 'avcAdjustRateDuration',
    title: 'AVC调节未达标持续时长阈值(min)',
    width: 250
  },
  {
    key: 'avcOperationRateThreshold',
    title: 'AVC投运率持续时长阈值(%)',
    width: 220
  },
  {
    key: 'shortPowerPredictionAccuracyThreshold',
    title: '短期功率预测准确率阈值(%)',
    width: 200
  },
  {
    key: 'superShortPowerPredictionAccuracyThreshold',
    title: '超短期功率预测准确率阈值(%)',
    width: 200
  },
  {
    key: 'middlePowerPredictionAccuracyThreshold',
    title: '中期功率预测准确率阈值(%)',
    width: 200
  },
  {
    key: 'updateTime',
    title: '更新时间',
    width: 160,
    sort: true
  },
  {
    key: 'updateUser',
    title: '更新人',
    width: 120
  }
];
