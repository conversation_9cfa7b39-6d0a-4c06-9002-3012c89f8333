<template>
  <a-row :gutter="24" style="padding:0 16px">
    <a-col :span="24">
      <a-timeline class="margin-t-16">
        <a-timeline style="margin: auto;">
          <template v-for="(activity, index) in activities" >
            <a-timeline-item :key="index">
              <div class="detail_layout_content">
                <span class="process-node ellipsis" :title='activity.stepName'>{{activity.stepName}}</span>
              </div>
              <div class="margin-t-16">
                <div class="content-div ">
                  <div class="label">情况描述：</div>
                  <div class="value">{{ activity.workDesc || '--' }}</div>
                </div>
              </div>
              <div class="margin-t-16">
                <div class="content-div">
                  <div class="label">上传图片：</div>
                  <div class="value">
                    <uploadFile v-if="activity.stepPictureList && activity.stepPictureList.length > 0" :disabled="true"
                      v-model="activity.stepPictureList" listType="picture-card"/>
                    <span v-else>--</span>
                  </div>
                </div>
              </div>
            </a-timeline-item>
          </template>
        </a-timeline>
      </a-timeline>
    </a-col>
  </a-row>
</template>
<script>
import uploadFile from '@/components/com/fileUploadView';
export default {
  props: {
    activities: {
      type: Array,
      default: () => {
        return [];
      }
    }
  },
  components: {
    uploadFile
  }
};
</script>

<style lang="less" scoped>
.detail_layout_content {
  display: inline-flex;
  flex-direction: row;
  line-height: var(--line-height);
  width: 100%;
  .process-node{
    font-size: 14px;
    line-height: 24px;
    flex: 1;
    &.ellipsis {
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      word-wrap: none;
    }
  }
}
.process-node {
  color: var(--zw-text-1-color--default);
}
.content-div {
  display: inline-flex;
  flex-direction: row;
  .label {
    width: 142px;
    padding-right: 8px;
    color: var(--zw-text-1-color--default);
    text-align: right;
    flex: none;
    opacity: 0.7;
  }
  .value {
    color: var(--zw-text-1-color--default);
  }
}
:deep(.ant-timeline-item) {
  min-height: 60px;
}
:deep(.ant-timeline-item-tail){
  top: 15px;
  height: calc(100% - 20px);
  border-color: var(--zw-border-color--default);
}
:deep(.ant-timeline-item-head-blue) {
  background: transparent;
}
</style>
