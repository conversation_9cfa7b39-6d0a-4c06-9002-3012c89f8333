<template>
  <a-modal
    title="新增"
    :visible="visible"
    @ok="handleOk"
    @cancel="handleCancel"
    width="50vw"
    :bodyStyle="{
      height: '400px',
      overflow: 'auto'
    }"
  >
    <a-spin size="small" :spinning="confirmLoading" style="height: 100%">
      <a-form-model
        :model="formData"
        ref="addFormRef"
        labelAlign="left"
        :labelCol="{ style: 'width: 80px' }"
        :wrapperCol="{ style: 'width: calc(100% - 80px)' }"
        :rules="rules"
      >
        <a-row :gutter="24">
          <a-col :span="11">
            <a-form-model-item label="任务类型" prop="taskType">
              <a-select size="default" :disabled="true" v-model="formData.taskType" placeholder="请选择">
                <a-select-option v-for="item in dict.ops_task_type" :key="item.dataValue" :value="item.dataValue">{{
                  item.dataLable
                }}</a-select-option>
              </a-select>
            </a-form-model-item>
          </a-col>
          <a-col :span="11">
            <a-form-model-item label="任务子类" prop="taskSubType">
              <a-select size="default" :disabled="true" v-model="formData.taskSubType" placeholder="请选择">
                <a-select-option v-for="item in dict.ops_task_sub_type" :key="item.dataValue" :value="item.dataValue">{{
                  item.dataLable
                }}</a-select-option>
              </a-select>
            </a-form-model-item>
          </a-col>
        </a-row>
        <a-row :gutter="24" v-for="(item, index) in formData.psaDtoList" :key="index">
          <a-col :span="11">
            <a-form-model-item
              label="电站名称"
              :prop="'psaDtoList.' + index + '.psaName'"
              :rules="{
                required: true,
                message: '此项为必填项',
                trigger: 'blur',
              }"
            >
              <a-input allow-clear :max-length='50' v-model="item.psaName" placeholder="请输入电站名称" />
            </a-form-model-item>
          </a-col>
          <a-col :span="11">
            <a-form-model-item
              label="计划日期"
              :prop="'psaDtoList.' + index + '.planTimeRange'"
              :rules="{
                required: true,
                message: '此项为必填项',
                trigger: 'change',
              }"
            >
              <MonthRange  style="width: 100%" v-model='item.planTimeRange'/>
            </a-form-model-item>
          </a-col>
          <a-col :span="2">
              <span class="operation-btn">
                <a-icon type="plus-circle" @click="addDtoItem" />
                <a-icon type="minus-circle" @click="removeDtoItem(index)" v-if="formData.psaDtoList.length > 1" />
              </span>
          </a-col>
        </a-row>
      </a-form-model>
    </a-spin>
    <template slot="footer">
      <div class="flex-end">
        <throttle-button
          label="取消"
          class="solar-eye-btn-primary-cancel"
          @click="handleCancel"
          :loading="confirmLoading"
        />
        <a-button class="ant-btn-primary" @click="onSubmit" :loading="confirmLoading">确认</a-button>
      </div>
    </template>
  </a-modal>
</template>

<script>
import { insertPreTest } from '@api/isolarErp/taskHall';
import moment from 'moment/moment';
import MonthRange from '@views/experiment/modules/MonthRange.vue';
export default {
  name: 'AddModal',
  components: { MonthRange },
  props: {
    dict: {
      type: Object,
      default: () => ({})
    }
  },
  data () {
    return {
      confirmLoading: false,
      visible: false,
      formData: {
        taskType: '3',
        taskSubType: '13',
        psaDtoList: [{ psaName: null, planTimeRange: [] }]
      },
      rules: {
        taskType: [{ required: true, message: '此项为必填项', trigger: 'change' }],
        taskSubType: [{ required: true, message: '此项为必填项', trigger: 'change' }]
      }
    };
  },
  mounted () {},
  methods: {
    handleOk () {
      this.visible = false;
    },
    handleCancel () {
      this.visible = false;
      Object.assign(this.formData, this.$options.data().formData);
      this.$refs.addFormRef.clearValidate();
    },
    init (visible) {
      this.visible = visible;
    },
    onSubmit () {
      this.$refs.addFormRef.validate((valid) => {
        if (valid) {
          this.confirmLoading = true;
          const psaDtoList = this.formData.psaDtoList.map((item) => ({
            psaName: item.psaName,
            planStartTime: item.planTimeRange[0],
            planEndTime: item.planTimeRange[1]
          }));
          const map = Object.assign({}, this.formData, { psaDtoList });
          insertPreTest(map)
            .then((res) => {
              this.handleCancel();
              this.confirmLoading = false;
              this.$emit('queryPageData');
            })
            .catch(() => {
              this.confirmLoading = false;
            });
        }
      });
    },
    addDtoItem () {
      this.formData.psaDtoList.push({ psaName: null, planTimeRange: [] });
    },
    removeDtoItem (index) {
      this.formData.psaDtoList.splice(index, 1);
    },
    disabledDate (current) {
      return current && current < moment().startOf('day');
    }
  }
};
</script>

<style lang="less" scoped>
:deep(.ant-form-item) {
  display: flex;
}
.operation-btn {
  line-height: 40px;
  height: 40px;
  .anticon:not(:last-child){
    margin-right: 8px;
  }
}
</style>
<style lang='less'>
.plan_date .ant-calendar-range .ant-calendar-footer-extra {
  float: right;
  .picker-close {
    color: #0c64eb;
    cursor:pointer;
    &:hover {
      color: var(--zw-primary-color--default) !important;
    }
  }
}
</style>
