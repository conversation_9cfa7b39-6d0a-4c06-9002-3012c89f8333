import { postAction } from '@/api/manage';
const baseUrl = process.env.VUE_APP_Health_BASE_URL;
const iotUrl = process.env.VUE_APP_IOT_BASE_URL;
// const baseUrl = 'http://192.168.156.240:8080';
// 获取电站及设备类型
const getDeviceTypeList = (params) => postAction(baseUrl + '/pointManagement/deviceTypeList', params);
const getPointList = (params) => postAction(baseUrl + '/pointManagement/pointsByDeviceType', params);
// 获取通信协议版本
const getVersionList = (params) => postAction(baseUrl + '/versionManagement/queryVersion', params);
const getVersionTree = (params) => postAction(baseUrl + '/versionManagement/tree', params);
const getManufacturersList = (params) => postAction(baseUrl + '/versionManagement/queryFactory', params);
const getDeviceModel = (params) => postAction(baseUrl + '/versionManagement/queryModel', params);
const getDeviceVersionSourceList = (params) => postAction(baseUrl + '/versionManagement/deviceVersionSourceList', params);
const getModelListByPage = (params) => postAction(baseUrl + '/versionManagement/modelListByPage', params);
const getPointListByPage = (params) => postAction(baseUrl + '/versionManagement/pointListByPage', params);
const getVersionPointList = (params) => postAction(baseUrl + '/versionManagement/pointList', params);

// 数据质量分析
const stationIntateAndEffectiveList = (params) => postAction(baseUrl + '/qalityAnalysis/stationIntateAndEffectiveList', params);
const modelDataDetail = (params) => postAction(baseUrl + '/qalityAnalysis/modelDataDetail', params);
const stationModelPointTree = (params) => postAction(baseUrl + '/qalityAnalysis/stationModelPointTree', params);
const stationIntateAndEffectiveDetail = (params) => postAction(baseUrl + '/qalityAnalysis/stationIntateAndEffectiveDetail', params);

// 数据洞察
const insightToolsDeviceTypeList = (params) => postAction(baseUrl + '/insightTools/deviceTypeList', params);
const insightToolsDeviceList = (params) => postAction(baseUrl + '/insightTools/deviceList', params);
const insightToolsMorePoint = (params) => postAction(baseUrl + '/insightTools/morePoint', params);
const insightToolsGetDefaultPs = (params) => postAction(baseUrl + '/insightTools/getDefaultPs', params);
const insightToolsInsightAnalysisData = (params) => postAction(baseUrl + '/insightTools/insightAnalysisData', params);
const getMeteo = (params) => postAction(baseUrl + '/healthPowerStation/getMeteo', params);

// 洞察工具新
const viewTemplateList = (params) => postAction(baseUrl + '/insightTools/viewTemplate/list', params); // 洞察工具模板列表
const deleteViewTemplate = (params) => postAction(baseUrl + '/insightTools/viewTemplate/delete', params); // 洞察工具删除模板
export const getIndicatorData = (params) => postAction(baseUrl + '/insightTools/getIndexData', params); // 获取指标图表数据
export const getIndexTableApi = (params) => postAction(baseUrl + '/insightTools/getIndexTable', params); // 获取指标表格数据

export const getIndexTypeApi = (params) => postAction(baseUrl + '/insightTools/getIndexType', params); // 获取指标表格数据

export const getIndexPsListApi = (params) => postAction(baseUrl + '/app/index/psList', params); // 获取电站列表
// 测点管理-遥信修改
const remoteSignalUpdate = (params) => postAction(baseUrl + '/pointManagement/remoteSignalUpdate', params);
// 实时库与报文
const realTimeLibraryList = (params) => postAction(iotUrl + '/iot/msgLibary/realtimeLibraryList', params);
const exportRealtimeLibraryList = (params) => postAction(iotUrl + '/iot/msgLibary/exportRealtimeLibraryList', params);
const getMessageList = (params) => postAction(iotUrl + '/iot/msgLibary/getMessageList', params);
const exportMessageList = (params) => postAction(iotUrl + '/iot/msgLibary/exportMessageList', params);
export {
  getDeviceTypeList,
  getVersionList,
  getVersionTree,
  getManufacturersList,
  getDeviceModel,
  getDeviceVersionSourceList,
  getPointList,
  getModelListByPage,
  getPointListByPage,
  getVersionPointList,
  stationIntateAndEffectiveList,
  modelDataDetail,
  stationModelPointTree,
  stationIntateAndEffectiveDetail,
  insightToolsDeviceTypeList,
  insightToolsDeviceList,
  insightToolsMorePoint,
  insightToolsGetDefaultPs,
  insightToolsInsightAnalysisData,
  getMeteo,
  viewTemplateList,
  deleteViewTemplate,
  remoteSignalUpdate,
  exportRealtimeLibraryList,
  realTimeLibraryList,
  getMessageList,
  exportMessageList,
  baseUrl

};
