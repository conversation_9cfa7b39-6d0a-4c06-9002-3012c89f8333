<!--监控中心头部数据-->
<template>
  <div class="data-header">
    <div class="info-data flex-between">
      <svg-icon iconClass="scale" style="font-size: 62px"></svg-icon>
      <div class="info-data-item" style="width:auto">
        <countTo
          :class="{'info-num': String(dataExchange(headData.ratedPowerCn, 'data')).length < 8&&String(dataExchange(headData.totalCapcityCn, 'data')).length < 8,'info-long':String(dataExchange(headData.ratedPowerCn, 'data')).length >= 8 || String(dataExchange(headData.totalCapcityCn, 'data')).length >= 8}"
          :startVal="0"
          :decimals="2"
          :endVal="dataExchange(headData.ratedPowerCn, 'data')"
          :duration="1000"
        ></countTo>
        <span style="display: inline-block;">/</span>
        <countTo
          :startVal="0"
          :decimals="2"
          :class="{'info-num': String(dataExchange(headData.totalCapcityCn, 'data')).length < 8 && String(dataExchange(headData.ratedPowerCn, 'data')).length < 8,'info-long':String(dataExchange(headData.totalCapcityCn, 'data')).length >= 8 || String(dataExchange(headData.ratedPowerCn, 'data')).length >= 8}"
          :endVal="dataExchange(headData.totalCapcityCn, 'data')"
          :duration="1000"
          ></countTo>
        <p class="info-text">
          装机规模<span>({{dataExchange(headData.ratedPowerCn, 'unit')}}/{{ dataExchange(headData.totalCapcityCn, 'unit') }})</span>
        </p>
      </div>
      <div class="info-data-item" style="width:auto">
        <countTo
          class="info-num"
          :startVal="0"
          :decimals="2"
          :class="{'info-long':String(dataExchange(headData.p370009 , 'data')).length > 9}"
          :endVal="dataExchange(headData.p370009 , 'data')"
          :duration="1000"
        ></countTo>
        <p class="info-text">
          有功功率<span>({{ dataExchange(headData.p370009, 'unit') }})</span>
        </p>
      </div>
    </div>
    <div class="divider"></div>
    <div class="info-data flex-between">
      <svg-icon iconClass="station" style="font-size: 62px"></svg-icon>
      <div class="info-data-item">
        <countTo class="info-num" :class="{'info-long': headData.psCount&& String(headData.psCount).length > 9}" :startVal="0" :decimals="0" :endVal="headData.psCount" :duration="1000"></countTo>
        <p class="info-text">
          {{ '电站数量' }}
          <span>(座)</span>
        </p>
      </div>
      <div class="info-data-item">
        <countTo
          class="info-num"
          :class="{'info-long': String(dataExchange(headData.p370016, 'data')).length > 9}"
          :startVal="0"
          :endVal="dataExchange(headData.deviceCount, 'data')"
          :duration="1000"
        ></countTo>
        <p class="info-text">
          {{ '变流器数量' }}
          <span>({{ dataExchange(headData.deviceCount, 'unit') }})</span>
        </p>
      </div>
    </div>

    <div class="divider"></div>
    <div class="info-data flex-between">
      <svg-icon iconClass="dailyStore" style="font-size: 62px"></svg-icon>
      <div class="info-data-item">
        <countTo
          class="info-num"
          :class="{'info-long':String(dataExchange(headData.p370016, 'data')).length > 9}"
          :decimals="4"
          :startVal="0"
          :endVal="dataExchange(headData.p370016, 'data')"
          :duration="1000"
        ></countTo>
        <p class="info-text">
          日充电量<span>({{ dataExchange(headData.p370016, 'unit') }})</span>
        </p>
      </div>
      <div class="info-data-item">
        <countTo
          class="info-num"
           :class="{'info-long': String(dataExchange(headData.p370017, 'data')).length > 9}"
          :decimals="4"
          :startVal="0"
          :endVal="dataExchange(headData.p370017, 'data')"
          :duration="1000"
        ></countTo>
        <p class="info-text">
          日放电量<span>({{ dataExchange(headData.p370017, 'unit') }})</span>
        </p>
      </div>
    </div>

    <div class="divider"></div>
    <div class="info-data flex-between">
      <svg-icon iconClass="totalStore" style="font-size: 62px"></svg-icon>
      <div class="info-data-item">
        <countTo
          class="info-num"
          :class="{'info-long':String(dataExchange(headData.p370014, 'data')).length > 9}"
          :decimals="4"
          :startVal="0"
          :endVal="dataExchange(headData.p370014, 'data')"
          :duration="1000"
        ></countTo>
        <p class="info-text">
          总充电量<span>({{ dataExchange(headData.p370014, 'unit') }})</span>
        </p>
      </div>
      <div class="info-data-item">
        <countTo
          class="info-num"
          :class="{'info-long':String(dataExchange(headData.p370015, 'data')).length > 9}"
          :decimals="4"
          :startVal="0"
          :endVal="dataExchange(headData.p370015, 'data')"
          :duration="1000"
        ></countTo>
        <p class="info-text">
          总放电量<span>({{ dataExchange(headData.p370015, 'unit') }})</span>
        </p>
      </div>
    </div>
  </div>
</template>

<script>
import countTo from 'vue-count-to'; // 数值滚动插件

export default {
  props: {
    headData: {
      type: Object,
      default: () => {
        return {
          deviceCount: '0,台', // 变流器数量
          p370014: '0,Wh', // 总充电量
          p370015: '0,Wh', // 总放电量
          p370016: '0,Wh', // 日充电量
          p370017: '0,Wh', // 日放电量
          psCount: 0, // 电站数量
          ratedPowerCn: '0,MW', // 有功功率
          totalCapcityCn: '0,W', // 装机容量
          p370009: '0,W'
        };
      }
    }
  },
  components: { countTo },
  data () {
    return {};
  },
  created () {},
  computed: {
    pngSuffix () {
      return this.isSupportWebp ? '.webp' : '.png';
    }
  },
  methods: {
    dataExchange (data, type) {
      if (type === 'unit') {
        return data.split(',')[1];
      } else {
        return Number(data.split(',')[0]);
      }
    },
    dynamicDecimals (unit) {
      if (unit == 'kW' || unit == 'kWh') {
        return 3;
      } else {
        return 4;
      }
    }
  }
};
</script>

<style lang="less" scoped>
.data-header {
  height: 96px;
  box-sizing: border-box;
  margin: 12px 0 16px;
  display: flex;
  display: -webkit-box;
  justify-content: flex-start;
  align-items: center;
  color: var(--zw-text-1-color--default);
  .info-data {
    background: var(--zw-card-bg-color--default);
    width: calc((100% - 3 * 16px) / 4);
    height: 96px;
     border-radius:6px;
    background-size: contain;
    display: flex;
    position: relative;
    padding: 0 24px;
    margin-right: 16px;
    .info-data-item {
      text-align: left;
      width: calc((100% - 120px) / 2);
      .info-num {
        font-size: 26px;
        font-weight: 600;
        margin: 0;
        color: var(--zw-text-1-color--default);
      }
      .info-long {
        display: inline-block;
        width: 98%;
        font-size: 18px;
        overflow-wrap: break-word;
      }
      .info-text {
        font-size: 14px;
        font-weight: 400;
        margin: 0;
        color: var(--zw-text-1-color--default);
      }
    }
  }
}
</style>
