<template>
  <div>
    <span class='item-content-title'>清洗验收确认单&清洗总结报告</span>
    <a-form-model
      :model="form"
      ref="SafeReport"
      :labelCol="{ style: 'width: 100px' }"
      :wrapperCol="{ style: 'width: calc(100% - 100px)' }"
      :rules='rules'
      class='mt-24'
    >
      <a-row>
        <a-col :span='8'>
          <a-form-model-item label="实际费用(元)" prop="orderRealCost" :rules="{ required: true, message: '此项为必填项', }">
            <a-input-number :disabled='isDisabled' :max="999999999999.99" :min="0.01" :precision="2" placeholder='请填写实际费用' v-model="form.orderRealCost" style="width: 100%" />
            <OrderRealCostCheck :disabled='isDisabled' :count='form.orderRealCost' />
          </a-form-model-item>
        </a-col>
      </a-row>
      <a-row :gutter="24">
        <a-col :span='16'>
          <a-form-model-item label="情况描述" prop="conditionRemark">
            <zw-textarea
              :disabled="isDisabled"
              v-model="form.conditionRemark"
              :max-length="255"
              :auto-size="{ minRows: 4, maxRows: 6}"
              placeholder="请输入相关描述" show-word-limit/>
          </a-form-model-item>
        </a-col >
        <a-col :span='24'>
          <a-form-model-item  label="上传确认单" prop="checkFiveOneFileList" >
            <uploadFileView
              v-model="form.checkFiveOneFileList"
              :maxNum="5"
              :maxSize="10"
              tip="最多上传5个文件，且上传的附件最大不超过10MB"
              :multiple="true"
              @set='checkPickure("checkFiveOneFileList")'
              :disabled="isDisabled"
            />
          </a-form-model-item>
        </a-col>
        <a-col :span='24' v-if="componentData.otherSts === '3'">
          <a-form-model-item label="清洗报告">
            <FileUpload
              v-model="form.cleanReportFileList"
              tip="可下载清洗执行报告，按需完善清洗效果分析后提交"
              :disabled="isDisabled"
              upload-text='上传' />
            <template v-if='!isDisabled'>
              <a v-if='!downloadLoading' class='download-report' @click='downloadCleanReport'>下载报告</a>
              <a-spin v-else class='download-report' size='small'></a-spin>
            </template>
          </a-form-model-item>
        </a-col>
      </a-row>
    </a-form-model>
  </div>
</template>

<script>
import uploadFileView from '@/components/com/fileUploadView';
import OrderRealCostCheck from '@views/distributed/orderHall/modules/OrderRealCostCheck.vue';
import FileUpload from '@views/distributed/orderHall/modules/upload/FileUpload.vue';
import DownBigExcel from '@/mixins/downBigExcel';
import { gatOrderDetail } from '@/api/distributed/orderHall';
export default {
  mixins: [DownBigExcel],
  components: { OrderRealCostCheck, uploadFileView, FileUpload },
  props: {
    componentData: {
      type: Object,
      default: () => ({})
    },
    isDisabled: {
      type: Boolean,
      default: false
    }
  },
  data () {
    return {
      form: {
        conditionRemark: undefined,
        checkFiveOneFileList: undefined,
        orderRealCost: undefined,
        cleanReportFileList: undefined
      },
      rules: {
        conditionRemark: [
          { required: true, message: '此项为必填项', trigger: 'change' },
          { max: 255, message: '最多允许输入255个字符', trigger: 'blur' }
        ],
        checkFiveOneFileList: [
          { required: true, message: '请上传文件', trigger: 'change', type: 'array' }
        ]
      },
      downloadLoading: false
    };
  },
  mounted () {
    const cleanDetailVo = this.componentData.cleanDetailVo || {};
    Object.assign(this.form, {
      conditionRemark: this.componentData.conditionRemark,
      checkFiveOneFileList: cleanDetailVo.checkFiveOneFileList,
      orderRealCost: this.componentData.orderRealCost,
      cleanReportFileList: cleanDetailVo.cleanReportFileList
    });
  },
  methods: {
    checkPickure (propertyName) {
      this.$nextTick(() => {
        this.$refs.SafeReport.validateField(propertyName);
      });
    },
    validateForm () {
      let result;
      this.$refs.SafeReport.validate(valid => {
        result = valid;
      });
      return result;
    },
    // 下载报告需要校验实际费用和清洗日志，如果没有一条已完成的清洗日志，则报错提示
    downloadCleanReport () {
      this.$refs.SafeReport.validateField('orderRealCost', async errMsg => {
        if (!errMsg) {
          const { orderRealCost } = this.form;
          const { id } = this.componentData;
          this.downloadLoading = true;
          const orderDetail = await gatOrderDetail({ businessesId: id });
          const { cleanDetailVo: { opsWorkOrderCleanDateVoList = [] } } = orderDetail.result_data;
          if (opsWorkOrderCleanDateVoList && opsWorkOrderCleanDateVoList.length && opsWorkOrderCleanDateVoList.some(item => item.isCommit)) {
            const map = { orderRealCost, orderId: id };
            this.exportExcelEvent('downloadLoading', 'poi-tl-100003', map, 'docx');
          } else {
            this.downloadLoading = false;
            this.$message.error('日志未填写，无法自动生成报告，请先填写日志');
          }
        }
      });
    }
  }
};
</script>

<style lang='less' scoped>
  .download-report{
    position: absolute;
    top: 0;
    left: 120px;
    color: #5BAAEDFF;
    &:hover{
      color: var(--zw-primary-color--default);
    }
  }
</style>
