<template>
  <div class="drawer-form-com device-info">
    <div  class="drawer-form-content" :style="{ paddingTop: '0', height: 'calc(100 % -55px)' }">
      <a-spin :spinning="loading">
        <a-form-model :model="form" :rules="rules" ref="form" :labelCol="{ style: 'width:140px' }"
          :wrapperCol="{ style: 'width:calc(100% - 140px)' }" :class="type =='4' ? 'detail-page' : ''">
          <template>
            <a-row>
              <a-col :span='24' >
                <div class="order-dispose first-title">
                  <div class="title-box">
                    <span class="before"></span>
                    <span>基本信息</span>
                  </div>
                </div>
              </a-col>
              <a-col :xs='24' :sm='12' :md='8'>
                <a-form-model-item label="设备型号" prop="deviceModel" :colon="type == '4'">
                  <a-input v-if="type === '1' || type === '2'" v-model="form.deviceModel" @blur="form.deviceModel = $trim($event)" placeholder="请输入设备型号" allow-clear :maxLength="50" style="width:100%;" />
                  <span v-else>{{ form.deviceModel }}</span>
                </a-form-model-item>
              </a-col>
              <a-col :xs='24' :sm='12' :md='8' >
                <a-form-model-item label="厂家名称" prop="makerId" :colon="type == '4'">
                  <DeviceMakerList :topIds="[form.makerId]" v-if="type === '1' || type === '2'" v-model="form.makerId"  allow-clear/>
                  <span v-else>{{ form.maker }}</span>
                </a-form-model-item>
              </a-col>
              <a-col :xs='24' :sm='12' :md='8' >
                <a-form-model-item label="设备类型" prop="deviceTypeId" :colon="type == '4'">
                    <a-cascader v-if="type === '1' || type === '2'" :show-search="{ filter }" v-model="form.deviceTypeId" style="width: 100%;"
                   :options="deviceList" @change="changeMaker" :allow-clear="false" placeholder="请选择设备类型" />
                   <span v-else>{{ form.deviceTypeLongName }}</span>
                </a-form-model-item>
              </a-col>
              <a-col :span='24' >
                <div class="order-dispose" style="margin-bottom: 16px">
                  <div class="title-box">
                    <span class="before"></span>
                    <span>技术参数</span>
                  </div>
                </div>
              </a-col>
              <a-col :span="24" style="margin-bottom: 8px;">
               <div class="add-table" >
                <div @click="addTechnicalTable" v-if="type === '1' || type === '2'">
                  <a-icon type="plus-circle" />
                  <span class="add-text" >添加</span>
                </div>
               </div>
                <vxe-table class="my-table" v-if="isShowTable" :data="technicalTableList"  resizable show-overflow
                  highlight-hover-row ref="xTable">
                  <vxe-table-column title='参数名称' field="itemData">
                    <template v-slot="{ row, rowIndex }">
                      <a-select
                        v-if="type === '1' || type === '2'"
                        show-search
                        :filter-option="filterOption"
                        option-filter-prop="children"
                        v-model="row.dataValue"
                        @change="changeTable(row.dataValue, rowIndex)"
                      >
                        <a-select-option
                          v-for="item in dictMap.device_type_set_item"
                          :key="item.dataValue"
                          :value="item.dataValue"
                          :title="item.dataLable"
                          allowClear
                        >
                          {{ item.dataLable }}
                        </a-select-option>
                      </a-select>
                      <span v-else>{{ row.itemLabel }}</span>
                    </template>
                  </vxe-table-column>
                  <vxe-table-column title='参数值' field="itemDataValue">
                    <template v-slot="{ row }">
                       <a-input v-if="type === '1' || type === '2'" v-model="row.itemDataValue" placeholder='输入多个值用","隔开' allow-clear :maxLength="50" style="width:100%;" />
                        <span v-else>{{ row.itemDataValue }}</span>
                    </template>
                  </vxe-table-column>
                  <vxe-table-column v-if="type === '1' || type === '2'"  title="操作" fixed="right" :resizable="false" width="100">
                    <template v-slot="{ rowIndex }">
                      <span title="删除" @click="delTable(rowIndex)" >
                        <svg-icon class="operation-icon" iconClass="delete-device" ></svg-icon>
                      </span>
                    </template>
                  </vxe-table-column>
                </vxe-table>
              </a-col>
              <a-col :span='24' >
                <div class="order-dispose">
                  <div class="title-box">
                    <span class="before"></span>
                    <span>技术资料</span>
                  </div>
                </div>
              </a-col>
              <div class="technical" v-for="(item, index) in technicalList" :key="index">
                <a-col :span='24' >
                  <a-col :span='24' >
                    <div class="technical-select">
                      <a-form-model-item label="资料类型" class="dept-info" :class="type != '4' ? 'no-colon' : ''">
                        <a-select v-if="type === '1' || type === '2'" size="default" v-model="item.technicalDataType" style="width: 500px" @change="changeData(item.technicalDataType, index)">
                          <a-select-option v-for="list in dictMap.device_model_info_type" :key="list.dataValue" :value="list.dataValue">{{list.dataLable}}</a-select-option>
                        </a-select>
                        <span v-else>{{ item.technicalDataTypeLabel || '--' }}</span>
                      </a-form-model-item>
                      <svg-icon v-if="technicalList.length > 1 && index !== technicalList.length - 1 && type !=='4'" class="operation-icon del-operation" @click="delData(index)" iconClass="delete-device" ></svg-icon>
                      <span  v-if="index === technicalList.length - 1 && type !=='4'" @click="addMore" class="add-more del-operation">+添加更多资料</span>
                    </div>
                    <a-form-model-item label="附件" class="attachment" :class="type != '4' ? 'no-colon' : ''">
                      <fileUploadView  v-model="item.annexList" :multiple="true" :disabled="type === '4'" :maxNum="5" :maxSize="20"  accept=".jpg,.png,.jpeg,.bmp,.pdf" tip="最多上传5个，单个不能超过20MB，支持jpg、jpeg、png、bmp、pdf格式">
                    </fileUploadView>
                    <span v-if="item.annexList.length === 0 && type == '4'">--</span>
                    </a-form-model-item>
                  </a-col>
                </a-col>
              </div>
            </a-row>
          </template>
        </a-form-model>
      </a-spin>
    </div>
    <div v-if="type === '1' || type === '2'" class="drawer-form-foot">
      <a-button class='solar-eye-btn-primary-cancel' @click="cancel('tips')">取消</a-button>
      <throttle-button label="提交" :loading="loading" @click="submitClick()" />
    </div>
    <div v-if="type === '4'" class="drawer-form-foot">
      <a-button class='solar-eye-btn-primary-cancel' @click="cancel">返回</a-button>
      <throttle-button v-has="'type:edit_health'" label="编辑" :loading="loading" @click="editInfo" />
    </div>
  </div>
</template>

<script>
import Sortable from 'sortablejs';
import disposeTree from '../disposeDeviceTree';
import { getComDeviceTypeTree } from '@/api/common/common';
import {
  listParam,
  saveDeviceModel,
  getDeviceModelDetail,
  editDeviceModel
} from '@/api/device/type';
import initDict from '@/mixins/initDict';
import DeviceMakerList from './DeviceMakerList';
export default {
  props: {
    // type 新增：1 编辑：2   查看：4
    type: {
      type: String,
      default: ''
    }
  },
  mixins: [initDict],
  components: {
    DeviceMakerList
  },
  data () {
    return {
      isShowTable: true,
      isFromDetail: false, // 编辑是否来自详情
      id: '', // 详情和编辑id
      technicalDataList: [], // 技术参数字典列表
      deviceList: [], // 设备类型
      technicalList: [
        { technicalDataType: '', annexList: [] }
      ],
      // 技术参数table列表
      technicalTableList: [
      ],
      loading: false,
      form: {
        id: '',
        deviceModel: '',
        makerId: [],
        deviceTypeId: ''
      },
      rules: {
        deviceModel: [{ required: true, message: '请输入设备型号', trigger: 'blur' }],
        makerId: [{ required: true, message: '请选择厂家名称', trigger: 'change' }],
        deviceTypeId: [{ required: true, message: '请选择设备类型', trigger: 'change' }]
      }
    };
  },
  created () {
    // 获取字典
    this.getDictMap('device_type_set_item,device_model_info_type');
  },

  mounted () {
    this.getDeviceList();
  },
  methods: {
    // 详情去编辑
    editInfo () {
      this.$emit('operateInDetails', false, '', '编辑');
      this.isValid('edit');
      this.isFromDetail = true;
      this.init('2', { id: this.id, isTips: true, aaa: 1 });
    },
    // 详情与编辑跳转校验处理
    isValid (data) {
      if (data === 'edit') {
        this.rules = {
          deviceModel: [{ required: true, message: '请输入设备型号', trigger: 'blur' }],
          makerId: [{ required: true, message: '请选择厂家名称', trigger: 'change' }],
          deviceTypeId: [{ required: true, message: '请选择设备类型', trigger: 'change' }]
        };
      } else {
        this.rules = null;
      }
      this.$refs.form.resetFields();
    },
    // 设备型号-详情
    getDeviceModelDetail () {
      getDeviceModelDetail({ id: this.id }).then((res) => {
        if (res.result_code == '1') {
          this.disposeEchoData(res.result_data);
          // 添加全部
          this.loading = false;
        }
      }).catch(() => {
        this.loading = false;
      });
    },
    // 处理回显数据
    disposeEchoData (data) {
      this.form = {
        id: data.id,
        deviceTypeLongName: data.deviceTypeLongName,
        maker: data.maker,
        deviceModel: data.deviceModel,
        makerId: data.makerId,
        deviceTypeId: data.ancestors ? data.ancestors.split(',') : []
      };
      // 设备类型转为数字类型
      this.form.deviceTypeId = this.form.deviceTypeId.map(item => Number(item));
      if (data.paramList && data.paramList.length) {
        this.technicalTableList = data.paramList.map(item => {
          return {
            dataValue: item.itemData,
            itemLabel: item.itemLabel,
            itemDataValue: item.itemDataValue
          };
        });
      }
      if (data.technicalInformationList && data.technicalInformationList.length) {
        this.technicalList = data.technicalInformationList;
      }
    },
    // 删除技术参数
    delTable (index) {
      this.$confirm({
        title: '提示',
        content: '确定删除此参数？',
        okText: '确定',
        cancelText: '取消',
        onOk: () => {
          this.technicalTableList.splice(index, 1);
        },
        onCancel () {}
      });
    },
    // 删除资料类型
    delData (index) {
      this.$confirm({
        title: '提示',
        content: '确定删除此资料类型？',
        okText: '确定',
        cancelText: '取消',
        onOk: () => {
          this.technicalList.splice(index, 1);
        },
        onCancel () {}
      });
    },
    // 判断选择的资料类型是否重复 重复置空
    changeData (value, index) {
      // 切换清空附件
      this.$set(this.technicalList[index], 'annexList', []);
      let technicalTableListTemplt = this.technicalList.concat();
      technicalTableListTemplt.splice(index, 1);
      let valueList = technicalTableListTemplt.map(item => item.technicalDataType);
      if (valueList.includes(value)) {
        this.$message.warning('所选资料类型已存在，请重新选择');
        this.$set(this.technicalList[index], 'technicalDataType', '');
      }
    },
    // 添加更多资料
    addMore () {
      let isSelect = true;
      this.technicalList.map(item => {
        if (!item.technicalDataType) {
          isSelect = false;
        }
      });
      if (isSelect) {
        let obj = {
          technicalDataType: '',
          annexList: []
        };
        this.technicalList.push(obj);
      } else {
        this.$message.warning('请先选择资料类型后添加');
      }
    },
    // 判断选择的技术参数是否重复 重复置空
    changeTable (value, index) {
      let technicalTableListTemplt = this.technicalTableList.concat();
      technicalTableListTemplt.splice(index, 1);
      let valueList = technicalTableListTemplt.map(item => item.dataValue);
      if (valueList.includes(value)) {
        this.$message.warning('所选参数名称已存在，请重新选择');
        this.$set(this.technicalTableList[index], 'dataValue', '');
      }
    },
    // 添加技术参数
    addTechnicalTable () {
      if (this.form.deviceTypeId) {
        let isSelect = true;
        this.technicalTableList.map(item => {
          if (!item.dataValue) {
            isSelect = false;
          }
        });
        if (isSelect) {
          let obj = {
            dataValue: '',
            itemDataValue: ''
          };
          this.technicalTableList.unshift(obj);
        } else {
          this.$message.warning('请先选择参数名称');
        }
      } else {
        this.$message.warning('请先选择设备类型');
      }
    },
    // 设备类型查询参数
    changeMaker (value) {
      if (value && value.length) {
        let params = JSON.parse(JSON.stringify(value)).pop();
        listParam({ deviceTypeId: params }).then((res) => {
          if (res.result_code == '1') {
          // 添加全部
            this.technicalTableList = res.result_data || [];
          }
        }).catch(() => {});
      }
    },
    // 获取设备类型列表
    getDeviceList (type) {
      getComDeviceTypeTree({ deviceStatus: '1' }).then((res) => {
        if (res.result_code == '1' && res.result_data.length > 0) {
          // 添加全部
          this.deviceList = disposeTree.formatDeviceTreeData(res.result_data);
        } else {
          this.deviceList = [];
        }
      });
    },
    rowDrop () {
      this.$nextTick(() => {
        const Tbody = this.$refs.xTable;
        const _this = this;
        Sortable.create(Tbody.$el.querySelector('.body--wrapper>.vxe-table--body tbody'), {
          // 拖拽结束后执行的方法  _this.edit_list是表格数据
          onEnd ({ newIndex, oldIndex }) {
            _this.technicalTableList.splice(newIndex, 0, _this.technicalTableList.splice(oldIndex, 1)[0]);
            var newArray = _this.technicalTableList.slice(0);
            _this.technicalTableList = [];
            _this.$nextTick(function () {
              _this.technicalTableList = newArray;
            });
          }
        });
      });
    },

    // 弹窗打开时初始化数据 type 新增：1 编辑：2   查看：4
    init (type, data) {
      if (data && data.isBackDetail === false) {
        this.isFromDetail = false;
      }
      this.isShowTable = false;
      this.editable = data.editable;
      this.id = data.id || '';
      this.form = {
        id: '',
        deviceModel: '',
        makerId: [],
        deviceTypeId: ''
      };
      this.type = type;
      this.afterEvent();
      switch (type) {
        case '4':
          return '详情';
        case '1':
          return '新增';
        case '2':
          return '编辑';
      }
    },
    filterOption (input, option) {
      return (
        option.componentOptions.children[0].text.toLowerCase().indexOf(input.toLowerCase()) >= 0
      );
    },
    // 切换抽屉时动画结束后的回调，页面初始化init函数的异步请求须放在这里调用，否则可能会会导致抽屉侧滑出来时卡死
    afterEvent () {
      this.$nextTick(() => {
        // 详情到编辑后再返回详情技术参数可以拖拽 临时修复方案
        this.isShowTable = true;
        if (this.type != '4') {
          this.rowDrop();
        }
        if (this.type === '2' || this.type === '4') {
          if (this.type === '4') {
            this.isValid();
          }
          if (this.type === '2') {
            this.isValid('edit');
          }
          this.getDeviceModelDetail();
        }
      });
    },
    // 保存点击事件
    submitClick () {
      this.loading = true;
      this.technicalTableList.map(item => {
        if (item.hasOwnProperty('itemDataValue')) {
          item.itemDataValue = item.itemDataValue.trim();
        }
        return item;
      });
      this.$refs['form'].validate(valid => {
        if (valid) {
          // 校验 参数名称和参数值是否都填写
          let technicalTableListTemplt = [];
          let isAllFillout = true;
          if (this.technicalTableList && this.technicalTableList.length) {
            this.technicalTableList.map(item => {
              if (item.dataValue && item.itemDataValue) {
                technicalTableListTemplt.push({
                  itemData: item.dataValue,
                  itemDataValue: item.itemDataValue
                });
              } else {
                isAllFillout = false;
              }
            });
          }
          if (!isAllFillout) {
            this.$message.warning('请填写技术参数名称或参数值');
            this.loading = false;
            return;
          }
          // 技术资料  上传图片 资料类型 互相必填
          let technicalListTemplt = JSON.parse(JSON.stringify(this.technicalList));
          this.technicalList.map(item => {
            if ((item.technicalDataType && !(item.annexList && item.annexList.length
            )) || (!item.technicalDataType && item.annexList && item.annexList.length)) {
              isAllFillout = false;
            }
          });
          if (!isAllFillout) {
            this.$message.warning('请选择资料类型或附件');
            this.loading = false;
            return;
          }
          // let params = Object.assign({}, this.form);
          let params = JSON.parse(JSON.stringify(this.form));
          params.deviceTypeId = params.deviceTypeId.pop();
          params.technicalInformationList = technicalListTemplt;
          params.paramList = technicalTableListTemplt;
          let postName = this.type === '1' ? saveDeviceModel : editDeviceModel;
          postName(params).then((res) => {
            if (res.result_code == '1' && res.result_data.length > 0) {
              this.$message.success('保存成功');
              // 添加全部
              this.loading = false;
              this.cancel();
            }
          }).catch(() => {
            this.loading = false;
          });
        } else {
          this.loading = false;
        }
      });
    },
    // 关闭弹窗回调方法
    cancel (data) {
      if (this.isFromDetail && data !== 'tips') {
        // 去详情
        this.$emit('operateInDetails', false, '', '详情');
        this.isValid();
        this.init('4', { id: this.id });
        this.isFromDetail = false;
      } else if (data === 'tips') {
        this.$confirm({
          title: '数据尚未保存，是否确定关闭?',
          okText: '确定',
          cancelText: '取消',
          onOk: () => {
            if (this.isFromDetail) {
              // 去详情
              this.$emit('operateInDetails', false, '', '详情');
              this.isValid();
              this.init('4', { id: this.id });
              this.isFromDetail = false;
            } else {
              this.$emit('cancel');
              this.reset();
            }
          }
        });
      } else {
        this.$emit('cancel');
        this.reset();
        this.loading = false;
      }
    },
    // 表单重置
    reset () {
      if (this.type != '4') {
        this.$refs['form'].resetFields();
        this.$refs['form'].clearValidate();
      }
    }
  }
};
</script>

<style lang="less" scoped>
.link-text {
  color: #1366EC;
  cursor: pointer;
  &:hover {
    color: #428AFD;
  }
}
.technical {
  .technical-select {
    display: flex;
    .operation-icon, .add-more {
      margin-left: 20px;
      margin-top: 8px;
      color: #666;
    }
    .add-more {
      color: #1890FF;
      cursor: pointer;
      font-size: 14px;
    }
  }
}
:deep(.ant-form-item) {
  width: 100%;
  display: inline-flex;
  min-height: 59px;
  margin-bottom: 0;
}
:deep(.technical-select .ant-form-item) {
  width: 640px;
}
.device-info .detail_layout_content .left {
  text-align: left !important;
}
.device-info .detail_layout .title, .title-box {
  padding-top: 16px;
  padding-bottom: 8px;
}
.first-title {
  .title-box {
    padding-top: 16px;
  }
}
.device-info {
  overflow: hidden;

  .drawer-form-content {
    height: calc(100% - 55px);

    &.detalis {
      height: calc(100% - 110px);
    }
  }
}

:deep(.upload-img .ant-upload-list-picture-card .ant-upload-list-item-actions .anticon-eye-o) {
  font-size: 18px !important;
  padding-right: 0;
}

:deep(.line_height) {
  display: flex;
  padding-bottom: 16px;

  .left {
    width: 124px;
    text-align: right;
    margin-right: 8px;
    font-weight: 600;
    color: #666;
  }

  .right {
    color: rgba(0, 0, 0, 0.65)
  }
}

:root[data-theme='dark'] .device-info.table-info .vxe-body--row:nth-child(even) {
  background-color: rgba(0, 0, 0, 0) !important;

  &:hover {
    background-color: rgba(66, 74, 85, 0.15) !important;

  }
}
:deep(.detail_layout_content .left) {
  text-align: left !important;
}
:deep(.detail_layout .title, .title-box) {
  padding-top: 40px;
  padding-bottom: 24px;
  &:first-child {
    padding-top: 24px;
  }
}
.dept-info {
  .search-item {
    margin-top: 2px;
  }
}
.add-table {
  display: flex;
  color: var(--zw-primary-color--default);
  font-size: 16px;
  align-items: center;
  justify-content: flex-end;
  & > div {
    display: flex;
    align-items: center;
    margin-bottom: 20px;
    cursor: pointer;
  }
  .add-text {
    margin-left: 5px;
  }
}
.attachment {
  margin-bottom: 24px;
}
.del-operation:hover {
  color: var(--zw-primary-color--default) !important;
}
:deep(.vxe-body--row.row--hover) {
  cursor: move;
  box-shadow: 2px 2px 8px 2px rgba(21, 60, 104, 0.09) !important;
}
.detail-page {
  :deep(.ant-form-item-label) {
    font-weight: bold !important;
    & > label::after {
      content: ':' !important;
    }
  }
  :deep(.ant-form-item) {
  min-height: 36px !important;
  line-height: 36px !important;
  & > .ant-col, .ant-form-item-control {
    min-height: 36px !important;
    line-height: 36px !important;

    }
  }
}
.dept-info, .attachment {
  :deep(.ant-form-item-label) {
    font-weight: normal !important;
    & > label::after {
      content: ':' !important;
    }
  }
  &.no-colon {
  :deep(.ant-form-item-label) {
    & > label::after {
      content: '' !important;
    }
  }
  }
}
:deep(.ant-form-item) {
  min-height: 40px !important;
}
</style>
