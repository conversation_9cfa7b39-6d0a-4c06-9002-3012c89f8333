<template>
     <a-drawer
      :title="title"
      placement="right"
      :width="389"
      :visible="visible"
      @close="onClose"
    >
   <a-form-model :model="form" :rules="rules"  ref="ruleForm" :label-col="{ span: 5 }" :wrapper-col="{ span: 16 }">
    <a-form-model-item label="测点系数">
       <a-input-number
        placeholder="请输入"
        type="number"
        :min="0.000000001"
        :max="999999999"
        step="0.000001"
        v-model="form.coefficient"
        style="width:100%"
      />
    </a-form-model-item>
    <a-form-model-item label="测点取反">
      <a-input
        placeholder="请输入值为0或1的数字"  @change="(e)=>onChangeEvent(e,'reverse')"
         v-model="form.reverse"
      />
    </a-form-model-item>
    <a-form-model-item label="偏移量">
      <a-input-number
        placeholder="请输入"
        type="number"
        :min="0.000000001"
        :max="999999999"
        step="0.000001"
        v-model="form.excursion"
        style="width:100%"
      />
    </a-form-model-item>
    <a-form-model-item :wrapper-col="{ span: 12, offset: 7 }" style="margin-top: 100px;">
      <throttle-button title="取消" label='取消' @click="onClose" style="margin-right: 20px;" class="solar-eye-btn-primary-cancel"/>
      <throttle-button title="确认" @click="handleSubmit" label='确认' />
    </a-form-model-item>
   </a-form-model>
    </a-drawer>
</template>

<script>
import { listMixin } from './../mixins';
import {
  configPointParam
} from '@/api/health/getPointConfig.js';
export default {
  mixins: [listMixin],
  props: {
    parentId: {
      type: Array,
      default: () => []
    },
    parentKey: {
      type: Array,
      default: () => []
    }
  },
  data () {
    return {
      visible: false,
      title: '批量编辑',
      formLayout: 'horizontal',
      form: {
        excursion: 0,
        coefficient: 1,
        reverse: 0
      },
      rules: {}
    };
  },
  methods: {
    // 表单提交
    handleSubmit () {
      let that = this;
      this.$refs.ruleForm.validate(valid => {
        if (valid) {
          let params = Object.assign({}, that.form, {
            pointMappingIdList: that.parentId,
            originalPsKeyList: that.parentKey
          });
          configPointParam(params).then(res => {
            if (res.result_code == '1') {
              that.onClose();
            }
          }).catch(() => {
          });
        } else {
          console.log('error submit!!');
          return false;
        }
      });
      // this.form.validateFields((err, values) => {
      //   if (!err) {
      //     that.configParamsTab4.coefficient = values.coefficient;
      //     that.configParamsTab4.reverse = values.reverse;
      //     that.configParamsTab4.excursion = values.excursion;
      //     that.configParamsTab4.pointMappingIdList = that.parentId;
      //     that.configParamsTab4.originalPsKeyList = that.parentKey;

      //   }
      // });
    },
    onChangeEvent (ev, name) {
      if (this.form[name] != 0 && this.form[name] != 1) {
        this.form[name] = '';
      }
    },
    showDrawer () {
      this.visible = true;
    },
    onClose () {
      this.form = {
        excursion: 0,
        coefficient: 1,
        reverse: 0
      };
      this.$refs.ruleForm.resetFields();
      this.$emit('cancel');
    }
  }
};
</script>
