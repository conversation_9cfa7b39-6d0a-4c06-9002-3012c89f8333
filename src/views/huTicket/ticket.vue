<!-- 两票管理 -->
<template>
  <div id="ticketPage">
    <a-spin :spinning="loading">
      <div id="ticketSearchArea" class="solar-eye-search-model">
        <a-row :gutter="24" class="solar-eye-search-content">
          <a-col :span="24">
            <div class="search-item" style="height: unset;">
              <span class="search-label">流程状态</span>
              <div>
                <a-checkbox @change="checkedAllClick" v-model="checkedAll">全部({{countAll}})</a-checkbox>
                <a-checkbox-group v-model="allStatusList" @change="statusClick">
                  <a-checkbox value="1">未提交({{taskStatus0}})</a-checkbox>
                  <a-checkbox value="2">处理中({{taskStatus1}})</a-checkbox>
                  <a-checkbox value="3">已完成({{taskStatus2}})</a-checkbox>
                  <a-checkbox value="4">已作废({{taskStatus3}})</a-checkbox>
                </a-checkbox-group>
              </div>
            </div>
          </a-col>
          <a-col :xxl="12" :xl="16" :md="24">
            <role-tree-select @change="roleTreeChange"></role-tree-select>
          </a-col>
          <a-col :xxl="6" :xl="8" :md="12">
            <div class="search-item">
              <span class="search-label">业主</span>
              <a-select v-model="seachData.ownerId" :showSearch="true"  option-filter-prop="children" :filter-option="filterOption"
                        allow-clear size="default" placeholder="请选择业主" style="width:100%;">
                <a-select-option v-for="o in owners" :key="o.id" :value="o.id">
                  {{ o.ownerName }}
                </a-select-option>
              </a-select>
            </div>
          </a-col>
          <template v-if="toggleSearchStatus">
            <a-col :xxl="6" :xl="8" :md="12">
              <div class='search-item'>
                <span class='search-label'>运维商</span>
                <a-select allowClear showSearch
                          placeholder='请选择'
                          mode="multiple"
                          :filter-option="true"
                          optionFilterProp='children'
                          v-model='seachData.deptIds'
                          :maxTagCount='1' :maxTagTextLength='32'
                          @change='getOrderMaintenance'
                >
                  <a-select-option v-for='data in maintenance' :key='data.deptId' :value='data.deptId' :title='data.maintenance'>
                    {{ data.maintenance }}
                  </a-select-option>
                </a-select>
              </div>
            </a-col>
            <a-col :xxl="6" :xl="8" :md="12">
              <div class="search-item">
                <span class="search-label">填报类别</span>
                <a-select v-model="seachData.reportType" @change="ticketTypeEvent" allow-clear size="default" placeholder="请选择">
                  <a-select-option v-for="item in dictMap.ticket_report_type" :key="item.dataValue" :value="item.dataValue">
                    {{item.dispName}}
                  </a-select-option>
                </a-select>
              </div>
            </a-col>
            <a-col :xxl="6" :xl="8" :md="12">
              <div class="search-item">
                <span class="search-label">两票类型</span>
                <a-select v-model="seachData.ticketType" @change="ticketTypeEvent" allow-clear size="default" placeholder="请选择">
                  <a-select-option v-for="item in dictMap.ticket_type" :key="item.dataValue" :value="item.dataValue">
                    {{item.dispName}}
                  </a-select-option>
                </a-select>
              </div>
            </a-col>
            <a-col :xxl="6" :xl="8" :md="12">
              <div class="search-item">
                <span class="search-label">具体类别</span>
                <a-select v-model="seachData.ticketTypeDtl" :disabled="!kinds.length" allow-clear size="default" placeholder="请选择">
                  <a-select-option v-for="item in kinds" :key="item.dataValue" :value="item.dataValue">
                    {{item.dispName}}
                  </a-select-option>
                </a-select>
              </div>
            </a-col>
            <a-col :xxl="6" :xl="8" :md="12">
              <div class="search-item">
                <span class="search-label">当前环节</span>
                <a-select v-model="seachData.handleType" allow-clear size="default" placeholder="请选择">
                  <a-select-option v-for="item in dictMap.hu_ticket_flow_user" :key="item.dataValue" :value="item.dataValue">
                    {{item.dispName}}
                  </a-select-option>
                </a-select>
              </div>
            </a-col>
            <a-col :xxl="6" :xl="8" :md="12">
              <div class="search-item">
                <span class="search-label">票号</span>
                <a-input v-model="seachData.ticketNum" size="default" @blur="seachData.icketNum = $trim($event)" placeholder="请输入" allow-clear />
              </div>
            </a-col>
            <a-col :xxl="6" :xl="8" :md="12">
              <div class="search-item">
                <span class="search-label">填报时间</span>
                <a-range-picker v-model="times" :placeholder="['选择日期', '选择日期']" format="YYYY-MM-DD" value-format="YYYY-MM-DD" size="default" />
              </div>
            </a-col>
            <a-col :xxl="6" :xl="8" :md="12">
              <div class="search-item">
                <span class="search-label">超时未终结</span>
                <a-select v-model="seachData.isOver" allow-clear size="default" placeholder="请选择">
                  <a-select-option value="1">是</a-select-option>
                </a-select>
              </div>
            </a-col>
          </template>

          <a-col :xxl="4" :xl="8" :md="12">
            <div class="search-item">
              <throttle-button label="查询" @click="pageChange()" />
              <a class="com-color" @click="handleToggleSearch" style="margin-left: 8px">
                {{ toggleSearchStatus ? '收起' : '展开' }}
                <a-icon :type="toggleSearchStatus ? 'up' : 'down'" />
              </a>
            </div>
          </a-col>
        </a-row>
      </div>

      <div class="solar-eye-gap"></div>
      <div class="solar-eye-main-content">
        <div class="operation-btn">
          <throttle-button perms="920110102301" label="删除" :disabled="is_deleted" @click="deletedTicket()" />
          <throttle-button perms="920110102302" label="导出" :disabled="!ticket_data.length" :loading="excel_load" @click="exportExcelEvent('excel_load','100018')" />
          <a-dropdown v-if="showHandle('920110102304,920110102305')">
            <a-menu slot="overlay">
              <a-menu-item key="1" v-if="showHandle('920110102304')" @click="exportExcelEvent('zip_load','200004')" >
                <span>附件打包下载</span>
              </a-menu-item>
              <a-menu-item key="2" v-if="showHandle('920110102305')" @click="modal_visible = true">
                <span>样票下载</span>
              </a-menu-item>
            </a-menu>
            <a-button class="solar-eye-btn-primary"> 下载 <a-icon type="down" /> </a-button>
          </a-dropdown>
        </div>
        <!-- 表格数据 -->
        <vxe-table :data="ticket_data" resizable border align="center" @checkbox-all="selectEvent" @checkbox-change="selectEvent" size="small"
                   :checkbox-config="{showHeader:true, highlight: true}" :height="tableHeight - 24" :seq-config="{startIndex: (page - 1) * size}" ref="ticketTable">
          <vxe-table-column type="checkbox" width="60"></vxe-table-column>
          <vxe-table-column type="seq" width="80px" title="序号"></vxe-table-column>
          <vxe-table-column v-for="item in columnList" :key="item.name" show-overflow="title" :formatter="tabFormatter"
                            :field="item.name" :title="item.comment" :min-width="item.width">
          </vxe-table-column>
          <vxe-table-column title="操作" fixed="right" width="120" align="center" :resizable="false" class-name="fixed-right-column-120">
            <template slot-scope="scope">
              <g-button @detail="showTicketForm('3', scope.row)"
                        @review="ticketPreview(scope.row)" @download="zipDownLoad(scope.row)"
                        :list="[{
                  icon: 'file-text',
                  emit: 'detail',
                  name: '详情',
                  has: true, // 未分配权限 则添加 true,  否则传入权限标识即可
                  show: true // 显示条件
                },{
                  icon: 'file-protect',
                  emit: 'review',
                  name: '票面预览',
                  has: '920110102306', // 未分配权限 则添加 true,  否则传入权限标识即可
                  show: true // 显示条件
                },{
                  icon: 'download',
                  emit: 'download',
                  name: '附件下载',
                  has: '920110102307', // 未分配权限 则添加 true,  否则传入权限标识即可
                  show: scope.row.reportType == '0' // 显示条件
                }]" />
            </template>
          </vxe-table-column>
        </vxe-table>
        <!--分页组件-->
        <page-pagination :pageSize="size" :current="page" :total="total" @size-change="sizeChange" />
      </div>
    </a-spin>
    <!--表单组件-->
    <ticket-form :visible.sync="detailVisible" :row.sync="row" parentId="drawerViewDetail" />
    <!-- 票面预览 -->
    <img-carousel :visible.sync="visible" :items.sync="files" title="票面预览" parentId="drawerViewDetail" />
    <!-- 样票下载 -->
    <a-modal title="样票下载" :visible="modal_visible" @cancel="handleCancel" width="400px">
      <div style="width: 100%;height: 15vh;">
        <span class="search-label">样票</span>
        <a-select size="default" v-model="ticket_type_dtl" mode='multiple' :maxTagCount="1" allowClear placeholder="请选择"
                  style="width: calc(100% - 40px);height: 32px;margin-left: 12px;">
          <a-select-option v-for="item in dictMap.ticket_sample" :key="item.key" :value="item.dispName">
            {{item.dispName}}
          </a-select-option>
        </a-select>
      </div>
      <template slot="footer">
        <div class="modal-footer">
          <throttle-button :loading="ticket_load" :disabled="!ticket_type_dtl || !ticket_type_dtl.length" @click="ticketDownLoad" label="下载" />
        </div>
      </template>
    </a-modal>
    <!--表单组件-->
    <drawer-view ref="drawerForm" @cancel="pageChange" parentId="drawerViewDetail" />
  </div>
</template>

<script>
import moment from 'moment';
import initDict from '@/mixins/initDict';
import imgCarousel from './imgCarousel';
import { getWidth } from '@/utils/util.js';
import { LeftMixin } from '@/mixins/LeftMixin';
import { downloadBizAnnex } from '@/api/isolarErp/employee/employee';
import { getTicketInfo, countTicketSts, deleteTicket, getTicketAnnex, downloadSpecimen, specimenPreviewHu } from '@/api/operations/huTicket';
import { specimenPreview } from '@/api/operations/ticket';
import tableHeight from '@/mixins/tableHeightAndSearchModel';
import downBigExcel from '@/mixins/downBigExcel';
import ticketForm from './ticketForm';
import { getOwner } from '@/api/isolarErp/config/archives';
import { orderMaintenance } from '@/api/operations/orderHall';
export default {
  mixins: [initDict, LeftMixin, tableHeight, downBigExcel],
  name: 'ticket',
  components: {
    ticketForm,
    imgCarousel
  },
  data () {
    return {
      loading: false,
      // 查询参数
      seachData: {
        reportType: undefined,
        ticketType: undefined,
        ticketTypeDtl: undefined,
        handleType: undefined,
        ticketNum: undefined,
        isOver: undefined,
        ownerId: undefined, // 业主单位
        deptIds: undefined
      },
      owners: [],
      maintenance: [],
      times: [],
      kinds: [],
      is_deleted: true,
      excel_load: false,
      zip_load: false,
      ticket_load: false,
      columnList: [
        { name: 'area', comment: '区域', width: 200 },
        { name: 'psaName', comment: '电站名称', width: 200 },
        { name: 'maintenanceCompany', comment: '运维商', width: 200 },
        { name: 'ownerName', comment: '业主', width: 200 },
        { name: 'ticketTypeName', comment: '两票类型', width: 200 },
        { name: 'ticketTypeDtlName', comment: '具体类别', width: 200 },
        { name: 'workContent', comment: '工作内容', width: 200 },
        { name: 'ticketNum', comment: '票号', width: 200 },
        { name: 'reportTypeName', comment: '填报类别' },
        { name: 'planTimeFrom', comment: '计划开始时间', width: 200 },
        { name: 'planTimeTo', comment: '计划结束时间', width: 200 },
        { name: 'isOver', comment: '超时未终结' },
        { name: 'flowUser', comment: '当前环节' },
        { name: 'flowStsName', comment: '流程状态' },
        { name: 'flowStartTime', comment: '流程开始时间', width: 200 },
        { name: 'flowEndTime', comment: '流程结束时间', width: 200 },
        { name: 'createUserName', comment: '填报人' },
        { name: 'createTime', comment: '填报时间', width: 200 }
      ],
      ticket_data: [],
      total: 0,
      page: 1,
      size: 10,
      // 附件预览
      visible: false,
      files: [],
      modal_visible: false,
      ticket_type_dtl: undefined,
      queryMap: {},
      detailVisible: false,
      row: {}
    };
  },
  created () {
    this.tabKind = '1';
    this.allStatusList = this.defAll = ['1', '2', '3', '4'];
    this.getDictMap('ticket_report_type,ticket_type,hu_ticket_flow_user,ticket_type_dtl,ticket_sample');
    this.times = [moment().subtract(1, 'months').format('YYYY-MM').concat('-01'), moment().format('YYYY-MM-DD')];
    this.columnList.forEach(item => {
      if (!item.width) {
        item.width = getWidth(item.comment);
      }
    });
    this.getOwners();
    this.getOrderMaintenance();
  },
  methods: {
    moment,
    // 表-列刷新
    refreshColumn () {
      if (this.$refs.ticketTable) {
        this.$refs.ticketTable.refreshColumn();
      }
    },
    // 查询
    async queryData () {
      this.loading = true;
      let $table = this.$refs.ticketTable;
      if ($table) {
        await $table.clearScroll();
        $table.clearCheckboxRow();
      }
      this.is_deleted = true;
      this.queryParam();
      this.queryMap.size = this.size;
      this.queryMap.curPage = this.page;
      getTicketInfo(this.queryMap).then(res => {
        this.total = res.result_data.total;
        this.ticket_data = res.result_data.rows;

        this.loading = false;
      }).catch(() => {
        this.total = 0;
        this.ticket_data = [];
        this.loading = false;
      });
      this.countTicketStsData();
    },
    //  统计各状态数据量
    countTicketStsData () {
      const self = this;
      this.queryParam();
      this.queryMap.size = this.size;
      this.queryMap.curPage = this.page;
      countTicketSts(this.queryMap).then(res => {
        if (res.result_code == '1' && res.result_data) {
          self.assingnData(res.result_data);
        } else {
          self.assingnData(null);
        }
      }).catch(() => {
        self.assingnData(null);
      });
    },
    // 两票类型change事件
    ticketTypeEvent () {
      this.seachData.ticketTypeDtl = undefined;
      let ticketTypeDtl = this.dictMap.ticket_type_dtl;
      this.getKinds(this.seachData.ticketType, this.seachData.reportType, ticketTypeDtl);
    },
    // 表格数据选中事件
    selectEvent ({ checked, records }) {
      if (records.length) {
        // 选中的填报类别必须全是第三方的才可以删除
        this.is_deleted = !(records.length == records.filter(item => item.reportType == '0').length);
      }
    },
    // 删除数据
    deletedTicket () {
      let self = this;
      self.$confirm({
        title: '是否删除',
        okText: '确定',
        cancelText: '取消',
        centered: true,
        onOk () {
          self.loading = true;
          let map = {
            'id': (self.$refs.ticketTable.getCheckboxRecords()).map(item => { return item.id; }).toString()
          };
          deleteTicket(map).then(res => {
            if (res.result_code == '1') {
              self.loading = false;
              self.$message.success('操作成功');
              self.pageChange();
            }
          }).catch(() => {
            self.loading = false;
          });
        }
      });
    },
    /* 获取业主事件 */
    getOwners () {
      getOwner({}).then(res => {
        this.owners = res.payload;
      }).catch(() => {
        this.owners = [];
      });
    },
    // 获取运维商
    getOrderMaintenance () {
      orderMaintenance({ ifHuComp: false }).then(res => {
        this.maintenance = res.result_data;
      }).catch(() => {
        this.maintenance = [];
      });
    },
    // 导出列表
    async getExportId (loadName, exportBusinessType) {
      this[loadName] = true;
      this.queryParam();
      let params = Object.assign({}, this.queryMap);
      params.exportBusinessType = exportBusinessType;
      let isZip = loadName == 'zip_load';
      let getId = await this.promiseReturn(params, loadName, isZip);
      return new Promise((resolve) => {
        resolve(getId);
      });
    },
    //    附件下载
    zipDownLoad (row) {
      this.$notification.info({
        message: '系统提示',
        description: '正在下载，请稍后。。。'
      });
      let promise = new Promise((resolve, reject) => {
        getTicketAnnex({ 'id': row.id }).then(res => {
          resolve(res.result_data);
        }).catch(err => {
          reject(err);
        });
      });
      promise.then(values => {
        if (values) {
          values.forEach(item => {
            downloadBizAnnex({ 'fileId': item.id }).then(res => {
              this.$downloadFile(res.result_data);
            }).catch(() => {});
          });
        }
      });
    },
    // 样票下载
    ticketDownLoad () {
      let self = this;
      self.ticket_load = true;
      let all = [];
      self.ticket_type_dtl.forEach(item => {
        let o = new Promise((resolve, reject) => {
          let map = {
            'ticketName': item
          };
          downloadSpecimen(map).then(res => {
            self.$downloadFile(res.result_data);
            resolve(res.result_data);
          }).catch(err => {
            reject(err);
          });
        });
        all.push(o);
      });
      Promise.all(all).then(values => {
        self.handleCancel();
      }).catch(() => {
        self.ticket_load = false;
      });
    },
    // 样票下载弹窗关闭回调事件
    handleCancel () {
      this.ticket_load = false;
      this.modal_visible = false;
      this.ticket_type_dtl = undefined;
    },
    // 票面预览
    ticketPreview (row) {
      let self = this;
      self.loading = true;
      // 填报类别 不同 预览不同
      if (row.reportType == '1') {
        let map = {
          id: row.id,
          ticketTypeDtl: row.ticketTypeDtl
        };
        let preview = row.ticketTypeDtl == '8' ? specimenPreviewHu : specimenPreview;
        preview(map).then(res => {
          self.$windowOpenPdf(res.result_data);
          self.loading = false;
        }).catch(() => {
          self.loading = false;
        });
      } else {
        let promise = new Promise((resolve, reject) => {
          getTicketAnnex({ 'id': row.id }).then(res => {
            resolve(res.result_data);
          }).catch(err => {
            reject(err);
          });
        });
        promise.then(values => {
          let all = [];
          values.forEach(item => {
            let o = new Promise((resolve, reject) => {
              downloadBizAnnex({ 'fileId': item.id }).then(res => {
                resolve(res.result_data);
              }).catch(err => {
                reject(err);
              });
            });
            all.push(o);
          });
          Promise.all(all).then(result => {
            self.files = result.map(i => {
              return `data:image/png;base64,${i.fileBase64Code}`;
            });
            self.loading = false;
            self.visible = true;
          }).catch(() => {
            self.loading = false;
          });
        });
      }
    },

    // 详情
    showTicketForm (type, row) {
      if (row.ticketTypeDtl == '8') {
        this.detailVisible = true;
        this.row = row;
      } else {
        this.$refs.drawerForm.init('3', row, '/ticket/ticketManage/modules/ticketForm');
      }
    },

    // 获取具体类别
    getKinds (ticketType, reportType, ticketTypeDtl) {
      switch (ticketType) {
        // 工作票
        case '1':
          if (reportType == '1') {
            this.kinds = (Array.isArray(ticketTypeDtl)
              ? (ticketTypeDtl).filter(item => ['8'].includes(item.dataValue)) : []);
          } else {
            this.kinds = (Array.isArray(ticketTypeDtl) ? (ticketTypeDtl).filter(item => !['6', '7', '8'].includes(item.dataValue)) : []);
          }
          break;
        // 操作票
        case '2':
          this.kinds = [];
          break;
        // 动火票工作票
        case '3':
          this.kinds = (Array.isArray(ticketTypeDtl) ? (ticketTypeDtl).filter(item => ['6', '7'].includes(item.dataValue)) : []);
          break;
        default:
          this.kinds = [];
          break;
      }
    },

    // 查询参数
    queryParam () {
      this.queryMap = {
        'treeId': this.selectId,
        'flowSts': this.allStatusList,
        'startDate': (Array.isArray(this.times) && this.times.length ? moment(this.times[0]).format('YYYY-MM-DD') : undefined),
        'endDate': (Array.isArray(this.times) && this.times.length ? moment(this.times[1]).format('YYYY-MM-DD') : undefined)
      };
      Object.assign(this.queryMap, this.seachData);
    },
    /*
          数据角色树change事件
        */
    roleTreeChange (deptCode, psaIds) {
      this.page = 1;
      Object.assign(this.seachData, { 'deptCode': deptCode, 'psaIds': psaIds });
      this.queryData();
    },
    // 查询点击事件
    pageChange () {
      this.page = 1;
      this.queryData();
    },
    // 分页事件
    sizeChange (page, size) {
      this.page = page;
      this.size = size;
      this.queryData();
    },
    filterOption (input, option) {
      return (
        option.componentOptions.children[0].text.indexOf(input) >= 0
      );
    }
  }
};
</script>
