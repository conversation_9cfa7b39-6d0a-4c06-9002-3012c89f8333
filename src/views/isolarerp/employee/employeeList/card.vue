<template>
  <!-- 联系人信息 -->
  <a-row>
    <a-spin :spinning="loading">
      <a-col v-show="show || type" :span="24" class="top-btn-group">
        <a-button size="default" class="solar-eye-btn-primary" @click="addCard" style="margin-right:10px;">新增</a-button>
        <a-button size="default"  @click="delCard">删除</a-button>
      </a-col>
      <a-col :span="24">
        <vxe-table class="emp-table" ref="vxe-table-card" :data="cardTable" :edit-rules="rules" resizable align="center" max-height="457" border show-overflow highlight-hover-row size="small">
          <vxe-table-column type="checkbox" fixed="left" width="60"></vxe-table-column>
          <vxe-table-column title="序号" type="seq" width="80"></vxe-table-column>
          <vxe-table-column field="bankCardType" title="账户类型" show-overflow="title" width="200">
            <template v-slot="{ row }">
              <a-select v-model="row.bankCardType" :allowClear="false" v-if="show || row.edit" size="default" style="width:100%;">
                <a-select-option v-for="(option,index) in dictMap['bank_card_type']" :key="index" :value="option.dataValue">{{option.dataLable}}</a-select-option>
              </a-select>
              <span v-else>{{getLabel(row.bankCardType,dictMap['bank_card_type'])}}</span>
            </template>
          </vxe-table-column>
          <vxe-table-column field="bankCard" title="银行卡卡号" show-overflow="title" width="230">
            <template v-slot="{ row }">
              <a-input v-model="row.bankCard" v-if="show || row.edit" :maxLength='19' size="default" @blur="row.bankCard = $trim($event)" style="width: 95%;"></a-input>
              <span v-else>{{getLabel(row.bankCard,null)}}</span>
            </template>
          </vxe-table-column>
          <vxe-table-column field="bankName" title="开户行" show-overflow="title" min-width="300">
            <template v-slot="{ row }">
              <a-input v-model="row.bankName" v-if="show || row.edit" :maxLength='100' size="default" @blur="row.bankName = $trim($event)" style="width: 95%;"></a-input>
              <span v-else>{{getLabel(row.bankName,null)}}</span>
            </template>
          </vxe-table-column>
          <vxe-table-column :visible="type" title="操作" fixed="right" width="120" :resizable="false">
            <template slot-scope="scope">
              <template v-if="scope.row.edit">
                <a-button size="default" icon="save" :loading="loading" title="保存" @click="saveRow(scope.$rowIndex)"></a-button>
                <a-button size="default" icon="arrow-left" title="取消" @click="cacelRow(scope.$rowIndex)"></a-button>
              </template>
              <template v-else>
                <a-button size="default" type="primary" icon="form" title="编辑" @click="editRow(scope.$rowIndex)"></a-button>
              </template>
            </template>
          </vxe-table-column>
          <template v-slot:empty>
            <span>查询无数据</span>
          </template>
        </vxe-table>
      </a-col>
    </a-spin>
  </a-row>
</template>

<script>
import initDict from '@/mixins/initDict';
import { delBankById, saveBank } from '@/api/isolarErp/employee/employee';
import { TENANT_ID, USER_NAME } from '@/store/mutation-types';
export default {
  mixins: [initDict],
  props: {
    type: {
      type: Boolean,
      default: false
    },
    empId: {
      type: [String, Number],
      default: ''
    }
  },
  data () {
    return {
      cardTable: [],
      show: false,
      rules: {
        bankCardType: [
          { required: true, message: '请选择账户类型' }
        ],
        bankCard: [
          { required: true, message: '请填写银行卡号' },
          { pattern: /^([1-9]{1})(\d{15}|\d{18})$/, message: '格式不正确' }
        ],
        bankName: [
          { required: true, message: '开户行' }
        ]
      },
      loading: false,
      oldRow: null
    };
  },
  created () {
    this.getDictMap('bank_card_type');
  },
  methods: {
    // 初始化数据
    init (data, type) {
      this.show = (type && type != '3');
      data = (data || []);
      data.forEach(item => {
        item.edit = false;
      });
      this.cardTable = data;
    },
    // 新增
    addCard () {
      let row = {
        bankCardType: '',
        bankCard: '',
        bank: ''
      };
      if (this.type) {
        for (let item of this.cardTable) {
          if (item.edit) {
            this.$message.warning('请先保存当前编辑的数据!');
            return;
          }
        }
        row.edit = true;
        this.oldRow = null;
      }
      this.cardTable.unshift(row);
    },
    // 行编辑
    editRow (index) {
      for (let item of this.cardTable) {
        if (item.edit) {
          this.$message.warning('请先保存当前编辑的数据!');
          return;
        }
      }
      this.oldRow = Object.assign({}, this.cardTable[index]);
      this.cardTable[index].edit = true;
    },
    // 行保存
    async saveRow (index) {
      const self = this;
      self.loading = true;
      const errMap = await self.$refs['vxe-table-card'].validate(self.cardTable[index]).catch(errMap => errMap);
      if (errMap) {
        self.loading = false;
        return;
      }
      self.$confirm({
        title: '确定保存修改吗?',
        okText: '确定',
        cancelText: '取消',
        onOk () {
          let row = self.cardTable[index];
          row.sysTenantId = Vue.ls.get(TENANT_ID);
          row.userAccount = Vue.ls.get(USER_NAME);
          row.empId = self.empId;
          saveBank({ 'bank': [row] }).then(res => {
            self.loading = false;
            if (res.result_code == '1') {
              self.$message.success('操作成功');
              self.cardTable[index].edit = false;
              self.$emit('refresh');
            } else {
              self.$message.warning(res.result_msg);
            }
          }).catch(() => {
            self.loading = false;
          });
        },
        onCancel () {
          self.loading = false;
        }
      });
    },
    // 行-取消保存
    cacelRow (index) {
      const self = this;
      self.$confirm({
        title: '确定放弃修改吗?',
        okText: '确定',
        cancelText: '取消',
        onOk () {
          // 用于区分修改的是已有数据还是新加的数据
          if (self.oldRow) {
            self.cardTable[index] = self.oldRow;
            const data = self.cardTable;
            self.cardTable = [];
            self.$nextTick(() => {
              self.cardTable = data;
            });
          } else {
            self.cardTable.splice(index, 1);
          }
        }
      });
    },
    // 删除
    delCard () {
      const self = this;
      const select = self.$refs['vxe-table-card'].getCheckboxRecords();
      if (select.length < 1) {
        self.$message.warning('请至少勾选一条数据');
        return;
      }
      self.$confirm({
        title: '确定要删除吗?',
        okText: '确定',
        cancelText: '取消',
        onOk () {
          if (self.type) {
            let ids = [];
            select.forEach(item => {
              if (item.id) {
                ids.push(item.id);
              }
            });
            if (ids.length == 0) {
              self.remove(select);
            } else {
              self.loading = true;
              delBankById({ 'ids': ids }).then(res => {
                self.loading = false;
                if (res.result_code == '1') {
                  self.$message.info('数据删除成功');
                  self.remove(select);
                } else {
                  self.$message.warning(res.result_msg);
                }
              }).catch(() => {
                self.loading = false;
              });
            }
          } else {
            self.remove(select);
          }
        }
      });
    },
    // 页面移除数据
    remove (data) {
      let _XIDS = [];
      data.forEach(item => {
        _XIDS.push(item._XID);
      });
      this.cardTable = this.cardTable.filter(item => !_XIDS.includes(item._XID));
    },
    // 主页-获取数据
    getData () {
      return this.cardTable;
    },
    // 数据校验
    async validate () {
      const errMap = await this.$refs['vxe-table-card'].validate(true).catch(errMap => errMap);
      return errMap;
    }
  }
};
</script>

<style lang="less" scoped>
</style>
