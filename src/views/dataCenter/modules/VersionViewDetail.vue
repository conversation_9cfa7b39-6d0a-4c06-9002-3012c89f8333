<template>
  <div class="version-detail" style="width: 100%">
    <vxe-table
      v-loading="pageloading"
      :data="modelSource"
      align="center"
      ref="deviceTable"
      class="device-table"
      highlight-hover-row
      size="small"
      v-if="!isBindPoint"
      :height="height + 'px'"
    >
      <vxe-table-column
        title="第三方设备型号"
        align="center"
        :formatter="tabFormatter"
        field="modelCode"
        :width="width"
      ></vxe-table-column>

      <template v-slot:empty>
        <span>查询无数据</span>
      </template>
    </vxe-table>
    <page-pagination
      :pageSize="queryDeviceParams.pageSize"
      :current="queryDeviceParams.currentPage"
      :total="deviceTotal"
      :pageSizeOptions="pageSizeOptions"
      @size-change="sizeDeviceChange"
      v-show="!isBindPoint"
    />
  </div>
</template>
<script>
import { postAction } from '@/api/manage';
let innerHeight = window.innerHeight - 60 - 40 - 24;
const baseUrl = process.env.VUE_APP_Health_BASE_URL;
export default {
  name: 'VersionViewDetail',
  props: {
    isBindPoint: {
      type: Boolean,
      default: true
    },
    form: {
      type: Object,
      default: () => {
        return {
          deviceType: '',
          version: ''
        };
      }
    }
  },
  data () {
    return {
      pageSizeOptions: [5, 10, 15, 20],
      queryDeviceParams: {
        pageSize: '10',
        currentPage: '1',
        order: ''
      },
      queryParams: {
        pageSize: '10',
        currentPage: '1',
        order: ''
      },
      url: {
        pointList: '/versionManagement/pointListByPage',
        modelList: '/versionManagement/modelListByPage'
      },
      pointSource: [],
      modelSource: [],
      width: document.getElementsByClassName('version-mage')[0].clientWidth * 0.75 - 48,
      deviceTotal: ''
    };
  },
  created () {
    this.isFirst = true;
    this.modelSource = [];
    this.pointSource = [];
    this.queryParams = Object.assign({}, this.queryParams, this.form);
    this.queryDeviceParams = Object.assign({}, this.queryDeviceParams, this.form);
    this.loadData(1, true);
    this.loadData(1, false);
  },
  computed: {
    height () {
      return innerHeight > 700 ? innerHeight - 150 : 621;
    }
  },
  watch: {
    'form.version' () {
      if (this.form.version) {
        this.modelSource = [];
        this.pointSource = [];
        this.queryParams = Object.assign({}, this.queryParams, this.form);
        this.queryDeviceParams = Object.assign({}, this.queryDeviceParams, this.form);
        this.loadData(1, true);
        this.loadData(1, false);
      }
    },
    isBindPoint () {
      this.$nextTick(() => {
        this.width = document.getElementsByClassName('version-mage')[0].clientWidth * 0.75 - 48;
      });
    }
  },
  methods: {
    sizeChange (current, size) {
      this.queryParams.pageSize = size;
      this.queryParams.currentPage = current;
      this.loadData('', true);
    },
    sizeDeviceChange (current, size) {
      this.queryDeviceParams.pageSize = size;
      this.queryDeviceParams.currentPage = current;
      this.loadData('', false);
    },
    loadData (arg, isPoint) {
      // 加载数据 若传入参数1则加载第一页的内容
      if (arg === 1) {
        this.queryParams.currentPage = 1;
      }
      this.loading = true;
      let isPointUrl = isPoint ? this.url.pointList : this.url.modelList;
      let params = this.queryDeviceParams;
      params.source = this.form.platform == '辽电投' ? '8' : null;
      postAction(baseUrl + isPointUrl, params)
        .then((res) => {
          if (res.result_code == '1') {
            let data = res.result_data.pageList;
            let isTrue = !!(data && data.length);
            if (isPoint) {
              this.$emit('hasData', { isPoint: isTrue, flag: 'isPoint' });
              this.pointSource = data;
            } else {
              this.$emit('hasData', { isDevice: isTrue, flag: 'isDevice' });
              this.modelSource = data;
            }
            if (res.result_data) {
              if (isPoint) {
                this.total = res.result_data.rowCount;
              } else {
                this.deviceTotal = res.result_data.rowCount;
              }
            }
          }
          this.loading = false;
        })
        .catch(() => {
          this.loading = false;
        });
    },
    // 排序
    handleTableSortChange (val) {
      if (val.order == null) {
        this.queryParams.order = '';
      } else {
        this.queryParams.order = val.property + ' ' + val.order;
      }
      this.loadData(1, this.isBindPoint);
    }
  }
};
</script>
<style lang="less">
.device-table .vxe-table--body {
  width: 100% !important;
}
.version-detail {
  padding-left: 24px;
}
</style>
