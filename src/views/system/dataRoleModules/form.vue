<template>
  <a-form-model ref="baseFormModel" :model="form" :rules="rules" :labelCol="{ style:'width: 90px' }" :wrapperCol="{ style:'width: calc(100% - 90px)' }">
    <a-row :gutter="24">
      <a-col :span='24'>
        <a-form-model-item label="名称" prop="departName">
          <a-input v-model="form.departName" placeholder="请输入名称" :disabled="disabled" :maxLength="25"/>
        </a-form-model-item>
      </a-col>
      <a-col :span='24'>
        <a-form-model-item label="上级部门" prop="planTimes">
          <a-select v-model="form.parentId" disabled>
            <a-select-option :value="form.parentId">{{form.parentName}}</a-select-option>
          </a-select>
        </a-form-model-item>
      </a-col>
      <!-- <a-col :span='24'>
        <a-form-model-item label="状态" prop="status">
          <a-radio-group v-model="form.status" :disabled="disabled" name="radioGroup">
            <a-radio value="1">开启</a-radio>
            <a-radio value="0">关闭</a-radio>
          </a-radio-group>
        </a-form-model-item>
      </a-col> -->
    </a-row>
  </a-form-model>
</template>

<script>
export default {
  props: {
    disabled: {
      type: Boolean,
      default: false
    }
  },
  data () {
    return {
      form: {
        departName: undefined,
        parentId: undefined,
        parentName: '',
        status: '1'
      },
      rules: {
        departName: [{
          required: true,
          message: '请填写'
        }]
        // status:[{
        //   required: true,
        //   message: '请选择'
        // }]
      },
      old: null
    };
  },
  methods: {
    /*
        数据初始化
      */
    init (record, type) {
      if (record) {
        if (type == 'edit') {
          this.old = Object.assign({}, record, { departName: record.name });
          this.form = Object.assign({}, record, { departName: record.name });
        } else {
          Object.assign(this.form, { parentId: record.id, parentName: record.name });
        }
        this.$forceUpdate();
      } else {
        this.old = null;
        Object.assign(this.form, this.$options.data().form);
        this.$refs.baseFormModel.resetFields();
        this.$refs.baseFormModel.clearValidate();
      }
    },
    /*
        数据重置
      */
    reset () {
      Object.assign(this.form, this.old);
      this.$forceUpdate();
    },
    /*
        数据校验
      */
    validate () {
      let _this = this;
      return new Promise((resolve, reject) => {
        _this.$refs.baseFormModel.validate(valid => {
          if (valid) {
            let form = Object.assign({}, _this.form);
            resolve(form);
          } else {
            reject(new Error());
          }
        });
      });
    }
  }
};
</script>

<style lang="less" scoped>
  :deep(.ant-form-item) {
    display: flex;
    align-items: center;
  }
</style>
