<template>
  <div>
    <!-- <a-layout class="device-info-bulk-changes" :style="{height:drawerBodyHeight}" theme="light"> -->
      <!-- <a-layout-sider :width="'20%'" class="left-section"> -->
        <a-row :gutter="16">
          <a-col :span="4">
             <a-input-search placeholder="输入电站名称" enter-button @search="getDeviveType" v-model="deviceName" allowClear>
                <div slot="enterButton">
                  <a-icon type="search" @click="getDeviveType()" />
                </div>
              </a-input-search>
              <a-table :data-source="deviceNameList" ref="multipleTable" :loading="loadingDeviceType" :pagination="false"
                :row-key="(r,i)=>(i.toString())" :row-selection="{ selectedRowKeys: selectedRowKeys, onChange: (keys, rows)=>{selectedRowKeys = keys;handleSelectionChange(rows)}}"
                :scroll="{x:'max-content',y:  height - 240 +'px'}" size="small" style="width: 100%;margin-top: 16px;" bordered :style="{height: height - 180 +'px'}">
                <a-table-column key="device_name" title="设备名称" data-index="device_name">
                  <template slot-scope="text">
                    <a-tooltip placement="topLeft" :title="text">
                      {{text}}
                    </a-tooltip>
                  </template>
                </a-table-column>
              </a-table>
               <a-button class="solar-eye-btn-primary" @click.stop.prevent="batchUp()">批量修改</a-button>
          </a-col>
          <a-col :span="20"  v-show="isShow">
                <a-form-model ref="form" :model="form" :rules="rules" v-bind="{
                      labelCol: {
                        xs: { span: 24 },
                        sm: { span: 11},
                      },
                      wrapperCol: {
                        xs: { span: 24 },
                        sm: {offset:1, span: 12 },
                      }}" :style="{height: height - 140 +'px', overflow:auto}">
              <a-row>
                <a-col :xs="23" :sm="12" v-if="provideData.deviceType === 1">
                  <a-form-model-item class="item form-item-inject" label="逆变器类型：" prop="inverterType">
                    <a-select v-model="form.inverterType" style="width: 100%">
                      <a-select-option v-for="item in stationSelectType['0027']" :key="item.secondTypeCode" :label="item.secondName"
                        :value="item.secondTypeCode">{{item.secondName}}</a-select-option>
                    </a-select>
                  </a-form-model-item>
                </a-col>
                <a-col :xs="23" :sm="12" v-if="provideData.deviceType === 1">
                  <a-form-model-item class="item form-item-inject" label="所属集电线路（#）:">
                    <a-input v-model="form.belongLine"></a-input>
                  </a-form-model-item>
                </a-col>
              </a-row>
              <a-row>
                <a-col :xs="23" :sm="12" v-if="provideData.deviceType === 1">
                  <a-form-model-item class="item form-item-inject" label="所属方阵（#）:">
                    <a-input v-model="form.belongMatrix"></a-input>
                  </a-form-model-item>
                </a-col>
                <a-col :xs="23" :sm="12">
                  <a-form-model-item class="item form-item-inject" label="工作温度最小值（℃）：" prop="workingTemperatureMin">
                    <a-input v-model="form.workingTemperatureMin"></a-input>
                  </a-form-model-item>
                </a-col>
                <a-col :xs="23" :sm="12">
                  <a-form-model-item class="item form-item-inject" label="工作温度最大值（℃）：" prop="workingTemperatureMax">
                    <a-input v-model="form.workingTemperatureMax"></a-input>
                  </a-form-model-item>
                </a-col>
                <a-col :xs="23" :sm="12" v-if="provideData.deviceType === 1">
                  <a-form-model-item class="item form-item-inject" label="启动电压（V）：" prop="startingVoltage">
                    <a-input v-model="form.startingVoltage"></a-input>
                  </a-form-model-item>
                </a-col>
                <a-col :xs="23" :sm="12" v-if="provideData.deviceType === 1">
                  <a-form-model-item class="item form-item-inject" label="额定并网电压（V）：" prop="ratedOperatingVoltage">
                    <a-input v-model="form.ratedOperatingVoltage"></a-input>
                  </a-form-model-item>
                </a-col>
                <a-col :xs="23" :sm="12" v-if="provideData.deviceType === 1">
                  <a-form-model-item class="item form-item-inject" label="每路MPPT可接入最大组串数：" prop="subassemblyNumMax">
                    <a-input v-model.number="form.subassemblyNumMax" @blur="blurEvent"></a-input>
                  </a-form-model-item>
                </a-col>
                <a-col :xs="23" :sm="12" v-if="provideData.deviceType === 4">
                  <a-form-model-item class="item form-item-inject" label="可接入组串数：" prop="accessStringNum">
                    <a-input v-model="form.accessStringNum" @blur="blurEvent"></a-input>
                  </a-form-model-item>
                </a-col>
                <a-col :xs="23" :sm="12" v-if="provideData.deviceType === 1">
                  <a-form-model-item class="item form-item-inject" label="MPPT路数：" prop="mpptNum">
                    <a-input v-model="form.mpptNum" @blur="blurEvent"></a-input>
                  </a-form-model-item>
                </a-col>
                <a-col :xs="23" :sm="12" v-if="provideData.deviceType === 1">
                  <a-form-model-item class="item form-item-inject" label="降额温度点（℃）：" prop="deratingTemperaturePoint">
                    <a-input v-model="form.deratingTemperaturePoint"></a-input>
                  </a-form-model-item>
                </a-col>
                <a-col :xs="23" :sm="12" v-if="provideData.deviceType === 1 && form.inverterType !='1'">
                  <a-form-model-item class="item form-item-inject" label="模块温度限值（℃）：" prop="moduleTemperatureLimit">
                    <a-input v-model="form.moduleTemperatureLimit"></a-input>
                  </a-form-model-item>
                </a-col>
                <a-col :xs="23" :sm="12" v-if="form.inverterType == '2' && provideData.deviceType === 1">
                  <a-form-model-item class="item form-item-inject" label="逆变单元数：">
                    <a-select v-model="form.inverterUnitNum" style="width: 100%">
                      <a-select-option v-for="item in stationSelectType['0028']" :key="item.secondTypeCode" :label="item.secondName"
                        :value="item.secondTypeCode">{{item.secondName}}</a-select-option>
                    </a-select>
                  </a-form-model-item>
                </a-col>
                <a-col :xs="23" :sm="12" v-if="form.inverterType == '1' || provideData.deviceType === 4">
                  <a-form-model-item class="item form-item-inject" label="输入端最大允许接入电流（A）：" prop="inputElectricityMax">
                    <a-input v-model="form.inputElectricityMax"></a-input>
                  </a-form-model-item>
                </a-col>
              </a-row>
              <a-row style="margin-bottom: 24px;">
                <a-table :data-source="form.tableData" class="config-data-table" bordered v-if="form.inverterType == 1 || provideData.deviceType === 4"
                  size="small" :scroll="{x:cols[0].width + (form.accessStringNum?form.accessStringNum:0) * 120}"
                  :pagination="false" :row-key="(r,i)=>(i.toString())">
                  <a-table-column v-for="(col, index) in cols" :data-index="col.prop" :title="col.label" :key="index"
                    align="center" :width="col.width || 'auto'">
                    <template slot-scope="text, record">
                      <span v-if="col.prop == 'name'">{{ record.name }} </span>
                      <a-form-model-item v-else-if="record.value != 'p130005'" class="table-form-item" :prop="
                                 'tableData.' +
                                 getValue(record.value, 0) +
                                 '.' +
                                 [col.prop]
                               "
                        :rules="
                                 col.prop == 'name'
                                   ? []
                                   : [
                                       {
                                         trigger: 'blur',
                                         validator: getValue(record.value, 2),
                                       },
                                     ]
                               ">
                        <a-input v-model="record[col.prop]" :key="col.prop + index" style="width: 100%" @input="isModifySameTime(record, record[col.prop])"></a-input>
                      </a-form-model-item>
                      <a-form-model-item class="table-form-item" label-width="0" v-else-if="record.value == 'p130005'">
                        <a-select v-model="record[col.prop]" style="width: 100%" @change="isModifySameTime(record, record[col.prop])"
                          allowClear>
                          <a-select-option v-for="item in stationSelectType['0029']" :key="item.secondTypeCode" :label="item.secondName"
                            :value="item.secondTypeCode">{{item.secondName}}</a-select-option>
                        </a-select>
                      </a-form-model-item>
                    </template>
                  </a-table-column>
                  <a-table-column title="是否同时修改" fixed="right" align="center">
                    <template slot-scope="text, record">
                      <a-checkbox v-model="record.isCheked"></a-checkbox>
                    </template>
                  </a-table-column>
                </a-table>
              </a-row>

            </a-form-model>
            <a-row>
                <div style="width: 100%;text-align: center;">
                  <a-button class="solar-eye-btn-primary" @click="saveBatch()" :loading="saveBtn">确 定</a-button>
                </div>
              </a-row>
          </a-col>
        </a-row>
        <!-- <a-card style="height: 100%;" :bodyStyle="{height:drawerBodyHeight}">
          <div class="left-area">
            <div>

            </div>
            <a-button class="solar-eye-btn-primary" @click.stop.prevent="batchUp()">批量修改</a-button>
          </div>
        </a-card>
      </a-layout-sider> -->
      <!-- <a-layout class="right-section" >
        <a-layout-content style="display: flex;flex-direction: column;justify-content: space-evenly;">
          <a-card>

          </a-card>
        </a-layout-content>
      </a-layout>
    </a-layout> -->
  </div>
</template>
<script>
import {
  updatePsAttribute,
  queryDeviceListByUserId
} from '@/api/health/healthapi.js';
import deviceInfo from '@/mixins/health/device_info';
import drawerBodyHeight from '@/mixins/health/drawerBodyHeight';
import baseInfo from '../../mixins/baseInfo';
import valitateTips, {
  validateTemperature,
  validateNumber,
  validateMaxNumber,
  validate32
} from '@/api/health/validate.js';
const innerHeight = window.innerHeight - 40 - 60 - 24;
export default {
  inject: ['provideData'],
  props: {
    stationSelectTypeList: {
      type: Array,
      default: () => []
    },
    inverterList: {
      type: Array,
      default: () => []
    },
    open: {
      type: Boolean,
      default: false
    }
  },
  mixins: [deviceInfo, baseInfo, drawerBodyHeight],
  computed: {
    leftTableHeight () {
      return (this.drawerBodyHeight - 24) * 0.7;
    }
  },
  data () {
    return {
      deviceNameList: [],
      psKeyList: [],
      saveBtn: false,
      deviceName: '',
      isShow: false,
      selectedRowKeys: [],
      height: innerHeight,
      rules: {
        inputElectricityMax: [{
          validator: valitateTips.valitate('validateInputElectricityMax'),
          trigger: 'blur'
        } ],
        subassemblyNumMax: [{
          validator: valitateTips.valitate('validateSubassemblyNumMax'),
          trigger: 'blur'
        } ],
        workingTemperatureMax: [{
          validator: validateTemperature,
          trigger: 'blur'
        } ],
        workingTemperatureMin: [{
          validator: validateTemperature,
          trigger: 'blur'
        } ],
        modulePeakPower: [{
          validator: validateMaxNumber,
          trigger: 'blur'
        } ],
        accessStringNum: [{
          validator: validate32,
          trigger: 'blur'
        } ],
        deratingTemperaturePoint: [{
          validator: validateTemperature,
          trigger: 'blur'
        } ],
        moduleTemperatureLimit: [{
          validator: validateTemperature,
          trigger: 'blur'
        } ],
        startingVoltage: [{
          validator: validateNumber,
          trigger: 'blur'
        }],
        ratedOperatingVoltage: [{
          validator: validateNumber,
          trigger: 'blur'
        }],
        ratedPower: [{
          validator: validateMaxNumber,
          trigger: 'blur'
        }]
      }
    };
  },
  created () {
    // this.open = true;
    this.getDeviveType();
    this.getSystemCodeList();
  },
  mounted () {
    //   console.log(this.psId, this.deviceType)
  },
  watch: {
    'provideData.deviceType' () {
      this.getDeviveType();
    }
  },
  methods: {
    getDeviveType () {
      this.loadingDeviceType = true;
      queryDeviceListByUserId({
        deviceType: this.provideData.deviceType,
        psId: this.provideData.psId,
        deviceName: this.deviceName
      }).then((res) => {
        this.loadingDeviceType = false;
        if (res.result_code === '1') {
          this.deviceNameList = res.result_data;
        } else {
          this.$message.error(res.result_msg);
        }
      }).catch(() => {
        this.loadingDeviceType = false;
      });
    },
    close () {
      // this.open = false;
      this.isShow = false; // 弹框右侧的设备信息隐藏
      // this.$emit('update:open', false)
      // this.$emit("dialogClose", 2);
      // this.cData.deviceNameList = [];
    },
    batchUp () {
      if (this.psKeyList == null || this.psKeyList == '') {
        this.$message.warning('请勾选要修改的设备');
        return;
      }
      this.isShow = true; // 弹框右侧的设备信息显示
    },

    saveBatch () {
      // this.flag = false;
      let flag = true;
      this.$refs.form.validate((valid) => {
        flag = false;
        if (valid) {
          this.updataBatch();
          this.selectedRowKeys = [];
        } else {
          return false;
        }
      });
      this.$nextTick(() => {
        if (
          flag &&
            document.getElementsByClassName('has-error').length == 0
        ) {
          this.updataBatch();
          this.selectedRowKeys = [];
        }
      });
    },
    isEmpty (obj) {
      for (var key in obj) {
        if (obj[key] == '' || !obj[key]) {
          delete obj[key];
        }
      }
      return obj;
    },
    updataBatch () {
      this.form = this.isEmpty(this.form);
      this.form.psKey = this.psKeyList;
      this.saveBtn = true;
      updatePsAttribute({
        psId: this.provideData.psId,
        attributeType: '02',
        psKey: this.psKeyList,
        isMulti: true,
        deviceConfig: this.form,
        seriesConfig: this.form.tableData
      })
        .then((res) => {
          this.saveBtn = false;
          this.isShow = false;
          if (res.result_code === '1') {
            this.$message.success('批量修改成功');
            // this.open = false;
            // this.$emit("dialogClose", 2);
            this.deviceNameList = [];
            this.$emit('changeData', 1);
            this.$emit('close');
            // this.getDeviceInfo(true);
          } else {
            this.$message.error(res.result_msg);
          }
        })
        .catch(function (err) {
          this.$message.error(err);
        });
    },
    handleSelectionChange (val) {
      if (this.$refs['form']) {
        this.$nextTick(() => {
          this.$refs['form'].resetFields();
        });
      }
      let psKeyArray = [];
      val.forEach((item) => {
        psKeyArray.push(item.ps_key);
      });
      this.psKeyList = psKeyArray.join(',');
    }
  }
};
</script>
<style lang="less" scoped>
  .device-info-bulk-changes {
    .left-section {
      background-color: inherit;
      margin-right: 5px;
    }

    .right-section {
      margin-right: 5px;
    }

    :deep(.left-area) {
      height: 100%;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: space-between;
    }
  }
</style>
