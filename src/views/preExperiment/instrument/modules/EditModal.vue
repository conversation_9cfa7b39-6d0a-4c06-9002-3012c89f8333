<template>
  <a-modal :title="title" :visible="visible" @cancel="handleCancel" destroyOnClose>
    <a-spin size="small" :spinning="loading" style="height: 100%">
      <a-form-model
        :model="formData"
        ref="instrumentModal"
        lable-align="left"
        :labelCol="{ style: 'width: 80px' }"
        :wrapperCol="{ style: 'width: calc(100% - 80px)' }"
        :rules="rules"
      >
        <a-form-model-item label="仪器型号" prop="instrumentModel">
          <a-input v-model="formData.instrumentModel" @blur="formData.instrumentModel = $trim($event)" placeholder="请输入仪器型号" :max-length="50" style="width: 100%" allow-clear />
        </a-form-model-item>
        <a-form-model-item label="仪器名称" prop="instrumentName">
          <a-input v-model="formData.instrumentName" @blur="formData.instrumentName = $trim($event)" placeholder="请输入仪器名称" :max-length="50" style="width: 100%" allow-clear/>
        </a-form-model-item>
        <a-form-model-item label="仪器状态" prop="isEnable">
          <a-switch v-model="formData.isEnable" checked-children="启用" un-checked-children="禁用" />
        </a-form-model-item>
      </a-form-model>
    </a-spin>
    <template slot="footer">
      <div class="flex-end">
        <throttle-button label="取消" class="solar-eye-btn-primary-cancel" @click="handleCancel" :loading="loading" />
        <a-button class="ant-btn-primary" @click="handleOk" :loading="loading">确认</a-button>
      </div>
    </template>
  </a-modal>
</template>

<script>
import { updateInstrumentConfigApi } from '@api/isolarErp/preExperiment';

export default {
  data () {
    return {
      visible: false,
      loading: false,
      formData: {
        id: null,
        isEnable: true,
        instrumentModel: null,
        instrumentName: null
      },
      model: {
        type: '',
        row: {}
      },
      rules: {
        instrumentModel: [
          {
            required: true,
            message: '此项为必填项'
          }
        ],
        instrumentName: [
          {
            required: true,
            message: '此项为必填项'
          }
        ],
        isEnable: [
          {
            required: true,
            message: '此项为必填项',
            trigger: 'change'
          }
        ]
      },
      title: ''
    };
  },
  methods: {
    handleOk () {
      this.$refs.instrumentModal.validate((valid) => {
        if (valid) {
          this.loading = true;
          const map = {
            ...this.formData,
            isEnable: this.formData.isEnable ? 1 : 0
          };
          updateInstrumentConfigApi(map)
            .then((res) => {
              if (res.result_code === '1') {
                this.loading = false;
                this.$message.success(map.id ? '更新成功' : '新增成功');
                this.handleCancel();
                setTimeout(() => { this.$emit('queryData'); });
              }
            })
            .catch(() => {
              this.loading = false;
            });
        }
      });
    },
    handleCancel () {
      this.visible = false;
      this.formData = this.$options.data().formData;
      this.$refs.instrumentModal.clearValidate();
    },
    init (type, row = {}) {
      this.model.type = type;
      if (row.id) { this.title = '编辑'; } else { this.title = '新增'; }
      Object.assign(this.formData, row, { isEnable: type === '2' ? row.isEnable === 1 : true });
      this.visible = true;
    }
  }
};
</script>

<style lang="less" scoped>
:deep(.ant-form-item) {
  display: flex;
}
</style>
