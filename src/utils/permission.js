// 判断用户是否有按钮权限:根据传入的权限标识，查看是否存在于用户的权限标识集合内
import store from '@/store';

export function hasPermission (perms) {
  let hasPermission = false;
  let permission = store.state.permission.permission;
  for (var i = 0; i < permission.length; i++) {
    if (permission[i] == perms) {
      hasPermission = true;
      break;
    }
  }
  // console.log(100000000,permission);
  // console.log(200000000,hasPermission);
  return hasPermission;
}

/**
 * @param {Array} value
 * @returns {Boolean}
 * @example see @/views/auth/directive.vue
 */

export default function checkPermission (value) {
  if (value && value instanceof Array && value.length > 0) {
    const roles = store.getters && store.getters.roles;
    const permissionRoles = value;
    const hasPermission = roles.some(role => {
      return permissionRoles.includes(role);
    });

    if (!hasPermission) {
      return false;
    }
    return true;
  } else {
    console.error(`need roles! Like v-permission="['admin','editor']"`);
    return false;
  }
}
