<!-- 版本管理 -->
<template>
  <div style="position: 'relative'">
    <a-tabs v-model="activityKey" class="right-bar" @change="changeEvent">
      <a-tab-pane key="realTime" tab="实时库"> </a-tab-pane>
      <a-tab-pane key="message" tab="报文" force-render> </a-tab-pane>
    </a-tabs>
    <message-search-model @search="searchEvent" @refresh="refreshEvent" :disabled="isTrue" :active="activityKey" ref='searchModel'></message-search-model>
    <div class="solar-eye-gap"></div>
    <a-spin :spinning="firstLoad">
    <a-row :gutter="16">
      <a-col :span="4">
        <div :style="{ height: tableHeight + 84 + 'px' }" class="left-tree-bg">
        <a-spin :spinning='treeLoading && !firstLoad'>
          <virtual-tree
            :data="treeData"
            :height="isShowMenu ? 'calc(100vh - 280px)' : 'calc(100vh - 156px)'"
            node-key="key"
            ref="deviceTree"
            @node-click="onClick"
            :current-node-key="selectedKeys"
            :expand-on-click-node="false"
            :props="{
              label: 'title',
              isLeaf: 'isLeaf',
              children: 'children',
              key: 'psKey'
            }"
            v-show='!isTrue'
          >
           <span class="custom-tree-node" slot-scope="{ node, data }">
              <span :title="data.deviceStatus == 1 ? data.title + '（停用）' : data.title" :class="data.selectable ? '' : 'unSelectable'">
                {{ data.deviceStatus == 1 ? data.title + '（停用）' : data.title}}
              </span>
            </span>
          </virtual-tree>
          </a-spin>
        </div>
      </a-col>
      <a-col :span="20">
        <div class="solar-eye-main-content">
          <!-- 操作按钮 -->
          <div class="operation" style="height: 32px">
            <div class="operation-btn">
            <a-checkbox v-if="activityKey=='message'" v-model="isTrue" @change="changeDeviceCode">查看无设备报文</a-checkbox>
              <a-button class="solar-eye-btn-primary" :loading="exportLoading" @click="exportEvent">导出</a-button>
            </div>
          </div>
          <RealTableList :dataSource="dataSource" :loading="tableLoad && !firstLoad" ref='tableList' :tableHeight="tableHeight" :baseInfo="baseInfo" v-if="activityKey=='realTime'"></RealTableList>
          <MessageList :dataSource="dataSource" :loading="tableLoad && !firstLoad" ref='tableList'  :tableHeight="tableHeight" v-else :type="messageType"></MessageList>
          <page-pagination
            :pageSize="baseParam.size"
            :current="baseParam.curPage"
            :total="total"
            @size-change="sizeChange"
          />
        </div>
      </a-col>
    </a-row>
    </a-spin>
  </div>
</template>
<script>
import tableHeight from '@/mixins/tableHeightAndSearchModel';
import virtualTree from '@/components/virtrulTree/ve-tree.vue';
import { DeviceTree } from './mixins/deviceTree.js';
import MessageSearchModel from './modules/message/MessageSearchModel';
import RealTableList from './modules/message/RealTableList';
import MessageList from './modules/message/MessageList';
import download from '@/utils/download';
import { realTimeLibraryList, exportRealtimeLibraryList, getMessageList, exportMessageList } from '@/api/dataCenter';
export default {
  name: 'Message',
  mixins: [tableHeight, DeviceTree],
  components: { virtualTree, MessageSearchModel, RealTableList, MessageList },
  data () {
    return {
      baseParam: {
        psKey: '',
        deviceType: '',
        size: 10,
        curPage: 1
      },
      baseInfo: {
        psId: '',
        psName: ''
      },
      messageType: '1',
      treeData: [],
      total: 0,
      selectedRowKeys: [],
      activityKey: 'realTime',
      tableLoad: true,
      exportLoading: false,
      noDeviceCode: '',
      isTrue: false,
      firstLoad: true
    };
  },
  created () {
  },
  computed: {
    isShowMenu () {
      return this.$store.state.user.isShowMenu;
    }
  },
  methods: {
    setSelectNodeInfo (node) {
      this.selectNode = node;
      this.selectedKeys = [node.psKey];
      this.baseParam.psKey = node.psKey;
      this.baseParam.deviceType = node.deviceType;
      this.baseParam.curPage = 1;
      this.getList();
    },
    changeDeviceCode (val) {
      this.noDeviceCode = this.isTrue ? 1 : '';
      this.$refs.searchModel.queryParams.sn = '';
      this.getList(this.isTrue ? { psId: '', psName: '', psKey: '', sn: '' } : '');
    },
    exportEvent () { // 导出
      this.exportLoading = true;
      let isRealTime = this.activityKey == 'realTime';
      let exportList = isRealTime ? exportRealtimeLibraryList : exportMessageList;
      let messageType = isRealTime ? undefined : this.messageType;
      let noDeviceCode = isRealTime ? undefined : this.noDeviceCode;
      exportList({ ...this.$refs.searchModel.queryParams, ...this.baseParam, messageType, noDeviceCode }).then(res => {
        this.exportLoading = false;
        download(res.payload, 'strBase64');
      }).catch(() => {
        this.exportLoading = false;
      });
    },
    refreshEvent () {
      this.treeData = [];
      this.dataSource = [];
      this.treeLoading = true;
      this.tableLoad = true;
      this.firstLoad = true;
    },
    changeEvent () {
      let searchModel = this.$refs.searchModel;
      searchModel.dealQueryParams(this.activityKey);
      this.isTrue = false;
      this.noDeviceCode = '';
      this.baseParam.curPage = 1;
      if (!this.baseInfo.psId) return;
      this.getList();
    },
    getList (param) {
      let isRealTime = this.activityKey == 'realTime';
      let getList = isRealTime ? realTimeLibraryList : getMessageList;
      let noDeviceCode = isRealTime ? undefined : this.noDeviceCode;
      let params = { ...this.$refs.searchModel.queryParams, ...this.baseParam, noDeviceCode };
      if (param) params = Object.assign({}, params, param);
      this.tableLoad = true;
      let tableList = this.$refs.tableList;
      tableList.clearScroll();
      getList(params).then(res => {
        this.dataSource = res.result_data || res.payload.list;
        this.tableLoad = false;
        this.total = res.payload.total;
        this.firstLoad = false;
      }).catch(() => {
        this.firstLoad = false;
        this.tableLoad = false;
      });
    },
    searchEvent (form, isNew) {
      this.baseInfo.psId = form.psId;
      this.baseInfo.psName = form.psName;
      this.messageType = form.messageType;
      if (form.hasOwnProperty('isLoading')) {
        this.firstLoad = false;
        this.tableLoad = false;
        this.treeLoading = false;
        return;
      }
      if (isNew) {
        this.baseParam.psKey = '';
        this.baseParam.deviceType = '';
        this.getPsTreeMenu({
          psId: form.psId
        });
        this.firstLoad = true;
      } else {
        this.firstLoad = false;
      }
      this.baseParam.curPage = 1;
      this.getList(this.isTrue ? { psId: '', psName: '', psKey: '', sn: '' } : '');
    },
    sizeChange (current, size) {
      this.baseParam.size = size;
      this.baseParam.curPage = current;
      this.getList(this.isTrue ? { psId: '', psName: '', psKey: '', sn: '' } : '');
    }
  }
};
</script>
<style lang="less" scoped>
:deep(.ant-tabs-bar) {
  margin: 0;
}
.right-bar,
.left-tree-bg {
  background: var(--zw-card-bg-color--default);
}
.left-tree-bg  {
 border-radius: 4px;
  padding-top:16px;
}
.unSelectable {
  color: var(--zw-text-color--disable);
}
.custom-tree-node {
    overflow: hidden;
    text-overflow: ellipsis;
}
</style>
