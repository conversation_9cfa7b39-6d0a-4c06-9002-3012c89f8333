<template>
  <div class="select-option" @click="selectItem" @mouseover="isShow = true" @mouseleave="isShow = false">
    <div v-if="isMultiple" class="is-multiple" :class="{'selected': source['checked'], 'isDisabled': source['isDisabled']}">
      <span :title="source['name']" class="option-name">{{source[label]}}</span>
      <svg-icon  :class="{ 'isShow': isShow }" v-if="source['checked'] || isShow" iconClass="selected"></svg-icon>
    </div>
    <div v-else :class="{'selected': source['checked'], 'isDisabled': source['isDisabled']}">
      <span :title="source[label]" class="option-name">{{source[label]}}</span>
    </div>
  </div>
</template>
<script>
export default {
  name: 'OptionNode',
  props: {
    // 每一行的索引
    index: {
      type: Number
    },
    // 每一行的内容
    source: {
      type: Object,
      default () {
        return {};
      }
    },
    // 需要显示的名称
    label: {
      type: String
    },
    // 绑定的值
    value: {
      type: String
    },
    // 是否多选
    isMultiple: {
      type: Boolean,
      default () {
        return false;
      }
    },
    onItemClick: {
      type: Function,
      default () {
        return () => {};
      }
    }
  },
  data () {
    return {
      isShow: false // 多选hover后面√是否显示
    };
  },
  methods: {
    selectItem () {
      if (!this.source.isDisabled) {
        this.onItemClick(this.source);
      }
    }
  }
};
</script>
<style lang="less" scoped>
.select-option {
  width: 100%;
  cursor: pointer;
  color: var(--zw-text-2-color--default);
  font-size: 14px;
  & > div {
    padding: 5px 12px;
  }
  &:hover {
    background-color: var(--zw-primary-bg-color--hover);
    border-radius: 4px;
  }
  .selected {
    background: var(--zw-primary-color--default);
    color: var(--zw-common-white-color--default);
    .checked {
      color: var(--zw-common-white-color--default) !important;
    }
  }
  & > div {
    display: flex;
    align-items: center;
    .option-name {
      padding-right: 32px;
      display: block;
      position: relative;
      flex: 1;
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
    }

  }
  .isDisabled {
    color: var(--zw-text-reset-grey-color--default);
    cursor: not-allowed;
    .isShow {
      color: var(--zw-text-reset-grey-color--default) !important;
    }
  }
}
</style>
