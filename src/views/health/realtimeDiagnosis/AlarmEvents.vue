<template>
  <div id="AlarmEvents">
    <a-spin :spinning="pageloading">
      <div class="solar-eye-search-model">
        <div class="solar-eye-search-content">
          <a-row :gutter="24" style="margin: 0">
            <a-col :xxl="4" :xl="6" :md="10">
              <div class="search-item">
                <span class="search-label">电站名称</span>
                <ps-tree-select @change="getStationChange" :isPsName="psName" v-model="psId" :isPsaDisable="false"
                  :isQueryPs="1" ref="tree" style="width: 100%;" psCateGory="'CentralizedPV','DistributedPV','Storage','CIPV'"
                  :hasMaintenanceStatusParams="true" />
              </div>
            </a-col>
            <a-col :xxl="4" :xl="6" :md="10">
              <div class="search-item">
                <span class="search-label">电量保证类型</span>
                <a-select :maxTagCount="1" class="electric-input" mode="multiple" v-model="elecEnsureType"
                  @change="paramsChange($event, 'elecEnsureType')">
                  <a-select-option value="0">全部</a-select-option>
                  <a-select-option v-for="data in dictMap.elec_ensure_type" :key="data.dataValue" :value="data.dataValue">
                    {{ data.dataLable }}
                  </a-select-option>
                </a-select>
              </div>
            </a-col>
            <a-col :xxl="4" :xl="6" :md="10">
              <div class="search-item">
                <span class="search-label">发生时间</span>
                <a-range-picker @change="paramsChange($event, 'searchTime')" v-model="searchTime"
                  format="YYYY-MM-DD"></a-range-picker>
              </div>
            </a-col>
            <a-col :xxl="3" :xl="6" :md="10">
              <div class="search-item">
                <span class="search-label">处理状态</span>
                <a-select v-model="alarmStatus" @change="alarmStatusChange($event, 'alarmStatus')">
                  <a-select-option value="01">待处理</a-select-option>
                  <a-select-option value="03">已派发</a-select-option>
                  <a-select-option value="04">已闭环</a-select-option>
                </a-select>
              </div>
            </a-col>
            <a-col :xxl="3" :xl="6" :md="10" v-show="type == '4'">
              <div class="search-item">
                <span class="search-label">隐患类型</span>
                <a-select v-model="alarmRemark" placeholder="请选择" @change="alarmCategoryChange">
                 <a-select-option value="">全部</a-select-option>
                  <a-select-option :value="item.remark" v-for="item in deviceTypeList" :key="item.remarkName">
                    {{ item.remarkName }}
                  </a-select-option>
                  <!--
                  <a-select-option value="23">防雷故障</a-select-option>
                  <a-select-option value="9">开关故障</a-select-option>
                  <a-select-option value="11">风扇故障</a-select-option> -->
                </a-select>
              </div>
            </a-col>
            <a-col :xxl="4" :xl="6" :md="10">
              <div class="search-item">
                <span class="search-label">{{ getTypeName }}原因</span>
                <a-select v-model="alarmReason" @change="alarmReasonChange" placeholder="请选择" show-search
                  :filter-option="filterOption">
                  <a-select-option value="">全部</a-select-option>
                  <a-select-option :value="item.reason" v-for="item in alarmReasonOptions" :key="item.reasonName">
                    {{ item.reasonName }}
                  </a-select-option>
                </a-select>
              </div>
            </a-col>
            <a-col :xxl="3" :xl="6" :md="10" v-show="alarmStatus == '04'">
              <div class="search-item">
                <span class="search-label">闭环方式</span>
                <a-select v-model="closedType" @change="paramsChange($event, 'closedType')"
                  :getPopupContainer="(node) => node.parentNode">
                  <a-select-option value="">全部</a-select-option>
                  <a-select-option v-for="item in dictMap.farm_scene_condition_query" :key="item.key"
                    :value="item.codeValue" :title="item.dispName">
                    {{ item.dispName }}
                  </a-select-option>
                </a-select>
              </div>
            </a-col>
            <a-col :xxl="2" :xl="6" :md="2">
              <div class="search-item ">
                <div class="search-label">
                  <throttle-button label="重置" class="solar-eye-btn-primary-cancel"
                    @click="resetSearchData(true)"></throttle-button>
                  <throttle-button label="查询" class="solar-eye-btn-primary"
                    @click="pageChange(1, true)"></throttle-button>
                </div>
              </div>
            </a-col>
          </a-row>
        </div>
        <div class="split-line"></div>
      </div>
      <a-col class="solar-eye-main-content">
        <div class="operation" style="height: 32px">
          <div class="operation-btn">
            <a-radio-group v-model="deviceType" @change="deviceTypeChange" size="default">
              <a-radio-button value="">全部({{ activeTotal }})</a-radio-button>
              <!-- 集中式、分布式 -->
              <template v-if="categoryList.includes('CentralizedPV') || categoryList.includes('DistributedPV') || categoryList.includes('CIPV')">
                <template v-if="type == '1' || type == '2' || type == '3'">
                  <a-radio-button value="11">电站({{ deviceTotal[11] }})</a-radio-button>
                </template>
                  <template v-if="type == '1' || type == '2'">
                  <!-- <a-radio-button value="11">电站({{ deviceTotal[11] }})</a-radio-button> -->
                  <a-radio-button value="66" v-if="type == '1'">升压站({{ deviceTotal[66] }})</a-radio-button>
                  <a-radio-button value="3">集电线({{ deviceTotal[3] }})</a-radio-button>
                  <!-- <a-radio-button value="17">方阵({{ deviceTotal[17] }})</a-radio-button> -->
                </template>
                <template v-if="type == '1' || type == '2' || type == '4'">
                  <a-radio-button value="17">方阵({{ deviceTotal[17] }})</a-radio-button>
                </template>
                <a-radio-button  value="1">逆变器({{ deviceTotal[1] }})</a-radio-button>
                <a-radio-button v-if="type == '1' || type == '2'" value="4">汇流箱({{ deviceTotal[4] }})</a-radio-button>
                <a-radio-button v-if="type == '1' || type == '3'" value="10">组串({{ deviceTotal[10] }})</a-radio-button>
                <template v-if="type == '2'">
                  <a-radio-button value="7">电表({{ deviceTotal[7] }})</a-radio-button>
                  <a-radio-button value="301">组串计量箱({{ deviceTotal[301] }})</a-radio-button>
                  <a-radio-button value="5">环境检测仪({{ deviceTotal[5] }})</a-radio-button>
                </template>
              </template>
              <!-- 储能 -->
              <template v-if="categoryList.includes('Storage') && (type==1|| type==4)">
               <a-radio-button value="26">EMS({{ deviceTotal[26] }})</a-radio-button>
               <a-radio-button value="37">储能变流器({{ deviceTotal[37] }})</a-radio-button>
                <a-radio-button value="23">系统BMS({{ deviceTotal[23] }})</a-radio-button>
                <a-radio-button value="24">RackBMS({{ deviceTotal[24] }})</a-radio-button>
                </template>
            </a-radio-group>
            <div>
              <a-checkbox v-show="alarmStatus == '01'" ref="faultStatus" :defaultChecked="true"
                @change="faultStatusChange" class="faultStatusBox">只看持续</a-checkbox>
              <a-dropdown :disabled="isShowBatchBtn || isIncludeStorage" v-if="(type == '1' || type == '2') && alarmStatus == '01'">
                <a-menu slot="overlay" @click="handleImportMenuClick">
                  <a-menu-item key="1">添加挂牌</a-menu-item>
                  <a-menu-item key="2">取消挂牌</a-menu-item>
                </a-menu>
                <a-button v-has="'alarmCenter:batchEdit'" class='solar-eye-btn-primary'>批量挂牌<a-icon
                    type="down" /></a-button>
              </a-dropdown>
              <erp-button label="导出" perms="910108101107" @click="doExport"
                class="solar-eye-btn-primary-cancel export-btn"></erp-button>
            </div>
          </div>
        </div>
        <vxe-table :tree-config=treeObj :data="data" :height="tableHeight" ref="multipleTable"
          align="center" resizable border show-overflow highlight-hover-row size="small" :row-class-name="rowClassName"
          :seq-config="{ startIndex: (page - 1) * size }" @scroll="onScroll" @checkbox-all="handleSelectionChange"
          @checkbox-change="handleSelectionChange">
          <vxe-table-column type="checkbox" v-if="(type == '1' || type == '2') && alarmStatus == '01'" :width="60">
          </vxe-table-column>
          <vxe-table-column type="seq" :width="80" align="center" title="序号">
            <template v-slot="{ row, rowIndex }">
              <span :class="{ 'expand-index': rowIndex == '-1' }">{{ rowIndex != '-1' ? (((page - 1) * size) + rowIndex +
                1) : row.index }}</span>
            </template>
          </vxe-table-column>
          <vxe-table-column :tree-node="isShowTree" field="alarmGrade" :title="getTypeName + '等级'" width="110">
            <template #header="{ column }">
              <table-sort :isUp="isUp" :column="column" filed="alarmGrade" @sortChange="mySortChange" :downSort="downSort"
                :upSort="upSort" :sortFiled="sortFiled" />
            </template>
            <template v-slot="{ row }">
              <div style="display: flex; width: 80px; align-items: center;">
                <img v-if="row.alarmGrade && ['1', '2', '3', '4'].includes(row.alarmGrade)"
                  :src="getGradePng(row.alarmGrade)" style="margin-right: 3px;">
                <span :title="row.alarmGradeName || '--'">{{ row.alarmGradeName || '--' }}</span>
              </div>
            </template>
          </vxe-table-column>
          <vxe-table-column :visible="type == '4'" show-overflow="title" field="alarmRemarkName"
            :title="getTypeName + '类型'" min-width="120">
          </vxe-table-column>
          <vxe-table-column show-overflow="title" field="alarmReasonName" :title="getTypeName + '原因'" min-width="160"
            align="center">
            <template #default="{ row }">
              <div style="display: inline-block; align-items: center">

                <div v-if="row.tagName" style="margin: 4px;" @mouseover="showIcon(row, index)"
                  @mouseout="hideIcon(row, index)">
                  <div
                    style="display:flex; justify-content: center; align-items: center; color: #ff4d4f; border: 1px solid #ff4d4f; padding:0 4px; border-radius: 3px; margin-right: 3px; font-size: 12px; background: hsla(27.05882353, 100%, 60%, 0.1) !important;">
                    <span @mouseover="showIcon(row, index)" @mouseout="hideIcon(row, index)">{{ row.tagName }}</span>
                    <a-icon v-if="alarmStatus != '03'" @mouseover="showIcon(row, index)" @mouseout="hideIcon(row, index)"
                      @click="delReason(row)" style="display: flex;cursor: pointer; padding: 4px;" v-show="row.isShow"
                      type="close" />
                  </div>
                </div>
              </div>
              <span>{{ row.alarmReasonName }}</span>
            </template>
          </vxe-table-column>
          <vxe-table-column show-overflow="title" field="deviceNameShow" title="设备名称" min-width="120">
            <template #header="{ column }">
              <table-sort :isUp="isUp" :column="column" filed="deviceNameShow" @sortChange="mySortChange"
                :downSort="downSort" :upSort="upSort" :sortFiled="sortFiled" />
            </template>
            <template v-slot="{ row, rowIndex }">
              <span @click="expandTree(row, rowIndex)" :class="{ 'change-color': !row.id }">{{ row.deviceNameShow || '--'
              }}</span>
            </template>
          </vxe-table-column>
          <vxe-table-column show-overflow="title" field="deviceTypeName" title="设备类型" min-width="100">
          </vxe-table-column>
          <vxe-table-column show-overflow="title" field="psName" title="电站名称" min-width="200">
            <template #header="{ column }">
              <table-sort :isUp="isUp" :column="column" filed="psName" @sortChange="mySortChange" :downSort="downSort"
                :upSort="upSort" :sortFiled="sortFiled" />
            </template>
          </vxe-table-column>
          <vxe-table-column show-overflow="title" field="happenTime" title="发生时间" min-width="120">
            <template #header="{ column }">
              <table-sort :isUp="isUp" :column="column" filed="happenTime" @sortChange="mySortChange" :downSort="downSort"
                :upSort="upSort" :sortFiled="sortFiled" />
            </template>
          </vxe-table-column>
          <vxe-table-column :visible="dealStatus == '04'" show-overflow="title" field="distributeTime" title="派发时间"
            min-width="120">
            <template #header="{ column }">
              <table-sort :isUp="isUp" :column="column" filed="distributeTime" @sortChange="mySortChange"
                :downSort="downSort" :upSort="upSort" :sortFiled="sortFiled" />
            </template>
          </vxe-table-column>
          <vxe-table-column show-overflow="title" field="lastHappenTime" title=" 更新时间 " width="140">
            <template #header="{ column }">
              <table-sort :isUp="isUp" :column="column" filed="lastHappenTime" @sortChange="mySortChange"
                :downSort="downSort" :upSort="upSort" :sortFiled="sortFiled" />
              <a-popover placement="right">
                <template slot="content">
                  最新一次发生时间
                </template>
                <!-- <img class="info-icon" :src="getPng('info')"/> -->
                <div class="info-icon"><svg-icon iconClass="health-info"></svg-icon></div>
              </a-popover>
            </template>
            <template v-slot="{ row }">
              <span>{{ row.lastHappenTime || '--' }}</span>
            </template>
          </vxe-table-column>
          <vxe-table-column field="updateTimes" :visible="dealStatus == '01' || (type == '3' && dealStatus == '03')" title="次数" width="120">
            <template #header="{ column }">
              <table-sort :isUp="isUp" :column="column" filed="updateTimes" @sortChange="mySortChange"
                :downSort="downSort" :upSort="upSort" :sortFiled="sortFiled" />
              <a-popover placement="right">
                <template slot="content">
                  发生时间和更新时间之间的故障发生次数
                </template>
                <!-- <img class="info-icon" :src="getPng('info')"/> -->
                <span class="info-icon"><svg-icon iconClass="health-info"></svg-icon></span>
              </a-popover>
            </template>
            <template v-slot="{ row }">
              <span>{{ row.updateTimes || '--' }}</span>
            </template>
          </vxe-table-column>
          <vxe-table-column :visible="dealStatus == '01' || alarmStatus == '03'" show-overflow="title" field="faultStatus"
            title="故障状态" min-width="100">
            <template #header="{ column }">
              <table-sort :isUp="isUp" :column="column" filed="faultStatus" @sortChange="mySortChange"
                :downSort="downSort" :upSort="upSort" :sortFiled="sortFiled" />
            </template>
            <template v-slot="{ row }">
              <span :title="row.faultStatusName">{{ row.faultStatusName || '--' }}</span>
            </template>
          </vxe-table-column>
          <vxe-table-column :visible="dealStatus == '04'" show-overflow="title" field="finishedTime" title="闭环时间"
            min-width="120">
            <template #header="{ column }">
              <table-sort :isUp="isUp" :column="column" filed="finishedTime" @sortChange="mySortChange"
                :downSort="downSort" :upSort="upSort" :sortFiled="sortFiled" />
            </template>
          </vxe-table-column>
          <vxe-table-column :visible="dealStatus == '04'" show-overflow="title" field="closedType" title="闭环方式"
            min-width="120">
            <template #header="{ column }">
              <table-sort :isUp="isUp" :column="column" filed="closedType" @sortChange="mySortChange" :downSort="downSort"
                :upSort="upSort" :sortFiled="sortFiled" />
            </template>
            <template v-slot="{ row }">
              <span :title="row.closedTypeShow">{{ row.closedTypeShow }}</span>
            </template>
          </vxe-table-column>
          <vxe-table-column
            :visible="(type == '1' && !(['22', '23', '24', '25'].includes(userInfo.companyId))) || type == '3'"
            show-overflow="title" field="powerLoss" title="电量损失值(kWh)" min-width="140">
            <template #header="{ column }">
              <table-sort :isUp="isUp" :column="column" filed="powerLoss" @sortChange="mySortChange" :downSort="downSort"
                :upSort="upSort" :sortFiled="sortFiled" />
            </template>
            <template v-slot="{ row }">
              <span>{{ row.powerLoss === 0 ? 0 : row.powerLoss || '--' }}</span>
            </template>
          </vxe-table-column>
          <!-- type == '3' -->
          <!--  :visible="['1','2','3','4'].includes(type)"  -->
          <vxe-table-column show-overflow="title" field="opinion" title="处理建议" min-width="200">
          </vxe-table-column>

          <vxe-table-column :visible="dealStatus == '03'" show-overflow="title" field="orderStatus" title="工单状态"
            min-width="120">
            <template v-slot="{ row }">
              <span @click="workOrderDetail(row, 'alarm')" style="cursor: pointer;" class="blue">{{
                row.orderStatus }}</span>
            </template>
          </vxe-table-column>
          <vxe-table-column :visible="dealStatus == '03'" show-overflow="title" field="declareUser" title="派发人"
            min-width="120">
            <template #header="{ column }">
              <table-sort :isUp="isUp" :column="column" filed="declareUser" @sortChange="mySortChange"
                :downSort="downSort" :upSort="upSort" :sortFiled="sortFiled" />
            </template>
          </vxe-table-column>
          <vxe-table-column :visible="dealStatus == '03'" show-overflow="title" field="distributeTime" title="派发时间"
            min-width="120">
            <template #header="{ column }">
              <table-sort :isUp="isUp" :column="column" filed="distributeTime" @sortChange="mySortChange"
                :downSort="downSort" :upSort="upSort" :sortFiled="sortFiled" />
            </template>
          </vxe-table-column>
          <vxe-table-column fixed="right" title="操作" min-width="180">
            <template v-slot="{ row }">
              <!-- <erp-button  perms="910108101110" v-show="dealStatus == '04'" title="详情" icon="file-text"
                size="default" @click="workOrderDetail(row, 'alarm')" class="detail-btn">
              </erp-button> -->
              <div style="display: flex; align-items: center; justify-content: center;">
                <span title="详情" v-if="show(910108101110)">
                  <svg-icon v-show="dealStatus == '04'" class="health-icon" @click="workOrderDetail(row, 'alarm')"
                    iconClass="health-detail"></svg-icon>
                </span>
                <span title="分析" v-if="show(910108101101) && row.id">
                  <svg-icon @click="doAnalysis(row)" iconClass="health-analysis" class="health-icon"></svg-icon>
                </span>
                <span title="派发" v-if="show(910108101102)">
                  <svg-icon v-show="dealStatus == '01'" class="health-icon" :class="{disabled: !isDisplayBtn(row)}" @click="batchDistributeEvent(row, '1', '4')"
                    iconClass="health-flaw"></svg-icon>
                </span>
                <span v-if="(type == '1' || type == '2') && alarmStatus == '01' " title="挂牌" v-has="'alarmCenter:hangtag'">
                  <svg-icon v-show="dealStatus == '01'" class="health-icon" :class="{disabled: !isDisplayBtn(row)}" style="font-size: 16px;"
                    @click="showSelecteTag(row)" iconClass="add-tag"></svg-icon>
                </span>
                <span title="删除" v-if="show(910108101106)">
                  <svg-icon v-show="dealStatus == '01'" class="health-icon" @click="doDelete(row, '1')"
                    iconClass="health-delete"></svg-icon>
                </span>
              </div>
            </template>
          </vxe-table-column>
          <template v-slot:empty>
            <span>查询无数据</span>
          </template>
        </vxe-table>
        <!--分页组件-->
        <page-pagination :pageSize="size" :current="page" :total="total" @size-change="sizeChange" />
      </a-col>
      <drawer-view ref="orderForm" parentId="AlarmCenter" />
      <!-- 添加标签弹窗 -->
      <a-modal ref="activereview" title="设置设备挂牌" v-model="tagVisible" :maskClosable="false" centered @cancel="cancel"
        width="450px">
        <a-spin :spinning="loading">
          <a-form-model ref="backNullifyForm" :model="form" :rules="rules" :label-col="{ span: 0 }"
            :wrapper-col="{ span: 24 }">
            <a-form-item label="现场情况:" name="resource" class="slect-radio">
              <a-radio-group v-model="tagReason">
                <a-radio :disabled="item.secondTypeCode === '7' && isDisableBtn" v-for="item in dictOptions"
                  :key="item.secondTypeCode" :value="item.secondTypeCode">{{
                    item.secondName
                  }}</a-radio>
              </a-radio-group>
            </a-form-item>
          </a-form-model>
        </a-spin>
        <div slot="footer">
          <a-button size="default" class="solar-eye-btn-primary" :loading="loading" @click="commitData()">确定</a-button>
          <a-button size="default" :disabled="loading" @click="cancel()">取消</a-button>
        </div>
      </a-modal>
    </a-spin>
    <alarm-analysis ref="alarmAnalysis" @alarmAnalysisClose="alarmAnalysisClose" :tabName="tabName" :type="type"
      :deviceType="deviceType" />

  </div>
</template>
<script>
import { getAlarmEvents, exportAlarmEvents, getReasonByRemarkAndDeviceType, getDict, tagging, taggingList, getRemarkDynamic } from '@/api/health/AlarmEvents.js';
import { AlarmEventMixins } from './mixins/alarmEventMixins';
import initDict from '@/mixins/initDict';
import AlarmAnalysis from './modules/AlarmAnalysis';
import { mixin } from '@/utils/mixin.js';
import moment from 'moment';
import { USER_INFO } from '@/store/mutation-types';
import { getPsCategory } from '@/api/monitor/device';
export default {
  name: 'AlarmEvents',
  mixins: [initDict, AlarmEventMixins, mixin],
  components: {
    AlarmAnalysis
  },
  props: {
    type: {
      type: String,
      default: ''
    },
    activeTotal: {
      type: Number,
      default: 0
    },
    stationParams: {
      type: Object,
      default: () => {
        return {};
      }
    },
    tabName: {
      type: String,
      default: ''
    }
  },
  data () {
    return {
      isShowTree: true,
      isUp: true,
      isBatchTag: false, // 添加标签时候区分是否是批量  true 批量  false 单个
      isShowBatchBtn: true, // 批量按钮
      selectedRows: [], // 选中列表
      selectedRowData: [], // 打标签 选中的行数据
      dictOptions: [],
      isDisableBtn: false, // 行打标签时判断是否是组串未接 组串未接只限制在组串类型
      loading: false,
      tagReason: '', // 打标签项
      // 添加标签弹窗
      tagVisible: false,
      pageloading: false,
      psId: '',
      dealTotal: 0,
      separateTotal: 0,
      distributeTotal: 0,
      closedTotal: 0,
      tableHeight: 600,
      showSeparate: false,
      showDistribute: false,
      alarmType: '1',
      alarmTypes: [],
      deviceType: '',
      faultStatus: '1',
      searchTime: [],
      searchData: {},
      seprarateVisable: false,
      seprarateIds: '',
      nodeType: '',
      timeInterval: null,
      warningTypeOptions: [],
      elecEnsureType: ['0'],
      alarmCategoryOptions: [],
      alarmReasonOptions: [],
      allAlarmReasonOptions: [],
      alarmRemark: '',
      alarmReason: '',
      isolateReason: '',
      isolateReasonOptions: [],
      psName: '',
      closedType: '',
      deviceList: [],
      filterTypeList: [],
      alarmList: [],
      filterList: [],
      sameRemark: [],
      depCodes: '',
      clickedIds: [],
      alarmRemarkString: '',
      alarmRemarkArr: [],
      alarmRemarkChildren: [],
      alarmReasonString: '',
      userInfo: {},
      alarmStatus: '01',
      deviceTypeList: [],
      dealStatus: '01',
      firdtLoading: true,
      deviceTotal: {
        1: 0,
        3: 0,
        4: 0,
        10: 0,
        11: 0,
        66: 0,
        17: 0,
        7: 0,
        301: 0,
        5: 0,
        23: 0,
        24: 0,
        26: 0,
        37: 0
      },
      scrollHeight: 0,
      remarkList: [],
      categoryList: [],
      treeObj: null
    };
  },
  async created () {
    if (!window.comFromMonitor) {
      await this.getRemarkDynamic();
      let data = await this.getPsCategory();
      this.categoryList = data;
    }
    this.psName = this.stationParams.psName;
    this.depCodes = this.stationParams.orgCode;
    this.getTagDict();
    this.initData();
    this.getAlarmReasionOptions();
    this.timeInterval = setInterval(this.getList, 300000);
    this.userInfo = Vue.ls.get(USER_INFO);
  },
  activated () {
    if (window.comFromMonitor && !this.firdtLoading) {
      this.dealRouterEvent();
    }
    if (this.$refs.multipleTable) {
      this.$refs.multipleTable.loadData(this.data);
      setTimeout(() => {
        this.$refs.multipleTable.$el.querySelector('.vxe-table--body-wrapper').scrollTop = this.scrollHeight;
      }, 100);
    }
    if (this.$refs.alarmAnalysis.alarmAnalysisVisable) {
      this.$refs.alarmAnalysis.dealData();
    }
    this.firdtLoading = false;
  },
  watch: {
    type (val, old) {
      this.isShowTree = val == '3';
    }
  },
  mounted () {
    // 设置表格高度
    this.getTableHeight();
    window.onresize = () => {
      this.getTableHeight();
    };
  },
  computed: {
    'getTypeName' () {
      return this.type == '3' ? '低效' : (this.type == '4' ? '隐患' : '故障');
    },
    isShowMenu () {
      return this.$store.state.user.isShowMenu;
    },
    isIncludeStorage () {
      return this.selectedRows.filter(item => {
        return item.psCategory && item.psCategory.includes('Storage');
      }).length != 0;
    }
  },
  deactivated () {
    clearInterval(this.timeInterval);
    let self = document; let healthClean = self.getElementsByClassName('health-clean-icon');
    if (healthClean && healthClean.length > 0) {
      self.body.removeChild(healthClean[0]);
    }
    window.onresize = null;
  },
  beforeDestroy () {
    clearInterval(this.timeInterval);
    this.$nextTick(() => {
      let self = document; let healthClean = self.getElementsByClassName('health-clean-icon');
      if (healthClean && healthClean.length > 0) {
        self.body.removeChild(healthClean[0]);
      }
    });
    window.onresize = null;
  },
  methods: {

    moment,
    // 行选中事件
    handleSelectionChange ({ records }) {
      this.selectedRows = records;
      this.isShowBatchBtn = this.selectedRows.length === 0;
    },
    handleImportMenuClick (e) {
      const { key } = e; // 1 添加标签  2  删除标签
      if (key === '1') {
        this.isBatchTag = true;
        this.tagVisible = true;
      } else if (key === '2') {
        let params = {
          tag: '0',
          list: []
        };
        this.selectedRows.forEach(item => {
          params.list.push({
            id: item.id,
            psKey: item.psKey,
            alarmReason: item.alarmReason
          });
        });
        this.$confirm({
          title: '提示',
          content: '是否确认批量删除？',
          okText: '确定',
          cancelText: '取消',
          onOk: () => {
            taggingList(params).then(res => {
              this.$message.success(`操作成功`);
              this.isShowBatchBtn = true;
              this.getList(false);
            }).catch((e) => {
            });
          }
        });
      }
    },
    // 取消弹窗
    cancel () {
      this.tagVisible = false;
      this.tagReason = '';
    },
    // 提交数据-  1 单个打标签  2 多个打标签
    commitData () {
      // 批量打标签
      if (this.isBatchTag) {
        let params = {
          tag: this.tagReason,
          list: []
        };
        this.selectedRows.forEach(item => {
          params.list.push({
            id: item.id,
            psKey: item.psKey,
            alarmReason: item.alarmReason
          });
        });
        taggingList(params).then(res => {
          this.$message.success(`操作成功`);
          this.isShowBatchBtn = true;
          this.tagVisible = false;
          this.getList(false);
        }).catch((e) => {
        });
      } else {
        let params = {
          id: this.selectedRowData[0].id,
          tag: this.tagReason,
          psKey: this.selectedRowData[0].psKey,
          alarmReason: this.selectedRowData[0].alarmReason
        };
        tagging(params).then(res => {
          this.$message.success(`操作成功`);
          this.tagVisible = false;
          this.getList(false);
        }).catch((e) => {
        });
      }
    },
    // 显示选择标签
    showSelecteTag (row) {
      this.selectedRowData = [];
      this.selectedRowData.push(row);
      // 判断组串未接
      if (row && row.isString && row.isString === '1') {
        this.isDisableBtn = false;
      } else {
        this.isDisableBtn = true;
      }
      if (row.tag) {
        this.tagReason = row.tag.toString();
      } else {
        this.tagReason = '';
      }
      this.isBatchTag = false;
      this.tagVisible = true;
    },

    getTagDict () {
      getDict({ firstTypeCode: '0233' }).then(res => {
        this.dictOptions = res.result_data;
      }).catch((e) => {
      });
    },
    // 待派发-点击时序分析后表格行置灰
    rowClassName ({ row }) {
      let { clickedIds, dealStatus } = this;
      if (clickedIds.includes(row.id) && ['01', '03', '04'].includes(dealStatus)) {
        return 'health-row-clicked';
      }
    },
    /*
      表格列刷新
    */
    refreshColumn () {
      if (this.$refs.multipleTable) {
        this.$refs.multipleTable.refreshColumn();
      }
    },
    // 设置初始数据
    initData () {
      this.getDictMap('alarm_type,device_type,fault_status,alarm_stauts,elec_ensure_type,farm_scene_condition_query');
      this.dealRouterEvent();
    },
    // 删除原因
    delReason (row) {
      this.$confirm({
        title: '提示',
        content: '是否确认取消挂牌？',
        okText: '确定',
        cancelText: '取消',
        onOk: () => {
          let params = {
            id: row.id,
            tag: '0',
            psKey: row.psKey,
            alarmReason: row.alarmReason
          };
          tagging(params).then(res => {
            this.$message.success(`操作成功`);
            this.tagVisible = false;
            this.getList(false);
          }).catch((e) => {
          });
        }
      });
    },
    // 鼠标移入显示close
    showIcon (row) {
      this.$set(row, 'isShow', true);
    },
    // 鼠标移出隐藏close
    hideIcon (row) {
      this.$set(row, 'isShow', false);
    },
    dealRouterEvent () {
      if (window.comFromMonitor) {
        window.comFromMonitor = false;
        this.resetSearchData(false);
        this.psId = this.$route.params.psId;
        this.psName = this.$route.params.psName;
        this.categoryList = this.$route.params.categoryList;
        this.remarkList = this.$route.params.remarkList;
        console.log(this.categoryList, this.remarkList);
        this.nodeType = '3';
        this.faultStatus = '1';
        if (this.$refs.faultStatus) {
          this.$refs.faultStatus.checked = true;
        }
        this.pageChange(1, true);
      }
    },
    resetSearchData (refresh) {
      this.alarmStatus = '01';
      this.elecEnsureType = ['0'];
      this.closedType = '';
      this.alarmRemark = '';
      this.alarmRemarkString = '';
      this.alarmReason = '';
      this.alarmReasonString = '';
      this.searchTime = [];
      if (this.$refs.tree) {
        let node = this.$refs.tree.rootNode;
        this.setStationParams(node);
        this.$refs.tree.refresh = false;
        setTimeout(() => {
          this.$refs.tree.refresh = true;
        }, 300);
        this.$emit('resetParams');
        if (refresh) {
          this.pageChange(1, true);
        }
      }
      this.getTableHeight();
    },
    // 电站树变化事件
    getStationChange (val, node) {
      this.setStationParams(node);
      this.$nextTick(() => {
        this.pageChange(1, true);
      });
    },
    setStationParams (node) {
      this.psName = node.name;
      this.psId = node.id + '';
      this.depCodes = node.orgCode;
      if (node.isPsa == '0' && !node.isPs) {
        this.nodeType = 1;
      } else if (node.isPsa == '1') {
        this.nodeType = 2;
      } else if (node.isPs == 1) {
        this.nodeType = 3;
      } else {
        this.nodeType = '';
      }
      let stationParams = {
        psId: this.psId,
        psName: this.psName,
        nodeType: this.nodeType,
        orgCode: this.depCodes,
        parentId: node.parentId,
        parentType: node.parentType,
        parentName: node.parentName,
        parentOrgCode: node.parentOrgCode
      };
      this.$emit('stationChange', stationParams);
    },
    // 格式化查询、导出条件数据
    fomartSearchData () {
      let obj = {
        sortFiled: this.sortFiled,
        sortKind: this.sortKind,
        size: this.size,
        curPage: this.page,
        alarmStatus: this.alarmStatus,
        alarmRemark: this.getAlarmRemark(this.type),
        deviceType: this.deviceType.toString(),
        elecEnsureType: this.elecEnsureType.join(),
        alarmReason: this.alarmReasonString,
        isClosed: this.alarmStatus == '04' ? '1' : '0',
        closedType: this.closedType,
        depCodes: this.depCodes,
        faultStatus: this.faultStatus,
        tabCode: this.type
      };
      if (this.psId) {
        if (this.nodeType == '1') {
          obj.treeOrgId = this.psId;
        } else if (this.nodeType == '2') {
          obj.treePsaId = this.psId;
        } else {
          obj.treePsId = this.psId;
          if (this.stationParams.parentType == '1') {
            obj.treeOrgId = this.stationParams.parentId;
          } else {
            obj.treePsaId = this.stationParams.parentId;
          }
        }
      }
      if (this.searchTime.length > 0) {
        obj.startTime = this.searchTime[0].format('YYYY-MM-DD');
        obj.endTime = this.searchTime[1].format('YYYY-MM-DD');
      } else {
        obj.startTime = null;
        obj.endTime = null;
      }
      this.searchData = obj;
    },
    // 获取告警列表
    getList (refreshScroll) {
      this.fomartSearchData();
      this.$emit('searchDataChange', this.searchData);
      // this.getTabTotal();
      // this.pageloading = true;
      if (this.$refs.multipleTable && refreshScroll) {
        this.$refs.multipleTable.clearScroll();
      }
      this.dealStatus = this.alarmStatus;
      this.treeObj = null;
      getAlarmEvents(this.searchData).then(res => {
        if (res.result_code == '1') {
          if (res.result_data.rowCount == 0 && this.page != 1) {
            this.pageChange(1, true);
          } else {
            this.data = res.result_data.pageList;
            // 合并项加表示符
            if (this.data && this.data.length && this.type == '3' && this.deviceType == 10) {
              this.treeObj = { children: 'displayList' };
              this.data.forEach(item => {
                this.$set(item, 'isExpand', false);
                if (item.displayList && item.displayList.length) {
                  item.displayList.forEach((list, index) => {
                    this.$set(list, 'mergeRow', true);
                    this.$set(list, 'index', index + 1);
                  });
                }
              });
            }
            this.total = res.result_data.rowCount;
            this.$refs.multipleTable.loadData(this.data);
            this.$refs.multipleTable.refreshColumn();
          }
        }
        this.$emit('loadingEnd');
      }).catch(() => {
        this.$emit('loadingEnd');
      });
    },
    // 自定义表格排序事件
    mySortChange (sortKind, sortFiled, downSort, upSort) {
      this.sortKind = sortKind;
      this.sortFiled = sortFiled;
      this.downSort = downSort;
      this.upSort = upSort;
      this.page = 1;
      this.getList(true);
    },
    // 导出告警列表
    doExport () {
      this.fomartSearchData();
      this.pageloading = true;
      exportAlarmEvents(this.searchData).then(res => {
        this.$downloadFile({ fileBase64Code: res.fileBase64Code, fileName: res.fileName, fileType: res.fileType });
        this.pageloading = false;
      }).catch(() => {
        this.pageloading = false;
      });
    },
    // 打开分析页面
    doAnalysis (row) {
      let { clickedIds, dealStatus } = this;
      // 待处理、已派发、已闭环 状态的数据 点击需要置灰当前行
      if (clickedIds.indexOf(row.id) == -1 && ['01', '03', '04'].includes(dealStatus)) {
        this.clickedIds.push(row.id);
      }
      this.$refs.alarmAnalysis.init(row, dealStatus);
    },
    // 时序分析页面关闭回调事件
    alarmAnalysisClose (flag) {
      if (flag) {
        this.getList(false);
      }
    },
    // 只看持续勾选事件
    faultStatusChange (e) {
      this.$refs.faultStatus.checked = e.target.checked;
      this.faultStatus = e.target.checked ? '1' : undefined;
      this.paramsChange(this.faultStatus, 'faultStatus');
      this.pageChange(1, true);
    },
    getPng (name) {
      return require('@/assets/images/health/alarmEvents/' + name + '_' + this.navTheme + '.png');
    },
    getBg (name) {
      let isDark = this.navTheme == 'dark' ? '_dark' : '';
      return require('@/assets/images/health/alarmEvents/' + name + isDark + '.png');
    },
    getGradePng (grade) {
      if (grade == '4') {
        grade = '3';
      }
      return require('@/assets/images/health/alarmEvents/grade' + grade + '.png');
    },
    getTableHeight () {
      this.$nextTick(() => {
        this.tableHeight = document.body.offsetHeight - document.getElementsByClassName('solar-eye-search-content')[0].offsetHeight -
        (!this.isShowMenu ? 268 : 396);
      });
    },
    getAlarmRemark (tabCode) {
      let alarmRemark = '';
      switch (tabCode) {
        case '1':
          alarmRemark = this.remarkFilterByTab('1');
          break;
        case '2':
          alarmRemark = this.remarkFilterByTab('2');
          break;
        case '3':
          alarmRemark = this.remarkFilterByTab('4');
          break;
        case '4':
          if (this.alarmRemark) {
            alarmRemark = this.alarmRemarkString;
          } else {
            alarmRemark = this.remarkFilterByTab('3');
          }

          break;
      }
      return alarmRemark;
    },
    isDisplayBtn (row) {
      return row.psCategory ? !row.psCategory.includes('Storage') : true;
    },
    getRemarkDynamic () {
      getRemarkDynamic().then(res => {
        this.remarkList = res.result_data;
      });
    },
    remarkFilterByTab (type) {
      let arr = [];
      this.remarkList.forEach(item => {
        if (item.grade == type) {
          arr.push(item.remark);
        }
      });
      return arr.join(',');
    },
    getPsCategory () {
      return new Promise((resolve, reject) => {
        getPsCategory().then(res => {
          resolve(res.result_data || []);
        }).catch(() => {

        });
      });
    },
    setDeviceTotal (map) {
      this.deviceTotal = {
        1: map[1] || 0,
        3: map[3] || 0,
        4: map[4] || 0,
        10: map[10] || 0,
        11: map[11] || 0,
        66: map[66] || 0,
        17: map[17] || 0,
        7: map[7] || 0,
        301: map[301] || 0,
        5: map[5] || 0,
        23: map[23] || 0,
        24: map[24] || 0,
        26: map[26] || 0,
        37: map[37] || 0
      };
    },
    // 展开树
    expandTree (row, index) {
      if (row.isExpand) {
        this.$refs.multipleTable.setTreeExpand(this.data[index], false);
        this.$set(row, 'isExpand', false);
      } else {
        this.$refs.multipleTable.setTreeExpand(this.data[index], true);
        this.$set(row, 'isExpand', true);
      }
    },
    // 获取 故障、隐患、低效 原因
    getAlarmReasionOptions () {
      let deviceType = this.deviceType.toString() || undefined;
      if (deviceType == '3') {
        deviceType = '3,12';
      }
      if (deviceType == '17') {
        deviceType = '17,6';
      }
      getReasonByRemarkAndDeviceType({ tabCode: this.type, deviceType: deviceType }).then(res => {
        this.allAlarmReasonOptions = res.result;
        this.deviceTypeList = this.arrUnique(this.allAlarmReasonOptions, 'remarkName');
        this.alarmReasonOptions = this.arrUnique(this.allAlarmReasonOptions, 'reasonName');
      });
    },
    deviceTypeChange () { // 设备类型变化
      this.alarmReason = '';
      this.alarmReasonString = '';
      this.getAlarmReasionOptions();
      this.pageChange(1, true);
    },
    // 报警类型下拉框变化事件
    alarmCategoryChange (value, event) {
      this.alarmReason = '';
      this.alarmReasonString = '';
      if (value) {
        let sameNameArr = this.allAlarmReasonOptions.filter(item => {
          return item.remarkName == event.key;
        });
        let arr = Array.from(new Set(sameNameArr.map(item => {
          return item.remark;
        })));
        this.alarmRemarkString = arr.toString();
        let alarmReasonOptions = this.allAlarmReasonOptions.filter(item => { return arr.includes(item.remark); });
        this.alarmReasonOptions = this.arrUnique(alarmReasonOptions, 'reasonName');
      } else {
        this.alarmReasonOptions = this.arrUnique(this.allAlarmReasonOptions, 'reasonName');
      }
      this.pageChange(1, true);
    },
    // 报警原因下拉框变化事件
    alarmReasonChange (value, event) {
      if (value) {
        let sameNameArr = this.allAlarmReasonOptions.filter(item => {
          return item.reasonName == event.key;
        });
        this.alarmReasonString = Array.from(new Set(sameNameArr.map(item => {
          return item.reason;
        }))).toString();
      } else {
        this.alarmReasonString = '';
      }
      // this.pageChange(1, true);
    },
    alarmStatusChange (val, prop) {
      this.closedType = '';
      this.$emit('paramsChange', 'closedType', this.closedType);
      this.getTableHeight();
      this.paramsChange(val, prop);
    },
    paramsChange (val, prop) {
      this.$emit('paramsChange', prop, val);
      // this.pageChange(1, true);
    },
    onScroll () {
      this.scrollHeight = this.$refs.multipleTable.$el.querySelector('.vxe-table--body-wrapper').scrollTop;
    }
  }
};
</script>
<style lang="less" scoped>
:deep(.vxe-table.size--small .vxe-header--column:not(.col--ellipsis)) {
  padding: 0;
}

.expand-index {
  padding-left: 20px;
}

.change-color {
  color: var(--zw-conduct-color--normal);
  cursor: pointer;
}

.slect-radio {
  display: flex;
  align-items: flex-start;

  :deep(.ant-col-0) {
    display: block !important;
    line-height: 1.8;
  }

  :deep(.ant-form-item-control-wrapper) {
    flex: 1;

    .ant-radio-group {
      display: flex;
      flex-wrap: wrap;
      justify-content: space-between;
    }

    .ant-radio-wrapper {
      flex: 1;
      margin-bottom: 10px;
    }
  }
}

:deep(.vxe-table .vxe-header--row .vxe-cell) {
  padding: 0;
}

:deep(.vxe-table .vxe-header--column) {
  line-height: 20px;
}

.sort-head {
  cursor: pointer;
  padding: 2px 0 10px;
}

:deep(.sort-head span) {
  display: inline-block;
  margin-top: 10px;
}

:deep(.up-icon) {
  top: 12px;
}

:deep(.down-icon) {
  top: 21px;
}

.sort-head:hover {
  background: var(--zw-table-bg-color--hover);
}

.detail-btn {
  position: relative;
  top: -1px;
}

.disabled-btn {
  cursor: not-allowed;
}

.split-line {
  height: 1px;
  margin: 0 24px;
  background: var(--zw-divider-color--default);
}

.solar-eye-main-content .operation-btn {
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 32px;
}

.solar-eye-main-content .operation {
  margin-bottom: 12px
}

.solar-eye-main-content {
  padding: 20px 24px 16px;
  border-radius: 0 0 4px 4px;
}

.solar-eye-search-model {
  border-radius: 4px 4px 0 0;
}

.grade-image {
  position: absolute;
  top: 14px;
  left: 28px;
}

.info-icon {
  position: absolute;
  top: 12px;
  right: 10px;
  font-size: 14px;

  svg {
    color: var(--zw-text-2-color--default);
  }

}

.info-icon:hover {
  svg:hover {
    color: var(--zw-text-2-color--default);
  }
}

.health-icon {
  color: var(--zw-conduct-color--normal);
  font-size: 28px;
}
.health-icon.disabled {
  cursor: not-allowed;
  pointer-events: none;
  opacity: 0.5;
}
.health-icon:hover {
  color: var(--zw-primary-color--default) !important;
}
</style>
