import { postAction } from '@/api/manage';
const url = process.env.VUE_APP_Health_BASE_URL
// const url = 'http://10.5.5.253:8088'

export const getTaskList = (params) => postAction(url + '/standardAccess/getTaskList', params); // 参数列表查询
export const getParamList = (params) => postAction(url + '/standardAccess/getParamList', params); // 参数列表查询
export const exportParamList = (params) => postAction(url + '/standardAccess/exportParamList', params); // 导出
export const importParamMappingList = (params) => postAction(url + '/standardAccess/importParamMappingList', params); // 导入
export const getPsList = (params) => postAction(url + '/standardAccess/getPsList', params); // 电站信息
export const getDeviceList = (params) => postAction(url + '/standardAccess/getDeviceList', params); // 设备信息
export const getAccessList = (params) => postAction(url + '/standardAccess/getAccessList', params); // 对接配置列表
export const getParamGroupList  = (params) => postAction(url + '/standardAccess/getParamGroupList', params); // 对接协议配置-规则参数查询
export const addOrUpdateParamGroup  = (params) => postAction(url + '/standardAccess/addOrUpdateParamGroup', params); // 对接协议配置-规则参数新增或编辑
export const deleteParamGroup  = (params) => postAction(url + '/standardAccess/deleteParamGroup', params); // 对接协议配置-规则参数新增或编辑
export const exportPsList  = (params) => postAction(url + '/standardAccess/exportPsList', params); // 电站列表信息-导出电站列表
export const exportPsDetailList  = (params) => postAction(url + '/standardAccess/exportPsDetailList', params); // 电站列表信息-导出电站详情
export const exportDeviceList  = (params) => postAction(url + '/standardAccess/exportDeviceList', params); // 电站列表信息-导出设备信息