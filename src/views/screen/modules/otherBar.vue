<template>
<div class="solar-eye-screen-bar">
  <div class="solar-eye-screen-title">等效小时数</div>
  <div class="bar-model">
    <div class="bar-model" ref="otherBar">
    </div>
    <div class="axis-title"></div>
  </div>
</div>
</template>
<script>
import { getHoursRank } from '@/api/screen/screen';
var bg = 'image://data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAANsAAAAICAYAAAEHLrpXAAAAAXNSR0IArs4c6QAAALxJREFUWAljDKhYb8xAJ7ChI/AsIz0tpKtldApEuDXD1nOgZMIE9+YwZAzbmBuGcQX2kpAg06155f6fh3WqHI6R9+79P7Wkzo28ozluCMXuaG4bQpEFcyoop4HYo0UkLESGCA0qHkcjbYhEFsyZoNw2WqfBQmOUHg0BKocAKwvTl9Ut/jdhxo6WkLCQGKVHQ4DKIfD7zz+e0JqN6jBjRzMbLCRG6dEQoEEIIGe40cxGgwAeNXI0BJBDAJbhAHJ5NL/0xgnNAAAAAElFTkSuQmCC';
var bar = 'image://data:image/png;base64,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';
export default {
  methods: {
    hoursRank (echarts) {
      let self = this;
      getHoursRank({}).then(res => {
        let data = res.result_data ? res.result_data : {};
        let x_data = (data.hasOwnProperty('x') ? data.x : []);
        let rank = (data.data.value ? data.data.value : []);
        let unit = (data.data.unit ? data.data.unit : 'h');
        self.initOtherBar(echarts, x_data, rank, unit);
      });
    },
    initOtherBar (echarts, x_data, data, unit) {
      if (!this.$refs.otherBar) { // dom 不存在时不画
        return;
      }
      // 不重复初始化
      let otherBar = echarts.getInstanceByDom(this.$refs.otherBar);
      if (!otherBar) {
        otherBar = echarts.init(this.$refs.otherBar);
        this.$once('hook:beforeDestroy', function () {
          echarts.dispose(otherBar);
          otherBar = null;
        });
      }
      let option = {
        grid: {
          x: 90,
          y: 35,
          x2: 60,
          y2: 15
        },
        tooltip: {
          trigger: 'axis',
          show: true,
          axisPointer: {
            type: 'shadow',
            shadowStyle: {
              color: 'rgba(150,150,150,0.1)'
            }
          },
          backgroundColor: 'rgba(55, 71, 89, 0.9)',
          borderColor: '#69C7EB',
          borderWidth: '1',
          textStyle: {
            color: '#ffffff'
          },
          formatter: function (params) {
            return (
              "<span style='display:inline-block;margin-right:5px;border-radius:10px;width:9px;height:9px;background-color:rgba(36,207,233,0.9)'></span>" +
                params[0].name + ' : ' + params[0].value + unit + '<br/>'
            );
          }
        },
        xAxis: {
          show: false,
          type: 'value'
        },
        yAxis: [{
          triggerEvent: true,
          type: 'category',
          inverse: true,
          axisLabel: {
            show: true,
            textStyle: {
              color: 'rgba(255, 255, 255, 0.7)',
              fontSize: 12
            },
            formatter: function (label) {
              if (label.length > 6) {
                return label.substring(0, 6) + '...';
              }
              return label;
            }
          },
          splitLine: {
            show: false
          },
          axisTick: {
            show: false
          },
          axisLine: {
            show: false
          },
          data: x_data
        },
        {
          type: 'category',
          inverse: true,
          axisTick: 'none',
          axisLine: 'none',
          show: true,
          axisLabel: {
            textStyle: {
              color: '#ffffff',
              fontSize: '16'
            },
            formatter: '{value} {a|h}',
            rich: {
              a: {
                color: 'rgba(255,255,255,0.5)',
                fontSize: 12
              }
            },
            margin: 4
          },
          data: data
        }
        ],
        series: [{
          name: '等效小时数',
          type: 'pictorialBar',
          zlevel: 2,
          itemStyle: {
            normal: {
              color: new echarts.graphic.LinearGradient(0, 0, 1, 0, [{
                offset: 0,
                color: 'rgba(110, 220, 255, 1)'
              },
              {
                offset: 1,
                color: 'rgba(37, 147, 252, 1)'
              }
              ])
            }
          },
          barWidth: 15,
          barCategoryGap: '100%',
          data: data.map(function (item) {
            return {
              value: item,
              symbol: bar
            };
          })
        }, {
          type: 'pictorialBar',
          zlevel: 1,
          barWidth: 15,
          barCategoryGap: '100%',
          data: data.map(function (item) {
            return {
              realValue: item,
              symbol: bg,
              value: Math.max(...data)
            };
          })
        }]
      };
      otherBar.setOption(option, true);
      // 控制yAxis超出显示
      otherBar.on('mouseover', function (params) {
        if (params.componentType == 'yAxis') {
          $('.axis-title').css({
            'display': 'block'
          }).text(params.value);

          $('html').mousemove(function (event) {
            const top = event.offsetY - 30;
            const left = event.offsetX + 20;
            $('.axis-title').css('top', top + 'px').css('left', left + 'px');
          });
        }
      });
      otherBar.on('mouseout', function (params) {
        if (params.componentType == 'yAxis') {
          $('.axis-title').css('display', 'none');
        }
      });
    }
  }
};
</script>

<style lang="less" scoped>
  .bar-model {
    width: 100%;
    height: 92%;
    position: relative;
  }

  .axis-title {
    position: absolute;
    display: none;
    padding: 5px 20px 5px 5px;
    z-index: 200;
    background: rgba(0, 0, 0, 0.2);
    color: rgb(255, 255, 255);
  }
</style>
