<template>
  <!-- <a-transfer
    :data-source="dataSoruce"
    :target-keys="tagetKeys"
    show-search="true"
    :filter-option="(inputValue, item) => item.title.indexOf(inputValue) !== -1"
    :show-select-all="false"
    @change="onChange"
    :list-style="{
      width: '48%',
      height: '500px',
    }"
  > -->
  <a-transfer :data-source="dataSoruce"
              show-search
              :target-keys="tagetKeys"
              @change="handleChange"
              :render="(item) => `${item.modelCode}`"
              :list-style="{
                width: '48%',
                height: '500px',
              }">
    <!-- <template
      slot="children"
      slot-scope="{ props: { direction, filteredItems, selectedKeys }, on: { itemSelectAll, itemSelect } }"
    >
      <a-table
        :row-selection="getRowSelection({ selectedKeys, itemSelectAll, itemSelect })"
        :scroll="{ y: '400px' }"
        :pagination="false"
        :columns="direction === 'left' ? leftColumns : rightColumns"
        :data-source="filteredItems"
        size="small"
        :custom-row="
          ({ key }) => ({
            on: {
              click: () => {
                itemSelect(key, !selectedKeys.includes(key))
              },
            },
          })
        "
      />
    </template> -->
  </a-transfer>
</template>
<script>
import difference from 'lodash/difference';
const leftTableColumns = [
  {
    dataIndex: 'modelCode',
    title: '设备型号'
  }
];
const rightTableColumns = [
  {
    dataIndex: 'modelCode',
    title: '设备型号'
  }
];
const mockData = [];
for (let i = 0; i < 20; i++) {
  mockData.push({
    key: i.toString(),
    title: `content${i + 1}`,
    description: `description of content${i + 1}`
  });
}
export default {
  name: 'PointMageDrawer',
  props: {
    dataSoruce: {
      type: Array,
      default: () => {
        return [];
      }
    },
    defaultKeys: {
      type: Array,
      default: () => {
        return [];
      }
    }
  },
  data () {
    return {
      leftColumns: leftTableColumns,
      rightColumns: rightTableColumns,
      tagetKeys: []
    };
  },
  created () {
    this.tagetKeys = this.defaultKeys;
  },
  watch: {
    defaultKeys () {
      this.tagetKeys = this.defaultKeys;
    }
  },
  mounted () {},
  methods: {
    handleChange (targetKeys) {
      this.$emit('change', targetKeys);
      this.tagetKeys = targetKeys;
    },
    getRowSelection ({ selectedKeys, itemSelectAll, itemSelect }) {
      return {
        onSelectAll (selected, selectedRows) {
          const treeSelectedKeys = selectedRows.map(({ key }) => key);
          const diffKeys = selected
            ? difference(treeSelectedKeys, selectedKeys)
            : difference(selectedKeys, treeSelectedKeys);
          itemSelectAll(diffKeys, selected);
        },
        onSelect ({ key }, selected) {
          itemSelect(key, selected);
        },
        selectedRowKeys: selectedKeys
      };
    }
  }
};
</script>
