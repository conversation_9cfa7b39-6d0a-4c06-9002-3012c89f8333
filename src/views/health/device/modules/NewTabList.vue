<template>
  <div class="tab-list p-b-12">
    <div
      v-for="(item, index) in list"
      :key="index"
      class="tab-item pointer"
      @click="handleChange(index)"
      :class="{ active: activeKey === index }"
      v-has="item.auth"
    >
      <div class="left-img-box">
        <img
          class="width-100 height-100"
          :src="require('@/assets/images/health/alarmEvents/' + item.imageName + '-' + theme + '.png')"
          :alt="item.remark"
        />
      </div>
      <div class="right-text-box">
        <div class="text-box flex-between" style="line-height: 26px">
          <span class="text">{{ item.remark }}</span>
          <span class="total num-font-700">{{ total[item.keyValue] || 0 }}</span>
        </div>
      </div>
    </div>
    <div v-if="activeKey !== ''" class="active-tab" :style="{ left: left + 'px' }">
      <img
        class="width-100"
        :src="require('@/assets/images/health/alarmEvents/tab-active-' + theme + '.png')"
        alt="当前激活的导航栏"
      />
    </div>
  </div>
</template>
<script>
import { mapGetters } from 'vuex';

export default {
  name: 'NewTabList',
  props: {
    list: {
      type: Array,
      default: () => [],
      required: true
    },
    total: {
      type: Object,
      default: () => {
        return {
          1: 0,
          2: 0,
          3: 0,
          4: 0,
          5: 0,
          6: 0
        };
      }
    }
  },
  data () {
    return {
      activeKey: '',
      left: 0
    };
  },
  mounted () {
    window.addEventListener('resize', this.setLeft);
  },
  computed: {
    ...mapGetters(['theme'])
  },
  methods: {
    handleChange (index) {
      let { remark, keyValue } = this.list[index];
      if (this.activeKey === index) {
        this.activeKey = '';
        keyValue = '';
      } else {
        this.activeKey = index;
      }
      this.$emit('change', keyValue, remark);
    },
    async setLeft () {
      await this.$nextTick();
      const tabItemEl = $('.tab-item');
      const activeTabBg = $('.active-tab');
      this.left =
        (tabItemEl.innerWidth() - activeTabBg.innerWidth()) / 2 + 16 + this.activeKey * (tabItemEl.innerWidth() + 16);
    }
  },
  watch: {
    activeKey: {
      immediate: true,
      handler (val) {
        if (val !== '') {
          this.setLeft();
        }
      }
    }
  },
  beforeDestroy () {
    window.removeEventListener('resize', this.setLeft);
  }
};
</script>
<style scoped lang="less">
.tab-list {
  display: flex;
  position: relative;
  background: var(--zw-card-bg-color--default);

  .tab-item {
    display: flex;
    align-items: center;
    padding: 2px 14px 2px 8px;
    border-radius: 4px;
    width: 185px;
    height: 44px;
    background: var(--gradient-default);
    margin-left: 16px;
    font-variation-settings: "opsz" auto;

    &.active {
      .right-text-box {
        color: var(--zw-primary-color--default);
      }
    }

    &:hover {
      background: var(--gradient-hover);
    }

    .left-img-box {
      width: 40px;
      height: 40px;

      img {
        object-fit: cover;
      }
    }

    .right-text-box {
      margin-left: 4px;
      flex: 1;
      color: var(--zw-text-1-color--default);

      .text-box {
        .text {
          font-size: 14px;
          font-weight: 600;
        }

        .total {
          font-size: 16px;
          font-weight: bold;
        }
      }
    }
  }

  .active-tab {
    position: absolute;
    width: 104px;
    height: 24px;
    bottom: 9px;
    transition: 0.5s;
  }
}
</style>
