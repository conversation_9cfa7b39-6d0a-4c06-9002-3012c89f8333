<template>
  <div >
    <a-form-model ref="disposeForm" :model="dispose" :rules="rules" :labelCol="{ style:'width: 145px' }" :wrapperCol="{ style:'width: calc(100% - 145px)' }">
      <a-row>
        <template v-if="!disabled">
          <a-col :span='24'>
            <div class="order-dispose">
              <div class="title-box">
                <span class="before"></span>
                <span>执行信息</span>
              </div>
            </div>
          </a-col>
          <template v-if="dispose.orderType != '1'">
            <a-col :xl='8' :sm='12' :xs='24'>
              <a-form-model-item label="处理人" prop="handler">
                <span>{{dispose.handler}}</span>
              </a-form-model-item>
            </a-col>
            <a-col :span="24">
              <a-form-model-item label="情况说明" prop="orderResolveRemark">
                <a-textarea :max-length="255" v-model="dispose.orderResolveRemark" :title="dispose.orderResolveRemark" placeholder="请输入" style="width:100%" />
              </a-form-model-item>
            </a-col>
            <a-col :span="24">
              <a-form-model-item label="附件">
                <uploadFile @change="validateDocsField" @set="validateDocsField" v-model="dispose.resolveDocs" :maxNum="5" :tipIsInline="true" tip="最多上传5个文件,且上传的附件最大不超过10MB!">上传</uploadFile>
              </a-form-model-item>
            </a-col>
          </template>
          <template v-else>
            <a-col :xl='8' :sm='12' :xs='24'>
              <a-form-model-item label="工作内容" prop="workContent">
                <a-select v-model="dispose.workContent" @change="workContentChange" size="default" placeholder="请选择">
                  <a-select-option v-for="item in dictMap.farm_order_work_content" :key="item.dataValue" :value="item.dataValue">
                    {{item.dispName}}
                  </a-select-option>
                </a-select>
              </a-form-model-item>
            </a-col>
            <a-col :xl='16' :sm='12' :xs='24' v-if="isToTicket">
              <a-form-model-item label="关联两票" prop="ticketText">
                <template v-if="dispose.ticketText">
                  <span class="handleTypeStatus">【{{dispose.handleTypeLabel}}】</span>
                  <span @click="toTicketEven()">
                  {{ dispose.ticketText}}
                </span>
                </template>
                <template v-else>
                  {{ '请前往手机端开具两票' }}
                </template>
              </a-form-model-item>
            </a-col>
            <a-col :span="24">
              <a-form-model-item label="处理结果" prop="resolveResult">
                <a-radio-group v-model="dispose.resolveResult">
                  <a-radio value="2">已完成</a-radio>
                  <a-radio value="1">待协调</a-radio>
                </a-radio-group>
              </a-form-model-item>
            </a-col>
            <a-col :xl='8' :sm='12' :xs='24' v-if="isDiagnosis=='4' ||dispose.isFarm=='1'">
              <a-form-model-item label="现场情况" prop="sceneCondition">
                <a-select v-model="dispose.sceneCondition" placeholder="请选择" style="width:100%">
                  <a-select-option v-for="item in sceneIsList" :key="item.key" :value="item.codeValue" :title="item.dispName">
                    {{item.dispName}}
                  </a-select-option>
                </a-select>
              </a-form-model-item>
            </a-col>
            <a-col :xl='8' :sm='12' :xs='24'>
              <a-form-model-item label="故障原因" prop="faultReason">
                <a-select v-model="dispose.faultReason" placeholder="请选择" style="width:100%">
                  <a-select-option placeholder="请选择" v-for="item in faultResonList" :key="item.key" :value="item.codeValue"
                                   :title="item.dispName">
                    {{ item.dispName }}
                  </a-select-option>
                </a-select>
              </a-form-model-item>
            </a-col>
            <a-col :span="24">
              <a-form-model-item label="情况说明" prop="orderResolveRemark">
                <a-textarea :max-length="255" v-model="dispose.orderResolveRemark" :title="dispose.orderResolveRemark" placeholder="请输入" style="width:100%" />
              </a-form-model-item>
            </a-col>
            <a-col :span="24">
              <a-form-model-item label="附件" prop="resolveDocs">
                <uploadFile @change="validateDocsField" @set="validateDocsField" v-model="dispose.resolveDocs" :maxNum="5" :tipIsInline="true" tip="最多上传5个文件,且上传的附件最大不超过10MB!">上传</uploadFile>
              </a-form-model-item>
            </a-col>
          </template>
        </template>
        <template v-else>
          <detail-layout v-if='[1, 11].includes(dispose.orderType)' :labelList="disposeDetail" :form="dispose" title="执行信息">
            <template v-slot:annex>
              <a-col :span="24" class="detail_layout_content">
                <span class="left">附件</span>
                <span class="right">
                  <div class="file-div detail-file">
                    <div class="file-label">签到：</div>
                    <file-upload-view v-if="dispose.attachmentsForCheckin && dispose.attachmentsForCheckin.length > 0"
                       class="file-upload" :disabled="true" v-model="dispose.attachmentsForCheckin" listType="text"/>
                    <span v-else>--</span>
                  </div>
                  <div class="file-div detail-file">
                    <div class="file-label">处理前：</div>
                    <file-upload-view v-if="dispose.attachmentsBeforeTaskProcessing && dispose.attachmentsBeforeTaskProcessing.length > 0"
                      :disabled="true" v-model="dispose.attachmentsBeforeTaskProcessing" listType="text"/>
                    <span v-else>--</span>
                  </div>
                  <div class="file-div detail-file">
                    <div class="file-label">处理后：</div>
                    <file-upload-view v-if="dispose.resolveDocs && dispose.resolveDocs.length > 0" :disabled="true" v-model="dispose.resolveDocs" listType="text"/>
                    <span v-else>--</span>
                  </div>
                </span>
              </a-col>
            </template>
          </detail-layout>
          <detail-layout v-else :labelList="noFarmDisposeDetail" :form="dispose" title="执行信息"></detail-layout>
        </template>
      </a-row>
    </a-form-model>
    <!-- 两票新增、编辑、详情-->
    <ticket-form :visible.sync="detailVisible" :row.sync="row" parentId="parentId" />
    <drawer-view ref="orderForms" @cancel="getTicketInfo" parentId="parentId" />
  </div>
</template>

<script>
import { getTicketInfo, deleteTicket } from '@/api/common_gy/workOrder.js';
import uploadFile from '@/components/common_gy/uploadFiles';
import moment from 'moment';
import initDict from '@/mixins/initDict';
import ticketForm from '../../../../huTicket/ticketForm';
export default {
  name: 'orderDispose',
  components: {
    ticketForm,
    uploadFile
  },
  mixins: [initDict],
  props: {
    /* 抽屉挂在的DOM节点ID，必要参数 */
    parentId: {
      type: String,
      required: true,
      default: ''
    },
    disabled: {
      type: Boolean,
      default: false
    },
    isDiagnosis: {
      default: false,
      type: [Boolean, String]
    },
    isFarm: {
      default: '',
      type: [String, Number]
    }
  },
  data () {
    return {
      // 是否需要开票
      isToTicket: false,
      detailVisible: false,
      row: {},
      // 普通表单
      dispose: {
        id: undefined,
        orderType: undefined,
        workContent: undefined, // 工作内容
        workContentCopy: undefined, // 工作内容备份
        ticketText: undefined, // 关联两票
        handleTypeLabel: undefined, // 关联两票状态
        ticketId: undefined, // 两票id
        flowSts: undefined, // 两票流程状态
        findTime: undefined, // 缺陷发现时间
        actualTimeResolve: undefined, // 实际消除时间
        resolveResult: '2', // 处理结果
        orderResolveRemark: undefined, // 情况说明
        faultCapacityNum: undefined, // 故障停运容量
        repairTime: undefined, // 累计修复时间
        lossNum: undefined, // 累计损失电量
        overdueReason: undefined, // 逾期原因
        resolveDocs: [], // 附件
        attachmentsBeforeTaskProcessing: [], // 处理前附件
        sceneCondition: '',
        faultReason: ''
      },
      rules: {
        workContent: [{
          required: true,
          message: '请选择工作内容'
        }], // 工作内容
        ticketText: [{
          required: true,
          message: '当前工作内容需开具工作票'
        }], // 关联两票
        actualTimeResolve: [{
          required: true,
          message: '请选择实际消除时间'
        }], // 实际消除时间
        resolveResult: [{
          required: true,
          message: '请选择处理结果'
        }], // 处理结果
        orderResolveRemark: [{
          required: true,
          message: '请输入情况说明'
        }], // 情况说明
        faultCapacityNum: [{
          required: true,
          message: '请输入故障停运容量'
        }], // 故障停运容量
        repairTime: [{
          required: false,
          message: '请输入累计修复时间'
        }], // 累计修复时间
        lossNum: [{
          required: true,
          message: '请输入累计损失电量'
        }], // 累计损失电量
        sceneCondition: [{
          required: true,
          message: '请选择现场情况',
          trigger: 'change'
        }],
        resolveDocs: [{
          required: true,
          message: '请选择需要上传的文件',
          trigger: 'change'
        }],
        overdueReason: [{
          required: false,
          message: '请填写逾期原因'
        }]
      },
      noFarmDisposeDetail: [
        { label: '处理人', key: 'handler' },
        { label: '情况说明', key: 'orderResolveRemark', span: 24, lineClamp: 3 },
        { label: '附件', key: 'resolveDocs', span: 24, type: 'file:text' }
      ],
      disposeDetail: [
        { label: '工作内容', key: 'workContentLabel' },
        {
          label: '关联两票',
          key: 'ticketText',
          func: (params) => {
            if (params.orderType == 1) {
              return ['1', '2', '3', '4', '5', '6', '7', '8', '9', '10'].includes(params.workContent);
            } else {
              return ['4', '5', '6'].includes(params.workContent);
            }
          }
        },
        { label: '处理结果', key: 'resolveResultName' },
        {
          label: '现场情况',
          key: 'sceneConditionName',
          func: (params) => {
            return params.isDiagnosis == '4' || params.isFarm == '1';
          }
        },
        { label: '故障原因',
          key: 'faultReasonLabel',
          func: (params) => {
            return params.orderType == 1;
          }
        },
        { label: '低效原因',
          key: 'faultReasonLabel',
          func: (params) => {
            return params.orderType == 11;
          }
        },
        { label: '情况说明', key: 'orderResolveRemark', span: 24, lineClamp: 3 },
        { label: '反馈时间', key: 'feedbackTime' },
        { label: '实际消除时间', key: 'actualTimeResolve' },
        {
          label: '累计修复时间(h)',
          key: 'repairTime',
          func: (params) => {
            return params.isFarm == '1';
          }
        },
        {
          label: '故障停运容量(MWp)',
          key: 'faultCapacityNum',
          func: (params) => {
            return params.isFarm != '1';
          }
        },
        {
          label: '累计修复时间(h)',
          key: 'repairTime',
          func: (params) => {
            return params.isFarm != '1';
          }
        },
        {
          label: '累计损失电量(万KWh)',
          key: 'lossNum',
          func: (params) => {
            return params.isFarm != '1';
          }
        },
        { slot: 'annex' },
        {
          label: '逾期原因',
          key: 'overdueReason',
          func: (params) => {
            return params.isFarm != '1';
          }
        }
      ]
    };
  },
  created () {
    this.getDictMap('care_scene_condition,farm_fault_reason,farm_order_work_content');
  },
  computed: {
    sceneIsList () {
      if (this.isDiagnosis != '4') {
        return this.dictMap.care_scene_condition.filter(item => {
          return item.dataValue != '5';
        });
      } else {
        return this.dictMap.care_scene_condition;
      }
    },
    faultResonList () {
      return this.dictMap.farm_fault_reason;
    }
  },
  watch: {
    // 监听工单处理现场情况，是真实故障关账原因必填，反之；
    dispose: {
      immediate: true,
      deep: true,
      handler: function (val, old) {
        if (val.sceneCondition == '1') {
          this.$set(this.rules, 'faultReason', [{
            required: true,
            message: '请选择故障原因'
          }]);
        } else {
          this.$set(this.rules, 'faultReason', [{
            required: false
          }]);
        }
      }
    }
  },
  methods: {
    moment,
    // 更新数据
    updataDispose (order) {
      let dispose = JSON.parse(JSON.stringify(order));
      dispose.isDiagnosis = this.isDiagnosis;
      delete dispose.processInstanceId;
      delete dispose.taskId;
      if (!dispose.resolveResult) {
        dispose.resolveResult = '2';
      }
      if (!dispose.sceneCondition) {
        dispose.sceneCondition = '1';
      }
      Object.assign(this.dispose, dispose);
      if (dispose && dispose.hasOwnProperty('realRecoverTime')) {
        this.dispose.actualTimeResolve = dispose.realRecoverTime;
      } else {
        this.dispose.actualTimeResolve = moment().format('YYYY-MM-DD HH:mm');
      }
      if (!this.dispose.repairTime) {
        this.getRepairTime();
      }
      this.getTicketInfo();
    },
    // 设置repairTime
    getRepairTime () {
      if (this.dispose.actualTimeResolve && this.dispose.findTime) {
        let time = (new Date(this.dispose.actualTimeResolve).getTime()) - (new Date(this.dispose.findTime).getTime());
        this.dispose.repairTime = (time / 1000 / 60 / 60).toFixed(2);
      } else {
        this.dispose.repairTime = '';
      }
    },
    // 保存校验
    saveOrderDispose (type) {
      let actualTimeResolve = this.dispose.actualTimeResolve;
      if (this.isFarm != '1') {
        this.rules.overdueReason[0].required = !this.disabled && actualTimeResolve && this.dispose.predictPlanTimeLimit && (new Date(actualTimeResolve).getTime() > new Date(this.dispose
          .predictPlanTimeLimit).getTime());
      }
      return new Promise((resolve, reject) => {
        this.$refs.disposeForm.validate(valid => {
          if (valid) {
            resolve(this.dispose);
          } else {
            reject(new Error());
          }
        });
      });
    },
    // 取消检验信息
    clearValidate () {
      Object.assign(this.dispose, this.$options.data().dispose);
      this.$refs['disposeForm'].resetFields();
      this.$refs['disposeForm'].clearValidate();
    },
    // 实际消除时间不可小于发现时间
    disabledRecoverTime (endValue) {
      const startValue = moment(this.dispose.findTime).format('YYYY-MM-DD HH:mm');
      if (!endValue || !startValue) {
        return false;
      }
      return moment(endValue, 'YYYY-MM-DD HH:mm').valueOf() <= moment(startValue, 'YYYY-MM-DD HH:mm').valueOf();
    },
    // 手动验证附件
    validateDocsField () {
      this.$refs.disposeForm.validateField('resolveDocs');
    },
    // 所选工作内容是否需要开票
    workContentChange (value, option) {
      let self = this;
      if (self.dispose.ticketText) {
        self.$confirm({
          title: '切换工作内容已有工作票将作废，确认是否继续?',
          okText: '确定',
          cancelText: '取消',
          onOk () {
            self.dispose.workContentCopy = value;
            let requestMap = {
              'workOrderId': self.dispose.id
            };
            deleteTicket(requestMap).then(res => {
              self.isToTicketInfo(value);
              self.dispose.ticketText = undefined;
              self.dispose.handleTypeLabel = undefined;
              self.$message.success('作废成功');
            });
          },
          onCancel () {
            self.dispose.workContent = self.dispose.workContentCopy;
          }
        });
      } else {
        self.isToTicketInfo(value);
      }
    },
    toTicketEven () {
      if (this.dispose.ticketTypeDtl == '8') {
        this.detailVisible = true;
        this.row = { 'id': this.dispose.ticketId };
      } else {
        this.$refs.orderForms.init('3', { 'id': this.dispose.ticketId }, '/ticket/ticketManage/modules/ticketForm');
      }
    },

    // 查询关联两票信息 工作内容
    getTicketInfo () {
      let map = {
        workOrderId: this.dispose.id
      };
      getTicketInfo(map).then(res => {
        if (res.result) {
          Object.assign(this.dispose, res.result);
          this.dispose.workContentCopy = res.result.workContent;
          this.isToTicketInfo(this.dispose.workContent);
        }
      });
    },
    // 是否需要开票
    isToTicketInfo (value) {
      this.isToTicket = ['1', '2', '3', '4', '5', '6', '7', '8', '9', '10'].includes(value);
    }
  }
};
</script>

<style lang="less" scoped>
:deep(.ant-form-item) {
  width: 100%;
  display: inline-flex;
}
.handleTypeStatus {
  border-radius: 5px;
  margin-left: 10px;
  color: rgb(255, 138, 31);
}
.file-div {
  display: flex;
  .file-label {
    width: 56px;
    text-align: right;
  }
  .file-upload {
    padding-top: 6px;
  }
}
.detail-file {
  :deep(.ant-upload-list-item) {
    margin-top: 0;
  }
}
</style>
