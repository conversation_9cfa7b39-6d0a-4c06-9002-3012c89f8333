<template>
   <div class='single-select'  >
      <div v-for='(item,index) in list' :key='index'  class='single-select-item' v-error='item' :data-required='item ? false : true' :data-msg="`${projectIndex+1}-${tableItemIndex+1}`" >
        <a-input style='width: 80%' v-model='list[index]' placeholder='请输入'/>
        <span class='btn-box'>
         <a-icon type="plus-circle" v-if='list.length < 10' @click='addSelectItem'/>
         <a-icon type="minus-circle" v-if='list.length > 1' @click='removeSelectItem(index)'/>
       </span>
      </div>
   </div>
</template>

<script>
export default {
  props: {
    list: {
      type: Array
    },
    projectIndex: {
      type: Number
    },
    tableItemIndex: {
      type: Number
    }
  },
  methods: {
    addSelectItem () {
      this.list.push(null);
    },
    removeSelectItem (index) {
      this.list.splice(index, 1);
    }
  }
};
</script>

<style lang='less' scoped>
.single-select{
  .single-select-item{
    &:not(:last-child){
      margin-bottom: 8px;
    }
    .btn-box{
      width: 20%;
      display: inline-block;
      padding-left: 12px;
      .anticon:not(:last-child){
        margin-right: 8px;
      }
    }
  }
}
</style>
