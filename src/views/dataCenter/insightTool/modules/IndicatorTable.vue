<template>
    <vxe-table class="indicator-table my-table" :height="tableHeight - 20" :style="{width:tableWidth}" ref="xTable"
        :data="dataSource" resizable show-overflow>
        <vxe-table-column v-for="item in Object.keys(columnMap)" :key="item" :field="item.key" :title="columnMap[item]"
            :min-width="120" show-overflow="title">
            <template #default="{ row }">
                <span>{{ row[item] || '--' }}</span>
            </template>
        </vxe-table-column>
    </vxe-table>
</template>

<script>
import { getIndexTableApi } from '@/api/dataCenter';
import tableHeight from '@/mixins/tableHeightAndSearchModel';
import { mutationObserverFun } from '../data';
import * as XLSX from 'xlsx';
import moment from 'moment';
// 指标表格
export default {
  name: 'IndicatorTable',
  mixins: [tableHeight],
  props: {
    indicatorChartParams: {
      type: Object
    }
  },
  inject: ['updateRightContentLoading'],
  data () {
    return {
      dataSource: [],
      columnMap: {},
      tableWidth: 0
    };
  },
  mounted () {
    const targetNode = document.querySelector('.left-area');
    mutationObserverFun(targetNode, () => {
      this.tableWidth = `calc(100vw - ${$('.left-area').width() + 100}px)`;
      this.$refs.xTable && this.$refs.xTable.refreshColumn();
    });
    this.tableWidth = `calc(100vw - ${$('.left-area').width() + 100}px)`;
  },
  methods: {
    async init (params, tabType, dataSource, columnMap) {
      if (tabType === 1) {
        // 电站分析
        this.updateRightContentLoading(true);
        const res = await getIndexTableApi(params);
        const result = res.result_data;
        if (params.indexType == 303) {
          // 如果是温度分析，按照从大到小排列
          this.dataSource = result.resultData.reverse();
        } else {
          this.dataSource = result.resultData;
        }
        this.columnMap = result.columnMap;
        this.updateRightContentLoading(false);
        return res;
      } else {
        this.updateRightContentLoading(true);
        setTimeout(() => {
          // 设备分析
          this.dataSource = Object.freeze(dataSource);
          this.columnMap = Object.freeze(columnMap);
          this.updateRightContentLoading(false);
        }, 500);
        return 'success';
      }
    },
    getWidth (title) {
      if (!title) {
        return null;
      }
      return ((title.length / 4 + 1) * 60);
    },
    exportExcel () {
      const tHeads = Object.values(this.columnMap);
      const tHeadKeys = Object.keys(this.columnMap);
      const tBodys = this.dataSource.reduce((acc, cur) => {
        const itemBody = tHeadKeys.reduce((inner, innerKey) => {
          inner.push(cur[innerKey]);
          return inner;
        }, []);
        acc.push(itemBody);
        return acc;
      }, []);
      const aoa = [tHeads, ...tBodys];
      var ws = XLSX.utils.aoa_to_sheet(aoa);
      /* create workbook and export */
      var wb = XLSX.utils.book_new();
      XLSX.utils.book_append_sheet(wb, ws, 'Sheet1');
      const currentDate = moment().format('YYYY年MM月DD日HH时mm分');
      XLSX.writeFile(wb, `${currentDate}.xlsx`);
    }
  },
  computed: {

  }
};
</script>

<style lang="less" scoped>
.indicator-table {
    margin-top: 12px;
}
</style>
