// 引入相关文件
import { axios } from '@/utils/erp-request';
import { postAction } from '@/api/manage';
const baseSystemUrl = process.env.VUE_APP_API_BASE_URL;
// 根据用户id查询项目树-反查
export function getErpPjPsaTreeByUserId (data) {
  return axios({
    url: '/system/v1/erpProjectManager/getErpPjPsaTreeByUserId',
    method: 'post',
    data
  });
}

// 获取所有项目树-反查
export function getAllPjPsaTree (data) {
  return axios({
    url: '/system/v1/erpProjectManager/getAllPjPsaTree',
    method: 'post',
    data
  });
}
// 绩效树
export const getKpiDepTree = (params) => postAction(baseSystemUrl + '/system/v1/departPsaTree/getKpiDepTree', params);
// 户用电站
// const baseUrl ="http://192.168.159.79:8080"
// const getTreeList = (params) => postAction(baseUrl + "/system/v1/departPsaTree/getDepartPsaTree", params);
const getTreeList = (params) => postAction(baseSystemUrl + '/system/v1/departPsaTree/getDepartPsaTreeLazy', params);
const getDepartPsaTree = (params) => postAction(baseSystemUrl + '/system/v1/departPsaTree/getDepartPsaTree', params);
const searchTreeList = (params) => postAction(baseSystemUrl + '/system/v1/departPsaTree/getDepartPsaTreeLazySearch', params);
const getDepartPsaCount = (params) => postAction(baseSystemUrl + '/system/v1/departPsaTree/getDepartPsaCount', params);
export {
  getTreeList,
  searchTreeList,
  getDepartPsaTree,
  getDepartPsaCount
};
