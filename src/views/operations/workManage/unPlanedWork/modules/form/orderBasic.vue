<template>
  <a-form-model :model="form" ref="baseForm" :rules="isEdit ? rules:{}" :labelCol="{ style:'width: 120px' }" :wrapperCol="{ style:'width: calc(100% - 120px)' }">
    <a-row :gutter="24">
      <a-col :span="24">
        <a-form-model-item label="基本信息" class="title-label">
          <div style="width: 100%;text-align: right;">
            <!-- v-show="resultObj.buttonType!='4'" -->
            <!-- <throttle-button label="创建计划" :disabled="form.planId" @click="$refs.selfModel.init('1',{'id': resultObj.id})" /> -->
          </div>
        </a-form-model-item>
      </a-col>
      <template v-if="baseInfo.taskType!='10'">

      <a-col :xl='8' :sm='12' :xs='24'>
        <a-form-model-item label="缺陷类别" prop="faultClassify">
          <a-input disabled :value="getFaultClassify" style="width: 100%;"/>
        </a-form-model-item>
      </a-col>
      <!-- <a-col :xl='8' :sm='12' :xs='24'>
        <a-form-model-item label="关联计划">
          <a @click="$refs.selfModel.open(3,form.planId)" :disabled="form.planId?false:true">查看详情</a>
        </a-form-model-item>
      </a-col> -->
        <a-col :xl='8' :sm='12' :xs='24'>
        <a-form-model-item label="设备状态">
          <a-input disabled :value="form.twoDeviceRunStsName" style="width: 100%;"/>
        </a-form-model-item>
      </a-col>
       <a-col :xl='8' :sm='12' :xs='24'>
        <a-form-model-item label="缺陷来源">
          <a-input disabled :value="form.orderFrom" style="width: 100%;"/>
        </a-form-model-item>
      </a-col>
      <a-col :xl='8' :sm='12' :xs='24'>
        <a-form-model-item label="电站名称">
          <a-input disabled :value="form.psaName" :title="form.psaName" style="width: 100%;"/>
        </a-form-model-item>
      </a-col>
     <a-col :xl='8' :sm='12' :xs='24'>
        <a-form-model-item label="故障名称" prop="faultName">
          <a-input disabled :value="form.faultName" style="width: 100%;"/>
        </a-form-model-item>
      </a-col>
       <a-col :xl='8' :sm='12' :xs='24' >
        <a-form-model-item label="实体电站" :style="{opacity:form.orderSource=='4'?1:0}">
          <a-input disabled :value="form.realPsaName" style="width: 100%;"/>
        </a-form-model-item>
      </a-col>
      <a-col :xl='8' :sm='12' :xs='24'>
        <a-form-model-item label="设备类型"  prop="deviceTypeList">
            <g-cascader v-model="form.deviceTypeList" :options="options" style="width: 100%;" v-if="isEdit" @change="deviceTypeChange" />
          <a-input v-else v-model="form.deviceTypeName" disabled :title="form.deviceTypeName" style="width: 100%;"/>
        </a-form-model-item>
      </a-col>
      <a-col :xl='8' :sm='12' :xs='24'>
        <a-form-model-item label="设备名称">
          <a-input disabled :value="form.deviceName" :title="form.deviceName" style="width: 100%;"/>
        </a-form-model-item>
      </a-col>
      <a-col :xl='8' :sm='12' :xs='24'>
        <a-form-model-item label="设备编号">
          <a-input disabled :value="form.deviceUuid" style="width: 100%;"/>
        </a-form-model-item>
      </a-col>

      <a-col :xl='8' :sm='12' :xs='24'>
        <a-form-model-item label="发现人">
          <a-input disabled :value="form.findUserName" style="width: 100%;"/>
        </a-form-model-item>
      </a-col>
      <a-col :xl='8' :sm='12' :xs='24'>
        <a-form-model-item label="发现时间">
          <a-input disabled :value="form.findTime" style="width: 100%;"/>
        </a-form-model-item>
      </a-col>
        <a-col :xl='8' :sm='12' :xs='24'>
        <a-form-model-item label="预计消除时间" prop="predictPlanTimeLimit">
          <a-input disabled v-model="form.predictPlanTimeLimit" style="width: 100%;"/>
        </a-form-model-item>
      </a-col>
      <template  v-if="(type == '7') || (form.resolveResult && ['6','7','8','9'].includes(form.orderStatus)) ||Number(form.flowSts)>=3">
        <a-col :xl='8' :sm='12' :xs='24'>
        <a-form-model-item label="消除人">
          <a-input disabled :value="form.eliminateName" style="width: 100%;"/>
        </a-form-model-item>
      </a-col>
      <a-col :xl='8' :sm='12' :xs='24'>
        <a-form-model-item label="领取时间">
          <a-input disabled :value="form.actualStartTime" style="width: 100%;"/>
        </a-form-model-item>
      </a-col>
      </template>
       <a-col :xl='8' :sm='12' :xs='24' v-if="form.isFarm=='1' && Number(form.flowSts)>=3">
        <a-form-model-item label="运维经销商">
          <a-input disabled :value="form.operationalDealers" style="width: 100%;"/>
        </a-form-model-item>
      </a-col>
      <a-col :span='24'>
        <a-form-model-item label="缺陷描述">
          <a-textarea disabled :value="form.faultContent" :title="form.faultContent" style="width: 100%;"/>
        </a-form-model-item>
      </a-col>
         </template>
         <template v-else>
           <a-col :xl='8' :sm='12' :xs='24'>
        <a-form-model-item label="电站名称">
          <a-input disabled :value="form.psaName" :title="form.psaName" style="width: 100%;"/>
        </a-form-model-item>
      </a-col>
       <a-col :xl='8' :sm='12' :xs='24'>
        <a-form-model-item label="任务类型">
          <a-input disabled :value="form.psaName" :title="form.psaName" style="width: 100%;"/>
        </a-form-model-item>
      </a-col>
      <a-col :xl='8' :sm='12' :xs='24'>
        <a-form-model-item label="计划日期">
          <a-input disabled :value="form.planTime" :title="form.psaName" style="width: 100%;"/>
        </a-form-model-item>
      </a-col>
      <a-col :xl='8' :sm='12' :xs='24'>
        <a-form-model-item label="任务名称">
          <a-input disabled :value="form.taskName" :title="form.taskName" style="width: 100%;"/>
        </a-form-model-item>
      </a-col>
      <a-col :xl='24' :sm='24' :xs='24'>
        <a-form-model-item label="任务描述">
             <a-textarea size="default" :max-length="200" :auto-size="{ minRows: 2, maxRows: 2}" disabled
            style="width: 100%;" v-model="form.taskDescription"
            placeholder="请输入"></a-textarea>
        </a-form-model-item>
      </a-col>
        <a-col :xl='8' :sm='12' :xs='24'>
        <a-form-model-item label="工作状态">
           <a-form-model-item label="工作状态" prop="workStatus">
          <a-radio-group v-model="form.workStatus" placeholder="请选择" disabled style="width: 100%;">
            <a-radio  v-for="item in dictMap.ticket_work_condition" :key="item.dataId" :value="item.dataValue">{{item.dataLable}}</a-radio>
          </a-radio-group>
        </a-form-model-item>
        </a-form-model-item>
      </a-col>
         </template>
      <a-col :span='24'>
        <a-form-model-item label="备注">
          <a-textarea :disabled="isEdit ? false: true" :max-length="1000" v-model="form.faultRemark" :title="form.faultRemark" style="width: 100%;"/>
        </a-form-model-item>
      </a-col>
      <a-col :span='24'>
        <a-form-model-item label="附件">
          <uploadFile :disabled="isEdit ? false: true" v-model="form.files" :maxNum="5" :tipIsInline="true" tip="最多上传5个文件,且上传的附件最大不超过10MB!"></uploadFile>
        </a-form-model-item>
      </a-col>
    </a-row>
  </a-form-model>
</template>

<script>
import { getPlanTimeLimit } from '@/api/common_gy/workOrder.js';
import uploadFile from '@/components/com/fileUploadView';
import { getDeviecClassByDeviceId } from '@/api/common_gy/faultManage.js';
import { TENANT_ID } from '@/store/mutation-types';
export default {
  components: {
    uploadFile
    // workOperation
  },
  props: {
    id: {
      type: [String, Number],
      default: undefined
    },
    // type: {
    //   type: String,
    //   default: undefined
    // },
    isFarm: {
      type: String,
      default: undefined
    }
  },
  inject: ['type'],
  watch: {
    id: {
      immediate: true,
      handler: function () {
        this.getOrder();
      }
    }
  },
  computed: {
    getFaultClassify () {
      let faultClassify = this.form.faultClassify;
      if (!faultClassify) {
        return '--';
      }
      return this.faultClassify[faultClassify];
    },
    isEdit () {
      return (this.form.orderSource == '4') && (this.form.flowSts < 3) && this.type != 3;
    }
  },
  data () {
    return {
      faultClassify: { '1': '一类', '2': '二类', '3': '三类', '4': '四类' },
      form: {
        planId: undefined,
        faultClassify: undefined,
        psaName: undefined,
        deviceTypeName: undefined,
        deviceName: undefined,
        deviceUuid: undefined,
        eliminateName: undefined,
        findTime: undefined,
        twoDeviceRunStsName: undefined,
        actualStartTime: undefined,
        predictPlanTimeLimit: undefined,
        faultContent: undefined,
        faultRemark: undefined,
        files: [],
        orderFrom: '',
        realPsaName: '',
        sceneCondition: '',
        deviceTypeList: []
      },
      processDefKey: '',
      rules: {
        deviceTypeList: [{
          required: true,
          message: '设备类型不能为空',
          trigger: 'change'
        }],
        predictPlanTimeLimit: [{
          required: true,
          message: '预计消除时间不能为空',
          trigger: 'blur'
        }],
        faultClassify: [{
          required: true,
          message: '缺陷类别不能为空',
          trigger: 'blur'
        }]
      },
      options: [],
      deviceType: []
    };
  },
  methods: {

    // 设备类型change事件
    deviceTypeChange (val) {
      this.form.faultClassify = '';
      if (!Array.isArray(val) || !val.length || !this.form.psId || val.length <= 1) {
        this.form.deviceTypeList = undefined;
        return;
      }
      // 设置缺陷类别
      getDeviecClassByDeviceId({
        typeId: val[val.length - 1],
        faultType: this.form.twoDeviceRunSts
      }).then((res) => {
        this.form.faultClassify = res.result;
      });
      this.getPlanTimeLimit();
    },
    // 获取预计消除时间
    getPlanTimeLimit () {
      getPlanTimeLimit({
        deviceTypeList: this.form.deviceTypeList,
        twoDeviceRunSts: this.form.twoDeviceRunSts,
        sysTenantId: Vue.ls.get(TENANT_ID),
        findTime: this.form.findTime
      }).then(res => {
        this.form.predictPlanTimeLimit = res.result;
      });
    },
    // 校验表单返回表单数据
    getValidate () {
      let isValid = true;
      this.$refs.baseForm.validate((valid) => {
        if (valid) {
          isValid = true;
          this.$emit('updateOrder', this.form);
        } else {
          isValid = false;
        }
      });
      return isValid;
    }
  }
};
</script>

<style lang="less" scoped>
  :deep(.ant-form-item) {
    width: 100%;
    display: inline-flex;
  }
  .title-label{
    :deep(.ant-form-item-label){
      text-align: left;
      font-weight: 400;
      line-height: 25px;
      display: flex;
      flex-flow: row nowrap;
      justify-content: space-between;
      label{
        font-size: 14px;
      }
    }
  }
</style>
