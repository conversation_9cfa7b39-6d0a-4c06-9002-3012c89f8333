import { axios } from '@/utils/erp-request';

// 查询中心点配置列表信息
export function getCentreSetList (data) {
  return axios({
    url: 'system/v1/portalSet/getCentreSetList',
    method: 'POST',
    data
  });
}

// 删除中心点配置信息
export function delCentreSetList (data) {
  return axios({
    url: 'system/v1/portalSet/delCentreSetList',
    method: 'POST',
    data
  });
}

// 查询中心点配置所属省份
export function getAreaIdList (data) {
  return axios({
    url: 'system/v1/portalSet/getAreaIdList',
    method: 'POST',
    data
  });
}

// 查询中心点配置详情信息
export function getCentreInfo (data) {
  return axios({
    url: 'system/v1/portalSet/getCentreInfo',
    method: 'POST',
    data
  });
}

// 新增中心点配置信息
export function insertCentreInfo (data) {
  return axios({
    url: 'system/v1/portalSet/insertCentreInfo',
    method: 'POST',
    data
  });
}

// 编辑中心点配置信息
export function updateCentreInfo (data) {
  return axios({
    url: 'system/v1/portalSet/updateCentreInfo',
    method: 'POST',
    data
  });
}

// 获取地图上所有实体电站的点信息
export function getAllStationInfo (data) {
  return axios({
    url: 'system/v1/portal/getAllStationInfo',
    method: 'POST',
    data
  });
}

// 查询路径配置列表信息
export function getRouteSetList (data) {
  return axios({
    url: 'system/v1/portalSet/getRouteSetList',
    method: 'POST',
    data
  });
}

// 删除路径配置信息
export function delRouteSetList (data) {
  return axios({
    url: 'system/v1/portalSet/delRouteSetList',
    method: 'POST',
    data
  });
}

// 查询路径配置详情信息
export function getRouteInfo (data) {
  return axios({
    url: 'system/v1/portalSet/getRouteInfo',
    method: 'POST',
    data
  });
}

// 新增路径配置信息
export function insertRouteInfo (data) {
  return axios({
    url: 'system/v1/portalSet/insertRouteInfo',
    method: 'POST',
    data
  });
}

// 编辑路径配置信息
export function updateRouteInfo (data) {
  return axios({
    url: 'system/v1/portalSet/updateRouteInfo',
    method: 'POST',
    data
  });
}

// 编辑路径配置 一键开启/关闭
export function updateOnekeyEnableStatus (data) {
  return axios({
    url: 'system/v1/portalSet/updateOnekeyEnableStatus',
    method: 'POST',
    data
  });
}

// 路径配置 查询起点、终点
export function checkRouteStartEnd (data) {
  return axios({
    url: 'system/v1/portalSet/checkRouteStartEnd',
    method: 'POST',
    data
  });
}
