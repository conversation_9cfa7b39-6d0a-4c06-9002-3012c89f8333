<template>
  <!-- 提交表单 -->
  <a-form-model ref="orderDispose" :model="dispose" :rules="rules" :labelCol="{ style:'width: 160px' }" :wrapperCol="{ style:'width: calc(100% - 160px)' }">
    <a-row>
      <a-col :xl='8' :sm='12' :xs='24'>
        <a-form-model-item label="实际消除时间" prop="completeTime" v-if="getStatus() && dispose.handleResult=='1'">
          <a-date-picker :disabled="disabled" v-model="dispose.completeTime" @change="getRepairTime()" :disabled-date="disabledRecoverTime"
            showTime valueFormat="YYYY-MM-DD HH:mm" format="YYYY-MM-DD HH:mm" style="width:100%" placeholder="请选择" />
        </a-form-model-item>
      </a-col>
      <div style="clear:both"></div>
      <a-col :xl='8' :sm='12' :xs='24' v-if="order().orderSource=='4'">
        <a-form-model-item label="现场情况" prop="sceneCondition">
          <a-select v-model="dispose.sceneCondition" placeholder="请选择" :disabled="disabled" style="width:100%">
            <a-select-option v-for="item in sceneIsList" :key="item.key" :value="item.codeValue" :title="item.dispName">
              {{item.dispName}}
            </a-select-option>
          </a-select>
        </a-form-model-item>
      </a-col>
      <a-col :xl='8' :sm='12' :xs='24'>
        <a-form-model-item label="故障停运容量(MWp)" prop="faultCap">
          <a-input-number :max="999999.9999" :min="0" :precision="4"
            :disabled="disabled" v-model="dispose.faultCap" placeholder="请输入" style="width:100%" />
        </a-form-model-item>
      </a-col>
      <a-col :xl='8' :sm='12' :xs='24' v-if="getStatus() && dispose.handleResult=='1'">
        <a-form-model-item label="累计修复时间(h)" prop="totalRepairTime">
          <a-input-number :max="99999999.99" :min="0" :precision="2" disabled v-model="dispose.totalRepairTime" placeholder="请输入" style="width:100%" />
        </a-form-model-item>
      </a-col>
      <a-col :xl='8' :sm='12' :xs='24'>
        <a-form-model-item label="累计损失电量(万KWh)" :labelCol="{ style:'width: 165px' }" :wrapperCol="{ style:'width: calc(100% - 165px)' }" prop="totalLossPower">
          <a-input-number :precision="4" :min="0" :max="9999999.999" :disabled="disabled" v-model="dispose.totalLossPower" placeholder="请输入" style="width:100%" />
        </a-form-model-item>
      </a-col>
      <a-col :span="24"></a-col>
      <a-col :span="12">
        <a-form-model-item label="逾期原因" prop="overdueReason">
          <a-textarea :max-length="1000" :disabled="disabled" v-model="dispose.overdueReason" :title="dispose.overdueReason" placeholder="请输入" style="width:100%" />
        </a-form-model-item>
      </a-col>
      <a-col :span="12" class="last-form-item">
        <a-form-model-item label="备注" prop="conditionRemark">
          <a-textarea
            :max-length="1000"
            :disabled="disabled"
            v-model="dispose.conditionRemark"
            :title="dispose.conditionRemark"
            placeholder="请输入"
            style="width: 100%" />
        </a-form-model-item>
      </a-col>
      <a-col :span="24" v-if="dispose.stepVoList.length">
        <div class="title-box">
          <span class="before"></span>
          <span>作业步骤</span>
        </div>
        <a-steps v-model="current" direction="vertical" class="step-box">
          <a-step :key="step.title" v-for="(step, index) in dispose.stepVoList">
            <template slot="title">
              {{step.stepName}}
              <throttle-button v-if="step.uploadExampleFileList && step.uploadExampleFileList.length > 0" @click="handlePreview(step.uploadExampleFileList)"
                label="示例图片" class="example-btn"></throttle-button>
            </template>
            <template slot="description">
              <a-form-model-item
                label="情况描述"
                :prop="'stepVoList.' + index + '.workDesc'"
                :rules="{
                  required: isRequired,
                  message: '情况描述不能为空',
                  trigger: 'blur',
                }"
                :wrapperCol="{ style: 'width: calc(50% - 150px)' }"
                v-if="step.stepContent && step.stepContent.indexOf('1') > -1">
                <a-textarea
                  :max-length="1000"
                  :disabled="disabled"
                  v-model="step.workDesc"
                  :title="step.workDesc"
                  placeholder="请输入"
                  style="width: 100%" />
              </a-form-model-item>
              <a-form-model-item
                label="上传图片"
                :prop="'stepVoList.' + index + '.uploadPictureList'"
                :rules="[{
                  required: isRequired,
                  message: '请上传图片',
                  trigger: 'blur',
                },{
                  validator:validateFileList,
                  trigger:'change'
                }]"
                v-if="step.stepContent && step.stepContent.indexOf('2') > -1">
                <uploadFileView v-model="step.uploadPictureList" @set="fieldChange('stepVoList.' + index + '.uploadPictureList')" listType="picture-card" :isAllImage="true"
                  :disabled="disabled" :zoom="0.8" :multiple="true" accept=".jpg,.png,.jpeg,.bmp" tip="最多上传5张图片，单张图片不超10MB，支持格式jpg/jpeg/png/bmp！" />
              </a-form-model-item>
              <a-form-model-item
                label="上传附件"
                :prop="'stepVoList.' + index + '.uploadFileList'"
                :rules="{
                  required: isRequired,
                  message: '请上传附件',
                  trigger: 'blur',
                }"
                v-if="step.stepContent && step.stepContent.indexOf('3') > -1">
                <uploadFileView v-model="step.uploadFileList" @set="fieldChange('stepVoList.' + index + '.uploadFileList')"
                  tip="最多上传5个文件,且上传的附件最大不超过10MB!" :multiple="true" :disabled="disabled" />
              </a-form-model-item>
            </template>
          </a-step>
        </a-steps>
      </a-col>
    </a-row>
  </a-form-model>
</template>

<script>
import moment from 'moment';
import initDict from '@/mixins/initDict';
import { orderDispose } from './mixins';
export default {
  name: 'diagnosis',
  mixins: [initDict, orderDispose],
  props: {
    isDiagnosis: {
      default: false,
      type: [Boolean, String]
    }
  },
  inject: ['order'],
  watch: {

  },
  data () {
    return {
      disabled: false,
      // 普通表单
      dispose: {
        findTime: undefined, // 缺陷发现时间
        completeTime: undefined, // 实际消除时间
        handleResult: '2', // 处理结果
        orderResolveRemark: undefined, // 情况说明
        faultCap: undefined, // 故障停运容量
        totalRepairTime: undefined, // 累计修复时间
        totalLossPower: undefined, // 累计损失电量
        overdueReason: undefined, // 逾期原因
        sceneCondition: '',
        stepVoList: [],
        conditionRemark: ''
      },
      rules: {
        completeTime: [{
          required: true,
          message: '请选择实际消除时间'
        }], // 实际消除时间
        handleResult: [{
          required: true,
          message: '请选择处理结果'
        }], // 处理结果
        orderResolveRemark: [{
          required: true,
          message: '请输入情况说明'
        }], // 情况说明
        overdueReason: [{
          required: false,
          message: '逾期原因不能为空'
        }],
        faultCap: [{
          required: true,
          message: '请输入故障停运容量'
        }], // 故障停运容量
        totalRepairTime: [{
          required: true,
          message: '请输入累计修复时间'
        }], // 累计修复时间
        totalLossPower: [{
          required: true,
          message: '请输入累计损失电量'
        }], // 累计损失电量
        sceneCondition: [{
          required: true,
          message: '请选择现场情况',
          trigger: 'change'
        }]
      }
    };
  },
  created () {
    this.getDictMap('farm_scene_condition');
    let order = this.order();
    this.disabled = order.disabled;
    if (!this.getStatus() && order.stepList.length == 0) {
      this.getStepList();
    }
    this.dispose = {
      completeTime: order.completeTime, // 实际消除时间
      handleResult: order.handleResult, // 处理结果
      faultCap: order.faultCap, // 故障停运容量
      totalRepairTime: order.totalRepairTime, // 累计修复时间
      totalLossPower: order.totalLossPower, // 累计损失电量
      overdueReason: order.overdueReason, // 逾期原因
      sceneCondition: order.sceneCondition,
      stepVoList: order.stepList ? order.stepList : [],
      conditionRemark: order.conditionRemark
    };
    if (!this.orderSource && !this.dispose.completeTime) {
      this.dispose.completeTime = moment().format('YYYY-MM-DD HH:mm');
      this.getRepairTime();
    }
    if (this.orderSource && !order.sceneCondition) {
      this.dispose.sceneCondition = '1';
    }
  },
  computed: {
    sceneIsList () {
      return this.dictMap.farm_scene_condition;
    },
    orderSource () {
      return this.order().orderSource == '4' && this.order().step == '2';
    }
  },
  methods: {
    moment,
    // 更新数据
    updataDispose (order) {
      let dispose = JSON.parse(JSON.stringify(order));
      delete dispose.processInstanceId;
      delete dispose.taskId;
      this.disabled = dispose.disabled;
      Object.assign(this.dispose, dispose);

      if (dispose && dispose.hasOwnProperty('realRecoverTime')) {
        this.dispose.completeTime = dispose.realRecoverTime;
      } else {
        this.dispose.completeTime = moment().format('YYYY-MM-DD HH:mm');
      }
      if (!this.dispose.totalRepairTime) {
        this.getRepairTime();
      }
    },
    // 设置repairTime
    getRepairTime () {
      if (this.dispose.completeTime && this.order().findTime) {
        let time = (new Date(this.dispose.completeTime).getTime()) - (new Date(this.order().findTime).getTime());
        this.dispose.totalRepairTime = (time / 1000 / 60 / 60).toFixed(2);
      } else {
        this.dispose.totalRepairTime = '';
      }
    },
    // 取消检验信息
    clearValidate () {
      Object.assign(this.dispose, this.$options.data().dispose);
      this.$refs['orderDispose'].resetFields();
      this.$refs['orderDispose'].clearValidate();
    },
    // 实际消除时间不可小于发现时间
    disabledRecoverTime (endValue) {
      const startValue = moment(this.dispose.findTime).format('YYYY-MM-DD HH:mm');
      if (!endValue || !startValue) {
        return false;
      }
      return moment(endValue, 'YYYY-MM-DD HH:mm').valueOf() <= moment(startValue, 'YYYY-MM-DD HH:mm').valueOf();
    }

  }
};
</script>

<style lang="less" scoped>
  :deep(.ant-form-item) {
    width: 100%;
    display: inline-flex;
  }

  :deep(.ant-steps-vertical .ant-steps-item-description) {
    padding: 24px 0 0;
  }

  .step-box {
    :deep(.ant-form-item-label) {
      width: 134px !important;
    }
  }

  .last-form-item {
    :deep(.ant-form-item-label) {
      width: 62px !important;
    }

    :deep(.ant-form-item-control-wrapper) {
      width: calc(100% - 62px) !important;
    }
  }

  .example-btn {
    font-size: 12px !important;
    padding: 0 8px !important;
    height: 22px !important;
    margin-left: 16px;
  }

  .title-box {
    margin-top: -8px;
  }
</style>
