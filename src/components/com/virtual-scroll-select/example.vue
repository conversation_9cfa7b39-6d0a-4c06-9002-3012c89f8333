<template>
  <div class="virtual-select-example">
    <h2>Virtual Scroll Select 使用示例</h2>
    
    <!-- 基础用法 -->
    <div class="example-section">
      <h3>1. 基础用法</h3>
      <virtual-scroll-select
        v-model="basicValue"
        :select-data="basicData"
        :is-multiple="true"
        placeholder="请选择基础选项"
      />
      <p>选中值: {{ basicValue }}</p>
    </div>

    <!-- 自定义用户列表 -->
    <div class="example-section">
      <h3>2. 自定义用户列表</h3>
      <virtual-scroll-select
        v-model="userValue"
        :select-data="userData"
        :is-multiple="true"
        :use-slot="true"
        placeholder="请选择用户"
      >
        <template #option="{ item, selected, disabled }">
          <div class="user-option">
            <div class="user-avatar">
              <img :src="item.avatar || defaultAvatar" :alt="item.name" />
            </div>
            <div class="user-info">
              <div class="user-name">{{ item.name }}</div>
              <div class="user-role">{{ item.role }}</div>
            </div>
            <div class="user-status">
              <span :class="['status-dot', item.status]"></span>
              <span class="status-text">{{ item.statusText }}</span>
            </div>
          </div>
        </template>
      </virtual-scroll-select>
      <p>选中用户: {{ userValue }}</p>
    </div>

    <!-- 自定义产品列表 -->
    <div class="example-section">
      <h3>3. 自定义产品列表</h3>
      <virtual-scroll-select
        v-model="productValue"
        :select-data="productData"
        :is-multiple="false"
        :use-slot="true"
        placeholder="请选择产品"
      >
        <template #option="{ item, selected }">
          <div class="product-option">
            <div class="product-image">
              <img :src="item.image || defaultProductImage" :alt="item.name" />
            </div>
            <div class="product-details">
              <div class="product-name">{{ item.name }}</div>
              <div class="product-price">¥{{ item.price }}</div>
              <div class="product-category">{{ item.category }}</div>
            </div>
            <div class="product-stock" :class="{ 'out-of-stock': item.stock === 0 }">
              <span v-if="item.stock > 0">库存: {{ item.stock }}</span>
              <span v-else class="no-stock">缺货</span>
            </div>
          </div>
        </template>
      </virtual-scroll-select>
      <p>选中产品: {{ productValue }}</p>
    </div>

    <!-- 自定义标签列表 -->
    <div class="example-section">
      <h3>4. 自定义标签列表</h3>
      <virtual-scroll-select
        v-model="tagValue"
        :select-data="tagData"
        :is-multiple="true"
        :use-slot="true"
        placeholder="请选择标签"
      >
        <template #option="{ item }">
          <div class="tag-option">
            <span class="tag-color" :style="{ backgroundColor: item.color }"></span>
            <span class="tag-name">{{ item.name }}</span>
            <span class="tag-count">({{ item.count }})</span>
          </div>
        </template>
      </virtual-scroll-select>
      <p>选中标签: {{ tagValue }}</p>
    </div>
  </div>
</template>

<script>
import VirtualScrollSelect from './index.vue'

export default {
  name: 'VirtualSelectExample',
  components: {
    VirtualScrollSelect
  },
  data() {
    return {
      // 基础数据
      basicValue: [],
      basicData: {
        data: Array.from({ length: 100 }, (_, i) => ({
          name: `选项 ${i + 1}`,
          code: `option_${i + 1}`
        })),
        label: 'name',
        value: 'code'
      },

      // 用户数据
      userValue: [],
      userData: {
        data: [
          { name: '张三', code: 'user1', role: '前端开发', status: 'online', statusText: '在线', avatar: '' },
          { name: '李四', code: 'user2', role: '后端开发', status: 'offline', statusText: '离线', avatar: '' },
          { name: '王五', code: 'user3', role: 'UI设计师', status: 'busy', statusText: '忙碌', avatar: '' },
          { name: '赵六', code: 'user4', role: '产品经理', status: 'online', statusText: '在线', avatar: '' },
          { name: '钱七', code: 'user5', role: '测试工程师', status: 'away', statusText: '离开', avatar: '' }
        ],
        label: 'name',
        value: 'code'
      },

      // 产品数据
      productValue: '',
      productData: {
        data: [
          { name: 'iPhone 15', code: 'iphone15', price: 5999, category: '手机', stock: 50, image: '' },
          { name: 'MacBook Pro', code: 'macbook', price: 12999, category: '笔记本', stock: 20, image: '' },
          { name: 'iPad Air', code: 'ipad', price: 4399, category: '平板', stock: 0, image: '' },
          { name: 'AirPods Pro', code: 'airpods', price: 1899, category: '耳机', stock: 100, image: '' }
        ],
        label: 'name',
        value: 'code'
      },

      // 标签数据
      tagValue: [],
      tagData: {
        data: [
          { name: '前端', code: 'frontend', color: '#1890ff', count: 25 },
          { name: '后端', code: 'backend', color: '#52c41a', count: 18 },
          { name: 'Vue.js', code: 'vue', color: '#4fc08d', count: 32 },
          { name: 'React', code: 'react', color: '#61dafb', count: 28 },
          { name: 'Node.js', code: 'nodejs', color: '#339933', count: 15 },
          { name: 'TypeScript', code: 'typescript', color: '#3178c6', count: 22 }
        ],
        label: 'name',
        value: 'code'
      },

      defaultAvatar: 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzIiIGhlaWdodD0iMzIiIHZpZXdCb3g9IjAgMCAzMiAzMiIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPGNpcmNsZSBjeD0iMTYiIGN5PSIxNiIgcj0iMTYiIGZpbGw9IiNmNWY1ZjUiLz4KPHBhdGggZD0iTTE2IDhDMTMuNzkgOCAxMiA5Ljc5IDEyIDEyQzEyIDE0LjIxIDEzLjc5IDE2IDE2IDE2QzE4LjIxIDE2IDIwIDE0LjIxIDIwIDEyQzIwIDkuNzkgMTguMjEgOCAxNiA4WiIgZmlsbD0iI2Q5ZDlkOSIvPgo8cGF0aCBkPSJNMTYgMThDMTIuNjkgMTggMTAgMjAuNjkgMTAgMjRIMjJDMjIgMjAuNjkgMTkuMzEgMTggMTYgMThaIiBmaWxsPSIjZDlkOWQ5Ii8+Cjwvc3ZnPgo=',
      defaultProductImage: 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAiIGhlaWdodD0iNDAiIHZpZXdCb3g9IjAgMCA0MCA0MCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjQwIiBoZWlnaHQ9IjQwIiByeD0iNCIgZmlsbD0iI2Y1ZjVmNSIvPgo8cGF0aCBkPSJNMTIgMTZIMjhWMjRIMTJWMTZaIiBmaWxsPSIjZDlkOWQ5Ii8+CjxjaXJjbGUgY3g9IjIwIiBjeT0iMjAiIHI9IjMiIGZpbGw9IiNiZmJmYmYiLz4KPC9zdmc+Cg=='
    }
  }
}
</script>

<style lang="less" scoped>
.virtual-select-example {
  padding: 20px;
  max-width: 800px;
  margin: 0 auto;

  h2 {
    color: var(--zw-text-1-color--default);
    margin-bottom: 30px;
  }

  .example-section {
    margin-bottom: 40px;
    
    h3 {
      color: var(--zw-text-1-color--default);
      margin-bottom: 15px;
      font-size: 16px;
    }

    p {
      margin-top: 10px;
      color: var(--zw-text-2-color--default);
      font-size: 14px;
    }
  }

  // 用户选项样式
  .user-option {
    display: flex;
    align-items: center;
    padding: 8px 0;

    .user-avatar {
      width: 32px;
      height: 32px;
      margin-right: 12px;
      
      img {
        width: 100%;
        height: 100%;
        border-radius: 50%;
        object-fit: cover;
      }
    }

    .user-info {
      flex: 1;
      
      .user-name {
        font-weight: 500;
        color: var(--zw-text-1-color--default);
        font-size: 14px;
      }
      
      .user-role {
        font-size: 12px;
        color: var(--zw-text-3-color--default);
        margin-top: 2px;
      }
    }

    .user-status {
      display: flex;
      align-items: center;
      
      .status-dot {
        width: 8px;
        height: 8px;
        border-radius: 50%;
        margin-right: 6px;
        
        &.online { background: #52c41a; }
        &.offline { background: #d9d9d9; }
        &.busy { background: #faad14; }
        &.away { background: #ff7875; }
      }
      
      .status-text {
        font-size: 12px;
        color: var(--zw-text-3-color--default);
      }
    }
  }

  // 产品选项样式
  .product-option {
    display: flex;
    align-items: center;
    padding: 8px 0;

    .product-image {
      width: 40px;
      height: 40px;
      margin-right: 12px;
      
      img {
        width: 100%;
        height: 100%;
        border-radius: 4px;
        object-fit: cover;
      }
    }

    .product-details {
      flex: 1;
      
      .product-name {
        font-weight: 500;
        color: var(--zw-text-1-color--default);
        font-size: 14px;
      }
      
      .product-price {
        color: #ff4d4f;
        font-weight: 600;
        margin-top: 2px;
      }
      
      .product-category {
        font-size: 12px;
        color: var(--zw-text-3-color--default);
        margin-top: 2px;
      }
    }

    .product-stock {
      font-size: 12px;
      color: var(--zw-text-3-color--default);
      
      &.out-of-stock .no-stock {
        color: #ff4d4f;
      }
    }
  }

  // 标签选项样式
  .tag-option {
    display: flex;
    align-items: center;
    padding: 4px 0;

    .tag-color {
      width: 12px;
      height: 12px;
      border-radius: 2px;
      margin-right: 8px;
    }

    .tag-name {
      flex: 1;
      color: var(--zw-text-1-color--default);
      font-size: 14px;
    }

    .tag-count {
      font-size: 12px;
      color: var(--zw-text-3-color--default);
    }
  }
}
</style>
