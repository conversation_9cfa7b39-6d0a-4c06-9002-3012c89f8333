import { postAction } from '@/api/manage';
const url = process.env.VUE_APP_API_ERP_URL;
// const url = 'http://10.5.6.73:8080';

// 工作台-列表查询
const selectWorkBenchList = (params) => postAction(url + '/system/v1/workbench/hypage', params);

// 工作台-查询全部待办
const selectAllTodoList = (params) => postAction(url + '/system/v1/workbench/allHyTodo', params);

// 工作台-删除
const deleteWorkBenchData = (params) => postAction(url + '/system/v1/workbench/delete', params);

//
const reassignmentSave = (params) => postAction(url + '/ops/order/reassignment', params);

export {
  selectWorkBenchList,
  selectAllTodoList,
  deleteWorkBenchData,
  reassignmentSave
};
