import { axios } from '@/utils/erp-request';

// 获取最新交接数据的交接班状态
export function getNowDutySts (data) {
  return axios({
    url: '/work/v1/handover/getNowDutySts',
    method: 'post',
    data
  });
}
// 判断当前登录人员是否可交班
export function isOperatorInfo (data) {
  return axios({
    url: '/work/v1/handover/isOperatorInfo',
    method: 'post',
    data
  });
}
// 获取交接班管理列表
export function queryDutyManagelist (data) {
  return axios({
    url: '/work/v1/handover/query',
    method: 'post',
    data
  });
}
// 获取值班详情页面数据
export function queryDutyDetail (data) {
  return axios({
    url: '/work/v1/handover/queryDutyDetail',
    method: 'post',
    data
  });
}
// 接班/不接班操作
export function connectSuccession (data) {
  return axios({
    url: '/work/v1/handover/connectSuccession',
    method: 'post',
    data
  });
}
// 交班操作
export function dealSuccession (data) {
  return axios({
    url: '/work/v1/handover/dealSuccession',
    method: 'post',
    data
  });
}
// 保存
export function addDutyMessage (data) {
  return axios({
    url: '/work/v1/handover/addDutyMessage',
    method: 'post',
    data
  });
}
// 判断当天是否有交接班信息
export function getHandOverByDutyDate (data) {
  return axios({
    url: '/work/v1/handover/getHandOverByDutyDate',
    method: 'post',
    data
  });
}
// 判断当前登录人员是否在当天的排班人员当中
export function isHandOverDutyDtl (data) {
  return axios({
    url: '/work/v1/handover/isHandOverDutyDtl',
    method: 'post',
    data
  });
}
// 运行值班记录本导出
export function download (data) {
  return axios({
    url: '/work/v1/handover/download',
    method: 'post',
    data
  });
}
// 获取服务器时间
export function getSystemDate (data) {
  return axios({
    url: '/work/v1/handover/getSystemDate',
    method: 'post',
    data
  });
}
// 获取服务器时间
export function exportData (data) {
  return axios({
    url: '/work/v1/handover/export',
    method: 'post',
    data
  });
}
