<template>
  <div class="drawer-form-com">
    <a-spin :spinning="loading" class="width-100 height-100">
      <div class="drawer-form-content">
        <a-form-model
          :model="formData"
          ref="AlgorithmModelForm"
          :labelCol="{ style: 'width:160px' }"
          :wrapperCol="{ style: 'width:calc(100% - 160px)' }"
          :rules="rules"
          class="form-model-wrapper"
        >
          <a-row :gutter="24" class="margin-t-16">
            <a-col :span="24">
              <div class="order-dispose first-title">
                <div class="title-box">
                  <span class="before"></span>
                  <span>基本信息</span>
                </div>
              </div>
            </a-col>
            <a-col :xs="24" :sm="12" :md="8">
              <a-form-model-item label="电站类型" prop="psType">
                <a-select
                  v-model="formData.psType"
                  placeholder="请选择电站类型"
                  @change="psTypeChange"
                  :disabled="isEdit"
                >
                  <a-select-option v-for="item in psTypeList" :key="item.key" :value="item.secondCode"
                    >{{ item.nodeDesc }}
                  </a-select-option>
                </a-select>
              </a-form-model-item>
            </a-col>
            <a-col :xs="24" :sm="12" :md="8">
              <a-form-model-item label="应用状态" prop="isEnable">
                <a-switch v-model="formData.isEnable" />
                <span class="m-l-2">{{ formData.isEnable ? '开启' : '禁用' }}</span>
              </a-form-model-item>
            </a-col>
          </a-row>
          <a-row :gutter="24">
            <a-col :xs="24" :sm="12" :md="8">
              <a-form-model-item label="设备类型" prop="deviceType">
                <a-select
                  v-model="formData.deviceType"
                  placeholder="请选择设备类型"
                  @change="deviceTypeChange"
                  :filterOption="$filterOption"
                  option-filter-prop="children"
                  :showSearch="true"
                  :disabled="handleDisabledField('psType')"
                >
                  <a-select-option v-for="item in deviceTypeList" :key="item.key" :value="item.secondCode"
                    >{{ item.nodeDesc }}
                  </a-select-option>
                </a-select>
              </a-form-model-item>
            </a-col>
            <a-col :xs="24" :sm="12" :md="8">
              <a-form-model-item label="诊断类型" prop="alarmRemark">
                <a-select
                  v-model="formData.alarmRemark"
                  placeholder="请选择诊断类型"
                  :filterOption="$filterOption"
                  option-filter-prop="children"
                  :showSearch="true"
                  :disabled="handleDisabledField('deviceType')"
                >
                  <a-select-option v-for="item in alarmRemarkList" :key="item.key" :value="item.secondCode"
                    >{{ item.nodeDesc }}
                  </a-select-option>
                </a-select>
              </a-form-model-item>
            </a-col>
          </a-row>
          <a-row :gutter="24">
            <a-col :xs="24" :sm="12" :md="16">
              <a-form-model-item label="诊断原因" prop="alarmReasonName">
                <zw-textarea
                  v-model="formData.alarmReasonName"
                  style="width: 100%"
                  :max-length="200"
                  :auto-size="{ minRows: 2, maxRows: 4 }"
                  placeholder="请输入诊断原因"
                  :allow="false"
                />
              </a-form-model-item>
            </a-col>
          </a-row>
          <a-row :gutter="24">
            <a-col :xs="24" :sm="12" :md="8">
              <a-form-model-item label="对应测点编号" prop="algoPointList">
                <!--                <a-select
                  v-model="formData.algoPointList"
                  placeholder="请选择对应测点编号"
                  :disabled="disabled"
                  :filterOption="$filterOption"
                  option-filter-prop="children"
                  :showSearch="true"
                >
                  <a-select-option v-for="(item, index) in remoteSignalList" :key="index" :value="item.point"
                    >{{ item.point }}({{ item.nameCn }})
                  </a-select-option>
                </a-select>-->
                <VirtualScrollSelect
                  :key="formData.psType + formData.deviceType"
                  class="width-100"
                  ref="remoteSignalSelect"
                  v-model="formData.algoPointList"
                  :selectData="{
                    data: remoteSignalList,
                    label: 'nameCn',
                    value: 'point',
                  }"
                  :maxTagPlaceholder="getComplexPlaceholder"
                />
              </a-form-model-item>
            </a-col>
            <a-col :xs="24" :sm="12" :md="8">
              <a-form-model-item label="时序分析测点编号" prop="faultCurvePointList">
                <!--                <a-select
                  v-model="formData.faultCurvePointList"
                  placeholder="请选择时序分析测点编号"
                  :disabled="disabled"
                  :filterOption="$filterOption"
                  option-filter-prop="children"
                  :showSearch="true"
                  mode="multiple"
                  :max-tag-count="1"
                >
                  <a-select-option v-for="(item, index) in remoteSignalAndPointList" :key="index" :value="item.point"
                    >{{ item.point }}({{ item.nameCn }})
                  </a-select-option>
                </a-select>-->
                <VirtualScrollSelect
                  :key="formData.psType + formData.deviceType"
                  class="width-100"
                  ref="remoteSignalAndPointSelect"
                  v-model="formData.faultCurvePointList"
                  :selectData="{
                    data: remoteSignalAndPointList,
                    label: 'nameCn',
                    value: 'point',
                  }"
                />
              </a-form-model-item>
            </a-col>
          </a-row>
          <a-row :gutter="24">
            <a-col :xs="24" :sm="12" :md="8">
              <a-form-model-item label="诊断等级" prop="alarmLevel">
                <a-select v-model="formData.alarmLevel" placeholder="请选择诊断等级">
                  <a-select-option v-for="(item, index) in dictMap['0070']" :key="index" :value="item.codeValue"
                    >{{ item.dispName }}
                  </a-select-option>
                </a-select>
              </a-form-model-item>
            </a-col>
            <a-col :xs="24" :sm="12" :md="8" v-if="formData.deviceType == 1">
              <a-form-model-item label="逆变单元编号" prop="inverterUnitCode">
                <a-select
                  v-model="formData.inverterUnitCode"
                  placeholder="请选择逆变单元编号"
                  :disabled="isEdit"
                  allowClear
                >
                  <a-select-option v-for="(item, index) in inverterUnitCodeList" :key="index" :value="item"
                    >{{ item }}
                  </a-select-option>
                </a-select>
              </a-form-model-item>
            </a-col>
          </a-row>
          <a-row :gutter="24">
            <a-col :xs="24" :sm="12" :md="16">
              <a-form-model-item label="风险提示" prop="alarmRiskWarning">
                <zw-textarea
                  style="width: 100%"
                  :max-length="10 * 1000"
                  :auto-size="{ minRows: 4, maxRows: 6 }"
                  placeholder="请输入风险提示"
                  :allow="false"
                  v-model="formData.alarmRiskWarning"
                />
              </a-form-model-item>
            </a-col>
            <a-col :xs="24" :sm="12" :md="16">
              <a-form-model-item label="处理建议" prop="alarmOpinionContent">
                <zw-textarea
                  style="width: 100%"
                  :max-length="10 * 1000"
                  :auto-size="{ minRows: 4, maxRows: 6 }"
                  placeholder="请输入处理建议"
                  :allow="false"
                  v-model="formData.alarmOpinionContent"
                />
              </a-form-model-item>
            </a-col>
          </a-row>
        </a-form-model>
      </div>
      <div class="drawer-form-foot">
        <a-button class="solar-eye-btn-primary-cancel" @click="$emit('cancel', false)">取消</a-button>
        <throttle-button label="确定" @click="handleConfirmEvent" />
      </div>
    </a-spin>
  </div>
</template>
<script>
import { ACTION_NAMES, ACTION_TYPES } from '@/utils/erpcommon';
import {
  diagnosisModelCreateApi,
  diagnosisModelDetailApi,
  diagnosisModelUpdateApi,
  getPointListApi
} from '@/api/health/healthapi';
import initHealthDict from '@/mixins/health/initHealthDict';
import { clone } from 'xe-utils';
import VirtualScrollSelect from '@comp/com/virtual-scroll-select';

export default {
  name: 'AlgorithmModelForm',
  mixins: [initHealthDict],
  components: { VirtualScrollSelect },
  data () {
    return {
      loading: false,
      rowInfo: { type: null, row: {} },
      formData: {
        psType: undefined,
        isEnable: 1,
        deviceType: undefined,
        alarmRemark: undefined,
        alarmReasonName: undefined,
        algoPointList: undefined,
        faultCurvePointList: [],
        alarmLevel: undefined,
        alarmRiskWarning: undefined,
        alarmOpinionContent: undefined,
        inverterUnitCode: undefined
      },
      rules: {
        psType: [{ required: true, message: '请选择电站类型', trigger: 'change' }],
        isEnable: [{ required: true, message: '请选择启用状态', trigger: 'change' }],
        deviceType: [{ required: true, message: '请选择设备类型', trigger: 'change' }],
        alarmRemark: [{ required: true, message: '请选择诊断类型', trigger: 'change' }],
        alarmReasonName: [{ required: true, message: '请选择诊断原因', trigger: 'change' }],
        alarmLevel: [{ required: true, message: '请选择诊断等级', trigger: 'change' }]
      },
      remoteSignalAndPointList: [], // 遥信+遥测
      remoteSignalList: [], // 遥信
      psTypeList: [],
      deviceTypeList: [],
      alarmRemarkList: [],
      flattenedData: [],
      inverterUnitCodeList: [1, 2, 3, 4]
    };
  },
  created () {
    this.getHealthDict('0315,0070');
  },
  computed: {
    isEdit () {
      const { type } = this.rowInfo;
      return type === ACTION_TYPES.EDIT;
    }
  },
  watch: {
    'formData.inverterUnitCode': {
      handler (val) {
        if (val) {
          Object.assign(this.rules, {
            algoPointList: [{ required: true, message: '请选择对应测点编号', trigger: 'change' }]
          });
        } else {
          this.$set(this.rules, 'algoPointList', [
            { required: false, message: '请选择对应测点编号', trigger: 'change' }
          ]);
          this.$refs.AlgorithmModelForm.clearValidate('algoPointList');
        }
      },
      immediate: true,
      deep: true
    }
  },
  methods: {
    init (type, row) {
      Object.assign(this.rowInfo, { type, row });
      this.requestAsyncTask();
      return ACTION_NAMES[type];
    },
    async requestAsyncTask () {
      const {
        row: { alarmRemark, alarmReason, inverterUnitCode, treeData },
        type
      } = this.rowInfo;

      if ([ACTION_TYPES.EDIT, ACTION_TYPES.COPY].includes(type)) {
        this.loading = true;
        const res = await diagnosisModelDetailApi({ alarmRemark, alarmReason, inverterUnitCode });
        Object.assign(this.formData, res);
        const { psType, deviceType } = this.formData;
        const firstLevel = treeData.find((item) => item.nodeType === 'psType' && item.secondCode === psType) || {};
        this.psTypeList = treeData.filter((item) => item.nodeType === 'psType') || {};
        this.deviceTypeList = firstLevel.children || [];
        const secondLevel =
          this.deviceTypeList.find((item) => item.nodeType === 'deviceType' && item.secondCode === deviceType) || {};
        this.alarmRemarkList = secondLevel.children || [];
        await this.getPointList();
        this.loading = false;
      } else if (type === ACTION_TYPES.ADD) {
        this.psTypeList = treeData.filter((item) => item.nodeType === 'psType') || {};
      }
    },
    handleConfirmEvent () {
      this.$refs.AlgorithmModelForm.validate((valid) => {
        if (valid) {
          this.loading = true;
          const serviceApi =
            this.rowInfo.type === ACTION_TYPES.EDIT ? diagnosisModelUpdateApi : diagnosisModelCreateApi;
          const { isEnable } = this.formData;
          serviceApi({
            ...this.formData,
            isEnable: isEnable ? 1 : 0
          })
            .then((res) => {
              this.loading = false;
              this.$message.success('操作成功');
              this.$emit('cancel', true);
            })
            .catch((e) => {
              this.loading = false;
            });
        }
      });
    },
    psTypeChange (value) {
      const {
        row: { treeData }
      } = this.rowInfo;
      Object.assign(this.formData, {
        deviceType: undefined,
        alarmRemark: undefined,
        algoPointList: undefined,
        faultCurvePointList: [],
        inverterUnitCode: undefined
      });
      const firstLevel = treeData.find((item) => item.nodeType === 'psType' && item.secondCode === value) || {};
      this.deviceTypeList = firstLevel.children || [];
      this.remoteSignalAndPointList = this.remoteSignalList = [];
      this.$refs.remoteSignalSelect.reset();
      this.$refs.remoteSignalAndPointSelect.reset();
    },
    deviceTypeChange (value, event) {
      Object.assign(this.formData, {
        alarmRemark: undefined,
        algoPointList: undefined,
        faultCurvePointList: [],
        inverterUnitCode: undefined
      });
      const secondLevel =
        this.deviceTypeList.find((item) => item.nodeType === 'deviceType' && item.secondCode === value) || {};
      this.alarmRemarkList = secondLevel.children || [];
      this.$refs.remoteSignalSelect.reset();
      this.$refs.remoteSignalAndPointSelect.reset();
      this.getPointList();
    },
    getPointList () {
      const { deviceType } = this.formData;
      getPointListApi({ deviceType })
        .then((res) => {
          this.remoteSignalAndPointList = clone(res, true).map((item) => ({
            ...item,
            nameCn: item.point + '(' + item.nameCn + ')'
          }));
          this.remoteSignalList = clone(res, true)
            .filter((item) => item.pointType === '1')
            .map((item) => ({
              ...item,
              nameCn: item.point + '(' + item.nameCn + ')'
            }));
        })
        .catch(() => {});
    },
    handleDisabledField (field) {
      const { type } = this.rowInfo;
      return (!this.formData[field] && type !== ACTION_TYPES.ADD) || type === ACTION_TYPES.EDIT;
    },
    getComplexPlaceholder(omitValues) {
    //  在当前组件内，传入maxTagPlaceholder给VirtualScrollSelect组件，使其显示定义的内容

    }
  }
};
</script>

<style scoped lang="less">
:deep(.height-100) {
  .ant-spin-container {
    width: 100%;
    height: 100%;
  }
}

.form-model-wrapper.ant-form-horizontal {
  .ant-form-item {
    display: inline-flex;
    width: 100%;
  }
}
</style>
