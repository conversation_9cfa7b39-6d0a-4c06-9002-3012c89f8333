// 设备健康-诊断详情-基本信息
export const baseInfoList = [
  { label: '电站名称', key: 'psName' },
  { label: '设备类型 ', key: 'deviceTypeName' },
  { label: '设备名称', key: 'deviceName' },
  { label: '厂家名称', key: 'maker' },
  { label: '设备型号', key: 'deviceModel' }
];
// 设备健康-诊断详情-告警信息
export const alarmInfoList = [
  { label: '诊断等级', key: 'alarmLevelName' },
  { label: '诊断类型', key: 'alarmRemarkName' },
  { label: '诊断原因', key: 'alarmReasonName' },
  { label: '最新发生时间', key: 'lastHappenTime' },
  { label: '发生次数', key: 'updateTimes' }
];
// 设备健康-诊断详情-诊断结论及建议
export const conclusionList = [
  { label: '持续时长(h)', key: 'durationTime' },
  { label: '预计损失电量(kWh)', key: 'powerLoss' },
  { label: '风险提示', key: 'riskWarning' },
  // { label: '处理建议', key: 'alarmOpinion', span: 16 },
  { slot: 'alarmOpinion' }
];
// 设备健康-诊断详情-现场处理
export const dealList = [
  { label: '工单编号', key: 'workCode', slot: 'workCode' },
  { label: '现场情况', key: 'sceneConditionName' },
  { label: '故障停运容量(MWp)', key: 'faultCap' },
  { label: '累计损失电量(万KWh)', key: 'totalLossPower' },
  { label: '执行提交时间', key: 'impCommitTime' },
  { label: '累计修复时间(h)', key: 'totalRepairTime' },
  { label: '情况描述', key: 'workDesc' },
  { label: '工单闭环时间', key: 'finishedTime' },
  { label: '上传图片', key: 'stepPictureList', type: 'file:picture-card' }
];
// 设备健康-诊断详情-处理结果验证
export const resultList = [{ label: '诊断状态', key: 'faultStatusName' }, { slot: 'disappearTime' }];
// 设备健康-工单详情-基本信息
export const orderBaseInfoList = [
  { label: '工单分类', key: 'taskCategoryName' },
  { label: '工单类型', key: 'taskTypeName' },
  { label: '工程遗留', key: 'isEngLegacyLabel' },
  { label: '来源分类', key: 'orderSourceName', span: 24 },
  { label: '项目名称', key: 'psaName' },
  { label: '电站名称', key: 'psName' },
  { label: '发生时间', key: 'happenTime' },
  { label: '发现时间', key: 'findTime', span: 24 },
  { label: '故障描述', key: 'taskDescription', span: 24 },
  { label: '处理建议', key: 'handleOpinions', span: 24 },
  { label: '发现人', key: 'findUserName' },
  { label: '预计消除时间', key: 'predictRecoverTime' },
  { label: '负责人', key: 'liablePersonName' },
  { label: '故障图片', key: 'faultFileList', span: 24, type: 'file:picture-card' }
];

// 设备健康-工单详情-设备信息
export const orderDeviceInfoList = [
  { label: '设备类型', key: 'deviceTypeName' },
  { label: '设备名称', key: 'deviceName' },
  { label: '设备编号', key: 'deviceNo' },
  { label: '生产厂家', key: 'maker' },
  { label: '设备型号', key: 'deviceModel' },
  { label: '设备状态', key: 'deviceStatusName' },
  { label: '故障名称', key: 'defectName' },
  { label: '停电范围', key: 'powerCutRangeName' },
  { label: '故障类别', key: 'defectTypeName' }
];

// 设备健康-工单详情-两票信息
export const orderTicketInfoList = [{ label: '关联两票', key: 'ticketNo', slot: 'ticketNo' }];

// 设备健康-工单详情-执行信息
export const orderExecuteInfoList = [
  { label: '现场情况', key: 'sceneConditionName' },
  { label: '故障停运容量(MWp)', key: 'faultCap' },
  { label: '累计损失电量(万kWh)', key: 'totalLossPower' },
  { label: '派发/领取时间', key: 'realStartTime' },
  { label: '执行提交时间', key: 'impCommitTime' },
  { label: '累计修复时间(h)', key: 'totalRepairTime' },
  { label: '备注', key: 'conditionRemark', span: 24 }
];

/**
 * 诊断类型列表
 * @type {Array<{imageName: string, remark: string, keyValue: string}>}
 * @property {string} keyValue - 诊断类型的唯一标识 1 故障停机, 2 通讯中断, 3 隐患运行, 4 低效缺陷, 6 故障预警, 7 考核预警
 *
 */
export const diagnosticTypeList = [
  {
    imageName: 'boot',
    remark: '故障停机',
    keyValue: '1',
    auth: 'health:faultShutdown'
  },
  {
    imageName: 'circut',
    remark: '通讯中断',
    keyValue: '2',
    auth: 'health:communicationInterruption'
  },
  {
    imageName: 'boxChange',
    remark: '隐患运行',
    keyValue: '3',
    auth: 'health:hiddenRisks'
  },
  {
    imageName: 'inverter',
    remark: '低效缺陷',
    keyValue: '4',
    auth: 'health:inefficientDefect'
  },
  {
    imageName: 'failure-warn',
    remark: '故障预警',
    keyValue: '6',
    auth: 'health:failureWarning'
  },
  {
    imageName: 'assessment-warn',
    remark: '考核预警',
    keyValue: '7',
    auth: 'health:assessmentWarning'
  }
];

/**
 * 电站类型归属映射
 * @type {Object.<number, {iconClass: string, label: string, deviceTypeList: Array<{remark: string, keyValue: string, optList: Array<string>, total: number}>}>}
 * @property {string} key - 电站类型的唯一标识 1 光伏诊断, 2 储能诊断
 * @property {string} key.iconClass - 图标类名
 * @property {string} key.label - 电站类型标签
 * @property {Array<{remark: string, keyValue: string, optList: Array<string>, total: number}>} key.deviceTypeList - 电站类型列表
 */
export const deviceTypeBelongMap = {
  '1': {
    iconClass: 'photovoltaic',
    label: '光伏诊断',
    deviceTypeList: [
      { remark: '全部', keyValue: '', optList: ['1', '2', '3', '4', '6', '7'], total: 0 },
      { remark: '电站', keyValue: '11', optList: ['1', '2', '4', '7'], total: 0 },
      { remark: '升压站', keyValue: '66', optList: ['1', '3', '7'], total: 0 },
      { remark: '集电线', keyValue: '3', optList: ['1', '2', '3'], total: 0 },
      { remark: '方阵', keyValue: '17', optList: ['1', '2', '3'], total: 0 },
      { remark: '逆变器', keyValue: '1', optList: ['1', '2', '4', '3', '6'], total: 0 },
      { remark: '汇流箱 ', keyValue: '4', optList: ['1', '2', '3'], total: 0 },
      { remark: '组串', keyValue: '10', optList: ['1', '4'], total: 0 },
      // { remark: '组件', keyValue: '58', optList: ['1', '4'], total: 0 },
      { remark: '电表', keyValue: '7', optList: ['2'], total: 0 },
      { remark: '组串计量箱', keyValue: '301', optList: ['2'], total: 0 },
      { remark: '环境检测仪', keyValue: '5', optList: ['2'], total: 0 }
    ]
  },
  '2': {
    iconClass: 'energy-storage',
    label: '储能诊断',
    deviceTypeList: [
      { remark: '全部', keyValue: '', optList: ['1', '3'], total: 0 },
      { remark: 'EMS', keyValue: '26', optList: ['1', '3'], total: 0 },
      { remark: '储能变流器', keyValue: '37', optList: ['1', '3'], total: 0 },
      { remark: '系统BMS', keyValue: '23', optList: ['1', '3'], total: 0 },
      { remark: 'RackBMS', keyValue: '24', optList: ['1', '3'], total: 0 }
    ]
  }
};

// 演示故障id
export const demonstrateIds = [
  231432910, 231432912, 231432914, 231432918, 231432920, 231432922, 5116015, 9189085, 5116017, 8635311, 8572339,
  8572336, 8572335, 9210213, 5109777, 8599245, 8629169, 231433515, 231433517, 231433519, 231433525, 231433150
];

export const tempPredictionPoints = [
  'p2300026',
  'p2300027',
  'p2300028',
  'p2300029',
  'p2300030',
  'p2300031',
  'p2300032',
  'p2300033',
  'p2300034',
  'p2300035',
  'p2300036',
  'p2300037',
  'p2300038',
  'p2300039',
  'p2300040',
  'p2300041',
  'p2300042',
  'p2300043',
  'p2300044',
  'p2300045',
  'p2300046',
  'p2300047',
  'p2300048',
  'p2300049',
  'p2300050',
  'p2300051',
  'p2300052',
  'p2300053'
];
// 对象的value值为null时，处理成空对象｛｝
export function handleNullToEmptyObj (obj = {}) {
  for (let key in obj) {
    if (obj[key] === null) {
      obj[key] = {};
    }
  }
  return obj;
}
