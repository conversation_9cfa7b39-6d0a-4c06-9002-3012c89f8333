<template>
  <div class="psa-appearance">
    <div class='mark-box' v-if='afterExecuteStatus'>
      <a-popover placement="topRight" arrow-point-at-center>
        <template slot="content">
          <div>注：优秀：95分及以上，良好：90-95分（不含95），</div>
          <div>合格：85-90（不含90），不合格：85分以下</div>
        </template>
        <svg-icon class="info-icon" iconClass="health-info" />
      </a-popover>
      <span>总分：</span>
      <span class='score'>{{isNumber(appearanceScore) ? appearanceScore : '-'}}分</span>
      <span :class="[scoreLevelObj ?'score-level ' + scoreLevelObj.class :'']" v-if="otherSts ==='3' || afterExecuteStatus">
        {{scoreLevelObj ? scoreLevelObj.label : '-'}}
      </span>
    </div>
    <vxe-table ref="vxeTable" :data="appearanceVoList" :seq-config="{ startIndex: 0 }" :height="height" class='my-table'
      highlight-hover-row size="small" :show-overflow='false'>
      <vxe-table-column type="seq" width="60" title="序号"></vxe-table-column>
      <vxe-table-column v-for="item in psaAppearanceColumn" :key="item.field" :min-width="item.width" :field="item.field"
        :title="item.title" :fixed="item.fixed ? item.fixed : ''" :visible='item.visible'>
        <template #default="{ row, $rowIndex,column }">
          <!--    检测内容      -->
          <template v-if="item.field === 'contentList'">
            <check-content :list="row.contentList" :index='$rowIndex' :key='$rowIndex'/>
          </template>
          <!--    是否涉及      -->
          <template v-else-if="item.field === 'isNotInvolve'">
            <a-checkbox :disabled="isDisabled || afterExecuteStatus" :checked='row.isNotInvolve' @change='e=>handleAffectedChange(e,row)'>否</a-checkbox>
          </template>
          <!--    示例图片      -->
          <template v-else-if="item.field === 'exampleFileList'">
            <PsaAppearanceExample :list='row.exampleFileList' />
          </template>
          <!--    上传水印照片      -->
          <template v-else-if="item.field === 'uploadFileList'">
            <UploadFileView :multiple="true" v-model='row.uploadFileList' list-type='picture-card' upload-text="上传图片"
              accept=".jpg,.png,.jpeg,.bmp,.JPG,.PNG,.JPEG,.BMP" :disabled='row.isNotInvolve || isDisabled || afterExecuteStatus' :zoom='0.9'
              @set="$refs.vxeTable.updateStatus({row,column})" :key="row.isNotInvolve"/>
          </template>
          <!--    评分      -->
          <template v-else-if="item.field === 'score'">
            <span v-if="['3','4'].includes(type)">{{getLabel(row[item.field], null)}}</span>
            <a-input-number v-else v-model='row.score' :class="'appearance-score-'+$rowIndex" :disabled='row.isNotInvolve' :max='row.mark'
             @change="$refs.vxeTable.updateStatus({row,column})" :precision="0" :min='0' @blur='handleScoreBlur'/>
          </template>
          <!--    改进建议      -->
          <template v-else-if="item.field === 'improvementSuggest'">
            <span v-if="['3','4'].includes(type)">{{getLabel(row[item.field], null)}}</span>
            <a-input :disabled='row.isNotInvolve'  v-else v-model="row.improvementSuggest" :max-length='200'/>
          </template>
          <!--    审核建议      -->
          <template v-else-if="item.field === 'examineSuggest'">
            <a-input v-if="type === '4'" v-model="row.examineSuggest" :max-length='200'/>
            <span v-else>{{getLabel(row[item.field], null)}}</span>
          </template>
          <span v-else>{{ getLabel(row[item.field], null) }}</span>
        </template>
        <!--    表头自定义    -->
        <template #header='{ column }'>
          <template v-if="column.property === 'uploadFileList'">
            <span v-if='afterExecuteStatus'>执行照片</span>
            <div v-else>
              <span class='first-title'>上传水印照片</span>
              <span class='sub-title'>提示：至少1张图片，不超过5张，单张图片不超10MB，支持格式jpg/jpeg/png/bmp！</span>
            </div>
          </template>
          <span v-else>{{ column.title }}</span>
        </template>
      </vxe-table-column>
    </vxe-table>
  </div>
</template>

<script>
import PropTypes from 'ant-design-vue/es/_util/vue-types';
import { psaAppearanceColumnFn } from '../../js/config';
import CheckContent from './CheckContent';
import PsaAppearanceExample from './PsaAppearanceExample';
import UploadFileView from './PsaAppearanceUpload';
import { isNumber } from 'xe-utils';

export default {
  name: 'PsaAppearance',
  components: { PsaAppearanceExample, CheckContent, UploadFileView },
  props: {
    appearanceVoList: PropTypes.array.def([]),
    isDisabled: PropTypes.bool,
    appearanceScore: PropTypes.number,
    otherSts: PropTypes.string,
    type: PropTypes.string
  },
  data () {
    return {
      psaAppearanceColumn: [],
      isNumber,
      height: 0
    };
  },
  mounted () {
    this.height = $('.drawer-form-content').height() - 200;
    this.psaAppearanceColumn = psaAppearanceColumnFn(this.otherSts, this.appearanceVoList);
    this.$refs.vxeTable.refreshColumn();
  },
  methods: {
    // 切换是否涉及时清空示例图片
    handleAffectedChange (e, row) {
      const checked = e.target.checked;
      if (checked && row.uploadFileList && row.uploadFileList.length) {
        this.$confirm({
          title: '确定无该检查项吗？',
          content: '取消后上传的图片将不保存',
          okText: '确认',
          cancelText: '取消',
          onOk: () => {
            row.isNotInvolve = checked;
            this.$set(row, 'uploadFileList', []);
          }
        });
      } else {
        row.isNotInvolve = checked;
      }
    },
    handleScoreBlur () {
      const filters = this.appearanceVoList.filter(item => isNumber(item.score));
      this.appearanceScore = filters.length ? filters.reduce((acc, cur) => {
        acc += cur.score;
        return acc;
      }, 0) : null;
    }
  },
  computed: {
    scoreLevelObj () {
      if (isNumber(this.appearanceScore)) {
        if (this.appearanceScore >= 95) {
          return {
            label: '优秀',
            class: 'outstanding'
          };
        } else if (this.appearanceScore >= 90) {
          return {
            label: '良好',
            class: 'good'
          };
        } else if (this.appearanceScore >= 85) {
          return {
            label: '合格',
            class: 'passing'
          };
        } else if (this.appearanceScore < 85) {
          return {
            label: '不合格',
            class: 'unqualified'
          };
        }
      }
      return null;
    },
    afterExecuteStatus () {
      return ['5', '9', '10'].includes(this.otherSts);
    }
  }
};
</script>

<style lang="less" scoped>
.psa-appearance {
  margin: 0 0 0 24px;

  .mark-box {
    width: 100%;
    display: flex;
    justify-content: flex-end;
    margin-bottom: 17px;
    align-items: center;
    .info-icon{
      margin-right: 8px;
    }
    .score{
      color: var(--zw-primary-color--default);
      font-weight: 500;
      letter-spacing: 0em;
      font-size: 14px;
      margin-right: 8px;
    }
    .score-level{
      display: inline-block;
      border-radius: 2px;
      padding: 0px 12px;
    }
    .outstanding{
      color: #D54941;
      background: #FDDDDD;
      border: 1px solid #D54941;
    }
    .good{
      color: var(--zw-primary-color--default);
      background: rgba(255, 129, 0, 0.1);
      border: 1px solid var(--zw-primary-color--default);
    }
    .passing{
      color: #2BA471;
      background: #E3F9E9;
      border: 1px solid #2BA471;
    }
    .unqualified{
      color: #8E56DD;
      background: rgba(142, 86, 221, 0.2);
      border: 1px solid #8E56DD;
    }
  }

  :deep(.vxe-cell--title) {
    font-size: 14px;

    .first-title::after {
      content: '*';
      color: #D54941;
      vertical-align: middle;
    }

    .sub-title {
      color: #666666;
      font-size: 12px;
      margin-left: 8px;
      font-weight: normal;
    }
  }
    :deep(.file-upload){
      display: flex;
      .ant-upload-list-picture-card {
          .ant-upload-list-picture-card-container{
              margin-bottom: 0;
              .ant-upload-list-item{
                margin-bottom: 0;
              }
            }
      }
    }
}

:root[data-theme='dark'] {
  .psa-appearance {
    :deep(.vxe-cell--title) {
      .sub-title {
        color: #fff;
      }
    }
  }
}
</style>
