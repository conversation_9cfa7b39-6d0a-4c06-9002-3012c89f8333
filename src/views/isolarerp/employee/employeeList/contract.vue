<template>
  <!-- 合同信息 -->
  <a-row>
    <a-spin :spinning="loading">
      <a-col v-show="(show || type)&& !isgId" :span="24" class="top-btn-group">
        <a-button size="default" class="solar-eye-btn-primary" @click="addContract" style="margin-right:10px;">新增</a-button>
        <a-button size="default" @click="delContract">删除</a-button>
      </a-col>
      <a-col :span="24">
        <vxe-table class="emp-table" ref="contractTable" :data="contractTable" :edit-rules="!isgId ? rules : {}" resizable max-height="457" align="center" border show-header-overflow show-overflow highlight-hover-row size="small">
          <vxe-table-column type="checkbox" fixed="left" width="60"></vxe-table-column>
          <vxe-table-column title="序号" type="seq" width="80"></vxe-table-column>
          <vxe-table-column field="agreement" title="合同编号" show-overflow="title" min-width="160">
            <template v-slot="{ row }">
              <a-input v-model="row.agreement" v-if="show || row.edit" :maxLength='32' size="default" @blur="row.agreement = $trim($event)" style="width: 95%;"></a-input>
              <span v-else>{{getLabel(row.agreement,null)}}</span>
            </template>
          </vxe-table-column>
          <vxe-table-column field="contractName" title="合同名称" show-overflow="title" min-width="160">
            <template v-slot="{ row }">
              <a-input v-model="row.contractName" v-if="show || row.edit" :maxLength='100' size="default" @blur="row.contractName = $trim($event)" style="width: 95%;"></a-input>
              <span v-else>{{getLabel(row.contractName,null)}}</span>
            </template>
          </vxe-table-column>
          <vxe-table-column field="contractCompany" title="合同公司" show-overflow="title" min-width="160">
            <template v-slot="{ row }">
              <a-input v-model="row.contractCompany" v-if="show || row.edit" :maxLength='100' size="default" @blur="row.contractCompany = $trim($event)" style="width: 95%;"></a-input>
              <span v-else>{{getLabel(row.contractCompany,null)}}</span>
            </template>
          </vxe-table-column>
          <vxe-table-column field="contractType" title="合同类型" show-overflow="title" width="140">
            <template v-slot="{ row }">
              <a-select v-model="row.contractType" v-if="show || row.edit" size="default" style="width:100%;">
                <a-select-option v-for="(option,index) in dictMap['contract_type']" :key="index" :value="option.dataValue">{{option.dataLable}}</a-select-option>
              </a-select>
              <span v-else>{{getLabel(row.contractType,dictMap['contract_type'])}}</span>
            </template>
          </vxe-table-column>
          <vxe-table-column field="contractTerm" title="合同期限" show-overflow="title" width="140">
            <template v-slot="{ row }">
              <a-select v-model="row.contractTerm" v-if="show || row.edit" size="default" style="width:100%;">
                <a-select-option v-for="(option,index) in dictMap['contract_term']" :key="index" :value="option.dataValue">{{option.dataLable}}</a-select-option>
              </a-select>
              <span v-else>{{getLabel(row.contractTerm,dictMap['contract_term'])}}</span>
            </template>
          </vxe-table-column>
          <vxe-table-column field="agreementStartDate" title="合同开始时间" show-overflow="title" width="160">
            <template v-slot="{ row }">
              <a-date-picker v-model="row.agreementStartDate" v-if="show || row.edit" format="YYYY-MM-DD" valueFormat="YYYY-MM-DD" size="default" style="width: 95%;"/>
              <span v-else>{{getLabel(row.agreementStartDate,null)}}</span>
            </template>
          </vxe-table-column>
          <vxe-table-column field="agreementEndDate" title="合同结束时间" show-overflow="title" width="160">
            <template v-slot="{ row }">
              <a-date-picker v-model="row.agreementEndDate" v-if="show || row.edit" format="YYYY-MM-DD" valueFormat="YYYY-MM-DD" size="default" style="width: 95%;"/>
              <span v-else>{{getLabel(row.agreementEndDate,null)}}</span>
            </template>
          </vxe-table-column>
          <vxe-table-column :visible="type" title="操作" fixed="right" width="120">
            <template slot-scope="scope">
              <template v-if="scope.row.edit">
                <a-button size="default" icon="save" :loading="loading" title="保存" @click="saveRow(scope.$rowIndex)"></a-button>
                <a-button size="default" icon="arrow-left" title="取消" @click="cacelRow(scope.$rowIndex)"></a-button>
              </template>
              <template v-else>
                <a-button size="default" type="primary" icon="form" title="编辑" @click="editRow(scope.$rowIndex)"></a-button>
              </template>
            </template>
          </vxe-table-column>
          <template v-slot:empty>
            <span>查询无数据</span>
          </template>
        </vxe-table>
      </a-col>
    </a-spin>
  </a-row>
</template>

<script>
import initDict from '@/mixins/initDict';
import { delContractById, saveContract } from '@/api/isolarErp/employee/employee';
import { TENANT_ID, USER_NAME } from '@/store/mutation-types';
export default {
  mixins: [initDict],
  props: {
    type: {
      type: Boolean,
      default: false
    },
    empId: {
      type: [String, Number],
      default: ''
    },
    isgId: {
      type: String,
      default: ''
    }
  },
  data () {
    return {
      contractTable: [],
      show: false,
      loading: false,
      oldRow: null,
      rules: {
        agreement: [
          { required: true, message: '请填写合同编号', trigger: 'blur' }
        ],
        contractName: [
          { required: true, message: '请填写合同名称', trigger: 'blur' }
        ],
        contractCompany: [
          { required: true, message: '请填写合同公司', trigger: 'blur' }
        ],
        contractType: [
          { required: true, message: '请选择合同类型', trigger: 'change' }
        ],
        contractTerm: [
          { required: true, message: '请选择合同期限', trigger: 'change' }
        ],
        agreementStartDate: [
          { required: true, message: '请选择现合同起始日', trigger: 'change' }
        ],
        agreementEndDate: [
          { required: true, message: '请选择现合同到期日', trigger: 'change' }
        ]
      }
    };
  },
  created () {
    this.getDictMap('contract_type,contract_term');
  },
  methods: {
    // 初始化数据
    init (data, type) {
      this.show = (type && type != '3');
      data = (data || []);
      data.forEach(item => {
        item.edit = false;
      });
      this.contractTable = data;
    },
    // 新增
    addContract () {
      let row = {
        agreement: '',
        contractName: '',
        contractCompany: '',
        contractType: '',
        contractTerm: '',
        agreementStartDate: '',
        agreementEndDate: ''
      };
      if (this.type) {
        for (let item of this.contractTable) {
          if (item.edit) {
            this.$message.warning('请先保存当前编辑的数据!');
            return;
          }
        }
        row.edit = true;
        this.oldRow = null;
      }
      this.contractTable.unshift(row);
    },
    // 行编辑
    editRow (index) {
      for (let item of this.contractTable) {
        if (item.edit) {
          this.$message.warning('请先保存当前编辑的数据!');
          return;
        }
      }
      this.oldRow = Object.assign({}, this.contractTable[index]);
      this.contractTable[index].edit = true;
    },
    // 行保存
    async saveRow (index) {
      const self = this;
      self.loading = true;
      const err = await self.$refs['contractTable'].validate(self.contractTable[index]).catch(errMap => errMap);
      if (err) {
        self.loading = false;
        return;
      }
      const check = self.checkData();
      if (check) {
        self.loading = false;
        return;
      }
      self.$confirm({
        title: '确定保存修改吗?',
        okText: '确定',
        cancelText: '取消',
        onOk () {
          let row = self.contractTable[index];
          row.sysTenantId = Vue.ls.get(TENANT_ID);
          row.userAccount = Vue.ls.get(USER_NAME);
          row.empId = self.empId;
          saveContract({ 'contract': [row] }).then(res => {
            self.loading = false;
            if (res.result_code == '1') {
              self.$message.success('操作成功');
              self.contractTable[index].edit = false;
              self.$emit('refresh');
            } else {
              self.$message.warning(res.result_msg);
            }
          }).catch(() => {
            self.loading = false;
          });
        },
        onCancel () {
          self.loading = false;
        }
      });
    },
    // 行-取消保存
    cacelRow (index) {
      const self = this;
      self.$confirm({
        title: '确定放弃修改吗?',
        okText: '确定',
        cancelText: '取消',
        onOk () {
          // 用于区分修改的是已有数据还是新加的数据
          if (self.oldRow) {
            self.contractTable[index] = self.oldRow;
            const data = self.contractTable;
            self.contractTable = [];
            self.$nextTick(() => {
              self.contractTable = data;
            });
          } else {
            self.contractTable.splice(index, 1);
          }
        }
      });
    },
    // 删除
    delContract () {
      const self = this;
      const select = self.$refs.contractTable.getCheckboxRecords();
      if (select.length < 1) {
        self.$message.warning('请至少勾选一条数据');
        return;
      }
      self.$confirm({
        title: '确定要删除吗?',
        okText: '确定',
        cancelText: '取消',
        onOk () {
          if (self.type) {
            let ids = [];
            select.forEach(item => {
              if (item.id) {
                ids.push(item.id);
              }
            });
            if (ids.length == 0) {
              self.remove(select);
            } else {
              self.loading = true;
              delContractById({ 'ids': ids }).then(res => {
                self.loading = false;
                if (res.result_code == '1') {
                  self.$message.info('数据删除成功');
                  self.remove(select);
                } else {
                  self.$message.warning(res.result_msg);
                }
              }).catch(() => {
                self.loading = false;
              });
            }
          } else {
            self.remove(select);
          }
        }
      });
    },
    remove (select) {
      let _XIDS = [];
      select.forEach(item => {
        _XIDS.push(item._XID);
      });
      this.contractTable = this.contractTable.filter(item => !_XIDS.includes(item._XID));
    },
    // 主页-获取数据
    getData () {
      return this.contractTable;
    },
    // 数据校验
    async validate () {
      const errMap = await this.$refs['contractTable'].validate(true).catch(errMap => errMap);
      if (errMap) {
        return errMap;
      }
      return this.checkData();
    },
    checkData () {
      try {
        this.contractTable.forEach((item, index) => {
          if (item.agreementStartDate > item.agreementEndDate) {
            throw new Error('合同信息第' + (index + 1) + '行时间区间有误');
          }
        });
        return null;
      } catch (e) {
        this.$message.warning(e.message);
        return true;
      }
    }
  }
};
</script>

<style lang="less" scoped>
</style>
