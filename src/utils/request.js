// import Vue from 'vue'
import axios from 'axios';
import store from '@/store';
import { VueAxios, isResponseHeaderToken } from './axios';
import { notification } from 'ant-design-vue';
import { USER_INFO, ACCESS_TOKEN, TENANT_ID } from '@/store/mutation-types';
import { logoutToSolareye } from '@/utils/index';
// let apiBaseUrl = 'http://192.168.155.143:8081/solareye-boot' 'http://192.168.157.115:8080/' ||
/**
 * 【指定 axios的 baseURL】
 * 如果手工指定 baseURL: '/solareye'
 * 则映射后端域名，通过 vue.config.js
 * @type {*|string}
 */
let apiBaseUrl = process.env.VUE_APP_API_BASE_URL || '/';
const CanCelToken = axios.CancelToken;
let source = CanCelToken.source();
// 创建 axios 实例
const service = axios.create({
  baseURL: apiBaseUrl // api base_url
  // timeout: 9000 // 请求超时时间
});
const err = (error) => {
  if (error.response || error.data) {
    let data = '';
    let status = '';
    let url = error.response && error.response.config.url;
    if (error.response) {
      data = error.response.data;
      status = error.response.status;
    } else {
      data = error.data;
      status = error.data.code;
    }
    status = (isNaN(status) ? '' : Number(status));
    switch (status) {
      case 403:
        notification.error({
          message: '系统提示',
          description: '拒绝访问',
          duration: 4
        });
        break;
      case 500:
        if (error.response && error.response.request && error.response.request.type === 'blob') {
          blobToJson(data);
        } else {
          notification.error({
            message: '系统提示',
            description: (data.message ? data.message : data.result_msg),
            duration: 4
          });
        }
        break;
      case 404:
        notification.error({
          message: '系统提示',
          description: '很抱歉，资源未找到!',
          duration: 4
        });
        break;
      case 504:
        if (url.indexOf('/monitor/lineChart') > -1 || url.endsWith('/stationStatisticsByPsId') > -1 || url.endsWith('/inverterList') > -1 || url.indexOf('/combinerBoxList') > -1) {
          break;
        } else {
          notification.error({
            message: '系统提示',
            description: '网络超时'
          });
          break;
        }

      case 401:
        notification.destroy();
        notification.error({
          message: '系统提示',
          description: (data.message ? data.message : data.result_msg) || 'Token失效，请重新登录!'
        });
        store.dispatch('Logout').then(() => {
          logoutToSolareye();
        });
        break;
      default:
        let href = window.location.href;
        let path = href.split('/#')[1];
        if (!Vue.ls.get(USER_INFO) && path != '/' && path.indexOf('/user/login') == -1) {
          source.cancel();
          store.dispatch('Logout').then(() => {
            Vue.ls.remove(ACCESS_TOKEN);
            window.location.reload();
          });
        }
        notification.error({
          message: '系统提示',
          description: (data.message ? data.message : data.result_msg || data.msg),
          duration: 4
        });
        break;
    }
  }
  // eslint-disable-next-line new-cap
  return new Promise.reject(error);
};

// request interceptor
service.interceptors.request.use(config => {
  const token = Vue.ls.get(ACCESS_TOKEN);
  let tenantid = Vue.ls.get(TENANT_ID);
  source = CanCelToken.source();
  config.canCelToken = source.token;

  config.cancelToken = new axios.CancelToken(c => {
    // 数组存储一个cancel函数用于取消本次的网络请求
    store.commit('ADD_CANCEL_TOKEN', c);
  });
  // 添加参数到body和header中
  const getBonusParams = (isHeader = false) => {
    let userInfo = store.getters.userInfo;
    let excludePath = ['sys/login', 'sys/logout', '/sys/refreshToken', '/captcha/get', '/sys/user/add', '/sys/user/edit', '/sys/user/firstChangePassword', 'sys/user/passwordChange', '/sys/user/changePassword', '/sys/user/passwordChange'];
    if (!userInfo || !store.getters.token || !userInfo) {
      return null;
    }
    let bonusParams = null;
    if (!excludePath.some(itm => config.url.indexOf(itm) > -1)) {
      let isHasAllData = config.data && config.data.hasOwnProperty('hasAllData');
      bonusParams = {
        dataRoles: userInfo.dataRoles || [],
        userId: userInfo.id,
        sysTenantId: tenantid,
        token: store.getters.token, // 后端迁移结束判断条件去除
        userAccount: userInfo.username,
        username: isHeader ? encodeURIComponent(userInfo.realname) : userInfo.realname,
        orgCode: userInfo.orgCode,
        workNo: userInfo.workNo,
        needHandover: userInfo.needHandover,
        hasAllData: isHasAllData ? config.data.hasAllData : userInfo.hasAllData,
        lang: '_zh_CN'
      };
    }
    return bonusParams;
  };
  if (token) {
    config.headers['X-Access-Token'] = token;
  }
  config.headers['client-type'] = store.getters.device == 'mobile' ? 'app' : 'web';
  config.headers['device-id'] = localStorage.getItem('deviceId');
  config.headers['Authorization'] = 'Bearer ' + token;
  // if (token && config.url.indexOf("/sys/")>-1) {
  //   config.headers[ 'X-Access-Token' ] = token // 让每个请求携带自定义 token 请根据实际情况自行修改
  // } else {
  //   delete config.headers['X-Access-Token']
  // }

  // 添加参数到body和header中
  let headerBonusParams = getBonusParams(true);
  headerBonusParams && Object.assign(config.headers, headerBonusParams); // 阳光云的接口会报错

  config.headers['tenant_id'] = tenantid;
  config.headers['sys_code'] = '901';
  // for:多租户
  //  添加参数到body和header中
  // let bonusParams = getBonusParams();
  if (config.method == 'get') {
    let userInfo = store.getters.userInfo;
    config.params = {
      _t: Date.parse(new Date()) / 1000,
      userAccount: userInfo && userInfo.username ? userInfo.username : undefined,
      ...config.params
    };
    // }
    // bonusParams && Object.assign(config.params, bonusParams)
  } else if (config.method === 'post') {
    config.data = Object.assign({}, {}, {
      ...config.data
    }, {
      ...getBonusParams()
    });
  }
  return config;
}, (error) => {
  // eslint-disable-next-line new-cap
  return new Promise.reject(error).catch((e) => {});
});

// response intercepto
service.interceptors.response.use((response) => {
  isResponseHeaderToken(response);
  let data = response.data;
  let configUrl = response.config.url;
  let index = configUrl.indexOf(apiBaseUrl);
  let result = configUrl.slice(index + apiBaseUrl.length);
  let url = ['/sys/login', '/sys/user/sms', '/sys/sms'];
  if ((response.status == 200 && (data.result_code == '1' || data.result_code == '2' || data.success || data.repCode == '0000' || data.code == 0)) || response.config.responseType == 'blob' || url.includes(result)) {
    return response.data;
  } else if (response.status == 200 && !data.hasOwnProperty('code') && !data.hasOwnProperty('result_code') && !data.hasOwnProperty('success')) {
    return data;
  } else {
    return err(response);
  }
}, err);

const installer = {
  vm: {},
  install (Vue, router = {}) {
    Vue.use(VueAxios, router, service);
  }
};
/**
     * Blob解析
     * @param data
     */
function blobToJson (data) {
  let fileReader = new FileReader();
  let token = Vue.ls.get(ACCESS_TOKEN);
  fileReader.onload = function () {
    try {
      let jsonData = JSON.parse(this.result); // 说明是普通对象数据，后台转换失败
      if (jsonData.status === 500) {
        if (token && jsonData.message.includes('Token失效')) {
          store.dispatch('Logout').then(() => {
            logoutToSolareye()
            // Vue.ls.remove(ACCESS_TOKEN);
            // window.location.reload();
          });
        }
      }
    } catch (err) {
      // 解析成对象失败，说明是正常的文件流
      console.log('blob解析fileReader返回err', err);
    }
  };
  fileReader.readAsText(data);
}

export {
  installer as VueAxios,
  service as axios
};
