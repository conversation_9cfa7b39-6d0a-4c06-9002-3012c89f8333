import SlsTracker from '@aliyun-sls/web-track-browser';
const opts = {
  host: 'cn-hangzhou.log.aliyuncs.com', // 所在地域的服务入口。例如cn-hangzhou.log.aliyuncs.com
  project: 'k8s-log-c6e3ae5014d9c4eb4af16e413d3a42db4', // Project名称。
  logstore: 'app-error-log', // Logstore名称。
  time: 10, // 发送日志的时间间隔，默认是10秒。
  count: 10, // 发送日志的数量大小，默认是10条。
  topic: 'topic', // 自定义日志主题。
  source: 'source',
  tags: {
    tags: 'tags'
  }
};
const env = process.env.VUE_APP_ENV;
const tracker = new SlsTracker(opts);
window.tracker = tracker;
const list = ['unhandledrejection', 'error'];
const vueList = ["TypeError: Cannot read properties of null (reading 'tagName')", "TypeError: Cannot read properties of null (reading '_infiniteScrollHeight')"];
export function captureJSError (event) {
  if (process.env.NODE_ENV === 'development') {
    console.log(event.type);
    return;
  }
  if (env == 'pro' && list.indexOf(event.type) == -1) {
    tracker.send({
      err: (event.reason && event.reason.stack) || event.message,
      type: event.type || 'promise',
      env: env,
      platForm: 'health'
    });
  }
}
export function captureVueError (err, vm, info) {
  if (process.env.NODE_ENV === 'development' && vueList.indexOf(err.toString()) == -1) {
    console.log(err.name, info, err.stack);
    return;
  }
  if (env == 'pro' && vueList.indexOf(err.toString()) == -1) {
    tracker.send({
      err: err.stack,
      info: info,
      name: err.name,
      path: vm.$route.path,
      type: 'vue',
      env: env,
      platForm: 'health'
    });
  }
}
