import { axios } from '@/utils/erp-request';
import { postAction } from '@/api/manage';

// 查询员工信息
export function getEmployeeList (data) {
  return axios({
    url: '/employeeManager/v1/getEmployeeList',
    method: 'POST',
    data
  });
}
// 统计员工各状态
export function getEmployeeCount (data) {
  return axios({
    url: '/employeeManager/v1/getEmployeeCount',
    method: 'POST',
    data
  });
}
// 新增员工时-根据员工姓名、手机号码生成用户账号
export function autoGenerateEmployeeAccount (data) {
  return axios({
    url: '/employeeManager/v1/autoGenerateEmployeeAccount',
    method: 'POST',
    data
  });
}
// 新增
export function saveEmployeeBaseInfo (data) {
  return axios({
    url: '/employeeManager/v1/saveEmployeeBaseInfo',
    method: 'POST',
    data
  });
}
// 编辑
export function updateEmployeeBaseInfo (data) {
  return axios({
    url: '/employeeManager/v1/updateEmployeeBaseInfo',
    method: 'POST',
    data
  });
}
// 获取员工详情
export function getEmployeeBaseInfo (data) {
  return axios({
    url: '/employeeManager/v1/getEmployeeBaseInfo',
    method: 'POST',
    data
  });
}
// 获取导师列表
export function getTutorList (data) {
  return axios({
    url: '/employeeManager/v1/getTutorList',
    method: 'POST',
    data
  });
}
// 员工信息批量删除
export function deleteEmployee (data) {
  return axios({
    url: '/employeeManager/v1/deleteEmployee',
    method: 'POST',
    data
  });
}
// 图片预览
export function downloadBizAnnex (data) {
  return axios({
    url: '/system/v1/annex/downloadBizAnnex',
    method: 'POST',
    data
  });
}
// 员工导出
export function exportEmployeeList (data) {
  return axios({
    url: '/employeeManager/v1/exportEmployeeList',
    method: 'POST',
    data
  });
}
// 员工详情
export function exportEmployeeBaseInfoList (data) {
  return axios({
    url: '/employeeManager/v1/exportEmployeeBaseInfoList',
    method: 'POST',
    data
  });
}
// 组织关系
export function exportOrgRelation (data) {
  return axios({
    url: 'employeeManager/v1/exportOrgRelation',
    method: 'POST',
    data
  });
}
// 导出模板
export function downloadExcleTemplate (data) {
  return axios({
    url: '/employeeManager/v1/downloadExcleTemplate',
    method: 'POST',
    data
  });
}
// 角色判断
export function checkUserIsHROrEngineer (data) {
  return axios({
    url: '/employeeManager/v1/checkUserIsHROrEngineer',
    method: 'POST',
    data
  });
}
// 个人主页-删除银行卡信息
export function delBankById (data) {
  return axios({
    url: '/employeeManager/v1/delBankById',
    method: 'POST',
    data
  });
}
// 个人主页-删除个人主页之证书信息
export function delCertificateById (data) {
  return axios({
    url: '/employeeManager/v1/delCertificateById',
    method: 'POST',
    data
  });
}
// 个人主页-删除个人主页之学历信息
export function delErpEmpEduById (data) {
  return axios({
    url: '/employeeManager/v1/delErpEmpEduById',
    method: 'POST',
    data
  });
}
// 个人主页-删除个人主页之联系人信息
export function delErpEmpEmerContactById (data) {
  return axios({
    url: '/employeeManager/v1/delErpEmpEmerContactById',
    method: 'POST',
    data
  });
}
// 个人主页-删除个人主页之合同信息
export function delContractById (data) {
  return axios({
    url: '/employeeManager/v1/delContractById',
    method: 'POST',
    data
  });
}
// 个人主页-删除个人主页之照片信息
export function delPhotoById (data) {
  return axios({
    url: '/employeeManager/v1/delPhotoById',
    method: 'POST',
    data
  });
}
// 个人主页-新增、更新个人主页之学历信息
export function saveEmpEdu (data) {
  return axios({
    url: '/employeeManager/v1/saveEmpEdu',
    method: 'POST',
    data
  });
}
// 个人主页-新增、更新个人主页之联系人信息
export function saveEmpEmerContact (data) {
  return axios({
    url: '/employeeManager/v1/saveEmpEmerContact',
    method: 'POST',
    data
  });
}
// 个人主页-新增、更新个人主页之合同信息
export function saveContract (data) {
  return axios({
    url: '/employeeManager/v1/saveContract',
    method: 'POST',
    data
  });
}
// 个人主页-新增、更新个人主页之银行卡信息
export function saveBank (data) {
  return axios({
    url: '/employeeManager/v1/saveBank',
    method: 'POST',
    data
  });
}
// 个人主页-新增、更新个人主页之照片信息
export function savePhoto (data) {
  return axios({
    url: '/employeeManager/v1/savePhoto',
    method: 'POST',
    data
  });
}
// 个人主页-新增、更新个人主页之证书信息
export function saveCertificate (data) {
  return axios({
    url: '/employeeManager/v1/saveCertificate',
    method: 'POST',
    data
  });
}
// 个人主页-更新个人主页之基本信息
export function updateHomepageEmployee (data) {
  return axios({
    url: '/employeeManager/v1/updateHomepageEmployee',
    method: 'POST',
    data
  });
}
// 个人主页-删除薪资信息
export function deleteSalary (data) {
  return axios({
    url: '/employeeManager/v1/deleteSalary',
    method: 'POST',
    data
  });
}

// 下载薪资模板
export function downloadSalaryListTemplate (data) {
  return axios({
    url: '/employeeManager/v1/download',
    method: 'POST',
    data
  });
}

// 导出薪资
export function exportSalaryList (data) {
  return axios({
    url: '/employeeManager/v1/exportSalaryList',
    method: 'POST',
    data
  });
}
// 导入薪资
export function importSaraly (data) {
  return axios({
    url: '/employeeManager/v1/importSaraly',
    method: 'POST',
    data
  });
}

export function queryRoleByPosition (data) {
  return axios({
    url: '/employeeManager/v1/queryRoleByPosition',
    method: 'POST',
    data
  });
}

export function queryProvinceAndCity (data) {
  return axios({
    url: '/employeeManager/v1/queryProvinceAndCity',
    method: 'POST',
    data
  });
}
const baseUrl = process.env.VUE_APP_API_ERP_URL;
// 获取账号
const queryAccount = (params) => postAction(baseUrl + '/employeeManager/v1/autoCreateWorkNo', params);
export {
  queryAccount
};

// 调动轨迹列表
export function getTransferTrajectoryInfo (data) {
  return axios({
    url: '/employeeManager/v1/getEmployeeTransferTrack',
    method: 'POST',
    data
  });
}

// 调动轨迹列表导出
export function exportTrajectoryList (data) {
  return axios({
    url: '/employeeManager/v1/exportTrajectoryList',
    method: 'POST',
    data
  });
}
// ocr 识别导入
export function certRecognition (data) {
  return axios({
    url: '/employeeManager/v1/certRecognition',
    method: 'POST',
    data
  });
}
