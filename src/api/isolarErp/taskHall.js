// 任务大厅
import { axios } from '@/utils/erp-request';

export function getTaskHallPage (data) {
  return axios({
    url: '/pre/test/page',
    method: 'post',
    data
  });
}

export function getTaskHallCount (data) {
  return axios({
    url: '/pre/test/count',
    method: 'post',
    data
  });
}

// 新增任务
export function insertPreTest (data) {
  return axios({
    url: '/pre/test/insert',
    method: 'post',
    data
  });
}
// 终止任务
export function terminationTestApi (data) {
  return axios({
    url: '/pre/test/termination',
    method: 'post',
    data
  });
}
// 任务详情
export function taskDetailApi (data) {
  return axios({
    url: '/pre/test/detail',
    method: 'post',
    data
  });
}
// 报告导出
export function checkReportApi (data) {
  return axios({
    url: '/pre/test/check/report',
    method: 'POST',
    data
  });
}
