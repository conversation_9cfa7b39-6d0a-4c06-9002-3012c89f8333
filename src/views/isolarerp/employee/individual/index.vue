<template>
  <div class="page-header-index-wide solar-eye-pure-bg" style="height: 100%;">
    <a-spin :spinning="loading">
      <template v-if="form">
        <base-form :form="form" :dict="dictMap" />
        <div class="tabs-model">
          <!-- tabs 学历、联系人、合同、银行卡、照片、资格证书 -->
          <a-tabs v-model="active" @change="tabChange">
            <a-tab-pane key="education" tab="学历信息">
              <education ref="education" :empId="empId" :forceRender="true" @refresh="getEmpInfo" />
            </a-tab-pane>
            <a-tab-pane key="contacts" tab="联系人信息" :forceRender="true">
              <contacts ref="contacts" :empId="empId" :type="true" @refresh="getEmpInfo" />
            </a-tab-pane>
            <a-tab-pane key="contract" tab="合同信息" :forceRender="true">
              <contract ref="contract" :empId="empId" @refresh="getEmpInfo" />
            </a-tab-pane>
            <a-tab-pane key="card" tab="银行卡信息" :forceRender="true">
              <card ref="card" :empId="empId" :type="true" @refresh="getEmpInfo" />
            </a-tab-pane>
            <a-tab-pane key="picFile" tab="照片附件" :forceRender="true">
              <picFile ref="picFile" :empId="empId" :empName="form.empName" :type="true" @refresh="getEmpInfo" />
            </a-tab-pane>
            <a-tab-pane key="certificate" tab="资格证书" :forceRender="true">
              <certificate ref="certificate" :empId="empId" :empName="form.empName" :type="true" @refresh="getEmpInfo" pageFrom="individual" />
            </a-tab-pane>
          </a-tabs>
        </div>
      </template>
      <template v-else>
        <div class="no-date com-color">
          <div class="no-date-img">
          </div>
          <div>未查询到用户数据</div>
        </div>
      </template>
    </a-spin>
  </div>
</template>

<script>
import initDict from '@/mixins/initDict';
import baseForm from './base.vue'; // 基本信息
import education from '../employeeList/education';
import contacts from '../employeeList/contacts';
import contract from '../employeeList/contract';
import card from '../employeeList/card';
import picFile from '../employeeList/picFile';
import certificate from '../employeeList/certificate';
import { USER_NAME } from '@/store/mutation-types';
import { getEmployeeBaseInfo } from '@/api/isolarErp/employee/employee';
export default {
  name: 'index',
  mixins: [initDict],
  components: {
    baseForm,
    education,
    contacts,
    contract,
    card,
    picFile,
    certificate
  },
  data () {
    return {
      active: 'education',
      form: null,
      loading: false,
      oldRow: null,
      empId: ''
    };
  },
  created () {
    // 加载数据字典
    const dict = 'emp_pic_type,position,nation,political_status,yes_no_emp,marital_sts,country,property,level,work_sts_emp,probation_period,work_tic_thr_ppl,sex';
    this.getDictMap(dict);
    this.getEmpInfo();
  },
  methods: {
    // 获取用户信息
    getEmpInfo () {
      this.loading = true;
      const map = {
        account: Vue.ls.get(USER_NAME)
      };
      getEmployeeBaseInfo(map).then(res => {
        if (res.result_code == '1') {
          this.initForm(res.result_data);
        } else {
          this.empId = '';
          this.loading = false;
          this.$message.warning(res.result_msg);
        }
      }).catch(() => {
        this.loading = false;
      });
    },
    // 加载数据
    initForm (data) {
      if (!data.baseInfo) {
        this.loading = false;
        return;
      }
      let form = data.baseInfo;
      this.form = form;
      this.empId = form.empId;
      this.$nextTick(() => {
        this.loading = false;
        const erpEmpEdu = (data.hasOwnProperty('erpEmpEdu') && data.erpEmpEdu ? data.erpEmpEdu : []);
        this.$refs['education'].init(erpEmpEdu, '3');
        const erpEmpEmerContact = (data.hasOwnProperty('erpEmpEmerContact') && data.erpEmpEmerContact ? data.erpEmpEmerContact : []);
        this.$refs['contacts'].init(erpEmpEmerContact, '3');
        const contract = (data.hasOwnProperty('contract') && data.contract ? data.contract : []);
        this.$refs['contract'].init(contract, '3');
        const bank = (data.hasOwnProperty('bank') && data.bank ? data.bank : []);
        this.$refs['card'].init(bank, '3');
        const certificate = (data.hasOwnProperty('certificate') && data.certificate ? data.certificate : []);
        this.$refs['certificate'].init(certificate, '3');
        const picFile = (data.hasOwnProperty('photo') && data.photo ? data.photo : []);
        this.$refs['picFile'].init(picFile, '3');
      });
    }
  }
};
</script>

<style lang="less" scoped>
  .page-header-index-wide {
    width: 100%;
    padding-top: 5px;
    padding-bottom: 10px;
    padding-right: 12px;
    padding-left: 12px;
    overflow: hidden auto;
  }

  .tabs-model {
    border: 1px solid #6eb9ef;
    padding: 0 20px 20px 20px;
    border-radius: 5px;
    margin-top: 20px;
  }

  .no-date {
    width: 100%;
    height: 80vh;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    font-size: 16px;
  }

  .no-date-img {
    width: 323px;
    height: 516px;
    background: url('../../../../assets/images/public/no-data.png') no-repeat;
    background-size: 100% 100%;
    margin-bottom: 16px;
  }

  :deep(.top-btn-group) {
    margin-bottom: 10px;

    :deep(.ant-btn) {
      margin-right: 10px;
    }
  }
</style>
