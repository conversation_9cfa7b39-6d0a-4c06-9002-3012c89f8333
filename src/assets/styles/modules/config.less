// 公共单行样式 比如 间距 边框 布局 旋转 定位等
// margin和padding原子化类名
@arr: 0, 2, 4, 8, 10, 12, 16, 20, 24, 28, 30, 32, 36, 40, 44, 48, 52, 56;

each(@arr, {
  @num: extract(@arr, @index);

  .m-@{value} {
    margin: ~"@{num}px";
  }

  .m-t-@{value} {
    margin-top: ~"@{num}px";
  }

  .m-r-@{value} {
    margin-right: ~"@{num}px";
  }

  .m-b-@{value} {
    margin-bottom: ~"@{num}px";
  }

  .m-l-@{value} {
    margin-left: ~"@{num}px";
  }

  // vertical 垂直方向
  .m-v-@{value} {
    margin-top: ~"@{num}px";
    margin-bottom: ~"@{num}px";
  }

  // horizontal 水平方向
  .m-h-@{value} {
    margin-left: ~"@{num}px";
    margin-right: ~"@{num}px";
  }

  .p-@{value} {
    padding: ~"@{num}px";
  }

  .p-t-@{value} {
    padding-top: ~"@{num}px";
  }

  .p-r-@{value} {
    padding-right: ~"@{num}px";
  }

  .p-b-@{value} {
    padding-bottom: ~"@{num}px";
  }

  .p-l-@{value} {
    padding-left: ~"@{num}px";
  }

  // vertical 垂直方向
  .p-v-@{value} {
    padding-top: ~"@{num}px";
    padding-bottom: ~"@{num}px";
  }

  // horizontal 水平方向
  .p-h-@{value} {
    padding-left: ~"@{num}px";
    padding-right: ~"@{num}px";
  }
});

// width原子化类名 px
@widthArr: 80, 90, 100, 110, 120, 130, 140;

each(@widthArr, {
  @num: extract(@widthArr, @index);

  .width-@{value} {
    width: ~"@{num}px";
  }
});

/**
 * flex布局
 * @param {string} @justifyContent
 * @param {string} @alignItems
 * @param {string} @display
 */
.layoutFlex(@justifyContent: center, @alignItems: center, @display: flex ) {
  justify-content: @justifyContent;
  align-items: @alignItems;
  display: @display;
}

//边框
.border {
  border: 1px solid var(--zw-border-color--default);
}

.border-t {
  border-top: 1px solid var(--zw-border-color--default);
}

.border-b {
  border-bottom: 1px solid var(--zw-border-color--default);
}

.border-l {
  border-left: 1px solid var(--zw-border-color--default);
}

.border-r {
  border-right: 1px solid var(--zw-border-color--default);
}

// 上下边框
.border-v {
  border-top: 1px solid var(--zw-border-color--default);
  border-bottom: 1px solid var(--zw-border-color--default);
}

// 常用flex布局类名
.zw-flex-center {
  display: flex;
  flex-direction: row;
  align-items: center;
}

//纵向布局
.flex-col {
  flex-direction: column;
}

//横向翻转布局
.flex-row-r {
  flex-direction: row-reverse;
}

//纵向翻转布局
.flex-column-r {
  flex-direction: column-reverse;
}

.flex-column {
  flex-direction: column;
}

//两边对齐第一个元素弹性布局
.flex-baseline {
  .layoutFlex(space-between, baseline);
}

//两边对齐弹性布局
.flex-between {
  .layoutFlex(space-between);
}

//间隔相等弹性布局
.flex-around {
  .layoutFlex(space-around);
}

//水平居中
.flex-center {
  .layoutFlex();
}

//弹性布局从头排
.flex-start {
  .layoutFlex(flex-start);
}

//弹性布局从尾排
.flex-end {
  .layoutFlex(flex-end);
}

//弹性布局项目缩放
.flex-grow {
  flex-grow: 1;
}

.flex-shrink {
  flex-shrink: 0;
}

//弹性布局换行
.flex-wrap {
  flex-wrap: wrap;
}

//弹性布局文字不换行，溢出显示...
.flex-1 {
  flex: 1;
  overflow: hidden;
}

//项目顶部对齐
.align-start {
  align-self: flex-start;
}

//项目尾部对齐
.align-end {
  align-self: flex-end;
}

//相对定位
.relative {
  position: relative;
}

//绝对定位
.absolute {
  position: absolute;
}

//粘性定位
.sticky {
  position: sticky;
  top: 0;
  left: 0;
  z-index: 400;
}

//水平垂直居中定位(父元素要是定位元素)
.zw-center {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

//行内块
.ib {
  display: inline-block;
}

//行内元素对齐
.text-c {
  text-align: center;
}

.text-l {
  text-align: left;
}

.text-r {
  text-align: right;
}

// 透明度
.opacity-0 {
  opacity: 0;
}

.opacity-20 {
  opacity: .2;
}

.opacity-40 {
  opacity: .4;
}

.opacity-50 {
  opacity: .5;
}

.opacity-70 {
  opacity: .7;
}

// 旋转
.rotate-90 {
  transform: rotate(90deg);
}

.rotate-180 {
  transform: rotate(180deg);
}

.rotate-270 {
  transform: rotate(270deg);
}

// 常用宽高-100%
.height-100 {
  height: 100%;
}

.width-100 {
  width: 100%;
}

.width-height-100 {
  width: 100%;
  height: 100%;
}

.no-padding {
  padding: 0 !important;
}

.no-margin {
  margin: 0 !important;
}

.no-bg {
  background: transparent !important;
}

.no-border {
  border: none !important;
}

//鼠标手指
.pointer {
  cursor: pointer;

  &:hover {
    color: var(--zw-primary-color--default);
  }
}

.cursor-pointer {
  cursor: pointer;
}

.flex-gap-8 {
  gap: 8px;
}

.flex-gap-12 {
  gap: 12px;
}

.flex-gap-16 {
  gap: 16px;
}

.flex-gap-24 {
  gap: 24px;
}

