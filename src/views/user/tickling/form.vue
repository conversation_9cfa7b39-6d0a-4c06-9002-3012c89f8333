<template>
  <div class="drawer-form-com">
    <div class="drawer-form-content">
      <a-spin :spinning="loading">
        <a-form-model :model="dataForm" :rules="rules" ref="dataForm" :labelCol="{ style: 'width: 135px' }" :wrapperCol="{ style: 'width: calc(100% - 135px)' }">
          <a-row type="flex">
            <div v-if="isComQuestion == '1'" class="formTitle com-color">问题内容</div>
            <div v-if="isComQuestion == '0'" class="formTitle com-color">常见问题</div>
            <a-col :span='24'>
              <a-form-model-item label="问题类型" prop="questionType">
                <a-radio-group v-model="dataForm.questionType " :disabled="!(isEdit || type=='5')" @change="onChange">
                  <a-radio-button value="1">
                    新需求
                  </a-radio-button>
                  <a-radio-button value="2">
                    优化建议
                  </a-radio-button>
                  <a-radio-button value="3">
                    使用异常
                  </a-radio-button>
                  <a-radio-button value="4">
                    问题咨询
                  </a-radio-button>
                  <a-radio-button value="5">
                    其他问题
                  </a-radio-button>
                </a-radio-group>
              </a-form-model-item>
            </a-col>
          </a-row>
          <a-row>
            <a-col :span='24'>
              <a-form-model-item label="问题描述" prop="questionDesc">
                <a-textarea size="default" :max-length="300" :disabled="!isEdit" :auto-size="{ minRows: 2, maxRows: 4}"
                  v-model="dataForm.questionDesc" style="width: 100%;"></a-textarea>
              </a-form-model-item>
            </a-col>
          </a-row>
          <a-row v-if="['2','4'].includes(type)|| ((type=='3'||type=='6') &&isComQuestion == '0')">
            <a-col :span='24'>
              <a-form-model-item label="解决方案" prop="answerContent">
                <a-textarea size="default" :max-length="500" :disabled="!isEdit" :auto-size="{ minRows: 3, maxRows: 5}"
                  v-model="dataForm.answerContent" style="width: 100%;"></a-textarea>
              </a-form-model-item>
            </a-col>
          </a-row>
          <a-row>
            <a-col :span='12'>
              <a-form-model-item label="图片" prop="uploadFileList">
                <file-upload v-model="dataForm.uploadFileList" :isEdit="isEdit" :iconType="iconType" :noConvert="'1'" :showDownload="true"
                  :accept="accept" :maxSize="10" :maxNum="3" :tip="tip" />
              </a-form-model-item>
            </a-col>
          </a-row>

          <a-row v-if="['5'].includes(type) || replyDisabled">
            <div class="formTitle com-color">问题回复</div>
            <a-col :span='24'>
              <a-form-model-item label="回复内容" prop="replyContent">
                <a-textarea size="default" :max-length="300" :disabled="type != '6' && type !='5'" :auto-size="{ minRows: 2, maxRows: 4}" placeholder="请输入您的回复内容"
                  v-model="dataForm.replyContent" style="width: 100%;"></a-textarea>
              </a-form-model-item>
            </a-col>
            <a-col :span='12'>
              <a-form-model-item label="图片" prop="replyUploadFileList">
                <file-upload v-model="dataForm.replyUploadFileList" :isEdit="['5','6'].includes(type)" :iconType="iconType" :noConvert="'1'" :showDownload="true"
                  :accept="accept" :maxSize="10" :maxNum="3" :tip="tip" />
              </a-form-model-item>
            </a-col>
          </a-row>

          <a-row>
            <div v-if="['1', '2' ,'4' ,'5'].includes(type) || replyDisabled" class="form-foot">
              <throttle-button label="提交" :loading="loading" @click="doSubmit()" />
              <throttle-button label="取消" type="info" @click="cancel" />
            </div>
            <div v-if="closeDisabled" class="form-foot">
              <throttle-button label="解决" :loading="loading" @click="closeFeedBack()" />
              <throttle-button label="再回复" type="warning" :loading="loading" @click="replyClick()" />
            </div>
          </a-row>

          <a-row v-if="['3','5','6'].includes(type)&&isComQuestion == '1'">
            <a-divider></a-divider>
            <div class="formTitle com-color">历史记录</div>
            <template v-for="(item,index) in dataForm.answers">
              <a-col :span='24' :key="item.createTime">
                <span style="margin-left: 10px;">{{(index+1) + '、' + item.createTime}} &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;{{item.createUser}} &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                  {{item.operation }}</span>
              </a-col>
              <a-col :span='24' :key="index">
                <a-textarea v-if="item.answerContent" size="default" disabled :auto-size="{ minRows: 2, maxRows: 4}"
                  v-model="item.answerContent" style="width: 100%;margin-left: 40px;"></a-textarea>
              </a-col>
              <a-col :span='12' v-if="item.answerFileList" :key="index*10">
                <a-form-model-item>
                  <file-upload v-if="item.answerFileList" v-model="item.answerFileList" :isEdit="false" :iconType="iconType" :noConvert="'1'" :showDownload="true"
                    :accept="accept" :maxSize="30" :maxNum="10" :tip="tip" style="width: 100%;margin-left: 40px;" />
                </a-form-model-item>
              </a-col>
            </template>
          </a-row>
        </a-form-model>
      </a-spin>
    </div>

  </div>
</template>

<script>
import { saveFeedBack, detailFeedBack, closeFeedBack } from '@/api/isolarErp/tickling';
import initDict from '@/mixins/initDict';
import moment from 'moment';
import fileUpload from '@/components/erp/upload/fileUpload';
export default {
  components: {
    fileUpload
  },
  mixins: [initDict],
  props: {
    // 流程图抽屉挂在的DOM节点ID，必要参数
    parentId: {
      type: String,
      required: true
    }
  },
  data () {
    return {
      loading: false,
      isEdit: false,
      title: '',
      type: '',
      isComQuestion: '',
      replyDisabled: false,
      closeDisabled: false,
      // 附件上传
      accept: '.jpg,.png,.jpeg,.bmp',
      iconType: 'button',
      tip: '只能上传jpg、jpeg、png、bmp文件，且上传的附件最大不超过10MB!',
      fileList: [], // 附件备份
      fileListEdit: [], // 附件编辑后汇总
      dataForm: {
        id: '',

        questionType: '',
        questionDesc: '',
        answerContent: '',
        uploadFileList: undefined,

        replyContent: '',
        replyUploadFileList: undefined,

        answers: [],
        delQaFile: []
      },
      rules: {
        questionType: [{ required: true, message: '请选择问题类型', trigger: 'change' }],
        questionDesc: [{ required: true, message: '请填写问题描述', trigger: 'blur' }],

        answerContent: [{ required: false, message: '请填写解决方案', trigger: 'blur' }],
        replyContent: [{ required: false, message: '请填写回复内容', trigger: 'blur' }]
      }
    };
  },
  created () {
    // 加载数据字典
    this.getDictMap('question_type');
  },
  methods: {
    moment,
    // 打开流程图
    openFlowChart () {
      this.$refs.flowChartDrawer.openView();
    },
    // 切换抽屉时动画结束后的回调，页面初始化init函数的异步请求须放在这里调用，否则可能会会导致抽屉侧滑出来时卡死
    afterEvent () {
      let self = this;
      this.$nextTick(() => {
        self.rulesChange();
        if (self.type != '1' && self.type != '2') {
          self.initDetail();
        } else {
          self.loading = false;
        }
      });
    },
    // type 反馈问题 1  新增常见问题 2 查看 3  编辑  4  回复 5  查看(需要刷新主页面) 6
    init (type, row) {
      let self = this;
      self.isEdit = ['1', '2', '4'].includes(type);
      self.loading = true;
      self.type = type;
      if (type == '1') {
        self.isComQuestion = '1';
        self.dataForm.questionDesc = '问题描述：\n\n\n期望：';
      } else if (type == '2') {
        self.isComQuestion = '0';
        self.dataForm.questionDesc = '问题描述：如何修改密码？';
        self.dataForm.answerContent = '解决方案：\n第一步\n\n第二步\n\n第三步';
      } else if (type == '6') {
        self.closeDisabled = true;
      }
      if (row) {
        self.dataForm.id = row.id;
        self.isComQuestion = row.isComQuestion;
      }
      self.afterEvent();
      switch (type) {
        case '1':
          return '意见反馈';
        case '2':
          return '常见问题';
        case '3':
          return '查看';
        case '4':
          return '编辑';
        case '5':
          return '回复';
        case '6':
          return '查看';
      }
    },
    // 详情
    initDetail () {
      const self = this;
      self.loading = true;
      let map = {
        id: self.dataForm.id
      };
      detailFeedBack(map).then(res => {
        if (res.result_code == '1' && res.result_data) {
          let resultData = res.result_data;
          resultData.delQaFile = [];
          if (resultData.uploadFileList) {
            resultData.uploadFileList.forEach(item => {
              item.name = item.annexName;
              item.delete = true;
              item.uid = item.fileId;
              item.id = item.fileId;
              item.url = '';
            });
            if (self.type == '4') {
              self.fileList = [...resultData.uploadFileList];
            }
          }
          if (resultData.replyUploadFileList) {
            resultData.replyUploadFileList.forEach(item => {
              item.name = item.annexName;
              item.delete = true;
              item.uid = item.fileId;
              item.id = item.fileId;
              item.url = '';
            });
          }
          resultData.answers.forEach(item => {
            if (item.answerFileList) {
              item.answerFileList.forEach(item => {
                item.name = item.annexName;
                item.delete = true;
                item.uid = item.fileId;
                item.id = item.fileId;
                item.url = '';
              });
            }
          });
          if (self.type == '3' || self.type == '5' || self.type == '6') {
            resultData.replyContent = '';
            resultData.replyUploadFileList = undefined;
          }
          Object.assign(self.dataForm, resultData);
        }
        self.loading = false;
      }).catch(() => {
        self.loading = false;
      });
    },
    // 提交
    doSubmit () {
      this.loading = true;
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          this.saveFeedBack();
        } else {
          this.loading = false;
          return false;
        }
      });
    },
    // 保存方法
    saveFeedBack () {
      const self = this;
      // 读文件
      self.getBlob().then(() => {
        self.getDelFile();
        self.saveOrUpdate();
      });
    },
    // 提交、再回复
    saveOrUpdate () {
      const self = this;
      let map = self.dataForm;
      map.buttonType = self.type;
      saveFeedBack(map).then(res => {
        if (res.result_code == '1') {
          self.$message.success('操作成功');
          self.cancel();
        }
        self.loading = false;
      }).catch(() => {
        self.loading = false;
      });
    },
    // 文件转base64
    getBlob () {
      return new Promise((resolve, reject) => {
        let promiseList = [];
        const self = this;
        if (self.dataForm.uploadFileList) {
          self.fileListEdit = [...self.fileListEdit, ...self.dataForm.uploadFileList];
        }

        if (['1', '2', '4'].includes(self.type)) {
          let uploadFileList = [];
          if (self.dataForm.uploadFileList) {
            uploadFileList = self.dataForm.uploadFileList.filter(item => !item.hasOwnProperty('delete'));
          }
          uploadFileList.forEach(file => {
            let promise = new Promise((resolve, reject) => {
              let reader = new FileReader();
              reader.readAsDataURL(file);
              // 转base64
              reader.onload = function () {
                let blob = reader.result.split(',')[1];
                file.file = blob;
                file.fileName = file.name;
                resolve(true);
              };
            });
            promiseList.push(promise);
          });
          self.dataForm.uploadFileList = uploadFileList;
        } else {
          let replyUploadFileList = [];
          if (self.dataForm.replyUploadFileList) {
            replyUploadFileList = self.dataForm.replyUploadFileList.filter(item => !item.hasOwnProperty('delete'));
          }
          replyUploadFileList.forEach(file => {
            let promise = new Promise((resolve, reject) => {
              let reader = new FileReader();
              reader.readAsDataURL(file);
              // 转base64
              reader.onload = function () {
                let blob = reader.result.split(',')[1];
                file.file = blob;
                file.fileName = file.name;
                resolve(true);
              };
            });
            promiseList.push(promise);
          });
          self.dataForm.replyUploadFileList = replyUploadFileList;
        }
        Promise.all(promiseList).then(values => {
          resolve(values);
        }, reason => {
          reject(reason);
        });
      });
    },

    // 获取删除的原数据id
    getDelFile () {
      const self = this;
      let old = [];
      if (self.fileList.length > 0) {
        self.fileList.forEach(item => {
          old.push(item.id);
        });
        if (self.fileListEdit.length == 0) {
          self.dataForm.delQaFile = old;
        }
        let now = [];
        self.fileListEdit.forEach(item => {
          if (item.hasOwnProperty('delete')) {
            now.push(item.id);
          }
        });
        self.dataForm.delQaFile = old.filter(item => {
          return !now.includes(item);
        });
      } else {
        self.dataForm.delQaFile = old;
      }
    },
    // 再回复点击事件
    replyClick () {
      this.replyDisabled = true;
      this.closeDisabled = false;
    },
    // 关闭
    closeFeedBack () {
      let self = this;
      self.$confirm({
        title: '关闭后此问题将不能再回复，确定要关闭吗?',
        confirmText: '确定',
        okText: '确定',
        cancelText: '取消',
        onOk () {
          self.loading = true;
          let map = {
            'id': self.dataForm.id
          };
          closeFeedBack(map).then(res => {
            if (res.result_code == '1') {
              self.$message.success('操作成功');
              self.cancel();
            }
            self.loading = false;
          }).catch(() => {
            self.loading = false;
          });
        }
      });
    },
    // 弹窗关闭回调方法
    cancel () {
      this.$emit('cancel');
      this.reset();
    },
    reset () {
      let dictMap = { ...this.dict, ...this.dictMap };
      Object.assign(this.$data, this.$options.data());
      this.$refs.dataForm.resetFields();
      this.$refs.dataForm.clearValidate();
      this.dictMap = dictMap;
    },
    // rulesChange事件
    rulesChange () {
      Object.assign(this.rules, this.$options.data().rules);
      if (this.type == '2' || this.type == '3' || this.type == '4' || this.type == '6') {
        this.rules.answerContent[0].required = true;
      }
      if (this.type == '5' || this.type == '6') {
        this.rules.replyContent[0].required = true;
      }
    }

  }
};
</script>

<style lang="less" scoped>
:deep(.ant-form-item) {
    width: 100%;
    display: inline-flex;
  }

  .form-foot {
    text-align: center;
    padding: 10px 0;

    button+button {
      margin-bottom: 0;
      margin-left: 16px;
    }
  }

  .formTitle {
    font-weight: bold;
    padding: 24px 16px;
    width: 100%;
  }

  :deep(.ant-divider-horizontal) {
    margin: 0px;
  }

  .ant-radio-button-wrapper {
    margin-right: 16px !important;

  }
</style>
