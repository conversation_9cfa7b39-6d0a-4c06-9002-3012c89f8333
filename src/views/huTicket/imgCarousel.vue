<!-- 票面预览 -->
<template>
  <a-drawer :title="title" width="100%" :visible="visible" @close="cancel" :get-container="getContainer" :wrap-style="{ position: 'absolute' }"
    :afterVisibleChange="afterVisibleChange" :destroyOnClose="true" class="drawer-box">
    <div class="drawer-form-com">
      <div class="drawer-form-content">
        <a-carousel arrows dots-class="slick-dots slick-thumb">
          <a slot="customPaging" slot-scope="props">
            <img :src="getImgUrl(props.i)" />
          </a>
          <div v-for="(item, index) in items" :key="index">
            <img :src="item" id="imgId"/>
          </div>
        </a-carousel>
      </div>
    </div>
    <!-- 下载、最大最小化-->
    <div class="top-group-btn">
      <a-button  size="default" icon="redo" title="旋转" @click="rotate">
      </a-button>
    </div>
  </a-drawer>
</template>

<script>
export default {
  name: 'drawerView',
  props: {
    // 图片路径
    items: {
      type: Array,
      default: () => []
    },
    visible: {
      type: Boolean,
      default: null
    },
    /* 抽屉挂在的DOM节点ID，必要参数 */
    parentId: {
      type: String,
      required: true,
      default: ''
    },
    title: {
      type: String,
      required: true,
      default: ''
    }
  },
  data () {
    return {
      numCar: 0
    };
  },
  watch: {
    'parentId' (val, old) {
      this.getContainer();
    }
  },
  methods: {
    // 获取抽屉组件挂在的DOM
    getContainer () {
      let mains = document.querySelectorAll('.main');
      return document.getElementById(this.parentId) || mains[mains.length - 1].querySelector('div');
    },
    // 抽屉关闭回调事件
    cancel (map) {
      var box = document.querySelector('.slick-active').querySelector('#imgId');
      box.style.transform = 'rotateZ(' + 0 + 'deg)';
      this.numCar = 0;
      this.$emit('update:items', []);
      this.$emit('update:visible', false);
    },
    rotate () {
      this.numCar = this.numCar + 1;
      var box = document.querySelector('.slick-active').querySelector('#imgId');
      box.style.transform = 'rotateZ(' + 90 * this.numCar + 'deg)';
    },
    // 切换抽屉时动画结束回调事件
    afterVisibleChange (visible) {
      if (visible && !this.items.length) {
        this.$notification.error({
          message: '系统提示',
          description: '附件加载失败，请重新操作',
          duration: 2
        });
      }
    },
    getImgUrl (i) {
      return this.items[i];
    }
  }
};
</script>

<style lang="less" scoped>
  :deep(.ant-drawer-body) {
    background: #fff;
  }

  :deep(.slick-current img) {
    margin: auto;
    max-height: 100%;
  }

  :deep(.slick-dots li button) {
    background: #1890FF;
  }

  :deep(.ant-carousel) {
    height: calc(100% - 50px);
  }

  :deep(.slick-initialized) {
    height: 100%;
  }

  :deep(.slick-list) {
    height: 100%;
  }

  :deep(.slick-track) {
    height: 100%;
  }

  :deep(.slick-slide) {
    height: 100%;
  }

  :deep(.slick-slide div) {
    height: 100%;
  }

  /* For demo */
  :deep(.ant-carousel .slick-dots) {
    height: auto;
  }

  :deep(.ant-carousel .slick-slide img) {
    border: 5px solid #fff;
    display: block;
    margin: auto;
    max-height: 100%;
  }

  :deep(.ant-carousel .slick-thumb) {
    bottom: -45px;
  }

  :deep(.ant-carousel .slick-thumb li) {
    width: 60px;
    height: 45px;
  }

  :deep(.ant-carousel .slick-thumb li img) {
    width: 100%;
    height: 100%;
    filter: grayscale(100%);
  }

  :deep(.ant-carousel .slick-thumb li.slick-active img) {
    filter: grayscale(0%);
  }

  .top-group-btn {
    position: absolute;
    right: 50px;
    top: 0;
    background: transparent;
    display: flex;
    align-items: center;
    .ant-btn {
      border: none;
      height: 50px;
      width: 40px;
      line-height: 60px;
      background: transparent;
    }
    :deep(.ant-btn-icon-only > i) {
      color: inherit;
      vertical-align: unset !important;
    }
  }
</style>
