<!--  @creator l<PERSON><PERSON>
      @date 2021/02/24
      @description   健康度分析模块:组串离散率分析；-->
<template>
  <div class="history-analysis-model isolar-layout">
    <a-row :gutter="16" class="history-analysis" ref="DispersionRateAnalysis">
      <a-col :md="4" :sm="24">
        <Funnel @refreshData="refreshData"></Funnel>
      </a-col>
      <a-col :md="20" :sm="24" style="height: 100%">
        <a-card
          :bodyStyle="{ height: '100%' }"
          :style="{height:chartShow ?cardHeight +'px' :''}"
          class="solareye-card"
          :title="title"
        >
          <a slot="extra" href="#">
            <PowerStationWeather v-if="psId" :psId="psId" />
          </a>
          <div class="right-info-row between" :style="{marginBottom: !chartShow? '16px' :0 }">
            <a-space style="padding-top: 16px">
              <label>诊断时间：</label>
              <a-date-picker
                v-model="recordDate"
                type="date"
                format="YYYY/MM/DD"
                value-format="YYYY/MM/DD"
                placeholder="选择日期"
                :getCalendarContainer="
                    (trigger) => trigger.parentNode || document.body
                  "
                @change="clickSearch"
              >
                <template slot="dateRender" slot-scope="current">
                  <div
                    :class="`ant-calendar-date ${getCurrentClassName(
                        current
                      )}`"
                  >{{ current.date() }}</div>
                </template>
              </a-date-picker>
            </a-space>
            <div @click="changeChartShowStatus" style="color:#878e9b;float:right">
              {{ chartShow ? "收起" : "展开"
              }}
              <a-icon :type="chartShow ? 'up' : 'down'" style="padding-left: 10px" />
            </div>
          </div>
          <div class="container-middle" v-show="chartShow">
            <a-spin :spinning="loadingChart">
              <a-row :gutter="16">
                <a-col
                  v-for="key in 4"
                  :xs="12"
                  :sm="12"
                  :md="6"
                  :xxl="6"
                  :key="key"
                  class="echart-graf"
                  :style="{height: (cardHeight - 100) +'px'}"
                  :id="`echart-${key}`"
                  ref="`echart-${key}`"
                ></a-col>
              </a-row>
            </a-spin>
          </div>
        </a-card>
        <a-card
          class="right-bottom-card"
          :style="{height:chartShow ? cardHeight +'px' : 1.62 *cardHeight +'px'}"
        >
          <div>
            <div class="table-operator">
              <a-button
                class="solar-eye-btn-primary"
                :disabled="loadingTable"
                :loading="loadingExport"
                @click.stop.prevent="clickExport()"
              >导出</a-button>
            </div>
            <a-table
              :data-source="tableData"
              :pagination="pagination"
              :scroll="{ x: 1500,y: chartShow ?0.45 * cardHeight +'px' :1.06*cardHeight +'px'}"
              @change="handleTableChange"
              size="small"
              :loading="loadingChart"
              ref="tableRate"
            >
              <a-table-column
                fixed="left"
                width="150px"
                key="deviceName"
                align="center"
                data-index="deviceName
                  "
              >
                <span slot="title" style="color: #1890ff">
                  <a-select style="width: 120px" v-model="deviceType" @change="changeDeviceType">
                    <a-select-option
                      v-for="(item,index) in deviceTypeOptions"
                      :key="index"
                      :value="item.deviceType"
                    >{{ item.deviceName }}</a-select-option>
                  </a-select>
                </span>
                <template slot-scope="text,record">{{record.deviceName}}</template>
              </a-table-column>

              <a-table-column
                fixed="left"
                width="150px"
                key="dispersionRate"
                data-index="dispersionRate"
                title="日均离散率(%)"
                align="center"
                :sorter="true"
              >
                <template slot-scope="text">{{ dispersionRateTransfer(text) || '--' }}</template>
              </a-table-column>
              <a-table-column
                fixed="left"
                width="150px"
                key="convertGeneratingCapacity"
                data-index="convertGeneratingCapacity"
                title="等效小时数(h)"
                align="center"
                :sorter="true"
              >
                <template slot-scope="text">{{ coverformdataThree(text) || '--' }}</template>
              </a-table-column>
              <a-table-column
                fixed="left"
                width="150px"
                key="averageInternalTemp"
                data-index="averageInternalTemp"
                title="日均机内温度(℃)"
                align="center"
                :sorter="true"
              >
                <template slot-scope="text">{{ coverformdataOne(text) || '--' }}</template>
              </a-table-column>
              <a-table-column
                fixed="left"
                width="150px"
                key="approximateOpenVoltage"
                data-index="approximateOpenVoltage"
                title="并网工作最大电压(V)"
                align="center"
                :sorter="true"
              >
                <template slot-scope="text">{{ coverformdataOne(text) || '--' }}</template>
              </a-table-column>
              <a-table-column
                fixed="left"
                width="150px"
                key="orderDifferenceScore"
                data-index="orderDifferenceScore"
                title="设备综合得分"
                align="center"
                :sorter="true"
              >
                <template slot-scope="text">{{ coverformdataOne(text) || '--' }}</template>
              </a-table-column>
              <a-table-column-group title="组串平均电流(A)">
                <a-table-column
                  v-for="cur in currentAvgChildren"
                  :key="cur.key"
                  :title="cur.title"
                  :width="cur.width"
                  :data-index="cur.dataIndex"
                  align="center"
                />
              </a-table-column-group>
            </a-table>
          </div>
        </a-card>
      </a-col>
    </a-row>
  </div>
</template>
<script>
import Funnel from '@/components/health/funnel';
import PowerStationWeather from '@/components/health/weather/PowerStationWeather.vue';
import moment from 'moment';
import { downloadFile } from '@/utils/util.js';
import echarts from '@/utils/enquireEchart';
import {
  getPowerStationType,
  getDispersionRateLevel,
  indexAnalysisHistory,
  downloadExcelDispersionRate
} from '@/api/health/healthapi.js';
import treeHeight from '@/mixins/health/leftTreeHeight';
import TypicalDate from '@/mixins/health/typicalDate';

let innerHeight = window.innerHeight - 60 - 40 - 24;
export default {
  components: {
    Funnel,
    PowerStationWeather
  },
  name: 'HistoryAnalysis',
  mixins: [treeHeight, TypicalDate],
  data () {
    return {
      title: '',
      deviceTypeOptions: [],
      filteredDeviceTypes: [],
      currentAvgChildren: [],
      currentKey: '',
      recordDate: '',
      psId: '',
      psName: '',
      rowCount1: 0,
      rowCount: 0,
      filterText: '',
      dataValue: [],
      tableColumnList: [],
      tableData: [],
      pagination: {
        current: 1,
        pageSize: 10,
        pageSizeOptions: ['10', '20', '30', '50'],
        defaultPageSize: 10,
        showSizeChanger: true,
        defaultCurrent: 1,
        showQuickJumper: true,
        showTotal: total => `共 ${total} 条`
      },
      isorter: {
        sortFiled: '',
        sortKind: ''
      },
      labelNames: [],
      accordion: true,
      data: [],
      pageList: {},
      loadingChart: true,
      loadingTable: true,
      loadingExport: false,
      rateLevel: [],
      flags: '0',
      chartShow: true,
      deviceType: undefined
    };
  },

  created () {
    this.flags = '0'; // 标识是否刷新图表，0---全部刷新，1---只刷新表格
    // 从电站首页跳转过来，获取电站id
    if (this.$route.query.id) {
      this.psId = this.$route.query.id;
      this.currentKey = this.psId;
    }
    this.recordDate = moment()
      .subtract(1, 'days')
      .format('YYYY/MM/DD');

    // this.recordDate = endTime;
    // this.getDispersionRateLevel();
  },
  computed: {
    cardHeight () {
      return innerHeight > 700 ? (innerHeight - 16) / 2 : (800 - 16) / 2;
    }
  },
  methods: {
    refreshData (obj) {
      this.psId = obj.psId;
      this.psName = obj.psName;
      this.title = obj.psName;
      this.flags = '0';
      this.pagination.current = 1;
      this.getData();
      this.getTopicalDay(this.psId);
      // this.getDispersionRateLevel();
    },
    changeChartShowStatus () {
      this.chartShow = !this.chartShow;
    },
    // 选择离散率等级查询表格数据
    getLevelData () {
      this.flags = '1';
      this.pagination.current = 1;
      this.getData();
    },
    // 获取离散率等级
    getDispersionRateLevel () {
      getDispersionRateLevel()
        .then(res => {
          if (res.result_code === '1') {
            this.rateLevel = res.result_data;
          }
        })
        .catch(function () {});
    },
    /**
     * @description   搜索框click事件，筛选电站
     */
    clickSearch () {
      this.dataValue = [];
      this.flags = '0';
      this.getData();
    },
    // 遍历数组
    mapArr (arr, isx) {
      let xData = [];
      let yData = [];
      arr &&
        arr.map(item => {
          xData.push(Object.keys(item)[0]);
          yData.push(Object.values(item)[0]);
        });
      return isx ? xData : yData;
    },
    // 获取数据
    getData (isSelectChange) {
      this.tableData = [];
      this.dataValue = [];
      this.currentAvgChildren = [];
      this.filteredDeviceTypes = [];
      this.loadingTable = true;
      let param = {
        psId: this.psId
      };
      // let paramObj = {
      //   curPage: this.pagination.current,
      //   size: this.pagination.pageSize,
      //   psId: this.psId,
      //   recordDate: moment(this.recordDate).format("YYYY/MM/DD"),
      //   deviceType:
      //     this.filteredDeviceTypes.length == 0 && this.filteredDeviceTypes[0],
      // };
      if (this.flags == '0') {
        this.loadingChart = true;
      }

      const beforePromise = () => {
        if (!isSelectChange) {
          this.deviceTypeOptions = [];
          return new Promise((resolve, reject) => {
            getPowerStationType(param)
              .then(res => {
                // 下拉项
                this.deviceTypeOptions = res.result_data.deviceGroup;
                for (let obj in this.deviceTypeOptions) {
                  if (res.result_data.deviceGroup[obj].isDefault === '1') {
                    // this.defaultDeviceName = res.result_data.deviceGroup[obj].deviceName
                    // this.defaultDeviceType = res.result_data.deviceGroup[obj].deviceType
                    this.deviceType =
                      res.result_data.deviceGroup[obj].deviceType;
                    console.log('this.defaultDeviceType=' + this.deviceType);
                  }
                }
                resolve && resolve();
              })
              .catch(function (err) {
                reject && reject(err);
                console.log(err);
              });
          });
        } else {
          return new Promise((resolve, reject) => {
            resolve();
          });
        }
      };

      let promise = beforePromise();
      promise.then(() => {
        let paramObj = {
          recordDate: moment(this.recordDate).format('YYYY/MM/DD'),
          curPage: this.pagination.current,
          size: this.pagination.pageSize,
          psId: this.psId,
          psKeys: this.psKeys,
          deviceType: this.deviceType
        };
        // TODO 接口需要改
        indexAnalysisHistory(Object.assign({}, paramObj, this.isorter))
          .then(res => {
            this.loadingChart = false;
            this.loadingTable = false;
            if (res.result_code === '1') {
              let data = res.result_data;
              if (this.flags == '0') {
                this.drawChart(data);
              }
              // 下拉项
              this.deviceTypeOptions = res.result_data.deviceGroup;
              let defaultDevice = res.result_data.deviceGroup.filter(
                itm => itm.isDefault == 1
              );
              if (defaultDevice && defaultDevice.length) {
                this.filteredDeviceTypes = [defaultDevice[0].deviceType];
              }
              res.result_data.pageBean.pageList.forEach(e => {
                e.currentAvgList.forEach(arr => {
                  if (arr.currentAvg) {
                    arr.currentAvg = Number(arr.currentAvg).toFixed(2);
                  } else {
                    arr.currentAvg = '--';
                  }
                });
              });
              let currentAvgList =
                (res.result_data.pageBean.pageList[0] &&
                  res.result_data.pageBean.pageList[0]['currentAvgList']) ||
                [];
              if (currentAvgList.length) {
                this.currentAvgChildren = currentAvgList.map((c, index) => ({
                  width: 70,
                  title: c.pointName,
                  key: index,
                  dataIndex: `currentAvgList[${index}].currentAvg`
                }));
              } else {
                this.currentAvgChildren = [];
              }
              const pager = { ...this.pagination };
              pager.total = res.result_data.pageBean.rowCount;
              pager.pageSize = res.result_data.pageBean.size;
              pager.current = res.result_data.pageBean.curPage;
              this.pagination = pager;
              this.tableData = res.result_data.pageBean.pageList;
            }
          })
          .catch(function (err) {
            console.log(err);
            this.loadingChart = false;
            this.loadingTable = false;
          });
      });
    },
    drawChart (dataList) {
      this.drawLine('日均离散率(%)', dataList.dispersionPicture, 1);
      this.drawLine('等效小时数(h)', dataList.convertPicture, 2);
      this.drawLine('日均机内温度(℃)', dataList.internalPicture, 3);
      this.drawLine('并网工作最大电压(V)', dataList.voltagePicture, 4);
    },
    handleTableChange (pagination, filters, sorter) {
      const pager = { ...this.pagination };
      if (Object.keys(sorter).length > 0) {
        this.isorter.sortFiled = this.filedToLowerCase(sorter.field);
        this.isorter.sortKind = sorter.order == 'ascend' ? 'asc' : 'desc';
      } else {
        this.isorter.sortFiled = '';
        this.isorter.sortKind = '';
      }
      pager.current = pagination.current;
      pager.pageSize = pagination.pageSize;
      this.pagination = pager;
      this.flags = '1'; // 只刷新表格
      this.getData();
    },
    filedToLowerCase (str) {
      var arr = str.split('');
      var new_arr = arr.map(item => {
        return item === item.toUpperCase() ? '_' + item.toLowerCase() : item;
      });
      return new_arr.join('');
    },
    coverformdataThree (text) {
      if (text == '0') {
        return Number(text).toFixed(3);
      }
      if (text == null || text == '') {
        return '';
      }
      return Number(text).toFixed(3);
    },
    coverformdataTwo (text) {
      if (text == '0') {
        return Number(text).toFixed(2);
      }
      if (text == null || text == '') {
        return '';
      }
      return Number(text).toFixed(2);
    },
    coverformdataOne (text) {
      if (text == '0') {
        return Number(text).toFixed(1);
      }
      if (text == null || text == '') {
        return '';
      }
      return Number(text).toFixed(1);
    },
    coverformdata (text) {
      if (text == null || text == '') {
        return '';
      }
      return text;
    },
    dispersionRateTransfer (text) {
      if (text == '0') {
        return Number(text).toFixed(2);
      }
      if (text == null || text == '') {
        return '';
      }
      return (Number(text) * 100).toFixed(2);
    },
    changeDeviceType () {
      this.pagination.current = 1;
      this.pagination.pageSize = 10;
      this.getData(true);
    },
    drawLine (xName, dataList, chartNo) {
      // 基于准备好的dom，初始化echarts实例
      let myChart = echarts.init(document.getElementById('echart-' + chartNo));
      var option = {
        tooltip: {
          trigger: 'axis',
          formatter: function (params) {
            let xName = `${params[0].axisId.split('0')[0]}`;
            let xValue = params[0].axisValue;
            let yValue = params[0].value;
            return `${params[0].marker}&nbsp;&nbsp;${xName}：${xValue}<br/>${params[1].marker}&nbsp;&nbsp;数量(台)：${yValue}`;
          }
        },
        toolbox: {
          // feature: {
          //   dataView: { show: true, readOnly: false },
          //   magicType: { show: true, type: ["line", "bar"] },
          //   restore: { show: true },
          //   saveAsImage: { show: true }
          // }
        },
        xAxis: {
          type: 'category',
          name: xName,
          nameLocation: 'center',
          data: this.mapArr(dataList, true),
          nameTextStyle: {
            verticalAlign: 'top',
            padding: [10, 0, 0, 0]
          }
        },
        yAxis: {
          type: 'value',
          name: '数量（台）'
        },
        dataZoom: [
          {
            type: 'inside'
          }
        ],
        series: [
          {
            data: this.mapArr(dataList, false),
            type: 'line',
            smooth: true,
            showBackground: false,
            lineStyle: {
              color: function (params) {
                let colorList = ['#0066ff', '#FFBB00', '#CC0000', '#008800'];
                return colorList[chartNo - 1];
              }
            },
            itemStyle: {
              normal: {
                color: function (params) {
                  let colorList = ['#0066ff', '#FFBB00', '#CC0000', '#008800'];
                  return colorList[chartNo - 1];
                }
              }
            }
          },
          {
            data: this.mapArr(dataList, false),
            type: 'bar',
            showBackground: false,
            itemStyle: {
              normal: {
                color: function (params) {
                  let colorList = ['#0066ff', '#FFBB00', '#CC0000', '#008800'];
                  return colorList[chartNo - 1];
                }
              }
            }
          }
        ]
      };
      myChart.setOption(option);
      window.addEventListener('resize', () => {
        myChart && myChart.resize();
      });
    },

    clickExport () {
      if (this.recordDate == null || this.recordDate == '') {
        this.$message.error('诊断时间不能为空');
        return;
      }
      if (this.tableData.length == 0) {
        this.$message.error('暂无数据，无法导出');
        return;
      }
      this.loadingExport = true;
      let paramsObj = {
        curPage: '',
        size: '',
        psId: this.psId,
        deviceType: this.filteredDeviceTypes[0],
        recordDate: moment(this.recordDate).format('YYYY/MM/DD')
      };
      // 请求导出接口
      downloadExcelDispersionRate(paramsObj)
        .then(res => {
          if (res.result_code == '1') {
            downloadFile(res.result_data);
          }
          this.loadingExport = false;
        })
        .catch(() => {
          this.loadingExport = false;
        });
    }
  }
};
</script>
<style lang="less" scoped>
.history-analysis {

  .right {
    background-color: inherit;
    overflow-y: auto;
    border: none;

    :deep(.right-info-row) {
      line-height: 30px;
      align-items: center;
    }

   :deep(.between) {
      display: flex;
      justify-content: space-between;
    }

    .right-top-card {
      margin-bottom: 10px;
      border-radius: 0 0 4px 4px;
    }
  }

  .container-middle {
    .echart-graf {
      // width: 50%;
      height: 33vh;
    }
    @media screen and(max-width: 1600px) {
      .echart-graf {
      // width: 50%;
      height: 30vh;
    }
    }
     @media screen and(max-width: 1360px) {
      .echart-graf {
      // width: 50%;
      height: 27vh;
    }
    }
  }
}

</style>
