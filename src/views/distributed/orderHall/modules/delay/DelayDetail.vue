<template>
  <div>
    <a-row :gutter="24">
      <a-col :span="24">
        <div class="order-dispose">
          <div class="title-box">
            <span class="before"></span>
            <span>延期信息</span>
          </div>
        </div>
      </a-col>
    </a-row>
    <vxe-table :data="extensionList" :height="200" ref="multipleTable" class='my-table'
               resizable show-overflow highlight-hover-row size="small" style='margin-left: 16px'>
      <vxe-table-column type="seq" width="80" title="序号"></vxe-table-column>
      <vxe-table-column show-overflow="title" v-for="item in columnList" :key="item.name" :field="item.name"
                        :title="item.comment" :min-width="item.width || 140">
        <template v-slot:default="{row}">
          <div v-if="item.name == 'flowUser' && row.flowUser" class="flex-start">
            <div :class="`table-statusCol color-table-${getColor(row.flowSts)}`"></div>
            <a @click="clickEvent(row)" class="blue">{{ row.flowUser }}</a>
          </div>
          <span v-else>{{ getLabel(row[item.name], null) }}</span>
        </template>
      </vxe-table-column>
      <template v-slot:empty>
        <span>查询无数据</span>
      </template>
    </vxe-table>
    <flow-chart-drawer v-if="showDiagram" ref="flowChartDrawer" :parentId="parentId"
          :processInstanceId="processInstanceId" :flowUser="flowUser" />
  </div>
</template>

<script>
import PropTypes from 'ant-design-vue/es/_util/vue-types';
// import { LeftMixin } from '@/mixins/LeftMixin';
export default {
  name: 'DelayDetail',
  // mixins: [LeftMixin],
  props: {
    extensionList: PropTypes.array,
    parentId: {
      type: String,
      required: true,
      default: ''
    }
  },
  data () {
    return {
      showDiagram: false,
      processInstanceId: undefined,
      flowUser: undefined,
      columnList: [
        { name: 'applicant', comment: '申请人员' },
        { name: 'flowStartTime', comment: '申请时间' },
        { name: 'originalPlannedTime', comment: '原计划完成时间' },
        { name: 'extensionPlanTime', comment: '延期后完成时间' },
        { name: 'durationTime', comment: '延期时长(h)' },
        { name: 'extensionReasonLabel', comment: '延期原因' },
        { name: 'description', comment: '具体延期说明' },
        { name: 'flowUser', comment: '当前节点' }
      ]
    };
  },
  methods: {
    clickEvent (row) {
      this.showDiagram = true;
      this.processInstanceId = row.procInstId;
      this.flowUser = row.flowUser;
      const _self = this;
      this.$nextTick(() => {
        _self.$refs.flowChartDrawer.openView();
      });
    },
    getColor (flowSts) {
      switch (flowSts) {
        case '1':
        case '4':
          return 'waitBegin';
        case '2':
          return 'processing';
        case '3':
          return 'finish';
      }
    }
  }
};
</script>
<style lang="less" scoped>
</style>
