<template>
  <a-row :gutter="24" class="search-content">
    <a-col class="m-b--12" :xxl="5" :xl="8" :md="12">
      <div class="search-item">
        <span class="search-label">电站名称</span>
        <ps-tree-select
          @change="psTreeChange"
          :isPsName="extraPsInfo.psName"
          v-model="extraPsInfo.psId"
          :isPsaDisable="false"
          :isQueryPs="1"
          ref="tree"
          style="width: 100%"
          psCateGory="'CentralizedPV','DistributedPV','Storage','CIPV'"
          :hasMaintenanceStatusParams="true"
        />
      </div>
    </a-col>
    <a-col class="m-b--12" :xxl="5" :xl="6" :md="12">
      <div class="search-item">
        <span class="search-label">发生时间</span>
        <a-range-picker v-model="searchTime" format="YYYY-MM-DD" @change="searchTimeChange" />
      </div>
    </a-col>
    <a-col class="m-b--12" :xxl="5" :xl="8" :md="12">
      <div class="search-item">
        <span class="search-label">诊断原因</span>
        <a-select
          v-model="alarmReason"
          @change="alarmReasonChange"
          placeholder="请选择"
          show-search
          :filter-option="$filterOption"
          style="width: 100%"
        >
          <a-select-option value="">全部</a-select-option>
          <a-select-option
            v-for="item in alarmReasonOptions"
            :value="item.alarmReasonName + '-di-' + item.alarmReason + '-di-' + item.alarmRemark"
            :key="item.alarmReasonName + '-di-' + item.alarmReason + '-di-' + item.alarmRemark"
          >
            {{ item.alarmReasonName }}
          </a-select-option>
        </a-select>
      </div>
    </a-col>
    <a-col class="m-b--12" :xxl="5" :xl="8" :md="12">
      <div class="search-item">
        <span class="search-label">处理状态</span>
        <a-select
          v-model="searchData.alarmStatus"
          @change="alarmStatusChange"
          style="width: 100%"
          showArrow
          placeholder="请选择"
        >
          <!--          <a-select-option value="">全部</a-select-option>-->
          <a-select-option value="01">待处理</a-select-option>
          <a-select-option value="03">已派发</a-select-option>
          <a-select-option value="04">已闭环</a-select-option>
        </a-select>
      </div>
    </a-col>
    <a-col class="m-b--12" :xxl="4" :xl="8" :md="12">
      <div class="search-item">
        <throttle-button label="查询" class="solar-eye-btn-primary" @click="doSearch" />
        <throttle-button label="重置" class="solar-eye-btn-primary-cancel" @click="doReset" />
      </div>
    </a-col>
  </a-row>
</template>
<script>
import { alarmRemarkAndReasonApi } from '@/api/health/AlarmEvents.js';

export default {
  props: {
    deviceType: {
      type: String,
      default: ''
    },
    type: {
      type: String,
      default: ''
    },
    psType: {
      type: String,
      default: '1'
    }
  },
  data() {
    return {
      allAlarmReasonOptions: [],
      alarmReasonOptions: [],
      alarmReason: '',
      searchTime: [],
      searchData: {
        startTime: null,
        endTime: null,
        alarmReason: '',
        alarmStatus: '01',
        depCodes: undefined,
        treeOrgId: undefined,
        treePsaId: undefined,
        treePsId: undefined
      },
      extraPsInfo: {
        psId: undefined,
        psName: undefined,
        depCodes: undefined,
        parentId: undefined,
        parentType: undefined,
        parentName: undefined,
        parentOrgCode: undefined,
        nodeType: undefined
      }
    };
  },
  methods: {
    // 发生时间选择框变化事件
    searchTimeChange(val) {
      this.searchData.startTime = val[0] ? val[0].format('YYYY-MM-DD') : null;
      this.searchData.endTime = val[1] ? val[1].format('YYYY-MM-DD') : '';
      this.$emit('searchParamsChange', this.searchData);
    },
    // 获取诊断原因下拉选项
    getAlarmReasionOptions(alarmReasonName) {
      let deviceType = this.deviceType.toString() || undefined;
      if (deviceType === '3') deviceType = '3,12';
      if (deviceType === '17') deviceType = '17,6';
      alarmRemarkAndReasonApi({ tabCode: this.type, deviceType: deviceType, psType: this.psType })
        .then((res) => {
          this.allAlarmReasonOptions = res || [];
          this.alarmReasonOptions = this.arrUnique(this.allAlarmReasonOptions, 'alarmReasonName');
          if (alarmReasonName) {
            let data = this.alarmReasonOptions.find((item) => item.alarmReasonName == alarmReasonName);
            this.alarmReason = data ? data.alarmReasonName + '-di-' + data.alarmReason + '-di-' + data.alarmRemark : '';
            this.alarmReasonChange(this.alarmReason);
            this.doSearch();
          }
        })
        .catch(() => {
          if (alarmReasonName) {
            this.$emit('cancelLoading');
          }
        });
    },
    // 诊断原因下拉框变化事件
    alarmReasonChange(value) {
      if (value) {
        let sameNameArr = this.allAlarmReasonOptions.filter((item) => {
          return item.alarmReasonName == value.split('-di-')[0];
        });
        this.searchData.alarmReason = Array.from(
          new Set(
            sameNameArr.map((item) => {
              return item.alarmReason;
            })
          )
        ).toString();
      } else {
        this.searchData.alarmReason = '';
      }
      let reasonName = value ? value.split('-di-')[0] : '';
      this.$emit('reasonNameChange', reasonName);
      this.$emit('searchParamsChange', this.searchData);
    },
    // 处理状态下拉框变化事件
    alarmStatusChange() {
      this.$emit('searchParamsChange', this.searchData);
    },
    // 查询
    doSearch() {
      this.$emit('doSearch', 1, true);
    },
    // 重置
    doReset() {
      this.alarmReason = '';
      this.searchTime = [];
      this.searchData = {
        startTime: null,
        endTime: null,
        alarmReasonString: '',
        alarmStatus: '01'
      };
      if (this.$refs.tree) {
        let node = this.$refs.tree.rootNode;
        this.setStationParams(node);
        this.$refs.tree.refresh = false;
        setTimeout(() => {
          this.$refs.tree.refresh = true;
        }, 300);
        this.doSearch();
      }
    },
    // 对象数组去重
    arrUnique(arr, key) {
      let returnArr = [];
      let obj = {};
      returnArr =
        arr &&
        arr.length > 0 &&
        arr.reduce((cur, next) => {
          // eslint-disable-next-line no-unused-expressions
          obj[next[key]] ? '' : (obj[next[key]] = true && cur.push(next));
          return cur;
        }, []);
      return returnArr;
    },
    psTreeChange(val, node) {
      if (window.comFromMonitor) {
        window.comFromMonitor = false;
        this.$refs.tree.psName = this.extraPsInfo.psName;
        // this.doSearch();
        return;
      }
      this.setStationParams(node);
      this.doSearch();
    },
    setStationParams(node) {
      Object.assign(this.searchData, {
        depCodes: undefined,
        treeOrgId: undefined,
        treePsaId: undefined,
        treePsId: undefined
      });
      this.extraPsInfo = this.$options.data().extraPsInfo;

      if (node.isPsa == '0' && !node.isPs) {
        Object.assign(this.extraPsInfo, { nodeType: 1 });
      } else if (node.isPsa == '1') {
        Object.assign(this.extraPsInfo, { nodeType: 2 });
      } else if (node.isPs == 1) {
        Object.assign(this.extraPsInfo, { nodeType: 3 });
      } else {
        Object.assign(this.extraPsInfo, { nodeType: undefined });
      }
      Object.assign(this.extraPsInfo, {
        psId: String(node.id),
        psName: node.name,
        depCodes: node.orgCode,
        parentId: node.parentId,
        parentType: node.parentType,
        parentName: node.parentName,
        parentOrgCode: node.parentOrgCode
      });
      Object.assign(this.searchData, { depCodes: node.orgCode });
      if (this.extraPsInfo.psId) {
        if (this.extraPsInfo.nodeType == '1') {
          this.searchData.treeOrgId = this.extraPsInfo.psId;
        } else if (this.extraPsInfo.nodeType == '2') {
          this.searchData.treePsaId = this.extraPsInfo.psId;
        } else {
          this.searchData.treePsId = this.extraPsInfo.psId;
          if (this.extraPsInfo.parentId == '1') {
            this.searchData.treeOrgId = this.extraPsInfo.parentId;
          } else {
            this.searchData.treePsaId = this.extraPsInfo.parentId;
          }
        }
      }
      this.$emit('searchParamsChange', this.searchData);
    }
  }
};
</script>
<style lang="less" scoped>
.ant-btn + .ant-btn {
  margin-left: 10px;
}

.search-content {
  .search-item {
    display: inline-flex;
    width: 100%;
    align-items: center;

    & > .search-label {
      width: auto;
      text-align: right;
      align-items: center;
      white-space: nowrap;
      color: var(--zw-text-1-color--default);
      font-size: 14px;

      &:after {
        content: ':';
        padding-right: 8px;
      }

      & + div {
        width: calc(100% - 4rem);
      }
    }
  }
}
</style>
