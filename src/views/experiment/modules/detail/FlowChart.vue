<template>
<div>
  <div @click="openFlowChart" class="flow-chart-btn">
    <svg-icon iconClass="flow"></svg-icon>
    流程图
  </div>
  <flow-chart-drawer
    v-if="showDiagram"
    ref="flowChartDrawer"
    parentId="taskHall"
    :processInstanceId="formData.processInstanceId"
    :flowUser="formData.flowUser"
    task-no=""
  />
</div>
</template>

<script>
export default {
  name: 'Flow<PERSON><PERSON>',
  props: {
    formData: {
      type: Object
    }
  },
  data () {
    return {
      showDiagram: false
    };
  },
  methods: {
    openFlowChart () {
      this.showDiagram = true;
      const _self = this;
      this.$nextTick(() => {
        _self.$refs.flowChartDrawer.openView();
      });
    }
  }
};
</script>

<style scoped>

</style>
