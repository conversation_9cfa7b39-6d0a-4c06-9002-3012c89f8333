import { axios } from '@/utils/erp-request';

// 获取公告列表
export function getAnnounceInfoList (data) {
  return axios({
    url: '/system/v1/announceManager/getAnnounceInfoList',
    method: 'post',
    data
  });
}

// 新增公告
export function insertAnnounceInfo (data) {
  return axios({
    url: '/system/v1/announceManager/insertAnnounceInfo',
    method: 'post',
    data
  });
}

// 根据id查询公告详情
export function getAnnounceInfo (data) {
  return axios({
    url: '/system/v1/announceManager/getAnnounce',
    method: 'post',
    data
  });
}

// 修改公告
export function updateAnnounceInfo (data) {
  return axios({
    url: '/system/v1/announceManager/updateAnnounceInfo',
    method: 'post',
    data
  });
}

// 删除公告
export function deleteAnnounceInfo (data) {
  return axios({
    url: '/system/v1/announceManager/deleteAnnounceInfo',
    method: 'post',
    data
  });
}

// 下载公告附件
export function download (data) {
  return axios({
    url: 'system/v1/annex/downloadBizAnnex',
    method: 'post',
    data
  });
}
