<!-- 数据角色数-部门、电站拆分 -->
<template>
  <a-row :gutter="24">
    <a-col :span="(isOnlyDep || isColumn) ? 24 : 12">
      <div class="search-item">
        <span class="search-label">{{ depLabel }}</span>
        <a-tree-select v-model="depValue" :tree-data="treeData" show-search :replace-fields="{title: 'name', key:'code', value: 'code'}"
         :dropdown-style="{ maxHeight: '50vh', overflow: 'auto' }" placeholder="请选择部门" treeNodeFilterProp="title" :allow-clear='depTreeAllowClear'
         :treeExpandedKeys.sync="expandedKeys" class="role-tree-select" dropdownClassName="role-tree-select-dropdown" @search="depSearch" @change="depChange" @click="handleVisible($event)">
        </a-tree-select>
      </div>
    </a-col>
    <a-col :span="isColumn? 24 : 12" v-if="!isOnlyDep">
      <div class="search-item">
        <span class="search-label">{{ psaLabel }}</span>
        <a-select v-model="psaValue" @change="psaChange" @dropdownVisibleChange="psaSearch(null)" :allow-clear="allowClear" :mode="mode" :maxTagCount="1" :maxTagTextLength="32" show-search
         placeholder="输入关键字搜索" :filter-option="false" @search="psaSearch" class="psa-select-multiple" dropdownClassName="psa-dropdown">
          <template slot="dropdownRender" slot-scope="menu">
            <v-nodes :vnodes="menu" />
            <template v-if="showMore">
              <a-divider style="margin: 4px 0;" />
              <div style="padding: 4px 8px;text-align:right;" @mousedown="e => e.preventDefault()">
                <a-button type="link" :loading="moreLoading" :disabled="moreLoading" @click="loadMore()">更多</a-button>
              </div>
            </template>
          </template>
          <a-select-option v-for="item in psaOptions" :key="item.id" :value="item.id" :title="item.name">{{ item.name }}</a-select-option>
        </a-select>
      </div>
    </a-col>
  </a-row>
</template>

<script>
import { myDeptTree, relPsas } from '@/api/api';
import { USER_INFO, USER_DEPT_LCA } from '@/store/mutation-types';
export default {
  components: {
    VNodes: {
      functional: true,
      render: (h, ctx) => ctx.props.vnodes
    }
  },
  props: {
    // 部门label
    depLabel: {
      type: String,
      default: '部门'
    },
    // 电站label
    psaLabel: {
      type: String,
      default: '电站'
    },
    // 多角色时，是否默认账号所属部门
    defaultUserDep: {
      type: Boolean,
      default: true
    },
    // 是否只显示部门，不显示电站
    isOnlyDep: {
      type: Boolean,
      default: false
    },
    excludeHy: { // 是否排除户用
      type: Boolean,
      default: false
    },
    excludeClosed: { // 排除已关闭电站：
      type: Boolean,
      default: false
    },
    mode: {
      type: String,
      default: 'multiple'
    },
    // 是否清空按钮
    allowClear: {
      type: Boolean,
      default: true
    },
    // 是否有默认项  顶级节点
    defaultTopValue: {
      type: Boolean,
      default: false
    },
    // 部门是否需要默认值
    hasDepDefaultValue: {
      type: Boolean,
      default: true
    },
    // 部门是否可以清除
    depTreeAllowClear: {
      type: Boolean,
      default: false
    },
    // 是否是纵向布局
    isColumn: {
      type: Boolean,
      default: false
    }
  },
  data () {
    return {
      nodes: [],
      treeData: [],
      expandedKeys: [],
      depValue: undefined,
      psaValue: [],
      psaOptions: [],
      showMore: false,
      params: {
        userId: undefined,
        deptCode: undefined,
        pageNo: 1,
        pageSize: 100,
        fuzz: '',
        excludeHy: undefined,
        excludeClosed: undefined
      },
      moreLoading: false,
      times: null
    };
  },
  created () {
    this.params.excludeHy = this.excludeHy;
    this.params.excludeClosed = this.excludeClosed;
    this.getMyDeptTree();
    let deptLca = Vue.ls.get(USER_DEPT_LCA) || {};
    if (this.isOnlyDep && this.hasDepDefaultValue) {
      this.$emit('change', deptLca.code, []);
    } else if (this.isOnlyDep && !this.hasDepDefaultValue) {
      this.$emit('change', null, []);
    } else {
      let user = Vue.ls.get(USER_INFO) || {};
      this.params.userId = user.id;
      this.depChange(deptLca.code);
    }
  },
  methods: {
    /*
        部门树change事件
      */
    depChange (value) {
      this.$emit('change', value, []);
      if (this.isOnlyDep || !value || this.params.deptCode == value) {
        return;
      }
      this.psaValue = [];
      Object.assign(this.params, { 'pageNo': 1, 'deptCode': value, 'fuzz': '' });
      this.geTrelPsas().then(res => {
        this.psaOptions = Object.freeze(res);
        if (this.defaultTopValue) {
          this.psaValue = [this.psaOptions[0].id];
          this.psaChange(this.psaOptions[0].id);
        }
      }).catch(() => {
        this.psaOptions = [];
      });
    },
    /*
        设置展开节点
      */
    setExpandedKeys () {
      let expandedKeys = [];
      let _this = this;
      let { depValue, nodes } = this;
      let find = (value, isParentId = true) => {
        let node = isParentId ? nodes.find(o => o.id == value) : nodes.find(o => o.code == value);
        if (node) {
          expandedKeys.push(node.code);
          node.parentId && find(node.parentId);
        }
      };
      find(depValue, false);
      _this.expandedKeys = expandedKeys;
      _this.$forceUpdate();
    },
    /*
        部门筛选事件
      */
    depSearch () {
      let _this = this;
      let find = () => {
        _this.expandedKeys = _this.nodes.map(node => {
          return node.code;
        });
        _this.$forceUpdate();
      };
      this.debounce(find);
    },
    /*
        节点点击事件
      */
    handleVisible () {
      this.setExpandedKeys();
      this.$nextTick(() => {
        let box = document.querySelector('.ant-select-tree li .ant-select-tree-node-content-wrapper.ant-select-tree-node-selected');
        let dropdown = document.querySelector('.role-tree-select-dropdown');
        if (box && !dropdown.display) {
          box.scrollIntoView(false);
        }
      });
    },
    /*
        电站选择
      */
    psaChange (value) {
      let _this = this;
      let fn = () => {
        let depValue = _this.depValue;
        _this.$emit('change', depValue, value);
        if (!value.length) {
          _this.psaSearch(null);
        }
      };
      this.debounce(fn);
    },
    /*
        电站筛选
      */
    psaSearch (input) {
      let { psaValue, psaOptions } = this;
      let _this = this;
      let filterPsa = () => {
        let selected = [];
        // 找出已选项
        if (Array.isArray(psaValue) && psaValue.length) {
          selected = psaOptions.filter(item => psaValue.includes(item.id));
        }
        Object.assign(_this.params, { 'pageNo': 1, 'fuzz': input });
        _this.geTrelPsas().then(res => {
          _this.psaOptions = Object.freeze(selected.concat(res.filter(item => !psaValue.includes(item.id))));
          if (_this.defaultTopValue) {
            _this.psaValue = [_this.psaOptions[0].id];
            _this.psaChange(_this.psaOptions[0].id);
          }
        }).catch(() => {
          _this.$forceUpdate();
        });
      };
      this.debounce(filterPsa, 800);
    },
    /*
        防抖
      */
    debounce (fn, wait = 500) {
      if (this.times) {
        clearTimeout(this.times);
        this.times = null;
      }
      this.times = setTimeout(fn, wait);
    },
    /* 获取树 */
    getMyDeptTree () {
      let user = Vue.ls.get(USER_INFO) || {};
      let map = {
        'userId': user.id,
        excludeHy: this.excludeHy
      };
      let _this = this;
      myDeptTree(map).then(res => {
        let deptLca = Vue.ls.get(USER_DEPT_LCA) || {};
        let treeData = Array.isArray(res.result) ? res.result : (res.result ? [res.result] : []);
        _this.getNodes(treeData);
        _this.treeData = Object.freeze(treeData);
        if (this.hasDepDefaultValue) _this.depValue = deptLca.code;
        _this.$forceUpdate();
      }).catch(() => {
        _this.treeData = [];
      });
    },
    /*
        节点平铺
      */
    getNodes (treeData) {
      let nodes = [];
      let fn = (items) => {
        items.forEach(item => {
          Object.assign(item, { order: undefined, type: undefined });
          let o = JSON.parse(JSON.stringify(item));
          o.children = undefined;
          nodes.push(o);
          if (Array.isArray(item.children)) {
            fn(item.children);
          }
        });
      };
      fn(treeData);
      this.nodes = Object.freeze(nodes);
    },
    /* 根据选中部门查电站 */
    geTrelPsas () {
      let _this = this;
      _this.moreLoading = true;
      return new Promise((resolve, reject) => {
        relPsas(_this.params).then(res => {
          let data = res.result || {};
          _this.showMore = data.t1;
          let result = Array.isArray(data.t2) ? data.t2 : [];
          resolve(result);
          _this.moreLoading = false;
        }).catch(err => {
          reject(err);
          _this.showMore = false;
          _this.moreLoading = false;
        });
      });
    },
    /*
        电站加载更多
      */
    loadMore () {
      let { psaValue, psaOptions } = this;
      if (this.params.pageNo >= 5) {
        this.$notification.info({ message: '提示', description: '试试搜索', duration: 3 });
        return;
      }
      this.params.pageNo += 1;
      let selected = [];
      // 找出已选项
      if (Array.isArray(psaValue) && psaValue.length) {
        selected = psaOptions.filter(item => psaValue.includes(item.id));
      }
      this.geTrelPsas().then(res => {
        let all = res.concat(psaOptions);
        this.psaOptions = Object.freeze(selected.concat(all.filter(item => !psaValue.includes(item.id))));
        if (this.defaultTopValue) {
          this.psaValue = [this.psaOptions[0].id];
          this.psaChange(this.psaOptions[0].id);
        }
        this.$forceUpdate();
        document.querySelector('.psa-dropdown .ant-select-dropdown-menu').scrollTo(0, 0);
      }).catch(() => {
        this.$forceUpdate();
      });
    },
    /* 默认选中重置 */
    reset () {
      let deptLca = Vue.ls.get(USER_DEPT_LCA) || {};
      if (this.hasDepDefaultValue) {
        this.depValue = deptLca.code;
      } else {
        this.depValue = null;
      }
      this.psaValue = [];
      this.depChange(this.depValue);
    }
  },
  beforeDestroy () {
    this.psaOptions = this.treeData = this.nodes = this.expandedKeys = [];
    this.times && clearTimeout(this.times);
    this.times = null;
    this.data = null;
  }
};
</script>

<style lang="less" scoped>
  .search-item{
    width: 100%;
  }
  :deep(.ant-btn-link){
    border: 0;
    color: #FF8F33;
  }
  .role-tree-select, .psa-select-multiple{
    width: 100%;
    height:32px;
    overflow: hidden;
  }
  :deep(.ant-select-selection--multiple){
    height: 32px;
    overflow: hidden;
  }
</style>
