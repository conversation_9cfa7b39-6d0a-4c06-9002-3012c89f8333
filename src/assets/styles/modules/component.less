// 公共组件样式
@import "./config.less";

// 1级字体颜色
.block-name,
.search-item,
.title-label label,
.solar-eye-btn-grey,
.solar-eye-btn-primary[disabled],
.search-item-label,
.search-box .text,
.ztree li a,
.setting-drawer-index-title {
  color: var(--zw-text-1-color--default);
}

// 2级字体颜色
.ant-radio-button-wrapper,
.ant-tabs-tab,
.anticon-fullscreen,
.anticon-fullscreen-exit,
.vxe-table--empty-content,
.ant-pagination-total-text,
.ant-select-dropdown.ant-select-dropdown--multiple .ant-select-dropdown-menu-item:hover .ant-select-selected-icon {
  color: var(--zw-text-2-color--default);
}

.ant-select-dropdown-menu-item-selected {
  color: var(--zw-primary-color--default) !important;
}

// 抽屉
.drawer-form-foot {
  background: var(--zw-card-bg-color--default);
  border-top: 1px solid var(--zw-divider-color--default);
}

.ant-drawer-header {
  border-bottom: 1px solid var(--zw-divider-color--default);
}

.solar-eye-flow-bg {
  background: var(--zw-input-bg-color--disable) !important;
}

// 滚动条
::-webkit-scrollbar {
  /*滚动条整体样式*/
  width: 8px;
  /*高宽分别对应横竖滚动条的尺寸*/
  height: 8px;
}

::-webkit-scrollbar-thumb {
  /*滚动条里面小方块*/
  background: var(--zw-border-color--default);
  border-radius: 4px;

  &:hover {
    background-color: var(--zw-text-color--default);
  }

  &:active {
    background-color: var(--zw-text-color--default);
  }
}

::-webkit-scrollbar-track {
  /*滚动条里面轨道*/
  background: var(--zw-small-area-bg-color--default);
}

// ant-table
.ant-table-small {
  border: none;

  .ant-table-content .ant-table-header {
    //background: var(--zw-table-header-bg-color--default);
  }

  .ant-table-thead {
    background: var(--zw-table-header-bg-color--default);;
  }
}

.ant-table-header {
  background: var(--zw-table-header-bg-color--default) !important;
}
.ant-table-thead > tr > th {
  background: var(--zw-table-header-bg-color--default) !important;
}

.ant-table-bordered .ant-table-thead > tr > th, .ant-table-bordered .ant-table-tbody > tr > td {
  border-right: transparent;
}

.ant-table-tbody > tr.ant-table-row-selected td, .ant-table-tbody > tr.ant-table-row-selected:hover td {
  background-color: var(--zw-border-color--default);
}

// 分页居中
.ant-table-pagination.ant-pagination {
  float: none;
  margin: 16px 0;
  text-align: center;
}

.ant-table-thead > tr > th.ant-table-column-sort,
.ant-table-tbody > tr > td.ant-table-column-sort {
  background: transparent;
}

/*列表td的padding设置 可以控制列表大小*/
.ant-table-tbody .ant-table-row td {
  padding-top: 15px;
  padding-bottom: 15px;
}

/* 滚动条优化 end */
.ant-table-fixed-header .ant-table-body-inner {
  overflow: hidden;
}

.ant-alert-info.ant-alert-info {
  background-color: var(--zw-card-bg-color--default);
  border-color: var(--zw-border-color--default);
}

.ant-tabs,
.ant-select-dropdown .ant-select-dropdown-search .ant-select-search__field {
  background-color: var(--zw-card-bg-color--default);
}

.ant-select-focused .ant-select-selection, .ant-select-selection:focus, .ant-select-selection:active,
.ant-input:focus {
  box-shadow: none !important;
}

.ant-input[disabled] {
  color: var(--zw-text-3-color--default) !important;
  background-color: var(--zw-input-bg-color--disable) !important;
}

.ant-layout {
  background: #f0f0f0;
}

form .ant-cascader-picker, form .ant-select {
  width: 100%;
}

// 圆形进度条底色
.ant-progress .ant-progress-circle-trail {
  stroke: var(--zw-input-bg-color--disable) !important;
}

/*列表页面弹出modal*/
.ant-modal-cust-warp {
  height: 100%
}

/*弹出modal Y轴滚动条*/
.ant-modal-cust-warp .ant-modal-body {
  height: calc(100% - 110px) !important;
  overflow-y: auto
}

/*弹出modal 先有content后有body 故滚动条控制在body上*/
.ant-modal-cust-warp .ant-modal-content {
  height: 90% !important;
  overflow-y: hidden
}

/*列表中有图片的加这个样式 参考用户管理*/
.anty-img-wrap {
  height: 25px;
  position: relative;
}

.anty-img-wrap > img {
  max-height: 100%;
}

/* 内嵌子表背景颜色 */
.j-inner-table-wrapper :deep(.ant-table-expanded-row .ant-table-wrapper .ant-table-tbody .ant-table-row) {
  background-color: #FFFFFF;
}

/**隐藏样式-modal确定按钮 */
.solareye-hidden {
  display: none
}

.user-layout-login {
  .ant-tabs-tab {
    opacity: 0.43;
    font-family: PingFangSC-Semibold;
    font-size: 34px;
    color: #333333;

    &.ant-tabs-tab-active {
      font-family: PingFangSC-Semibold;
      font-size: 36px;
      color: #333333;
    }
  }
}

.ant-modal {
  top: 0 !important
}

.ant-card {
  background: var(--zw-card-bg-color--default) !important;
  border-color: transparent !important;
  border-radius: 4px !important;
  box-shadow: 2px 4px 10px rgba(51, 51, 51, 0.1);

  &:hover {
    box-shadow: 4px 4px 10px rgba(51, 51, 51, 0.2)
  }

  .ant-card-extra {
    padding: 0;
  }

  .ant-card-head-title {
    padding: 13px 0;
    font-weight: bolder;
    color: var(--zw-text-1-color--default);
    font-family: PingFangSC-Medium, PingFang SC;
  }
}

.ant-card + .ant-card {
  margin-top: 16px;
}

.ant-card.solareye-card {
  .ant-card-body {
    padding-top: 4px;
  }

  .ant-card-head {
    border-radius: 4px 4px 0 0;
    padding: 0px 24px;
    box-shadow: 0px 1px 4px 0px rgba(0, 0, 0, 0.07);

    .ant-card-extra {
      .detail {
        color: #333;
        padding-right: 16px;
        vertical-align: -webkit-baseline-middle;
      }
    }

    .ant-card-extra,
    .ant-card-head-title {
      padding: 10px 0;
    }
  }
}

// 列表上方的操作按钮
.table-operator {
  display: flex;
  justify-content: flex-end;
  margin-bottom: 16px;

  .ant-btn {
    margin: 0 16px 0 0;
    height: 32px;
  }
}

.table-operator .ant-btn-group .ant-btn {
  margin: 0;
}

.table-operator .ant-btn-group .ant-btn:last-child {
  margin: 0 8px 8px 0;
}

.solar-eye-btn-primary-line {
  color: var(--zw-primary-color--default);
  border-color: var(--zw-primary-color--default);
}

.solar-eye-btn-grey {
  border-radius: 4px;
  margin-left: 16px;

  &:hover {
    color: var(--zw-primary-color--default);
    border-color: var(--zw-primary-color--default);
  }
}

.a-row-span {
  border: 1px solid var(--zw-primary-color--default);
  background-color: var(--zw-primary-color--default);
}

.flex-action-button {
  a {
    color: var(--zw-conduct-color--normal);

    &:hover {
      color: var(--zw-conduct-color--normal);
    }
  }

  a + a {
    margin-left: 10px;
  }
}

.bottom-auto-height {
  display: flex;
  flex-direction: column;
  height: calc(100vh - 124px);
  .top {
    flex-shrink: 0;
  }
  .bottom {
    flex: 1;
    overflow: hidden;
  }
}

.run-monitor {
  .main-content-chart .chart-card {
    .chart-card-header-title-read {
      color: var(--zw-text-1-color--default) !important;
    }
  }

  .search-label,
  .rang-input-label {
    color: var(--zw-text-1-color--default);
  }

  .chart-data-text {
    color: var(--zw-text-1-color--default);

    .left {
      background: url('../../../assets/images/monitor/indicators_bg.png');
    }
  }

  .warning-info-item {
    background: linear-gradient(270deg, var(--zw-card-bg-color--default) 0%, var(--zw-card-light-bg-color--default) 100%);
    color: var(--zw-text-1-color--default);

    span {
      color: var(--zw-text-3-color--default);
    }
  }
}

.solar-eye-seal {
  color: var(--zw-text-3-color--default);

  &:hover {
    color: var(--zw-primary-color--default);
  }
}

.run-monitor .main-content-chart .chart-card,
.power-station-card {
  background: var(--zw-card-bg-color--default);
  border: 1px solid var(--zw-border-color--default);

  &:hover {
    border: 1px solid var(--zw-primary-color--default);
    box-shadow: 0px 2px 4px 0px rgba(0, 0, 0, 0.5), 0px 0px 17px 6px rgba(13, 137, 255, 0.06) inset;
  }

  .chart-card-header-title-read {
    color: var(--zw-text-3-color--default) !important;
  }
}
// 监控中心和洞察工具共用的排序
.sort-header-item {
  border: 1px solid var(--zw-border-color--default);

  .sort-header-item-text,
  .sort-header-item-icon {
    color: var(--zw-text-2-color--default);
  }
}

.sort-header-item-active,
.sort-header-item:hover {
  border: 1px solid var(--zw-primary-color--default) !important;
  background: var(--zw-primary-partial-areas-color--hover) !important;
}

.sort-header-item-active,
.sort-header-item:hover {
  .sort-header-item-text,
  .sort-header-item-icon {
    color: var(--zw-primary-color--default);
  }
}

// 多选框最小宽度
.multiple_select_com {
  min-width: 140px;
}

// 字体/图标颜色
.solareye-color-off-line {
  color: #9D9997 !important;
}

.solareye-color-link {
  color: #64B1FF !important;
}

.main-parent .main {
  width: 100%;
  height: 100%;
  overflow: hidden;
}

.full-modal .ant-modal {
  height: calc(100vh - 88px);
  top: 111px !important;
  position: absolute;
}

.full-modal .ant-tabs-bar {
  position: fixed;
  width: 100%;
  background: var(--zw-text-1-color--default);
  z-index: 9;
  margin: 0;
}

.full-modal .ant-modal .ant-tabs-content {
  position: relative;
  margin-top: 60px;
}

.full-modal .ant-modal .ant-modal-content {
  height: 100%;
}

.full-modal .ant-modal .ant-modal-body {
  padding: 0px 24px 24px 24px;
}

.full-modal .ant-modal .ant-modal-footer {
  position: absolute;
  bottom: 0;
  width: 100%;
}

@media screen and (max-width: 1680px) {
  .page-header-index-wide, .app-container {
    height: 100%;
    overflow: hidden auto;
  }
}

video::-webkit-media-controls {
  display: none !important;
}

.solar-eye-hover-primary:hover {
  color: var(--zw-primary-color--default) !important;
  transition: .3s;
}

.ant-menu-inline .ant-menu-item {
  width: calc(100%);

  &:after {
    right: 3px !important;
  }
}

.ant-btn-icon-only + .ant-btn-icon-only {
  margin-left: 8px;
}

.ztree {
  .button.chk + [target=_blank] {
    cursor: default;
    pointer-events: none;
  }
}

.tab-content {
  .ant-tabs-bar {
    margin: 0 !important;
  }
}

.search-item {
  .ps-tree-input {
    margin-bottom: 0 !important;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
    display: inline-flex;
    vertical-align: middle;

    &.ant-input-disabled {
      pointer-events: none;
      cursor: not-allowed;
    }

    .ellipsis {
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
  }
}

.drawer-position {
  position: relative;
}

// 虚拟树需要固定高度和宽度
.ps-tree-select {
  min-width: 500px !important;
}

.ant-select-selection--multiple :not(.ant-select-selection__choice__disabled) {
  .ant-select-selection__choice:first-child {
    max-width: 70%;
  }
}

.ant-select-selection--multiple li:first-child:nth-last-child(2) {
  max-width: 90% !important;
}

.title-fontSize {
  font-size: 18px;
}

.title-fontSize-small {
  font-size: 16px;
}

.ant-dropdown-trigger.ant-dropdown-open > .anticon.anticon-down, .ant-dropdown-link > .anticon.anticon-down {
  transform: rotate(180deg);
}

.editor-detail {
  table {
    border-collapse: collapse;
    width: 100%
  }

  table td {
    border: 1px solid #ccc;
    margin: 0;
    padding: 4px 12px;
  }
}

.header-line {
  display: flex;
  flex-direction: row;
  align-items: center;
  margin-bottom: 8px;
  justify-content: space-between;

  [class*=left], [class*=right] {
    cursor: pointer;
  }

  [class*=left] {
    padding-right: 8px;
  }

  [class*=right] {
    padding-left: 8px;
  }
}

.solaryeye-menu {
  .menu-title {
    .solar-eye-arrow-up {
      color: var(--zw-primary-color--default);
    }
  }

  .menu-title-before {
    border-left: 4px solid var(--zw-primary-color--default);
  }
}

// 公用样式
.ant-menu.ant-menu-root .isParent.ant-menu-item-selected {
  background-size: 209px 73px !important;
  border-bottom: transparent !important;
  background-position: center !important;
}

.solar-eye-seal-name {
  color: var(--zw-warning-color--normal);
  position: absolute;
  left: 28px;
  top: 33px;
  transform: rotate(-45deg);
  font-size: 18px;
}

.nav-setting-icon {
  &::before,
  &::after {
    left: 40% !important;
  }
}

.nav-mail-icon {
  &::before,
  &::after {
    left: 45% !important;
  }
}

.nav-screen-icon {
  &::before,
  &::after {
    left: 45% !important;
  }
}

.search-menu {
  top: 42px !important;
}

.ant-dropdown .ant-dropdown-menu,
.ant-select-dropdown-placement-bottomLeft {
  margin-top: 16px;

  &::before {
    content: '';
    display: block;
    position: absolute;
    top: -10px;
    left: 30%;
    border-left: 10px solid transparent;
    border-right: 10px solid transparent;
  }

  &::after {
    content: '';
    display: block;
    position: absolute;
    top: -7.6px;
    left: 30%;
    border-left: 10px solid transparent;
    border-right: 10px solid transparent;
  }
}

.ant-dropdown.ant-dropdown-placement-bottomRight .ant-dropdown-menu {
  &::before {
    left: 70%;
  }

  &::after {
    left: 70%;
  }
}

.ant-tabs-card-bar {
  .ant-tabs-nav-container,
  .ant-tabs-tab {
    height: 40px !important;
    line-height: 40px !important;

    span {
      display: inline-block;
      height: 40px !important;
      line-height: 40px !important;
    }
  }

  .ant-tabs-ink-bar {
    visibility: visible !important;
    width: 30px !important;
    background-size: contain;
    height: 3px;
    left: 38px;
  }
}

.right-icon-border,
.monitor-footer-icon-border {
  background: var(--zw-primary-color--hover) !important;
  border: 1px solid var(--zw-primary-color--default);
}

.screen-action {
  width: 32px;
  margin-right: 16px;
  @media screen and(max-width: 1366px) {
    width: 42px;
  }
  height: 32px !important;
  border-radius: 4px;
  line-height: 36px !important;
  font-size: 16px;
  padding: 0 !important;
  color: #ACBAC5;
  border-left: 1px solid rgba(255, 255, 255, 0.23);
  border-radius: 0;
  text-align: center;
}

.ant-menu.ant-menu-root .isParent.ant-menu-item-selected,
.ant-menu.ant-menu-root .ant-menu-submenu-horizontal.ant-menu-submenu-selected {
  border-bottom: transparent !important;
  animation: none;
  transition: none;
}

.top-nav-header-index .header-index-wide .ant-menu.ant-menu-horizontal {
  line-height: 56px !important;
  background: var(--blue);

  .ant-menu-submenu:hover,
  .isParent:hover,
  .ant-menu-submenu-active {
    background: var(--zw-other-top-bg-color--default);
    border-radius: 3px;
  }

  .ant-menu-item > a, .ant-menu-submenu-title {
    color: #fff !important;
    opacity: 0.9 !important;
  }

  .ant-menu-submenu-selected > a, .ant-menu-submenu-selected > .ant-menu-submenu-title,
  .ant-menu-item-selected > a, .ant-menu-item-selected > .ant-menu-submenu-title {
    color: #fff !important;
    opacity: 1 !important;
  }

  .submenu-title-wrapper:hover {
    background: none !important;
  }

  .submenu-title-wrapper {
    span:hover {
      background: none !important;
    }
  }
}

.ant-radio-button-wrapper-disabled.ant-radio-button-wrapper-checked {
  color: var(--zw-common-white-color--default) !important;
  background-color: var(--zw-primary-color--disable) !important;
  border-color: var(--zw-primary-color--disable) !important;
  box-shadow: none;
}

.chart-eg-item {
  border: 1px solid var(--zw-border-color--default);
}

.card-area.ant-card-bordered {
  border: none !important;
  box-shadow: none !important;

  .ant-card-body {
    padding: 0 !important;
  }
}

.sort-header {
  display: flex;
  justify-content: flex-end;
  flex: 1;
  align-items: center;

  .desc {
    flex: 1;
    font-family: PingFangSC-Regular, PingFang SC;
  }

  .sort-header-item {
    height: 28px;
    padding: 0 16px;
    text-align: center;
    border-radius: 14px;
    box-sizing: border-box;
    font-size: 16px;
    margin-left: 10px;
  }

  .sort-header-item-text,
  .sort-header-item-icon {
    font-size: 14px;
  }

  .sort-header-item-active,
  .sort-header-item:hover {
    cursor: pointer;
  }
}

.anticon-fullscreen,
.anticon-fullscreen-exit,
.anticon-vertical-align-bottom {
  width: 36px;
  height: 36px;
  border-radius: 36px;
  line-height: 36px;
  text-align: center;
}

.solaryeye-menu {
  .menu-title {
    .solar-eye-arrow-up {
      color: var(--zw-primary-color--default);
    }
  }

  .menu-title-before {
    border-left: 4px solid var(--zw-primary-color--default);
  }
}

.solar-eye-tooptip {
  max-width: 455px !important;
  max-height: 212px !important;
  overflow: auto;
}

.deep-anly-tooptip {
  max-width: 1000px !important;
  max-height: 500px !important;
  overflow: auto;
}

.solar-eye-tooptip {
  background-color: var(--zw-card-bg-color--default) !important;
  border-color: var(--zw-text-reset-grey-color--default) !important;
  color: var(--zw-text-1-color--default) !important;

  div,
  span {
    color: var(--zw-text-1-color--default) !important;
  }
}

.ant-menu-inline,
.ant-menu-vertical,
.ant-menu-vertical-left {
  border-right: none;
}

.chart-content {
  .chart-title {
    border-left: 3px solid var(--zw-primary-color--default);
  }
}

:deep(.ant-pagination-item-link) {
  border: 0px;
}

:deep(.ant-pagination-item) {
  border: 0px;
}

.solar-eye-linkage-text {
  margin-right: 40px;
  margin-left: 10px;
}

.solar-eye-linkage-icon {
  display: inline-block;
  height: 12px;
  width: 12px;
}

.solar-eye-flow-bg {
  height: 65vh;
  display: flex;

  img {
    margin: auto;
    max-width: 100%;
  }
}

.alarm-events-tooptips {
  max-width: 800px !important;
  max-height: 400px !important;
}
// 由于ai诊断要求悬浮窗超出不隐藏  所以tooltip加了appendToBody: true,  导致样式在诊断文件中不生效， 通过全局搜索  这个类只有在诊断中使用 所以放在这里
.alarm-events-tooptips {
  padding: 12px 16px !important;
  font-size:12px !important;
  .time {
    color: var(--zw-text-1-color--default);
    margin-right: 12px;
    font-size: 14px;
    font-weight: 600;
  }
  .station-starus {
    background: var(--zw-warning-color--normal);
    color: var(--white);
    box-shadow: 0px 2px 8px 0px rgba(0, 0, 0, 0.15);
    border-radius: 10px 2px 10px 2px;
    height: 20px;
    font-size: 12px;
    padding: 0 8px;
    font-weight: 600;
    line-height: 20px;
  }
  .alarm-data {
    display: flex;
    .data-content {
      background: linear-gradient(270deg, rgba(234, 115, 109, 0) 0%, rgba(234, 115, 109, 0.31) 100%);
      border-radius: 2px;
      padding: 4px 8px;
      margin-bottom: 12px;
      font-weight: 400;
      font-size: 12px;
      color: var(--zw-text-1-color--default);
      display: flex;
      align-items: center;
      .split-line {
        width: 1px;
        height: 12px;
        background: var(--zw-text-1-color--default);
        margin: 0 12px;
      }
    }
  }
}

.homepage-block-info-show.block-component[position='right'] {
  .left {
    img {
      width: 40px;
      height: 40px;
    }
  }

  .right {
    margin-left: 8px;
  }

  .info-num {
    font-size: 16px !important;
  }
}

.homepage-main-link:hover {
  color: var(--zw-primary-color--default) !important;
}

.over-flow {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.fault-table .vxe-header--row .vxe-cell {
  padding: 8px 10px !important;
}

.has-colon {
  .ant-form-item-label > label::after {
    content: '\FF1A' !important;
    margin: 0;
    padding-right: 8px;
  }
}

.close-search-menu {
  opacity: 0 !important;
}

.self-system {
  .loading-container {
    display: inline-block;
  }
}
:root[data-theme='light'] {
  .flow-chart-btn {
    background: url('../../images/budget_analysis/abnormal_analysis_bg_light.png') no-repeat;
  }

  .ant-menu.ant-menu-root .isParent.ant-menu-item-selected,
  .ant-menu.ant-menu-root .ant-menu-submenu-horizontal.ant-menu-submenu-selected {
    background: url('../../images/public/menu-select.png') no-repeat !important;
    background-size: 66px 14px !important;
    background-position: center 30px !important;
    background-color: transparent !important;
  }
}

:root[data-theme='dark'] {
  .ant-switch-loading-icon, .ant-switch::after {
    background: var(--zw-common-white-color--default) !important;
  }

  .ant-checkbox:not(.ant-checkbox-checked):not(.ant-checkbox-disabled) .ant-checkbox-inner, .ant-tree-checkbox:not(.ant-tree-checkbox-checked):not(.ant-tree-checkbox-disabled) .ant-tree-checkbox-inner {
    background-color: transparent;
    border-color: var(--zw-border-color--default);
  }

  .ant-tree li .ant-tree-node-content-wrapper.ant-tree-node-selected {
    background-color: var(--zw-table-bg-color--hover);
    color: var(--zw-primary-color--active);
  }

  .anticon.ant-tree-switcher-icon {
    color: var(--zw-common-white-color--default)
  }

  .ant-tree-checkbox.ant-tree-checkbox-disabled .ant-tree-checkbox-inner {
    background-color: var(--zw-border-color--default);
  }

  .flow-chart-btn {
    background: url('../../images/budget_analysis/abnormal_analysis_bg_dark.png') no-repeat;
  }

  .ant-menu.ant-menu-root .isParent.ant-menu-item-selected,
  .ant-menu.ant-menu-root .ant-menu-submenu-horizontal.ant-menu-submenu-selected {
    background: url('../../images/public/menu-select-dark.png') no-repeat !important;
    background-size: 66px 14px !important;
    background-position: center 30px !important;
    background-color: transparent !important;
  }
  .alarm-events-tooptips {
    .data-content {
      background: linear-gradient(90deg, #753e4d 0%, #1f334e 100%) !important;
    }
  }
}
