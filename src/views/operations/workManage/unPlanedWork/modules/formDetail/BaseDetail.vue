<!-- 任务基础表单 -->
<template>
  <detail-layout :labelList="baseInfo" :form="order()" title="" :dictMap="dictMap">
    <template v-slot:uavModelId="baseform">
      <a-col :span="8" class="detail_layout_content" v-if="order().taskType == '10' && order().workSubclass == '2'">
        <span class="left">无人机型号</span>
        <span class="right">{{ baseform.uavModelId }}</span>
      </a-col>
    </template>
    <template v-slot:stationEnvironment="baseform">
      <a-col :span="8" class="detail_layout_content" v-if="['1', '2'].includes(order().taskType)">
        <span class="left">电站环境</span>
        <span class="right">{{getStationModelList}}</span>
      </a-col>
    </template>
    <template v-slot:planTime="baseform">
      <a-col :span="8" class="detail_layout_content">
        <span class="left">计划时间</span>
        <span class="right">{{baseform.planStartTime}} &nbsp;~&nbsp;{{ baseform.planEndTime }}</span>
      </a-col>
    </template>
    <template v-slot:deviceNumber="baseform">
      <a-col :span="8" class="detail_layout_content" v-if="['4', '5', '6', '9', '10'].includes(order().taskType) && order().taskType != '10'">
        <span class="left">设备数量</span>
        <span class="right">{{getLength(order().deviceNo) }}</span>
      </a-col>
    </template>
  </detail-layout>
</template>

<script>
import { baseDetail } from './detailList';
import initDict from '@/mixins/initDict';
export default {
  name: 'baseForm',
  inject: ['type', 'order'],
  mixins: [initDict],
  data () {
    return {
      baseInfo: baseDetail
    };
  },
  watch: {

  },
  mounted () {},
  created () {
    this.getDictMap('pdca_clean_type,pdca_grass_type,ticket_work_condition,psa_model');
  },
  computed: {
    getStationModelList () {
      let arr = [];
      let items = this.dictMap.psa_model;
      let code = this.order().stationEnvironment;
      for (let i = items.length; i--; i > 0) {
        if (code.indexOf(items[i].codeValue) > -1 || code.indexOf(items[i].value) > -1) {
          arr.push(items[i].dispName || items[i].label);
        }
      }
      return arr.join('，');
    }
  },
  methods: {
    getLength (deviceNo) {
      let isNull = deviceNo ? deviceNo.split(',').length : 0;
      return isNull;
    }
  }
};
</script>
