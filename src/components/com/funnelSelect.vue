<!-- 漏斗筛选条件--全局组件 -->
<template>
  <div class="com-project-select" :style="{width: treeWidth}">
    <a-tree-select show-search v-model="selectId" :allowClear="false" :tree-default-expand-all="false" :tree-data="treeData" style="width: 100%;"
      :treeCheckable="treeCheckable" :treeCheckStrictly="!treeCheckable" :maxTagCount="0" :dropdownStyle="{ maxHeight: '65vh', overflow: 'auto' }"
      :replace-fields="{ children: 'children', title: 'title', key: 'key', value: 'value' }" treeNodeFilterProp="title" :treeExpandedKeys.sync="treeExpandedKeys"
      @change="onChange" @search="onSearch" v-if="!health" :class="{'tree-width':expanded}" dropdownClassName="tree-select-dropdown" @click="handleVisible($event)">
      <template v-if="treeCheckable" slot="maxTagPlaceholder">
        <span>已选电站&nbsp;{{ selectId.length }}&nbsp;个</span>
      </template>
    </a-tree-select>
    <!-- 监控中心使用 -->
    <a-dropdown v-else :getPopupContainer="(node) => node.parentNode" :trigger="['click']" v-model="visible">
      <div class="ant-input" @click="stopEvent">
        <span v-if="ischeck && !treeLoading">已选电站&nbsp;{{ selectId.length }}&nbsp;座</span>
        <span v-if="ischeck && treeLoading">请选择电站</span>
        <ellipsis v-else :value="psName" :length="length" />
      </div>
      <a-menu slot="overlay">
        <z-tree :nodes="treeData" :setting="setting" :showSearch="true" @onCreated="handleCreated" @onCheck="onChange"
          @onClick="onClick" v-if="treeData.length > 0"></z-tree>
      </a-menu>

      <a-button style="margin-left: 8px"> Button
        <a-icon type="down" />
      </a-button>
    </a-dropdown>
    <a-icon v-show="expanded" :title="doubleRight ? '收起' : '展开'" class="double-icon" @click="showMore()"
      :type="doubleRight ? 'menu-fold' : 'menu-unfold'" />
  </div>
</template>

<!-- 二.JS脚本 -->

<script>
import { USER_INFO } from '@/store/mutation-types';
import { queryDepartTreeList, queryDepartTreeListByOrgCode, deptTree } from '@/api/api';
import zTree from '@/components/ZTree';
import Ellipsis from '@/components/solareye/Ellipsis';
// import Vue from 'vue'
import store from '@/store/index';
import notification from 'ant-design-vue/es/notification';
import { getErpPjPsaTreeByUserId, getAllPjPsaTree } from '@/api/isolarErp/com/funnel';
export default {
  name: 'funnelSelect',
  components: {
    zTree,
    Ellipsis
  },
  model: {
    prop: 'value',
    event: 'change'
  },
  props: {
    // psId: {
    //   type: String | Number,
    //   default: ''
    // },
    value: {
      type: [String, Number],
      default: ''
    },
    // 项目树、部门树区分
    isproject: {
      type: Boolean,
      default: true
    },
    // 是否显示关闭的
    show: {
      type: Boolean,
      default: false
    },
    //
    expanded: {
      type: Boolean,
      default: true
    },
    showAllPsa: {
      type: Boolean,
      default: false
    },
    all: {
      type: Boolean,
      default: false
    },
    // 是否是监控中心的实体电站树
    health: {
      type: Boolean,
      default: false
    },
    ischeck: {
      type: Boolean,
      default: true
    },
    isDiabled: {
      type: Boolean,
      default: true
    },
    length: {
      type: Number,
      default: 16
    },
    width: {
      type: [Number, String],
      default: 180
    },
    isReset: {
      default: false,
      type: Boolean
    },
    isDeptTree: {
      type: Boolean,
      default: false
    }
  },
  data () {
    return {
      selectId: '',
      treeExpandedKeys: [],
      doubleRight: false,
      treeData: [],
      treeCheckable: false,
      treeLoading: false,
      parentId: null,
      allData: [],
      searchValue: '',
      autoExpandParent: false,
      visible: false,
      expandedKeys: [],
      backupsExpandedKeys: [],
      setting: {
        check: {
          enable: true
        },
        data: {
          simpleData: {
            enable: true,
            idKey: 'zid',
            pIdKey: 'pid'
          }
        },
        view: {
          showIcon: false,
          showLine: false
        }
      },
      psaList: [], // 实体电站列表
      psName: '',
      treeWidth: '',
      ztreeObj: null
    };
  },
  watch: {
    value (val, old) {
      if (this.health) {
        // if (val != old) {
        //   this.getProjectAndStationTree()
        // }
        if (!Array.isArray(val)) {
          this.selectId = [];
        } else {
          this.selectId = [...val];
        }
      } else {
        try {
          this.selectId = val.toString();
        } catch (e) {
          this.selectId = '';
        }
        if (this.isReset) {
          let top = this.treeData[0];
          let node = this.getNode(top);
          this.selectId = top.value;
          this.$emit('listenChange', top.value, top.title, node);
        }
      }
    },
    show (val, old) {
      this.doubleRight = val;
    }
  },
  created () {
    this.doubleRight = this.show;
    if (!this.ischeck) {
      this.setting = Object.assign({}, this.setting, {
        check: {
          enable: false
        },
        view: {
          fontCss: this.isDiabled ? this.setFontCss : {},
          showIcon: false,
          showLine: false
        }
      });
    }

    if (this.health) {
      this.treeCheckable = true;
      this.getProjectAndStationTree();
    } else {
      if (this.isproject) {
        // 项目
        this.loadNode();
      } else {
        // 部门
        this.loadDepart();
      }
    }
    this.treeWidth = typeof this.width === 'string' ? this.width : this.width + 'px';
  },
  methods: {
    // 展开或收起左侧树
    showMore () {
      this.doubleRight = !this.doubleRight;
      this.$emit('on-change', this.doubleRight);
    },
    handleVisible (el) {
      this.setTreeExpandedKeys();
      this.$nextTick(() => {
        let doc = document;
        let box = doc.querySelector('.ant-select-tree li .ant-select-tree-node-content-wrapper.ant-select-tree-node-selected');
        let dropdown = doc.querySelector('.tree-select-dropdown');
        if (box && !dropdown.display) {
          box.scrollIntoView(false);
        }
      });
    },
    setTreeExpandedKeys () {
      let _this = this;

      _this.$nextTick(() => {
        this.findSelected(this.selectId);
      });
    },
    findSelected (selectId) {
      let treeExpandedKeys = this.treeExpandedKeys = [];
      let node = this.allData.find(item => (item.projectId || item.id) == selectId);
      if (node) {
        treeExpandedKeys.push(node.key);
        if (node.pId || node.parentId) {
          this.findSelected(node.pId || node.parentId);
        }
      }
      node = null;
      this.treeExpandedKeys = treeExpandedKeys.reverse();
    },
    setFontCss (treeId, treeNode) {
      return treeNode.treeNodeType != '3' ? {
        color: '#9D9997',
        cursor: 'not-allowed',
        'pointer-events': 'none'
      } : {};
    },
    stopEvent (e) {
      e.preventDefault();
      this.visible = !this.visible;
      this.searchValue = '';
    },
    handleCreated: function (ztreeObj) {
      // 初始化获取ztree 对象
      this.ztreeObj = ztreeObj;
      if (this.ischeck) { // 第一次初始化选中所有节点 单选则选中节点展开
        this.ztreeObj.checkAllNodes(true);
      } else {
        this.selectId = this.value;
        let treeNode = this.ztreeObj.getNodeByParam('projectId', this.selectId);
        this.ztreeObj.selectNode(treeNode, false, false);
      }

      this.selectStation();
      this.selectId = this.psaList;
    },
    onClick (event, treeId, treeNode) {
      if (!this.ischeck) {
        this.psName = treeNode.name;
        this.$emit('listenChange', {
          psName: treeNode.name,
          psId: treeNode.value,
          node: treeNode
        });
      }
      this.visible = false;
    },
    selectStation () {
      // 树的选中状态
      this.selectId = [];
      let arr = this.ztreeObj.getCheckedNodes(true);
      let ids = [];
      let is_all = false;
      if (arr.length) {
        arr.forEach((item) => {
          if (item.treeNodeType === '3') {
            this.selectId.push(item.value);
            ids.push(item.value);
          }
        });
        // 判断是否选中根节点
        is_all = this.selectId.length === this.psaList.length;
      }
      if (this.ischeck) {
        this.$emit('listenChange', {
          isRootNode: is_all,
          psIds: is_all === true ? [] : ids,
          selectId: this.selectId.length,
          isFirstReq: false
        });
      }
    },
    // 树节点选中change事件
    onChange (value, label, extra) {
      const self = this;
      if (self.health) {
        this.selectStation();
      } else {
        value = value == null ? '' : value.toString();
        self.$emit('listenChange', value, (Array.isArray(label) ? label.join(',') : label), this.getSelectData(value, this.allData));
      }
    },
    onSearch (value) {
      let _this = this;
      this.$nextTick(() => {
        _this.treeExpandedKeys = _this.allData.map(item => {
          return item.key;
        });
      });
    },
    getSelectData (id, _arr) {
      let result = false;
      seachItem(_arr);
      // 递归查询当前选中的信息给后端
      function seachItem (arr) {
        for (let i = 0, len = arr.length; i < len; i++) {
          if (arr[i].projectId == id) {
            result = {
              name: arr[i].name,
              isPjPsa: arr[i].isPjPsa,
              psaId: arr[i].psaId,
              projectId: arr[i].projectId
            };
            break;
          }
          if (!result && arr[i].children && arr[i].children.length > 0) {
            seachItem(arr[i].children);
          }
        }
      }
      return result || {};
    },
    // 实体电站树
    getProjectAndStationTree () {
      let arr = store.getters.realStationTreeList;
      let realStationTreeList = typeof arr === 'string' ? JSON.parse(arr) : arr;
      this.treeData = [];
      this.allData = [];
      if (realStationTreeList.length === 0) {
        this.treeLoading = true;
        store
          .dispatch('GetRealStationTreeList')
          .then((res) => {
            this.treeLoading = false;
            if (res.result_code == '1') {
              if (res.result_data.length === 0) {
                return;
              }
              this.dealTreeData({
                data: res.result_data,
                isRealStation: true
              });
            }
          })
          .catch((err) => {
            notification.error({
              message: '提示',
              description: err || '该用户下暂无实体电站树'
            });
          });
      } else {
        this.dealTreeData({
          data: realStationTreeList,
          isRealStation: true
        });
      }
    },
    // 项目树
    loadNode () {
      const user = Vue.ls.get(USER_INFO);
      const map = {
        userId: user.id,
        onlyAllProject: (this.all ? '1' : '0')
      };
      if (this.showAllPsa) {
        getAllPjPsaTree(map).then((res) => {
          this.dealTreeData({
            data: res.result_data
          });
        }).catch(() => {
        });
      } else {
        getErpPjPsaTreeByUserId(map).then((res) => {
          this.dealTreeData({
            data: res.result_data
          });
        }).catch(() => {

        });
      }
    },
    setDataToChild (isRealStation) {
      let self = this;
      if (self.treeData.length) {
        if (!isRealStation) {
          let top = self.treeData[0];
          let node = self.getNode(top);
          self.selectId = top.value;
          self.$emit('listenChange', top.value, top.title, node, {
            treeData: self.treeData,
            allData: self.allData
          });
        } else {
          self.parentId = self.treeData[0].value;
          if (self.ischeck) {
            self.selectId = self.psaList;
            self.$forceUpdate();
            self.$emit('listenChange', {
              isRootNode: true,
              isFirstReq: true,
              psIds: [],
              selectId: self.selectId.length
            });
          }
        }
      }
    },
    /**
       *  处理返回的数据
       * params arr 后端返回的原始树数据
       */
    dealTreeData (obj) {
      // 处理返回的数据
      let items = obj.data;
      let isRealStation = obj.hasOwnProperty('isRealStation') && obj.isRealStation;
      items.forEach((item, index) => {
        item.value = item.projectId.toString();
        item.title = item.name;
        if (isRealStation) {
          item.pId = item.pid;
          item.key = 'key'.concat(item.projectId.toString());
          if (item.treeNodeType === '3') {
            this.psaList.push(item.value);
          }

          if (this.value == item.projectId) {
            this.psName = item.name;
            this.$emit('listenChange', {
              psName: item.name,
              psId: item.projectId
            });
          }
          if (!this.isDiabled && item.pid == -1) {
            this.psName = item.name;
          }
        } else {
          item.key = 'key'.concat(index.toString());
        }
      });
      this.allData = Object.freeze(items);
      if (!isRealStation) {
        this.treeData = Object.freeze(this.getNodes(items));
      } else { // 实体电站树使用ztree 不转化成children 格式
        this.treeData = Object.freeze(items);
      }

      this.setDataToChild(isRealStation);
    },
    // 获取项目节点信息
    getNode (data) {
      if (!data) {
        return {};
      }
      let self = this;
      const node = {
        name: self.isHasProperty('name', data),
        isPjPsa: self.isHasProperty('isPjPsa', data),
        psaId: self.isHasProperty('psaId', data),
        projectId: self.isHasProperty('projectId', data)
      };
      return node;
    },
    isHasProperty (key, obj) {
      return obj.hasOwnProperty(key) ? obj[key] : null;
    },
    // 树形数据转换
    getNodes (data) {
      let result = [];
      if (!Array.isArray(data)) {
        return result;
      }
      //  空对象
      let map = {};
      data.forEach((item) => {
        map[item.projectId] = item;
      });
      data.forEach((item) => {
        let parent = map[item.pId];
        if (parent) {
          ;
          (parent.children || (parent.children = [])).push(item);
        } else {
          result.push(item);
        }
      });
      return result;
    },
    // 部门树
    loadDepart () {
      const user = Vue.ls.get(USER_INFO);
      let isHasAllData = this.isDeptTree ? deptTree({
        show: this.show
      }) : (this.all || user.hasAllData == '1'
        ? queryDepartTreeList({
          show: this.show
        })
        : queryDepartTreeListByOrgCode({
          orgCode: user.orgCode,
          show: this.show
        }))
          ;
      isHasAllData.then((res) => {
        if (res.success) {
          this.treeData = Object.freeze([...res.result]);
        } else {
          this.treeData = [];
        }
        if (this.treeData.length) {
          // 初始化加载时默认顶级节点
          let top = this.treeData[0];
          this.selectId = top.value;
          this.$emit('listenChange', top.value, top.title);
          // 返回树的顶节点 方便页面手动添加默认节点
          this.$emit('listenTopValue', top.value);
        }
        this.getAllDep(this.treeData);
      });
    },
    getAllDep (data) {
      let all = [];
      let getAll = (list) => {
        list.forEach(item => {
          if (Array.isArray(item.children) && item.children.length) {
            getAll(item.children);
          }
          let o = JSON.parse(JSON.stringify(item));
          delete o.children;
          all.push(o);
        });
      };
      getAll(data);
      this.allData = Object.freeze(all);
    }
  },
  destroyed () {

  },
  beforeDestroy () {
    this.treeData = null;
    this.allData = null;
    this.$el.remove();
    this.$el = null;
    if (this.ztreeObj) {
      this.ztreeObj.destroy();
      this.ztreeObj = null;
    }
    this.data = null;
  }
};
</script>

<style lang="less" scoped>
  .com-project-select {
    //width: 180px;
    display: inline-flex;
    align-items: center;
  }

  .tree-width {
    width: calc(100% - 36px) !important;
  }

  .double-icon {
    margin-left: 8px;
    font-size: 18px;
    padding: 6px;
    border-radius: 4px;
    cursor: pointer;
    border: 1px solid #d6d6d6;
  }

  .double-icon:hover {
    color: #40a9ff;
    border-color: #40a9ff;
  }

  :deep(.ztree) {
    padding: 0 10px;
  }
</style>
