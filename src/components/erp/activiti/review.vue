<template>
<a-modal ref="activereview" title="审批意见" v-model="visible" :maskClosable="false" centered @cancel="cancel" width="30%">
  <a-spin :spinning="loading">
    <a-form-model ref="auditform" :model="form" :rules="rules" :label-col="{span: 0}" :wrapper-col="{span: 24}">
      <a-form-model-item label="" prop="auditOpinion">
        <a-textarea v-model="form.auditOpinion" @blur="form.auditOpinion = $trim($event)" :max-length="64" :auto-size="{ minRows: 4, maxRows: 6}"
          placeholder="请输入审批意见" show-word-limit></a-textarea>
      </a-form-model-item>
      <a-form-model-item v-if="taskDefKey =='act1' && type =='1'" label="" prop="needViceManagerApproval">
        <span>是否需要运维副总经理审批&nbsp;&nbsp;&nbsp;</span>
        <a-radio-group v-model="form.needViceManagerApproval">
          <a-radio value="0">不需要</a-radio>
          <a-radio value="1">需要</a-radio>
        </a-radio-group>
      </a-form-model-item>
    </a-form-model>
    </a-spin>
     <div slot="footer" class="modal-footer">
      <!-- 单条数据审批 -->
      <template v-if="simple">
        <a-button size="default" type="primary" :loading="loading" @click="commit()">确定</a-button>
        <a-button size="default" type="warning" :disabled="loading" @click="cancel()">取消</a-button>
      </template>
      <!-- 批量审批 -->
      <template v-else>
        <a-button size="default" type="primary" :loading="loading" @click="commitOth('1')">通过</a-button>
        <a-button size="default" type="warning" :loading="loading" @click="commitOth('-1')">退回</a-button>
      </template>
    </div>
  </a-modal>
</template>

<script>
export default {
  model: {
    prop: 'visible',
    event: 'change'
  },
  props: {
    visible: {
      type: Boolean,
      default: false,
      required: false
    },
    // 1、通过  -1、退回
    type: {
      type: String,
      default: '1'
    },
    simple: {
      type: Boolean,
      default: true
    },
    // 月度费用审批判断第一阶段使用
    taskDefKey: {
      type: String,
      default: ''
    }
  },
  watch: {
    visible (val, old) {
      this.form.auditOpinion = '';
      this.form.needViceManagerApproval = '0';
      this.auditOpinion = '';
      this.loading = false;
    },
    type (val, old) {
      // 退回时审批意见必填
      if (val && val != '1') {
        this.rules = {
          auditOpinion: [
            { required: true, message: '请输入审批意见' }
          ]
        };
      } else {
        this.rules = {};
      }
    }
  },
  data () {
    return {
      form: {
        auditOpinion: '', // 审批意见
        needViceManagerApproval: '0'
      },
      rules: {},
      loading: false
    };
  },
  methods: {
    commit () {
      const self = this;
      self.loading = true;
      self.$refs['auditform'].validate((valid) => {
        if (valid) {
          // 调用父页面的审批方法并回传审批意见和状态
          self.$emit('reviewFlow', self.form.auditOpinion, self.type, self.form.needViceManagerApproval);
        } else {
          self.loading = false;
          return false;
        }
      });
    },
    // 批量审批
    commitOth (type) {
      const self = this;
      self.loading = true;
      if (type == '-1') {
        self.rules = {
          auditOpinion: [
            { required: true, message: '请输入审批意见' }
          ]
        };
      } else {
        self.rules = {};
      }
      self.$nextTick(() => {
        self.$refs['auditform'].validate((valid) => {
          if (valid) {
            // 调用父页面的审批方法并回传审批意见和状态
            self.$emit('reviewFlow', self.form.auditOpinion, type);
          } else {
            self.loading = false;
            return false;
          }
        });
      });
    },
    // 关闭回调方法
    cancel () {
      this.loading = false;
      this.form.auditOpinion = '';
      this.form.needViceManagerApproval = '0';
      this.$refs['auditform'].clearValidate();
      this.$emit('change', false);
      this.visible = false;
      // 确保关闭后可以再次打开
      this.$refs.activereview.$destroyAll();
    }
  }
};
</script>

<style lang="less" scoped>
  .dialog-footer{
    width: 100%;
    margin-top: 10px;
    text-align: center;
  }
</style>
