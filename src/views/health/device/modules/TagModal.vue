<!-- 添加标签弹窗 -->
<template>
  <a-modal title="设置设备挂牌" v-model="tagVisible" :maskClosable="false" centered @cancel="cancel" width="450px">
    <a-spin :spinning="loading">
      <a-form-model
        ref="backNullifyForm"
        :label-col="{ span: 0 }"
        :model="form"
        :rules="rules"
        :wrapper-col="{ span: 24 }"
      >
        <a-form-item label="现场情况:" name="resource" class="slect-radio">
          <a-radio-group v-model="tagReason">
            <a-radio
              :disabled="item.secondTypeCode === '7' && isDisableBtn"
              v-for="item in dictOptions"
              :key="item.secondTypeCode"
              :value="item.secondTypeCode"
              >{{ item.secondName }}
            </a-radio>
          </a-radio-group>
        </a-form-item>
      </a-form-model>
    </a-spin>
    <div slot="footer">
      <throttle-button :loading="loading" label="确定" @click="commitData()" />
      <throttle-button :disabled="loading" class="solar-eye-btn-primary-cancel" label="取消" @click="cancel()" />
    </div>
  </a-modal>
</template>
<script>
import { taggingList, tagging, getDict } from '@/api/health/AlarmEvents.js';

export default {
  props: {
    isBatchTag: {
      type: Boolean,
      default: false
    },
    isDisableBtn: {
      type: Boolean,
      default: false
    },
    selectedRows: {
      type: Array,
      default: () => []
    },
    selectedRowData: {
      type: Array,
      default: () => []
    }
  },
  data () {
    return {
      tagVisible: false,
      tagReason: '',
      loading: false,
      form: {},
      rules: {},
      dictOptions: []
    };
  },
  created () {
    this.getTagDict();
  },
  methods: {
    init (tagReason) {
      this.tagVisible = true;
      this.tagReason = tagReason;
    },
    getTagDict () {
      getDict({ firstTypeCode: '0233' }).then((res) => {
        this.dictOptions = res.result_data;
      });
    },
    // 取消弹窗
    cancel () {
      this.tagVisible = false;
      this.tagReason = '';
    },
    // 提交数据-  1 单个打标签  2 多个打标签
    commitData () {
      this.loading = true;
      // 批量打标签
      if (this.isBatchTag) {
        let params = {
          tag: this.tagReason,
          list: []
        };
        this.selectedRows.forEach((item) => {
          params.list.push({
            id: item.id,
            psKey: item.psKey,
            alarmReason: item.alarmReason
          });
        });
        taggingList(params)
          .then(() => {
            this.$message.success(`操作成功`);
            this.tagVisible = false;
            this.$emit('addTag');
            this.loading = false;
          })
          .catch(() => {
            this.loading = false;
          });
      } else {
        let params = {
          id: this.selectedRowData[0].id,
          tag: this.tagReason,
          psKey: this.selectedRowData[0].psKey,
          alarmReason: this.selectedRowData[0].alarmReason
        };
        tagging(params)
          .then(() => {
            this.$message.success(`操作成功`);
            this.tagVisible = false;
            this.$emit('addTag');
            this.loading = false;
          })
          .catch(() => {
            this.loading = false;
          });
      }
    }
  }
};
</script>
<style lang="less" scoped>
.slect-radio {
  display: flex;
  align-items: flex-start;

  :deep(.ant-col-0) {
    display: block !important;
    line-height: 1.8;
  }

  :deep(.ant-form-item-control-wrapper) {
    flex: 1;

    .ant-radio-group {
      display: flex;
      flex-wrap: wrap;
      justify-content: space-between;
    }

    .ant-radio-wrapper {
      flex: 1;
      margin-bottom: 10px;
    }
  }
}
</style>
