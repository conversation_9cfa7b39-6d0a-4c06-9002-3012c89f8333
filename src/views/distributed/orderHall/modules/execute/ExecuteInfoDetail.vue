<template>
  <!-- 标准组串清洗 -->
  <detail-layout v-if='baseForm.taskType === "13"' :labelList="groupLabelList" :form="baseForm.groupCleanExecuteVo || {}" :dictMap='dict' title="执行信息">
    <template v-slot:uploadFileParams='groupCleanExecuteVo' v-if="baseForm.groupCleanExecuteVo && baseForm.groupCleanExecuteVo.groupCleanState === '1'">
      <a-col :span="8" class="detail_layout_content">
        <span class="left">上传视频</span>
        <span class="right" >
           <div class='video-box' v-if='groupCleanExecuteVo.uploadFileParams && groupCleanExecuteVo.uploadFileParams.length'>
              <img class='video-play' src='@/assets/images/orderHall/video-play.png' @click='videoPlayFn'/>
              <img  class='video-poster' :src='coverFirstFrame(groupCleanExecuteVo)'/>
            </div>
            <span v-else>--</span>
        </span>
      </a-col>
    </template>
  </detail-layout>
  <!-- 应发电量 -->
  <CapacityEvaluationDetail v-else-if="baseForm.taskType === '10'" :baseForm="baseForm.businessData" />
  <!-- 其他类型 -->
  <detail-layout v-else :labelList="labelList" :form="baseForm" title="执行信息">
    <template v-slot:serverCycleRange v-if="['4', ...weatherServiceType].includes(baseForm.taskType) && !(baseForm.stepVoList && baseForm.stepVoList.length) || ['17',...insuranceBuyType].includes(baseForm.taskType)">
      <a-col :span="8" class="detail_layout_content">
        <span class="left">服务周期</span>
        <span class="right" v-if='baseForm.serverCycleStartDate'>
          {{ getLabel(baseForm.serverCycleStartDate)}}~{{getLabel(baseForm.serverCycleEndDate)}}
        </span>
        <span class="right" v-else>--</span>
      </a-col>
    </template>
    <template v-slot:billCycleRange v-if="baseForm.taskType==='14'">
      <a-col :span="8" class="detail_layout_content">
        <span class="left">账单周期</span>
        <span class="right" v-if='baseForm.serverCycleStartDate'>
          {{ getLabel(baseForm.serverCycleStartDate)}}~{{getLabel(baseForm.serverCycleEndDate)}}
        </span>
        <span class="right" v-else>--</span>
      </a-col>
    </template>
    <template v-slot:picFileListSlot v-if="['15','16'].includes(baseForm.taskType)">
      <a-col :span="8" class="detail_layout_content">
        <span class="left">{{baseForm.taskType === '15' ? '完工确认单' :'入库单'}}/发票</span>
        <div class="right" style='display: flex'>
          <uploadFileView v-if='baseForm.documentPicFileList && baseForm.documentPicFileList.length' v-model="baseForm.documentPicFileList" disabled listType="picture-card" />
          <DetailEmpty v-else/>
          <uploadFileView v-if='baseForm.invoicePicFileList && baseForm.invoicePicFileList.length' v-model="baseForm.invoicePicFileList" disabled listType="picture-card" />
          <DetailEmpty v-else/>
        </div>
      </a-col>
    </template>
    <template v-slot:appearance v-if="baseForm.taskType==='18'">
      <PsaAppearance
        ref='psaAppearance'
        :isDisabled='true'
        :appearanceVoList='baseForm.appearanceVoList'
        :appearanceScore='baseForm.appearanceScore'
        :otherSts='baseForm.otherSts'
        :type='type'
      />
    </template>
    <!-- 签到图片 -->
    <template v-slot:sceneFileList v-if="['7','8'].includes(baseForm.taskType) && baseForm.checkInRecord && baseForm.checkInRecord.checkInPic">
      <a-col :span="8" class="detail_layout_content">
        <span class="left">签到图片</span>
        <div class="right" style='display: flex'>
          <uploadFileView v-model="baseForm.checkInRecord.checkInPic" disabled listType="picture-card" />
        </div>
      </a-col>
    </template>
  </detail-layout>
</template>

<script>

import uploadFileView from '@comp/com/fileUploadView.vue';
import DetailEmpty from '../stepsForClean/modules/DetailEmpty';
import PsaAppearance from './PsaAppearance';
import CapacityEvaluationDetail from './CapacityEvaluationDetail';
import TaskAndOrder, { weedTypeEnum, weatherServiceTypeEnum, insuranceBuyTypeEnum, renewalTaskTypeEnum } from '@/mixins/TaskAndOrder';
export default {
  name: 'ExecuteInfoDetail',
  mixins: [TaskAndOrder],
  components: { DetailEmpty, uploadFileView, PsaAppearance, CapacityEvaluationDetail },
  inject: ['coverFirstFrame', 'providePlayFn', 'getFileExtension'],
  props: {
    baseForm: {
      type: Object,
      default: () => {
        return {};
      }
    },
    dict: {
      type: Object,
      default: () => ({})
    },
    type: {
      type: String,
      default: ''
    }
  },
  data () {
    return {
      groupLabelList: [
        {
          // slot: 'groupCleanState',
          label: '清洗状态',
          key: 'groupCleanState',
          dict: 'group_clean_sts'
        },
        {
          // slot: 'groupCleanDate'
          label: '清洗时间',
          key: 'groupCleanDate',
          span: 16,
          func: (params) => {
            return params.groupCleanState === '1';
          }
        },
        {
          // slot: 'picBeforeClean'
          label: '清洗前照片',
          key: 'picBeforeClean',
          type: 'file:picture-card',
          func: (params) => {
            return params.groupCleanState === '1';
          }
        },
        {
          // slot: 'picAfterClean',
          label: '清洗后照片',
          key: 'picAfterClean',
          span: 16,
          type: 'file:picture-card',
          func: (params) => {
            return params.groupCleanState === '1';
          }
        },
        {
          slot: 'uploadFileParams'
        },
        {
          // slot: 'groupCleanRemark'
          label: '备注',
          key: 'groupCleanRemark',
          func: (params) => {
            return params.groupCleanState === '1';
          }
        },
        {
          // slot: 'groupCleanReason'
          label: '原因',
          key: 'groupCleanReason',
          func: (params) => {
            return params.groupCleanState === '0';
          }
        }
      ],
      labelList: [
        {
          label: '现场情况',
          key: 'sceneConditionName',
          func: (params) => {
            return this.conditionShow;
          }
        },
        {
          label: '故障停运容量(MWp)',
          key: 'faultCap',
          func: (params) => {
            return this.realConditionShow;
          }
        },
        {
          label: '累计损失电量(万KWh)',
          key: 'totalLossPower',
          func: (params) => {
            return this.realConditionShow;
          }
        },
        {
          label: '派发/领取时间',
          key: 'realStartTime',
          func: (params) => {
            return params.taskType !== '18';
          }
        },
        {
          label: '执行提交时间',
          key: 'impCommitTime',
          func: (params) => {
            return params.taskType !== '18';
          }
        },
        {
          label: '累计修复时间(h)',
          key: 'totalRepairTime',
          func: (params) => {
            return this.realConditionShow;
          }
        },
        {
          label: '逾期原因',
          key: 'overdueReason',
          func: (params) => {
            return this.realConditionShow;
          }
        },
        {
          label: '保单',
          key: 'guaranteeSlipFileList',
          type: 'file:text',
          func: (params) => {
            return ['17', ...Object.keys(insuranceBuyTypeEnum)].includes(params.taskType);
          }
        },
        {
          label: params => {
            if (params.stepVoList && params.stepVoList.length) {
              return '厂家名称';
            }
            return '供应商名称';
          },
          labelFunc: true,
          key: 'meteoMaker',
          func: (params) => {
            if (params.stepVoList && params.stepVoList.length) {
              return (params.taskType === '4' && params.workSubclass === '20') || params.taskType === '47';
            }
            return ['4', '17', ...Object.keys(renewalTaskTypeEnum)].includes(params.taskType);
          },
          ellipsis: true
        },
        {
          slot: 'serverCycleRange'
        },
        {
          label: '实际费用（元）',
          key: 'orderRealCost',
          func: (params) => {
            // 系统维护、除草、下网电费、物资代采、抢修、保险购买
            if (['2', '14', '15', '16', '17', ...Object.keys({ ...weedTypeEnum, ...insuranceBuyTypeEnum })].includes(params.taskType)) {
              return true;
            } else if (['4', ...Object.keys(weatherServiceTypeEnum)].includes(params.taskType)) {
              if (params.stepVoList && params.stepVoList.length) {
                return false;
              }
              return true;
            }
            return false;
          }
        },
        {
          slot: 'billCycleRange'
        },
        {
          label: '其他附件',
          key: 'executeUniversalFileList',
          type: 'file:text',
          func: (params) => {
            return ['17', '47', '48', '49', ...Object.keys(insuranceBuyTypeEnum)].includes(params.taskType);
          }
        },
        {
          label: '上传结算单',
          key: 'settlementDocFileList',
          type: 'file:picture-card',
          func: (params) => {
            return params.taskType === '14';
          },
          span: 24
        },
        {
          // label: '完工确认单/发票'
          slot: 'picFileListSlot'
        },
        {
          label: '备注',
          key: 'conditionRemark',
          span: 24,
          lineClamp: 6,
          func: (params) => {
            return this.realConditionShow || !['7', '8', '18'].includes(params.taskType);
          }
        },
        {
          label: '现场图片',
          key: 'sceneFileList',
          span: 24,
          type: 'file:text',
          func: (params) => {
            return this.otherConditionShow;
          }
        },
        {
          label: '签到图片',
          slot: 'sceneFileList'
        },
        {
          slot: 'appearance'
        }
      ]
    };
  },
  methods: {
    show () {
      return ['7', '8'].includes(this.baseForm.taskType);
    },
    videoPlayFn () {
      const uploadFileParams = this.baseForm.groupCleanExecuteVo.uploadFileParams;
      const fileType = this.getFileExtension(uploadFileParams[0].fileName);
      const fileName = uploadFileParams[0].fileName;
      const path = uploadFileParams[0].path;
      this.providePlayFn(fileType, fileName, path);
    }
  },
  computed: {
    conditionShow () {
      const formData = this.baseForm;
      // 6: 智能巡检
      return ['4', '5', '6'].includes(formData.orderSource) && ['7', '8'].includes(formData.taskType);
    },
    realConditionShow () {
      const formData = this.baseForm;
      if (this.conditionShow) {
        return formData.sceneCondition === '1' && ['7', '8'].includes(formData.taskType);
      }
      return ['7', '8'].includes(formData.taskType);
    },
    otherConditionShow () {
      const formData = this.baseForm;
      if (this.conditionShow) {
        return formData.sceneCondition && formData.sceneCondition !== '1';
      }
      return false;
    }
  }
};
</script>
<style lang="less" scoped>
:deep(.ant-form-item) {
  width: 100%;
  display: inline-flex;
}

:deep(.ant-steps-vertical .ant-steps-item-description) {
  padding: 24px 0 0;
}

.step-box {
  :deep(.ant-form-item-label) {
    width: 134px !important;
  }
}

.last-form-item {
  :deep(.ant-form-item-label) {
    width: 62px !important;
  }

  :deep(.ant-form-item-control-wrapper) {
    width: calc(100% - 62px) !important;
  }
}

.example-btn {
  font-size: 12px !important;
  padding: 0 8px !important;
  height: 22px !important;
  margin-left: 16px;
}

.title-box {
  margin-top: -8px;
}
</style>
