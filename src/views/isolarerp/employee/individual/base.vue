<template>
  <div class="base-form-model">
    <div class="left-model">
      <!-- 基本信息 -->
      <div class="top-divider color_bg com-color">基本信息</div>
      <a-row :gutter="24">
        <a-col v-for="item in baseList" :key="item.field" :xxl='8' :xl='12' :lg='24' class="col-item">
          <div class="item-label com-color">
            <span>{{item.label}}:</span>
          </div>
          <div class="item-data com-color">
            <!-- 联系方式 -->
            <template v-if="item.field == 'tel'">
              <a-input v-model="form['tel']" size="default" :maxLength="20" :disabled="!editTel" @blur="updateField('tel')" style="width:calc(100% - 30px);margin-right:10px;"></a-input>
              <a-icon @click="editField('tel')" v-show="!editTel && !form.isgId" type="form"/>
            </template>
            <!-- 文本 -->
            <template v-else-if="item.type == 'text'">
              <div class="label" :title="form[item.field]">{{getLabel(form[item.field],null)}}</div>
            </template>
            <!-- 数据字典 -->
            <template v-else>
              <div class="label">{{getLabel(form[item.field],dict[item.type])}}</div>
            </template>
          </div>
        </a-col>
      </a-row>
      <!-- 个人信息 -->
      <!-- 个人信息 -->
      <a-divider orientation="left">个人信息</a-divider>
      <a-row :gutter="24">
        <a-col v-for="item in indList" :key="item.field" :xxl='8' :xl='12' :lg='24' class="col-item">
          <div class="item-label com-color">
            <span>{{item.label}}:</span>
          </div>
          <div class="item-data com-color">
            <!-- 邮箱 -->
            <template v-if="item.field == 'mail'">
              <a-input v-model="form['mail']" size="default" :maxLength="50" :disabled="!editMail" @blur="updateField('mail')" style="width:calc(100% - 30px);margin-right:10px;"></a-input>
              <a-icon @click="editField('mail')" v-show="!editMail && !form.isgId" type="form"/>
            </template>
            <!-- 地址 -->
            <template v-else-if="item.field == 'address'">
              <a-input v-model="form['address']" size="default" :maxLength="200" :disabled="!editAddress" @blur="updateField('address')" style="width:calc(100% - 30px);margin-right:10px;"></a-input>
              <a-icon @click="editField('address')" v-show="!editAddress && !form.isgId" type="form"/>
            </template>
            <!-- 文本 -->
            <template v-else-if="item.type == 'text'">
              <div class="label" :title="form[item.field]">{{getLabel(form[item.field],null)}}</div>
            </template>
            <!-- 数据字典 -->
            <template v-else>
              <div class="label">{{getLabel(form[item.field],dict[item.type])}}</div>
            </template>
          </div>
        </a-col>
      </a-row>
      <!-- 工作信息 -->
      <!-- 工作信息 -->
      <a-divider orientation="left">工作信息</a-divider>
      <a-row :gutter="24">
        <a-col v-for="item in workList" :key="item.field" :xxl='8' :xl='12' :lg='24' class="col-item">
          <div class="item-label com-color">
            <span>{{item.label}}:</span>
          </div>
          <div class="item-data com-color">
            <!-- 文本 -->
            <template v-if="item.type == 'text'">
              <div class="label" :title="form[item.field]">{{getLabel(form[item.field],null)}}</div>
            </template>
            <!-- 数据字典 -->
            <template v-else>
              <div class="label">{{getLabel(form[item.field],dict[item.type])}}</div>
            </template>
          </div>
        </a-col>
      </a-row>
    </div>
    <!-- <div class="right-model">
      <img v-if="picImg" :src="picImg"/>
      <div v-else class="image-slot">
        <div style="margin: auto;">照片未上传</div>
      </div>
    </div> -->
  </div>
</template>

<script>
import { updateHomepageEmployee } from '@/api/isolarErp/employee/employee';
export default {
  mixins: [],
  components: {
  },
  props: {
    form: {
      type: Object,
      default: null
    },
    dict: {
      type: Object,
      default: null
    }
  },
  watch: {

  },
  data () {
    return {
      fieldList: [],
      picImg: '',
      oldValue: '',
      editTel: false,
      editMail: false,
      editAddress: false,
      // 基本信息
      baseList: [
        { field: 'empName', label: '姓名', type: 'text' }, { field: 'tel', label: '联系方式', type: 'text' }, { field: 'departName', label: '所属部门', type: 'tex' },
        { field: 'position', label: '职位', type: 'position' }, { field: 'roleName', label: '角色分配', type: 'tex' }, { field: 'drName', label: '数据权限', type: 'tex' }, { field: 'userAccount', label: '用户账号', type: 'text' }
      ],
      // 个人信息
      indList: [
        { field: 'idCard', label: '身份证号', type: 'text' }, { field: 'sex', label: '性别', type: 'sex' }, { field: 'nation', label: '民族', type: 'nation' }, { field: 'politicalStatus', label: '政治面貌', type: 'political_status' },
        { field: 'maritalSts', label: '婚姻状况', type: 'marital_sts' }, { field: 'country', label: '国家(地区)', type: 'country' }, { field: 'expiryDate', label: '证件有效期', type: 'tex' },
        { field: 'mail', label: '电子邮箱', type: 'text' }, { field: 'birthDate', label: '出生年月日', type: 'tex' }, { field: 'age', label: '年龄', type: 'text' },
        { field: 'workAge', label: '工龄', type: 'text' }, { field: 'address', label: '住址', type: 'text' }, { field: 'household', label: '身份证地址', type: 'text' }
      ],
      // 工作信息
      workList: [
        { field: 'empId', label: '工号', type: 'text' }, { field: 'property', label: '员工类型', type: 'property' }, { field: 'isleader', label: '是否是负责人', type: 'yes_no_emp' },
        { field: 'empLevel', label: '岗位职级', type: 'level' }, { field: 'workSts', label: '员工状态', type: 'work_sts_emp' }, { field: 'startDate', label: '入职时间', type: 'tex' },
        { field: 'workFirstTime', label: '首次参加工作时间', type: 'text' }, { field: 'probationPeriod', label: '试用期', type: 'probation_period' }, { field: 'tutorName', label: '导师', type: 'text' },
        { field: 'workTicThrPpl', label: '三种人', type: 'work_tic_thr_ppl' }, { field: 'companyAge', label: '司龄', type: 'text' }, { field: 'promotionDate', label: '转正日期', type: 'tex' },
        { field: 'desertDate', label: '离职时间', type: 'tex' }, { field: 'salaryPayPlace', label: '发薪地', type: 'text', length: 100 }
      ]
    };
  },
  methods: {
    // 显示修改信息 type TEL、联系方式  MAIL、电子邮箱  ADDRESS、家庭住址
    editField (field) {
      if (this.editTel || this.editMail || this.editAddress) {
        this.$message.warning('当前已有信息在修改，请先完善当前数据');
        return;
      }
      this.oldValue = this.form[field];
      if (field == 'tel') {
        this.editTel = true;
      } else if (field == 'mail') {
        this.editMail = true;
      } else if (field == 'address') {
        this.editAddress = true;
      }
    },
    // 显示修改信息 type TEL、联系方式  MAIL、电子邮箱  ADDRESS、家庭住址
    updateField (field) {
      const self = this;
      const value = self.form[field].trim();
      self.form[field] = value;
      if (field == 'tel') {
        if (!value) {
          self.$message.warning('请输入联系方式');
          return;
        }
        let reg = /^[1][3|4|5|6|7|8|9][0-9]{9}$/;
        if (!reg.test(value)) {
          self.$message.warning('请输入正确的联系方式');
          return;
        }
      }
      self.$confirm({
        title: '是否保存已修改的数据?',
        okText: '确定',
        cancelText: '取消',
        onOk () {
          let map = {};
          if (field == 'tel') {
            map.tel = value;
          } else if (field == 'mail') {
            map.mail = value;
          } else {
            map.address = value;
          }
          updateHomepageEmployee(map).then(res => {
            if (field == 'tel') {
              self.editTel = false;
            } else if (field == 'mail') {
              self.editMail = false;
            } else if (field == 'address') {
              self.editAddress = false;
            }
            if (res.result_code == '1') {
              self.$message.success('操作成功');
            } else {
              self.form[field] = self.oldValue;
              self.$message.warning(res.result_msg);
            }
          });
        },
        onCancel () {
          self.form[field] = self.oldValue;
          if (field == 'tel') {
            self.editTel = false;
          } else if (field == 'mail') {
            self.editMail = false;
          } else if (field == 'address') {
            self.editAddress = false;
          }
        }
      });
    }
  }
};
</script>

<style lang="less" scoped>
  .col-item {
    display: inline-flex;
    margin-bottom: 10px;
    height: 40px;
    overflow: hidden;
    .item-label{
      width: 140px;
      height: 100%;
      line-height: 40px;
      text-align: right;
      font-size: 14px;
      padding-right: 5px;
    }
    .item-data{
      width: calc(100% - 140px);
      height: 100%;
      line-height: 40px;
    }
  }
  .base-form-model{
    width: 100%;
    height: 100%;
    border: 1px solid #6eb9ef;
    padding: 22px 20px 10px 10px;
    border-radius: 5px;
    margin-top: 10px;
    display: flex;
    .label{
      width: calc(100% - 30px);
      padding: 0 11px;
      border: 1px solid #c05d1b33;
      border-radius: 4px;
      line-height: 30px;
      margin: 4px 0;
      overflow: hidden;
      height: 30px;
    }
    .left-model{
      width: 100%;
      height: 100%;
    }
    .top-divider{
      position: absolute;
      top: -12px;
      left: calc(5% - 8px);
      font-weight: 500;
      font-size: 16px;
      padding: 0 10px;
    }
    :deep(.ant-divider::before), :deep(.ant-divider::after) {
      border-top: 1px solid #6eb9ef;
    }
    .edit-icon .ant-btn{
      background: unset;
      padding: 0;
      border: 0;
      box-shadow: 0 0 black;
      :deep(.anticon-edit) {
        font-size: 16px;
        color: #5fbbff;
        }
      }
    }
</style>
