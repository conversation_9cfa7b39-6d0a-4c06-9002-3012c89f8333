<!-- 数据角色数-部门、电站拆分 -->
<template>
  <a-row :gutter="24" class="row-div">
    <a-col :span="6">
      <div class="search-item">
        <span class="search-label">电站类型</span>
        <a-select
          v-model="params.psCategoryLabels"
          allowClear
          placeholder="请选择电站类型"
          mode="multiple"
          :maxTagCount="1"
          :maxTagTextLength="32"
        >
          <a-select-option value="集中式光伏">集中式光伏</a-select-option>
          <a-select-option value="工商业光伏">工商业光伏</a-select-option>
          <a-select-option value="户用光伏">户用光伏</a-select-option>
        </a-select>
      </div>
    </a-col>
    <a-col :span="6">
      <div class="search-item">
        <span class="search-label">所属部门</span>
        <a-tree-select
          v-model="params.depCodes"
          placeholder="请选择所属部门"
          :tree-data="treeData"
          :replaceFields="{ title: 'name', key: 'id', value: 'code' }"
          allow-clear
          :treeCheckStrictly="true"
          search-placeholder="请选择数据权限"
          :dropdown-style="{ maxHeight: '400px', overflow: 'auto' }"
          treeNodeFilterProp="title"
          @change="depCodesChange"
        />
      </div>
    </a-col>
    <a-col :span="6">
      <div class="search-item">
        <span class="search-label">业主单位</span>
        <a-select
          v-model="params.ownerId"
          showSearch
          :filter-option="filterOption"
          placeholder="请选择业主单位"
          style="width: 100%; height: 32px"
          allowClear
          @change="ownerChange"
        >
          <a-select-option v-for="item in ownerOptions" :key="item.id" :value="item.id">
            {{ item.ownerName }}
          </a-select-option>
        </a-select>
      </div>
    </a-col>
    <template v-if="toggleSearchStatus">
      <a-col :span="6">
        <div class="search-item">
          <span class="search-label">项目公司</span>
          <a-select
            v-model="params.ownerProjectId"
            showSearch
            :filter-option="filterOption"
            placeholder="请选择项目公司"
            style="width: 100%; height: 32px"
            allowClear
            @change="projectChange"
          >
            <a-select-option v-for="item in projectOptions" :key="item.id" :value="item.id">
              {{ item.projectCompany }}
            </a-select-option>
          </a-select>
        </div>
      </a-col>
      <a-col :span="6">
        <div class="search-item">
          <span class="search-label">项目名称</span>
          <a-select
            v-model="params.treePsaIds"
            @dropdownVisibleChange="psaSearch('')"
            allowClear
            mode="multiple"
            :maxTagCount="1"
            :maxTagTextLength="24"
            show-search
            placeholder="输入项目名称搜索"
            :filter-option="false"
            @search="psaSearch"
            class="psa-select-multiple"
            dropdownClassName="psa-dropdown"
            notFoundContent="暂无数据"
          >
            <template slot="dropdownRender" slot-scope="menu">
              <v-nodes :vnodes="menu" />
              <template v-if="showMorePsa">
                <a-divider style="margin: 4px 0" />
                <div style="padding: 4px 8px; text-align: right" @mousedown="(e) => e.preventDefault()">
                  <a-button type="link" :loading="moreLoading" :disabled="moreLoading" @click="loadMorePsa()"
                    >更多
                  </a-button>
                </div>
              </template>
            </template>
            <a-select-option v-for="item in psaOptions" :key="item.id" :value="item.id" :title="item.name"
              >{{ item.name }}
            </a-select-option>
          </a-select>
        </div>
      </a-col>
      <a-col :span="6">
        <div class="search-item">
          <span class="search-label">所属地区</span>
          <g-cascader
            @change="changeByArea"
            :options="options"
            v-model="params.areaList"
            class="area-select"
            placeholder="全国"
          />
        </div>
      </a-col>
      <a-col :span="6">
        <div class="search-item">
          <span class="search-label">运维商</span>
          <a-select
            v-model="params.deptIds"
            @change="deptIdsChange"
            allowClear
            showSearch
            placeholder="请选择运维商"
            mode="multiple"
            :filter-option="filterOption"
            :maxTagCount="1"
            :maxTagTextLength="32"
          >
            <a-select-option
              v-for="data in maintenance"
              :key="data.deptId"
              :value="data.deptId"
              :title="data.maintenance"
            >
              {{ data.maintenance }}
            </a-select-option>
          </a-select>
        </div>
      </a-col>
      <a-col :span="6">
        <div class="search-item">
          <span class="search-label">运维开始时间</span>
          <a-range-picker v-model="params.times" format="YYYY-MM-DD" allowClear @change="tiemsChange"></a-range-picker>
        </div>
      </a-col>
      <a-col :span="6">
        <div class="search-item">
          <span class="search-label">电站名称</span>
          <a-select
            v-model="params.psIds"
            @change="psChange"
            @dropdownVisibleChange="psSearch('')"
            allowClear
            mode="multiple"
            :maxTagCount="1"
            :maxTagTextLength="24"
            show-search
            placeholder="输入电站名称搜索"
            :filter-option="false"
            @search="psSearch"
            class="psa-select-multiple"
            dropdownClassName="psa-dropdown"
            notFoundContent="暂无数据"
          >
            <template slot="dropdownRender" slot-scope="menu">
              <v-nodes :vnodes="menu" />
              <template v-if="showMore">
                <a-divider style="margin: 4px 0" />
                <div style="padding: 4px 8px; text-align: right" @mousedown="(e) => e.preventDefault()">
                  <a-button type="link" :loading="moreLoading" :disabled="moreLoading" @click="loadMore()"
                    >更多
                  </a-button>
                </div>
              </template>
            </template>
            <a-select-option v-for="item in psOptions" :key="item.id" :value="item.id" :title="item.name"
              >{{ item.name }}
            </a-select-option>
          </a-select>
        </div>
      </a-col>
      <a-col :span="6">
        <div class="search-item">
          <span class="search-label">算法状态</span>
          <a-select v-model="params.algorithmFlag" filterable placeholder="请选择算法状态" allow-clear>
            <a-select-option value="" key="">全部</a-select-option>
            <a-select-option
              v-for="item in algorithmFlagOptions"
              :key="item.secondTypeCode"
              :label="item.secondName"
              :value="item.secondTypeCode"
            >
              {{ item.secondName }}
            </a-select-option>
          </a-select>
        </div>
      </a-col>
      <a-col :span="6">
        <div class="search-item">
          <span class="search-label">电量保证类型</span>
          <a-select
            mode="multiple"
            :maxTagCount="1"
            v-model="params.elecEnsureType"
            filterable
            placeholder="请选择电量保证类型"
            default-value="0"
          >
            <a-select-option value="0" key="">全部</a-select-option>
            <a-select-option :value="item.dataValue" v-for="(item, index) in dictMap.elec_ensure_type" :key="index">
              {{ item.dataLable }}
            </a-select-option>
          </a-select>
        </div>
      </a-col>
    </template>

    <a-col :xxl="5" :xl="6" :md="8">
      <div class="search-item">
        <throttle-button label="查询" :loading="loading" @click="clickSearch" />
        <throttle-button label="重置" class="solar-eye-btn-primary-cancel" @click="resetSearch" />
        <span class="com-color m-l-8 pointer" @click="$emit('handleSearch')">
          {{ toggleSearchStatus ? '收起' : '展开' }}
          <a-icon :type="toggleSearchStatus ? 'up' : 'down'" />
        </span>
      </div>
    </a-col>
  </a-row>
</template>

<script>
import { queryDeptTree, relPsas } from '@/api/api';
import { orderMaintenance } from '@/api/operations/orderHall';
import { USER_LCA } from '@/store/mutation-types';
import {
  areaTree,
  ownerOfUserForIac,
  projectOfUserForIac,
  getPowerStations
} from '@/api/isolarErp/safetyquality/safetyRisk';
import { getPowerStationSystemCodeList } from '@/api/health/healthapi.js';
import initDict from '@/mixins/initDict';
import tableHeight from '@/mixins/tableHeightAndSearchModel';
import debounce from 'lodash/debounce';
import moment from 'moment';

export default {
  mixins: [initDict, tableHeight],
  props: {
    loading: {
      type: Boolean,
      default: false
    },
    toggleSearchStatus: {
      type: Boolean,
      default: false
    }
  },
  components: {
    VNodes: {
      functional: true,
      render: (h, ctx) => ctx.props.vnodes
    }
  },
  data () {
    return {
      params: {
        psCategoryLabels: [],
        depCodes: undefined,
        ownerId: undefined,
        ownerProjectId: undefined,
        treePsaIds: [],
        areaList: [],
        province: undefined,
        city: undefined,
        counties: undefined,
        deptIds: [],
        times: [],
        startTimeOfStart: undefined,
        startTimeOfEnd: undefined,
        psIds: [],
        algorithmFlag: undefined,
        elecEnsureType: ['0']
      },
      treeData: [],
      ownerOptions: [],
      projectOptions: [],
      psaOptions: [],
      options: [],
      maintenance: [],
      psOptions: [], // 电站选项
      showMore: false,
      showMorePsa: false,
      moreLoading: false,
      algorithmFlagOptions: [],
      // 项目查询条件
      psaQuery: {
        top: [],
        fuzz: '',
        ownerId: null,
        ownerProjectId: null,
        pageNo: 1,
        pageSize: 100,
        types: ['2', '5'],
        statusList: ['2', '3']
      },
      // 电站查询条件
      query: {
        top: [],
        fuzz: '',
        pageNo: 1,
        pageSize: 100,
        types: ['1', '2', '5'],
        statusList: ['2', '3']
      }
    };
  },
  created () {
    this.loadTree();
    this.getOwners();
    this.ownerChange(null);
    this.queryArea();
    this.getOrderMaintenance();
    this.getPsList().then((res) => {
      this.psOptions = res;
      this.$forceUpdate();
    });
    this.getDictMap('elec_ensure_type');
    this.getOptionsData();
  },
  watch: {},
  methods: {
    moment,
    // 部门加载
    loadTree () {
      this.treeData = [];
      // 0表示不显示关闭的，1表示显示
      queryDeptTree({ show: '1' })
        .then((res) => {
          let result = Array.isArray(res.result) ? res.result : [res.result];
          this.treeData = result;
          if (result.length == 1) {
            this.params.depCodes = result[0].code;
          }
          this.clickSearch();
        })
        .catch(() => {
          this.clickSearch();
        });
    },
    // 所属部门值变化事件
    depCodesChange (val) {
      if (val && this.params.deptIds.length > 0) {
        this.$message.destroy();
        this.$message.warning('请注意所属部门和运维商不能同时选择');
      }
    },
    // 获取 业主单位-下拉项数据
    getOwners () {
      ownerOfUserForIac({ defaultValueControlByWeb: true }).then((res) => {
        this.ownerOptions = res.payload || [];
      });
    },
    // 根据业主获取项目公司、项目名称选项
    ownerChange (val = null) {
      Object.assign(this.params, {
        ownerProjectId: undefined, // 项目公司
        treePsaIds: []
      });
      projectOfUserForIac({ ownerId: val, defaultValueControlByWeb: true }).then((res) => {
        this.projectOptions = res.payload || [];
      });
      Object.assign(this.psaQuery, {
        pageNo: 1,
        fuzz: ''
      });
      this.getPsaList().then((res) => {
        this.psaOptions = res;
        this.$forceUpdate();
      });
    },
    // 项目公司下拉changge事件，根据项目公司更新电站数据
    projectChange (val) {
      Object.assign(this.params, {
        treePsaIds: []
      });
      Object.assign(this.psaQuery, {
        pageNo: 1,
        fuzz: ''
      });
      this.getPsaList().then((res) => {
        this.psaOptions = res;
        this.$forceUpdate();
      });
    },
    // 获取 项目名称-下拉项数据
    getPsaList () {
      this.moreLoading = true;
      const { ownerId, ownerProjectId, treePsaIds } = this.params;
      let lca = Vue.ls.get(USER_LCA) || {};
      Object.assign(this.psaQuery, {
        deptCode: lca.code,
        ownerId,
        ownerProjectId,
        top: treePsaIds.filter((id) => !!id).join(',')
      });
      return new Promise((resolve) => {
        relPsas(this.psaQuery)
          .then((res) => {
            let result = res.result || {};
            let list = Array.isArray(result.t2) ? result.t2 : [];
            this.showMorePsa = !!result.t1;
            resolve(list);
            this.moreLoading = false;
          })
          .catch(() => {
            this.showMorePsa = false;
            this.moreLoading = false;
            resolve([]);
          });
      });
    },
    // 项目名称筛选
    psaSearch: debounce(function (input) {
      if (this.psaQuery.fuzz == input) {
        return;
      }
      Object.assign(this.psaQuery, {
        pageNo: 1,
        fuzz: input
      });
      this.getPsaList().then((res) => {
        this.psaOptions = res;
        this.$forceUpdate();
      });
    }, 800),
    // 项目名称加载更多
    loadMorePsa () {
      let { params, psaQuery, psaOptions } = this;
      if (psaQuery.pageNo >= 5) {
        this.$notification.info({ message: '提示', description: '试试搜索', duration: 3 });
        return;
      }
      this.psaQuery.pageNo = psaQuery.pageNo + 1;
      this.getPsaList().then((res) => {
        let list = res.filter((item) => params.treePsaIds.includes(item.psaId));
        this.psaOptions = [...psaOptions, ...list];
        this.$forceUpdate();
        this.$nextTick(() => {
          document.querySelector('.psa-dropdown .ant-select-dropdown-menu').scrollTo(0, 0);
        });
      });
    },
    // 获取 所属地区-下拉项数据
    queryArea () {
      areaTree({}).then((res) => {
        this.options = res.result_data[0].children;
      });
    },
    // 改变地区选项
    changeByArea (val) {
      this.params.province = val[0] || undefined;
      this.params.city = val[1] || undefined;
      this.params.counties = val[2] || undefined;
    },
    // 获取 运维商-下拉项数据
    getOrderMaintenance () {
      orderMaintenance({ ifHuComp: true })
        .then((res) => {
          this.maintenance = res.result_data;
        })
        .catch(() => {
          this.maintenance = [];
        });
    },
    // 运维商-下拉项数据变化事件
    deptIdsChange (val) {
      if (val.length > 0 && this.params.depCodes) {
        this.$message.destroy();
        this.$message.warning('请注意所属部门和运维商不能同时选择');
      }
    },
    // 运维时间变化事件
    tiemsChange (val) {
      let flag = val.length > 0;
      this.params.startTimeOfStart = flag ? val[0].format('YYYY-MM-DD') + ' 00:00:00' : undefined;
      this.params.startTimeOfEnd = flag ? val[1].format('YYYY-MM-DD') + ' 23:59:59' : undefined;
    },
    // 获取电站数据
    getPsList () {
      this.moreLoading = true;
      const { psIds } = this.params;
      let lca = Vue.ls.get(USER_LCA) || {};
      Object.assign(this.query, {
        deptCode: lca.code,
        top: psIds.filter((id) => !!id).join(',')
      });
      return new Promise((resolve) => {
        getPowerStations(this.query)
          .then((res) => {
            let result = res.result || {};
            let list = Array.isArray(result.t2) ? result.t2 : [];
            this.showMore = !!result.t1;
            resolve(list);
            this.moreLoading = false;
          })
          .catch(() => {
            this.showMore = false;
            this.moreLoading = false;
            resolve([]);
          });
      });
    },
    // 电站筛选
    psSearch: debounce(function (input) {
      if (this.query.fuzz == input) {
        return;
      }
      Object.assign(this.query, {
        pageNo: 1,
        fuzz: input
      });
      this.getPsList().then((res) => {
        this.psOptions = res;
        this.$forceUpdate();
      });
    }, 800),
    // 电站加载更多
    loadMore () {
      let { params, query, psOptions } = this;
      if (query.pageNo >= 5) {
        this.$notification.info({ message: '提示', description: '试试搜索', duration: 3 });
        return;
      }
      this.query.pageNo = query.pageNo + 1;
      this.getPsList().then((res) => {
        let list = res.filter((item) => params.psIds.includes(item.psId));
        this.psOptions = [...psOptions, ...list];
        this.$forceUpdate();
        this.$nextTick(() => {
          document.querySelector('.psa-dropdown .ant-select-dropdown-menu').scrollTo(0, 0);
        });
      });
    },

    // 获取 算法状态-下拉项数据
    getOptionsData () {
      getPowerStationSystemCodeList()
        .then((res) => {
          if (res.result_code == '1') {
            let data = res.result_data;
            this.algorithmFlagOptions = data.algorithmFlagList;
          } else {
            this.$message.error(res.result_msg);
          }
        })
        .catch(function () {});
    },
    // 查询
    clickSearch () {
      this.$emit('clickSearch', this.params);
    },
    resetSearch () {
      const depCodes = this.treeData && this.treeData.length === 1 ? this.treeData[0].code : undefined;
      this.params = Object.assign({}, this.$options.data().params, { depCodes });
      this.$emit('clickSearch', this.params);
    },
    // 对象数组去重
    arrUnique (arr, key) {
      let returnArr = [];
      let obj = {};
      if (arr && arr.length > 0) {
        returnArr = arr.reduce((cur, next) => {
          if (!obj[next[key]]) {
            obj[next[key]] = true && cur.push(next);
          }
          return cur;
        }, []);
      }
      return returnArr;
    },
    filterOption (input, option) {
      return option.componentOptions.children[0].text.toLowerCase().indexOf(input.toLowerCase()) >= 0;
    }
  },
  beforeDestroy () {
    this.psaOptions = [];
    this.data = null;
  }
};
</script>
<style lang="less" scoped>
.row-div {
  margin: 0 0 8px !important;
  padding-bottom: 12px !important;
  background-color: var(--zw-card-bg-color--default);
}

.search-item {
  width: 100%;
}

.role-tree-select,
.psa-select-multiple {
  width: 100%;
  height: 32px;
  overflow: hidden;
}

:deep(.ant-select-selection--multiple) {
  height: 32px;
  overflow: hidden;
}

.area-select {
  width: 100%;
  position: relative;
  top: 2px;
}
</style>
