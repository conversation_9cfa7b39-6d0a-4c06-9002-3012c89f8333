<!-- 文件上传-使用最新的UI规范，仅提供文件列表和照片墙模式，默认文件列表，可预览图片和PDF，图片采用最新的预览方式，PDF采用打开新窗口方式，不可预览的文件点击直接下载 -->
<!-- 此组件预览需要文件的path(oss的文件URL) -->
<template>
  <div class="file-upload" :style="{'--zoom': zoom}">
    <a-upload class="upload" ref="upload" action="#" :accept="accept" :customRequest="()=>{}"
    :beforeUpload="beforeUpload" :multiple="multiple" @preview="handlePreview" :showUploadList="{ showRemoveIcon : !disabled }"
    :remove="handleRemove" :file-list="fileList" :list-type="listType" v-bind:class="{'upload-disabled': isDisabled}">
      <template v-if="listType == 'picture-card'">
        <a-icon :type="loading ? 'loading' : 'plus'" v-bind:style="{'font-size': iconSize + 'px'}"/>
        <div v-if="uploadText" class="ant-upload-text com-color">{{ uploadText }}</div>
      </template>
      <template v-else>
        <a-button icon="upload" :disabled="isDisabled || loading" :loading="loading" class="solar-eye-btn-primary com-color">上传</a-button>
      </template>
      <template v-if="listType == 'text' && !disabled && tip">
        <p @click.stop="()=>{}" class="ant-upload-hint" style="color:#999;font-size: 12px;">提示：{{tip}}</p>
      </template>
    </a-upload>
    <slot v-if="!isDisabled" name="downloadExcelButton"></slot>
    <template v-if="listType == 'picture-card' && !disabled && tip">
      <span @click.stop="()=>{}" class="ant-upload-hint picture-card-tip" style="color:#999;font-size: 12px;">提示：{{tip}}</span>
    </template>
  </div>
</template>

<script>
import axios from 'axios';
import VueViewer from '@/mixins/VueViewer';
import { apiBaseUrl } from '@/utils/gy-request';
import { exportFile } from '@/api/common_gy/erp-manage.js';
import { ACCESS_TOKEN, TENANT_ID } from '@/store/mutation-types';
const review_type = ['png', 'jpg', 'jpeg', 'bmp', 'pdf'];
export default {
  mixins: [ VueViewer ],
  model: {
    prop: 'files',
    event: 'set'
  },
  props: {
    // 上传地址
    url: {
      type: String,
      default: '/sys/oss/file/upload'
    },
    // 文件列表
    files: {
      type: Array,
      default: () => {
        return [];
      }
    },
    // 是否为只读属性
    disabled: {
      type: Boolean,
      default: false
    },
    // 单个文件大小最大上限
    maxSize: {
      type: Number,
      default: 10
    },
    // 允许上传的文件数量
    maxNum: {
      type: Number,
      default: 5
    },
    // 允许上传的文件格式
    accept: {
      type: String,
      default: ''
    },
    // 提示语
    tip: {
      type: String,
      default: ''
    },
    // 是否允许重名
    isDuplicate: {
      type: Boolean,
      default: false
    },
    /*
      文件列表内建样式
      支持两种种基本样式 text 和 picture-card
    */
    listType: {
      type: String,
      default: 'text'
    },
    // 上传时文件是否可多选
    multiple: {
      type: Boolean,
      default: false
    },
    // 是否纯图片
    isAllImg: {
      type: Boolean,
      default: false
    },
    // 按钮名称
    uploadText: {
      type: String,
      default: '上传图片'
    },
    // 上传icon的fontSize
    iconSize: {
      type: Number,
      default: 30
    },
    // 照片墙模式下-图片的缩放, 范围 0.5-1，值越小图片越小
    zoom: {
      type: Number,
      default: 1
    }
  },
  watch: {
    watchfiles: {
      immediate: true,
      handler (val, old) {
        let files = Array.isArray(val) ? val.filter(file => !!file) : [];
        files.forEach((item, index) => {
          item.uid = item.uid || index;
          item.name = item.fileName || item.pathName;
          item.url = item.path;
        });
        this.fileList = files;
      }
    }
  },
  computed: {
    watchfiles () {
      return this.files;
    },
    isDisabled () {
      return this.disabled || (this.maxNum && this.fileList.length >= this.maxNum);
    }
  },
  data () {
    return {
      max: 0,
      fileList: [],
      loading: false
    };
  },
  methods: {
    /*
        获取已上传的文件名
      */
    getExistName () {
      return this.fileList.map(item => {
        return item.fileName;
      });
    },
    /*
        文件上传
      */
    beforeUpload (file) {
      let { max, url, fileList } = this;
      let maxLength = max || fileList.length;
      let _this = this;
      _this.loading = true;
      let checkResult = _this.checkFile(file);
      if (!checkResult) {
        _this.loading = false;
        return false;
      }
      _this.max = maxLength + 1;
      let fd = new FormData(); // 表单格式
      fd.append('file', file); // 添加file表单数据
      fd.append('isNeedBase', '1'); // 不需要返回base64
      axios({
        method: 'post',
        url: apiBaseUrl + url,
        data: fd,
        headers: {
          'Content-Type': 'multipart/form-data',
          'X-Access-Token': Vue.ls.get(ACCESS_TOKEN),
          'tenant_id': Vue.ls.get(TENANT_ID)
        }
      }).then((response) => {
        _this.loading = false;
        let data = response.data;
        if (response.status == '200' && data.success && data.result) {
          let result = Object.assign({}, data.result, { 'base64code': undefined, 'url': undefined, 'fileType': _this.getFileExtension(file.name) });
          _this.fileList = _this.fileList.concat([result]);
          // 上传接口回调 触发父组件 change 事件
          _this.$emit('change', data.result);
          _this.$emit('set', _this.fileList);
          // _this.$message.success('上传成功');
          _this.$notification.success({
            message: '系统提示',
            description: `${file.name}上传成功`,
            duration: 3
          });
        } else {
          _this.max -= 1;
          _this.$notification.error({
            message: '系统提示',
            description: `${file.name}上传失败`,
            duration: 3
          });
        }
      }).catch(() => {
        _this.max -= 1;
        _this.loading = false;
      });
      return false;
    },
    /*
      文件校验
    */
    checkFile (file) {
      let _this = this;
      let { max, maxNum, maxSize, isDuplicate, accept, fileList } = this;
      let maxLength = max || fileList.length;
      _this.loading = true;
      if (!file || (file && !file.size)) {
        _this.showNotification('emptyError', '禁止上传空文件!');
        return false;
      }
      if (maxNum && maxLength >= maxNum) {
        _this.showNotification('maxError', `最多上传${maxNum}个附件，请确认!`);
        return false;
      }
      if (file.size > (maxSize * 1024 * 1024)) {
        _this.showNotification('sizeError', `请上传小于${maxSize}MB文件!`);
        return false;
      }
      /*
        文件重名验证
      */
      if (!isDuplicate) {
        let fileNames = _this.getExistName();
        if (fileNames.includes(file.name)) {
          _this.$notification.error({
            message: '系统提示',
            description: `已存在${file.name}!`,
            duration: 3
          });
          return false;
        }
      }
      // 文件格式验证
      let ext = _this.getFileExtension(file.name);
      if (['exe', 'com'].includes(ext)) {
        _this.showNotification('exeError', '禁止上传安装包!');
        return false;
      }
      if (accept && accept.indexOf(ext) == -1) {
        _this.showNotification('acceptError', `请上传${accept.replaceAll('.', '')}格式文件!`);
        return false;
      }
      return true;
    },
    /*
      错误提示
    */
    showNotification (key, description) {
      this.$notification.close(key);
      this.$notification.error({
        'key': key,
        'message': '系统提示',
        'description': description,
        'duration': 3
      });
    },
    /*
      移除
    */
    handleRemove (file) {
      let self = this;
      if (self.disabled) {
        return;
      }
      let index = self.fileList.indexOf(file);
      self.fileList.splice(index, 1);
      self.$emit('set', self.fileList);
      self.max = self.fileList.length;
    },
    /*
      图片类型文件支持预览
    */
    handlePreview (file) {
      let { isAllImg, listType, fileList } = this;
      // 照片墙或纯图片
      if (isAllImg || listType == 'picture-card') {
        let index = fileList.indexOf(file);
        this.viewerImage({ 'images': fileList, 'index': index });
        return;
      }
      let ext = this.getFileExtension(file.fileName);
      if (ext == 'pdf') {
        window.open(file.path);
        return;
      }
      if (review_type.includes(ext)) {
        this.viewerImage({ 'images': [file] });
        return;
      }
      this.downLoadFile(file);
    },
    /*
        下载
      */
    downLoadFile (file) {
      let _this = this;
      let params = {
        path: file.pathName,
        fileName: file.fileName
      };
      exportFile('/sys/oss/file/downLoad', params).then((data) => {
        let fileReader = new FileReader();
        fileReader.onload = function () {
          try {
            let jsonData = JSON.parse(this.result);
            if (jsonData.code || !jsonData.result) {
              // 说明是普通对象数据，后台转换失败
              _this.$notification.error({
                'message': '系统提示',
                'description': `${file.fileName}文件下载失败!`,
                'duration': 3
              });
            }
          } catch (err) {
            if (typeof window.navigator.msSaveBlob !== 'undefined') {
              window.navigator.msSaveBlob(new Blob([data]), file.fileName);
            } else {
              let url = window.URL.createObjectURL(new Blob([data]));
              let link = document.createElement('a');
              link.style.display = 'none';
              link.href = url;
              link.setAttribute('download', file.fileName);
              document.body.appendChild(link);
              link.click();
              document.body.removeChild(link);
              window.URL.revokeObjectURL(url);
            }
            _this.$notification.success({
              'message': '系统提示',
              'description': `${file.fileName}下载成功!`,
              'duration': 3
            });
          }
        };
        fileReader.readAsText(data);
      });
    },
    /*
        获取文件扩展名
      */
    getFileExtension (fileName) {
      let extension = fileName.substring(fileName.lastIndexOf('.') + 1);
      return extension.toLocaleLowerCase();
    }
  }
};
</script>
<style lang="less" scoped>
  .file-upload{
    position: relative;
  }
  .picture-card-tip{
    position: absolute;
    left: 0;
    bottom: -10px;
  }
  :deep(.ant-upload-list-picture-card){
    zoom: var(--zoom)
  }
  :deep(.ant-upload-select-picture-card){
    zoom: var(--zoom)
  }
  :deep(.upload-disabled > .ant-upload-select){
    display: none;
  }
  :deep(.ant-upload-list-text){
    max-width: 500px;
  }
</style>
<style>
  :root[data-theme='dark'] .file-upload .ant-upload-list-item-name{
    color: #60CAFE;
  }
</style>
