<template>
  <div class="clean-analysis">
    <div class="isolar-layout">
      <a-row
        :gutter="16"
        class="dispersion-rate-analysis"
        ref="DispersionRateAnalysis"
      >
        <a-col :md="4" :sm="24">
          <Funnel @refreshData="refreshData" :allPsData="true" :onlyCleanPsFlag="true"></Funnel>
        </a-col>
        <a-col :md="20" :sm="24">
          <div class="right">
            <a-card
              :style="{height: height}"
              :title="selectedPsInfo.psName"
              :bodyStyle="{
                height: '100%',
              }"
              class="solareye-card"
            >
              <a slot="extra" href="#">
              <div class="weather-area">
                <a-popover placement="rightBottom" trigger="click">
                  <div class="weather-right-card">
                    <a-card
                      size="small"
                      :bodyStyle="{
                        padding: '4px 10px',
                        display: 'flex',
                        'align-items': 'center',
                      }"
                    >
                      <a-space class="weather-right">
                        <span>{{ weatherInfo.areaName }}</span>
                        <a-col class="weather-icon">
                          <img
                            v-if="weatherInfo.iconDir"
                            :src="weatherInfo.iconDir"
                            style="height: 30px; object-fit: contain"
                          />
                        </a-col>
                        <span
                          >{{ weatherInfo.minTemp }}℃~{{
                            weatherInfo.maxTemp
                          }}℃</span
                        >
                      </a-space>
                    </a-card>
                  </div>
                  <template slot="content">
                    <div class="weather-popper">
                      <a-table
                        :data-source="weatherInfo.fifthWeather"
                        :pagination="false"
                        :scroll="{ x: 500, y: 300 }"
                        bordered
                        size="small"
                      >
                        <a-table-column
                          width="150"
                          key="predictDate"
                          data-index="predictDate"
                          title="日期"
                          align="center"
                        ></a-table-column>
                        <a-table-column
                          width="200"
                          key="conditionDay"
                          data-index="conditionDay"
                          title="天气"
                          align="center"
                        >
                          <template slot-scope="text, record">
                            <div class="weather-row">
                              <img
                                style="
                                  height: 30px;
                                  object-fit: contain;
                                  margin-right: 5px;
                                "
                                :src="mapWeatherIconById(record.conditionIdDay)"
                              />
                              <span>{{ record.conditionDay }}</span>
                            </div>
                          </template>
                        </a-table-column>
                        <a-table-column
                          width="200"
                          key="tempDay"
                          data-index="tempDay"
                          title="气温[℃]"
                          align="center"
                        ></a-table-column>
                      </a-table>
                    </div>
                  </template>
                </a-popover>
              </div>
              </a>
              <!-- 循环展示各个主要的参数 弹性布局 -->
              <div class="base-data">
                <div class="base-data-flex">
                  <div
                    v-for="(data,index) in baseData"
                    class="base-data-detail"
                    :key="data.value + index"
                  >
                    <img
                      class="base-data-icon"
                      :src="
                        require(`@/assets/images/health/cleanAnalysisReport/${data.icon}.png`)
                      "
                    />
                    <div class="base-data-value-label">
                      <div class="base-data-value">{{ data.value }}</div>
                      <div class="base-data-label">{{ data.label }}</div>
                    </div>
                  </div>
                </div>
              </div>
              <!-- 日期框和导出按钮 -->
              <div class="search-area">
                <a-space>
                  <a-range-picker
                    :placeholder="['开始日期', '结束日期']"
                    v-model="dateRange"
                    value-format="YYYYMMDD"
                    style="width: 300px"
                    @change="dateRangeChange"
                    :disabledDate="disabledDate"
                  ></a-range-picker>
                  <a-button
                    class="solar-eye-btn-primary"
                    style="margin-left:10px"
                    @click.stop.prevent="exportData()"
                    >导出</a-button
                  >
                </a-space>
              </div>
              <div class="chart-area" :style="{height: isShowMenu ? '45vh' : '55vh'}">
                <div id="cleanAnalysisEchart" ref="chart"></div>
              </div>
              <div class="bottom-button-area">
                <div class="button-sub">
                  <a-button
                    class="solar-eye-btn-primary"
                    @click="handleCleanOver"
                    :disabled="!cleanFlag ? true : false"
                    >全站清洗结束</a-button
                  >
                </div>
                <div class="button-sub"><span>点击生成</span></div>
                <div class="button-sub">
                  <a-button
                    class="solar-eye-btn-primary"
                    :loading="loadingReport"
                    @click="clickReportClean"
                    :disabled="!reportFlag ? true : false"
                    >清洁度评估报告</a-button
                  >
                </div>
              </div>
            </a-card>
          </div>
        </a-col>
      </a-row>
    </div>
    <reportClean
      v-if="selectedPsInfo.psId && reportCleanOpen"
      ref="reportClean"
      :loadingReport.sync="loadingReport"
      :psId="selectedPsInfo.psId"
    />
  </div>
</template>

<script>
import Funnel from '@/components/health/funnel';
import reportClean from './components/reportClean.vue';
import echarts from '@/utils/enquireEchart';
import {
  queryPsCleanInfo,
  getFuture15DaysWeatherByPsId,
  downloadExcelCleanReport,
  addCleanRecord
} from '@/api/health/healthapi.js';
import moment from 'moment';
import { downloadFile } from '@/utils/util';
import store from '@/store';
let innerHeight = window.innerHeight - 60 - 40 - 24 + (store.state.user.isShowMenu ? 0 : 124);
export default {
  components: {
    Funnel,
    reportClean
  },
  name: 'CleanAnalysis',
  data () {
    return {
      reportCleanOpen: false,
      selectedPsInfo: {
        psId: '',
        psName: ''
      },
      cleanFlag: true,
      reportFlag: true,
      dateRange: [],
      baseData: [],
      weatherInfo: {},
      loadingReport: false,
      myChart: null,
      start: require('@/assets/images/health/start.png'),
      disabledDate (time) {
        return time.isAfter(new Date());
      },
      height: innerHeight > 700 ? innerHeight + 'px' : 800 + 'px'
    };
  },
  computed: {
    isShowMenu () {
      return this.$store.state.user.isShowMenu;
    }
  },
  created () {
  },
  methods: {
    hasReportClean () {
      return this.$refs.reportClean ? 'true' : 'false';
    },
    refreshData (obj) {
      this.selectedPsInfo = obj;
      this.queryData(obj);
      this.reportCleanOpen = false;
      this.$nextTick(() => {
        this.reportCleanOpen = true;
      });
    },
    queryData (obj) {
      this.getPsCleanInfo(obj);
      this.editWeatherInfo(obj);
    },
    getPsCleanInfo ({ psId }) {
      queryPsCleanInfo({
        psId,
        startDay: this.dateRange.length ? this.dateRange[0] : null,
        endDay: this.dateRange.length > 1 ? this.dateRange[1] : null
      }).then((res) => {
        if (res.result_code == 1) {
          let data = res.result_data;
          this.reportFlag = data.reportFlag;
          this.cleanFlag = data.cleanFlag;
          const editBaseData = (
            { dustLoss, cleanSuggestion, lossValue },
            baseData
          ) => {
            const placeholder = '--';
            baseData.length = 0;
            baseData.push({
              icon: 'icon_chart',
              label: '灰尘损失度',
              value: dustLoss ? `${(dustLoss * 100).toFixed(2)}%` : placeholder
            });
            baseData.push({
              icon: 'icon_loss',
              label: '预估清洗收益',
              value: lossValue ? `${lossValue}元` : placeholder
            });
            baseData.push({
              icon: 'icon_advise',
              label: '清洗建议',
              value: cleanSuggestion ? `${cleanSuggestion}` : placeholder
            });
          };
          editBaseData(data, this.baseData);
          this.drawLine(data.fifthInfoNew);
        } else {
          this.$message.error(res.result_msg);
        }
      });
    },
    editWeatherInfo ({ psId }) {
      this.weatherInfo = {};
      getFuture15DaysWeatherByPsId({
        psId
      }).then((res) => {
        if (res.result_code == 1) {
          let { city, forecast } = res.result_data;
          let fifthWeather = forecast.filter((item) => {
            return moment(item.predictDate).isSameOrAfter(new Date(), 'day');
          });
          let todayInfo = forecast.find((item) => {
            return moment(item.predictDate).isSame(new Date(), 'day');
          });

          this.weatherInfo = {
            areaName: city.name,
            iconDir: this.mapWeatherIconById(todayInfo.conditionIdDay),
            minTemp: todayInfo.tempNight,
            maxTemp: todayInfo.tempDay,
            fifthWeather
          };
        } else {
          this.$message.error(res.result_msg);
        }
      });
    },
    mapWeatherIconById (conditionId, flag) {
      if (conditionId >= 0) {
        return require(`@/assets/images/weather/W${conditionId}.png`);
      }
      return null;
    },
    getStart () {
      return require(`@/assets/images/health/start.png`);
    },
    dateRangeChange (obj) {
      // 传入日期范围 查询
      this.getPsCleanInfo(this.selectedPsInfo);
    },
    optionReturn (info) {
      let that = this;
      console.log(that.start);
      let option = {
        title: {
          text: ''
        },
        // color:['#5794f8','#7d57f8','#f8aa57','#6df857'],
        tooltip: {
          trigger: 'axis',
          formatter: function (params) {
            let result = `<div style='text-align:left'>${params[0].name}</div>`;
            for (let i = 0; i < params.length; i++) {
              let value = '0';
              value = params[i].value ? params[i].value : '--';
              result += `<div class='tooltip tooltip-100'>${params[i].marker} "灰尘损失度": ${value}% <span  style="padding-left:40px"> 天气 : ${params[i].data.weather}</span></div>`;
            }
            return result;
          }
        },
        dataZoom: [
          {
            show: true,
            realtime: true,
            start: 0,
            end: 100,
            type: 'inside',
            xAxisIndex: [0]
          }
        ],
        grid: {
          left: '3%',
          right: '4%',
          bottom: '10%',
          containLabel: true
        },
        xAxis: {
          type: 'category',
          boundaryGap: false,
          data: that.getData(info, 'day'),
          axisLine: {
            onZero: true,
            rotate: 45
          }
        },
        yAxis: [
          {
            type: 'value',
            name: '灰尘损失度（%）',
            nameTextStyle: {
              fontSize: 14,
              align: 'left'
            },
            max: function (value) {
              return value.max < 10 ? 10 : null;
            },
            axisLabel: {
              formatter: '{value}%'
            }
          }
        ],
        series: (() => {
          let series = [];
          // for (let index = 0; index < info.length; index++) {
          let obj = {
            name: ['灰尘损失度'],
            type: 'line',
            // symbol: that.isFlag[index] ? "roundRect" : "none",
            symbolSize: 10,
            smooth: true,
            // symbol: "none",
            markPoint: {
              symbol: 'image://' + that.getStart(),
              data: that.getData(info, 'cleanFlag')
            },
            markLine: {
              symbol: 'none',
              label: {
                show: false
              },
              data: [{
                name: '参考值',
                yAxis: 5,
                lineStyle: {
                  type: 'solid',
                  color: '#ff6600',
                  width: 2
                }
              }]
            },
            data: that.getData(info, 'dustLoss')
          };
          series.push(obj);
          // }
          return series;
        })()
      };
      return option;
    },
    drawLine (fifthInfo) {
      if (this.myChart) {
        this.myChart.dispose();
      }
      // 基于准备好的dom，初始化echarts实例
      this.myChart = echarts.init(
        document.getElementById('cleanAnalysisEchart')
      );
      let that = this;
      this.myChart.setOption(this.optionReturn(fifthInfo));
      if (that.myChart) {
        window.addEventListener('resize', function () {
          that.myChart.resize();
        });
      }
    },
    exportData () {
      downloadExcelCleanReport({
        psId: this.selectedPsInfo.psId,
        startDay: this.dateRange.length ? this.dateRange[0] : null,
        endDay: this.dateRange.length > 1 ? this.dateRange[1] : null
      }).then((res) => {
        if (res.result_code == 1) {
          downloadFile(res.result_data);
        }
      });
    },
    getData (fifthInfo, str) {
      // day 是x 轴，dustLoss 是损失量
      let arr = [];
      let cleanList = [];
      let dustLossList = [];
      fifthInfo.forEach((item) => {
        if (str == 'dustLoss') {
          item[str] = !isNaN(item[str])
            ? `${(item[str] * 100).toFixed(2)}`
            : '--';
          dustLossList.push({ value: item[str], weather: item.weather });
        }
        if (str == 'cleanFlag' && item[str]) {
          let dustLoss = '';
          dustLoss = !isNaN(item.dustLoss)
            ? `${(item.dustLoss * 100).toFixed(2)}`
            : '--';
          // item.dustLoss =  !isNaN(item.dustLoss) ? `${(item.dustLoss*100).toFixed(2)}` : '--'
          cleanList.push({ xAxis: item.day, yAxis: dustLoss, value: dustLoss });
        }
        arr.push(item[str]);
      });
      return str == 'cleanFlag'
        ? cleanList
        : str == 'dustLoss'
          ? dustLossList
          : arr;
    },
    clickReportClean () {
      this.$refs.reportClean.exportPdf();
    },
    handleCleanOver () {
      let that = this;
      this.$confirm({
        title: '全站清洗结束',
        content: '请确认全站清洗结束且对比组串和标准组串是否全部完成清洗？',
        okText: '确定',
        cancelText: '取消',
        onOk () {
          addCleanRecord({ psId: that.selectedPsInfo.psId }).then((res) => {
            if (res.result_code == 1) {
              that.$message.success(res.result_msg);
              that.getPsCleanInfo(that.selectedPsInfo);
            }
          });
        }
      });
    }
  }
};
</script>

<style lang="less">

.clean-analysis {
  .weather-area {
    flex-grow: 0;
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 56px;
    font-size: 16px;

    .weather-left {
      font-size: 100%;
      display: flex;
      align-items: center;
    }
    .weather-right-card {
      cursor: pointer;
    }
  }

  .base-data {
    flex-grow: 0;
    height: 51px;
    padding: 0 5%;
    display: flex;
    margin-top:16px;
    justify-content: center;

    .base-data-flex {
      width: 80%;
      display: flex;
      justify-content: space-between;

      .base-data-detail {
        display: flex;
        justify-content: center;
        align-items: center;

        .base-data-icon {
          margin-right: 10px;
          height: 40px;
          width: 40px;
        }

        .base-data-value-label {
          display: flex;
          flex-direction: column;
        }
      }
    }

    .base-data-value {
      font-size: 16px;
      font-weight: bold;
      color: var(--zw-warning-color--normal);
    }

    .base-data-label {
      font-size: 18px;
      font-weight: bold;
      white-space: nowrap;
    }
  }

  .search-area {
    flex-grow: 0;
    height: 40px;
    display: flex;
    justify-content: flex-end;
    padding-right: 32px;
    padding-top: 16px;
    // margin-bottom: -2%;
  }

  .chart-area {
    flex-grow: 1;
    width: 100%;
    height: 45vh;
    display: flex;
    justify-content: center;

    #cleanAnalysisEchart {
      width: 95%;
      height: 100%;
    }
  }

  .bottom-button-area {
    flex-grow: 0;
    // height: 32px;
    display: flex;
    align-items: center;
    padding: 0 4%;
    height: 5vh;

    .button-sub {
      height: 90%;
      font-size: 120%;
      display: flex;
      align-items: center;
      margin-right: 1%;

      :deep(.a-button) {
        font-size: 100% !important;
      }
    }
  }
}

.weather-popper {
  .weather-row {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 30px;

    image {
      height: 30px;
      margin-right: 5px;
    }
  }
}
</style>
