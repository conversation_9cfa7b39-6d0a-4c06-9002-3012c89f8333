/**
 * maxTagPlaceholder 工具函数
 * 提供常用的 maxTagPlaceholder 实现
 */

/**
 * 创建简单的数量显示占位符
 * @param {string} suffix - 后缀文本，默认为 "更多"
 * @returns {Function} maxTagPlaceholder 函数
 */
export function createCountPlaceholder(suffix = '更多') {
  return function(omittedValues) {
    return `+${omittedValues.length} ${suffix}`
  }
}

/**
 * 创建带 Tooltip 的占位符
 * @param {Object} options - 配置选项
 * @param {Function} options.getLabelByValue - 根据值获取标签的函数
 * @param {string} options.placement - Tooltip 位置
 * @param {string} options.separator - 分隔符
 * @param {Object} options.style - 自定义样式
 * @returns {Function} maxTagPlaceholder 函数
 */
export function createTooltipPlaceholder(options = {}) {
  const {
    getLabelByValue = (value) => value,
    placement = 'top',
    separator = ', ',
    style = { color: '#1890ff', cursor: 'pointer' }
  } = options

  return function(omittedValues) {
    const omittedLabels = omittedValues.map(getLabelByValue)
    const title = omittedLabels.join(separator)
    
    return this.$createElement('a-tooltip', {
      props: {
        title,
        placement,
        overlayStyle: { maxWidth: '300px' }
      }
    }, [
      this.$createElement('span', {
        style
      }, `+${omittedValues.length} 项`)
    ])
  }
}

/**
 * 创建标签样式的占位符
 * @param {Object} options - 配置选项
 * @param {string} options.color - 标签颜色
 * @param {string} options.text - 显示文本模板，{count} 会被替换为数量
 * @param {Function} options.getLabelByValue - 根据值获取标签的函数
 * @returns {Function} maxTagPlaceholder 函数
 */
export function createTagPlaceholder(options = {}) {
  const {
    color = 'processing',
    text = '+{count}',
    getLabelByValue = null
  } = options

  return function(omittedValues) {
    const displayText = text.replace('{count}', omittedValues.length)
    
    const tagProps = {
      props: { color },
      style: { margin: '2px', cursor: 'pointer' }
    }

    // 如果提供了 getLabelByValue 函数，则添加 Tooltip
    if (getLabelByValue) {
      const omittedLabels = omittedValues.map(getLabelByValue)
      const title = omittedLabels.join(', ')
      
      return this.$createElement('a-tooltip', {
        props: {
          title,
          placement: 'top',
          overlayStyle: { maxWidth: '300px' }
        }
      }, [
        this.$createElement('a-tag', tagProps, displayText)
      ])
    }

    return this.$createElement('a-tag', tagProps, displayText)
  }
}

/**
 * 创建自定义样式的占位符
 * @param {Object} options - 配置选项
 * @param {Object} options.style - 自定义样式
 * @param {string} options.text - 显示文本模板
 * @param {string} options.element - HTML 元素类型
 * @returns {Function} maxTagPlaceholder 函数
 */
export function createCustomPlaceholder(options = {}) {
  const {
    style = {
      background: 'linear-gradient(45deg, #1890ff, #722ed1)',
      color: 'white',
      padding: '2px 6px',
      borderRadius: '10px',
      fontSize: '12px',
      fontWeight: 'bold'
    },
    text = '+{count}',
    element = 'span'
  } = options

  return function(omittedValues) {
    const displayText = text.replace('{count}', omittedValues.length)
    
    return this.$createElement(element, {
      style
    }, displayText)
  }
}

/**
 * 创建分组显示的占位符
 * @param {Object} options - 配置选项
 * @param {Function} options.getGroupByValue - 根据值获取分组的函数
 * @param {Function} options.getLabelByValue - 根据值获取标签的函数
 * @returns {Function} maxTagPlaceholder 函数
 */
export function createGroupedPlaceholder(options = {}) {
  const {
    getGroupByValue = () => '其他',
    getLabelByValue = (value) => value
  } = options

  return function(omittedValues) {
    // 按分组统计
    const groups = {}
    omittedValues.forEach(value => {
      const group = getGroupByValue(value)
      if (!groups[group]) {
        groups[group] = []
      }
      groups[group].push(getLabelByValue(value))
    })

    // 构建显示内容
    const groupTexts = Object.entries(groups).map(([group, items]) => {
      return `${group}: ${items.join(', ')}`
    })

    return this.$createElement('a-tooltip', {
      props: {
        title: groupTexts.join('\n'),
        placement: 'topLeft',
        overlayStyle: { maxWidth: '400px', whiteSpace: 'pre-line' }
      }
    }, [
      this.$createElement('a-tag', {
        props: { color: 'default' },
        style: { cursor: 'pointer' }
      }, `+${omittedValues.length} 项`)
    ])
  }
}

/**
 * 使用示例：
 * 
 * // 1. 简单数量显示
 * maxTagPlaceholder: createCountPlaceholder('个用户')
 * 
 * // 2. 带 Tooltip 显示
 * maxTagPlaceholder: createTooltipPlaceholder({
 *   getLabelByValue: (value) => {
 *     const user = this.users.find(u => u.id === value)
 *     return user ? user.name : value
 *   }
 * })
 * 
 * // 3. 标签样式
 * maxTagPlaceholder: createTagPlaceholder({
 *   color: 'success',
 *   text: '还有{count}个',
 *   getLabelByValue: (value) => this.getUserName(value)
 * })
 * 
 * // 4. 自定义样式
 * maxTagPlaceholder: createCustomPlaceholder({
 *   style: { background: '#f50', color: 'white', padding: '4px 8px' },
 *   text: '隐藏{count}项'
 * })
 * 
 * // 5. 分组显示
 * maxTagPlaceholder: createGroupedPlaceholder({
 *   getGroupByValue: (value) => {
 *     const user = this.users.find(u => u.id === value)
 *     return user ? user.department : '未知部门'
 *   },
 *   getLabelByValue: (value) => {
 *     const user = this.users.find(u => u.id === value)
 *     return user ? user.name : value
 *   }
 * })
 */
