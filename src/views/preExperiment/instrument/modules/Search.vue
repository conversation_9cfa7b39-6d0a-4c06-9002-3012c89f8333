<template>
  <a-row :gutter="24">
    <a-col :xxl="4" :xl="6" :md="12">
      <div class="search-item">
        <span class="search-label">仪器型号</span>
        <a-input allow-clear v-model='queryParams.instrumentModel' placeholder='请输入' />
      </div>
    </a-col>
    <a-col :xxl="4" :xl="6" :md="12">
      <div class="search-item">
        <span class="search-label">仪器名称</span>
        <a-input allow-clear v-model='queryParams.instrumentName' placeholder='请输入' />
      </div>
    </a-col>
    <a-col :xxl="4" :xl="6" :md="12">
      <div class="search-item">
        <span class="search-label">仪器状态</span>
        <a-select v-model="queryParams.isEnable" placeholder="请选择" style="width: 100%; height: 32px" allowClear>
          <a-select-option v-for="(item,index) in statusEnum" :key="index" :value="item.value">
            {{ item.label }}
          </a-select-option>
        </a-select>
      </div>
    </a-col>
    <a-col :xxl="4" :xl="6" :md="8">
      <div class="search-item">
        <throttle-button label="查询" @click="$emit('queryData')" />
        <throttle-button label="重置" class="solar-eye-btn-primary-cancel" @click='reset'/>
      </div>
    </a-col>
  </a-row>
</template>
<script>
export default {
  inject: ['statusEnum'],
  data () {
    return {
      queryParams: {}

    };
  },
  methods: {
    reset () {
      this.queryParams = this.$options.data().queryParams;
      this.$emit('queryData');
    }
  }
};
</script>
