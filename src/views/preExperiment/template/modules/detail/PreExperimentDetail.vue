<template>
  <div class="drawer-form-com">
    <div class="drawer-form-content">
      <a-spin size="small" :spinning="loading" style="height: 100%;width: 100%">
        <div v-if='detailData.businessId' style='display: flex;flex-direction: column;gap: 24px' >
          <detail-layout :labelList="baseList" :form="detailData" title="设备信息"/>
          <div>
            <TitleItem title-lable='试验信息'/>
            <ExperimentProjectItem :projectItemData='item' v-for='(item,index) in detailData.projectList' :projectItemIndex='index' :key='index' />
          </div>
        </div>
      </a-spin>
    </div>
    <div class="drawer-form-foot" >
      <a-button type="info" :disabled="loading" @click="reset">返回</a-button>
      <a-button :loading="loading" v-has="'preTestTemplate:edit'" class="ant-btn-primary" @click="editEvent">编辑</a-button>
    </div>
    <drawer-view ref="editTemplateRef" @cancel='initDetailData' parent-id='pro-experiment-template' />
  </div>
</template>

<script>

import TitleItem from '@views/preExperiment/modules/TitleItem.vue';
import ExperimentProjectItem from '@views/preExperiment/template/modules/detail/ExperimentProjectItem.vue';
import { templateConfigDetailApi } from '@api/isolarErp/preExperiment';
import { clone } from 'xe-utils';

export default {
  components: { ExperimentProjectItem, TitleItem },
  inject: ['editFn'],
  data () {
    return {
      loading: false,
      detailData: {},
      baseList: [{
        label: '设备类型',
        key: 'deviceTypeName'
      }],
      model: {
        type: '',
        row: {}
      }
    };
  },
  // computed: {
  //   list () {
  //     const cloneData = clone(this.detailData.projectList, true);
  //     return cloneData.reverse();
  //   }
  // },
  methods: {
    reset () {
      this.$emit('cancel', false);
    },
    init (type, row) {
      this.model = { type, row };
      this.initDetailData(row);
      return '详情';
    },
    initDetailData () {
      this.loading = true;
      const { row } = this.model;
      templateConfigDetailApi({ deviceTypeId: row.deviceTypeId })
        .then(res => {
          if (res.result_code === '1') {
            this.loading = false;
            this.detailData = res.result_data;
          }
        })
        .catch(() => {
          this.loading = false;
          this.detailData = {};
        });
    },
    editEvent () {
      // 编辑需要reverse，从下到上排序
      const cloneData = clone(this.detailData, true);
      cloneData.projectList.reverse();
      this.$refs.editTemplateRef.init('2', cloneData, '/preExperiment/template/modules/PreExperimentForm');
    }
  }
};
</script>

<style lang='less' scoped>
.drawer-form-com {
  padding: 24px 0 0;

}
</style>
