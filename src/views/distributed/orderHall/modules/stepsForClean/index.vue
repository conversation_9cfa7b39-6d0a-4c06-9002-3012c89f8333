<template>
    <div class='solareye-steps'>
      <div class='left-steps'>
          <div :class="['steps-item',current == index ? 'steps-item-active':'']" v-for='(item,index) in stepsList' :key='index'>
              <div class='steps-item-container' @click='currentChange(index)'>
                  <div class='steps-item-tail'></div>
                  <div class='steps-item-icon'>
                    <span class='steps-icon'>{{index+1}}</span>
                  </div>
                  <div class='steps-item-content'>
                    <div class='steps-item-title'>{{item.title}}</div>
                  </div>
              </div>
          </div>
      </div>
      <div class='right-container'>
        <component
           v-for='(item,index) in stepsList'
           :isDisabled='isDisabled'
           v-show='index == current'
           :is="item.component"
           :componentData="formData"
           :key='item.component'
           ref='stepListRef'
           :current='current'
        />
      </div>
    </div>
</template>

<script>
import SafeTrain from '@views/distributed/orderHall/modules/stepsForClean/modules/SafeTrain.vue';
import SafeMeasure from '@views/distributed/orderHall/modules/stepsForClean/modules/SafeMeasure.vue';
import SpotSupervise from '@views/distributed/orderHall/modules/stepsForClean/modules/SpotSupervise.vue';
import SpotCheck from '@views/distributed/orderHall/modules/stepsForClean/modules/SpotCheck.vue';
import SafeReport from '@views/distributed/orderHall/modules/stepsForClean/modules/SafeReport.vue';
import { getSingleCleanDetail, insertCleanData } from '@api/distributed/orderHall';
import moment from 'moment';
import { initialItemFn } from '@views/distributed/orderHall/modules/stepsForClean/js/utils';
export default {
  components: {
    SafeTrain,
    SafeMeasure,
    SpotSupervise,
    SpotCheck,
    SafeReport
  },
  provide () {
    return {
      spotSuperviseIndex: () => this.spotSuperviseIndex
    };
  },
  props: {
    stepsList: {
      type: Array,
      default: () => ([])
    },
    formData: {
      type: Object,
      default: () => ({})
    },
    isDisabled: {
      type: Boolean,
      default: false
    }
  },
  data () {
    return {
      current: 0
    };
  },
  computed: {
    spotSuperviseIndex () {
      return this.stepsList.findIndex(item => item.component === 'SpotSupervise');
    }
  },
  methods: {
    currentChange (current) {
      if (this.current == this.spotSuperviseIndex && current != this.spotSuperviseIndex) {
        const activeKey = this.$refs.stepListRef[this.spotSuperviseIndex].activeKey;
        const { panes } = this.$refs.stepListRef[this.spotSuperviseIndex];
        const activeIndex = panes.findIndex(item => item.id == activeKey);
        const uuidArr = activeKey.split('-');
        const { editable } = panes[activeIndex];
        if (editable) {
          this.$confirm({
            title: '离开后信息将不被保存，是否暂存',
            okText: '暂存',
            cancelText: '不暂存',
            onOk: () => {
              this.$refs.stepListRef[this.spotSuperviseIndex].loading = true;
              const cleanDate = panes[activeIndex].cleanDate;
              if (!cleanDate) {
                this.$notification.error({
                  message: '系统提示',
                  description: '清洗日期不可为空'
                });
                this.$refs.stepListRef[this.spotSuperviseIndex].loading = false;
                return;
              }
              const map = panes[activeIndex];
              const newParams = Object.assign({}, map, { isCommit: 0, orderId: this.formData.id });
              if (uuidArr.length > 1) newParams.id = null;
              insertCleanData(newParams).then(res => {
                if (res.result_code === '1') {
                  this.$refs.stepListRef[this.spotSuperviseIndex].loading = false;
                  this.$message.success('保存成功');
                  panes[activeIndex] = Object.assign(panes[activeIndex], { ...panes[activeIndex], id: res.result_data.id, tabTitle: moment(panes[activeIndex].cleanDate).format('MM-DD') });
                  // 数据备份
                  // this.$refs.stepListRef[this.spotSuperviseIndex].backups = { ...panes[activeIndex], id: res.result_data.id, tabTitle: moment(panes[activeIndex].cleanDate).format('MM-DD') };
                  this.$refs.stepListRef[this.spotSuperviseIndex].activeKey = res.result_data.id;
                  this.current = current;
                }
              }).catch(() => {
                this.$refs.stepListRef[this.spotSuperviseIndex].loading = false;
              });
            },
            onCancel: async () => {
              if (uuidArr.length > 1 && panes.length > 1) {
                panes.splice(activeIndex, 1);
                const lastKey = panes[panes.length - 1].id;
                this.$refs.stepListRef[this.spotSuperviseIndex].activeKey = lastKey;
              } else if (uuidArr.length > 1 && panes.length == 1) {
                const res = await this.$refs.stepListRef[this.spotSuperviseIndex].getWeatherFn();
                const initialItem = initialItemFn({ todayWeather: res.result_data });
                panes.splice(activeIndex, 1, initialItem);
                const lastKey = panes[panes.length - 1].id;
                this.$refs.stepListRef[this.spotSuperviseIndex].activeKey = lastKey;
              } else if (uuidArr.length === 1) {
                console.log('activeKey', activeKey, activeIndex, panes[activeIndex]);
                getSingleCleanDetail({ businessesId: activeKey }).then(res => {
                  if (res.result_code === '1') {
                    panes.splice(activeIndex, 1, Object.assign({}, panes[activeIndex], res.result_data || {}));
                  }
                });
              }
              this.current = current;
            }

          });
        } else {
          this.current = current;
        }
      } else {
        this.current = current;
      }
    },
    async loopValidateForm () {
      let valid;
      for (let i = 0; i < this.stepsList.length; i++) {
        const itemRef = this.$refs.stepListRef[i];
        valid = (itemRef.validateForm && itemRef.validateForm());
        if (!valid) { this.current = i; break; }
      }
      return valid;
    },
    loopFormData () {
      return this.stepsList.reduce((acc, cur, index) => {
        const itemRef = this.$refs.stepListRef[index];
        Object.assign(acc, itemRef.form);
        if (itemRef.panes) Object.assign(acc, { cleanLogs: itemRef.panes });
        return acc;
      }, {});
    },
    // 暂存校验现场执行与监督 清洗日志日期必填
    // 暂存日志规则：左侧导航栏为执行日志且当前日志处于编辑状态才暂存日志
    async callChildStaging () {
      const { panes, activeKey } = this.$refs.stepListRef[this.spotSuperviseIndex];
      const activePaneIndex = panes.findIndex(item => item.id === activeKey);
      const isCommit = panes[activePaneIndex] && panes[activePaneIndex].isCommit;
      if (this.spotSuperviseIndex === this.current && !isCommit) {
        const res = await this.$refs.stepListRef[this.spotSuperviseIndex].callGrandsontaging();
        return res;
      }
      return { result_code: '1' };
    },
    setStepsCurrentToSpotSupervise () {
      this.current = this.spotSuperviseIndex;
    }
  }
};

</script>

<style lang='less' scoped>
  .solareye-steps{
    display: flex;
    padding-left: 16px;
    .left-steps{
      width: 205px;
      border-right: 1px solid #E8E8E8;
      box-sizing: border-box;
      margin: 0;
      padding: 0;
      color: #666666;
      font-size: 16px;
      line-height: 1.5;
      list-style: none;
      font-family: PingFangSC-Regular, PingFang SC;
      font-weight: 400;
      display: flex;
      display: block;

      .steps-item{
        overflow: visible;
        position: relative;
        flex: 1;
        vertical-align: top;

         .steps-item-container {
          outline: none;
           cursor: pointer;
          .steps-item-tail{
            position: absolute;
            top: 0;
            left: 16px;
            width: 1px;
            height: 100%;
            padding: 38px 0 6px;
            display: inline-block;
            &:after{
              width: 1px;
              height: 100%;
              background-color: var(--zw-primary-color--default);
              display: inline-block;
              border-radius: 1px;
              transition: background .3s;
              content: "";
            }
          }
          .steps-item-icon{
            float: left;
            margin-right: 16px;
            background: var(--zw-primary-color--default);
            border-color: var(--zw-primary-color--default);
            width: 32px;
            height: 32px;
            margin-right: 8px;
            font-size: 16px;
            line-height: 32px;
            text-align: center;
            border-radius: 32px;
            transition: background-color .3s,border-color .3s;
            .steps-icon{
              color: #fff;
              position: relative;
              line-height: 1;
            }
          }
          .steps-item-content {
            display: block;
            min-height: 48px;
            overflow: hidden;
            vertical-align: top;
            .steps-item-title{
              position: relative;
              display: inline-block;
              padding-right: 16px;
              font-size: 16px;
              line-height: 32px;
            }
          }
        }
      }
    }
    :deep(.right-container){
      //flex: 1;
      width: calc(100% - 205px);
      padding-left: 40px;
      .item-content-title{
        font-size: 15px;
        font-weight: 550;
        line-height: 26px;
        //color: #222222;
        //margin-bottom: 24px;
        display: inline-block;
      }
      .mt-24{
        margin-top: 24px;
      }
      .require-none{
        .ant-form-item-required:before{
          content:''
        }
      }
      .ant-form-item-children{
        display: inline-block;
        width:100%;
        height: 100%;
        .ant-upload.ant-upload-select-picture-card{
          //margin-bottom: 0px;
          .ant-upload-text{
            font-size: 12px;
          }
        }

      }
      .example-img-flex{
          .ant-form-item-children{
            display: flex;
          }
      }

    }
  }
  .solareye-steps{
    .left-steps{
      .steps-item:not(:last-child) {
        min-height: 80px;
        .steps-item-container .steps-item-tail {
          display: block;
        }
      }
      .steps-item:last-child .steps-item-container .steps-item-tail {
        display: none;
      }
      .steps-item{
        &.steps-item-active{
          .steps-item-container{
            .steps-item-content{
              .steps-item-title{
                font-weight: bold;
                color: #222222;
              }
            }
          }
        }

      }
    }
  }
  :root[data-theme='dark'] {
    .solareye-steps {
      .left-steps {
        border-right-color: #364457;

        .steps-item {
          &.steps-item-active {
            .steps-item-container {
              .steps-item-content {
                .steps-item-title {
                  font-weight: bold;
                  color: #fff !important;
                }
              }
            }
          }

          .steps-item-icon, .steps-item-tail:after {
            background-color: #267DCF !important;
          }
        }
      }
    }

    .right-container {
      :deep(.item-content-title), :deep(.execute-title) {
        color: #fff !important;
      }

      :deep(.ant-tabs.ant-tabs-card ) {
        .ant-tabs-bar {
          border-bottom: none !important;
        }

        .ant-tabs-bar .ant-tabs-tab {
          border: 1px solid #fff !important;
        }

        .ant-tabs-bar .ant-tabs-tab-active {
          color: #267DCF;
          border: 1px solid #267DCF !important;
        }
      }

      :deep(.ant-progress) {
        .ant-progress-inner {
          background-color: #4a5a72 !important;

          .ant-progress-bg {
            background-color: #267DCF !important;
          }
        }

        .ant-progress-text {
          color: #fff;
        }
      }

      :deep(.ant-btn) {
        .anticon-export {
          color: rgba(0, 0, 0, 0.65);
        }

        &:hover {
          .anticon-export {
            color: #4b9adb;
          }
        }
      }

      :deep(.ant-btn-link) {
        color: #267DCF !important;

        .anticon-form, .anticon-file-text {
          color: #267DCF !important;
        }
      }
    }
  }
</style>
