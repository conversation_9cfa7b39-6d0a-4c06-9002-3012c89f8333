<template>
  <div class="screen-page">
    <a-spin :spinning="loading">
      <div class="solar_eye_logo" @click="toNewPage()">
        <div class="left-icon-group">
          <div class="left-icon" style="border-color: rgba(255,255,255,0.6);"></div>
          <div class="left-icon" style="border-color: rgba(255,255,255,0.4);"></div>
          <div class="left-icon" style="border-color: rgba(255,255,255,0.2);"></div>
        </div>
        <template v-if="userInfo.userType==9&& userInfo.companyId==22">
           <img src="../../assets/images/screen/jd_logo.png"  />
        </template>
         <img src="../../assets/images/screen/SolarEye.png" v-else />
      </div>
      <div id="timeModel" class="right-time-model">
        <span class="now-time">{{nowTime}}</span>
        <br />
        <span class="now-day">{{nowDay}}</span>
      </div>
      <div class="top-icon"></div>
      <!-- 总发电量、装机容量 -->
      <div class="all-elec-power">
        <div class="all-elec">
          <div class="data">{{allElec.value}}</div>
          <div class="unit">年发电量&nbsp;/&nbsp;{{allElec.unit}}</div>
        </div>
        <div class="all-power">
          <div class="data">{{allPower.value}}</div>
          <div class="unit">运维容量&nbsp;/&nbsp;{{allPower.unit}}</div>
        </div>
      </div>
      <div class="amap-model">
        <!-- 地图 -->
        <div class="amap-div" id="screenAmap"></div>
        <!-- 搜索模块以及氮化物等统计数 -->
        <div class="search-model">
          <a-select v-model="psId" class="screen-select" dropdownClassName="screen-dropdown" show-search
            style="width: 13vw" :default-active-first-option="false" :allowClear="true" :showArrow="false"
            @select="onSelect" :filter-option="false" :not-found-content="null" @change="onChange" @search="onSearch"
            placeholder="请输入关键字搜索">
            <div slot="dropdownRender" slot-scope="menu">
              <v-nodes :vnodes="menu" />
              <a-divider v-show="psaItems.length" style="margin: 4px 0;" />
              <div v-show="psaItems.length" style="cursor: pointer;text-align: right; padding-bottom: 4px;"
                @mousedown="e => e.preventDefault()">
                <a-button :loading="loadingMore" :disabled="loadingMore" @click="onLoadMore">loading more</a-button>
              </div>
            </div>
            <a-select-option v-for="psa in refreshList" :key="psa.psId" :value="psa.psId" :title="psa.psName">
              {{ psa.psName }}
            </a-select-option>
          </a-select>
          <br />
          <card-cycle ref="environmentalCycle" />
        </div>
        <!-- 全部数据总览 -->
        <div class="all-preview" v-bind:style="{'visibility': !show ? 'visible' : 'hidden'}">
          <a-row :gutter="24">
            <!-- 日发电量 -->
            <a-col :sm="24" :md="12" :xl="12" style="position: relative;">
              <div class="all-preview-com">
                <line-model :type="0" ref="dayLine" />
              </div>
            </a-col>
            <!-- 合同发电完成率 -->
            <a-col :sm="24" :md="12" :xl="12" style="position: relative;">
              <div class="all-preview-com">
                <bar-model ref="planRateBar" title="合同发电完成率" legendName="已完成" v-if="userInfo.companyId =='1'"/>
                <bar-model ref="planRateBar" title="发电计划完成率" legendName="已完成" v-else />
              </div>
            </a-col>
          </a-row>
          <a-row :gutter="24">
            <!-- 月发电量 -->
            <a-col :sm="24" :md="12" :xl="12" style="position: relative;">
              <div class="all-preview-com">
                <line-model :type="1" ref="monthLine" />
              </div>
            </a-col>
            <!-- 等效小时数 -->
            <a-col :sm="24" :md="12" :xl="12" style="position: relative;">
              <div class="all-preview-com">
                <bar-model ref="otherBarModel" title="生产发电完成率" legendName="已完成" v-if="userInfo.companyId =='1'"/>
                <!-- <other-bar :type="1" ref="otherBarModel" v-if="tenantId =='1'" /> -->
                <bar-model ref="otherBarModel" title="等效小时数" legendName="等效小时数" v-else />
              </div>
            </a-col>
          </a-row>
          <a-row :gutter="24" style="margin-bottom: 0;">
            <!-- 年发电量 -->
            <a-col :sm="24" :md="12" :xl="12" style="position: relative;">
              <div class="all-preview-com">
                <line-model :type="2" ref="yearLine" @setTotal="setTotal" />
              </div>
            </a-col>
            <!-- 电站运行情况 -->
            <a-col :sm="24" :md="12" :xl="12" style="position: relative;">
              <div class="all-preview-com">
                <pie-model title="电站运行情况" type="1" ref="psaRunPreview" />
              </div>
            </a-col>
          </a-row>
        </div>
        <!-- 单站详情页 -->
        <psa-detail @close="closeDetail" class="psa-detail-info" v-bind:style="{'visibility':(show ? 'visible' : 'hidden')}" ref="psaDetail"/>
      </div>
    </a-spin>
  </div>
</template>

<script>
import echarts from '@/utils/enquireEchart';
import moment from 'moment';
// import otherBar from './modules/otherBar';
import barModel from './modules/barModel';
import pieModel from './modules/pieModel';
import cardCycle from './modules/cardCycle';
import lineModel from './modules/lineModel';
import psaDetail from './modules/psaDetail';
import { USER_INFO } from '@/store/mutation-types';
import { getAllRouteInfo, getAllStationInfo, getSumScale, getTenant22Data, getDMData } from '@/api/screen/screen';
require('echarts-extension-amap');
const charge_gif = require('../../assets/images/screen/charge.gif');
export default {
  components: {
    cardCycle,
    pieModel,
    lineModel,
    barModel,
    // otherBar,
    psaDetail,
    VNodes: {
      functional: true,
      render: (h, ctx) => ctx.props.vnodes
    }
  },
  data () {
    return {
      psId: undefined,
      loading: false,
      allElec: {
        value: 0,
        unit: '万kWh'
      },
      allPower: {
        value: 0,
        unit: '万kW'
      },
      // 单站详情参数
      show: false,
      nowTime: '',
      timeInterval: null,
      nowDay: '',
      psaItems: [],
      refreshList: [],
      loadingMore: false,
      mapText: null,
      symbol: 'image://data:image/png;base64,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',
      allPsa: [],
      infoWindow: null,
      times: null,
      allInterval: null,
      userInfo: Vue.ls.get(USER_INFO),
      tanant22List: []
    };
  },
  computed: {
    firstMenu () {
      return this.$store.state.user.firstMenu;
    }
  },
  mounted () {
    let self = this;
    self.nowTime = moment().format('HH:mm:ss');
    self.timeInterval = setInterval(function () {
      self.nowTime = moment().format('HH:mm:ss');
      self.nowDay = moment().format('YYYY-MM-DD');
    }, 1000);
    self.nowDay = moment().format('YYYY-MM-DD');
    // 鼠标移动到指定位置后，地图恢复至初始状态
    $('.all-elec-power').mouseenter(function () {
      window.setTimeout(function () {
        self.isBackAll();
      }, 500);
    });
    // 装机容量
    self.getScale();
    // 定时任务，15分钟刷新数据
    self.intervalWork();
    self.allInterval = window.setInterval(function () {
      self.intervalWork();
    }, 1000 * 60 * 15);
    // 加载地图
    self.getAllRoute();
    window.infoClick = function (psId) {
      let psa = self.allPsa.find(item => item.psId == psId);
      psa && self.showMarker(psa);
    };
  },
  methods: {
    /*
        定时任务，15分钟刷新数据
      */
    intervalWork () {
      let self = this;
      self.$refs.dayLine && self.$refs.dayLine.getElec(echarts);
      if (self.userInfo.companyId != '1') {
        self.getTenant22Data();
      } else {
        self.getDMData();
        // self.$refs.planRateBar.initPlanFinishRate(echarts);
        self.$refs.monthLine.getElec(echarts);
        // self.$refs.otherBarModel.hoursRank(echarts);
        self.$refs.yearLine.getElec(echarts);
        self.$refs.environmentalCycle.getEnvironmental();
      }
      self.$refs.psaRunPreview.init(echarts);
      self.getScale();
    },
    /** 查询dm */
    getDMData () {
      let self = this;
      getDMData({}).then(res => {
        let dataObj = res.payload;
        let proFinish = dataObj.proElecFinishRate;
        let contract = dataObj.contractElecFinishRate;
        self.$refs.otherBarModel.initLineBar(echarts, proFinish.x, proFinish.y[0].data, proFinish.y[0].unit);
        self.$refs.planRateBar.initLineBar(echarts, contract.x, contract.y[0].data, contract.y[0].unit);
      });
    },
    /*
        租户22的查询
      */
    getTenant22Data () {
      let self = this;
      getTenant22Data({}).then(res => {
        let dataObj = res.result_data;
        let yearSumElec = dataObj.yearSumElec;
        self.setTotal(yearSumElec.totalElec);
        self.$refs.monthLine.initLineBar(echarts, dataObj.monthSumElec.x, [dataObj.monthSumElec.data.value], [ dataObj.monthSumElec.data.unit ]);
        self.$refs.yearLine.initLineBar(echarts, yearSumElec.x, [yearSumElec.yearElec.value], [yearSumElec.yearElec.unit]);
        self.$refs.otherBarModel.initLineBar(echarts, yearSumElec.x, yearSumElec.hourData.value, yearSumElec.hourData.unit);
        self.$refs.planRateBar.initLineBar(echarts, yearSumElec.x, yearSumElec.planRateData.value, yearSumElec.planRateData.unit);
        self.$refs.environmentalCycle.getEnvironmental(dataObj.environmentalData);
      });
    },
    /*
        总装机容量
      */
    getScale () {
      getSumScale({}).then(res => {
        this.allPower = res.result_data;
      }).catch(() => {
        Object.assign(this.allPower, this.$options.data().allPower);
      });
    },
    // 获取路径
    getAllRoute () {
      let _this = this;
      _this.loading = true;
      Promise.all([_this.getAllRoutePromise(), _this.getAllStationNumberPromise()]).then(() => {
        _this.drawPsPoints();
      }).catch(() => {
        _this.loading = false;
      });
    },
    /*
        获取飞线数据
      */
    getAllRoutePromise () {
      let _this = this;
      return new Promise((resolve, reject) => {
        getAllRouteInfo({}).then(res => {
          let result = res.result_code == '1' && res.result_data ? res.result_data : {};
          _this.initMap(result);
          resolve();
        }).catch(err => {
          reject(err);
        });
      });
    },
    /*
        获取电站数据
      */
    getAllStationNumberPromise () {
      this.allPsa = [];
      return new Promise((resolve, reject) => {
        getAllStationInfo({}).then(res => {
          this.resetSubData(res);
          resolve();
        }).catch(err => {
          reject(err);
        });
      });
    },
    /*
        初始化静态地图及地图相关
      */
    initMap (result) {
      if (!document.getElementById('screenAmap')) { // dom 不存在时不画
        return;
      }
      let _this = this;
      let data = _this.getMapData(result);
      let option = {
        backgroundColor: 'transparent',
        // amap地图配置
        amap: {
          resizeEnable: true,
          center: [125.397428, 39.90923],
          zooms: [4.5, 18], // 设置地图级别范围
          zoom: 4.5, // 地图视图缩放级别
          pitch: 0, // 地图俯仰角度，有效范围 0 度- 83 度
          viewMode: '3D',
          rotateEnable: true,
          pitchEnable: true,
          mapStyle: 'amap://styles/grey',
          skyColor: '#1ff0f0'
        },
        tooltip: {
          trigger: 'item',
          formatter: function (params) {
            return _this.getLabel(params);
          },
          backgroundColor: 'transparent',
          borderWidth: 0
        },
        animation: false,
        series: [
          // 流线
          {
            coordinateSystem: 'amap', // 该系列使用的坐标系是高德地图的坐标系
            type: 'lines', // 该类型用于地图上路线的绘制
            zlevel: 1, // 相当于z-index
            effect: { // 线特效的配置
              symbol: 'circle',
              symbolSize: [2, 15],
              show: true,
              period: 4, // 箭头指向速度，值越小速度越快
              trailLength: 0.1, // 特效尾迹长度[0,1]值越大，尾迹越长重
              color: new echarts.graphic.LinearGradient(0, 0, 1, 0, [{
                offset: 1,
                color: 'rgba(89, 206, 255, 0)' // 0% 处的颜色
              }, {
                offset: 0,
                color: 'rgba(89, 206, 255, 1)' // 100% 处的颜色
              }], false)
            },
            lineStyle: {
              normal: {
                color: new echarts.graphic.LinearGradient(0, 0, 1, 0, [{
                  offset: 1,
                  color: 'rgba(89, 206, 255, 0.1)' // 0% 处的颜色
                }, {
                  offset: 0,
                  color: 'rgba(89, 206, 255, 0)' // 100% 处的颜色
                }], false), // 'rgba(73, 81, 88, 0.7)',
                width: 2,
                opacity: 0.5,
                curveness: 0.3
              }
            },
            data: data.lines // self.flyLine
          },
          // 起点
          {
            name: '起点',
            zlevel: 2, // 相当于z-index
            // 使用高德地图坐标系
            coordinateSystem: 'amap',
            // 数据格式跟在 geo 坐标系上一样，每一项都是 [经度，纬度，数值大小，其它维度...]
            data: data.startScatter, // _this.getEffectScatter(),
            type: 'effectScatter',
            effectType: 'ripple',
            showEffectOn: 'render',
            rippleEffect: {
              color: 'rgba(75, 255, 228, 0.1)',
              brushType: 'fill', // stroke
              scale: 2.5,
              period: 5
            },
            symbol: 'circle',
            symbolSize: function (params) {
              return _this.getSymbolSize(params, data.goals);
            },
            itemStyle: {
              color: {
                type: 'radial',
                x: 0.5,
                y: 0.5,
                r: 0.5,
                colorStops: [{
                  offset: 0,
                  color: 'rgba(8, 173, 235, 0.1)' // 0% 处的颜色#495058
                }, {
                  offset: 1,
                  color: 'rgba(8, 173, 235, 0.2)' // 70% 处的颜色#aaffff
                }],
                global: true // 缺省为 false
              }
            },
            label: {
              normal: {
                show: true,
                formatter: function (params) {
                  return params.data.num;
                },
                fontSize: 14,
                align: 'center',
                verticalAlign: 'middle',
                color: '#40C7FF'
              }
            }
          },
          // 集团数据中心、区域数据中心(不包含省数据中心)
          {
            name: '集团中心',
            type: 'scatter',
            coordinateSystem: 'amap',
            data: data.scatter, // _this.goals.filter(item => (item.centreType != '3')),
            symbol: _this.symbol,
            symbolSize: function (val) {
              return 35;
            },
            label: {
              normal: {
                show: false
              },
              emphasis: {
                show: true,
                formatter: '{b}',
                offset: [40, 0],
                textStyle: {
                  color: '#59CEFF'
                }
              }
            },
            zlevel: 3
          },
          {
            name: '集团中心',
            zlevel: 4, // 相当于z-index
            // 使用高德地图坐标系
            coordinateSystem: 'amap',
            // 数据格式跟在 geo 坐标系上一样，每一项都是 [经度，纬度，数值大小，其它维度...]
            data: data.scatter, // _this.goals.filter(item => (item.centreType != '3')),
            type: 'effectScatter',
            effectType: 'ripple',
            showEffectOn: 'render',
            rippleEffect: {
              color: 'rgba(75, 255, 228, 0.1)',
              brushType: 'fill',
              scale: 3,
              period: 5
            },
            symbol: 'circle',
            symbolSize: 25,
            itemStyle: {
              color: 'rgba(0, 0, 0, 0)'
            },
            label: {
              normal: {
                show: false
              },
              emphasis: {
                show: true,
                formatter: '{b}',
                offset: [40, 0],
                textStyle: {
                  color: '#59CEFF'
                }
              }
            }
          }
        ]
      };
        // 不重复初始化
      let mapChart = echarts.getInstanceByDom(document.getElementById('screenAmap'));
      if (!mapChart) {
        mapChart = echarts.init(document.getElementById('screenAmap'));
        _this.$once('hook:beforeDestroy', function () {
          mapChart.clear();
          echarts.dispose(mapChart);
        });
      }
      mapChart.setOption(option, true);
      window.aMap = mapChart.getModel().getComponent('amap').getAMap();
      // echarts迁徙图绑定目标点的点击事件
      mapChart.on('click', function (params) {
        if (params.seriesType == 'effectScatter') {
          window.aMap.setZoomAndCenter(6, params.value);
        }
      });
      // 绑定滚轮事件-比例缩放时切换地图、迁徙图的层级关系
      window.aMap.on('zoomstart', function (e) {
        _this.changeMap();
      });
      window.aMap.on('zoomend', function (e) {
        _this.changeMap();
      });
      window.aMap.on('complete', function () {
        _this.loading = false;
        $('#screenAmap').css({
          'visibility': 'visible'
        });
      });
    },
    /*
        地图数据
      */
    getMapData (result) {
      let flyLine = (Array.isArray(result.flyLine) ? result.flyLine : []); // 飞线数据
      flyLine.forEach(item => {
        if (Array.isArray(item.coords) && item.coords.length && Array.isArray(item.coords[0])) {
          item.lng_lat = (item.coords[0]).join(',');
        } else {
          item.lng_lat = '';
        }
      });
      let goals = (Array.isArray(result.goals) ? result.goals : []); // 散点数据
      let has_station_goals = [];
      let no_station_goals = []; // 没有统计数据的散点坐标
      goals.forEach(item => {
        if (item.num && (Number(item.num) > 0)) {
          has_station_goals.push(item);
        } else {
          let lng_lat = (Array.isArray(item.value) ? (item.value).join(',') : '');
          no_station_goals.push(lng_lat);
        }
      });
      let lines = flyLine.filter(item => (!no_station_goals.includes(item.lng_lat)));// 去除散点统计无电站的飞线数据
      let startScatter = [];
      let items = [];
      lines.forEach(item => {
        let lanLat = item.coords[0].join(',');
        if (!items.includes(lanLat)) {
          items.push(lanLat);
        }
      });
      has_station_goals.forEach(item => {
        let lanLat = item.value.join(',');
        if (items.includes(lanLat) || (item.centreType == '3' && !items.includes(lanLat))) {
          startScatter.push(item);
        }
      });
      return {
        'lines': lines,
        'scatter': has_station_goals.filter(item => (item.centreType != '3')),
        'goals': has_station_goals,
        'startScatter': startScatter
      };
    },
    toNewPage () {
      this.$router.push({
        path: this.firstMenu.path
      }).catch(() => {
        console.log('登录跳转首页出错,这个错误从哪里来的');
      });
    },
    // 年发电量统计
    setTotal (total) {
      this.allElec = total;
    },
    // 按区域随机更改电站类型
    resetSubData (res) {
      let data = (res.result_code == '1' ? res.result_data : []);
      if (!data || !data.length) {
        return;
      }
      data.map(item => {
        // eslint-disable-next-line no-return-assign
        return item.type = 2;
      });
      this.allPsa = Object.freeze(data);
    },
    // 返回至全国地图
    isBackAll () {
      let self = this;
      let fn = () => {
        self.clearText();
        self.show = false;
        self.psId = undefined;
        self.zoom_change = true;
        self.psaItems = self.refreshList = [];
        self.infoWindow && self.infoWindow.close();
        self.$refs.psaDetail && self.$refs.psaDetail.cancel();
        window.aMap && window.aMap.setZoomAndCenter(4.5, [125.397428, 39.90923]);
        self.changeMap(4.5);
      };
      self.debounce(fn);
    },
    /*
        电站渲染
      */
    drawPsPoints () {
      let _this = this;
      let markers = this.setMarkers();
      // 自定义聚合图标
      let count = markers.length;
      let _renderClusterMarker = function (context) {
        let div = document.createElement('div');
        div.className = 'render-cluster-marker';
        let size = Math.round(30 + Math.pow(context.count / count, 1 / 5) * 20);
        div.style.width = div.style.height = size + 'px';
        div.style.borderRadius = size / 2 + 'px';
        div.innerHTML = context.count;
        div.style.lineHeight = size + 'px';
        context.marker.setOffset(new AMap.Pixel(-size / 2, -size / 2));
        context.marker.setContent(div);
      };
        // 聚合
      window.aMap.plugin(['AMap.MarkerClusterer'], function () {
        // 自定义-集合的点击事件（更改放大比例）
        let markerClusterer = new AMap.MarkerClusterer(window.aMap, markers, {
          gridSize: 80,
          renderClusterMarker: _renderClusterMarker,
          zoomOnClick: false
        });
        markerClusterer.on('click', function (cluster) {
          _this.clustererClick(cluster);
        });
      });
    },
    /*
        聚合点击事件
      */
    clustererClick (cluster) {
      let _this = this;
      let { infoWindow } = this;
      let fn = () => {
        _this.clearText();
        let zoom = window.aMap.getZoom();
        if (zoom > 17) {
          if (!infoWindow) {
            _this.infoWindow = new AMap.InfoWindow({
              isCustom: true,
              autoMove: true,
              anchor: 'middle-left',
              closeWhenClickMap: true,
              offset: new AMap.Pixel(20, 0)
            });
          }
          _this.infoWindow.setContent(_this.getInfoWindowContent(cluster));
          _this.infoWindow.open(window.aMap, [cluster.lnglat.lng, cluster.lnglat.lat]);
        } else {
          window.aMap.setZoomAndCenter(zoom + 2, [cluster.lnglat.lng, cluster.lnglat.lat]);
        }
      };
      _this.debounce(fn);
    },
    /*
        获取InfoWindow的content
      */
    getInfoWindowContent (cluster) {
      let content = '<div class="info-window-content">';
      let cluster_psa = cluster.markers.map(item => {
        return item.De.extData;
      });
      cluster_psa.forEach(psa => {
        if (psa) {
          content += `<div class="psa-name" onclick="infoClick(${psa.psId})">${psa.psName}</div>`;
        }
      });
      content += '</div>';
      return content;
    },
    // 统计气泡大小
    getSymbolSize (params, goals) {
      let max = 6000; let min = 10;
      let maxSize4Pin = 100; let minSize4Pin = 20;
      let num = 0;
      let lag = params.join(',');
      goals.forEach(item => {
        if (lag == item.value.join(',')) {
          num = item.num;
        }
      });
      num = (isNaN(num) ? 0 : Number(num));
      let a = (maxSize4Pin - minSize4Pin) / (max - min);
      let b = maxSize4Pin - a * max;
      return a * num + b * 1.2;
    },
    // 气泡悬浮效果
    getLabel (params) {
      let _this = this;
      if (params.componentSubType == 'effectScatter' && params.seriesName == '起点') {
        let data = params.data;
        let types = _this.countTypes(data.areaId, data.num);
        let html = '';
        html += '<div class="effect-scatter">';
        // 所在区域名称
        html += '<div class="effect-scatter-top">';
        html += '<div class="effect-scatter-left"></div>';
        html += `<div class="effect-scatter-name">${data.name}</div>`;
        html += '<div class="effect-scatter-right"></div>';
        html += '</div>';
        // 电站类型统计
        html += '<div class="effect-scatter-center">';
        html += '<div class="type-wind"></div>';
        html += `<div class="psa-num">${types[1]}&nbsp;&nbsp;个</div>`;
        html += '<div class="type-pv"></div>';
        html += `<div class="psa-num">${types[0]}&nbsp;&nbsp;个</div>`;
        html += '<div class="type-storage"></div>';
        html += `<div class="psa-num last-data">${types[2]}&nbsp;&nbsp;个</div>`;
        html += '</div>';
        html += '</div>';
        return html;
      }
      return '';
    },
    // 统计各类电站个数
    countTypes (areaId, total) {
      if (!areaId || !total) {
        return [0, 0, 0];
      }
      let allPsa = this.allPsa;
      let num1 = (allPsa.filter(item => (item.areaId == areaId && item.type == 0))).length;
      let num2 = (allPsa.filter(item => (item.areaId == areaId && item.type == 1))).length;
      return [Number(total) - num1 - num2, num1, num2];
    },
    // 设置电站坐标点
    setMarkers () {
      let _this = this;
      let allPsa = this.allPsa;
      let markers = allPsa.map(item => {
        let marker = new AMap.Marker({
          clickable: true,
          title: item.psName,
          content: _this.getContent(item),
          position: [item.lon, item.lat], // 位置
          extData: item
        });
          // 坐标点-电站点击事件(validFlag == '1'正常状态)
        if (item.validFlag == '1' && item.type != '0' && item.type != '1') {
          marker.on('click', function (e) {
            _this.showMarker(item);
          });
        }
        return marker;
      });
      return markers;
    },
    /*
        marker 的 content
      */
    getContent (psa) {
      let content = '';
      // 未投入使用的电站置灰
      if (psa.validFlag == '1' && psa.type == '2') {
        content = '<div class="station-com">';
      } else {
        content = '<div class="station-com station-no">';
      }
      // 风力：0，储能：1， 光伏：其他
      if (psa.type == 0) {
        content += '<div class="wind"></div>';
      } else if (psa.type == 1) {
        content += '<img class="charge-pv-gif" src="' + charge_gif + '"/>';
      } else {
        content += '<div class="pv">';
        content += '<div class="pv-img"></div>';
        content += '</div>';
      }
      content += '</div>';
      return content;
    },
    // 查看单站详情
    showMarker (marker) {
      this.$refs.psaDetail.initData(marker);
      this.show = true;
    },
    // 单站详情关闭回调事件
    closeDetail () {
      this.psId = undefined;
      this.show = false;
      this.clearText();
      this.psaItems = this.refreshList = [];
      this.infoWindow && this.infoWindow.close();
    },
    onLoadMore () {
      this.loadingMore = true;
      let { psaItems, refreshList } = this;
      if (psaItems.length) {
        let items = [...refreshList, ...psaItems.splice(0, 20)];
        items.forEach(item => {
          item.psId = (item.psId).toString();
        });
        this.refreshList = items;
      }
      this.$nextTick(() => {
        this.loadingMore = false;
      });
    },
    // 电站搜索
    onSearch (value) {
      let self = this;
      // eslint-disable-next-line no-unused-vars
      let { psaItems, allPsa, refreshList } = this;
      let fn = () => {
        psaItems = refreshList = [];
        psaItems = (!value ? [] : allPsa.filter(psa => (psa.psName.indexOf(value) != -1)));
        let items = psaItems.splice(0, 20);
        items.forEach(item => {
          item.psId = (item.psId).toString();
        });
        self.refreshList = items;
      };
      self.debounce(fn);
    },
    // 搜索选中事件-选中后自动到相应电站位置
    onSelect (value) {
      if (!value) {
        this.psaItems = this.refreshList = [];
        return;
      }
      let psa = this.allPsa.find(item => item.psId == value);
      if (psa && psa.lon && psa.lat) {
        this.setMapText(psa.psName, [psa.lon, psa.lat]);
        window.aMap.setZoomAndCenter(18, [Number(psa.lon) + 0.0004, Number(psa.lat)]);
      }
      this.psaItems = [];
      this.refreshList = psa ? [psa] : [];
    },
    /*
        设置文本标记
      */
    setMapText (psName, position) {
      let { mapText } = this;
      if (!mapText) {
        this.mapText = new AMap.Text({
          offset: new AMap.Pixel(0, -50)
        });
      }
      this.mapText.setText(psName);
      this.mapText.setPosition(position);
      window.aMap.add(this.mapText);
    },
    /*
        清除文本标记
      */
    clearText () {
      window.aMap && this.mapText && window.aMap.remove(this.mapText);
    },
    onChange (val) {
      if (!val) {
        this.psaItems = this.refreshList = [];
      }
    },
    // 迁徙图和地图坐标的隐藏显示
    changeMap (zoom = null) {
      let fn = () => {
        zoom = zoom || window.aMap.getZoom();
        if (zoom <= 5) {
          $('.amap-drags').removeClass('max-z-index');
          $('.amap-drags').addClass('min-z-index');
          $('.amap-markers').removeClass('show');
          $('.amap-markers').addClass('hide');
        } else {
          $('.amap-drags').removeClass('min-z-index');
          $('.amap-drags').addClass('max-z-index');
          $('.amap-markers').removeClass('hide');
          $('.amap-markers').addClass('show');
        }
        if (zoom <= 12) {
          this.clearText();
        }
      };
      this.debounce(fn);
    },
    /*
        防抖
      */
    debounce (fn, wait = 500) {
      if (this.times) {
        clearTimeout(this.times);
        this.times = null;
      }
      this.times = setTimeout(fn, wait);
    }
  },
  beforeDestroy () {
    this.allPsa = this.psaItems = this.refreshList = [];
    window.infoClick = undefined;
    this.times && clearInterval(this.times);
    this.allInterval && clearInterval(this.allInterval);
    this.allInterval = null;
    this.timeInterval && clearInterval(this.timeInterval);
    this.timeInterval = null;
    if (window.aMap) {
      window.aMap.clearMap();
      window.aMap.clearInfoWindow();
      window.aMap.destroy();
    }
    window.aMap = undefined;
  }
};
</script>
<style lang="less" scoped>
 @import "./screen.less";
</style>
<style type="text/css">
  .screen-dropdown {
    border: 0 !important;
  }

  .screen-dropdown::before {
    display: none !important;
  }

  .screen-dropdown::after {
    display: none !important;
  }
</style>
