import { axios } from '@/utils/erp-request';
const url = process.env.VUE_APP_Health_BASE_URL;
// const url = 'http://10.5.7.50:8085';

// 字典接口
export function getDictByListForR (data) {
  return axios({
    url: url + '/dict/getDictByListForR',
    method: 'post',
    data
  });
}
// 数据接入配置-sn查询
export function getAccessSnList (data) {
  return axios({
    url: url + '/access/getAccessSnList',
    method: 'post',
    data
  });
}
// tab表头统计
export function getAccessHeaderStatistics (data) {
  return axios({
    url: url + '/access/getAccessHeaderStatistics',
    method: 'post',
    data
  });
}
// tab1-数据接入配置-原始测点接收表-列表查询
export function getAccessPointList (data) {
  return axios({
    url: url + '/access/getAccessPointList',
    method: 'post',
    data
  });
}
// tab1-数据接入配置-原始测点接收表-编辑状态
export function updatePointAccessStatus (data) {
  return axios({
    url: url + '/access/updatePointAccessStatus',
    method: 'post',
    data
  });
}
// tab1-数据接入配置-原始测点接收表-删除
export function deleteAccessPoint (data) {
  return axios({
    url: url + '/access/deleteAccessPoint',
    method: 'post',
    data
  });
}
// tab2-测点绑定设备-列表查询
export function getAccessDeviceList (data) {
  return axios({
    url: url + '/access/getAccessDeviceList',
    method: 'post',
    data
  });
}
// tab2-测点绑定设备-批量绑定电站-单条绑定
export function accessDeviceMappingBindingForOne (data) {
  return axios({
    url: url + '/access/accessDeviceMappingBindingForOne',
    method: 'post',
    data
  });
}
// tab2-测点绑定设备-批量绑定电站-批量绑定-校验
export function checkAccessDeviceMappingBindingForMore (data) {
  return axios({
    url: url + '/access/checkAccessDeviceMappingBindingForMore',
    method: 'post',
    data
  });
}
// tab2-测点绑定设备-批量绑定电站-批量绑定
export function accessDeviceMappingBindingForMore (data) {
  return axios({
    url: url + '/access/accessDeviceMappingBindingForMore',
    method: 'post',
    data
  });
}
// tab2-测点绑定设备-批量绑定电站-批量取消绑定
export function accessDeviceMappingCancelBindingForMore (data) {
  return axios({
    url: url + '/access/accessDeviceMappingCancelBindingForMore',
    method: 'post',
    data
  });
}
// tab2-测点绑定设备-电站设备列表查询
export function getAccessDeviceMappingForPsDeviceList (data) {
  return axios({
    url: url + '/access/getAccessDeviceMappingForPsDeviceList',
    method: 'post',
    data
  });
}
// tab3-测点编码配置-列表查询
export function getConfigPointList (data) {
  return axios({
    url: url + '/access/getConfigPointList',
    method: 'post',
    data
  });
}
// tab3-测点编码配置-匹配测点编码-按测点编码绑定
export function configPointListByStrategy (data) {
  return axios({
    url: url + '/access/configPointListByStrategy',
    method: 'post',
    data
  });
}
// tab3-测点编码配置-匹配测点编码的校验
export function checkConfigPoint (data) {
  return axios({
    url: url + '/access/checkConfigPoint',
    method: 'post',
    data
  });
}
// tab3-测点编码配置-匹配测点编码-直采测点表-列表查询
export function getConfigPointListForCollect (data) {
  return axios({
    url: url + '/access/getConfigPointListForCollect',
    method: 'post',
    data
  });
}
// tab3-测点编码配置-匹配测点编码-标准测点表-列表查询
export function getConfigPointListForStandard (data) {
  return axios({
    url: url + '/access/getConfigPointListForStandard',
    method: 'post',
    data
  });
}
// tab3-测点编码配置-匹配测点编码-测点绑定
export function configPointList (data) {
  return axios({
    url: url + '/access/configPointList',
    method: 'post',
    data
  });
}
// tab4-接入数据自检-列表查询
export function getAccessSelfCheckList (data) {
  return axios({
    url: url + '/access/getAccessSelfCheckList',
    method: 'post',
    data
  });
}
// tab4-接入数据自检-提交
export function submitConfigPoint (data) {
  return axios({
    url: url + '/access/submitConfigPoint',
    method: 'post',
    data
  });
}
// tab4-接入数据自检-配置系数
export function configPointParam (data) {
  return axios({
    url: url + '/access/configPointParam',
    method: 'post',
    data
  });
}
// tab4-接入数据自检-重新配置
export function relocationConfigPoint (data) {
  return axios({
    url: url + '/access/relocationConfigPoint',
    method: 'post',
    data
  });
}
// tab1导出（所有）
export function exportAllAccessPointList (data) {
  return axios({
    url: url + '/access/exportAllAccessPointList',
    method: 'post',
    data
  });
}
// tab1导出（原）
export function exportAccessPointList (data) {
  return axios({
    url: url + '/access/exportAccessPointList',
    method: 'post',
    data
  });
}
// tab2导出
export function exportAccessDeviceList (data) {
  return axios({
    url: url + '/access/exportAccessDeviceList',
    method: 'post',
    data
  });
}
// sn页面导出
export function exportAccessSnList (data) {
  return axios({
    url: url + '/access/exportAccessSnList',
    method: 'post',
    data
  });
}
// sn 列表

export function updatePointConfigStatusByDeviceAndPointMapping (data) {
  return axios({
    url: url + '/access/updatePointConfigStatusByDeviceAndPointMapping',
    method: 'post',
    data
  });
}
// 刷新redis缓存
export function updateRedisMappingByDeviceAndPointMapping (data) {
  return axios({
    url: url + '/access/updateRedisMappingByDeviceAndPointMapping',
    method: 'post',
    data
  });
}
// 绑定关系查询
export function getAccessDeviceMappingBindingForMoreList (data) {
  return axios({
    url: url + '/access/getAccessDeviceMappingBindingForMoreList',
    method: 'post',
    data
  });
}
// 单条绑定校验
export function checkAccessDeviceMappingBindingForOne (data) {
  return axios({
    url: url + '/access/checkAccessDeviceMappingBindingForOne',
    method: 'post',
    data
  });
}
