<!-- 直采配置 - 配置测点编码 -->
<template>
  <div>
    <a-spin :spinning="loading" >
      <a-col class="solar-eye-main-content">
        <div class='test'>
          <div style="width: 60%">
              <a-form labelAlign="left">
              <a-form-item label="测点类型" :label-col="{ span: 2 }" :wrapper-col="{ span: 8 }">
                <a-select @change="handleChangePointType" :value="collectParams.pointType">
                  <a-select-option value="1">遥信</a-select-option>
                  <a-select-option value="2">遥测</a-select-option>
                </a-select>
              </a-form-item>
              <a-form-item label="直采测点表(首个设备)" class="title-color" :label-col="{ span: 24 }">
              </a-form-item>
              <a-form-item label="原测点名称" :label-col="{ span: 2 }" :wrapper-col="{ span: 8 }">
                <a-input v-model="collectParams.pointName" placeholder="输入关键词搜索" @pressEnter="enterCollect"></a-input>
              </a-form-item>
          </a-form>
          <!-- 直采测点表 -->
           <vxe-table
            :data="this.dataCollectList"
            ref="multipleTable"
            class="my-table"
            @sort-change="handleTableSortChange"
            :sort-config="{ remote: true }"
            resizable
            align="center"
            show-overflow
            highlight-hover-row
            size="small"
            @checkbox-all="onSelectChange"
            @checkbox-change="onSelectChange"
            :height="tableHeight - 200"
          >
            <vxe-table-column type="seq" :width="60" title="序号" align="center"></vxe-table-column>
               <vxe-table-column show-overflow="title" v-for="item in colunms1" :key="item.key" :field="item.key"
                  :title="item.title" :min-width="item.width || 80"
                  :fixed="item.fixed ? item.fixed : ''">
              <template v-slot:default="{row}">
                <span>{{ getLabel(row[item.key], null) }}</span>
              </template>
            </vxe-table-column>
            <vxe-table-column show-overflow="title" title="批量设备数" width="100" field="deviceCount"> </vxe-table-column>
            <vxe-table-column title="标准测点编码" :min-width="220" :resizable="false">
              <template v-slot="{ row }">
                <div
                     class='droparea'
                     @drop='endDrop($event,row)'
                     @dragenter='e => e.preventDefault()'
                     @dragover="e => e.preventDefault()"
                    >
                         <a-select
                        show-search
                        style="width: 100%"
                        v-model="row.point"
                        :default-active-first-option="false"
                        :show-arrow="false"
                        :not-found-content="null"
                        :filter-option="filterOption"
                        @change="(val,option)=>handleChange(val,option, row)"
                        allowClear
                        option-label-prop="label"
                        :disabled="row.configurationStatus==6"
                      >
                        <a-select-option v-for="d in data" :key="d.point" :label="d.point" :data-name="d.pointName">
                        {{d.pointName}} - {{ d.point }}
                        </a-select-option>
                      </a-select>
                </div>
              </template>
            </vxe-table-column>
            <vxe-table-column show-overflow="title"  title="标准测点名称" width="160" field="nameCn"> </vxe-table-column>
            <template v-slot:empty>
              <span>查询无数据</span>
            </template>
           </vxe-table>
             <page-pagination
            :pageSize="collectParams.pageSize"
            :current="collectParams.currentPage"
            :total="totalCollect"
            @size-change="sizeChangeCollect"
          />
          </div>
          <div style="width: 39%">
           <a-form labelAlign="left">
              <a-form-item label="测点类型" :label-col="{ span: 4 }" :wrapper-col="{ span: 8 }" style="visibility: hidden;">
                <a-input v-model="queryParams.pointName"></a-input>
              </a-form-item>
             <a-form-item label="标准测点表" class="title-color" :label-col="{ span: 24 }">
              </a-form-item>
              <a-form-item label="标准测点名称" :label-col="{ span: 4 }" :wrapper-col="{ span: 8 }">
                <a-input v-model="standardParams.pointName" @pressEnter="enterStandard" placeholder="输入关键词搜索"></a-input>
              </a-form-item>
           </a-form>
           <!-- 标准测点表 -->
           <a-spin :spinning="standardLoading">
          <vxe-table
            :data="this.dataStandardList"
            ref="multipleTable"
            class="my-table"
            @sort-change="handleTableSortChange"
            :sort-config="{ remote: true }"
            resizable
            align="center"
            show-overflow
            highlight-hover-row
            size="small"
            @checkbox-all="onSelectChange"
            @checkbox-change="onSelectChange"
            :height="tableHeight - 200"
          >
            <vxe-table-column type="seq" :width="80" title="序号" align="center"></vxe-table-column>
            <vxe-table-column title="标准测点编码" :width="120"  :resizable="false" class-name="fixed-right-column-120">
              <template v-slot="{ row }">
                <div :style='{width:"100px",height:"30px",cursor: "move"}'
                     @dragstart='startDrag($event,row)'
                     :draggable="true">{{ row.point }}</div>
              </template>
            </vxe-table-column>
            <vxe-table-column show-overflow="title" title="标准测点名称" min-width="120" field="nameCn"> </vxe-table-column>
            <vxe-table-column show-overflow="title" title="存储单位" width="120" field="storageUnit"> </vxe-table-column>
            <vxe-table-column show-overflow="title" title="是否必接" width="120" field="shieldFlagName"> </vxe-table-column>
            <template v-slot:empty>
              <span>查询无数据</span>
            </template>
          </vxe-table>
           <page-pagination
          :pageSize="standardParams.pageSize"
          :current="standardParams.currentPage"
          :total="totalStandard"
          @size-change="sizeChangeStandard"
          :pageSizeOptions="[10, 20, 50, 100, 500,5000]"
        />
        </a-spin>
          </div>
        </div>
        <div class="btnBox">
          <throttle-button title="取消" label='取消' style="margin-right: 20px;" class="solar-eye-btn-primary-cancel" @click="btnCancel"/>
          <throttle-button title="确认" :loading="okLoading"  label='确认' @click="btnOk"/>
        </div>
      </a-col>
    </a-spin>
  </div>
</template>
<script>
import tableHeight from '@/mixins/tableHeightAndSearchModel';
import { listMixin } from './../mixins';
import { PointCodeTab1 } from './../columns';
import {
  getConfigPointListForStandard,
  configPointList
} from '@/api/health/getPointConfig.js';
export default {
  name: 'PointMage',
  mixins: [tableHeight, listMixin],
  data () {
    return {
      queryParams: {

      },
      // 匹配测点编码-测点绑定请求参数
      configPointListParams: {
        pointType: '2',
        originalPsKeyList: [],
        accessPointMappingList: []
      },
      colunms1: PointCodeTab1,
      test: [],
      loading: false,
      data: [],
      okLoading: false
    };
  },
  created () {
  },
  computed: {},
  methods: {
    changeEvent (value, type) {
    },
    init (selectedRows, row) {
      this.standardParams.deviceType = selectedRows.deviceType;// 后续调整
      this.collectParams.originalPsKeyList = row;
      this.getCollectList();
      this.getStandardList();
      return `配置测点编码-${selectedRows.deviceTypeName}`;
    },
    startDrag (event, data) {
      event.dataTransfer.setData('point-drag', data.point);
      event.dataTransfer.setData('pointName', data.nameCn);
    },
    endDrop (event, data) {
      if (data.configurationStatus == 6) {
        return;
      }
      let point = event.dataTransfer.getData('point-drag');
      if (point) {
        data.point = point;
        data.nameCn = event.dataTransfer.getData('pointName');
      }
    },
    // 原测点名称输入框搜索
    enterCollect () {
      this.getCollectList();
    },
    // 标准测点名称输入框搜索
    enterStandard () {
      this.getStandardList();
    },
    handleChangePointType (value, event) {
      let that = this;
      this.$confirm({
        title: '提示',
        content: '已配置的测点编码还未保存，切换测点类型会导致数据丢失，您确认切换数据吗？',
        okText: '确定',
        cancelText: '取消',
        onOk: function () {
          that.collectParams.pointType = value;
          that.standardParams.pointType = value;
          that.configPointListParams.pointType = value;
          that.getCollectList();
          that.getStandardList();
        },
        onCancel: function () {
        }
      });
    },
    filterOption (input, option) {
      return (
        option.componentOptions.children[0].text.toLowerCase().indexOf(input.toLowerCase()) >= 0
      );
    },
    handleChange (value, option, row) {
      if (value) {
        row.nameCn = option.data.attrs['data-name'];
      } else {
        row.nameCn = '';
      }
      console.log(value, row);
      // this.oneBoundParams.psKeyMappingList[0].psKey = value;
    },
    // 标准测点列表数据
    getStandardList () {
      this.standardLoading = true;
      getConfigPointListForStandard(this.standardParams).then(res => {
        if (res.result_code == '1') {
          let arr = [];
          this.dataStandardList = res.result_data.pageList;
          this.dataStandardList.forEach((item) => {
            arr.push({ point: item.point, pointName: item.nameCn });
          });
          this.data = arr;
          if (res.result_data) {
            this.totalStandard = res.result_data.rowCount;
          }
        }
        this.standardLoading = false;
      }).catch(() => {
        this.standardLoading = false;
      });
    },
    // 标准测点表页面切换
    sizeChangeStandard (current, size) {
      this.standardParams.pageSize = size;
      this.standardParams.currentPage = current;
      this.getStandardList();
    },
    // 页面确认按钮
    btnOk () {
      let arrPointMappingList = [];
      let pointList = [];
      this.dataCollectList.map((item) => {
        // if (item.point) {
        arrPointMappingList.push(
          {
            'originalPointKey': item.originalPointKey,
            'originalPsKey': item.originalPsKey,
            'originalPoint': item.originalPoint,
            'point': item.point,
            'pointType': item.pointType,
            'psKey': item.psKey,
            'index': item.index
          }
        );
        // } else {
        if (!item.point) {
          if (item.pointType == this.collectParams.pointType) { // 2 遥测
            pointList.push(item.pointType);
          }
        }
        // }
      });
      this.configPointListParams.originalPsKeyList = this.collectParams.originalPsKeyList;
      this.configPointListParams.accessPointMappingList = arrPointMappingList;
      let that = this;
      this.$confirm({
        title: '提示',
        content: `您还有${pointList.length}${that.collectParams.pointType == 2 ? '条遥测' : '条遥信'}未配置，是否确认提交，并应用于所有设备？`,
        okText: '确定',
        cancelText: '取消',
        onOk: function () {
          that.okLoading = true;
          configPointList(that.configPointListParams).then(res => {
            if (res.result_code == '1') {
              // that.$emit('cancel', 1);
              that.$message.success('操作成功');
            }
            that.okLoading = false;
          }).catch(() => {
            that.okLoading = false;
          });
        },
        onCancel: function () {
        }
      });
    },
    // 页面取消按钮
    btnCancel () {
      this.$emit('cancel', 1);
    }
  }
};
</script>

<style lang='less' scoped >
.search-title-bound {
position: absolute;
font-size: 14px;
color: #3D3D3D;
margin-left: 14px;
}
.test{
  display: flex;
  justify-content: space-between;
}

.droparea:-moz-drag-over {
  border: 1px solid black;
}
.btnBox {
  text-align: center;
}
:deep(.ant-form-item) {
  margin-bottom: 8px;
  &.title-color {
    margin-top: 4px;
    label {
      font-size: 16px;
      font-weight: bold;
    }
  }
}
</style>
