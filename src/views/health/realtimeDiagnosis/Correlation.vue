<template>
  <div class="isolar-layout onLineWarning-model">
    <a-row :gutter="16" class="correlation">
      <a-col :md="4" :sm="24">
        <funnel @refreshData="refresh" :isShowIcon="false" ref="funnel" :isRealTime="true"></funnel>
          </a-col>
      <a-col :md="20" :sm="24" >
        <a-card
          :bordered="false"
          style="border-radius:0 0 4px 4px !important"
          :style="{height:cardHeight +'px'}"
          :bodyStyle="{ height: '100%' }"
          class="solareye-card"
         :title="title"
        >
        <a slot="extra" href="javascript:void(0)"><span class="detail">{{startTime}} </span> <PowerStationWeather v-if="psId" :psId="psId"/></a>
          <div class="isolar-layout-content">
            <h3 style="margin:0; padding:16px 0">设备数据联动情况&nbsp;(总数: &nbsp;&nbsp;{{chartData.length > 0 ? chartData[0].count : "--"}})</h3>
            <a-spin :spinning="loading">
              <div id="deviceCorrection" ref="deviceChart" style="width: 100%;" :style="{height: (cardHeight - 130) + 'px' }">
              </div>
            </a-spin>
          </div>
        </a-card>
        <a-card  :style="{height:cardHeight +'px'}" >
          <h3>组串数据质量情况&nbsp;</h3>
          <a-spin :spinning="abnormalDataLoading">
            <div id="abnormalData" style="widht: 100%;" :style="{height: (cardHeight - 70) + 'px' }">
            </div>
          </a-spin>
          <DeviceDetailInfo :time="time"
          :title="title"
          :psId="psId"
          :deviceDetailShow="deviceDetailShow"
          v-if="deviceDetailShow"
          :visible.sync = "deviceDetailShow"
          ref="DeviceDetailInfo" />
        </a-card>
      </a-col>
    </a-row>
  </div>
</template>
<script>
import moment from 'moment';
import {
  getLinkageByPsId,
  getAbnormalStringInfo
} from '@/api/health/healthapi.js';
import Funnel from '@/components/health/funnel';
import PowerStationWeather from '@/components/health/weather/PowerStationWeather.vue';
import echarts from '@/utils/enquireEchart';
import DeviceDetailInfo from './modules/DeviceDetailInfo.vue';
let innerHeight = window.innerHeight - 60 - 40 - 24;
export default {
  components: {
    Funnel,
    PowerStationWeather,
    DeviceDetailInfo
  },
  name: 'Correlation',
  data () {
    return {
      title: '',
      myChart: null,
      abnormalChart: null,
      psId: null,
      treePsId: null,
      treeOrgId: null,
      chartData: [],
      time: '',
      loading: true,
      deviceDetailShow: false,
      abnormalData: [],
      abnormalDataLoading: false,
      refreshTime: null,
      startTime: '',
      correlationLengd: ['已入库设备百分比', '未入库设备百分比']
    };
  },
  created () {
    this.startHappenTime = new Date();
    this.endHappenTime = new Date(); // 当前时间前一天
    this.startTime = moment(this.startHappenTime).format('YYYY-MM-DD');
    // this.startTime = '2021-02-01';
  },
  computed: {
    cardHeight () {
      return innerHeight > 700 ? (innerHeight - 16) / 2 : (800 - 16) / 2;
    }
  },
  beforeDestroy () {
    this.destoryInterval();
  },
  deactivated () {
    this.destoryInterval();
  },
  mounted () {
    window.onresize = () => {
      if (this.myChart) {
        this.myChart.resize();
      }
      if (this.abnormalChart) {
        this.abnormalChart.resize();
      }
    };
  },
  watch: {
    'psId' () {
      if (this.psId && this.$route.path === '/realtime/Correlation') {
        this.initInterval();
      } else {
        this.destoryInterval();
      }
    },
    '$route' (newRoute) {
      if (newRoute.path === '/realtime/Correlation') {
        this.initInterval();
      } else {
        this.destoryInterval();
      }
    }
  },
  methods: {
    refresh (data, flag) {
      this.psId = data.psId;
      this.title = data.psName;
      this.curPage = 1;
      if (flag) {
        this.treePsId = data.treePsId;
        this.treeOrgId = data.treeOrgId;
      } else {
        this.treePsId = data.psId;
      }
      this.getLinkage();
      this.getAbnormalStringInfo();
    },
    initInterval () {
      this.refreshTime = setInterval(() => {
        this.getLinkage();
        this.getAbnormalStringInfo();
      }, 5 * 1000);
    },
    destoryInterval () {
      if (this.refreshTime) {
        clearInterval(this.refreshTime);
        this.refreshTime = null;
      }
    },
    getareaData (flag, isAbnormal) {
      let data = [];
      let value = '';
      let arr = isAbnormal ? this.abnormalData : this.chartData;
      for (let index = 0; index < arr.length; index++) {
        switch (flag) {
          case 0:
            value = arr[index].time_stamp.split(' ')[1];
            break;
          case 1:
            value = isAbnormal
              ? (arr[index].deadValuePercent * 100).toFixed(2)
              : (arr[index].percent * 100).toFixed(2);
            break;
          case 2:
            value = isAbnormal ? (arr[index].missingDataPercent * 100).toFixed(2) : (100 - arr[index].percent * 100).toFixed(
              2);
            break;
          case 3:
            value = (arr[index].currentOverrunPercent * 100).toFixed(2);
            break;
        }
        let obj = {
          value: value
        };
        data.push(obj);
      }
      return data;
    },
    // 获取每5分钟的实时数据
    getLinkage () {
      this.loading = true;
      getLinkageByPsId({
        psId: this.psId,
        startTime: '2020/02/01',
        // startTime: this.formatDate(this.startHappenTime),
        endTime: this.formatDate(this.endHappenTime)
      }).then((res) => {
        if (res.result_code === '1') {
          this.loading = false;
          this.chartData = res.result_data.data || [];
          this.$refs.funnel.setInterruptInfo(res.result_data.interruptInfo || []);
          this.drawLine(true);
        } else {
          this.$message.error(res.result_msg);
        }
      }).catch(() => {
        this.loading = false;
      });
    },
    // 获取异常数据
    getAbnormalStringInfo () {
      this.abnormalDataLoading = true;
      getAbnormalStringInfo({
        psId: this.psId,
        // startTime: this.formatDate(new Date()),
        startTime: '2020/02/01'
      }).then((res) => {
        this.abnormalDataLoading = false;
        if (res.result_code === '1') {
          this.abnormalData = res.result_data || [];
          this.drawLine(false);
        } else {
          this.$message.error(res.result_msg);
        }
      }).catch(() => {
        this.abnormalDataLoading = false;
      });
    },
    returnColor (index) {
      let color = '';
      switch (index) {
        case 0:
          color = new echarts.graphic.LinearGradient(
            0, 0, 0, 1,
            [
              { offset: 0, color: '#8C9BFE' },
              { offset: 1, color: '#6CC1FF' }
            ]
          );
          break;
        case 1:
          color = new echarts.graphic.LinearGradient(
            0, 0, 0, 1,
            [
              { offset: 0, color: '#C2D1FE' },
              { offset: 1, color: '#c4ccfe' }
            ]
          );
          break;
      }
      return color;
    },
    drawLine (flag) {
      // 基于准备好的dom，初始化echarts实例
      if (document.getElementById('deviceCorrection') && !this.myChart) {
        this.myChart = echarts.init(
          document.getElementById('deviceCorrection')
        );
      }
      if (document.getElementById('abnormalData') && !this.abnormalChart) {
        this.abnormalChart = echarts.init(
          document.getElementById('abnormalData')
        );
      }

      let that = this;
      if (flag) {
        if (!this.myChart) {
          return;
        }
        this.myChart.setOption(
          Object.assign({},
            this.optionReturn(
              this.correlationLengd,
              this.getareaData(0),
              true
            ), {
              series: (function () {
                let series = [];
                for (let index = 0; index < that.correlationLengd.length; index++) {
                  let obj = {
                    name: that.correlationLengd[index],
                    type: 'bar',
                    stack: '总量',
                    barWdith: 32,
                    data: that.getareaData(index + 1),
                    itemStyle: {
                      normal: {
                        color: that.returnColor(index),
                        borderRadius: index == 0 ? [0, 0, 2, 2] : [2, 2, 0, 0],
                        shadowColor: 'rgba(21, 60, 104, 0.09)',
                        shadowBlur: 10
                      }
                    }
                  };
                  series.push(obj);
                }
                return series;
              })()
            }
          )

        );
        this.myChart.off('click');
        this.myChart.on('click', (params) => {
          this.deviceDetailShow = true;
          this.time = this.startTime + ' ' + params.name;
          this.$refs.DeviceDetailInfo.openDrawer();
        });
      } else {
        if (!this.abnormalChart) {
          return;
        }
        this.abnormalChart.setOption(
          Object.assign({},
            this.optionReturn(
              ['组串电流恒值', '组串电流空值', '组串电流越限'],
              this.getareaData(0, true)
            ), {
              series: [{
                name: '组串电流恒值',
                type: 'line',
                color: ['#FFDD87'],
                data: this.getareaData(1, true)
              },
              {
                name: '组串电流空值',
                type: 'line',
                color: ['#6FA7EE'],
                data: this.getareaData(2, true)
              },
              {
                name: '组串电流越限',
                type: 'line',
                color: ['#EF9CCA'],
                data: this.getareaData(3, true)
              }
              ]
            }
          )
        );
      }
    },
    optionReturn (legend, xData, legendPos) {
      let option = {
        title: {
          text: legendPos ? '' : '', // 异常组串百分比
          x: 'center',
          y: 'top',
          textAlign: 'center'
        },
        tooltip: {
          trigger: 'axis',
          alwaysShowContent: false
        },
        dataZoom: [
          {
            show: true,
            start: 0,
            end: 100,
            height: '1%',
            type: 'inside'
          }
        ],
        legend: {
          width: 800,
          top: legendPos ? 10 : 10, // 150
          left: legendPos ? 'right' : 'right',
          y: 'center', // 延Y轴居中
          x: 'right',
          orient: legendPos ? 'horizontal' : 'horizontal',
          align: 'left',
          symbolKeepAspect: true, // 缩放时保持该图形的长宽比
          textStyle: {
            color: '#000000',
            fontSize: 12,
            lineHeight: 20,
            height: 20
          },
          data: legend
        },
        grid: {
          left: '3%',
          right: '4%',
          bottom: '10%',
          containLabel: true
        },
        xAxis: {
          type: 'category',
          boundaryGap: true,
          data: xData
        },
        yAxis: [{
          type: 'value',
          name: '单位: %',
          axisLabel: {
            formatter: '{value} '
          }
        } ]
      };
      return option;
    },
    formatDate (date) {
      return moment(date).format('yyyy/MM/DD');
    }
  }
};
</script>
<style lang="less" scoped>
  .text-left {
    text-align: left;
    padding-left: 20px;
  }
  h3{
    color: var(--zw-text-3-color--default);
    font-size: 16px;
  }
</style>
