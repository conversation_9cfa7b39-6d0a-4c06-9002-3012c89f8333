<template>
  <a-modal v-model="visible" width="80vw" title="选择关联的两票" :bodyStyle="{ 'height': '570px','padding-top': 0, 'padding-bottom': 0 }"
    centered @cancel="handleCancel">
    <template #footer>
      <div style='display: flex;justify-content: flex-end'>
        <throttle-button class="solar-eye-btn-primary-cancel" label='取消' @click='handleCancel' />
        <throttle-button style='margin-left: 24px' label='确定' @click='handleOk' />
      </div>
    </template>
    <div class="solar-eye-search-model">
      <div class="solar-eye-search-content">
        <a-row :gutter="24" style="margin: 0">
           <a-col :xxl="4" :xl="8" :md="12">
             <div class="search-item">
                  <span class="search-label">票号</span>
                  <a-input v-model="searchData.ticketNum" placeholder="请输入票号" style="width: 100%;"/>
                </div>
          </a-col>
           <a-col :xxl="4" :xl="8" :md="12">
             <div class="search-item">
                  <span class="search-label">两票类型</span>
                  <a-select v-model="searchData.ticketType" allow-clear size="default" placeholder="请选择" @change="handleTicketTypeChange">
                    <a-select-option v-for="item in dictMap.ticket_type" :key="item.dataValue" :value="item.dataValue">
                      {{item.dataLable}}
                    </a-select-option>
                  </a-select>
                </div>
          </a-col>
           <a-col :xxl="4" :xl="8" :md="12">
             <div class="search-item">
                  <span class="search-label">具体类别</span>
                  <a-select v-model="searchData.ticketTypeDtl" allow-clear size="default" placeholder="请选择具体类别">
                    <a-select-option v-for="item in ticketTypeDtlList" :key="item.dataValue" :value="item.dataValue">
                      {{item.dataLable}}
                    </a-select-option>
                  </a-select>
                </div>
          </a-col>
           <a-col :xxl="4" :xl="8" :md="12">
             <div class="search-item">
                  <span class="search-label">当前环节</span>
                  <a-select v-model="searchData.handleType" allow-clear placeholder="请选择当前环节">
                  <a-select-option v-for="item in dictMap.ticket_flow_user" :title="item.dataLable" :key="item.dataValue" :value="item.dataValue">
                    {{item.dataLable}}
                  </a-select-option>
                </a-select>
                </div>
          </a-col>
           <a-col :xxl="5" :xl="8" :md="12">
                <div class="search-item">
                  <span class="search-label">填报时间</span>
                  <a-range-picker v-model="searchData.dateRanger" style="width: 100%; height: 32px" format="YYYY-MM-DD" value-format="YYYY-MM-DD"/>
                </div>
            </a-col>
          <a-col :xxl="3" :xl="6" :md="12">
              <div class="search-item">
                <throttle-button label="查询" class="solar-eye-btn-primary" @click="handleQuery"/>
                <throttle-button label="重置" class="solar-eye-btn-primary-cancel" @click="handleReset"/>
              </div>
            </a-col>
        </a-row>
      </div>
    </div>
    <a-spin :spinning="loading">
      <vxe-table ref="vxeTable" :data="dataSource" size="small" :height="450" highlight-hover-row resizable
        show-overflow="title" :seq-config="{ startIndex: (pageData.curPage - 1) * pageData.size }" class="my-table"
        align="left"  @checkbox-all="handleCheckboxAll" @checkbox-change="handleCheckboxChange"
        :checkbox-config="{ checkMethod: checkMethod, checkField:'checked',trigger: 'row' }">
        <vxe-table-column type="checkbox" :width="40"></vxe-table-column>
        <vxe-table-column type="seq" :width="60" title="序号"></vxe-table-column>
        <vxe-table-column v-for="item in ticketColumn" :key="item.field" :field="item.field" :title="item.title"
          :min-width="item.width || 80">
          <template #default="{ row }">
            <span v-if="item.field === 'ticketNo'">
              <a style="color: #1366ec;" @click="e=>handleView(row,e)">{{ row[item.field] }}</a>
            </span>
            <span v-else>{{ getLabel(row[item.field], null)  }}</span>
          </template>
        </vxe-table-column>
      </vxe-table>
      <page-pagination :pageSize="pageData.size" :current="pageData.curPage" :total="total" @size-change="sizeChange"/>
    </a-spin>
     <drawer-view ref="drawerForm" @close="handleDrawerViewCancel" @cancel="handleDrawerViewCancel" parentId="order-hall" />
  </a-modal>
</template>

<script>
import { ticketColumn } from '../../js/config';
import { getTicketPageApi } from '@/api/distributed/orderHall';
import PropTypes from 'ant-design-vue/es/_util/vue-types';
import { clone } from 'xe-utils';
import { uniqBy } from 'lodash';
export default {
  name: 'ChooseTicketModal',
  props: {
    dictMap: PropTypes.object.def({}),
    psaId: PropTypes.string.def(''),
    ticketDtoList: PropTypes.array.def([])
  },
  data () {
    return {
      visible: false,
      loading: false,
      ticketColumn,
      dataSource: [],
      pageData: { // 分页参数
        curPage: 1,
        size: 10
      },
      searchData: {
        ticketNum: undefined, // 票号
        startDate: undefined, // 开始日期
        endDate: undefined, // 结束日期
        dateRanger: [], // 日期区间
        ticketType: undefined, // 两票类型
        ticketTypeDtl: undefined, // 剧透类别
        handleType: undefined ,// 当前环节
        businessType: '5' // 业务类型：地面2, 工商业5
      },
      total: 0, // 表格数据总数
      selectedRows: [] // 所勾选的行信息集合
    };
  },

  methods: {
    init () {
      this.visible = true;
      this.selectedRows = clone(this.ticketDtoList, true);
      this.queryData(true);
    },
    handleOk () {
      // console.log('--->', uniqBy(this.selectedRows, 'ticketId'));
      const newSelectedRows = uniqBy(this.selectedRows.map(o => ({
        ...o,
        ticketId: String(o.ticketId)
      })), 'ticketId');
      if (this.$isEmpty(newSelectedRows)) {
        this.$message.warning('请选择两票');
        return;
      } else if (newSelectedRows && newSelectedRows.length > 10) {
        this.$message.warning('最多支持关联10张两票');
        return;
      }
      this.doReset();
      this.$emit('ok', uniqBy(newSelectedRows, 'ticketId'));
      this.visible = false;
    },
    handleCancel () {
      this.doReset();
      this.visible = false;
    },
    handleTicketTypeChange () {
      this.searchData.ticketTypeDtl = undefined;
    },
    formatSearchData () {
      if (this.searchData.dateRanger && this.searchData.dateRanger.length) {
        const [startDate, endDate] = this.searchData.dateRanger;
        Object.assign(this.searchData, { startDate, endDate });
      }
      return Object.assign({}, this.searchData, { psaId: this.psaId }, this.pageData);
    },
    sizeChange (curPage, size) {
      Object.assign(this.pageData, { curPage, size });
      this.queryData(true);
    },
    queryData (isNeedTopIds = false) {
      this.loading = true;
      const params = this.formatSearchData();
      Object.assign(params, { topIds: isNeedTopIds ? this.selectedRows.map(o => o.ticketId) : [] });
      getTicketPageApi(params).then(res => {
        this.dataSource = res.result_data.rows;
        this.total = res.result_data.total;
        this.setSelectedChecked();
        this.loading = false;
      }).catch(() => {
        this.loading = false;
      });
    },
    handleDrawerViewCancel () {
      this.visible = true;
    },
    handleView (row, e) {
      e.stopPropagation();
      Object.assign(row, { fromOrderHallToTicket: true, id: row.ticketId });
      this.$refs.drawerForm.init('3', row, '/distributed/ticketManage/modules/ticketForm');
      this.visible = false;
    },
    handleQuery () {
      this.pageData.curPage = 1;
      this.queryData(true);
    },
    handleReset () {
      this.doReset();
      this.queryData(true);
    },
    doReset () {
      this.pageData = this.$options.data().pageData;
      this.searchData = this.$options.data().searchData;
    },
    handleCheckboxAll ({ records, checked }, $event) {
      if (checked) {
        const arr1 = records.map(o => ({ ...o, ticketId: String(o.ticketId) }));
        const arr2 = this.selectedRows.map(o => ({ ...o, ticketId: String(o.ticketId) }));
        this.selectedRows = uniqBy([...arr1, ...arr2], 'ticketId');
      } else {
        let chooseIds = this.selectedRows.map(o => String(o.ticketId));
        this.dataSource.forEach(item => {
          if (chooseIds.includes(item.ticketId)) {
            this.selectedRows = this.selectedRows.filter(items => item.ticketId != items.ticketId);
          }
        });
      }
      console.log(this.selectedRows);
    },
    handleCheckboxChange ({ checked, row }) {
      // console.log('row--->', row);
      if (checked) {
        this.selectedRows.push(row);
      } else {
        this.selectedRows = this.selectedRows.filter(item => row.ticketId != item.ticketId);
      }
    },
    // 设置已选择的科目自动勾选
    setSelectedChecked () {
      let ticketIds = this.selectedRows.map(item => Number(item.ticketId));
      const data = clone(this.dataSource, true);
      data.forEach((item) => {
        if (ticketIds.includes(Number(item.ticketId))) {
          this.$set(item, 'checked', true);
        } else {
          this.$set(item, 'checked', false);
        }
      });
      this.dataSource = data;
    },
    checkMethod ({ row }) {
      return !!row.canRelevancy;
    }
  },
  computed: {
    ticketTypeDtlList () {
      const { ticketType } = this.searchData;
      const ticketTypeDtl = this.dictMap.ticket_type_dtl || [];
      switch (ticketType) {
        // 工作票
        case '1':
          return ticketTypeDtl.filter(item => !['6', '7', '8'].includes(item.dataValue));
          // 操作票
        case '2':
          return [];
          // 动火票工作票
        case '3':
          return (ticketTypeDtl).filter(item => ['6', '7'].includes(item.dataValue));
        default:
          return [];
      }
    }
  }
};
</script>
