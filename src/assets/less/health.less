
  //@layoutHeight: calc(100vh - 59px - 40px - 48px);
   @layoutHeight: 100%;
  .isolar-layout {
    //height: @layoutHeight;
    overflow: hidden;
    display: block;
    position: relative;
    width: 100%;
    @media screen and (max-width: 1600px)  {
      margin-bottom: 12px;
    }
     .ant-tree {
      overflow:hidden auto;
      height: 100%;
    }
    .cust-tree-node {
      width: 85%;
      display: flex;
      align-items: center;
      .show-elli {
        display: inline-block;
        text-align: left;
        width: 100%;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
        vertical-align: middle;
      }
    }
    .right {
      //float: left;
      // margin-left: 10px;
      // background-color: #ffffff;
      // border-bottom: solid 1px #c2c2c2;
      // width: calc(100% - 12.5%);
      //width: calc(86% - 10px);
      width: 100%;
      height: @layoutHeight;
      display: flex;
      flex-direction: column;
      //padding-top: 10px;
      .checkList-butt {
        display: flex;
        justify-content: space-between;
        padding-right: 10px;
        padding-left: 20px;
        width: 100%;
        .checked-list {
          padding-top: 5px;
         :deep(label) {
            font-size: 12px;
          }
          .echeckbox{
            padding-left: 20px;
          }
          span {
           
            padding-right: 0px;
            font-size: 12px;
          }
           .count{
            color: var(--zw-warning-color--normal);
          }
        }
        .batch-button{
          padding-right: 1.5vw;
        }
      }
      .search-model {
        padding-left: 10px;
        margin-bottom: 10px;
      }
      .hr {
        padding-top: 10px;
        hr {
          color: var(--zw-text-2-color--default);
        }
      }
      .page {
        position: absolute;
        right: 5px;
        bottom: 26px;
      }
    }
   
    .tree-box {
      overflow: hidden;
      display: flex;
      flex-direction: column;
      z-index: 10;
    }
    .isolar-layout-content{
      height: calc( @layoutHeight - 40px);
    }
  }
  
 
    /* 滚动条优化 end */
    .ant-table-fixed-header .ant-table-body-inner {
      overflow: hidden;
    }
    .ant-layout {
      background: var(--zw-primary-bg-color--default);
    }
    .red {
      color: var(--zw-warning-color--normal);
    }
    form .ant-cascader-picker, form .ant-select {
      width: 100%;
    }
    /** 在date-picker的面板中标记典型日 */
    .ant-calendar-date.topic-date {
      width: 24px;
      border-radius: 100%;
      background-color: var(--zw-warning-color--active);
      border: 1px solid var(--zw-warning-color--active);
      color: var(--white);
      &:hover{
        background-color: var(--zw-warning-color--hover);
      }
    }

    // 修正当日日期框偏移情况
  .ant-calendar-date.topic-date::before {
    left:0!important;
  }
    .tooltip {
      padding-right: 10px;
      display: inline-flex;
      text-align: left;
      align-items: center;
      justify-content: flex-start;
       
    }
    .tooltip-50 {
      width: 50%;
    }
    .tooltip-100 {
      width: 100%;
    }
    .health-base-param {
      padding: 10px 0.8%;
      max-height: calc(100vh - 200px);
      min-height: calc(100vh - 200px);
      margin-top: 16px;
      overflow-y: auto;
      padding: 0;
      :deep(.form-item-inject) {
        margin-bottom: 10px !important;
        label{
          font-weight: bold;
          text-align: right;
          white-space: pre-wrap;
        }
      }
      &.unique {
        margin-top: 0 ;
        max-height: calc(100vh - 124px);
        min-height: calc(100vh - 124px);
      }
    }