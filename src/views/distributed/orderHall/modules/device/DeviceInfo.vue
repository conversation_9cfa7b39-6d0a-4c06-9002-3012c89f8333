<template>
  <div v-if="deviceInfoShow">
    <DeviceInfoDetail :isDroneOrder='isDroneOrder' @openCareMenu='openCareMenu' :baseForm="formData" :dictMap='dictMap' />
  </div>
</template>

<script>
import DeviceInfoDetail from './DeviceInfoDetail';
import { ACCESS_TOKEN, TENANT_ID } from '@/store/mutation-types';

export default {
  name: 'device-info',
  components: { DeviceInfoDetail },
  props: {
    type: {
      type: String,
      required: true,
      default: () => {
        return null;
      }
    },
    formData: {
      type: Object,
      default: () => {
        return {};
      }
    },
    dictMap: {
      type: Object,
      default: () => {
        return {};
      }
    },
    isDroneOrder: {
      type: Boolean,
      default: false
    }
  },
  data () {
    return {
    };
  },
  methods: {
    // 跳转无人机菜单
    openCareMenu () {
      let menu = JSON.parse(sessionStorage.getItem('ALL_PERMISSION_MENU'));
      let env = process.env.VUE_APP_ENV;
      env = env ? (env === 'pro' ? 'www' : env) : 'fat';
      const path = env + '.isolarhealth.com/newCare';
      const isInclude = ['/solarCare', '/solarCare/patrolInsight'].every(item => menu.includes(item));
      if (isInclude) {
        const tenantId = this.$store.state.user.tenantid || Vue.ls.get(TENANT_ID);
        const token = this.$store.state.user.token || Vue.ls.get(ACCESS_TOKEN);
        const { psaId, psaName, thirdId, thirdParentId } = this.formData;
        const extraParam = `psaId=${psaId}&psaName=${psaName}&taskId=${thirdParentId}&defectId=${thirdId}&isFromOrder=1`;
        window.open(`https://${path}/#/user/login?tenant_id=${tenantId}&token=${token}&${extraParam}`);
      } else {
        this.$message.error('暂无权限，请联系管理员！');
      }
    }
  },
  computed: {
    deviceInfoShow () {
      const type = this.type;
      const formData = this.formData;
      if (['3', '6', '5', '8'].includes(type)) {
        const flag = ['7', '8'].includes(formData.taskType);
        return flag;
      }
      if (type === '7') {
        const flag = ['7', '8'].includes(formData.taskType);
        return flag;
      }
      return false;
    }
  }
};
</script>

<style lang='less' scoped>
.device-no{
  max-width: 200px;
  display: inline;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis
}
.device-ps{
  color: #267DCF;
  margin-left: 12px;
  cursor: pointer;
  &:hover{
    color: var(--zw-primary-color--default);
  }
}
</style>
