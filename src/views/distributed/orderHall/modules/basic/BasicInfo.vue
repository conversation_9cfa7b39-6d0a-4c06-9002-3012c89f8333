<template>
  <div style='margin-top: 12px'>
    <BasicInfoDetail :baseForm="formData" :dictMap="dictMap" />
  </div>
</template>

<script>
import BasicInfoDetail from './BasicInfoDetail.vue';

export default {
  name: 'basic-info',
  components: { BasicInfoDetail },
  props: {
    type: {
      type: String,
      default: () => {
        return null;
      }
    },
    formData: {
      type: Object,
      default: () => {
        return {};
      }
    },
    dictMap: {
      type: Object,
      default: () => {
        return {};
      }
    }
  },
  data () {
    return {
    };
  },
  mounted () {
  }
};
</script>
