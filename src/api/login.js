import api from './index';
import { axios } from '@/utils/request';

/**
 * @param parameter
 * @returns {*}
 */
export function login (parameter) {
  return axios({
    url: '/sys/login',
    method: 'post',
    data: parameter
  });
}

export function getSmsCaptcha (parameter) {
  return axios({
    url: api.SendSms,
    method: 'post',
    data: parameter
  });
}

export function logout (logoutToken) {
  return axios({
    url: '/sys/logout',
    method: 'post',
    headers: {
      'Content-Type': 'application/json;charset=UTF-8',
      'X-Access-Token': logoutToken
    }
  });
}

/**
 * 第三方登录
 * @param token
 * @param thirdType
 * @returns {*}
 */
export function thirdLogin (token, thirdType) {
  return axios({
    url: `/sys/thirdLogin/getLoginUser/${token}/${thirdType}`,
    method: 'get',
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  });
}
// 获取图片验证码
export function reqGet (data) {
  return axios({
    url: `/captcha/get`,
    method: 'post',
    data
  });
}
