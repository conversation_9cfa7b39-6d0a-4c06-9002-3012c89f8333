import { USER_INFO } from '@/store/mutation-types';
import uuid from 'uuid';
import moment from 'moment';
export const baseList = [
  {
    label: '清洗日期',
    key: 'cleanDate'
  },
  {
    label: '今日天气',
    key: 'todayWeatherName'
  },
  {
    label: '清洗地点',
    key: 'cleanPlace',
    ellipsis: true
  },
  {
    label: '清洗人数',
    key: 'cleanPeopleCount'
  },
  {
    label: '清洗工具',
    key: 'cleanToolName'
  },
  {
    label: '清洗容量(MW)',
    key: 'cleanCap'
  },
  {
    label: '人均清洗容量(MW)',
    key: 'cleanCapPeople'
  },
  {
    label: '累计清洗容量(MW)',
    key: 'totalCleanCap'
  },
  {
    label: ' 清洗进度(%)',
    key: 'cleanRate'
  },
  {
    // label: '进度说明',
    // key: 'cleanRateExplain',
    // ellipsis: true
    slot: 'cleanRateExplain'
  },
  {
    label: '清洗效果',
    key: 'cleanEffectName'
  },
  {
    label: ' 验收人',
    key: 'checkUserName'
  },
  // {
  //   label: '清洗中',
  //   key: 'cleaning',
  //   type: 'file:picture-card'
  // },
  //
  // {
  //   label: '清晰效果对比',
  //   key: 'cleanEffectCompare',
  //   type: 'file:picture-card',
  //   span: 16
  // }
  { slot: 'cleaning' },
  { slot: 'cleanEffectCompare' }
];

export const initialItemObj = {
  tabTitle: '自定义',
  cleanDate: moment().format('YYYY-MM-DD'),
  todayWeather: undefined,
  cleanPlace: undefined,
  cleanPeopleCount: undefined,
  cleanTool: undefined,
  cleanCap: undefined,
  cleanCapPeople: undefined,
  totalCleanCap: undefined,
  cleanRate: undefined,
  cleanRateExplain: undefined,
  cleanEffect: undefined,
  checkUser: Vue.ls.get(USER_INFO).username,
  sceneFileOneList: [],
  sceneFileTwoList: [],
  cleanBeforeSceneFileList: [],
  cleanAfterSceneFileList: [],
  cleanBeforeElectricFileList: [],
  cleanAfterElectricFileList: [],
  editable: true,
  type: 'add',
  id: uuid()
};

export function initialItemFn ({ todayWeather }) {
  return {
    tabTitle: '自定义',
    cleanDate: moment().format('YYYY-MM-DD'),
    todayWeather,
    cleanPlace: undefined,
    cleanPeopleCount: undefined,
    cleanTool: undefined,
    cleanCap: undefined,
    cleanCapPeople: undefined,
    totalCleanCap: undefined,
    cleanRate: undefined,
    cleanRateExplain: undefined,
    cleanEffect: undefined,
    checkUser: Vue.ls.get(USER_INFO).username,
    sceneFileOneList: [],
    sceneFileTwoList: [],
    cleanBeforeSceneFileList: [],
    cleanAfterSceneFileList: [],
    cleanBeforeElectricFileList: [],
    cleanAfterElectricFileList: [],
    editable: true,
    type: 'add',
    id: uuid()
  };
}
