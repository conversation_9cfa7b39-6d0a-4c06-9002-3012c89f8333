<template>
  <a-row :gutter="24" style="margin: 0">
    <a-col :xxl="4" :xl="8" :md="24">
      <role-tree-select :depTreeAllowClear='true' @change='roleTreeChange' v-model='queryParams.depcode' ref="taskRoleTree" :isOnlyDep='true' :hasDepDefaultValue='false'/>
    </a-col>
    <a-col :xxl="4" :xl="8" :md="12">
      <div class="search-item">
        <span class="search-label">电站名称</span>
        <a-input
          v-model="queryParams.psaName"
          allowClear
          placeholder="请输入电站名称"
          style="width: 100%"
        ></a-input>
      </div>
    </a-col>
    <a-col :xxl="4" :xl="8" :md="12">
      <div class="search-item">
        <span class="search-label">任务编号</span>
        <a-input
          v-model="queryParams.taskCode"
          allowClear
          placeholder="请输入任务编号"
          style="width: 100%"
        ></a-input>
      </div>
    </a-col>
    <a-col :xxl="4" :xl="8" :md="12">
      <div class="search-item">
        <span class="search-label">任务来源</span>
        <a-select v-model='queryParams.taskSource' placeholder='请选择任务来源' allow-clear>
          <a-select-option
            :value="item.dataValue" v-for="(item,index) in dict.pre_test_task_source" :key="index">
            {{ item.dataLable }}
          </a-select-option>
        </a-select>
      </div>
    </a-col>
    <a-col :xxl="4" :xl="8" :md="8">
      <div class="search-item">
        <throttle-button label="查询" @click="queryPageData" />
        <throttle-button label="重置" class="solar-eye-btn-primary-cancel" @click="resetChange" />
      </div>
    </a-col>
  </a-row>
</template>
<script>
import initDict from '@/mixins/initDict';
export default {
  name: 'Search',
  mixins: [initDict],
  props: {
    dict: {
      type: Object,
      default: () => ({})
    }
  },
  data () {
    return {
      queryParams: {}

    };
  },
  methods: {
    roleTreeChange (val) {
      this.queryParams.deptCode = val;
      setTimeout(this.queryPageData);
    },
    resetChange (val) {
      this.$refs.taskRoleTree.reset();
      this.queryParams = {};
      this.$emit('reset');
    },
    queryPageData () {
      this.$emit('queryPageData', this.queryParams);
    },
    taskSourceChange () {
      // if (this.queryParams.deptCode) {
      //   this.$refs.taskRoleTree.reset();
      // }
      this.$refs.taskRoleTree.depValue = undefined;
      this.queryParams.deptCode = null;
    }
  },
  computed: {}

};
</script>
