<template>
  <div v-if="deviceInfoShow">
    <div v-if="['3', '7', '8'].includes(type)">
      <DeviceInfoDetail :isDroneOrder='isDroneOrder' @openCareMenu='openCareMenu' :baseForm="formData" :dictMap='dictMap' />
    </div>
    <div v-else>
      <a-row :gutter="24">
        <a-col :span="24">
          <div class="order-dispose">
            <div class="title-box">
              <span class="before"></span>
              <span>设备信息</span>
            </div>
          </div>
        </a-col>
      </a-row>
      <a-row :gutter="24">
        <a-col :xl="8" :sm="12" :xs="24">
          <a-form-model-item label="设备类型" prop="deviceTypeId" :rules="{ required: true, message: '此项为必填项' }">
            <a-cascader
              size="default"
              v-model="formData.deviceTypeId"
              :options="options"
              style="width: 100%"
              placeholder="请选择"
              @change="deviceTypeIdChange"
            ></a-cascader>
          </a-form-model-item>
        </a-col>
        <template v-if="(formData.isEngLegacy == '0' && formData.taskType === '8') || formData.taskType === '7'">
          <template v-if="formData.deviceTypeId && formData.deviceTypeId[0] != '0'">
            <a-col :xl="8" :sm="12" :xs="24">
              <a-form-model-item
                label="设备名称"
                prop="deviceName"
                :rules="{ required: true, message: '此项为必填项' }"
              >
                <a-select
                  v-model="formData.deviceName"
                  placeholder="请输入"
                  style="width: 100%"
                  show-search
                  @change="deviceChange"
                >
                  <a-select-option
                    v-for="item in deviceNameList"
                    :key="item.id"
                    :data-id="item.id"
                    :value="item.name"
                    >{{ item.name }}</a-select-option
                  >
                </a-select>
              </a-form-model-item>
            </a-col>
            <a-col :xl="8" :sm="12" :xs="24">
              <a-form-model-item label="设备编号" prop="deviceNo">
                <a-select v-model="formData.deviceNo" placeholder="请选择" :disabled="isDisabled || disabledDevice">
                  <a-select-option v-for="(item, k) in deviceNoList" :key="k" :value="item.deviceId">{{
                    item.deviceId
                  }}</a-select-option>
                </a-select>
              </a-form-model-item>
            </a-col>
          </template>
          <a-col :xl="8" :sm="12" :xs="24">
            <a-form-model-item label="设备状态" prop="deviceType" :rules="{ required: true, message: '此项为必填项' }">
              <a-select disabled v-model="formData.deviceType" placeholder="请选择">
                <a-select-option v-for="item in dictMap.two_device_run_sts" :key="item.key" :value="item.dataValue">{{
                  item.dataLable
                }}</a-select-option>
              </a-select>
            </a-form-model-item>
          </a-col>
        </template>
        <a-col :xl="8" :sm="12" :xs="24" v-if="formData.deviceTypeId && formData.deviceTypeId[0] != '0'">
          <a-form-model-item
            :label="formData.taskType === '8' ? '缺陷名称' : '故障名称'"
            prop="defectName"
            :rules="{ required: true, message: '此项为必填项' }"
          >
            <a-select v-model="formData.defectName" placeholder="请选择" allowClear>
              <a-select-option v-for="item in faultNameList" :key="item.id" :value="item.name">
                {{ item.name }}
              </a-select-option>
            </a-select>
          </a-form-model-item>
        </a-col>
        <a-col :xl="8" :sm="12" :xs="24" v-if="defectHandleConditionShow">
          <a-form-model-item
            :label="formData.taskType === '8' ? '处理条件' : '停电范围'"
            :prop="formData.taskType === '8' ? 'defectHandleCondition' : 'powerCutRange'"
            :rules="{ required: true, message: '此项为必填项' }"
          >
            <a-select @change='getDefectTypeFn' v-if="formData.taskType === '8'" v-model="formData.defectHandleCondition" placeholder="请选择" style="width: 90%">
              <a-select-option v-for="item in dictMap.defect_handle_condit" :key="item.key" :value="item.dataValue">{{
                  item.dataLable
                }}</a-select-option>
            </a-select>

            <a-select @change='getDefectTypeFn'  v-else v-model="formData.powerCutRange" placeholder="请选择" style="width: 90%">
              <a-select-option v-for="item in powerDictList" :key="item.key" :value="item.dataValue">{{
                  item.dataLable
                }}</a-select-option>
            </a-select>
            <a-popover placement="topRight" arrow-point-at-center>
              <template slot="content">
                <span>{{formData.taskType === '8'?'预估处理此缺陷时的设备停运情况':'预估此故障造成的停电范围'}}</span>
              </template>
              <span>
                <svg-icon style="margin-left: 1em" iconClass="health-info"></svg-icon>
              </span>
            </a-popover>
          </a-form-model-item>
        </a-col>
        <a-col :xl="8" :sm="12" :xs="24">
          <a-form-model-item
            :label="formData.taskType === '8' ? '缺陷类别' : '故障类别'"
            prop="defectType"
            :rules="{ required: true, message: '此项为必填项' }"
          >
            <a-select v-model="formData.defectType" disabled placeholder="请选择" style="width: 100%">
              <a-select-option v-for="item in dictMap.two_fault_classify" :key="item.key" :value="item.dataValue">{{
                item.dataLable
              }}</a-select-option>
            </a-select>
          </a-form-model-item>
        </a-col>
      </a-row>
    </div>
  </div>
</template>

<script>
import { getDeviecTypeTree } from '@/api/common_gy/common.js';
import {
  getDeviceNameList,
  getDeviceIdList,
  getFaultName
  // getDeviecClassByDeviceId
} from '@/api/common_gy/faultManage.js';
// import initDict from '@/mixins/initDict';
import { getDate } from '@/api/config/digitalRegulation';
import DeviceInfoDetail from './DeviceInfoDetail';
import { getDefectType } from '@/api/isolarErp/orderHall';

export default {
  name: 'device-info',
  components: { DeviceInfoDetail },
  props: {
    type: {
      type: String,
      required: true,
      default: () => {
        return null;
      }
    },
    formData: {
      type: Object,
      default: () => {
        return {};
      }
    },
    dictMap: {
      type: Object,
      default: () => {
        return {};
      }
    },
    isDroneOrder: {
      type: Boolean,
      default: false
    }
  },
  // mixins: [initDict],
  data () {
    return {
      options: [],
      faultNameList: [],
      deviceNameList: [], // 设备名称
      deviceNoList: [] // 设备编号
    };
  },
  mounted () {
    console.log(this.dictMap);
    // this.getDictMap('')
    // this.getDeviecTypeTree();
  },
  methods: {
    getDeviecTypeTree () {
      getDeviecTypeTree({
        psId: this.formData.psaId
      }).then((response) => {
        this.options = this.formatData(response.result ? response.result : []);
        if (this.formData.taskType == '8') {
          this.options.unshift({
            ancestors: '0',
            children: [],
            deviceTypeLevel: 0,
            label: '非设备',
            name: '非设备',
            pid: 0,
            value: '0'
          });
        }
      });
    },
    formatData (data) {
      for (var i = 0; i < data.length; i++) {
        if (data[i].children.length < 1) {
          data[i].value = String(data[i].value);
          data[i].children = undefined;
        } else {
          data[i].value = String(data[i].value);
          this.formatData(data[i].children);
        }
      }
      return data;
    },
    // 获取设备名称
    getDeviceList () {
      let self = this;
      const list = this.formData.deviceTypeId;
      const l = list && list.length;
      const deviceTypeId = list && list[l - 1];
      getDeviceNameList({
        deviceTypeId: deviceTypeId,
        psId: this.formData.psaId
      })
        .then((res) => {
          self.deviceNameList = res.result;
        })
        .catch(() => {
          self.deviceNameList = [];
        });
    },
    // 获取设备编号
    getDeviceCodeList () {
      const formData = this.formData;
      const list = this.formData.deviceTypeId;
      const l = list && list.length;
      const deviceTypeId = list && list[l - 1];
      getDeviceIdList({
        deviceTypeId,
        name: formData.deviceName,
        psId: formData.psaId
      })
        .then((res) => {
          this.deviceNoList = res.result;
        })
        .catch(() => {
          this.deviceNoList = [];
        });
    },
    // 查询deviceName
    deviceChange (name, $event) {
      this.deviceNoList = [];
      this.formData.deviceNo = undefined;
      const deviceId = $event.data.attrs['data-id'];
      this.formData.deviceId = deviceId;
    },
    // 获取缺陷(故障)类别并赋值
    getDefectTypeFn () {
      const list = this.formData.deviceTypeId;
      if (!((this.formData.powerCutRange || this.formData.defectHandleCondition) && list.length)) return;
      const l = list && list.length;
      const deviceTypeId = list && list[l - 1];
      const formData = this.formData;
      getDefectType({
        taskType: formData.taskType,
        powerCutRange: formData.powerCutRange,
        defectHandleCondition: formData.defectHandleCondition,
        deviceType: deviceTypeId
      }).then((res) => {
        formData.defectType = res.result_data;
        if (['7', '8'].includes(formData.taskType) && res.result_data) {
          this.getPlanTimeLimit();
        }
      });
    },
    // 获取故障名称
    getFaultList () {
      if (this.formData.isEngLegacy == 1) {
        this.faultNameList = this.dictMap.pdca_defect_name.map((item) => ({
          ...item,
          id: item.dataValue,
          name: item.dataLable
        }));
        return;
      }
      const list = this.formData.deviceTypeId;
      const l = list && list.length;
      const deviceTypeId = list && list[l - 1];
      const param = {
        deviceTypeId,
        equipmentStatus: this.formData.deviceType || '2'
      };
      getFaultName(param)
        .then((res) => {
          this.faultNameList = res.result;
        })
        .catch(() => {
          this.faultNameList = [];
        });
    },
    deviceTypeIdChange (val) {
      // 重置设备名称，设备编号，缺陷（故障）名称
      this.formData.deviceName = undefined;
      this.formData.deviceId = null;
      this.formData.deviceNo = undefined;
      this.formData.defectName = undefined;
      // this.formData.defectType = undefined;
      if (val == 0 && this.formData.isEngLegacy == 1 && this.formData.taskType == '8') {
        this.formData.defectType = '4';
        return;
      }
      this.getDefectTypeFn();
    },
    // 获取预计消除时间
    getPlanTimeLimit () {
      const findTime = this.formData.findTime;
      getDate({
        taskType: this.formData.taskType,
        scale: this.formData.cleanCap,
        subTaskType: this.formData.defectType,
        endDate: findTime && findTime.split(' ')[0]
      }).then((res) => {
        if (res.result_code == 1) {
          this.formData.predictRecoverTime = res.result_data + ' ' + findTime.split(' ')[1];
        }
      });
    },
    // 跳转无人机菜单
    openCareMenu () {
      let menu = JSON.parse(sessionStorage.getItem('ALL_PERMISSION_MENU'));
      let env = process.env.VUE_APP_ENV;
      env = env ? (env === 'pro' ? 'www' : env) : 'fat';
      const path = env + '.isolarhealth.com/newCare';
      const isInclude = ['/solarCare', '/solarCare/patrolInsight'].every(item => menu.includes(item));
      if (isInclude) {
        const tenantId = this.$store.state.user.tenantid || Vue.ls.get(TENANT_ID);
        const token = this.$store.state.user.token || Vue.ls.get(ACCESS_TOKEN);
        const { psaId, psaName, thirdId, thirdParentId } = this.formData;
        const extraParam = `psaId=${psaId}&psaName=${psaName}&taskId=${thirdParentId}&defectId=${thirdId}&isFromOrder=1`;
        window.open(`https://${path}/#/user/login?tenant_id=${tenantId}&token=${token}&${extraParam}`);
      } else {
        this.$message.error('暂无权限，请联系管理员！');
      }
    }
  },
  computed: {
    deviceInfoShow () {
      const type = this.type;
      const formData = this.formData;
      if (['3', '6', '5', '8'].includes(type)) {
        const flag = ['7', '8'].includes(formData.taskType);
        return flag;
      }
      if (type === '7') {
        const flag = ['7', '8'].includes(formData.taskType);
        return flag;
      }
      return false;
    },
    powerDictList () {
      const { psaName } = this.formData;
      if (/扶贫|分布式/g.test(psaName)) {
        return this.dictMap.power_cut_dis;
      }
      return this.dictMap.power_cut_centre;
    },
    defectHandleConditionShow () {
      const formData = this.formData;
      if (formData.taskType === '8' && formData.deviceTypeId && formData.deviceTypeId[0] != '0') {
        return true;
      }
      if (formData.taskType === '7') {
        return true;
      }
      return false;
    }
  },
  watch: {
    'formData.deviceTypeId' (val) {
      //  指派领取可编辑设备信息
      if (['5', '6'].includes(this.type)) {
        // if (['7', '8'].includes(this.formData.taskType)) {
        //   const deviceTypeId = val[val.length - 1];
        //   // 非设备缺陷类别为4
        //   if (deviceTypeId == 0) {
        //     this.formData.defectType = '4';
        //   }
        // }
        // 获取故障
        this.getFaultList();
        // 获取设备名称
        this.getDeviceList();
      }
    },
    'formData.deviceName' (val) {
      if (['5', '6'].includes(this.type)) {
        // this.filterDeviceName();
        // 获取设备编号
        this.getDeviceCodeList();
      }
    },
    dictMap: {
      immediate: true,
      handler (val) {
        this.getFaultList();
      }
    },
    'formData.psaId' (val) {
      if (val) {
        this.getDeviecTypeTree();
      }
    }
  }
};
</script>
