<!-- 任务基础表单 -->
<template>
  <a-form-model :model="baseForm" :rules="base_rules" ref="historyical" :labelCol="{ style: 'width: 120px' }" :wrapperCol="{ style: 'width: calc(100% - 120px)' }">
    <a-row>
      <a-col :xl='8' :sm='12' :xs='24'>
        <a-form-model-item label="缺陷名称" prop="defectName">
          <a-select v-model="baseForm.defectName" :disabled="isDisabled">
            <a-select-option v-for="item in dictMap.pdca_defect_name" :key="item.dataId" :value="item.dataValue">{{item.dataLable}}</a-select-option>
          </a-select>
        </a-form-model-item>
      </a-col>
      <a-col :xl='8' :sm='12' :xs='24'>
        <a-form-model-item label="发现时间" prop="findTime">
          <a-date-picker style="width:100%" show-time format="YYYY-MM-DD HH:mm" :allowClear="false"
            valueFormat="YYYY-MM-DD HH:mm" :disabled="isDisabled" placeholder="请选择" v-model="baseForm.findTime" :disabled-time="disabledDateTime"
            :disabled-date="disabledFindTime" />
        </a-form-model-item>
      </a-col>
      <a-col :xl='8' :sm='12' :xs='24'>
        <a-form-model-item label="发现人" prop="findUserName">
          <a-input :max-length="150" :title="baseForm.findUserName" v-model="baseForm.findUserName" disabled style="width: 100%;" />
        </a-form-model-item>
      </a-col>
      <a-col :xl='8' :sm='12' :xs='24'>
        <a-form-model-item label="发现地点" prop="findPlace">
          <a-select v-model="baseForm.findPlace" placeholder="请输入" :disabled="isDisabled" style="width: 100%;" show-search>
            <a-select-option v-for="item in dictMap.find_place" :key="item.key" :value="item.dataValue">{{item.dataLable}}</a-select-option>
          </a-select>
        </a-form-model-item>
      </a-col>
      <a-col :xl='8' :sm='12' :xs='24'>
        <a-form-model-item label="设备类型" prop="deviceTypeId">
          <g-cascader v-model="baseForm.deviceTypeId" :disabled="isDisabled" :allowClear="false" :options="options"
            @change="deviceTypeChange" style="width: 100%;" />
        </a-form-model-item>
      </a-col>
      <a-col :xl='8' :sm='12' :xs='24'>
        <a-form-model-item label="缺陷类别" prop="defectType" show-search>
          <a-select v-model="baseForm.defectType" disabled placeholder="请选择" style="width: 100%;">
            <a-select-option v-for="item in dictMap.two_fault_classify" :key="item.key" :value="item.dataValue">{{item.dataLable}}</a-select-option>
          </a-select>
        </a-form-model-item>
      </a-col>
      <a-col :span="24" v-if="type != '9'">
        <a-form-model-item label="缺陷描述" prop="taskDescription">
          <a-textarea size="default" :max-length="200" :auto-size="{ minRows: 3, maxRows: 3}" :disabled="isDisabled"
            v-model="baseForm.taskDescription" @blur="baseForm.taskDescription = $trim($event)" placeholder="发生范围+发生概率+修复费用+预计损失(安全、电量损失)"
            style="width: 100%;"></a-textarea>
        </a-form-model-item>
      </a-col>
      <a-col :xl='8' :sm='12' :xs='24'>
        <a-form-model-item label="预计消除时间" prop="predictRecoverTime">
          <a-input disabled v-model="baseForm.predictRecoverTime" style="width: 100%;" />
        </a-form-model-item>
      </a-col>
      <a-col :span="24">
        <a-form-model-item label="整改要求" prop="rectifyReq">
          <a-textarea size="default" :max-length="200" :auto-size="{ minRows: 3, maxRows: 3}" :disabled="isDisabled"
            v-model="baseForm.rectifyReq" @blur="baseForm.rectifyReq = $trim($event)" placeholder="请输入"
            style="width: 100%;"></a-textarea>
        </a-form-model-item>
      </a-col>
      <a-col :xl="8" :sm="12" :xs="24">
        <a-form-model-item label="作业类别" prop="jobCategory">
          <a-select v-model="baseForm.jobCategory" :disabled="isDisabled" @change="jobCategoryChange" allowClear
            size="default" placeholder="请选择" style="width: 100%;">
            <a-select-option v-for="item in dictMap.job_category" :key="item.dataValue" :value="item.dataValue">
              {{item.dispName}}
            </a-select-option>
          </a-select>
        </a-form-model-item>
      </a-col>
      <a-col :xl="8" :sm="12" :xs="24">
        <a-form-model-item label="作业内容" prop="erpSqJobRiskMappingId">
          <a-select v-model="baseForm.erpSqJobRiskMappingId" :disabled="isDisabled" @change="erpSqJobRiskMappingIdChange" allowClear
            size="default" placeholder="请选择" style="width: 100%;">
            <a-select-option v-for="item in jobContentOptions" :key="item.id" :value="item.id" :code="item.riskLevel">
              {{item.jobContentName}}
            </a-select-option>
          </a-select>
        </a-form-model-item>
      </a-col>
      <a-col :xl="8" :sm="12" :xs="24">
        <a-form-model-item label="风险等级" prop="riskLevel">
          <a-select v-model="baseForm.riskLevel" :disabled="true" size="default" placeholder="请选择" style="width: 100%;">
            <a-select-option v-for="item in dictMap.risk_level" :key="item.dataValue" :value="item.dataValue">
              {{item.dispName}}
            </a-select-option>
          </a-select>
        </a-form-model-item>
      </a-col>
      <a-col :xl='8' :sm='12' :xs='24'>
        <a-form-model-item label="作业开始日期" prop="riskJobDate">
          <a-date-picker  style="width: 100%;" size="default" v-model="baseForm.riskJobDate"
            format="YYYY-MM-DD" valueFormat="YYYY-MM-DD" placeholder="请输入作业开始日期"
            :disabled-date="disabledRiskJobDate"></a-date-picker>
        </a-form-model-item>
      </a-col>
      <!-- 通用附件 -->
      <a-col :span='24' v-show="!(baseForm.isFarm == '1' && baseForm.taskType == '9')">
        <a-form-model-item label="附件" prop="uploadFileList">
          <uploadFile v-model="baseForm.uploadFileList" :disabled="isDisabled" :maxNum="5" :maxSize="10" :multiple="true" tip="最多上传5个文件,且上传的附件最大不超过10MB!"
            @set="fieldChange('historyical','uploadFileList')">上传</uploadFile>
        </a-form-model-item>
      </a-col>
    </a-row>
  </a-form-model>
</template>

<script>
import moment from 'moment';
import initDict from '@/mixins/initDict';
import uploadFile from '@/components/com/fileUploadView';
import { formRule } from './formMixins';
export default {
  name: 'historicalLegacy',
  mixins: [initDict, formRule],
  components: {
    uploadFile
  },
  data () {
    return {
      // 任务表单基础信息
      baseForm: {
        defectName: '',
        findUserName: undefined,
        findUser: '',
        findTime: undefined,
        findPlace: undefined,
        defectType: '',
        taskDescription: '',
        predictRecoverTime: undefined,
        rectifyReq: '',
        jobCategory: undefined, // 作业类别
        erpSqJobRiskMappingId: undefined, // 作业内容
        riskLevel: undefined, // 风险等级
        riskJobDate: undefined, // 作业开始日期
        uploadFileList: undefined,
        rectifyRlt: undefined,
        compTime: undefined,
        userNames: '',
        deviceTypeId: [],
        returnMessage: undefined
      },
      taskTypeOptions: [], // 户用电站的任务类型
      ordinaryTypeOptions: [], // 非户用类型电站的的任务类型
      base_rules: {
        deviceTypeId: [{ required: true, message: '请选择设备类型', trigger: 'change' }],
        defectName: [{ required: true, message: '请选择缺陷名称', trigger: 'change' }],
        findUserName: [{ required: true, message: '发现人不能为空', trigger: 'change' }],
        findTime: [{ required: true, message: '请填写发现时间' }],
        findPlace: [{ required: true, message: '请选择发现地点', trigger: 'change' }],
        defectType: [{ required: true, message: '请填写缺陷类别', trigger: 'blur' }],
        predictRecoverTime: [{ required: true, message: '预计消除时间不能为空' }],
        rectifyReq: [{ required: true, message: '请填写整改要求', trigger: 'blur' }],
        jobCategory: [{ required: true, message: '请选择作业类别', trigger: 'change' }],
        erpSqJobRiskMappingId: [{ required: true, message: '请选择作业内容', trigger: 'change' }],
        riskLevel: [{ required: true, message: '请选择风险等级', trigger: 'change' }],
        riskJobDate: [{ required: true, message: '请选择作业开始日期', trigger: 'change' }],
        uploadFileList: [{ required: true, message: '附件不能为空', trigger: 'change' }],
        rectifyRlt: [{ required: false, message: '请选择整改结果', trigger: 'change' }],
        compTime: [{ required: false, message: '请填写完成时间', trigger: 'blur' }],
        userNames: [{ required: false, message: '请填写确认人', trigger: 'blur' }],
        returnMessage: [{ required: false, message: '请填写审批意见', trigger: 'blur' }]
      },
      deviceNameList: [], // 设备名称
      deviceCodeList: [], // 设备编号
      assignList: [] // 指派人
    };
  },
  watch: {
    'baseInfo.jobCategory' (val) {
      if (val) {
        this.riskAllocation(this.baseInfo.jobCategory);
      }
    }
  },
  mounted () {},
  created () {
    this.getDictMap('pdca_defect_name,fault_class,find_place,two_fault_classify,job_category,risk_level');
  },
  methods: {
    moment,
    validateForm (type) {
      return new Promise((resolve, reject) => {
        if (type == 0) {
          resolve(this.baseForm);
        } else {
          this.$refs.historyical.validate(valid => {
            if (valid) {
              resolve(this.baseForm);
            } else {
              reject(new Error());
            }
          });
        }
      });
    },
    // 发现时间不能早于系统当前时间
    disabledDeadLineTime (current) {
      return current && moment().subtract(1, 'days') >= current;
    }
  }
};
</script>

<style lang="less" scoped>
  .link-span {
    color: #FF8F33;
    text-decoration: underline;
    cursor: pointer;
  }
</style>
