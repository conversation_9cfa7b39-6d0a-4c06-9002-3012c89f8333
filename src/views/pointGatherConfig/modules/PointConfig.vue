<!-- 直采测点配置 -->
<template>
<div>
  <a-drawer width="100%" :header-style="{height: '55px',background: 'transparent'}" :visible="visible" @close="close" :destroyOnClose="true"
            :get-container="getContainer" :maskClosable="false"
            :wrap-style="{ position: 'absolute' }" :afterVisibleChange="afterVisibleChange" class="drawer-box">
    <template slot="title">
      <div class='flex-start'>
        <span>直采测点配置</span>
        <div class='flex-start'>
          <div class='title-item'>
            <span class='label'>接入SN</span>
            <span class='value' :title="tabHeaderData.sn">{{ tabHeaderData.sn }}</span>
          </div>
          <div class='title-item'>
            <span class='label'>接入名称</span>
            <span class='value'>{{ tabHeaderData.snName }}</span>
          </div>
          <div class='title-item'>
            <span class='label'>测点总数</span>
            <span class='value'>{{ tabHeaderData.sum }}</span>
          </div>
          <div class='title-item'>
            <span class='label'>测点禁用数</span>
            <span class='value'>{{ tabHeaderData.forbidden }}</span>
          </div>
          <div class='title-item'>
            <span class='label'>配置完成数</span>
            <span class='value'>{{ tabHeaderData.complete }}</span>
          </div>
        </div>
      </div>
    </template>
    <a-spin :spinning="loadingConfig" >
      <div class="solar-eye-search-model">
        <a-spin :spinning="headerLoading">
          <div class='tabs flex-start'>
            <div class='tab-item flex-center' @click='activeTab(0)' :class='{"tab-item-active": activeTabIndex == 0 }'>
              <div class='tab-status'>
                <a-icon :type="tabHeaderData.accessAbnormal =='0'?'check-circle':'close-circle'" theme="filled" :style="{ fontSize: '24px',color:tabHeaderData.accessAbnormal =='0'? 'var(--zw-primary-color--default)' :'#D8D8D8'}" />
              </div>
              <div class='tab-data flex-column'>
                <span class="title">原始测点接收表</span>
                <span>接入异常数: {{ tabHeaderData.accessAbnormal }}</span>
              </div>
            </div>
            <div class='split-line'></div>

            <div class='tab-item flex-center' @click='activeTab(1)' :class='{"tab-item-active": activeTabIndex == 1 }'>
              <div class='tab-status'>
                <a-icon :type="tabHeaderData.deviceBindingAbnormal =='0'?'check-circle':'close-circle'" theme="filled" :style="{ fontSize: '24px', color:tabHeaderData.deviceBindingAbnormal =='0'? 'var(--zw-primary-color--default)' :'#D8D8D8'}" />
                <!-- <a-icon type="close-circle" theme="twoTone" two-tone-color="#D8D8D8" :style="{ fontSize: '30px'}" /> -->
              </div>
              <div class='tab-data flex-column'>
                <span class="title">测点绑定设备</span>
                <span>设备绑定异常数: {{ tabHeaderData.deviceBindingAbnormal }}</span>
              </div>
            </div>
            <div class='split-line'></div>

            <div class='tab-item flex-center' @click='activeTab(2)' :class='{"tab-item-active": activeTabIndex == 2 }'>
              <div class='tab-status'>
                <a-icon :type="tabHeaderData.configError =='0'?'check-circle':'close-circle'" theme="filled" :style="{ fontSize: '24px', color:tabHeaderData.configError =='0'? 'var(--zw-primary-color--default)' :'#D8D8D8'}" />
              </div>
              <div class='tab-data flex-column'>
                <span class="title">测点编码配置</span>
                <span >测点未配置数: {{ tabHeaderData.noConfig }}</span>
                <span>测点配置错误数: {{ tabHeaderData.configError }}</span>
              </div>
            </div>
            <div class='split-line'></div>
            <div class='tab-item flex-center' @click='activeTab(3)' :class='{"tab-item-active": activeTabIndex == 3 }'>
              <div class='tab-status'>
                <a-icon :type="tabHeaderData.unchecked =='0'?'check-circle':'close-circle'" theme="filled" :style="{ fontSize: '24px', color:tabHeaderData.unchecked =='0'? 'var(--zw-primary-color--default)' :'#D8D8D8'}" />
              </div>
              <div class='tab-data flex-column'>
                <span class="title">接入数据自检</span>
                <span>数据未核对数: {{ tabHeaderData.unchecked }}</span>
              </div>
            </div>
          </div>
        </a-spin>
        <a-form labelAlign="left"  class="solar-eye-search-content">
          <a-row :gutter="24" align="middle">
            <div v-if='activeTabIndex == 0'>
              <a-col  :md="8" :sm="24" :xxl="5" :xl="8" :lg="8">
                <a-form-item label="接收设备编号" :colon="false">
                  <a-input v-model="queryPointParams.deviceCode" placeholder="输入关键词搜索"></a-input>
                </a-form-item>
              </a-col>
              <!-- <a-col  :md="8" :sm="24" :xxl="5" :xl="8" :lg="8">
                <a-form-item label="接收子站类型" :colon="false">
                  <a-select v-model="queryPointParams.receiveStationType">
                  <a-select-option value="1">1</a-select-option>
                  <a-select-option value="2">2</a-select-option>
                  <a-select-option value="3">3</a-select-option>
                  <a-select-option value="4">4</a-select-option>
                  </a-select>
                </a-form-item>
              </a-col> -->
              <a-col  :md="8" :sm="24" :xxl="5" :xl="8" :lg="8">
                <a-form-item label="测点类型" :colon="false">
                  <a-select v-model="queryPointParams.pointType">
                  <a-select-option value="">全部</a-select-option>
                  <a-select-option value="1">遥信</a-select-option>
                  <a-select-option value="2">遥测</a-select-option>
                  </a-select>
                </a-form-item>
              </a-col>
              <a-col  :md="8" :sm="24" :xxl="5" :xl="8" :lg="8">
                <a-form-item label="配置状态" :colon="false">
                  <a-select v-model="queryPointParams.configurationStatus">
                  <a-select-option value="">全部</a-select-option>
                  <a-select-option v-for="item in pointConfigData" :value="item.secondTypeCode" :key="item.secondTypeCode">
                    {{ item.secondName }}
                  </a-select-option>
                  </a-select>
                </a-form-item>
              </a-col>
            </div>
            <div v-if='activeTabIndex == 1'>
              <a-col  :md="8" :sm="24" :xxl="5" :xl="8" :lg="8">
                <a-form-item label="接收设备编号" :colon="false">
                  <a-input v-model="queryDeviceParams.deviceCode" placeholder="输入关键词搜索"></a-input>
                </a-form-item>
              </a-col>
              <a-col  :md="8" :sm="24" :xxl="5" :xl="8" :lg="8">
                <a-form-item label="接收子站名称" :colon="false">
                  <a-input v-model="queryDeviceParams.receiveStationName" placeholder="输入关键词搜索"></a-input>
                </a-form-item>
              </a-col>
              <a-col  :md="8" :sm="24" :xxl="5" :xl="8" :lg="8">
                <a-form-item label="设备绑定状态" :colon="false">
                  <a-select v-model="queryDeviceParams.bindingStatus">
                    <a-select-option value="">全部</a-select-option>
                    <a-select-option value="1">已绑定</a-select-option>
                    <a-select-option value="2">未绑定</a-select-option>
                  </a-select>
                </a-form-item>
              </a-col>
            </div>

            <div v-if='activeTabIndex == 2'>
              <a-col  :md="8" :sm="24" :xxl="5" :xl="8" :lg="8">
                <div class="search-item">
                  <span class="search-label">电站名称</span>
                  <a-input placeholder="输入关键词搜索" v-model="queryConfigParams.psName"></a-input>
                  <!-- <ps-tree-select @change="refreshTab3" v-model="psIdTab3" ref="treeTab3" :isPsName="psNameTab3" :isQueryPs="1" :isPsaDisable="false" :checked="false"  style="width: 100%;" key="tree3"/> -->
                </div>
              </a-col>
              <a-col  :md="8" :sm="24" :xxl="5" :xl="8" :lg="8">
                <a-form-item label="设备名称" :colon="false">
                  <a-input v-model="queryConfigParams.deviceName" placeholder="输入关键词搜索"></a-input>
                </a-form-item>
              </a-col>
              <a-col  :md="8" :sm="24" :xxl="5" :xl="8" :lg="8">
                <a-form-item label="设备类型" :colon="false">
                 <a-select v-model="queryConfigParams.deviceType">
                  <a-select-option value="">全部</a-select-option>
                  <a-select-option v-for="item in deviceTypeData" :value="item.secondTypeCode" :key="item.secondTypeCode">
                    {{ item.secondName }}
                  </a-select-option>
                </a-select>
                  <!-- <a-input v-model="queryConfigParams.deviceTypeName" placeholder="输入关键词搜索"></a-input> -->
                </a-form-item>
              </a-col>
              <a-col  :md="8" :sm="24" :xxl="5" :xl="8" :lg="8">
                <a-form-item label="设备编号" :colon="false">
                  <a-input v-model="queryConfigParams.psKey" placeholder="输入关键词搜索"></a-input>
                </a-form-item>
              </a-col>
            </div>
            <div v-if='activeTabIndex == 3'>
              <a-col  :md="8" :sm="24" :xxl="4" :xl="8" :lg="8">
                <div class="search-item">
                  <span class="search-label">电站名称</span>
                   <a-input placeholder="输入关键词搜索" v-model="queryCheckParams.psName"></a-input>
                  <!-- <ps-tree-select @change="refreshTab4" v-model="psIdTab4" ref="treeTab4" :isPsName="psNameTab4" :isQueryPs="1" :isPsaDisable="false" :checked="false"  style="width: 100%;" key="tree4" /> -->
                </div>
              </a-col>
              <a-col  :md="8" :sm="24" :xxl="4" :xl="8" :lg="8">
                <a-form-item label="设备名称" :colon="false">
                  <a-input v-model="queryCheckParams.deviceName" placeholder="输入关键词搜索"></a-input>
                </a-form-item>
              </a-col>
              <a-col  :md="8" :sm="24" :xxl="4" :xl="8" :lg="8">
                <a-form-item label="设备类型" :colon="false">
                <a-select v-model="queryCheckParams.deviceType">
                  <a-select-option value="">全部</a-select-option>
                  <a-select-option v-for="item in deviceTypeData" :value="item.secondTypeCode" :key="item.secondTypeCode">
                    {{ item.secondName }}
                  </a-select-option>
                </a-select>
                  <!-- <a-input v-model="queryCheckParams.deviceTypeName" placeholder="输入关键词搜索"></a-input> -->
                </a-form-item>
              </a-col>
              <a-col  :md="8" :sm="24" :xxl="4" :xl="8" :lg="8">
                <a-form-item label="测点类型" :colon="false">
                  <a-select  v-model="queryCheckParams.pointType">
                    <a-select-option value="">全部</a-select-option>
                    <a-select-option value="1">遥信</a-select-option>
                    <a-select-option value="2">遥测</a-select-option>
                  </a-select>
                </a-form-item>
              </a-col>
              <a-col  :md="8" :sm="24" :xxl="4" :xl="8" :lg="8">
                <a-form-item label="测点名称" :colon="false">
                  <a-input v-model="queryCheckParams.pointName" placeholder="输入关键词搜索"></a-input>
                </a-form-item>
              </a-col>
            </div>

            <a-col  :md="8" :sm="24" :xxl="4" :xl="8" :lg="8" v-if='activeTabIndex == 0'>
              <div class="search-item">
                <throttle-button title="查询" label='查询' @click="searchPointData(1)"/>
                <throttle-button title="重置" label='重置'  class="solar-eye-btn-primary-cancel" @click="resetPointData" />
              </div>
            </a-col>
            <a-col  :md="8" :sm="24" :xxl="4" :xl="8" :lg="8" v-if='activeTabIndex == 1'>
              <div class="search-item">
                <throttle-button title="查询" label='查询' @click="searchDeviceData(1)"/>
                <throttle-button title="重置" label='重置'  class="solar-eye-btn-primary-cancel" @click="resetDeviceData" />
              </div>
            </a-col>
            <a-col  :md="8" :sm="24" :xxl="4" :xl="8" :lg="8" v-if='activeTabIndex == 2'>
              <div class="search-item">
                <throttle-button title="查询" label='查询' @click="searchConfigData()"/>
                <throttle-button title="重置" label='重置'  class="solar-eye-btn-primary-cancel" @click="resetConfigData(true)" />
              </div>
            </a-col>
            <a-col  :md="8" :sm="24" :xxl="4" :xl="8" :lg="8" v-if='activeTabIndex == 3'>
              <div class="search-item">
                <throttle-button title="查询" label='查询' @click="searchCheckData()"/>
                <throttle-button title="重置" label='重置'  class="solar-eye-btn-primary-cancel" @click="resetCheckData(true)" />
              </div>
            </a-col>
          </a-row>
        </a-form>
      </div>
      <div class="solar-eye-gap"></div>

      <a-col class="solar-eye-main-content">
        <!-- 操作按钮 -->
        <div class="operation" >
          <!-- tab1操作按钮 -->
          <div class="operation-btn" v-if='activeTabIndex == 0'>
          <!-- <a-upload name="file" :showUploadList="false" :multiple="false" :headers="tokenHeader" :action="importExcelUrl" @change="handleImportExcelTab1">
            <throttle-button title="导入原测点信息" label='导入原测点信息' />
          </a-upload> -->
            <throttle-button title="导入原测点信息" label='导入原测点信息' size="default" @click="handleImport('导入原测点信息', '/access/importAccessPointList', '1')"/>
            <throttle-button title="批量禁用" :disabled="isShowDisableBtn" label='批量禁用' @click="disableBtn(row,'2')"/>
            <throttle-button title="批量启用" :disabled="isShowEnableBtn" class="solar-eye-btn-primary-cancel"   label='批量启用' @click="enableBtn(row,'2')"/>
            <throttle-button title="批量删除" :disabled="isShowDelBtn" class="solar-eye-btn-primary-cancel"   label='批量删除' @click="delTab1(row,'2')"/>
            <a-dropdown>
                <a-menu slot="overlay" @click="handleImportMenuClick">
                  <a-menu-item key="1">导出所有信息</a-menu-item>
                  <a-menu-item key="2">导出原测点信息</a-menu-item>
                </a-menu>
                <a-button>导出<a-icon type="down" /></a-button>
             </a-dropdown>
          </div>
          <!-- tab2操作按钮 -->
          <div class="operation-btn" v-if='activeTabIndex == 1'>
            <throttle-button title="绑定设备" :disabled="isShowBoundDeviceBtn" label='绑定设备' @click="boundBtn(2,selectedRowKeysTab2)"/>
            <!-- <a-upload name="file" :showUploadList="false" :multiple="false" :headers="tokenHeader" :action="importExcelUrl" @change="handleImportExcelTab2">
              <throttle-button title="导入" label='导入' />
            </a-upload> -->
            <!-- <throttle-button title="导出" class="solar-eye-btn-primary-cancel"   label='导出' /> -->
            <throttle-button label="导出" @click="doExportTab2" class="solar-eye-btn-primary-cancel"
            size="default"/>
          </div>
          <!-- tab3操作按钮 -->
          <div class="operation-btn" v-if='activeTabIndex == 2'>
            <throttle-button title="按接收测点编号匹配编码" label='按接收测点编号匹配编码' :disabled="isShowNumberCodeBtn" @click="numberCode"/>
            <throttle-button title="按原测点名称匹配编码" label='按原测点名称匹配编码' :disabled="isShownNameCodeBtn" @click="nameCode"/>
            <throttle-button title="匹配测点编码" label='匹配测点编码' :disabled="isShownConfigPointBtn" @click="pointCodeBtn(type,selectedRowKeysTab3)"/>
          </div>
          <!-- tab4操作按钮 -->
          <div class="operation-btn" v-if='activeTabIndex == 3'>
            <throttle-button title="批量编辑" label='批量编辑' :disabled="isShowEditMore" @click="handleEdit()"/>
            <throttle-button title="提交" label='提交'  @click="submitTab4"/>
            <throttle-button title="重新配置"  class="solar-eye-btn-primary-cancel"  label='重新配置' @click="reconfigureBtn"/>
          </div>
        </div>
        <!-- tab1 -->
        <div v-if='activeTabIndex == 0'>
        <vxe-table
          :data="dataPointList"
          ref="xTable"
          class="my-table"
          @sort-change="(event)=>handleTableSortChange(event,queryPointParams,'pointList')"
          :sort-config="{ remote: true }"
          resizable
          align="center"
          show-overflow
          highlight-hover-row
          size="small"
          @checkbox-all="onSelectChangePoint"
          @checkbox-change="onSelectChangePoint"
          :checkbox-config="{checkMethod: checCheckboxkMethodTab1, strict: true}"
          :seq-config="{startIndex: (queryPointParams.currentPage - 1) * queryPointParams.pageSize}"
          :height="tableHeight - 190"
           key="table1"
        >
          <vxe-table-column type="checkbox" :width="60"></vxe-table-column>
          <vxe-table-column type="seq" :width="80" title="序号" align="center"></vxe-table-column>
           <vxe-table-column show-overflow="title" v-for="item in tabsColumns[activeTabIndex]" :key="item.key" :field="item.key"
                            :title="item.title" :min-width="item.width || 140"
                            :fixed="item.fixed ? item.fixed : ''" sortable>
            <template v-slot:default="{row}">
              <span><span v-if="item.key=='configurationStatusName' && row.accessStatus !=2 && isShowTips(row.configurationStatus)">
               <a-popover placement="right">
                <template slot="content">
                  {{getMsg(row.configurationStatus)}}
                </template>
                <span class="info-icon" style="color:var(--zw-primary-color--default)"><svg-icon iconClass="health-info" ></svg-icon></span>
              </a-popover>
              </span>{{ getLabel(row[item.key], null) }}</span>
            </template>
          </vxe-table-column>
            <!-- 接入状态 -->
          <vxe-table-column  :width="160" field="accessStatusName" sortable title="接入状态">
            <template v-slot:default="{row}">
              <a-select v-if="row.isEdit == 1" v-model="row.accessStatus">
                   <!-- <a-select-option v-for="item in accessStatusData" :value="item.secondTypeCode" :key="item.secondTypeCode">
                    {{ item.secondName }}
                   </a-select-option> -->
                  <a-select-option value="0">禁用</a-select-option>
                  <a-select-option value=1>配置中</a-select-option>
                  <!-- <a-select-option value="2">已完成</a-select-option> -->
              </a-select>
              <span v-else>{{ getLabel(row.accessStatusName, null) }}</span>
            </template>
          </vxe-table-column>
          <!-- 操作项 -->
          <vxe-table-column   title="操作" fixed="right" width="160"  class-name="fixed-right-column-160">
             <template v-slot:default="{row, rowIndex}">
              <template v-if="row.isEdit == 1 && activeTabIndex == 0">
               <div class="flex-action-button">
                <!-- <span @click="cancelRow(rowIndex)" class="operation-btn-hover">取消</span>
                <span @click="saveRow(row)" class="operation-btn-hover">保存</span> -->
                <a @click="cancelRow(rowIndex)" type="link" size="small">取消</a>
                <a @click="saveRow(row)" type="link" size="small">保存</a>
               </div>
              </template>
              <template v-if="row.isEdit !== 1  && activeTabIndex == 0 ">
               <div class="flex-action-button">
                <a @click="editRow(rowIndex)" type="link" size="small" v-show="row.accessStatus != 2">编辑</a>
                <a @click="delTab1(row,'1')" type="link" size="small" v-show="row.accessStatus !=2 ">删除</a>
               </div>
              </template>
            </template>
          </vxe-table-column>
          <template v-slot:empty>
            <span>查询无数据</span>
          </template>
        </vxe-table>
        </div>

        <!-- tab2 -->
        <div v-if='activeTabIndex == 1'>
        <vxe-table
          :data="getTableData(activeTabIndex)"
          ref="xTable"
          class="my-table"
          @sort-change="(event)=>handleTableSortChange(event,queryDeviceParams,'deviceList')"
          :sort-config="{ remote: true }"
          resizable
          align="center"
          show-overflow
          highlight-hover-row
          size="small"
          @checkbox-all="onSelectChangPointBound"
          @checkbox-change="onSelectChangPointBound"
          :checkbox-config="{checkMethod: checCheckboxkMethodTab2,strict: true}"
          :seq-config="{startIndex: (queryDeviceParams.currentPage - 1) * queryDeviceParams.pageSize}"
          :height="tableHeight - 190"
           key="table2"
        >
          <vxe-table-column type="checkbox" :width="60"></vxe-table-column>
          <vxe-table-column type="seq" :width="80" title="序号" align="center"></vxe-table-column>
           <vxe-table-column show-overflow="title" v-for="item in tabsColumns[activeTabIndex]" :key="item.key" :field="item.key"
                            :title="item.title" :min-width="item.width || 140"
                            :fixed="item.fixed ? item.fixed : ''" :sortable="item.key!='psName'">
            <template v-slot:default="{row}">
              <span>{{ getLabel(row[item.key], null) }}</span>
            </template>
          </vxe-table-column>
          <template v-slot:empty>
            <span>查询无数据</span>
          </template>
        </vxe-table>
        </div>

        <!-- tab3 -->
        <div v-if='activeTabIndex == 2'>
        <vxe-table
          :data="getTableData(activeTabIndex)"
          ref="xTable"
          class="my-table"
          @sort-change="(event)=>handleTableSortChange(event,queryConfigParams,'queryConfig' )"
          :sort-config="{ remote: true }"
          resizable
          align="center"
          show-overflow
          highlight-hover-row
          size="small"
          @checkbox-all="onSelectChangeCode"
          @checkbox-change="onSelectChangeCode"
          :seq-config="{startIndex: (queryConfigParams.currentPage - 1) * queryConfigParams.pageSize}"
          :height="tableHeight - 190"
           key="table3"
        >
          <vxe-table-column type="checkbox" :width="60"></vxe-table-column>
          <vxe-table-column type="seq" :width="80" title="序号" align="center"></vxe-table-column>
           <vxe-table-column show-overflow="title" v-for="item in tabsColumns[activeTabIndex]" :key="item.key" :field="item.key"
                            :title="item.title" :min-width="item.width || 140"
                            :fixed="item.fixed ? item.fixed : ''" sortable>
            <template v-slot:default="{row}">
              <span>{{ getLabel(row[item.key], null) }}</span>
            </template>
          </vxe-table-column>
          <template v-slot:empty>
            <span>查询无数据</span>
          </template>
        </vxe-table>
        </div>

        <!-- tab4 -->
        <div v-if='activeTabIndex == 3'>
        <vxe-table
          :data="dataCheckList"
          ref="xTableTab4"
          class="my-table"
          @sort-change="(event)=>handleTableSortChange(event,queryCheckParams,'queryCheck')"
          :sort-config="{ remote: true }"
          resizable
          align="center"
          show-overflow
          highlight-hover-row
          size="small"
          @checkbox-all="onSelectChangeTab4"
          @checkbox-change="onSelectChangeTab4"
          :checkbox-config="{checkMethod: checCheckboxkMethodTab4,strict: true}"
          :seq-config="{startIndex: (queryCheckParams.currentPage - 1) * queryCheckParams.pageSize}"
          :height="tableHeight - 190"
          key="table4"
        >
          <vxe-table-column type="checkbox" :width="60"></vxe-table-column>
          <vxe-table-column type="seq" :width="80" title="序号" align="center"></vxe-table-column>
           <vxe-table-column show-overflow="title" v-for="item in tabsColumns[activeTabIndex]" :key="item.key" :field="item.key"
                            :title="item.title" :min-width="item.width || 140"
                            :fixed="item.fixed ? item.fixed : ''" sortable>
            <template v-slot:default="{row}">
              <span>{{ getLabel(row[item.key], null) }}</span>
            </template>
          </vxe-table-column>
            <!-- 测点系数 -->
          <vxe-table-column  :width="160" field="coefficient" title="测点系数">
            <template v-slot:default="{row}">
              <a-input-number v-if="row.isEdit == 1 && row.pointType==2" v-model="row.coefficient"
        :min="0.000000001"
        :max="999999999"
        step="0.000001"></a-input-number>
              <span v-else>{{ getLabel(row.coefficient, null) }}</span>
            </template>
          </vxe-table-column>
           <!-- 测点取反 -->
          <vxe-table-column  :width="160" field="reverse" title="测点取反">
            <template v-slot:default="{row}">
              <a-input v-if="row.isEdit == 1 && row.pointType==1" placeholder="请输入值为0或1的数字" @change="(e)=>onKeydownEvent(e,row)" v-model="row.reverse"></a-input>
              <span v-else>{{ getLabel(row.reverse, null) }}</span>
            </template>
          </vxe-table-column>
           <!-- 偏移量 -->
          <vxe-table-column  :width="160" field="excursion" title="偏移量">
            <template v-slot:default="{row}">
              <a-input-number v-if="row.isEdit == 1 && row.pointType==2" v-model="row.excursion"></a-input-number>
              <span v-else>{{ getLabel(row.excursion, null) }}</span>
            </template>
          </vxe-table-column>
          <!-- 操作项 -->
          <vxe-table-column   title="操作" fixed="right" width="160"  class-name="fixed-right-column-160">
            <template v-slot:default="{row, rowIndex}">
              <template v-if="row.isEdit == 1">
               <div class="flex-action-button">
                <!-- <span @click="cancelRowEventTab4(rowIndex)" class="operation-btn-hover">取消</span>
                <span @click="saveRowEventTab4(row)" class="operation-btn-hover">保存</span> -->
                <a @click="cancelRowEventTab4(rowIndex)" type="link" size="small">取消</a>
                <a @click="saveRowEventTab4(row)" type="link" size="small">保存</a>
               </div>
              </template>

              <template v-else-if="row.isEdit !== 1 && row.configurationStatus !== 6">
               <div class="flex-action-button">
                 <!-- <span @click="editRowEventTab4(rowIndex)" class="operation-btn-hover">编辑</span> -->
                 <a @click="editRowEventTab4(rowIndex)" type="link" size="small">编辑</a>
               </div>
              </template>
            </template>
          </vxe-table-column>
          <template v-slot:empty>
            <span>查询无数据</span>
          </template>
        </vxe-table>
        </div>
        <page-pagination
          v-if="activeTabIndex == 0"
          :pageSize="queryPointParams.pageSize"
          :current="queryPointParams.currentPage"
          :total="totalPoint"
          @size-change="sizeChangePoint"
        />
        <page-pagination
          v-if="activeTabIndex == 1"
          :pageSize="queryDeviceParams.pageSize"
          :current="queryDeviceParams.currentPage"
          :total="totalDevice"
          @size-change="sizeChangeDevice"
        />
         <page-pagination
          v-if="activeTabIndex == 2"
          :pageSize="queryConfigParams.pageSize"
          :current="queryConfigParams.currentPage"
          :total="totalConfig"
          @size-change="sizeChangeConfig"
        />
         <page-pagination
          v-if="activeTabIndex == 3"
          :pageSize="queryCheckParams.pageSize"
          :current="queryCheckParams.currentPage"
          :total="totalCheck"
          @size-change="sizeChangeCheck"
        />
      </a-col>
    </a-spin>
  </a-drawer>
  <drawer-view ref="boundDevice" class="bound-device"  @cancel="hideBoundDevice" @close="hideBoundDevice(boundType)"  parentId="bound-config" />
  <drawer-view ref="pointCode" @cancel="cancelPonitCode" @close="cancelPonitCode(1)"  parentId="point-config" />
  <handle-modal ref="modalForm" :parentId="selectedRowKeysTab4Id" :parentKey="selectedRowKeysTab4" @cancel="cancelModalForm"></handle-modal>
  <upload v-model="upload.open" :upload="upload" @fileUpload="fileUpload" listType="text" />
 </div>
</template>
<script>
import tableHeight from '@/mixins/tableHeightAndSearchModel';
import { listMixin } from './../mixins';
import { pointConfigTab1, pointConfigTab2, pointConfigTab3, pointConfigTab4 } from './../columns';
import pagePagination from '../../../components/com/pagePagination.vue';
import { ACCESS_TOKEN } from '@/store/mutation-types';
import upload from '@/components/erp/upload/upload';
import HandleModal from './HandleModal';
import {
  checkConfigPoint,
  exportAccessDeviceList,
  exportAllAccessPointList,
  exportAccessPointList,
  submitConfigPoint,
  relocationConfigPoint,
  updatePointConfigStatusByDeviceAndPointMapping
} from '@/api/health/getPointConfig.js';
const baseUrl = process.env.VUE_APP_Health_BASE_URL;
export default {
  components: { pagePagination, HandleModal, upload },
  name: 'PointMage',
  mixins: [tableHeight, listMixin],
  data () {
    return {
      queryParams: {

      },
      psIdTab3: '',
      psNameTab3: '',
      depCodesTab3: '',
      nodeTypeTab3: '',
      psIdTab4: '',
      psNameTab4: '',
      depCodesTab4: '',
      nodeTypeTab4: '',
      tableHeightTab: '450px',
      tokenHeader: { 'X-Access-Token': Vue.ls.get(ACCESS_TOKEN) },
      importExcelUrl: 'https://www.mocky.io/v2/5cc8019d300000980a055e76',
      dataSource: [],
      rowData: {},
      tabsColumns: [
        pointConfigTab1, pointConfigTab2, pointConfigTab3, pointConfigTab4
      ],
      activeTabIndex: 0,
      colunms: pointConfigTab1,
      loading: false,
      visible: false,
      checkConfigPointParams: {
        psKeyList: []
      },
      // 导入参数
      upload: {
        title: '',
        url: '',
        open: false
      },
      importType: '1',
      // tab1导出参数
      objTab1: {
        snList: [],
        pointType: '',
        deviceCode: '',
        receiveStationType: '',
        configurationStatus: ''
      },
      // tab2导出参数
      objTab2: {
        snList: [],
        deviceCode: '',
        receiveStationName: '',
        receiveStationType: '',
        bindingStatus: '',
        ids: []
      },
      boundType: ''
    };
  },
  created () {

  },
  mounted () {},
  computed: {},
  methods: {
    // 禁用勾选
    checCheckboxkMethodTab1 ({ row }) {
      return row.configurationStatus !== 6;
    },
    checCheckboxkMethodTab2 ({ row }) {
      return row.isCheck !== 0;
    },
    checCheckboxkMethodTab4 ({ row }) {
      return row.configurationStatus !== 6;
    },
    // 导入
    handleImport (title, url, type) {
      this.upload = {
        title: title,
        url: baseUrl + url,
        open: true
      };
      this.importType = type;
    },
    // 查询 tab1
    searchPointData (val) {
      this.queryPointParams.currentPage = val;
      this.getPointList();
    },
    isShowTips (configurationStatus) {
      return [0, 1, 2, 4].includes(configurationStatus);
    },
    getMsg (configurationStatus) {
      let msg = '';
      switch (configurationStatus) {
        case 0:
          msg = '测点已禁用';
          break;
        case 1:
          msg = '设备编号或接收子站名称重复';
          break;
        case 2:
          msg = '未绑定或所绑设备不存在';
          break;
        case 4:
          msg = '设备内标准测点编码配置重复或编码不存在';
          break;
        default:
          break;
      }
      return msg;
    },
    // 重置 tab1
    resetPointData () {
      this.queryPointParams.deviceCode = '';
      this.queryPointParams.receiveStationType = '';
      this.queryPointParams.pointTypeName = '';
      this.queryPointParams.configurationStatusName = '';
      this.queryPointParams.sortFiled = '';
      this.queryPointParams.sortKind = '';
      this.searchPointData(1);
    },
    // 查询 tab2
    searchDeviceData (val) {
      this.queryDeviceParams.currentPage = val;
      this.getDeviceList();
    },
    // 重置 tab2
    resetDeviceData () {
      this.queryDeviceParams.deviceCode = '';
      this.queryDeviceParams.receiveStationName = '';
      this.queryDeviceParams.bindingStatus = '';
      this.queryDeviceParams.sortFiled = '';
      this.queryDeviceParams.sortKind = '';
      this.searchDeviceData(1);
    },
    // 查询 tab3
    searchConfigData () {
      if (this.psIdTab3 == '169a0f1565004adfb22af8f045d65913') {
        this.queryConfigParams.psId = '';
      } else {
        this.queryConfigParams.psId = this.psIdTab3;
      }
      this.getConfigList(1);
    },
    // 重置 tab3
    resetConfigData (refresh) {
      this.queryConfigParams.deviceName = '';
      this.queryConfigParams.deviceType = '';
      this.queryConfigParams.psKey = '';
      this.queryConfigParams.sortFiled = '';
      this.queryConfigParams.sortKind = '';
      this.queryConfigParams.psName = '';
      // if (this.$refs.treeTab3) {
      //   let node = this.$refs.treeTab3.rootNode;
      //   this.setStationParamsTab3(node);
      //   this.psIdTab3 = '';
      //   this.$refs.treeTab3.refresh = false;
      //   setTimeout(() => {
      //     this.$refs.treeTab3.refresh = true;
      //   }, 300);
      if (refresh) {
        this.getConfigList(1);
      }
      // }
    },
    // tab3 电站
    refreshTab3 (val, node) {
      this.setStationParamsTab3(node);
      // this.$nextTick(() => {
      //   this.searchConfigData();
      // });
    },
    setStationParamsTab3 (node) {
      this.psNameTab3 = node.name;
      this.psIdTab3 = node.id + '';
      this.depCodesTab3 = node.orgCode;
      if (node.isPsa == '0' && !node.isPs) {
        this.nodeTypeTab3 = 1;
      } else if (node.isPsa == '1') {
        this.nodeTypeTab3 = 2;
      } else if (node.isPs == 1) {
        this.nodeTypeTab3 = 3;
      } else {
        this.nodeTypeTab3 = '';
      }
    },
    // 查询 tab4
    searchCheckData () {
      if (this.psIdTab4 == '169a0f1565004adfb22af8f045d65913') {
        this.queryCheckParams.psId = '';
      } else {
        this.queryCheckParams.psId = this.psIdTab4;
      }
      this.getCheckList(1);
    },
    // 重置 tab4
    resetCheckData (refresh) {
      this.queryCheckParams.deviceName = '';
      this.queryCheckParams.deviceType = '';
      this.queryCheckParams.pointType = '';
      this.queryCheckParams.pointName = '';
      this.queryCheckParams.sortFiled = '';
      this.queryCheckParams.sortKind = '';
      this.queryCheckParams.psName = '';
      // if (this.$refs.treeTab4) {
      //   let node = this.$refs.treeTab4.rootNode;
      //   this.setStationParamsTab4(node);
      //   this.psIdTab4 = '';
      //   this.$refs.treeTab4.refresh = false;
      //   setTimeout(() => {
      //     this.$refs.treeTab4.refresh = true;
      //   }, 300);
      if (refresh) {
        this.searchCheckData(1);
      }
      // }
    },
    // tab4 电站
    refreshTab4 (val, node) {
      this.setStationParamsTab4(node);
      // this.$nextTick(() => {
      //   this.searchConfigData();
      // });
    },
    setStationParamsTab4 (node) {
      this.psNameTab4 = node.name;
      this.psIdTab4 = node.id + '';
      this.depCodesTab4 = node.orgCode;
      if (node.isPsa == '0' && !node.isPs) {
        this.nodeTypeTab4 = 1;
      } else if (node.isPsa == '1') {
        this.nodeTypeTab4 = 2;
      } else if (node.isPs == 1) {
        this.nodeTypeTab4 = 3;
      } else {
        this.nodeTypeTab4 = '';
      }
    },
    edit (val) {},
    getContainer () {
      return document.getElementById('point-config');
    },
    afterVisibleChange () {

    },
    // tab切换
    activeTab (index) {
      this.activeTabIndex = index;
      let event = { order: null };
      if (this.activeTabIndex == 0) {
        this.dataPointList = [];
        this.handleTableSortChange(event, this.queryPointParams, 'pointList');
        this.isShowDisableBtn = true;
        this.isShowEnableBtn = true;
        this.isShowDelBtn = true;
      } else if (this.activeTabIndex == 1) {
        this.dataDeviceList = [];
        this.handleTableSortChange(event, this.queryDeviceParams, 'deviceList');
        this.isShowBoundDeviceBtn = true;
      } else if (this.activeTabIndex == 2) {
        this.dataConfigList = [];
        this.handleTableSortChange(event, this.queryConfigParams, 'queryConfig');
        this.isShowNumberCodeBtn = true;
        this.isShownNameCodeBtn = true;
        this.isShownConfigPointBtn = true;
      } else if (this.activeTabIndex == 3) {
        this.dataCheckList = [];
        this.handleTableSortChange(event, this.queryCheckParams, 'queryCheck');
        this.isShowEditMore = true;
        this.isShowSubmit = true;
        this.isShowReconfigure = true;
      }
      this.$forceUpdate();
    },
    getTableData (activeTabIndex) {
      if (activeTabIndex == 0) {
        return this.dataPointList;
      } else if (activeTabIndex == 1) {
        return this.dataDeviceList;
      } else if (activeTabIndex == 2) {
        return this.dataConfigList;
      } else if (activeTabIndex == 3) {
        return this.dataCheckList;
      }
    },
    // tab1导入 -- 导入原测点信息
    handleImportExcelTab1 (info) {
      if (info.file.status !== 'uploading') {
      }
      if (info.file.status === 'done') {
      } else if (info.file.status === 'error') {
      }
    },
    // tab2导入
    handleImportExcelTab2 (info) {
      if (info.file.status !== 'uploading') {
      }
      if (info.file.status === 'done') {
      } else if (info.file.status === 'error') {
      }
    },
    // tab1导出
    handleImportMenuClick (e) {
      this.objTab1.snList = this.queryPointParams.snList;
      this.objTab1.pointType = this.queryPointParams.pointType;
      this.objTab1.deviceCode = this.queryPointParams.deviceCode;
      this.objTab1.receiveStationType = this.queryPointParams.receiveStationType;
      this.objTab1.configurationStatus = this.queryPointParams.configurationStatus;
      const { key } = e;
      if (key === '1') {
        // 1 导出所有信息
        exportAllAccessPointList(this.objTab1).then(res => {
          this.$downloadFile({ fileBase64Code: res.result_data.strBase64, fileName: res.result_data.fileName });
        }).catch(() => {
        });
      } else if (key === '2') {
        // 2 导出原测点信息
        exportAccessPointList(this.objTab1).then(res => {
          this.$downloadFile({ fileBase64Code: res.result_data.strBase64, fileName: res.result_data.fileName });
        }).catch(() => {
        });
      }
    },
    // tab2导出
    doExportTab2 () {
      this.objTab2.snList = this.queryDeviceParams.snList;
      this.objTab2.deviceCode = this.queryDeviceParams.deviceCode;
      this.objTab2.receiveStationName = this.queryDeviceParams.receiveStationName;
      this.objTab2.receiveStationType = this.queryDeviceParams.receiveStationType;
      this.objTab2.bindingStatus = this.queryDeviceParams.bindingStatus;
      this.objTab2.ids = this.queryDeviceParams.ids;
      exportAccessDeviceList(this.objTab2).then(res => {
        this.$downloadFile({ fileBase64Code: res.result_data.strBase64, fileName: res.result_data.fileName });
      }).catch(() => {
      });
    },
    boundBtn (type, row) {
      this.boundType = type;
      this.$refs.boundDevice.init(type, row, '/pointGatherConfig/modules/BoundDevice');
    },
    // 关闭绑定设备页面
    hideBoundDevice (boundType) {
      if (boundType == 1) {
        this.getList();
        this.isShowBoundDeviceHome = true;
      } else if (boundType == 2) {
        this.selectedRowKeysTab2 = [];
        this.getDeviceList();
        this.isShowBoundDeviceBtn = true;
        this.getHeaderList();
      }
      // this.$refs.boundDevice.visible = false;
      // this.selectedRowKeysTab2 = [];
      // this.getDeviceList();
      // this.isShowBoundDeviceBtn = true;
    },
    // tab3 -- 匹配测点编码
    pointCodeBtn (type, row) {
      // 校验
      checkConfigPoint(this.checkConfigPointParams).then(res => {
        if (res.result_code == '1') {
          this.$refs.pointCode.init(this.selectedRowsTab3[0], row, '/pointGatherConfig/modules/PointCode');
        }
      }).catch(() => {
      });
    },
    // 关闭匹配测点编码
    cancelPonitCode (value) {
      if (value == 1) {
        this.getConfigList();
        this.isShowNumberCodeBtn = true;
        this.isShownNameCodeBtn = true;
        this.isShownConfigPointBtn = true;
      }
      this.getHeaderList();
      // this.$refs.pointCode.visible = false;
      // this.getConfigList();
      // this.isShowNumberCodeBtn = true;
      // this.isShownNameCodeBtn = true;
      // this.isShownConfigPointBtn = true;
    },
    // 关闭tab4批量编辑弹出层
    cancelModalForm () {
      this.$refs.modalForm.visible = false;
      this.isShowEditMore = true;
      this.isShowSubmit = true;
      this.isShowReconfigure = true;
      this.getCheckList();
      this.getHeaderList();
    },
    // tab4 -- 批量编辑
    handleEdit () {
      this.$refs.modalForm.visible = true;
    },
    // tab4 -- 提交
    submitTab4 () {
      let that = this;
      this.$confirm({
        title: '提示',
        content: '提交后配置完成，不可编辑，是否提交？',
        okText: '确定',
        cancelText: '取消',
        onOk: function () {
          that.submitCheck();
        },
        onCancel: function () {
          that.isShowEditMore = true;
          that.isShowSubmit = true;
          that.isShowReconfigure = true;
          that.getCheckList();
        }
      });
    },
    // tab4 -- 重新配置
    reconfigureBtn () {
      let that = this;
      this.$confirm({
        title: '提示',
        content: '重新配置完成，设备未数据未校对，可编辑，是否重新配置？',
        okText: '确定',
        cancelText: '取消',
        onOk: function () {
          that.relocationConfig();
        },
        onCancel: function () {
        }
      });
    },
    // tab4 提交按钮接口
    submitCheck () {
      // this.queryCheckSubmitParmas.originalPointKeyList = this.selectedRowKeysTab4;
      submitConfigPoint(this.queryCheckSubmitParmas).then(res => {
        if (res.result_code == '1') {
          this.isShowEditMore = true;
          this.isShowSubmit = true;
          this.isShowReconfigure = true;
          this.getCheckList();
          this.getHeaderList();
          this.$message.success('操作成功!');
        }
      }).catch(() => {
      });
    },
    // tab4 重新配置按钮接口
    relocationConfig () {
      // this.queryCheckSubmitParmas.originalPointKeyList = this.selectedRowKeysTab4;
      relocationConfigPoint(this.queryCheckSubmitParmas).then(res => {
        if (res.result_code == '1') {
          this.isShowEditMore = true;
          this.isShowSubmit = true;
          this.isShowReconfigure = true;
          this.getCheckList();
          this.getHeaderList();
          this.$message.success('操作成功!');
        }
      }).catch(() => {
      });
    },
    close () {
      this.visible = false;
      this.activeTabIndex = 0;
      Object.assign(this.$data, this.$options.data());
    },
    async init (type, row) {
      this.visible = true;
      this.queryPointParams.snList[0] = row.sn;
      this.queryDeviceParams.snList[0] = row.sn;
      this.queryConfigParams.snList[0] = row.sn;
      this.queryCheckParams.snList[0] = row.sn;
      this.editParamsTab1.snList[0] = row.sn;
      this.queryHeaderParams.sn = row.sn;
      this.queryCheckSubmitParmas.snList[0] = row.sn;
      if (this.tableHeight > 600) {
        this.tableHeight = 600;
      }
      await updatePointConfigStatusByDeviceAndPointMapping({
        sn: row.sn
      }).then(res => {});
      this.getPointList();
      this.getHeaderList();
      this.getDictList();
    },
    fileUpload () {
      this.$message.success('文件导入成功');
      this.getPointList();
    },
    onKeydownEvent (ev, row) {
      if (row.reverse != 0 && row.reverse != 1) {
        row.reverse = '';
      }
    }
  }
};
</script>

<style lang='less' scoped >
.test{
  display: flex;
  justify-content: space-between;
}
.tabs{
  padding: 24px 24px 0;
  .tab-item{
    width: 240px;
    height: 100px;
    border-radius: 6px;
    border: 1px solid var(--zw-border-color--default);
    .tab-data{
      display: flex;
      justify-content: center;
      align-items: flex-start;
      span {
         font-family: Source Han Sans;
        font-weight: normal;
        line-height: 22px;
        letter-spacing: 0em;
        font-feature-settings: "kern" on;
        font-size: 14px;
        color: var(--zw-text-3-color--default)
      }
      .title {
       font-size: 18px;
       line-height: 24px;
       color: var(--zw-text-3-color--default);
      }
    }
  }
  .tab-item-active{
    background: var(--zw-card-light-bg-color--default);
    border: 1px solid var(--zw-primary-color--default);
  }
  .split-line{
    width: 60px;
    height: 1px;
    opacity: 1;
    background: var(--zw-divider-color--default);
    margin: 0 24px;
  }
}
.title-item{
  width: 300px;
  margin-left: 24px;
  .label{
    color: var(--zw-text-2-color--default);
    font-size: 14px;
    margin-right: 12px;
  }
  .value{
    font-size: 24px;
    font-weight: bold;
    color: var(--zw-text-3-color--default);
    display: inline-block;
    width: 60%;
    overflow: hidden;
    text-overflow: ellipsis;
    vertical-align: top;
  }
}

.droparea:-moz-drag-over {
  border: 1px solid black;
}

.btnBox {
  display: flex;
  flex-direction: row;
  align-items: center;
  padding: 10px 12px;
  gap: 10px;
  flex-grow: 1;
  align-self: stretch;
 .operation-btn-hover {
    position: static;
    left: 0px;
    top: 0px;
    width: 146px;
    height: 60px;
    opacity: 1;
    color: #3662EC;
  }
}

.tab-status {
  margin-right: 10px;
}
.bound-device.drawer-box {
  :deep(.ant-drawer-body){
    overflow-x: hidden;
  }
}
</style>
