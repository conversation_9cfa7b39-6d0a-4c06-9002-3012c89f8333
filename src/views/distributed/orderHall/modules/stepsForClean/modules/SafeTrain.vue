<template>
  <div>
    <span class='item-content-title'>清洗前对劳务人员进行保险核查及安全培训交底</span>
    <a-form-model
      :model="form"
      ref="SafeTrain"
      :wrapperCol="{ style: 'width: calc(100% - 150px)' }"
      :rules='rules'
      labelAlign='left'
      class='mt-24'
    >
      <a-row :gutter="24">
        <a-col :span='24'>
          <a-form-model-item label="上传图片" prop="insureOneFileList">
            <DetailEmpty v-if='insureOneFileListShow' />
            <uploadFileView
              v-else
              v-model="form.insureOneFileList"
              listType="picture-card"
              :disabled="isDisabled"
              uploadText='上传保险图片'
              accept=".jpg,.png,.jpeg,.bmp,.JPG,.PNG,.JPEG,.BMP"
              :max-num='1'
              @change='checkPickure("insureOneFileList")'
            />
            <ExampleImg name='step1_insure.png' imgStyle='position: absolute;left: 112px;top:0'/>
          </a-form-model-item>
        </a-col >
        <a-col :span='24'>
          <a-form-model-item  class='require-none' prop="cultivateOneFileList" >
             <template slot='label'><span style='width: 63px;display: inline-block'></span></template>
            <template>
              <DetailEmpty v-if='cultivateOneFileListShow' />
              <uploadFileView
                v-else
                v-model="form.cultivateOneFileList"
                listType="picture-card"
                :disabled="isDisabled"
                uploadText='上传培训图片'
                accept=".jpg,.png,.jpeg,.bmp,.JPG,.PNG,.JPEG,.BMP"
                tip="单张图片不能超过10MB，仅支持上传jpg、jpeg、png、bmp格式"
                :max-num='1'
                @change='checkPickure("cultivateOneFileList")'
                extraTipSty='width: 100vw'
              />
            </template>
            <ExampleImg name='step1_safe.png' imgStyle='position: absolute;left: 112px;top:0'/>
          </a-form-model-item>
        </a-col>
      </a-row>
    </a-form-model>
  </div>
</template>

<script>
import uploadFileView from '@/components/com/fileUploadView';
import ExampleImg from '@views/distributed/orderHall/modules/stepsForClean/modules/ExampleImg.vue';
import DetailEmpty from '@views/distributed/orderHall/modules/stepsForClean/modules/DetailEmpty.vue';
export default {
  components: { uploadFileView, ExampleImg, DetailEmpty },
  inject: ['type'],
  props: {
    componentData: {
      type: Object,
      default: () => ({})
    },
    isDisabled: {
      type: Boolean,
      default: false
    }
  },
  data () {
    return {
      form: {
        insureOneFileList: undefined,
        cultivateOneFileList: undefined
      },
      rules: {
        insureOneFileList: [
          { required: true, message: '请上传图片', trigger: 'change' }
        ],
        cultivateOneFileList: [
          { required: true, message: '请上传图片', trigger: 'change' }
        ]
      }
    };
  },
  mounted () {
    // console.log('cleanDetailVo', this.componentData.cleanDetailVo || {});
    const cleanDetailVo = this.componentData.cleanDetailVo || {};
    Object.assign(this.form, { insureOneFileList: cleanDetailVo.insureOneFileList, cultivateOneFileList: cleanDetailVo.cultivateOneFileList });
  },
  methods: {
    checkPickure (propertyName) {
      this.$nextTick(() => {
        this.$refs.SafeTrain.validateField(propertyName);
      });
    },
    validateForm () {
      let result;
      this.$refs.SafeTrain.validate(valid => {
        result = valid;
      });
      return result;
    }

  },
  computed: {
    insureOneFileListShow () {
      return this.isDisabled && ['3', '7', '8'].includes(this.type()) && !(this.form.insureOneFileList && this.form.insureOneFileList.length);
    },
    cultivateOneFileListShow () {
      return this.isDisabled && ['3', '7', '8'].includes(this.type()) && !(this.form.cultivateOneFileList && this.form.cultivateOneFileList.length);
    }
  }
};
</script>

<style lang='less' scoped>

</style>
