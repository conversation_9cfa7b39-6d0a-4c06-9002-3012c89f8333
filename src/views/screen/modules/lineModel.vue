<template>
  <div class="solar-eye-screen-bar">
    <div class="solar-eye-screen-title">{{ type == '0' ? '日发电量' : type == '1' ? '月发电量' : '年发电量' }}</div>
    <div class="line-bar-model" ref="lineAndBar"></div>
  </div>
</template>
<script>
import { getDaySumElec, getMonthSumElec, getYearSumElec } from '@/api/screen/screen';
export default {
  props: {
    // 日、月、年发电量区分 0,1,2
    type: {
      type: Number
    }
  },
  methods: {
    // 数据请求
    getElec (echarts) {
      let self = this;
      if (self.type == '0') {
        getDaySumElec({}).then((res) => {
          let data = res.result_data ? res.result_data : { 'x': [], elec: '', pow: '' };
          let xData = data.hasOwnProperty('x') ? data.x : [];
          let elec = data.elec.value ? data.elec.value : [];
          let elecUnit = data.elec.unit ? data.elec.unit : '万kWh';
          let pow = data.pow.value ? data.pow.value : [];
          let powUnit = data.pow.unit ? data.pow.unit : 'MW';
          self.initLineBar(echarts, xData, [elec, pow], [elecUnit, powUnit]);
        });
      } else if (self.type == '1') {
        getMonthSumElec({}).then((res) => {
          let data = res.result_data ? res.result_data : { 'x': [], data: '' };
          let xData = data.hasOwnProperty('x') ? data.x : [];
          let elec = data.data.value ? data.data.value : [];
          let unit = data.data.unit ? data.data.unit : '万kWh';
          self.initLineBar(echarts, xData, [elec], [unit]);
        });
      } else {
        getYearSumElec({}).then((res) => {
          let data = res.result_data ? res.result_data : { 'x': [], yearElec: '' };
          let xData = data.hasOwnProperty('x') ? data.x : [];
          let elec = data.yearElec.value ? data.yearElec.value : [];
          let unit = data.yearElec.unit ? data.yearElec.unit : '万kWh';
          let totalElec = data.totalElec ? data.totalElec : '';
          self.$emit('setTotal', totalElec);
          self.initLineBar(echarts, xData, [elec], [unit]);
        });
      }
    },
    initLineBar (echarts, xData, datas, units) {
      let electicLenged = {
        itemWidth: 10,
        itemHeight: 10,
        top: '7%',
        left: this.type == '0' ? '30%' : 'center',
        textStyle: {
          textAlign: 'center',
          fontSize: 14,
          color: 'rgba(255,255,255,0.7)'
        },
        data: [{ name: '发电量', icon: 'roundRect' }]
      };
      let legend =
        this.type == '0'
          ? [
            electicLenged,
            {
              top: '7%',
              right: '32%',

              textStyle: {
                textAlign: 'center',
                fontSize: 14,
                color: 'rgba(255,255,255,0.7)'
              },
              data: [{
                name: '功率'
                // icon: 'image://data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABEAAAAJCAYAAAGj7/eaAAAAAXNSR0IArs4c6QAAAERlWElmTU0AKgAAAAgAAYdpAAQAAAABAAAAGgAAAAAAA6ABAAMAAAABAAEAAKACAAQAAAABAAAAEaADAAQAAAABAAAACQAAAADeA09CAAAAzUlEQVQoFWNgAIL/M0s+g2gmEIEbMP6fVfqU4f9/KbgSRsZnYPb/maVlIAziMAJN+w9XAWOgqEAWBNreCeMDNZczpnd3gfgE3ALRwgjT+X/VKmaGD6dOgvkCZuaMYWF/QWwWmAKG96f+oLHBmrG7Fq4S6CIYG91bjOk9YDmEQwXNWIDKz4IxiA0FcBNgAlDH7mH4z+AAE4PTjAwHGATMXGAegIkz/p9d4cTw758dTAAYzqkocQGXgDJAcfOfcTZcmInpEMKpcFHSGVTxDgAxbk8bJybj/AAAAABJRU5ErkJggg==',
              }
              ]
            }
          ]
          : [electicLenged];
      // const lineAndBar = echarts.init(this.$refs.lineAndBar);
      if (!this.$refs.lineAndBar) {
        // dom 不存在时不画
        return;
      }
      // 不重复初始化
      let lineAndBar = echarts.getInstanceByDom(this.$refs.lineAndBar);
      if (!lineAndBar) {
        lineAndBar = echarts.init(this.$refs.lineAndBar);
        this.$once('hook:beforeDestroy', function () {
          echarts.dispose(lineAndBar);
        });
      }
      let option = {
        // title: {
        //   text: (this.type == '0' ? '日发电量' : (this.type == '1' ? '月发电量' : '年发电量')),
        //   top: '0',
        //   left: '0',
        //   textStyle: {
        //     fontSize: 16,
        //     color: 'rgba(255, 255, 255, 0.8)'
        //   }
        // },
        tooltip: {
          trigger: 'axis',
          show: true,
          borderColor: '#69C7EB',
          borderWidth: '1',
          axisPointer: {
            type: 'shadow',
            shadowStyle: {
              color: 'rgba(150,150,150,0.1)'
            }
          },
          backgroundColor: 'rgba(55, 71, 89, 0.9)',
          textStyle: {
            color: '#ffffff'
          },
          formatter: function (params) {
            var str = "<span style='text-align:left'>" + params[0].name + '</span><br/>';
            for (var i = 0; i < params.length; i++) {
              if (params[i].seriesName !== '') {
                str +=
                  `<div class='solar-eye-legend'><span class='legend-marker' style='background:${
                    i == 0 ? '#69BDE9' : '#FEA559'
                  }'></span>` +
                  "<span style='margin-right:24px'>" +
                  params[i].seriesName +
                  '</span>' +
                  (params[i].value != '--' ? Number(params[i].value).toLocaleString() : params[i].value) +
                  '</div>';
              }
            }
            return str;
          }
        },
        grid: {
          x: 10,
          y: 60,
          x2: 10,
          y2: 10,
          containLabel: true
        },
        legend: legend,
        xAxis: [
          {
            type: 'category',
            data: xData,
            axisPointer: {
              type: 'shadow'
            },
            axisLine: {
              show: false,
              lineStyle: {
                color: 'rgba(255, 255, 255, 0.7)',
                fontSize: 12
              }
            },
            axisTick: false
          }
        ],
        yAxis: this.getYAxis(units),
        dataZoom: [
          {
            type: 'inside',
            show: false
          }
        ],
        series: this.getSeries(echarts, datas)
      };
      lineAndBar.setOption(option, true);
    },
    // 柱状图和折线
    getSeries (echarts, datas) {
      let series = [
        {
          name: '发电量',
          type: 'bar',
          barMaxWidth: 20,
          itemStyle: {
            normal: {
              color: new echarts.graphic.LinearGradient(
                0,
                0,
                0,
                1,
                [
                  {
                    offset: 1,
                    color: this.type == '0' ? 'rgba(114, 170, 255, 0.9)' : '#158ac6' // 0% 处的颜色
                  },
                  {
                    offset: 0,
                    color: this.type == '0' ? 'rgba(113, 217, 255, 0.9)' : '#27C6C8' // 100% 处的颜色
                  }
                ],
                false
              )
            }
          },
          z: 9,
          data: datas[0]
        }
      ];
      // 日统计图时-会有趋势
      if (this.type == '0') {
        series.push({
          name: '功率',
          type: 'line',
          yAxisIndex: 1,
          lineStyle: {
            color: '#FEA559'
          },
          colorBy: 'data',
          symbolSize: 9,
          symbol: 'image://data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAkAAAAKCAYAAAERAkQoAAAAAXNSR0IArs4c6QAAAERlWElmTU0AKgAAAAgAAYdpAAQAAAABAAAAGgAAAAAAA6ABAAMAAAABAAEAAKACAAQAAAABAAAACaADAAQAAAABAAAACgAAAABo4uNxAAAAyUlEQVQYGWWQTQ4BQRCF69UQeyt2YiMO4Aq2sxM/ERzBhYgIJnaWzuAAYsHWir2fmadraPFTSXe/771OuqpF/opRp66MkzU4a+8pLENweF9DMmvRKAPI1QS47Bd4uxwNrJDNFVVu1+kTX7tnLvolW2aD89aJlHwKkLN6MMO0mvgsBXToDdMwYNStShxPXLu1dyjYSBD00Jxu3UPtEcmBD39PAGP3FsPf4JsZqkAbbvT0D75DN4/5lvsg7Su5h0JU3LQ70czK+rH8AUjISmYwhFThAAAAAElFTkSuQmCC',
          smooth: true,
          areaStyle: {
            normal: {
              color: new echarts.graphic.LinearGradient(
                0,
                0,
                0,
                1,
                [
                  {
                    offset: 0,
                    color: 'rgba(254, 165, 89, 0.25)'
                  },
                  {
                    offset: 1,
                    color: 'rgba(255, 209, 146, 0)'

                  }
                ],
                false
              ),
              shadowColor: 'rgba(0, 0, 0, 0.1)',
              shadowBlur: 10
            }
          },
          showSymbol: false,
          z: 3,
          connectNulls: true,
          data: datas[1]
        });
      }
      return series;
    },
    // 是否双坐标
    getYAxis (units) {
      let yAxis = [
        {
          type: 'value',
          name: units[0],
          splitLine: {
            show: true,
            lineStyle: {
              color: 'rgba(96, 130, 163, 0.19)'
            }
          },
          axisLine: {
            show: false,
            lineStyle: {
              color: 'rgba(255, 255, 255, 0.7)',
              fontSize: 12
            }
          },
          minInterval: 1,
          min: function (value) {
            if (isNaN(value.min) || value.min === Infinity) {
              return 0;
            }
          },
          max: function (value) {
            if (isNaN(value.max) || value.max === -Infinity) {
              return 100;
            }
          },
          axisTick: false
        }
      ];
      // 日统计图时-双坐标
      if (this.type == '0') {
        yAxis.push({
          type: 'value',
          name: units[1],
          yAxisIndex: 1,
          splitLine: {
            show: false,
            lineStyle: {
              color: '#FF8F33'
            }
          },
          axisLine: {
            show: false,
            lineStyle: {
              color: 'rgba(255, 255, 255, 0.7)',
              fontSize: 12
            }
          },
          minInterval: 1,
          min: function (value) {
            if (isNaN(value.min) || value.min === Infinity) {
              return 0;
            }
          },
          max: function (value) {
            if (isNaN(value.max) || value.max === -Infinity) {
              return 100;
            }
          },
          axisTick: false
        });
      }
      return yAxis;
    }
  }
};
</script>

<style lang="less" scoped>
.line-bar-model {
  width: 100%;
  height: 85%;
}
</style>
