<template>
  <div id="AlarmCenter">
    <a-spin :spinning="loading">
      <div class="alarm-head">
        <div class="alarm-tab" v-for="(item, index) in tabList" :key="index"
          @click="setActiveTab(item.tabCode, index, item.name)" :class="activeTab == item.tabCode ? 'active' : ''">
          <div class="image-box">
            <img :src="getPng(item.imgName)" />
          </div>
          <div :style="{ paddingTop: ['22', '23', '24', '25'].includes(userInfo.companyId) ? '12px' : '0' }">
            <div class="alarm-title">
              <span class="alarm-type">{{ item.name }}</span>
              <a-popover placement="right">
                <template slot="content">
                  <div style="max-width: 290px">
                    {{ item.mark }}
                  </div>
                </template>
                <span><svg-icon class="info-icon" iconClass="health-info"></svg-icon></span>
                <!-- <img class="info-icon" :src="getPng('info')" type="info-circle" /> -->
              </a-popover>
            </div>
            <div class="alarm-num">{{ item.sum }}</div>
            <div class="alarm-data" v-if="!(['22', '23', '24', '25'].includes(userInfo.companyId))">
              今日新增<span class="alarm-add">{{ item.sumToday }}</span>
              <div class="split-line"></div>
              <a-popover placement="right">
                <template slot="content">
                  <div>
                    {{ month }}月当前时间段和{{ lastMonth }}月同时间段对比{{ item.isAdd || item.noRate ? '新增了' : '下降了' }}{{ item.rate +
                      (item.noData ? '' : '%') }}
                  </div>
                </template>
                <span style="z-index:1">月环比</span>
              </a-popover>

              <span :class="item.noRate ? 'no-rate' : (item.isAdd ? 'alarm-add' : 'alarm-add down')">{{ item.rate +
                (item.noData ? '' : '%') }}</span>
              <img v-if="!item.noRate" :src="getPng(item.isAdd ? 'up' : 'down')">
            </div>
          </div>
        </div>
        <div v-if="menus.length > 0" class="high-bg" :style="{ left: activeIndex * 20 + '%' }"></div>
        <div v-if="menus.length > 0" class="high-border" :style="{ left: (activeIndex * 20) + 4.3 + '%' }"></div>
      </div>
      <div class="alarm-body">
        <alarm-events ref="alarmEvents" v-if="['1', '2', '3', '4'].includes(this.activeTab)" :type="activeTab"
          :activeTotal="activeTotal" :tabName="tabName" @resetParams="resetParams" @searchDataChange="searchDataChange"
          @loadingEnd="loadingEnd" @stationChange="stationChange" @paramsChange="paramsChange"
          :stationParams="stationParams" />
        <safety-events ref="safetyEvents" v-if="this.activeTab == '5'" @searchDataChange="searchDataChange"
          @paramsChange="paramsChange" @resetParams="resetParams" @stationChange="stationChange"
          :stationParams="stationParams" @loadingEnd="loadingEnd" />
      </div>
    </a-spin>
  </div>
</template>
<script>
import { dataStatisticalSumForRemarkTab, dataStatisticalRateForRemarkTab } from '@/api/health/AlarmEvents.js';
import { USER_INFO } from '@/store/mutation-types';
import AlarmEvents from './AlarmEvents';
import SafetyEvents from './safetyEvents';
import { mixin } from '@/utils/mixin.js';
export default {
  name: 'AlarmCenter',
  components: {
    AlarmEvents,
    SafetyEvents
  },
  mixins: [mixin],
  data () {
    return {
      userInfo: {},
      activeTab: '1',
      activeIndex: 0,
      activeTotal: 0,
      menus: [],
      searchData: {},
      loading: false,
      faultStatus: '1',
      elecEnsureType: ['0'],
      searchTime: [],
      closedType: '',
      alarmStatus: '01',
      stationParams: {
        psId: '',
        psName: '',
        nodeType: '',
        orgCode: '',
        parentId: '',
        parentName: '',
        parentType: '',
        parentOrgCode: ''
      },
      tabName: '',
      paramsArr: ['elecEnsureType', 'searchTime', 'closedType', 'alarmStatus', 'faultStatus'],
      tabList: [
        {
          name: '故障停机',
          perms: 'alarm:stop',
          mark: '电站设备在运行过程中因某种原因停止工作',
          sum: 0,
          rate: '0',
          sumToday: 0,
          tabCode: '1',
          isAdd: true,
          imgName: 'stop'
        },
        {
          name: '通讯中断',
          perms: 'alarm:comloss',
          mark: '数采设备等通讯设备因设备故障、软件升级或网络故障等问题导致平台数据异常',
          sum: 0,
          rate: '0',
          sumToday: 0,
          tabCode: '2',
          isAdd: true,
          imgName: 'comloss'
        },
        {
          name: '隐患运行',
          perms: 'alarm:hidden',
          mark: '电站设备因某些模块如风扇、开关等出现问题导致异常现象',
          sum: 0,
          rate: '0',
          sumToday: 0,
          tabCode: '4',
          isAdd: true,
          imgName: 'hidden'
        },
        {
          name: '低效缺陷',
          perms: 'alarm:inefficient',
          mark: '某些组串因非固定遮挡、破碎等问题导致发电低于正常组串',
          sum: 0,
          rate: '0',
          sumToday: 0,
          tabCode: '3',
          isAdd: true,
          imgName: 'inefficient'
        },
        {
          name: '安全隐患',
          perms: 'alarm:safety',
          mark: '因火灾、洪水等灾害导致电站存在安全问题',
          sum: 0,
          rate: '0',
          sumToday: 0,
          tabCode: '5',
          isAdd: true,
          imgName: 'safe'
        }
      ],
      month: '',
      lastMonth: '',
      loadingNum: 0
    };
  },
  created () {
    this.loading = true;
    this.setTabList();
    this.getDate();
    this.userInfo = Vue.ls.get(USER_INFO);
  },

  methods: {
    getDate () {
      let date = new Date();
      this.month = date.getMonth() + 1;
      this.lastMonth = this.month == 1 ? 12 : this.month - 1;
    },
    getSpecifyDate (num) {
      let date = new Date();
      let base = new Date(date).getTime();
      let oneDay = 24 * 3600 * 1000;
      let data = {
        month: new Date(base - oneDay * num).getMonth() + 1,
        day: new Date(base - oneDay * num).getDate()
      };
      return data;
    },
    setTabList () {
      this.tabList = this.tabList.filter(item => {
        return this.show(item.perms);
      });
      this.menus = this.tabList.map(item => {
        return item.tabCode;
      });
      if (this.menus.length > 0) {
        this.activeTab = this.menus[0];
        this.tabName = this.tabList[0].name;
      } else {
        this.activeTab = '';
        this.loading = false;
      }
    },
    searchDataChange (searchData) {
      let params = Object.assign({}, searchData);
      delete params.curPage;
      delete params.size;
      if (this.activeTab == '5') {
        delete params.alarmType;
        params.deviceType = '';
      }
      if (this.menus.length > 0) {
        this.loading = true;
        dataStatisticalSumForRemarkTab(params).then(res => {
          this.tabList.forEach(item => {
            let data = res.result_data.list.find(ele => { return item.tabCode == ele.tabCode; });
            item.sum = data ? data.sum : 0;
          });
          this.activeTotal = 0;
          for (let key in res.result_data.map) {
            this.activeTotal += res.result_data.map[key];
          }
          this.$refs.alarmEvents.setDeviceTotal(res.result_data.map);
          // this.loadingEnd();
        }).catch(() => { this.loadingEnd(); });
        let obj = Object.assign({}, params);
        if (obj.alarmStatus != '01') {
          delete obj.faultStatus;
        }
        dataStatisticalRateForRemarkTab(obj).then(res => {
          this.tabList.forEach(item => {
            let data = res.result_data.find(ele => { return item.tabCode == ele.tabCode; });
            if (data) {
              item.sumToday = data.sumToday;
              item.rate = data.rate;
              if (item.rate == '0' || item.rate == '--') {
                item.noRate = true;
                item.noData = item.rate != '0';
              } else {
                item.noRate = false;
                item.noData = false;
                item.isAdd = item.rate * 1 > 0;
                item.rate = item.rate * 1 > 0 ? item.rate : item.rate * -1;
              }
            } else {
              item.sumToday = 0;
              item.noRate = true;
              item.noData = true;
              item.rate = '--';
            }
          });
          // this.loadingEnd();
        }).catch(() => { this.loadingEnd(); });
      }
    },
    setActiveTab (tabCode, index, tabName) {
      if (tabCode != this.activeTab) {
        let changePage = false;
        if (tabCode == '5' || this.activeTab == '5') {
          changePage = true;
        }
        this.activeTab = tabCode;
        this.tabName = tabName;
        this.activeIndex = index;
        this.$nextTick(() => {
          if (tabCode != '5') {
            if (this.$refs.alarmEvents) {
              if (changePage) {
                this.$refs.alarmEvents.deviceType = '';
                this.$refs.alarmEvents.$refs.faultStatus.checked = this.faultStatus == '1';

                this.paramsArr.forEach(item => {
                  this.$refs.alarmEvents[item] = this[item];
                });
                this.setStationParams('alarmEvents');
              }
              this.$refs.alarmEvents.getTableHeight();
              this.$refs.alarmEvents.alarmRemark = '';
              // 低效缺陷 默认选中电站
              this.$refs.alarmEvents.deviceType = (tabCode == '3' ? '11' : '');
              this.$refs.alarmEvents.sortFiled = '';
              this.$refs.alarmEvents.sortKind = '';
              this.$refs.alarmEvents.alarmReason = '';
              this.$refs.alarmEvents.alarmReasonString = '';
              // if (tabCode != '2') {
              this.$refs.alarmEvents.getAlarmReasionOptions();
              // }
              this.$nextTick(() => {
                !changePage && this.$refs.alarmEvents.pageChange(1, true);
              });
            }
          } else {
            if (changePage) {
              this.paramsArr.forEach(item => {
                this.$refs.safetyEvents[item] = this[item];
              });
              let notPsId = this.stationParams.nodeType == '1' || this.stationParams.nodeType == '2';
              if (!notPsId) {
                this.stationParams.psId = this.stationParams.parentId;
                this.stationParams.psName = this.stationParams.parentName;
                this.stationParams.nodeType = this.stationParams.parentType;
                this.stationParams.depCodes = this.stationParams.parentOrgCode;
              }
              this.setStationParams('safetyEvents');
            }
            this.$nextTick(() => {
              this.$refs.safetyEvents.pageChange(1);
            });
          }
        });
      }
    },
    resetParams () {
      this.elecEnsureType = ['0'];
      this.searchTime = [];
      this.closedType = '';
      this.alarmStatus = '01';
    },
    paramsChange (prop, val) {
      this[prop] = val;
    },
    stationChange (stationParams) {
      this.stationParams = stationParams;
    },
    setStationParams (prop) {
      this.$refs[prop].psId = this.stationParams.psId;
      this.$refs[prop].psName = this.stationParams.psName;
      this.$refs[prop].nodeType = this.stationParams.nodeType;
      this.$refs[prop].depCodes = this.stationParams.orgCode;
    },
    show (perms) {
      return this.showHandle(perms);
    },
    getPng (name) {
      let navTheme = name == 'up' || name == 'down' ? '' : '_' + this.navTheme;
      return require('@/assets/images/health/alarmEvents/' + name + navTheme + '.png');
    },
    loadingEnd () {
      this.loadingNum++;
      if (this.loadingNum == 1) {
        this.loadingNum = 0;
        this.loading = false;
      }
    }
  }

};
</script>
<style lang="less" scoped>
.alarm-head {
  display: flex;
  height: 130px;
  box-shadow: 0px 2px 11px 0px rgba(0, 0, 0, 0.05);
  border-radius: 4px;
  background: var(--zw-card-bg-color--default);
  margin-bottom: 16px;

  .alarm-tab {
    width: 20%;
    padding: 20px 0 20px 16px;
    display: flex;
    cursor: pointer;
    position: relative;

    .image-box {
      width: 80px;
      // height: 80px;
      margin-right: 12px;
      display: flex;
      align-items: center;
      justify-content: center;

      img {
        width: 80px;
      }
    }

    .alarm-type {
      color: var(--zw-text-1-color--default);
      font-size: 14px;
      margin-right: 8px;
      font-weight: 600;
    }

    .alarm-num {
      font-weight: 600;
      color: var(--zw-text-1-color--default);
      line-height: 42px;
      font-size: 34px;
      margin: 4px 0;
    }

    .alarm-data {
      display: flex;
      font-weight: 400;
      color: var(--zw-text-2-color--default);
      line-height: 20px;
      align-items: center;
      font-size: 12px;

      .alarm-add {
        color: var(--zw-warning-color--normal);
        font-weight: 600;
        margin-left: 4px;
      }

      .no-rate {
        font-weight: 600;
        margin-left: 4px;
      }

      .down {
        color: var(--zw-proceed-color--normal);
      }

      .split-line {
        width: 1px;
        height: 12px;
        background: var(--zw-divider-color--default);
        margin: 0 4px;
      }
    }

    .info-icon {
      color: var(--zw-text-2-color--default);
      font-size: 14px;
    }

  }

  .alarm-tab:hover {
    background: var(--zw-card-light-bg-color--default);
  }

  .active:hover {
    background: transparent;
  }

  .high-bg {
    height: 90px;
    width: 20%;
    background: linear-gradient(180deg, var(--zw-card-bg-color--default) 0%, var(--zw-primary-color--default) 100%);
    opacity: 0.1;
    position: absolute;
    top: 40px;
    cursor: pointer;
  }

  .high-border {
    width: 11.4%;
    height: 3px;
    background: var(--zw-primary-color--default);
    border-radius: 2px;
    position: absolute;
    top: 127px;
    transition: left 0.2s;
    cursor: pointer;
  }

  .alarm-tab+.alarm-tab::after {
    height: 74px;
    width: 1px;
    margin: 28px 0px 28px -16px;
    border-left: 1px solid var(--zw-border-color--default);
    content: '';
    position: absolute;
    top: 0;
  }

}

@media screen and (min-width:1366px) and (max-width:1560px) {
  .alarm-head {
    .alarm-tab {
      padding: 20px 8px;

      .image-box {
        margin-right: 0px;
      }

    }

    .alarm-tab+.alarm-tab::after {
      margin: 28px 0px 28px -8px;
    }
  }
}
</style>
