import { axios } from '@/utils/erp-request';

/**
 * 获取地图配置的中心点和路径
 * @param {Object} data
 */
export function getAllRouteInfo (data) {
  return axios({
    url: '/system/v1/portal/getAllRouteInfo',
    method: 'post',
    data
  });
}
/**
 * 获取地图上所有实体电站的点信息
 * @param {Object} data
 */
export function getAllStationInfo (data) {
  return axios({
    url: '/system/v1/portal/getAllStationInfo',
    method: 'post',
    data
  });
}
/**
 * 综合页面-日发电量
 * @param {Object} data
 */
export function getDaySumElec (data) {
  return axios({
    url: '/system/v1/portal/getDaySumElec',
    method: 'post',
    data
  });
}
/**
 * 综合页面-环境指标
 * @param {Object} data
 */
export function getEnvironmentalData (data) {
  return axios({
    url: '/system/v1/portal/getEnvironmentalData',
    method: 'post',
    data
  });
}
/**
 * 综合页面-等效小时数排行
 * @param {Object} data
 */
export function getHoursRank (data) {
  return axios({
    url: '/system/v1/portal/getHoursRank',
    method: 'post',
    data
  });
}
/**
 * 综合页面-月发电量
 * @param {Object} data
 */
export function getMonthSumElec (data) {
  return axios({
    url: '/system/v1/portal/getMonthSumElec',
    method: 'post',
    data
  });
}
/**
 * 综合页面-计划完成率
 * @param {Object} data
 */
export function getPlanFinishRate (data) {
  return axios({
    url: '/system/v1/portal/getPlanFinishRate',
    method: 'post',
    data
  });
}
/**
 * 综合页面-电站运行情况
 * @param {Object} data
 */
export function getStationStatus (data) {
  return axios({
    url: '/system/v1/portal/getStationStatus',
    method: 'post',
    data
  });
}
/**
 * 综合页面-获取总装机容量
 * @param {Object} data
 */
export function getSumScale (data) {
  return axios({
    url: '/system/v1/portal/getSumScale',
    method: 'post',
    data
  });
}
/**
 * 综合页面-年发电量
 * @param {Object} data
 */
export function getYearSumElec (data) {
  return axios({
    url: '/system/v1/portal/getYearSumElec',
    method: 'post',
    data
  });
}
/**
 * 单站页面-上月PR
 * @param {Object} data
 */
export function getStationPr (data) {
  return axios({
    url: '/system/v1/portal/getStationPr',
    method: 'post',
    data
  });
}
/**
 * 单站页面-电站运行图
 * @param {Object} data
 */
export function getStationRunningData (data) {
  return axios({
    url: '/system/v1/portal/getStationRunningData',
    method: 'post',
    data
  });
}
/**
 * 单站页面-发电量/功率
 * @param {Object} data
 */
export function getStationTodayElec (data) {
  return axios({
    url: '/system/v1/portal/getStationTodayElec',
    method: 'post',
    data
  });
}
/**
 * 单站页面-天气数据
 * @param {Object} data
 */
export function getStationWeatherInfo (data) {
  return axios({
    url: '/system/v1/portal/getStationWeatherInfo',
    method: 'post',
    data
  });
}
/**
 * 单站页面-告警/故障
 * @param {Object} data
 */
export function getStationWarnInfo (data) {
  return axios({
    url: '/system/v1/portal/getStationWarnInfo',
    method: 'post',
    data
  });
}
/**
 * 单站页面-计划/消缺完成率
 * @param {Object} data
 */
export function getStationPlanDefectInfo (data) {
  return axios({
    url: '/system/v1/portal/getStationPlanDefectInfo',
    method: 'post',
    data
  });
}
/**
 * 下载附件
 * @param {Object} data
 */
export function downloadBizAnnex (data) {
  return axios({
    url: '/system/v1/annex/downloadBizAnnex',
    method: 'post',
    data
  });
}

/**
 * 公告列表 - 图表数据
 * @param {Object} data
 */
export function getAnnounceList (data) {
  return axios({
    url: '/system/v1/announceManager/getList',
    method: 'post',
    data
  });
}

/**
 * 插入公告阅读记录
 * @param {Object} data
 */
export function insertAnnounceReadRecord (data) {
  return axios({
    url: '/system/v1/announceManager/insertAnnounceReadRecord',
    method: 'post',
    data
  });
}

/**
 * 公告详情
 * @param {Object} data
 */
export function getAnnounce (data) {
  return axios({
    url: '/system/v1/announceManager/getAnnounce',
    method: 'post',
    data
  });
}
/**
 * 通知公告列表
 * @param {Object} data
 */
export function getListByTitle (data) {
  return axios({
    url: '/system/v1/announceManager/getListByTitle',
    method: 'post',
    data
  });
}
/**
 * 查询计划/任务/工单数据 - 图表数据
 * @param {Object} data
 */
export function getPlanTaskWorkOrderCount (data) {
  return axios({
    url: '/system/v1/home/<USER>',
    method: 'post',
    data
  });
}
/**
 * 超发分成电站数据 - 图表数据
 * @param {Object} data
 */
export function getSuperfastData (data) {
  return axios({
    url: '/system/v1/home/<USER>',
    method: 'post',
    data
  });
}
/**
 * 顶部统计数据
 * @param {Object} data
 */
export function sumData (data) {
  return axios({
    url: '/system/v1/home/<USER>',
    method: 'post',
    data
  });
}
/**
 * 上网电量/计划电量 - 图表数据
 * @param {Object} data
 */
export function getElecLineDataAndComments (data) {
  return axios({
    url: '/system/v1/home/<USER>',
    method: 'post',
    data
  });
}
/**
 * 上网电量/计划电量 - 图表数据
 * @param {Object} data
 */
export function getElecLineData (data) {
  return axios({
    url: '/system/v1/home/<USER>',
    method: 'post',
    data
  });
}
/**
 * 电站评分 - 图表数据
 * @param {Object} data
 */
export function getComments (data) {
  return axios({
    url: '/system/v1/home/<USER>',
    method: 'post',
    data
  });
}
