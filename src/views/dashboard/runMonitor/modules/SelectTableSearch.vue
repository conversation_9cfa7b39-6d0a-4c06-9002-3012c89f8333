<template>
  <div class="solayeye__table-search-select">
    <div class="solayeye__table-search-select--item"
         v-if="isSelectAll"
         :class="selectAllStatus? 'solayeye__table-search-select--item-active' : ''"
         @click="selectAll()"
    >
      全部
    </div>
    <div class="solayeye__table-search-select--item"
    :class="{'solayeye__table-search-select--item-active': classJudge(item)}"
         v-for="(item,index) in options"
         @click="selectItem(item)"
         :key="index"
    >
      {{item.label}}
    </div>
  </div>
</template>

<script>
/**
   * solareye特色的列表筛选select组件
   * @description 此组件一般用于列表头部筛选条件的select，建议选项较少且需要显眼显示时使用（eg:告警等级），选项较多时还是使用ant design的select组件。
   * @property {Boolean} multiple 是否是多选
   * @property {Boolean} isSelectAll 是否有全选按钮 只有multiple为true 生效
   * @property {String | Number | Array} value 当前选中code值 多选时为Array
   * @property {Array} options 选项 [{value:'',label:''}] 选项 固定传value和label
   * @event {Function} getValue(value,label) @return value:String|Number label:String 获取值回调
   */
export default {
  name: 'solareye-table-search-select',
  props: {
    value: {
      type: [String, Number, Array],
      default: () => {
        return null;
      }
    },
    multiple: {
      type: Boolean,
      default: false
    },
    isSelectAll: {
      type: Boolean,
      default: false
    },
    options: {
      type: Array,
      default: () => {
        return [];
      },
      validator: value => {
        let dataValidator = value.every(item => item.hasOwnProperty('value') && item.hasOwnProperty('label'));
        return value.length > 0 && dataValidator;
      }
    }
  },
  data: function () {
    return {
      selectAllStatus: false,
      selected: null,
      multipleSelected: [],
      listData: []

    };
  },
  created () {
    if (this.multiple) {
      this.multipleSelected = this.value || [];
    } else {
      this.selected = this.value;
    }
    this.listData = [...this.options];
  },
  computed: {},
  methods: {
    // 全选
    selectAll () {
      if (this.selectAllStatus) {
        this.multipleSelected = [];
        this.selectAllStatus = false;
        this.$forceUpdate();
        return;
      }
      if (!this.selectAllStatus) {
        this.multipleSelected = this.options.map(item => item.value);
        this.selectAllStatus = true;
      }
      this.$emit('change', this.multipleSelected);
      this.$forceUpdate();
    },
    // 为激活状态添加类名
    classJudge (val) {
      if ((this.multiple && this.multipleSelected.includes(val.value)) ||
          (!this.multiple && this.selected === val.value)
      ) {
        return true;
      } else {
        return false;
      }
    },
    // 选择时
    selectItem (val) {
      if (this.multiple) {
        if (this.multipleSelected.includes(val.value)) {
          this.multipleSelected.splice(this.multipleSelected.findIndex(item => item === val.value), 1);
          this.selectAllStatus = false;
        } else {
          this.multipleSelected.push(val.value);
          if (this.multipleSelected.length == this.options.length) {
            this.selectAllStatus = true;
          }
        }
      }

      if (!this.multiple) {
        if (val.value === this.selected) {
          this.selected = null;
        } else {
          this.selected = val.value;
        }
      }

      if (this.multiple) {
        this.$emit('change', this.multipleSelected);
      } else {
        this.$emit('change', this.selected);
      }
      this.$forceUpdate();
    }

  },
  watch: {
    options: { // 不建议随意修改选项
      deep: true,
      handler: function (newVal) {
        this.listData = [...newVal];
        this.selected = null;
        this.multipleSelected = [];
        this.selectAllStatus = false;
      }
    },
    value: {
      handler: function (newVal) {
        console.log(this);
        if (this.multiple) {
          this.multipleSelected = [...newVal];
        } else {
          this.selected = newVal;
        }
        this.$forceUpdate();
      }
    }
  }
};
</script>

<style lang="less" scoped>
  .solayeye__table-search-select {
    display: flex;

    .solayeye__table-search-select--item {
      min-width: 100px;
      padding: 0 20px;
      box-sizing: border-box;
      height: 28px;
      border-radius: 30px;
      text-align: center;
      line-height: 28px;
      cursor: pointer;
    }

    .solayeye__table-search-select--item:nth-child(n+1) {
      margin-left: 16px;
    }

  }
</style>
