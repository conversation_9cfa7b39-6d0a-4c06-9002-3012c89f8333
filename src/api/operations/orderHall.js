import { axios } from '@/utils/erp-request';

// 根据区域查询电站信息
export function psaListByArea (data) {
  return axios({
    url: '/hy/task/psaListByArea',
    method: 'post',
    data
  });
}

// 工单详情
export function hyOrderDetail (data) {
  return axios({
    url: '/hy/order/manage/detail',
    method: 'post',
    data
  });
}

// 工单查询
export function getOrderList (data) {
  return axios({
    url: '/hy/order/list',
    method: 'post',
    data
  });
}

// 工单处理
export function orderProcess (data) {
  return axios({
    url: '/hy/order/oper/process',
    method: 'post',
    data
  });
}

// 工单转派
export function orderTransfer (data) {
  return axios({
    url: '/hy/order/oper/transfer',
    method: 'post',
    data
  });
}

// 指派处理人
export function orderDistributeUserListByPSId (data) {
  return axios({
    url: '/hy/order/oper/distributeUserListByPSId',
    method: 'post',
    data
  });
}

// 运维商
export function orderMaintenance (data) {
  return axios({
    url: '/hy/order/manage/maintenance',
    method: 'post',
    data
  });
}
