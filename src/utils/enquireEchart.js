import * as echarts from 'echarts/core'; // echart 按需加载
import {
  DatasetComponent,
  TitleComponent,
  TooltipComponent,
  ToolboxComponent,
  LegendComponent,
  GridComponent,
  DataZoomComponent,
  MarkPointComponent,
  Mark<PERSON><PERSON>Component,
  Mark<PERSON><PERSON><PERSON>omponent,
  PolarComponent,
  GeoComponent
} from 'echarts/components';

import {
  <PERSON><PERSON>hart,
  <PERSON><PERSON><PERSON>,
  Bar<PERSON>hart,
  <PERSON>atter<PERSON>hart,
  CustomChart,
  TreemapChart,
  Gauge<PERSON>hart
} from 'echarts/charts';
import {
  CanvasRenderer
} from 'echarts/renderers';

echarts.use(
  [DatasetComponent, TitleComponent, TooltipComponent, Tool<PERSON>Component, LegendComponent, GridComponent, <PERSON>ZoomComponent, MarkPointComponent, MarkAreaComponent, LineChart, PieChart, BarChart, CanvasRenderer, MarkLineComponent, ScatterChart, CustomChart, TreemapChart, GaugeChart, PolarComponent, GeoComponent]
);

export default echarts;
