<!-- 直采配置 - 绑定测点 -->
<template>
  <div>
    <a-spin :spinning="loading" >
      <div class="solar-eye-search-model">
        <a-form labelAlign="left"  class="solar-eye-search-content" style="width: 55%">
           <div class="search-title-bound">直采接入设备</div>
            <a-row :gutter="5" align="middle">
            <a-col  :md="2" :sm="12" :xxl="4" :xl="4" :lg="4">
              <a-form-item label="接收子站名称" :colon="false">
                <a-input v-model="queryDeviceParams.receiveStationName" placeholder="输入关键词搜索"></a-input>
              </a-form-item>
            </a-col>
            <a-col  :md="2" :sm="24" :xxl="4" :xl="4" :lg="8">
              <a-form-item label="接收子站类型" :colon="false">
                <a-input v-model="queryDeviceParams.receiveStationType" placeholder="输入关键词搜索"></a-input>
              </a-form-item>
            </a-col>
            <a-col  :md="2" :sm="24" :xxl="4" :xl="4" :lg="8">
              <div class="search-item">
                <throttle-button title="查询" label='查询' :loading="searchLoading" @click="searchDevice"/>
                <throttle-button title="重置" label='重置' @click="resetDevice" class="solar-eye-btn-primary-cancel"/>
              </div>
            </a-col>
            </a-row>
        </a-form>
         <a-form labelAlign="left"  class="solar-eye-search-content" style="width: 44%">
          <div class="search-title-bound" style="marginLeft: 0px">电站设备台账</div>
            <a-row :gutter="5" align="middle">
            <a-col  :md="8" :sm="8" :xxl="7" :xl="2" :lg="2">
                <div class="search-item">
                  <span class="search-label">电站名称</span>
                  <ps-tree-select @change="getStationChange" v-model="psId" ref="tree" :isPsName="psName" :isQueryPs="1" :checked="false"  style="width: 100%;" />
                </div>
            </a-col>
            <a-col  :md="8" :sm="8" :xxl="7" :xl="2" :lg="2">
              <a-form-item label="设备名称" :colon="false">
                <a-input v-model="boundDeviceParams.deviceName" placeholder="输入关键词搜索"></a-input>
              </a-form-item>
            </a-col>
            <a-col  :md="8" :sm="8" :xxl="6" :xl="2" :lg="2">
              <a-form-item label="设备类型" :colon="false">
                <a-select v-model="boundDeviceParams.deviceType">
                  <a-select-option value="">全部</a-select-option>
                  <a-select-option v-for="item in deviceTypeData" :value="item.secondTypeCode" :key="item.secondTypeCode">
                    {{ item.secondName }}
                  </a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
            <a-col  :md="8" :sm="8" :xxl="1" :xl="2" :lg="2">
              <div class="search-item">
                <throttle-button title="查询" label='查询' :loading="accountLoading" @click="searchStationData"/>
                <throttle-button title="重置" label='重置' class="solar-eye-btn-primary-cancel" @click="resetStationData(true)"/>
              </div>
            </a-col>
            </a-row>
        </a-form>
      </div>
      <div class="solar-eye-gap"></div>
      <a-col class="solar-eye-main-content">
        <div class='test'>
          <div style="width: 55%">
            <div class="operation-btn">
              <throttle-button title="批量取消绑定" label='批量取消绑定' :loading="unBatchLoad" :disabled="isShowCancelBound"  @click="moreCancelBoundBtn" class="solar-eye-btn-primary-cancel" />
              <throttle-button title="批量绑定" :loading="batchLoad" label='批量绑定' :disabled="isShowlBound"  @click="moreBoundBtn"/>
              <!-- <throttle-button title="批量添加"  label='批量添加' /> -->
            </div>
              <a-spin :spinning="searchLoading">
           <vxe-table
            :data="tableData"
            ref="multipleTable"
            class="my-table"
            @sort-change="handleTableSortChange"
            :sort-config="{ remote: true }"
            resizable
            align="center"
            show-overflow
            highlight-hover-row
            size="small"
            @checkbox-all="onSelectChangeDevice"
            @checkbox-change="onSelectChangeDevice"
            :height="tableHeight - 100"
          >
            <vxe-table-column type="checkbox" :width="60"></vxe-table-column>
            <vxe-table-column type="seq" :width="60" title="序号" align="center"></vxe-table-column>
               <vxe-table-column show-overflow="title" v-for="item in colunms1" :key="item.key" :field="item.key"
                              :title="item.title" :min-width="item.width || 120"
                              :fixed="item.fixed ? item.fixed : ''">
              <template v-slot:default="{row}">
                <span>{{ getLabel(row[item.key], null) }}</span>
              </template>
            </vxe-table-column>
            <vxe-table-column title="绑定设备ID" width="240" fixed="right" :resizable="false">
              <template v-slot="{ row }">
                <div
                     class='droparea'
                     @drop='endDrop($event,row)'
                     @dragenter='e => e.preventDefault()'
                     @dragover="e => e.preventDefault()">
                      <a-select
                        show-search
                        style="width: 100%"
                        v-model="row.psKey"
                        :default-active-first-option="false"
                        :show-arrow="false"
                        :filter-option="filterOption"
                        :not-found-content="null"
                        @change="handleChange"
                      >
                        <a-select-option v-for="d in data" :key="d">
                         {{ d }}
                        </a-select-option>
                      </a-select>
                        <!-- {{row.psKey}} -->
                </div>
              </template>
            </vxe-table-column>
            <template v-slot:empty>
              <span>查询无数据</span>
            </template>
           </vxe-table>
             <!-- <page-pagination
            :pageSize="queryParams.pageSize"
            :current="queryParams.currentPage"
            :total="totalTableData"
            @size-change="sizeChange"
          /> -->
          </a-spin>
          </div>
          <div style="width: 44%">
            <div class="operation-btn" style="visibility: hidden;">
              <throttle-button title="批量添加"  label='批量添加'/>
            </div>
           <a-spin :spinning="accountLoading">
          <vxe-table
            :data="this.dataBoundDeviceList"
            ref="multipleTable2"
            class="my-table"
            @sort-change="handleTableSortChange"
            :sort-config="{ remote: true }"
            resizable
            align="center"
            show-overflow
            highlight-hover-row
            size="small"
            @checkbox-all="onSelectChange"
            @checkbox-change="onSelectChange"
            :height="tableHeight - 130"
          >
            <vxe-table-column type="seq" :width="80" title="序号" align="center"></vxe-table-column>
            <vxe-table-column title="设备编号" width="220"  :resizable="false">
              <template v-slot="{ row }">
                <div :style='{width:"100%",height:"30px",cursor: "move"}'
                     @dragstart='startDrag($event,row)'
                     :draggable="true">{{ row.psKey }}</div>
              </template>
            </vxe-table-column>
            <vxe-table-column show-overflow="title" :formatter="tabFormatter" title="设备名称" min-width="160" field="deviceName"> </vxe-table-column>
            <vxe-table-column show-overflow="title" :formatter="tabFormatter" title="设备类型" width="100" field="deviceTypeName"> </vxe-table-column>
            <vxe-table-column show-overflow="title" :formatter="tabFormatter" title="可用测点数" width="100" field="pointCount"> </vxe-table-column>
            <template v-slot:empty>
              <span>查询无数据</span>
            </template>
          </vxe-table>
           <page-pagination
          :pageSize="boundDeviceParams.pageSize"
          :current="boundDeviceParams.currentPage"
          :total="totalBoundDeviceList"
          @size-change="sizeChangeBoundDeviceList"
          :pageSizeOptions="[10, 20, 50, 100, 500,5000]"
        />
        </a-spin>
          </div>
        </div>
        <div class="btnBox">
          <throttle-button title="取消" label='取消' @click="btnCancel" style="margin-right: 20px;" class="solar-eye-btn-primary-cancel"/>
          <throttle-button title="确认" :loading="okLoading"  label='确认' @click="btnOk" />
        </div>
      </a-col>
    </a-spin>
  </div>
</template>
<script>
import tableHeight from '@/mixins/tableHeightAndSearchModel';
import { listMixin } from './../mixins';
import { boundDeviceTab1 } from './../columns';
import {
  accessDeviceMappingCancelBindingForMore,
  getAccessDeviceList,
  getAccessDeviceMappingForPsDeviceList,
  accessDeviceMappingBindingForOne,
  getAccessDeviceMappingBindingForMoreList,
  checkAccessDeviceMappingBindingForOne
} from '@/api/health/getPointConfig.js';
export default {
  name: 'PointMage',
  mixins: [tableHeight, listMixin],
  data () {
    return {
      queryParams: {

      },
      psName: '',
      depCodes: '',
      psId: '',
      nodeType: '',
      colunms1: boundDeviceTab1,
      data: [],
      test: [],
      tableData: [],
      totalTableData: '',
      loading: false,
      boundHomeParams: {
        snList: [],
        currentPage: 1,
        pageSize: 10
      },
      boundType: '',
      // 单条绑定请求参数
      oneBoundParams: {
        psId: '',
        psKeyMappingList: [
          // {
          //   originalPsKey: '',
          //   psKey: ''
          // }
        ]
      },
      obj: {
        originalPsKey: '',
        psKey: ''
      },
      // 批量绑定电站-批量绑定请求参数
      moreBoundParams: {
        psId: '',
        originalPsKeyList: []
      },
      // 批量绑定电站-批量取消绑定请求参数
      moreCancelBoundParams: {
        originalPsKeyList: []
      },
      isShowCancelBound: true,
      isShowlBound: true,
      selectedRowKeys: [], // 表1选中数据
      searchLoading: false,
      okLoading: false,
      accountLoading: false,
      bindingStatusList: [],
      batchLoad: false,
      unBatchLoad: false,
      selectedRows: [],
      originalPsKeyList: [],
      isMore: false
    };
  },
  created () {

  },
  computed: {},
  methods: {
    handleChange (value) {
      // this.obj.psKey = value;
    },
    getOriginPsKeyList () {
      this.originalPsKeyList = [];
      this.tableData.forEach(item => {
        this.originalPsKeyList.push(item.originalPsKey);
      });
    },
    searchDevice () {
      this.searchLoading = true;
      let params = {};
      if (this.isMore) {
        params = Object.assign(this.queryDeviceParams, { snList: this.boundHomeParams.snList });
      } else {
        params = this.queryDeviceParams;
      }
      getAccessDeviceList(params).then(res => {
        if (res.result_code == '1') {
          this.tableData = res.result_data.pageList;
          this.getOriginPsKeyList();
          if (res.result_data) {
            this.totalTableData = res.result_data.rowCount;
          }
        }
        this.searchLoading = false;
      }).catch(() => {
        this.searchLoading = false;
      });
    },
    resetDevice () {
      this.queryDeviceParams.receiveStationName = '';
      this.queryDeviceParams.receiveStationType = '';
      this.searchDevice();
    },
    onSelectChangeDevice (val) {
      this.bindingStatusList = [];
      this.selectedRows = [];
      let arr = [];
      val.records.forEach((item) => {
        arr.push(item.originalPsKey);
        this.selectedRows.push(item);
        if (item.bindingStatus != 0) {
          this.bindingStatusList.push(item.bindingStatus);
        }
      });
      this.selectedRowKeys = arr;

      this.isShowCancelBound = this.selectedRowKeys.length === 0;
      this.isShowlBound = this.selectedRowKeys.length === 0;
    },
    // 批量绑定
    moreBoundBtn () {
      if (this.psId == '169a0f1565004adfb22af8f045d65913' || !this.psId) {
        this.moreBoundParams.psId = '';
        this.$notification.error({
          message: '提示',
          description: '请先在右侧列表中选择目标电站!'
        });
        return;
      } else {
        this.moreBoundParams.psId = this.psId;
      }
      // if(this.bindingStatusList.length > 0) {
      //    this.$notification.error({
      //     message: '提示',
      //     description: "部分接入设备已绑定，请先解除这些绑定！"
      //   });
      //     return;
      // }
      this.moreBoundParams.originalPsKeyList = this.selectedRowKeys;
      this.batchLoad = true;
      getAccessDeviceMappingBindingForMoreList({
        psId: this.moreBoundParams.psId,
        detailList: this.selectedRows
      }).then(res => {
        this.batchLoad = false;
        res.result_data.forEach(item => {
          let index = this.originalPsKeyList.indexOf(item.originalPsKey);
          if (index != -1) {
            this.tableData[index] = item;
          }
        });
        this.tableData = [...this.tableData];
      }).catch(() => {
        this.batchLoad = false;
      });
    },
    moreCancelBoundBtn () {
      this.unBatchLoad = true;
      this.moreCancelBoundParams.originalPsKeyList = this.selectedRowKeys;
      accessDeviceMappingCancelBindingForMore(this.moreCancelBoundParams).then(res => {
        if (res.result_code == '1') {
          this.loading = false;
          this.$message.success(`操作成功`);
          this.isShowCancelBound = true;
          this.isShowlBound = true;
          this.$refs.multipleTable.clearCheckboxRow();
          this.searchDevice();
        }
        this.unBatchLoad = false;
      }).catch(() => {
        this.unBatchLoad = false;
      });
    },
    // 电站设备台账查询按钮
    searchStationData () {
      if (!this.psId) {
        this.boundDeviceParams.psId = '';
        this.$message.warning('请先选择电站');
        return;
      }
      this.boundDeviceParams.psId = this.psId;
      this.getBoundDeviceList();
    },
    // 重置
    resetStationData (refresh) {
      this.boundDeviceParams.deviceName = '';
      this.boundDeviceParams.deviceType = '';
      this.psId = '';
      this.dataBoundDeviceList = [];
      // if (this.$refs.tree) {
      //   let node = this.$refs.tree.rootNode;
      //   this.setStationParams(node);
      //   this.psId = '';
      //   this.$refs.tree.refresh = false;
      //   setTimeout(() => {
      //     this.$refs.tree.refresh = true;
      //   }, 300);
      // if (refresh) {
      //   this.searchStationData();
      // }
      // }
    },
    // tab2-测点绑定设备-电站设备列表查询
    getBoundDeviceList () {
      this.accountLoading = true;
      getAccessDeviceMappingForPsDeviceList(this.boundDeviceParams).then(res => {
        if (res.result_code == '1') {
          let arr = [];
          this.dataBoundDeviceList = res.result_data.pageList;
          this.dataBoundDeviceList.forEach((item) => {
            arr.push(item.psKey);
          });
          this.data = arr;
          if (res.result_data) {
            this.totalBoundDeviceList = res.result_data.rowCount;
          }
        }
        this.accountLoading = false;
      }).catch(() => {
        this.accountLoading = false;
      });
    },
    sizeChangeBoundDeviceList (current, size) {
      this.boundDeviceParams.pageSize = size;
      this.boundDeviceParams.currentPage = current;
      this.getBoundDeviceList();
    },
    // 电站树变化事件
    getStationChange (val, node) {
      this.setStationParams(node);
      // this.$nextTick(() => {
      //   this.searchStationData();
      // });
    },
    setStationParams (node) {
      this.psName = node.name;
      this.psId = node.id + '';
      this.depCodes = node.orgCode;
      if (node.isPsa == '0' && !node.isPs) {
        this.nodeType = 1;
      } else if (node.isPsa == '1') {
        this.nodeType = 2;
      } else if (node.isPs == 1) {
        this.nodeType = 3;
      } else {
        this.nodeType = '';
      }
    },
    changeEvent (value, type) {
    },
    init (type, row) {
      this.bindingStatusList = [];
      this.moreBoundParams.psId = '';
      this.boundType = type;
      this.getDictList();
      if (type == 1) {
        this.isMore = true;
        this.boundHomeParams.snList = row;
        getAccessDeviceList(this.boundHomeParams).then(res => {
          if (res.result_code == '1') {
            this.tableData = res.result_data.pageList;
            if (res.result_data) {
              this.totalTableData = res.result_data.rowCount;
            }
          }
        }).catch(() => {
        });
      } else if (type == 2) {
        this.isMore = false;
        this.tableData = row;
        this.totalTableData = row.length;
        let arrId = [];
        let arrSn = [];
        this.tableData.forEach((item) => {
          arrId.push(item.id);
          arrSn.push(item.sn);
        });
        this.queryDeviceParams.ids = arrId;
        this.queryDeviceParams.snList = arrSn;
      }
      this.getOriginPsKeyList();
      return '绑定设备';
    },
    startDrag (event, data) {
      event.dataTransfer.setData('point-drag', data.psKey);
    },
    endDrop (event, data) {
      let point = event.dataTransfer.getData('point-drag');
      if (point) {
        data.psKey = point;
      }
      // this.oneBoundParams.psKeyMappingList[0].originalPsKey = data.originalPsKey;
      // this.oneBoundParams.psKeyMappingList[0].psKey = data.psKey;
    },
    btnOk () {
      if (this.psId == '169a0f1565004adfb22af8f045d65913' || !this.psId) {
        this.moreBoundParams.psId = '';
        this.$notification.error({
          message: '提示',
          description: '请先在右侧列表中选择目标电站!'
        });
        return;
      }
      this.moreBoundParams.psId = this.psId;

      this.oneBoundParams.psId = this.psId;
      let arr = []; let psKeyList = [];
      this.tableData.map((item) => {
        if (item.psKey) {
          psKeyList.push(item.originalPsKey);
          arr.push(
            {
              'originalPsKey': item.originalPsKey,
              'psKey': item.psKey
            }
          );
        }
      });
      this.moreBoundParams.originalPsKeyList = psKeyList;
      this.oneBoundParams.psKeyMappingList = arr;
      this.okLoading = true;
      let that = this;
      // 校验
      checkAccessDeviceMappingBindingForOne(this.oneBoundParams).then(res => {
        if (res.result_code == '1') {
          if (res.result_msg) {
            this.$confirm({
              title: '提示',
              content: res.result_msg,
              onOk () {
                return that.bindingDevice();
              },
              onCancel () {
                that.okLoading = false;
              }
            });
          } else {
            this.bindingDevice();
          }
        }
      }).catch(() => {
        this.okLoading = false;
      });
    },
    bindingDevice () {
      let that = this;
      accessDeviceMappingBindingForOne(this.oneBoundParams).then(res => {
        if (res.result_code == '1') {
          // this.btnCancel();
          that.$message.success('操作成功');
          // that.$emit('cancel', this.boundType);
        }
        that.okLoading = false;
      }).catch(() => {
        that.okLoading = false;
      });
    },
    filterOption (input, option) {
      return (
        option.componentOptions.children[0].text.toLowerCase().indexOf(input.toLowerCase()) >= 0
      );
    },
    btnCancel () {
      this.$emit('cancel');
    }
  }
};
</script>

<style lang='less' scoped >
.ant-col-xxl-4 {
  width: 32.66%;
}
.solar-eye-search-model {
  display: flex;
  justify-content: space-between;
}
.search-title-bound {
/* 直采接入设备 */
margin-top: 16px;
font-size: 16px;
font-weight: bold;
color: #3D3D3D;
margin-left: 14px;
}
.test{
  display: flex;
  justify-content: space-between;
}

.droparea:-moz-drag-over {
  border: 1px solid black;
}
.btnBox {
  text-align: center;
}
</style>
