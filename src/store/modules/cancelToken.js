const cancelToken = {
  state: {
    cancelTokenArr: []
  },
  mutations: {
    ADD_CANCEL_TOKEN: (state, cancel) => {
      if (!state.cancelTokenArr) {
        state.cancelTokenArr = [];
      }
      if (cancel) {
        state.cancelTokenArr.push(cancel);
      }
    },
    CLEAR_CANCEL_TOKEN: (state) => {
      state.cancelTokenArr.forEach(controller => {
        if (controller) {
          controller && controller();
        }
      });
      state.cancelTokenArr = [];
    }
  },
  actions: {

  }
};

export default cancelToken;
