<template>
  <a-drawer
    title="测点定义"
    width="100%"
    :visible="visible"
    @close="close(false)"
    :get-container="getContainer"
    :wrap-style="{ position: 'absolute' }"
  >
    <a-spin :spinning="loading" >
      <a-row :gutter="24">
        <a-col :xxl="5" :xl="8" :md="10" class="search-box">
          <div class="text">
            <span>设备类型</span>
          </div>
          <div class="content">
            <a-select v-model="queryParams.deviceType" style="width:100%">
              <a-select-option value="" :key="''">全部</a-select-option>
              <a-select-option
                v-for="item in deviceTypeList"
                :key="item.code"
                :value="item.code"
              >
              {{item.name}}
              </a-select-option>
            </a-select>
          </div>
        </a-col>
        <a-col :xxl="5" :xl="8" :md="10" class="search-box">
          <div class="text">
            <span>测点名称</span>
          </div>
          <div class="content">
            <a-input v-model="queryParams.pointName" placeholder="请输入测点名称"></a-input>
          </div>
        </a-col>
        <a-col :xxl="5" :xl="8" :md="10" class="search-box">
          <div class="text">
            <span>告警等级</span>
          </div>
          <div class="content">
            <a-select
              v-model="queryParams.alarmGrade"
              :options="alarmGradeOptions1"
              placeholder="请选择"
              style="width: 100%">
            </a-select>
          </div>
        </a-col>
        <a-col :xxl="5" :xl="8" :md="10" class="search-box">
          <div class="text">
            <span>是否屏蔽</span>
          </div>
          <div class="content">
            <a-select v-model="queryParams.shieldFlag" style="width:100%;">
              <a-select-option :value="-1">全部</a-select-option>
              <a-select-option :value="1">是</a-select-option>
              <a-select-option :value="0">否</a-select-option>
            </a-select>
          </div>
        </a-col>
        <a-col :xxl="2" :xl="8" :md="10" class="search-box">
          <div class="content">
            <a-button @click="loadData(1)" class="solar-eye-btn-primary">查询</a-button>
          </div>
        </a-col>
      </a-row>
      <div style="text-align:right; margin-bottom:16px;">
        <a-button @click="doShields" class="solar-eye-btn-primary">批量屏蔽</a-button>
      </div>
      <vxe-table
        :data="dataSource"
        ref="multipleTable"
        class="my-table"
        resizable
        show-overflow
        highlight-hover-row
        size="small"
        @checkbox-all="onSelectChange"
        @checkbox-change="onSelectChange"
        :height="height - 280 +'px'"
      >
        <vxe-table-column type="checkbox" width="80" align="center"></vxe-table-column>
        <vxe-table-column show-overflow="title" :formatter="tabFormatter" title="设备类型" min-width="160" field="deviceType"> </vxe-table-column>
        <vxe-table-column show-overflow="title" :formatter="tabFormatter" title="测点名称" field="pointName" min-width="160"> </vxe-table-column>
        <vxe-table-column show-overflow="title" :formatter="tabFormatter" title="测点编码" field="pointCode" min-width="160"> </vxe-table-column>
        <vxe-table-column :formatter="tabFormatter" title="告警等级" field="alarmGrade" min-width="160">
          <template v-slot="{ row }">
            <a-select
              v-model="row.alarmGradeValue"
              :options="alarmGradeOptions2"
              placeholder="请选择"
              style="width: 100%">
            </a-select>
          </template>
        </vxe-table-column>
        <vxe-table-column show-overflow="title" :formatter="tabFormatter" title="是否屏蔽" field="shieldFlagValue" min-width="160">
          <template v-slot="{ row }">
            <a-select v-model="row.shieldFlagValue">
              <a-select-option :value="true">是</a-select-option>
              <a-select-option :value="false">否</a-select-option>
            </a-select>
          </template>
        </vxe-table-column>
      </vxe-table>
      <page-pagination
        :pageSize="queryParams.pageSize"
        :current="queryParams.currentPage"
        :total="total"
        @size-change="sizeChange"
      />
      <div class="drawer-form-foot" style="margin-top:16px">
        <a-button  @click="close(false)"> 取消 </a-button>
        <a-button type="primary" @click="doSubmit">确定</a-button>
      </div>
    </a-spin>
  </a-drawer>
</template>
<script>
import { dataCenterListMixin } from '../mixins/list';
import { remoteSignalUpdate } from '@/api/dataCenter';
import { getSystemCodeList } from '@/api/health/healthapi.js';
export default {
  name: 'RemoteSignalingEdit',
  mixins: [dataCenterListMixin],
  data () {
    return {
      visible: false,
      loading: false,
      queryParams: {},
      url: {
        list: '/pointManagement/list'
      },
      selectedData: [],
      alarmGradeOptions1: [],
      alarmGradeOptions2: []
    };
  },
  created () {
    // 获取设备类型下拉选项
    this.getDeviceTypeList(true);
    this.getAlarmGradeOptions();
  },
  methods: {
    // 将抽屉组件挂载到页面上
    getContainer () {
      return document.getElementById('pointMage');
    },
    // 页面数据初始化
    init () {
      this.visible = true;
      this.queryParams = {
        pointName: '',
        deviceType: '',
        pointType: '1',
        pageSize: '10',
        currentPage: '1',
        alarmGrade: '',
        shieldFlag: -1
      };
      this.loadData(1);
    },
    // 关闭页面
    close (flag) {
      this.visible = false;
      this.$emit('close', flag);
    },
    // 表格勾选变化事件
    onSelectChange (val) {
      this.selectedData = val.records;
    },
    // 批量屏蔽
    doShields () {
      if (this.selectedData.length > 0) {
        let that = this;
        this.$confirm({
          title: '提示',
          content: '确定要批量屏蔽？',
          okText: '确定',
          cancelText: '取消',
          onOk: function () {
            let selectedIds = [];
            that.selectedData.forEach(ele => {
              selectedIds.push(ele.id);
            });
            that.dataSource.forEach(item => {
              if (selectedIds.indexOf(item.id) != -1) {
                item.shieldFlagValue = true;
              }
            });
            that.$refs.multipleTable.refreshColumn();
          }
        });
      } else {
        this.$message.warning('请先勾选数据！');
      }
    },
    // 提交操作
    doSubmit () {
      this.loading = true;
      remoteSignalUpdate({ points: this.dealData() }).then(res => {
        if (res.result_code == '1') {
          this.close(true);
        }
        this.loading = false;
      }).catch(() => {
        this.loading = false;
      });
    },
    // 处理提交数据
    dealData () {
      let arr = [];
      this.dataSource.forEach(ele => {
        arr.push({
          id: ele.id,
          alarmGrade: ele.alarmGradeValue,
          shieldFlag: ele.shieldFlagValue ? 1 : 0,
          pointCode: ele.pointCode
        });
      });
      return arr;
    },
    // 获取告警等级下拉选项
    async getAlarmGradeOptions () {
      let res = await getSystemCodeList({ firstTypeCode: '0070' });
      let arr = res.result_data['0070'].map(item => {
        return {
          value: item.secondTypeCode,
          label: item.secondName
        };
      });
      this.alarmGradeOptions1 = [{ label: '全部', value: '' }, ...arr];
      this.alarmGradeOptions2 = [{ label: '请选择', value: null }, ...arr];
    }

  }
};
</script>
<style lang="less" scoped>
:deep(.ant-drawer-body) {
  padding: 16px 24px 0 24px;
}
.search-box {
  display:flex;
  margin-bottom:16px;
  .text {
    display:flex;
    align-items: center;
    width:64px;
  }
  .content {
    flex:1
  }
}
</style>
