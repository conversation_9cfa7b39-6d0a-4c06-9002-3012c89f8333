import { axios } from '@/utils/erp-request';

// 保存人员调动信息
export function saveMoveApplication (data) {
  return axios({
    url: '/personnelTransfer/v1/save',
    method: 'POST',
    data
  });
}
// 提交人员调动信息
export function commitMoveApplication (data) {
  return axios({
    url: '/personnelTransfer/v1/commit',
    method: 'POST',
    data
  });
}
// 获取人员调动基本信息
export function getBaseInfo (data) {
  return axios({
    url: '/personnelTransfer/v1/baseInfo',
    method: 'POST',
    data
  });
}
// 更新人员调动信息
export function updateMoveApplication (data) {
  return axios({
    url: '/personnelTransfer/v1/update',
    method: 'POST',
    data
  });
}
// 删除人员调动信息
export function deleteMoveApplication (data) {
  return axios({
    url: '/personnelTransfer/v1/delete',
    method: 'POST',
    data
  });
}
// 审批人员调动信息
export function auditMoveApplication (data) {
  return axios({
    url: '/personnelTransfer/v1/audit',
    method: 'POST',
    data
  });
}

// 人员调动台账
export function exportTransfer (data) {
  return axios({
    url: '/personnelTransfer/v1/exportTransfer',
    method: 'POST',
    data
  });
}
export function transferList (data) {
  return axios({
    url: '/personnelTransfer/v1/transfer',
    method: 'POST',
    data
  });
}
export function statusList (data) {
  return axios({
    url: '/personnelTransfer/v1/statusList',
    method: 'POST',
    data
  });
}
