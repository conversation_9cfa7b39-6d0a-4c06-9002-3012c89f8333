<template>
  <div class="drawer-form-com">
    <div class="drawer-form-content">
      <a-spin :spinning="loading">
        <a-form-model :model="dataForm" :rules="rules" ref="dataForm" :labelCol="{ style: 'width: 225px' }" :wrapperCol="{ style: 'width: calc(100% - 225px)' }">
          <a-row>
            <a-col :xl='8' :sm='12' :xs='24'>
              <a-form-model-item label="厂家" prop="maker">
                {{dataForm.maker}}
              </a-form-model-item>
            </a-col>
            <a-col :xl='8' :sm='12' :xs='24'>
              <a-form-model-item label="型号" prop="deviceModel">
                {{dataForm.deviceModel}}
              </a-form-model-item>
            </a-col>
            <a-col :xl='8' :sm='12' :xs='24'>
              <a-form-model-item label="设备类型" prop="equipmentType">
                <a-cascader size="default" v-model="dataForm.equipmentType" :options="equipmenOptions"
                  @change="equipmentTypeChange" placeholder="请选择设备类型" style="width: 100%;"
                  :field-names="{ label: 'deviceName', value: 'deviceId', children: 'childErpDeviceTypeInfo' }">
                </a-cascader>
              </a-form-model-item>
            </a-col>
            <template v-for="item in dataForm.typeSetingList">
              <a-col :xl='8' :sm='12' :xs='24' :key="item.parmKey">
                <a-form-model-item :label="item.name" :prop="item.parmKey">
                  <a-input v-model="item.value" size="default" style="width: 100%;"></a-input>
                </a-form-model-item>
              </a-col>
            </template>
          </a-row>
        </a-form-model>
      </a-spin>
    </div>
    <div class="drawer-form-foot">
      <throttle-button label="保存" :loading="loading" @click="doSubmit()" />
      <throttle-button label="取消" type="info" @click="cancel" />
    </div>
  </div>
</template>

<script>
import { updateParameter, selectParameter } from '@/api/isolarErp/equipment/config';
import { getParentDeviceTypeInfoList } from '@/api/isolarErp/equipment/type';
export default {
  data () {
    return {
      loading: false,
      // 设备类型数据
      equipmenOptions: [],
      dataForm: {
        maker: '',
        makerId: '',
        deviceModel: '',
        deviceTypeId: '',
        equipmentType: [],
        typeSetingList: []
      }
    };
  },
  methods: {
    // type  编辑：2
    init (type, row) {
      let self = this;
      self.loading = true;
      if (row) {
        self.dataForm.maker = row.maker;
        self.dataForm.makerId = row.makerId;
        self.dataForm.deviceModel = row.deviceModel;
        self.dataForm.deviceModelId = row.id;
        self.dataForm.deviceTypeId = row.deviceTypeId;
      }
      self.afterEvent();
      return '型号参数';
    },
    // 切换抽屉时动画结束后的回调，页面初始化init函数的异步请求须放在这里调用，否则可能会会导致抽屉侧滑出来时卡死
    afterEvent () {
      let self = this;
      this.$nextTick(() => {
        self.initOptions();
        self.initDetail();
      });
    },
    // 设备类型change事件
    equipmentTypeChange (val) {
      this.dataForm.deviceTypeId = val[val.length - 1];
      if (this.dataForm.deviceTypeId) {
        this.initDetail();
      }
    },
    // 获取设备类型下拉
    initOptions () {
      const self = this;
      getParentDeviceTypeInfoList({
        'needAllChild': '1',
        'deviceStatusCheck': '1'
      }).then(res => {
        if (res.result_code == '1' && res.result_data) {
          let options = res.result_data;
          self.deleteChildErpDeviceTypeInfo(options);
          self.equipmenOptions = options;
          let deviceTypeId = self.dataForm.deviceTypeId;
          let array = self.equipmenOptions;
          self.dataForm.equipmentType = self.familyTree(array, deviceTypeId);
          self.loading = false;
        } else {
          self.equipmenOptions = [];
          self.loading = false;
        }
      }).catch(() => {
        self.equipmenOptions = [];
        self.loading = false;
      });
    },
    // 删除空的子节点
    deleteChildErpDeviceTypeInfo (options) {
      options.forEach(item => {
        if (item.hasOwnProperty('childErpDeviceTypeInfo') && item.childErpDeviceTypeInfo.length > 0) {
          this.deleteChildErpDeviceTypeInfo(item.childErpDeviceTypeInfo);
        } else {
          delete item.childErpDeviceTypeInfo;
        }
      });
    },
    // 根据节点找到父节点一直到顶级节点
    familyTree (array, id) {
      let temp = [];
      const forFn = function (arr, id) {
        for (let i = 0; i < arr.length; i++) {
          let item = arr[i];
          if (item.deviceId == id) {
            temp.unshift(Number(item.parentId));
            forFn(array, item.parentId);
            break;
          } else {
            if (item.childErpDeviceTypeInfo) {
              forFn(item.childErpDeviceTypeInfo, id);
            }
          }
        }
      };
      forFn(array, id);
      temp.push(Number(id));
      let type = temp.filter(item => item != '0');
      return type;
    },
    // 详情
    initDetail () {
      const self = this;
      self.loading = true;
      let map = {
        makerId: self.dataForm.makerId,
        deviceModel: self.dataForm.deviceModel,
        deviceTypeId: self.dataForm.deviceTypeId
      };
      selectParameter(map).then(res => {
        if (res.result_code == '1' && res.result_data) {
          let result_data = res.result_data;
          self.dataForm.typeSetingList = result_data;
        }
        self.loading = false;
      }).catch(() => {
        self.loading = false;
      });
    },
    // 保存
    doSubmit () {
      const self = this;
      self.loading = true;
      let map = {
        makerId: self.dataForm.makerId,
        deviceModel: self.dataForm.deviceModel,
        deviceModelId: self.dataForm.deviceModelId,
        deviceTypeId: self.dataForm.deviceTypeId,
        typeSetingList: self.dataForm.typeSetingList
      };
      updateParameter(map).then(res => {
        self.loading = false;
        if (res.result_code == '1') {
          self.$message.success('操作成功');
          self.cancel();
        } else {
          self.$message.warning(res.result_msg);
        }
      }).catch(() => {
        self.loading = false;
      });
    },
    // 弹窗关闭回调方法
    cancel () {
      this.$emit('cancel');
      this.reset();
    },
    reset () {
      Object.assign(this.dataForm, this.$options.data().dataForm);
      this.$refs.dataForm.resetFields();
      this.$refs.dataForm.clearValidate();
    }
  }
};
</script>

<style lang="less" scoped>
  :deep(.ant-form-item) {
    width: 100%;
    display: inline-flex;
  }
</style>
