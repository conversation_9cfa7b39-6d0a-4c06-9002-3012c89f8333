<template>
   <div class='spot-supervise'>
     <a-row>
       <a-col :span="8" style='display: inline-flex;align-items: center'>
         <span class='item-content-title' style='width: 95px'>执行日志</span>
         <a-progress status="active" :percent='percent' size="small" stroke-color='#FF8100'>
           <template v-slot:format>
             <span :class="judgeExecuteRate">{{componentData.orderExecuteRate || 0}}%</span>
           </template>
         </a-progress>
       </a-col>
     </a-row>
     <a-row class='mt-24'>
       <a-tabs :activeKey="activeKey" :class='computedTabsClass' :type="computedType"  @edit="onEdit" :tabBarGutter='0' @change='tabChange'>
         <template slot='tabBarExtraContent'>
           <a-button :loading='exportLoading' @click='exportDataFn'>进度导出 <a-icon type="export" /></a-button>
         </template>
         <a-tab-pane v-for="(item) in panes" :key="item.id" :tab="item.tabTitle" :closable="computedClosable" force-render>
           <a-spin :spinning='loading'>
             <ExecutionLog
               ref='executionLogParent'
               :itemData.sync='item'
               :dictMap='dictMap'
               :orderForm='componentData'
               :current='current'
               @updateActiveKey = "updateActiveKey"
               :isDisabled = "isDisabled"
               :activeKey='activeKey'
             />
           </a-spin>
         </a-tab-pane>
       </a-tabs>
     </a-row>

   </div>
</template>
<script>
import ExecutionLog from '@views/distributed/orderHall/modules/stepsForClean/modules/ExecutionLog.vue';
import initDict from '@/mixins/initDict';
import { USER_INFO } from '@/store/mutation-types';
import { deleteCleanLogApi, exportData, getSingleCleanDetail } from '@api/distributed/orderHall';
import uuid from 'uuid';
import moment from 'moment';
import { omit } from 'xe-utils';
import { getWeatherApi } from '@api/common_gy/report';
export default {
  inject: ['trigggleFootShow', 'updateOpsWorkOrderCleanVo', 'spotSuperviseIndex'],
  mixins: [initDict],
  components: { ExecutionLog },
  props: {
    componentData: {
      type: Object,
      default: () => ({})
    },
    isDisabled: {
      type: Boolean,
      default: () => false
    },
    current: {
      type: Boolean,
      default: () => 0
    }
  },
  data () {
    return {
      activeKey: null,
      assignList: [],
      exportLoading: false,
      panes: [{
        tabTitle: '自定义',
        cleanDate: moment().format('YYYY-MM-DD'),
        todayWeather: undefined,
        cleanPlace: undefined,
        cleanPeopleCount: undefined,
        cleanTool: undefined,
        cleanCap: undefined,
        cleanCapPeople: undefined,
        totalCleanCap: undefined,
        cleanRate: undefined,
        cleanRateExplain: undefined,
        cleanEffect: undefined,
        checkUser: Vue.ls.get(USER_INFO).username,
        sceneFileOneList: [],
        sceneFileTwoList: [],
        cleanBeforeSceneFileList: [],
        cleanAfterSceneFileList: [],
        cleanBeforeElectricFileList: [],
        cleanAfterElectricFileList: [],
        editable: true,
        type: 'add',
        id: uuid()
      }],
      loading: false
      // backups: null // 数据备份
    };
  },
  mounted () {
    this.getDictMap('yiyuan_weather,wash_tool,clean_effect');
    // this.getAssignListFn();
    const cleanDetailVo = this.componentData.cleanDetailVo || {};
    const opsWorkOrderCleanDateVoList = cleanDetailVo.opsWorkOrderCleanDateVoList || [];
    const opsWorkOrderCleanVo = cleanDetailVo.opsWorkOrderCleanVo || {};
    const panes = this.panes;
    this.$nextTick(async () => {
      if (opsWorkOrderCleanDateVoList.length > 0) {
        this.panes = opsWorkOrderCleanDateVoList.map(item => ({
          ...item,
          editable: this.isDisabled ? false : item.isCommit != 1,
          tabTitle: item.cleanDate,
          type: item.isCommit ? 'edit' : 'add'
        }));
        const index = this.panes.findIndex(item => item.id === opsWorkOrderCleanVo.id);
        // Object.assign(this.panes[index], opsWorkOrderCleanVo);
        this.panes.splice(index, 1, Object.assign({}, this.panes[index], opsWorkOrderCleanVo));
        this.activeKey = opsWorkOrderCleanVo.id;
        // this.backups = clone(Object.assign({}, this.panes[index], opsWorkOrderCleanVo), true);
      } else {
        if (this.isDisabled) {
          this.panes[0].editable = false;
          this.panes[0].cleanDate = null;
        } else {
          const res = await this.getWeatherFn();
          this.panes[0].todayWeather = res.result_data;
        }
        this.activeKey = panes[0] && panes[0].id;
        // this.backups = clone(panes[0], true);
      }
    });
  },
  methods: {
    onEdit (targetKey, action) {
      this[action](targetKey);
    },
    async getWeatherFn () {
      const res = await getWeatherApi({
        reportTime: moment().format('YYYY-MM-DD'),
        psaId: this.componentData.psaId
      });
      return res;
    },
    async addIntialItem () {
      const res = await this.getWeatherFn();
      this.panes.push({ tabTitle: '自定义',
        cleanDate: moment().format('YYYY-MM-DD'),
        todayWeather: res.result_data,
        cleanPlace: undefined,
        cleanPeopleCount: undefined,
        cleanTool: undefined,
        cleanCap: undefined,
        cleanCapPeople: undefined,
        totalCleanCap: undefined,
        cleanRate: undefined,
        cleanRateExplain: undefined,
        cleanEffect: undefined,
        checkUser: Vue.ls.get(USER_INFO).username,
        sceneFileOneList: [],
        sceneFileTwoList: [],
        cleanBeforeSceneFileList: [],
        cleanAfterSceneFileList: [],
        cleanBeforeElectricFileList: [],
        cleanAfterElectricFileList: [],
        editable: true,
        type: 'add',
        id: uuid()
      });
    },
    async add () {
      const currentIndex = this.panes.findIndex(item => item.id == this.activeKey);
      const editable = this.panes[currentIndex].editable;
      if (editable) {
        this.confirmEditableTab();
        // const valid = await this.$refs.executionLogParent[currentIndex].validDate();
        // if (!valid)
      } else {
        await this.addIntialItem();
        this.activeKey = this.panes[this.panes.length - 1].id;
      }
    },
    remove (targetKey) {
      this.$confirm({
        title: '是否确认删除？',
        onOk: () => {
          const uuidArr = targetKey.split('-');
          if (uuidArr.length === 1) {
            this.loading = true;
            deleteCleanLogApi({ businessesId: targetKey }).then(res => {
              if (res.result_code == '1') {
                this.loading = false;
                const orderExecuteRate = res.result_data;
                this.afterRemoveSetActivityKeyFn(targetKey);
                this.updateOpsWorkOrderCleanVo(orderExecuteRate);
              }
            }).catch(() => {
              this.loading = false;
            });
          } else {
            this.afterRemoveSetActivityKeyFn(targetKey);
          }
        },
        onCancel () { }
      });
    },
    async afterRemoveSetActivityKeyFn (targetKey) {
      let activeKey = this.activeKey;
      let lastIndex;
      this.panes.forEach((pane, i) => {
        if (pane.id === targetKey) {
          lastIndex = i - 1;
        }
      });
      const panes = this.panes.filter(pane => pane.id != targetKey);
      if (panes.length && activeKey === targetKey) {
        if (lastIndex >= 0) {
          activeKey = panes[lastIndex].id;
        } else {
          activeKey = panes[0].id;
        }
        if (activeKey.split('-').length === 1) this.tabChange(activeKey, true);
      } else if (panes.length == 0) {
        const res = await this.getWeatherFn();
        panes.push({
          tabTitle: '自定义',
          cleanDate: moment().format('YYYY-MM-DD'),
          todayWeather: res.result_data,
          cleanPlace: undefined,
          cleanPeopleCount: undefined,
          cleanTool: undefined,
          cleanCap: undefined,
          cleanCapPeople: undefined,
          totalCleanCap: undefined,
          cleanRate: undefined,
          cleanRateExplain: undefined,
          cleanEffect: undefined,
          checkUser: Vue.ls.get(USER_INFO).username,
          sceneFileOneList: [],
          sceneFileTwoList: [],
          cleanBeforeSceneFileList: [],
          cleanAfterSceneFileList: [],
          cleanBeforeElectricFileList: [],
          cleanAfterElectricFileList: [],
          editable: true,
          type: 'add',
          id: uuid()
        });
        activeKey = panes[0].id;
      }
      this.activeKey = activeKey;
      this.panes = panes;
    },
    exportDataFn () {
      this.exportLoading = true;
      exportData({ businessesId: this.componentData.id }).then(res => {
        this.exportLoading = false;
        this.$downloadFile(res.result_data);
      }).catch(() => {
        this.exportLoading = false;
      });
    },
    // 一条日志都没有时不校验。只有未完成时校验
    validateForm () {
      let result = true;
      if (this.panes.length == 1 && this.panes[0].id.split('-').length > 1) {
        result = true;
      } else {
        for (let i = 0; i < this.panes.length; i++) {
          if (this.panes[i].editable) {
            result = false;
            const paneId = this.panes[i].id;
            this.activeKey = paneId;
            // 已暂存的日志
            if (paneId.split('-').length === 1) {
              this.loading = true;
              // this.tabChange(paneId, true, true);
              getSingleCleanDetail({ businessesId: paneId }).then(res => {
                if (res.result_code === '1') {
                  this.loading = false;
                  this.$notification.error({
                    message: '系统提示',
                    description: `${moment(res.result_data.cleanDate).format('YYYY年MM月DD日')}的日志未完成，请完成`
                  });
                  const newPaneObj = omit(this.panes[i], 'cleanDate');
                  this.panes.splice(i, 1, Object.assign({}, res.result_data || {}, newPaneObj));
                }
              }).catch(() => {
                this.loading = false;
              });
            } else {
              this.activeKey = paneId;
              // 未暂存的日志
              this.$notification.error({
                message: '系统提示',
                description: `您有未完成的日志，请完成后提交`
              });
            }
            break;
          }
        }
      }
      return result;
    },
    tabChange (lastActiveKey, deletable) {
      const currentIndex = this.panes.findIndex(item => item.id == this.activeKey);
      const editable = this.panes[currentIndex].editable;
      if (editable && !deletable) {
        this.confirmEditableTab(lastActiveKey);
        return;
      }
      this.loading = true;
      this.activeKey = lastActiveKey;
      const uuidArr = lastActiveKey.split('-');
      // 非本地存在的日志
      if (uuidArr.length === 1) {
        getSingleCleanDetail({ businessesId: lastActiveKey }).then(res => {
          this.loading = false;
          if (res.result_code === '1') {
            const index = this.panes.findIndex(item => item.id == lastActiveKey);
            this.panes.splice(index, 1, Object.assign({}, this.panes[index], res.result_data || {}));
            // 数据备份
            // this.backups = Object.assign({}, this.panes[index], res.result_data || {});
          }
        }).catch(() => {
          this.loading = false;
        });
      }
    },
    updateActiveKey ({ id, orderExecuteRate, editable, type, isLeave, detail = {} }) {
      const index = this.panes.findIndex(item => item.id == this.activeKey);
      this.panes.splice(index, 1, {
        ...detail,
        editable,
        type,
        tabTitle: moment(detail.cleanDate).format('MM-DD') });
      // Object.assign()
      if (!isLeave) this.activeKey = id;
      // this.backups = { ...detail, editable, type, tabTitle: moment(detail.cleanDate).format('MM-DD') };
      this.updateOpsWorkOrderCleanVo(orderExecuteRate);
    },
    confirmEditableTab (lastActiveKey) {
      const _self = this;
      const index = _self.panes.findIndex(item => item.id == _self.activeKey);
      this.$confirm({
        title: '离开后信息将不被保存，是否暂存',
        okText: '暂存',
        cancelText: '不暂存',
        onOk: async () => {
          // 暂存离开当前页面
          const notValid = await this.validateCleanDate();
          if (notValid) return;
          const result = await _self.$refs.executionLogParent[index].doExecute('0', true);
          if (result) {
            if (lastActiveKey) {
              _self.activeKey = lastActiveKey;
              getSingleCleanDetail({ businessesId: lastActiveKey }).then(res => {
                if (res.result_code === '1') {
                  const index = _self.panes.findIndex(item => item.id == lastActiveKey);
                  _self.panes.splice(index, 1, Object.assign({}, _self.panes[index], res.result_data || {}));
                  // _self.backups = Object.assign({}, _self.panes[index], res.result_data || {});
                  _self.loading = false;
                }
              }).catch(() => {
                _self.loading = false;
              });
            } else {
              await this.addIntialItem();
              _self.activeKey = _self.panes[_self.panes.length - 1].id;
            }
          }
        },
        onCancel: async () => {
          // tab切换
          const uuidArr = _self.activeKey.split('-');
          // 清空上个清洗日志必填项校验
          const nextRef = _self.$refs.executionLogParent[index];
          nextRef && nextRef.clearValidate();

          if (lastActiveKey) {
            if (uuidArr.length > 1) _self.panes.splice(index, 1);
            _self.activeKey = lastActiveKey;
            _self.loading = true;
            // 已暂存日志
            if (lastActiveKey.split('-').length === 1) {
              getSingleCleanDetail({ businessesId: lastActiveKey }).then(res => {
                if (res.result_code === '1') {
                  const index = _self.panes.findIndex(item => item.id == lastActiveKey);
                  _self.panes.splice(index, 1, Object.assign({}, _self.panes[index], res.result_data || {}));
                  // _self.backups = Object.assign({}, _self.panes[index], res.result_data || {});
                  _self.loading = false;
                }
              }).catch(() => {
                _self.loading = false;
              });
            } else {
              // 非已暂存日志，使用本地数据
              _self.loading = false;
            }
          } else {
            // 编辑状态的添加
            if (uuidArr.length > 1) _self.panes.splice(index, 1);
            await _self.addIntialItem();
            _self.activeKey = _self.panes[_self.panes.length - 1].id;
          }
        }
      });
    },
    // 校验当前日期的日志表单
    async validateCleanDate () {
      const errMsg = await this.$refs.executionLogParent[this.currentIndex].validDate();
      if (errMsg) {
        this.$notification.error({
          message: '系统提示',
          description: '清洗日期不可为空'
        });
      }
      return errMsg;
    },
    // 暂存校验现场执行与监督 清洗日志日期必填
    async callGrandsontaging () {
      const notValid = await this.validateCleanDate();
      if (notValid) {
        // this.$emit('setStepsCurrentToSpotSupervise');
        return;
      }
      // 校验当前步骤是否是现场执行
      if (this.current === this.spotSuperviseIndex()) {
        const result = await this.$refs.executionLogParent[this.currentIndex].doExecute('0');
        return result;
      }
      return { result_code: '1' };
    }
  },
  computed: {
    computedClosable () {
      if (this.isDisabled) return !this.isDisabled;
      return true;
    },
    computedType () {
      return this.isDisabled ? 'card' : 'editable-card';
    },
    percent () {
      const num = this.componentData.orderExecuteRate || '';
      return num.replace(',', '');
    },
    currentIndex () {
      return this.panes.findIndex(item => item.id == this.activeKey);
    },
    computedTabsClass () {
      return this.isDisabled ? 'local-executionLog' : '';
    },
    judgeExecuteRate () {
      const { orderExecuteRate } = this.componentData;
      return orderExecuteRate && orderExecuteRate.replace(/,/g, '') > 100 ? 'execute-rate' : '';
    }
  },
  watch: {
    // current: {
    //   immediate: true,
    //   handler (val, oldVal) {
    //     const _self = this;
    //     if (JSON.stringify(val) != JSON.stringify(oldVal)) {
    //       if (val == this.spotSuperviseIndex()) {
    //         const index = _self.panes.findIndex(item => item.id === _self.activeKey);
    //         const editable = _self.panes[index].editable;
    //         _self.trigggleFootShow(!editable);
    //       } else {
    //         _self.trigggleFootShow(true);
    //       }
    //     }
    //   }
    // },
    // activeKey: {
    //   deep: true,
    //   immediate: true,
    //   handler (val, oldVal) {
    //     const index = this.panes.findIndex(item => item.id === val);
    //     const currentPaneItem = this.panes[index];
    //     const editable = currentPaneItem && currentPaneItem.editable;
    //     if (this.current == this.spotSuperviseIndex()) this.trigggleFootShow(!editable);
    //   }
    // }
  }
};
</script>

<style lang='less' scoped>
  :deep(.ant-progress ){
      .ant-progress-inner{
        background-color: #E8E8E8 !important;
      }
      .execute-rate{
        color: #f00;
      }
    }
  :deep(.ant-btn.ant-btn-link){
    color: #FF8F33;
    background-color: transparent;
    border-color: transparent;
    -webkit-box-shadow: none;
    box-shadow: none;
  }
  :deep(.detail_layout){
    .ant-upload-list.ant-upload-list-picture-card{
      padding-top: 6px;
    }
  }
  :deep(.ant-tabs.ant-tabs-card){
    &.local-executionLog{
      .ant-tabs-bar{
        .ant-tabs-tab:first-of-type{
          border-top-left-radius: 4px;
          border-bottom-left-radius: 4px;
        }
        .ant-tabs-tab:last-of-type{
          border-top-right-radius: 4px;
          border-bottom-right-radius: 4px;
        }
      }
    }
    .ant-tabs-ink-bar{
      display: none !important;
    }
    .ant-tabs-bar{
      display: flex;
      max-width: calc(100% - 200px);
      border-bottom: none;
      .ant-tabs-tab.ant-tabs-tab-active{
        border-color: #FF8F33;
      }
      .ant-tabs-tab:first-of-type{
        border-radius: 4px 0 0 4px;
      }
      .ant-tabs-tab{
        border-radius: 0;
        border-bottom: 1px solid #e8e8e8;
        .anticon.anticon-close.ant-tabs-close-x{
          visibility: hidden;
        }
        &:hover{
          border-color: #FF8F33;
          .anticon.anticon-close.ant-tabs-close-x{
            visibility: initial;
          }
        }
      }
      .ant-tabs-extra-content{
        float: none;
        order: 10000;
        margin-left: 0px;
        .ant-tabs-new-tab{
          width: 60px;
          height: 100%;
          font-size: 20px;
          font-weight: bolder;
          border-radius: 0 4px 4px 0;
        }
        .ant-btn{
          position: absolute;
          right: 0;
          top: 0;
        }
      }
    }
  }

</style>
