<template>
  <div>
    <span class='item-content-title'>停电、验电、挂设接地线、悬挂标示牌、装设遮栏（围栏）和个人防护等</span>
    <a-form-model
      :model="form"
      ref="SafeMeasure"
      :wrapperCol="{ style: 'width: calc(100% - 150px)' }"
      :rules='rules'
      labelAlign='left'
      class='mt-24'
    >
      <a-col :span='24'>
        <a-form-model-item label="上传图片" prop="safeMeasureTwoFileList">
          <uploadFileView
            v-model="form.safeMeasureTwoFileList"
            listType="picture-card"
            :disabled="isDisabled"
            uploadText='上传照片'
            accept=".jpg,.png,.jpeg,.bmp,.JPG,.PNG,.JPEG,.BMP"
            tip="最多上传5张照片，单张图片不能超过10MB，仅支持上传jpg、jpeg、png、bmp格式"

            :max-num='5'
            @change='checkPickure("safeMeasureTwoFileList")'
          />
        </a-form-model-item>
      </a-col >

    </a-form-model>
  </div>
</template>

<script>
import uploadFileView from '@/components/com/fileUploadView';
import VueViewer from '@/mixins/VueViewer';
export default {
  mixins: [VueViewer],
  components: { uploadFileView },
  props: {
    componentData: {
      type: Object,
      default: () => ({})
    },
    isDisabled: {
      type: Boolean,
      default: false
    }
  },
  data () {
    return {
      form: {
        safeMeasureTwoFileList: undefined
      },
      rules: {
        safeMeasureTwoFileList: [
          { required: true, message: '请上传图片', trigger: 'change' }
        ]
      }
    };
  },
  mounted () {
    let safeMeasureTwoFileList = [];
    const cleanDetailVo = this.componentData.cleanDetailVo || {};
    safeMeasureTwoFileList = cleanDetailVo.safeMeasureTwoFileList;
    Object.assign(this.form, { safeMeasureTwoFileList });
  },
  methods: {
    checkPickure (propertyName) {
      this.$nextTick(() => {
        this.$refs.SafeMeasure.validateField(propertyName);
      });
    },
    validateForm () {
      let result;
      this.$refs.SafeMeasure.validate(valid => {
        result = valid;
      });
      return result;
    }
  }
};
</script>
