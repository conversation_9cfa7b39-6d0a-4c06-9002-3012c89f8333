import {
  axios
} from '@/utils/gy-request';

// 日常工作配置-获取列表
export function getTaskList (data) {
  return axios({
    url: '/workSetting/getTaskList',
    method: 'POST',
    data
  });
}

// 日常工作配置-通过Id获取信息
export function taskDetail (data) {
  return axios({
    url: '/workSetting/taskDetail',
    method: 'POST',
    data
  });
}

// 日常工作配置-新增配置
export function insertTask (data) {
  return axios({
    url: '/workSetting/insertTask',
    method: 'POST',
    data
  });
}
// 日常工作配置-更新配置
export function updateTask (data) {
  return axios({
    url: '/workSetting/updateTask',
    method: 'POST',
    data
  });
}
// 日常工作配置-删除配置
export function deleteTask (data) {
  return axios({
    url: '/workSetting/deleteTask',
    method: 'POST',
    data
  });
}
// 模板配置-获取列表
export function getTempList (data) {
  return axios({
    url: '/workSetting/getTempList',
    method: 'POST',
    data
  });
}
// 模板配置-通过Id获取信息
export function tempDetail (data) {
  return axios({
    url: '/workSetting/tempDetail',
    method: 'POST',
    data
  });
}
// 模板配置-新增配置
export function insertTemp (data) {
  return axios({
    url: '/workSetting/insertTemp',
    method: 'POST',
    data
  });
}
// 模板配置-更新配置
export function updateTemp (data) {
  return axios({
    url: '/workSetting/updateTemp',
    method: 'POST',
    data
  });
}
// 模板配置-删除配置
export function deleteTemp (data) {
  return axios({
    url: '/workSetting/deleteTemp',
    method: 'POST',
    data
  });
}
