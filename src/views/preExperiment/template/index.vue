<template>
  <div class="pro-experiment-template" id="pro-experiment-template">
    <div class="solar-eye-search-model">
      <div class="solar-eye-search-content">
        <template-search ref="search"  @queryList='initList'></template-search>
      </div>
    </div>
    <div class="solar-eye-gap"></div>
    <div class="solar-eye-main-content" :style='`height: ${tableHeight+80}px`'>
      <div class='add-box'><throttle-button perms='preTestTemplate:add' label="新增"  @click='addFn' /></div>
      <a-spin :spinning='loading'>
        <a-row :gutter='[16,16]' v-if='!!templateConfigList.length'>
          <a-col :xl="4" :sm="6" :xs="24" v-for='(item,index) in templateConfigList' :key='index'>
            <card-item style='width: 100%;cursor: pointer' :cardItemData='item' />
          </a-col>
        </a-row>
        <div v-else class="empty-content">
          <img :src='EmptyPng' alt=''/>
          <span>暂无数据</span>
        </div>
      </a-spin>
    </div>
    <drawer-view ref="proExperimentDrawerView" @cancel="val=>initList(val)" parentId="pro-experiment-template" />
  </div>
</template>
<script>
import TemplateSearch from '@views/preExperiment/template/modules/Search.vue';
import CardItem from '@views/preExperiment/template/CardIem.vue';
import tableHeight from '@/mixins/tableHeightAndSearchModel';
import { templateConfigListApi } from '@api/isolarErp/preExperiment';
import initDict from '@/mixins/initDict';
import EmptyPng from '@/assets/images/preExperiment/empty.png';

export default {
  name: 'preExperiment',
  mixins: [ tableHeight, initDict ],
  components: { TemplateSearch, CardItem },
  provide () {
    return {
      editFn: this.doEdit,
      goDetail: this.goDetail,
      tarnsformDeviceFn: this.tarnsformDeviceFn
    };
  },
  data () {
    return {
      templateConfigList: [],
      loading: false,
      EmptyPng
    };
  },

  mounted () {
    this.initList();
  },
  methods: {
    initList (bol = true) {
      this.loading = bol;
      const param = this.$refs.search.queryParams;
      templateConfigListApi(param).then(res => {
        if (res.result_code === '1') {
          this.loading = false;
          this.templateConfigList = res.result_data;
        }
      }).catch(() => {
        this.loading = false;
        this.templateConfigList = [];
      });
    },
    // 新增预试 1：新增 2：编辑 4：详情
    addFn () {
      this.$refs.proExperimentDrawerView.init('1', null, '/preExperiment/template/modules/PreExperimentForm');
    },
    goDetail (itemData) {
      this.$refs.proExperimentDrawerView.init('4', itemData, '/preExperiment/template/modules/detail/PreExperimentDetail');
    },
    // 转换设备类型公共方法
    tarnsformDeviceFn (data) {
      for (var i = 0; i < data.length; i++) {
        if (data[i].childErpDeviceTypeInfo.length < 1) {
          data[i].childErpDeviceTypeInfo = undefined;
        } else {
          this.tarnsformDeviceFn(data[i].childErpDeviceTypeInfo);
        }
      }
      return data;
    }
  }
};
</script>
<style lang='less' scoped>
  .pro-experiment-template{
    .solar-eye-main-content{
      overflow: auto;
      .add-box{
        margin-bottom: 24px;
        display: flex;
        justify-content: flex-end;
      }
      .empty-content{
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        height: calc(100vh - 350px);
      }
    }
  }
</style>
