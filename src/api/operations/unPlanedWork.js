import { postAction } from '@/api/manage';
const baseUrl = process.env.VUE_APP_API_ERP_URL;
// const baseUrl = "http://192.168.157.137:8080"
// 非工作计划

const getPage = (params) => postAction(baseUrl + '/pdca/noPlanWork/page', params);
const addOrUpdate = (params) => postAction(baseUrl + '/pdca/noPlanWork/insertOrUpdate', params);
const exportList = (params) => postAction(baseUrl + '/pdca/noPlanWork/export', params);
const getCount = (params) => postAction(baseUrl + '/pdca/noPlanWork/count', params);
const postApprove = (params) => postAction(baseUrl + '/pdca/noPlanWork/approve', params);
const unPlanedWorkDel = (params) => postAction(baseUrl + '/pdca/noPlanWork/delete', params);
const getOrderDetail = (params) => postAction(baseUrl + '/pdca/noPlanWork/detail', params);

export const distributeTangoTask = (params) => postAction(baseUrl + '/pdca/care/workOrder/distributeTask', params); // 无人机-指派飞手
export const performTangoTask = (params) => postAction(baseUrl + '/pdca/care/workOrder/performTask', params); // 无人机-执行任务
export const cancelUpload = (params) => postAction(baseUrl + '/pdca/care/workOrder/cancel', params); // 无人机-删除已上传但未保存文件
export const addTaskThird = (params) => postAction(baseUrl + '/pdca/care/workOrder/addTaskThird', params); // 无人机-新增第三方任务
export const accepTangoTask = (params) => postAction(baseUrl + '/pdca/care/workOrder/accepTask', params); // 无人机-退回任务
export const faultPage = (params) => postAction(baseUrl + '/pdca/care/fault/workOrder/faultPage', params); // 无人机巡检-获取工单处理缺陷列表
export const faultHandle = (params) => postAction(baseUrl + '/pdca/care/fault/workOrder/faultHandle', params); // 无人机巡检-工单缺陷处理
export const queryHandlePerson = (params) => postAction(baseUrl + '/pdca/care/fault/workOrder/queryHandlePerson', params); // 无人机巡检-工单缺陷处理
export const changeLeader = (params) => postAction(baseUrl + '/pdca/care/fault/workOrder/changeLeader', params); // 无人机巡检-查询协作人、负责人
export const countDefectByOrderId = (params) => postAction(baseUrl + '/pdca/care/fault/workOrder/countDefectByOrderId', params); // 无人机巡检-获取缺陷数量统计

export {
  getPage,
  addOrUpdate,
  exportList,
  getCount,
  postApprove,
  unPlanedWorkDel,
  getOrderDetail
};
