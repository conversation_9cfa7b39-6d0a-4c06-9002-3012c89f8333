<template>
  <a-modal title="修改密码" :visible="visible" :closable="false" :maskClosable="false" wrapClassName="security_modal"  width="480px">
    <div class="ant-alert-message"  style="margin-bottom: 24px">为了保证您的账号安全，请您设置新的登录密码</div>
    <a-form-model ref="ruleForm" :model="form" :rules="rules" :label-col="{ span: 5 }" :wrapper-col="{ span: 18 }">
      <a-form-model-item label="新密码" prop="password">
        <a-input-password v-model="form.password" autocomplete="new-password"> </a-input-password>
      </a-form-model-item>
      <a-form-model-item label="确认新密码" prop="confirmpassword">
        <a-input-password v-model="form.confirmpassword" autocomplete="new-password"> </a-input-password>
      </a-form-model-item>
    </a-form-model>
    <template #footer>
      <a-button @click="handleOk" type="primary"  size="large" :disabled="confirmLoading">确定</a-button>
    </template>
  </a-modal>
</template>
<script>
import { postAction } from '@/api/manage';
import { USER_INFO } from '@/store/mutation-types';
import { aesEncrypt } from '@/utils/verify';
export default {
  data () {
    return {
      visible: false,
      confirmLoading: false,
      form: {
        password: '',
        confirmpassword: ''
      },
      rules: {
        password: [
          { required: true, message: '请输入新密码', trigger: 'blur' },
          { validator: this.validateToNextPassword }
        ],
        confirmpassword: [
          {
            required: true,
            trigger: 'blur',
            message: '请输入确认新密码'
          },
          { validator: this.handlePasswordCheck }
        ]
      }
    };
  },
  methods: {
    showModal () {
      this.visible = true;
    },
    validateToNextPassword  (rule, value, callback) {
      if (value) {
        let reg = /^(?=.*[a-zA-Z])(?=.*\d)(?=.*[\x21-\x2f\x3a-\x40\x5b-\x60\x7B-\x7F])[\da-zA-Z\x21-\x2f\x3a-\x40\x5b-\x60\x7B-\x7F]{8,14}$/;
        if (!reg.test(value)) {
          callback(new Error('密码必须包含数字、字母及特殊符号，长度8-14位，不允许有空格！'));
        } else {
          if (this.form.confirmpassword && value !== this.form.confirmpassword) {
            callback(new Error('两次输入的密码不一致！'));
          } else if (value == this.form.confirmpassword) {
            this.$refs.ruleForm.validateField(['confirmpassword']);
          }
          callback();
        }
      }
      callback();
    },
    handlePasswordCheck (rule, value, callback) {
      if (value && this.form.password && value !== this.form.password) {
        callback(new Error('两次密码不一致！'));
      } else if (value && this.form.password && value == this.form.password) {
        this.$refs.ruleForm.validateField(['password']);
      }
      callback();
    },
    handleOk (e) {
      this.$refs.ruleForm.validate(valid => {
        if (valid) {
          this.confirmLoading = true;
          let user = Vue.ls.get(USER_INFO);
          let params = Object.assign({}, this.form, { username: user.username });
          params.password = aesEncrypt(params.password);
          params.confirmpassword = aesEncrypt(params.confirmpassword);
          postAction('/sys/user/firstChangePassword', params).then(res => {
            this.confirmLoading = false;
            if (res.success) {
              this.$message.success(res.message);
              this.$ls.set('firstTimeLogin', false);
              this.$store.commit('SET_FirstTimeLogin', false);
              this.visible = false;
              this.$store.dispatch('Logout').then(() => {
                if (this.$route.path !== '/user/login') {
                  this.$router.push({
                    path: '/user/login',
                    params: { 'username': user.username }
                  }).catch(res => {
                    console.log(res);
                  });
                }
              });
            } else {
              this.$message.warning(res.message);
            }
          });
        } else {
          return false;
        }
      });
    }
  }
};
</script>
<style lang="less" scoped>
  :deep(.security_modal) {
    .ant-modal-header {
      border-bottom: none;
    }
    .ant-modal-footer {
      border-top: none;
      text-align: center;
      padding-bottom: 24px;
      button {
        width: 416px
      }
    }
    .ant-modal-title {
      font-size: 16px;
      font-weight: 600;
    }
    .ant-modal-header {
      padding: 32px 32px 0;
    }
    .ant-modal-close {
      top: 12px;
    }
    .ant-modal-body {
      padding: 16px 32px 0;
    }
  }
</style>
