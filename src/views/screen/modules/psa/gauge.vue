<template>
  <div class="gauge-model" ref="gauge"></div>
</template>
<script>
// 引入基本模板
const echarts = require('echarts/lib/echarts');
// 引入折线图等组件
require('echarts/lib/chart/gauge');
require('echarts/lib/chart/radar');
// 引入提示框和title组件，图例
require('echarts/lib/component/tooltip');
require('echarts/lib/component/title');
require('echarts/lib/component/legend');
export default {
  data () {
    return {
      gauge: null
    };
  },
  methods: {
    initGauge (data) {
      const normal = (isNaN(data.value) ? 0 : Number(data.value));
      if (!this.$refs.gauge) { // dom 不存在时不画
        return;
      }
      // 不重复初始化
      let gauge = echarts.getInstanceByDom(this.$refs.gauge);
      if (!gauge) {
        gauge = echarts.init(this.$refs.gauge);
        this.$once('hook:beforeDestroy', function () {
          echarts.dispose(gauge);
        });
      }
      const all = 100;
      let option = {
        title: {
          show: false,
          text: '上月PR',
          top: 'center',
          left: 'center',
          textStyle: {
            fontSize: 14,
            color: '#fff'
          }
        },
        grid: {
          x: 0,
          y: 0,
          x2: 0,
          y2: 0,
          containLabel: true
        },
        series: [{
          name: '指标',
          type: 'gauge',
          max: 100,
          detail: {
            show: false
          },
          center: ['50%', '60%'],
          radius: '85%',
          startAngle: 200,
          endAngle: -20,
          data: [{
            value: 100
          }],
          axisLine: {
            show: false
          },
          splitLine: {
            length: 0
          },
          axisLabel: {
            distance: -20,
            show: true,
            padding: [0, 10],
            formatter: function (value) {
              if (value === 0 || value === 100) {
                return value + '%' + '\n';
              }
              return '';
            },
            color: '#67C9D3',
            lineHeight: -5,
            fontSize: 14
          },
          pointer: {
            show: false
          },
          axisTick: {
            show: false
          }
        },
        {
          name: '正常数',
          type: 'gauge',
          center: ['50%', '55%'],
          radius: '85%',
          startAngle: 200,
          endAngle: -20,
          axisLine: {
            lineStyle: {
              color: [
                [
                  normal / all,
                  new echarts.graphic.LinearGradient(0, 0, 1, 0, [{
                    offset: 0,
                    color: 'rgba(82, 187, 233, 1)'
                  },
                  {
                    offset: 1,
                    color: 'rgba(103, 211, 166, 1)'
                  }
                  ])
                ],
                [1, '#434A51']
              ],
              width: 10
            }
          },
          axisLabel: {
            show: false
          },
          axisTick: {
            show: false
          },
          splitLine: {
            show: false
          },
          itemStyle: {
            show: false
          },
          detail: {
            show: true,
            offsetCenter: ['0%', '-5%'],
            lineHeight: 20,
            formatter: '{value}%\n{a|上月PR}',
            color: '#67C9D3',
            fontSize: 14,
            rich: {
              a: {
                color: '#67C9D3',
                fontSize: 14
              }
            }
          },
          title: {
            // 标题
            show: false
          },
          data: [{
            value: normal
          }],
          pointer: {
            show: false
          }
        }
        ]
      };
      gauge.setOption(option, true);
    }
  }
};
</script>

<style lang="less" scoped>
  .gauge-model {
    margin-left: -20px;
    width: calc(100% + 40px);
    height: calc(100% + 10px)
  }
</style>
