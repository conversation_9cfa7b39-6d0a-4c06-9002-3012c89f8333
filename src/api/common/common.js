import { axios } from '@/utils/erp-request';
import { getAction } from '@api/manage';
const baseUrl = process.env.VUE_APP_API_BASE_URL;

// 图片预览
export function getComFilePreview (data) {
  return axios({
    url: '/equipment/v1/archives/filePreview',
    method: 'post',
    data
  });
}

// 设备树-最新
export const getComDeviceTypeTree = (params) => getAction(baseUrl + '/device-type/tree', params);
// 设备类型 => 设备名称

export const getListCompleteDevice = (params) => getAction(baseUrl + '/device/listCompleteDevice', params);
