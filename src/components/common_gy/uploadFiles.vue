<template>
  <div>
    <a-upload class="upload" ref="upload" action="#" :accept="accept" @change="sendfileList"
      :beforeUpload="beforeUpload" :multiple="false" @preview="handlePreview"
      :showUploadList="{ showRemoveIcon : !disabled }"
      :remove="handleRemove" list-type="picture" :file-list="fileList" :class="{'upload-list-inline':isInline }">
      <a-button v-show="!disabled" :disabled="disabled || loading || (maxNum && fileList.length >= maxNum)" :loading="loading" class="solar-eye-btn-primary">上传</a-button>
      <p @click.stop="noEvent()" v-show="!disabled && tip" class="ant-upload-hint" style="color:red" :class="{'is-inline':tipIsInline}">提示：{{tip}}</p>
    </a-upload>
    <pic-view :showDownload="showDownload" :noConvert="noConvert" v-model="file.open" :file="file" @change="handlePreview(null)" />
    <slot name="downloadExcelButton"></slot>
  </div>
</template>

<script>
// 上传接口回调 触发父组件 change 事件
// import Vue from 'vue'
import axios from 'axios';
import { ACCESS_TOKEN, TENANT_ID } from '@/store/mutation-types';
import { apiBaseUrl } from '@/utils/gy-request';
import { exportFile } from '@/api/common_gy/erp-manage.js';
import picView from '@/components/erp/picView';
const review_type = ['png', 'jpg', 'jpeg', 'bmp', 'pdf', 'PNG', 'JPG', 'BMP', 'JPEG'];
export default {
  model: {
    prop: 'defaultFile',
    event: 'set'
  },
  components: {
    picView
  },
  props: {
    // 上传地址  例如 /sys/oss/file/upload
    url: {
      type: String,
      default () {
        return '/sys/oss/file/upload';
      }
    },
    // 文件列表
    defaultFile: {
      type: [Array, Object],
      default () {
        return [];
      }
    },
    // 是否为只读属性
    disabled: {
      type: Boolean,
      default () {
        return false;
      }
    },
    // 单个文件大小最大上限
    maxSize: {
      type: Number,
      default: 10
    },
    // 允许上传的文件数量
    maxNum: {
      type: Number,
      default: 5
    },
    // 允许上传的文件格式
    accept: {
      type: String,
      default: ''
    },
    // 提示语
    tip: {
      type: String,
      default: ''
    },
    tipIsInline: {
      type: Boolean,
      default: false
    },
    // 是否显示下载
    showDownload: {
      type: Boolean,
      default () {
        return true;
      }
    },
    // 是否允许预览
    review: {
      Boolean,
      default: true
    },
    isInline: {
      type: Boolean,
      default: false
    },
    // 不需要base64 传1 需要则不传
    isNeedBase: {
      type: Number,
      default: null
    }
  },
  watch: {
    watchfiles: {
      // 代表在wacth里声明了firstName这个方法之后立即先去执行handler方法
      immediate: true,
      handler (val, old) {
        let files = (Array.isArray(val) ? val : (val ? [val] : []));
        files.forEach((item, index) => {
          item.uid = (item.uid ? item.uid : index);
          item.name = item.fileName || item.pathName;
          item.url = item.path;
        });
        this.fileList = files;
      }
    }
  },
  computed: {
    watchfiles () {
      return this.defaultFile;
    },
    navTheme () {
      return this.$store.state.app.theme;
    }
  },
  data () {
    return {
      loading: false,
      fileList: [],
      file: {
        open: false
      }
    };
  },
  methods: {
    getExistName () {
      let arr = [];
      this.fileList.length > 0 && this.fileList.forEach(item => {
        arr.push(item.fileName);
      });
      return arr;
    },
    /*
        文件上传
      */
    beforeUpload (file) {
      let _this = this;
      _this.loading = true;
      _this.$emit('loadingChange', _this.loading);
      if (!file || (file && !file.size)) {
        _this.loading = false;
        _this.$emit('loadingChange', _this.loading);
        _this.$message.warning('禁止上传空文件');
        return false;
      }
      if (_this.maxNum && _this.fileList.length >= _this.maxNum) {
        _this.loading = false;
        _this.$emit('loadingChange', _this.loading);
        _this.$message.warning(`最多上传${_this.maxNum}个附件，请确认!`);
        return false;
      }
      let size = (file.size / 1024 / 1024).toFixed(2);
      if (size > _this.maxSize) {
        _this.loading = false;
        _this.$emit('loadingChange', _this.loading);
        _this.$message.warning(`请上传小于${_this.maxSize}MB文件`);
        return false;
      }
      if (_this.getExistName().indexOf(file.name) > -1 && this.isInline) {
        _this.loading = false;
        _this.$emit('loadingChange', _this.loading);
        _this.$message.warning(`已存在${file.name}!`);
        return false;
      }
      // 文件格式验证
      let ext = file.name.substring(file.name.lastIndexOf('.') + 1);
      if (['exe', 'com'].includes(ext)) {
        _this.loading = false;
        _this.$emit('loadingChange', _this.loading);
        _this.$message.warning('不允许上传安装包!');
        return false;
      }
      if (_this.accept && _this.accept.indexOf(ext) == -1) {
        _this.loading = false;
        _this.$emit('loadingChange', _this.loading);
        _this.$message.warning(`只能上传${_this.accept}格式文件!`);
        return false;
      }
      let fd = new FormData(); // 表单格式
      fd.append('file', file); // 添加file表单数据
      if (_this.isNeedBase) {
        fd.append('isNeedBase', _this.isNeedBase);
      }
      const token = Vue.ls.get(ACCESS_TOKEN);
      axios({
        method: 'post',
        url: apiBaseUrl + _this.url,
        data: fd,
        headers: {
          'Content-Type': 'multipart/form-data',
          'X-Access-Token': token,
          'tenant_id': Vue.ls.get(TENANT_ID)
        }
      }).then((response) => {
        _this.loading = false;
        _this.$emit('loadingChange', _this.loading);
        let data = response.data;
        if (response.status == '200' && data.success && data.result) {
          _this.$message.success('上传成功!');
          _this.fileList = [..._this.fileList, data.result];
          // 上传接口回调 触发父组件 change 事件
          _this.$emit('change', data.result);
          _this.$emit('set', _this.fileList);
        } else {
          _this.$notification.error({
            message: '系统提示',
            description: (data.message ? data.message : '上传失败')
          });
        }
      }).catch(() => {
        _this.loading = false;
        _this.$emit('loadingChange', _this.loading);
      });
      return false;
    },
    // 传递文件值
    sendfileList () {
      this.$emit('set', this.fileList);
    },
    // 移除
    handleRemove (file) {
      const self = this;
      if (self.disabled) {
        return;
      }
      const index = self.fileList.indexOf(file);
      const newFileList = self.fileList.slice();
      newFileList.splice(index, 1);
      self.fileList = newFileList;
      self.$emit('set', self.fileList);
    },
    // 图片类型文件支持预览
    handlePreview (file) {
      if (!this.review) {
        return;
      }
      if (file) {
        /* 追加判断非图片、PDF文件不可预览 */
        let is_pic_pdf = this.checkFileType(file);
        if (!is_pic_pdf) {
          this.downLoad(file);
          return;
        }
        this.file = {
          ...file,
          'is_oss': true,
          'open': true
        };
      } else {
        this.file = {
          open: false
        };
      }
    },
    /*
        不支持预览的文件点击时下载文件
      */
    downLoad (file) {
      let _this = this;
      _this.$message.info('正在下载请稍后!');
      if (file.base64code && file.base64code != ' ') {
        _this.$downloadFile({ 'fileBase64Code': file.base64code, 'fileName': file.fileName });
      } else {
        let params = {
          path: file.pathName,
          fileName: file.fileName
        };
        exportFile('/sys/oss/file/downLoad', params).then((data) => {
          let fileReader = new FileReader();
          fileReader.onload = function () {
            try {
              let jsonData = JSON.parse(this.result);
              if (jsonData.code || !jsonData.result) {
                this.$message.destroy();
                // 说明是普通对象数据，后台转换失败
                _this.$message.error(jsonData.message || '文件下载失败');
              }
            } catch (err) {
              if (typeof window.navigator.msSaveBlob !== 'undefined') {
                window.navigator.msSaveBlob(new Blob([data]), file.fileName);
                this.$message.destroy();
                _this.$message.success('下载成功');
              } else {
                let url = window.URL.createObjectURL(new Blob([data]));
                let link = document.createElement('a');
                link.style.display = 'none';
                link.href = url;
                link.setAttribute('download', file.fileName);
                document.body.appendChild(link);
                link.click();
                document.body.removeChild(link);
                window.URL.revokeObjectURL(url);
                this.$message.destroy();
                _this.$message.success('下载成功');
              }
            }
          };
          fileReader.readAsText(data);
        });
      }
    },
    /*
        图片、PDF格式判断
      */
    checkFileType (file) {
      let extension0 = '';
      let extension1 = '';
      let extension2 = '';
      if (file.name) {
        extension0 = file.name.split('.')[file.name.split('.').length - 1];
      }
      if (file.annexName) {
        extension1 = file.annexName.split('.')[file.annexName.split('.').length - 1];
      }
      if (file.pathName) {
        extension2 = file.pathName.split('.')[file.pathName.split('.').length - 1];
      }
      return review_type.includes(extension0) || review_type.includes(extension1) || review_type.includes(extension2);
    },
    noEvent () {

    }
  }
};
</script>

<style lang="less" scoped>
  .is-inline {
    display: initial;
    padding-left: 20px;
  }
</style>
<style scoped>
  /* tile uploaded pictures */
  .upload-list-inline:deep(.ant-upload-list-item) {
    float: left;
    min-width: 300px;
    width: fit-content;
    margin-right: 8px;
  }

  .upload-list-inline:deep(.ant-upload-animate-enter) {
    animation-name: uploadAnimateInlineIn;
  }

  .upload-list-inline:deep(.ant-upload-animate-leave) {
    animation-name: uploadAnimateInlineOut;
  }
</style>
