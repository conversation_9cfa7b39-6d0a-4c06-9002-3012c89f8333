class ColorGenerator {
  constructor (segmentCount = 12, saturationRange = [60, 95], valueRange = [60, 95]) {
    this.segmentCount = segmentCount; // 色相段的数量，用于控制颜色的多样性
    this.saturationRange = saturationRange; // 饱和度范围，高饱和度保证鲜亮
    this.valueRange = valueRange; // 亮度范围，中高亮度保证鲜亮
    this.hueStep = 360 / segmentCount; // 每个色相段的大小
    this.generatedHues = new Set(); // 已生成的色相集合，确保唯一性
  }

  getRandomDistinctColor () {
    let hue = this.getUniqueHue();
    let saturation = Math.random() * (this.saturationRange[1] - this.saturationRange[0]) + this.saturationRange[0];
    let value = Math.random() * (this.valueRange[1] - this.valueRange[0]) + this.valueRange[0];

    return this.HSVtoHex(hue, saturation, value);
  }

  getUniqueHue () {
    if (this.generatedHues.size === this.segmentCount) {
      this.generatedHues.clear(); // 如果所有色相都被使用过，则重新开始
    }

    let hue;
    do {
      hue = Math.floor(Math.random() * this.segmentCount) * this.hueStep + this.hueStep / 2;
    } while (this.generatedHues.has(hue));

    this.generatedHues.add(hue);

    return hue;
  }

  HSVtoHex (h, s, v) {
    s /= 100;
    v /= 100;
    let c = v * s;
    let x = c * (1 - Math.abs((h / 60) % 2 - 1));
    let m = v - c;
    let r, g, b;

    if (h < 60) {
      r = c; g = x; b = 0;
    } else if (h < 120) {
      r = x; g = c; b = 0;
    } else if (h < 180) {
      r = 0; g = c; b = x;
    } else if (h < 240) {
      r = 0; g = x; b = c;
    } else if (h < 300) {
      r = x; g = 0; b = c;
    } else {
      r = c; g = 0; b = x;
    }
    r = Math.round((r + m) * 255);
    g = Math.round((g + m) * 255);
    b = Math.round((b + m) * 255);

    return `#${((1 << 24) + (r << 16) + (g << 8) + b).toString(16).slice(1).padStart(6, '0').toUpperCase()}`;
  }
  reset () {
    this.generatedHues.clear(); // 清空已生成的色相集合
  }
}

export default ColorGenerator;
