<template>
  <div class="experiment-project-item">
    <div class="title-box">
      <div class="detail_layout_content">
        <span class="left">{{projectItemIndex+1}}.试验项目</span>
        <span class='right'>{{projectItemData.projectNameLabel}}</span>
      </div>
      <div class="operation-box">
        <span>{{ projectItemData.isEnable ? '已启用': '已禁用' }}</span>
      </div>
    </div>
    <div class='table-item-box' v-for='(item,index) in projectItemData.projectTableList' :key='index'>
      <table-item
        style='width: 60%'
        :row-list='item.projectTitleList.filter(j=>j.titleType===1)'
        :col-list='item.projectTitleList.filter(j=>j.titleType===2)'
        :project-table-remark='item.projectTableRemark'
        :is-have-sub-title='item.isHaveSubTitle'
      />
      <span>{{item.isCanEdit?'支持编辑':'不支持编辑'}}</span>
    </div>
  </div>
</template>

<script>
import TableItem from '@views/preExperiment/template/modules/TableItem.vue';

export default {
  name: 'ExperimentProjectItem',
  components: { TableItem },
  props: {
    projectItemData: {
      type: Object
    },
    projectItemIndex: {
      type: Number
    }
  }
};
</script>

<style scoped lang="less">
.experiment-project-item {
  margin-bottom: 24px;
  .title-box{
    display: flex;
    justify-content: space-between;
    padding-left: 12px;
    margin-bottom: 16px;
    .detail_layout_content {
      display: inline-flex;
      .left {
        width: 160px;
        text-align: right;
        padding-right: 8px;
        color: #666;
        font-weight: 550;
        flex: none;
        &::after {
          content: "："
        }
      }
      .right {
        flex: 1;
      }
    }
    .operation-box{
      display: flex;
      gap: 8px;
      .split{
        position: relative;
        top: 0.3em;
        display: inline-block;
        height: 0.9em;
        margin: 0 8px;
        vertical-align: middle;
        border-top: 0;
        border-inline-start: 1px solid rgba(5, 5, 5, 0.2);
      }
    }
  }
  .table-item-box{
    padding: 0 0 0 24px;
    display: flex;
    gap: 24px;
    justify-content: space-between;
  }
}

:root[data-theme='dark'] {
  .experiment-project-item {
    .title-box{
      .left, .right {
        color: #fff;
      }
    }
    .operation-box{
      .split{
        border-inline-start: 1px solid rgba(255, 255, 255, 0.3);
      }
    }
  }
}
</style>
