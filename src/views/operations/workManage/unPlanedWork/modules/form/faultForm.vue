<!-- 故障 缺陷基础表单 -->
<template>
  <a-form-model :model="baseForm" :rules="base_rules" ref="faultForm" :labelCol="{ style: 'width: 120px' }" :wrapperCol="{ style: 'width: calc(100% - 120px)' }">
    <a-row>
      <a-col :xl='8' :sm='12' :xs='24'>
        <a-form-model-item label="设备状态" prop="deviceType">
          <a-select v-model="baseForm.deviceType" placeholder="请选择" @change="getfaultNameList" disabled>
            <a-select-option v-for="item in dictMap.two_device_run_sts" :key="item.key" :value="item.dataValue">{{item.dataLable}}</a-select-option>
          </a-select>
        </a-form-model-item>
      </a-col>
      <template>
        <a-col :xl='8' :sm='12' :xs='24' v-if="isOrder">
          <a-form-model-item label="缺陷来源">
            <a-select v-model="baseForm.orderSource" disabled placeholder="请选择">
              <a-select-option v-for="item in dictMap.two_order_source" :key="item.key" :value="item.dataValue">{{item.dataLable}}</a-select-option>
            </a-select>
          </a-form-model-item>
        </a-col>
        <a-col :xl='8' :sm='12' :xs='24'>
          <a-form-model-item label="实体电站" :style="{opacity:order().orderSource =='4'?1:0}">
            <a-input disabled :value="baseForm.psName" style="width: 100%;" />
          </a-form-model-item>
        </a-col>
      </template>
      <a-col :xl='24' :sm='24' :xs='24'>
        <a-form-model-item :label="(baseInfo.taskType=='7'?'故障':'缺陷')+'描述'" prop="taskDescription">
          <a-textarea size="default" :max-length="200" :auto-size="{ minRows: 2, maxRows: 2}" :disabled="isDisabled"
            v-model="baseForm.taskDescription" @blur="baseForm.taskDescription = $trim($event)"
            placeholder="发生范围+发生概率+修复费用+预计损失(安全、电量损失)"></a-textarea>
        </a-form-model-item>
      </a-col>
      <a-col :xl='8' :sm='12' :xs='24'>
        <a-form-model-item label="设备类型" prop="deviceTypeId">
          <g-cascader v-model="baseForm.deviceTypeId" :allowClear="false" :options="options"
            @change="deviceTypeChange" :disabled="isDisabled && !isEdit" />
        </a-form-model-item>
      </a-col>
      <template v-if="isDevice && order().orderSource != '6'">
        <a-col :xl='8' :sm='12' :xs='24'>
          <a-form-model-item label="设备名称" :prop="!(isDisabled || disabledDevice)?'deviceId':'deviceName'">
            <a-select v-model="baseForm.deviceId" placeholder="请输入" v-if="!(isDisabled || disabledDevice)" @change="deviceNameChange" :disabled="isDisabled || disabledDevice"
              show-search>
              <a-select-option v-for="item in deviceNameList" :key="item.id" :data-name="item.name" :value="String(item.id)">{{item.name}}</a-select-option>
            </a-select>
            <a-input v-else v-model="baseForm.deviceName" disabled></a-input>
          </a-form-model-item>
        </a-col>
        <a-col :xl='8' :sm='12' :xs='24'>
          <a-form-model-item label="设备编号" prop="deviceNo" show-search>
            <a-select v-model="baseForm.deviceNo" placeholder="请选择" :disabled="isDisabled || disabledDevice">
              <a-select-option v-for="(item,k) in deviceNoList" :key="k" :value="item.deviceId">{{item.deviceId}}</a-select-option>
            </a-select>
          </a-form-model-item>
        </a-col>
        <a-col :xl='8' :sm='12' :xs='24'>
          <a-form-model-item :label="(baseInfo.taskType=='7'?'故障':'缺陷')+'名称'" prop="defectName">
            <a-select v-model="baseForm.defectName" :disabled="isDisabled" placeholder="请选择" allowClear v-if="!isDisabled">
              <a-select-option v-for="item in  faultNameList" :key="item.id.toString()" :value="item.name">
                {{item.name}}
              </a-select-option>
            </a-select>
            <a-input disabled v-model="baseForm.defectName" v-else></a-input>
          </a-form-model-item>
        </a-col>
      </template>
      <a-col :xl='8' :sm='12' :xs='24'>
        <a-form-model-item :label="(baseInfo.taskType=='7'?'故障':'缺陷')+'类别'" prop="defectType" show-search>
          <a-select v-model="baseForm.defectType" disabled placeholder="请选择">
            <a-select-option v-for="item in dictMap.two_fault_classify" :key="item.key" :value="item.dataValue">{{item.dataLable}}</a-select-option>
          </a-select>
        </a-form-model-item>
      </a-col>
      <a-col :xl='8' :sm='12' :xs='24' v-if="order().orderSource != '6'">
        <a-form-model-item label="发现人" prop="findUserName">
          <a-input v-model="baseForm.findUserName" disabled></a-input>
        </a-form-model-item>
      </a-col>
      <a-col :xl='8' :sm='12' :xs='24'>
        <a-form-model-item label="发现时间" prop="findTime">
          <a-date-picker style="width:100%" show-time format="YYYY-MM-DD HH:mm" :allowClear="false" :disabled="isDisabled"
            valueFormat="YYYY-MM-DD HH:mm" placeholder="请选择" v-model="baseForm.findTime" :disabled-time="disabledDateTime"
            :disabled-date="disabledFindTime" />
        </a-form-model-item>
      </a-col>
      <a-col :xl='8' :sm='12' :xs='24'>
        <a-form-model-item label="预计消除时间" prop="predictRecoverTime">
          <a-input disabled v-model="baseForm.predictRecoverTime" style="width: 100%;" />
        </a-form-model-item>
      </a-col>
      <a-col :xl='8' :sm='12' :xs='24' v-if="isOrder && order().orderSource != '6'">
        <a-form-model-item label="责任人">
          <a-select v-model="baseForm.liablePerson" v-if="!isDisabled" :placeholder="isDisabled ?'':'请选择'" :disabled="isDisabled" style="width: 100%" allowClear>
            <a-select-option v-for="(item, k) in assignList" :hidden="item.hidden" :key="k" :value="item.username">
              {{ item.realName }}
            </a-select-option>
          </a-select>
          <a-input v-model="order().liablePersonName" v-else disabled></a-input>
        </a-form-model-item>
      </a-col>
      <template v-if="order().orderSource == '6' && type() !='5'">
        <a-col :xl='8' :sm='12' :xs='24'>
          <a-form-model-item label="协作人" prop="executorName">
            <a-input disabled :value="baseForm.executorName" style="width: 100%;" />
          </a-form-model-item>
        </a-col>
        <a-col :xl='8' :sm='12' :xs='24'>
          <a-form-model-item label="主负责人" prop="eliminateName">
            <a-input disabled :value="baseForm.eliminateName" style="width: 100%;" />
          </a-form-model-item>
        </a-col>
      </template>
      <a-col :xl="8" :sm="12" :xs="24">
        <a-form-model-item label="作业类别" prop="jobCategory">
          <a-select v-model="baseForm.jobCategory" :disabled="isDisabled && !isEdit" @change="jobCategoryChange" allowClear
            size="default" placeholder="请选择" style="width: 100%;">
            <a-select-option v-for="item in dictMap.job_category" :key="item.dataValue" :value="item.dataValue">
              {{item.dispName}}
            </a-select-option>
          </a-select>
        </a-form-model-item>
      </a-col>
      <a-col :xl="8" :sm="12" :xs="24">
        <a-form-model-item label="作业内容" prop="erpSqJobRiskMappingId">
          <a-select v-model="baseForm.erpSqJobRiskMappingId" :disabled="isDisabled && !isEdit" @change="erpSqJobRiskMappingIdChange" allowClear
            size="default" placeholder="请选择" style="width: 100%;">
            <a-select-option v-for="item in jobContentOptions" :key="item.id" :value="item.id" :code="item.riskLevel">
              {{item.jobContentName}}
            </a-select-option>
          </a-select>
        </a-form-model-item>
      </a-col>
      <a-col :xl="8" :sm="12" :xs="24">
        <a-form-model-item label="风险等级" prop="riskLevel">
          <a-select v-model="baseForm.riskLevel" :disabled="true" size="default" placeholder="请选择" style="width: 100%;">
            <a-select-option v-for="item in dictMap.risk_level" :key="item.dataValue" :value="item.dataValue">
              {{item.dispName}}
            </a-select-option>
          </a-select>
        </a-form-model-item>
      </a-col>
      <a-col :xl='8' :sm='12' :xs='24'>
        <a-form-model-item label="作业开始日期" prop="riskJobDate">
          <a-date-picker  style="width: 100%;" size="default" v-model="baseForm.riskJobDate"
            format="YYYY-MM-DD" valueFormat="YYYY-MM-DD" placeholder="请输入作业开始日期"
            :disabled-date="disabledRiskJobDate"></a-date-picker>
        </a-form-model-item>
      </a-col>
      <!-- 通用附件 -->
      <a-col :span='24' v-show="!(baseForm.isFarm == '1' && baseForm.taskType == '9')">
        <a-form-model-item label="附件" prop="uploadFileList">
          <uploadFile v-model="baseForm.uploadFileList" :disabled="isDisabled && !isEdit" :maxNum="5" :maxSize="10" :multiple="true" tip="最多上传5个文件,且上传的附件最大不超过10MB!"
            @set="fieldChange('faultForm','uploadFileList')">上传</uploadFile>
        </a-form-model-item>
      </a-col>
    </a-row>
  </a-form-model>
</template>

<script>
import moment from 'moment';
import initDict from '@/mixins/initDict';
import uploadFile from '@/components/com/fileUploadView';
import { formRule } from './formMixins';
import { getPlanTimeLimit } from '@/api/common_gy/workOrder.js';
import { queryHandlePerson } from '@/api/operations/unPlanedWork';
import { TENANT_ID } from '@/store/mutation-types';
export default {
  name: 'faultForm',
  mixins: [initDict, formRule],
  components: {
    uploadFile
  },
  data () {
    return {
      // 任务表单基础信息
      baseForm: {
        taskName: undefined, // 任务名称
        planTime: [], // 计划日期
        taskDescription: undefined, // 任务描述
        planProject: undefined, // 计划项目
        deviceName: undefined, // 设备名称
        deviceCode: undefined, // 设备编号
        defectName: undefined, // 故障名称
        workStatus: undefined, // 工作状态
        deviceType: '', // 设备状态
        assign: undefined, // 指派给
        jobCategory: undefined, // 作业类别
        erpSqJobRiskMappingId: undefined, // 作业内容
        riskLevel: undefined, // 风险等级
        riskJobDate: undefined, // 作业开始日期
        uploadFileList: [], // 附件列表
        isBudget: '',
        deviceTypeId: [], // 设备类型
        deviceId: undefined, // 设备名称
        deviceNo: [], // 设备编号
        findTime: '',
        findUserName: '',
        findUser: null,
        defectType: '',
        psName: '',
        predictRecoverTime: '',
        liablePerson: '',
        orderSource: '7',
        executorName: undefined, // 无人机-协作人
        eliminateName: undefined // 无人机-主负责人
      },
      taskTypeOptions: [], // 户用电站的任务类型
      ordinaryTypeOptions: [], // 非户用类型电站的的任务类型
      isDevice: true,
      nameChange: '缺陷',
      base_rules: {
        findUserName: [{
          required: true,
          message: '请填写发现人'
        }],
        taskName: [{
          required: true,
          message: '请填写任务名称'
        }], // 任务名称
        planTime: [{
          type: 'array',
          required: true,
          message: '请选择计划日期'
        }], // 计划日期
        pjPrice: [{
          required: true,
          message: '请填写施工价格'
        }], // 计划日期
        workStatus: [{
          required: true,
          message: '请选择工作状态'
        }], // 工作状态
        findTime: [{ required: true, message: '请填写发现时间' }],
        assign: [{
          required: true,
          message: '请选择指派人'
        }],
        // 其他校验根据任务类型设定
        deviceType: [{
          required: true,
          message: '请选择设备状态'
        }],
        deviceTypeId: [{
          required: true,
          message: '请选择设备类型'
        }], // 设备类型
        deviceId: [{
          required: true,
          message: '请选择设备名称'
        }], // 设备名称
        deviceName: [{
          required: true,
          message: '请选择设备名称'
        }],
        defectType: [{
          required: true,
          message: `${this.baseInfo.taskType == '7' ? '故障' : '缺陷'}类别不能为空`,
          trigger: 'change'
        }],
        defectName: [{
          required: true,
          message: `请选择${this.baseInfo.taskType == '7' ? '故障' : '缺陷'}名称`,
          trigger: 'change'
        }],
        uploadFileList: [{
          required: !['4', '6'].includes(this.order().orderSource),
          message: '请上传附件',
          trigger: 'change'
        }],
        executorName: [{
          required: true,
          message: '请选择协作人'
        }], // 设备类型
        eliminateName: [{
          required: true,
          message: '请选择主负责人'
        }], // 设备类型
        predictRecoverTime: [{ required: true, message: '预计消除时间不能为空' }],
        jobCategory: [{ required: true, message: '请选择作业类别', trigger: 'change' }],
        erpSqJobRiskMappingId: [{ required: true, message: '请选择作业内容', trigger: 'change' }],
        riskLevel: [{ required: true, message: '请选择风险等级', trigger: 'change' }],
        riskJobDate: [{ required: true, message: '请选择作业开始日期', trigger: 'change' }]
      },
      faultNameList: [],
      deviceNameList: [], // 设备名称
      deviceNoList: [], // 设备编号
      assignList: [] // 指派人
    };
  },
  computed: {
    isEdit () {
      return (this.order().orderSource == '4') && (this.type() == 5 || this.type() == 6) && this.type() != 3;
    },
    isOrder () {
      return this.order().orderSource && this.order().isWorkOrder == 1;
    }
  },
  watch: {
    'baseInfo.taskType' (val) {
      this.isDevice = true;
      this.disabledDevice = false;
      this.base_rules.defectName[0].message = `请选择${val == '7' ? '故障' : '缺陷'}名称`;
      this.base_rules.defectType[0].message = `${val == '7' ? '故障' : '缺陷'}类别不能为空`;
      this.$refs.faultForm.resetFields();
      this.setDeviceVal();
    },
    'baseInfo.workSubclass' (val) {
      this.baseForm.planTime = [];
    },
    'baseInfo.jobCategory' (val) {
      if (val) {
        this.riskAllocation(this.baseInfo.jobCategory);
      }
    }
  },
  mounted () {},
  created () {
    this.setDeviceVal();
    this.getDictMap('two_fault_classify,two_device_run_sts,two_order_source,job_category,risk_level');
  },
  methods: {
    moment,
    setDeviceVal () {
      if (this.baseInfo.taskType == '8') {
        this.baseForm.deviceType = '1';
      } else if (this.baseInfo.taskType == '7') {
        this.baseForm.deviceType = '2';
      }
    },
    // 获取预计消除时间
    getPlanTimeLimit () {
      getPlanTimeLimit({
        deviceTypeList: this.baseForm.deviceTypeId,
        twoDeviceRunSts: 2,
        sysTenantId: Vue.ls.get(TENANT_ID),
        findTime: this.baseForm.findTime
      }).then(res => {
        if (res.code == '200' && res.result) {
          this.baseForm.predictRecoverTime = moment(res.result).format('YYYY-MM-DD HH:mm');
        }
      });
    },
    validateForm (type) {
      this.baseForm.liablePerson = !this.baseForm.liablePerson ? '' : this.baseForm.liablePerson;
      return new Promise((resolve, reject) => {
        if (type == 0) {
          resolve(this.baseForm);
        } else {
          this.$refs.faultForm.validate(valid => {
            if (valid) {
              resolve(this.baseForm);
            } else {
              reject(new Error());
            }
          });
        }
      });
    },
    // 查询协作人
    getHandlePerson (orderId) {
      queryHandlePerson({ orderId: orderId }).then(res => {
        let arr = [];
        res.result_data.forEach(item => {
          arr.push(item.username);
          if (item.isLeader) {
            this.baseForm.eliminateName = item.username;
          }
        });
        this.baseForm.executorName = arr.toString();
      });
    }

  }
};
</script>

<style lang="less" scoped>
  .link-span {
    color: #FF8F33;
    text-decoration: underline;
    cursor: pointer;
  }
</style>
