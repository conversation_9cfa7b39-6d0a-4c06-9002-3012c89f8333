import { getDeviceNameList, getDeviceIdList, getFaultName, getDeviecClassByDeviceId } from '@/api/common_gy/faultManage.js';
import moment from 'moment';
import { getDate } from '@/api/config/digitalRegulation';
import { getAssignListByPSId } from '@/api/common_gy/common.js';
import { USER_INFO } from '@/store/mutation-types';
import { getThirdAssgin } from '@/api/tango/taskManage.js';
import { riskAllocation } from '@/api/isolarErp/safetyquality/safetyRisk';
export const formRule = {
  props: {
    baseInfo: {
      type: Object,
      default: null
    },
    options: {
      type: Array,
      default: null
    }
  },
  data () {
    this.dateFormat = 'YYYY-MM-DD';
    return {
      disabledDevice: false,
      assignList: [], // 责任人
      jobContentOptions: []
    };
  },
  inject: ['type', 'order'],
  created () {},
  computed: {
    isDisabled () {
      return !(['1', 1, '2', '9', '10'].includes(this.type()));
    }
  },
  watch: {
    'baseForm.defectType' (val, old) {
      if (!this.isDisabled && val && this.baseForm.findTime) {
        this.getTimeOutDate(val);
      }
    },
    'baseForm.findTime' (val, old) {
      if (!this.isDisabled && val && this.baseForm.defectType) {
        this.getTimeOutDate(this.baseForm.defectType);
      }
    }
  },
  methods: {
    moment,
    getDetail (detail) {
      if (this.type() == 10) {
        this.baseForm.planTime = detail.planStartTime && detail.planEndTime ? [moment(detail.planStartTime, this.dateFormat), moment(detail.planEndTime, this.dateFormat)] : [];
        this.baseForm.planNum = detail.planNum;
      } else {
        this.initData(detail || this.baseForm);
      }
    },
    nameToArray (key) {
      if (this.baseForm.deviceTypeId == 0) {
        this.isDevice = false;
      }
      if (this.baseForm[key] && !Array.isArray(this.baseForm[key])) {
        this.baseForm[key] = this.baseForm[key] ? this.baseForm[key].split(',') : [];
        if (key == 'deviceTypeId') {
          this.baseForm.deviceTypeId = this.baseForm.deviceTypeId.map((item) => {
            return Number(item);
          });
        }
      }
    },
    // 作业开始日期
    disabledRiskJobDate (current) {
      // 不能选择今天过去3个月之前的时间 或 今天3个月之后的时间
      return (current && current > moment().add(3, 'month')) || current < moment().subtract(3, 'month');
    },
    // 发现时间不能超过系统当前时间
    disabledFindTime (current) {
      return current && current > moment().endOf('day');
    },
    range (start, end) {
      const result = [];
      for (let i = start; i < end; i++) {
        result.push(i);
      }
      return result;
    },
    disabledDateTime (current) {
      if (new Date(this.moment(current).format('YYYY-MM-DD HH:mm')).valueOf() > new Date(this.moment().format('YYYY-MM-DD HH:mm')).valueOf()) {
        this.baseForm.findTime = moment().format('YYYY-MM-DD HH:mm');
      }
      let currentTimeH = new Date(this.moment(current).format('YYYY-MM-DD')).valueOf();
      let nowTimeH = new Date(this.moment().format('YYYY-MM-DD')).valueOf();
      let diffD = currentTimeH >= nowTimeH || !currentTimeH;
      let currentH = new Date(this.moment(current)).getHours();
      let nowH = new Date().getHours();
      let diffH = nowH == currentH;
      let currentM = new Date(this.moment(current)).getMinutes();
      let nowM = new Date().getMinutes();
      let diffM = nowM == currentM;
      return {
        disabledHours: () => diffD ? this.range(0, 24).splice(new Date().getHours() + 1, 20) : this.range(0, 0),
        disabledMinutes: () => diffD && diffH ? this.range(new Date().getMinutes() + 1, 60) : this.range(0, 0),
        disabledSeconds: () => diffD && diffH && diffM ? this.range(new Date().getSeconds() + 1, 60) : this.range(0, 0)
      };
    },
    initData (obj) {
      let isBaseForm = obj == this.baseForm;
      for (let item in obj) {
        if (this.baseForm.hasOwnProperty(item)) {
          let isArray = this.baseForm[item] ? Array.isArray(this.baseForm[item]) : false;
          let isBlank = isArray ? [] : '';
          this.baseForm[item] = isBaseForm ? isBlank : obj[item];
        }
      }
      if (['7', '8'].includes(obj.taskType) && this.baseForm.jobCategory) {
        this.riskAllocation(this.baseInfo.jobCategory);
      }
      this.nameToArray('deviceTypeId');
      if (!['7', '8'].includes(this.baseForm.taskType)) {
        this.nameToArray('deviceNo');
      }
      if (!(this.type() == 1 || this.type() == 10)) {
        let arr = this.baseForm.deviceTypeId;
        let len = arr ? arr.length : '';
        this.baseForm.planTime = this.baseForm.planStartTime && this.baseForm.planEndTime ? [moment(this.baseForm.planStartTime, this.dateFormat), moment(this.baseForm.planEndTime, this
          .dateFormat)] : [];
        if (arr && arr.length > 0) {
          this.getDeviceList(arr[len - 1]);
          this.getDeviceCodeList(arr[len - 1], this.baseForm.deviceName);
        }
        if (['7', '8'].includes(obj.taskType)) {
          if (!obj.isEngLegacy && !this.isDisabled) {
            this.getfaultNameList(null);
          }
        }
        if (this.type() == '6') {
          if (!obj.predictPlanTimeLimit) {
            this.getPlanTimeLimit();
          }
          let user = Vue.ls.get(USER_INFO);

          this.baseForm.liablePerson = user.username;
          this.assignList.push({
            username: user.username,
            realName: user.realname
          });
          this.baseForm.orderSource = '4';
        }
        if (this.order().orderSource == '4' && this.order().step > 1) {
          this.assignList.push({
            username: obj.liablePerson,
            realName: obj.liablePersonName
          });
        }
      }
      if (!this.baseForm.deviceNo) {
        this.baseForm.deviceNo = [];
        this.baseForm.deviceNumber = 0;
      } else {
        this.baseForm.deviceNumber = this.baseForm.deviceNo.length;
      }
    },
    formatData (data) {
      for (var i = 0; i < data.length; i++) {
        if (data[i].children.length < 1) {
          data[i].children = undefined;
        } else {
          this.formatData(data[i].children);
        }
      }
      return data;
    },
    // 获取责任人
    getAssignList (psId, assign) {
      if (this.baseInfo.taskType == '10' && this.baseInfo.workSubclass == '2') {
        this.getTangoAssignList();
      } else {
        let params = {
          psId: psId,
          initUserAccount: assign
        };
        let isExist = false;
        getAssignListByPSId(params).then((res) => {
          res.result && res.result.map(item => {
            if (item.username == this.baseForm.liablePerson) {
              isExist = true;
            }
          });
          if (!isExist) {
            res.result.unshift({
              username: this.baseForm.liablePerson,
              realName: this.order().liablePersonName
            });
          }
          this.assignList = res.result;
        }).catch(() => {
          this.assignList = [];
        });
      }
    },
    // 获取检测工程师下拉列表
    getTangoAssignList () {
      getThirdAssgin({}).then(res => {
        this.assignList = res.result_data;
      });
    },
    deviceNoSelectAll () {
      if (this.baseForm.deviceNo && this.baseForm.deviceNo.length === this.deviceNoList.length) {
        this.baseForm.deviceNo = undefined;
      } else {
        this.baseForm.deviceNo = this.deviceNoList.map(item => item.deviceId);
      }
      this.chooseDevice();
      this.$forceUpdate();
    },
    setDeviceDisable (isTrue) {
      this.base_rules.deviceId[0].required = !isTrue;
      this.disabledDevice = isTrue;
    },
    // 设备类型change事件
    deviceTypeChange (val) {
      let self = this;
      let baseForm = self.baseForm;
      let baseInfo = self.baseInfo;
      if (this.order().orderSource == '4') {
        this.getDefectRand(val[val.length - 1]);
        this.getPlanTimeLimit();
        return;
      }
      baseForm.deviceId = undefined;
      baseForm.deviceNo = undefined;
      baseForm.deviceNumber = 0;
      if (val == 0 && baseInfo.isEngLegacy == 1 && baseInfo.taskType == '8') {
        baseForm.defectType = '4';
        return;
      }

      if (baseInfo.taskType == '8' && baseInfo.isEngLegacy == 0) {
        if (val == 0) {
          self.setDeviceDisable(true);
          baseForm.defectName = undefined;
          self.isDevice = false;
          baseForm.defectType = '4';
          return;
        } else {
          self.setDeviceDisable(false);
        }
      }
      self.isDevice = true;
      if (Array.isArray(val) && val.length && baseInfo.psaId) {
        baseForm.deviceTypeId = val;
        self.getDeviceList(val[val.length - 1]);
      } else {
        baseForm.deviceTypeId = undefined;
        self.deviceNameList = [];
      }
      if (['7', '8'].includes(baseInfo.taskType) && !this.isDisabled) {
        if (!(baseInfo.isEngLegacy && baseInfo.isEngLegacy == 1)) {
          self.faultNameList = [];
          baseForm.defectType = baseForm.defectName = undefined;
        }
        self.getfaultNameList(val);
      }
    },
    // 获取设备名称(
    getDeviceList (deviceTypeId) {
      let self = this;
      getDeviceNameList({
        deviceTypeId: deviceTypeId,
        psId: this.baseInfo.psaId
      }).then((res) => {
        self.$nextTick(() => {
          self.deviceNameList = res.result;
        });
      }).catch(() => {
        self.deviceNameList = [];
      });
    },
    deviceNameChange (val, $event) {
      this.baseForm.deviceId = val;
      this.baseForm.deviceNo = undefined;
      this.baseForm.deviceNumber = 0;
      this.deviceNoList = [];
      if (!val || !Array.isArray(this.baseForm.deviceTypeId) || !this.baseForm.deviceTypeId.length || !this.baseInfo.psaId) {
        return;
      }
      this.baseForm.deviceName = $event.data.attrs['data-name'];
      this.getDeviceCodeList();
    },
    // 获取设备编号
    getDeviceCodeList () {
      getDeviceIdList({
        deviceTypeId: this.baseForm.deviceTypeId[this.baseForm.deviceTypeId.length - 1],
        name: this.baseForm.deviceName,
        psId: this.baseInfo.psaId
      }).then((res) => {
        this.deviceNoList = res.result;
      }).catch(() => {
        this.deviceNoList = [];
      });
    },
    chooseDevice () {
      if (this.baseForm.deviceNo == undefined || this.baseForm.deviceNo.length == 0) {
        this.baseForm.deviceNumber = 0;
      } else {
        this.baseForm.deviceNumber = this.baseForm.deviceNo.length;
      }
    },
    // 获取故障名称
    getfaultNameList (val) {
      if (this.baseForm.deviceTypeId && Array.isArray(this.baseForm.deviceTypeId) && this.baseForm.deviceTypeId.length) {
        let map = {
          deviceTypeId: this.baseForm.deviceTypeId[this.baseForm.deviceTypeId.length - 1],
          equipmentStatus: this.baseForm.deviceType
        };
        this.getFaultList(val, map);
        // 设置缺陷类别
        if (val) {
          this.getDefectRand(map.deviceTypeId);
        }
      } else {
        this.faultNameList = [];
        this.baseForm.defectType = this.baseForm.defectName = undefined;
      }
    },
    // 获取故障名称
    getFaultList (val, obj) {
      getFaultName(obj).then((res) => {
        this.faultNameList = res.result;
        if (val) {
          this.baseForm.faultName = undefined;
        }
      }).catch(() => {
        this.faultNameList = [];
        this.baseForm.faultName = undefined;
      });
    },
    // 获取缺陷类别
    getDefectRand (deviceTypeId) {
      getDeviecClassByDeviceId({
        typeId: deviceTypeId,
        faultType: this.baseInfo.taskType == '8' ? '1' : '2'
      }).then((res) => {
        this.baseForm.defectType = res.result ? res.result.join(',') : '';
      });
    },
    // 获取超时时间
    getTimeOutDate (val) {
      getDate({
        taskType: this.baseInfo.taskType,
        scale: this.cleanCap ? this.cleanCap : this.baseForm.cleanCap,
        subTaskType: val,
        endDate: this.baseForm.findTime.split(' ')[0]
      }).then((res) => {
        if (res.result_code == 1) {
          this.baseForm.predictRecoverTime = res.result_data + ' ' + this.baseForm.findTime.split(' ')[1];
        }
      }).catch(() => {});
    },
    fieldChange (form, val) {
      this.$refs[form].clearValidate(val);
    },
    // 作业类别change事件
    jobCategoryChange (val) {
      this.baseForm.erpSqJobRiskMappingId = undefined;
      this.jobContentOptions = [];
      this.baseForm.riskLevel = undefined;
      if (val) {
        this.riskAllocation();
      }
    },
    // 加载作业内容下拉框内容 备份并且处理数据
    riskAllocation (val) {
      let self = this;
      let map = {
        jobCategory: val || self.baseForm.jobCategory
      };
      riskAllocation(map).then(res => {
        self.jobContentOptions = res.result_data;
      });
    },
    // 作业内容change事件
    erpSqJobRiskMappingIdChange (val, option) {
      if (val) {
        this.baseForm.riskLevel = option.data.attrs.code;
      } else {
        this.baseForm.riskLevel = undefined;
      }
    }
  }
};
