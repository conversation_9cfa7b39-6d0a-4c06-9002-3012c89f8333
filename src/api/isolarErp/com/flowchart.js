import { axios } from '@/utils/erp-request';

// 显示初始流程图
export function showInitialProcessDiagram (params) {
  return axios({
    url: '/workflow/v1/showInitialProcessDiagram',
    method: 'POST',
    data: params
  });
}
// 显示流程图
export function showProcessDiagram (params) {
  return axios({
    url: '/workflow/v1/showProcessDiagram',
    method: 'POST',
    data: params
  });
}
// 获取流程信息
export function listAuditInfo (params) {
  return axios({
    url: '/budget/v1/erpbudgetyear/listAuditInfo',
    method: 'POST',
    data: params
  });
}
