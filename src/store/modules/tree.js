// import Vue from 'vue'
import { USER_INFO } from '@/store/mutation-types';
import { getErpPjPsaTreeByUserId, getAllPjPsaTree } from '@/api/isolarErp/com/funnel';
import { projectAndStationTree } from '@/api/monitor/runMonitor';
const tree = {
  state: {
    projectTree: [],
    realStationTree: []
  },
  mutations: {
    SET_PROJECT_TREE: (state, treeData) => {
      state.projectTree = treeData;
    },
    SET_REAL_STATION_TREE: (state, treeData) => {
      state.realStationTree = treeData;
    }
  },
  actions: {
    // 获取项目树
    GetProjectTreeList ({ commit }, isAll, isAllPsa) {
      const user = Vue.ls.get(USER_INFO);
      const map = {
        userId: user.id,
        onlyAllProject: (isAll ? '1' : '0')
      };
      if (isAllPsa) {
        return new Promise((resolve, reject) => {
          getAllPjPsaTree(map).then((res) => {
            commit('SET_PROJECT_TREE', JSON.stringify(res.result_data));
            // commit('SET_PROJECT_TREE', res.result_data)
            resolve(res);
          }).catch(error => {
            reject(error);
          });
        });
      } else {
        return new Promise((resolve, reject) => {
          getErpPjPsaTreeByUserId(map).then((res) => {
            commit('SET_PROJECT_TREE', JSON.stringify(res.result_data));
            // commit('SET_PROJECT_TREE', res.result_data)
            resolve(res);
          }).catch(error => {
            reject(error);
          });
        });
      }
    },
    GetRealStationTreeList ({ commit }) { // 实体电站树
      return new Promise((resolve, reject) => {
        projectAndStationTree().then((res) => {
          // commit('SET_REAL_STATION_TREE', JSON.stringify(res.result_data))
          commit('SET_REAL_STATION_TREE', res.result_data);
          resolve(res);
        }).catch(error => {
          reject(error);
        });
      });
    }
  }
};

export default tree;
