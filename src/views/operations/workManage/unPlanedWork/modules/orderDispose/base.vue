<template>
  <!-- 提交表单 -->
  <a-form-model ref="orderDispose" :model="dispose" :rules="rules" :labelCol="{ style: 'width: 120px' }" :wrapperCol="{ style: 'width: calc(50% - 134px)' }">
    <a-row>
      <a-col :span="24">
        <a-form-model-item label="备注" prop="conditionRemark" style="padding-left: 48px">
          <a-textarea
            :max-length="1000"
            :disabled="disabled"
            v-model="dispose.conditionRemark"
            :title="dispose.conditionRemark"
            :placeholder="disabled ?'':'请输入'"
            style="width: 100%" />
        </a-form-model-item>
      </a-col>
      <a-col :span="24" v-if="dispose.stepVoList.length">
        <div class="title-box">
          <span class="before"></span>
          <span>作业步骤</span>
        </div>
        <a-steps v-model="current" direction="vertical">
          <a-step :key="step.title" v-for="(step, index) in dispose.stepVoList">
            <template slot="title">
              {{step.stepName}}
              <throttle-button v-if="step.uploadExampleFileList && step.uploadExampleFileList.length > 0" @click="handlePreview(step.uploadExampleFileList)"
                label="示例图片" class="example-btn"></throttle-button>
            </template>
            <template slot="description">
              <a-form-model-item
                label="情况描述"
                :prop="'stepVoList.' + index + '.workDesc'"
                :labelCol="{ style: 'width: 134px' }"
                :wrapperCol="{ style: 'width: calc(50% - 140px)' }"
                :rules="[{
                  required: isRequired,
                  message: '情况描述不能为空',
                  trigger: 'blur',
                },{
                  max:200,
                  message:'最大输入200字符'
                }]"
                v-if="step.stepContent && step.stepContent.indexOf('1') > -1">
                <a-textarea
                  :max-length="200"
                  :disabled="disabled"
                  v-model="step.workDesc"
                  :title="step.workDesc"
                  placeholder="请输入"
                  style="width: 100%" />
              </a-form-model-item>
              <a-form-model-item
                label="上传图片"
                :labelCol="{ style: 'width: 134px' }"
                :prop="'stepVoList.' + index + '.uploadPictureList'"
                :rules="[{
                  required: isRequired,
                  message: '请上传图片',
                  trigger: 'blur',
                },{
                  validator:validateFileList,
                  trigger:'change'
                }]"
                v-if="step.stepContent && step.stepContent.indexOf('2') > -1">
                <uploadFileView v-model="step.uploadPictureList" @set="fieldChange('stepVoList.' + index + '.uploadPictureList')" listType="picture-card"
                  :disabled="disabled" :zoom="0.8" :multiple="true" accept=".jpg,.png,.jpeg,.bmp" tip="最多上传5张图片，单张图片不超10MB，支持格式jpg/jpeg/png/bmp！" />
              </a-form-model-item>
              <a-form-model-item
                label="上传附件"
                :labelCol="{ style: 'width: 134px' }"
                :prop="'stepVoList.' + index + '.uploadFileList'"
                :rules="{
                  required: isRequired,
                  message: '请上传附件',
                  trigger: 'blur',
                }"
                v-if="step.stepContent && step.stepContent.indexOf('3') > -1">
                <uploadFileView v-model="step.uploadFileList" @set="fieldChange('stepVoList.' + index + '.uploadFileList')" tip="最多上传5个文件,且上传的附件最大不超过10MB!"
                  :disabled="disabled" :multiple="true" />
              </a-form-model-item>
            </template>
          </a-step>
        </a-steps>
      </a-col>
    </a-row>
  </a-form-model>
</template>

<script>
import initDict from '@/mixins/initDict';
import { orderDispose } from './mixins';
export default {
  name: 'base',
  mixins: [initDict, orderDispose],
  props: {
    isDiagnosis: {
      default: false,
      type: [Boolean, String]
    },
    isFarm: {
      default: '',
      type: [String, Number]
    }
  },
  inject: ['order'],
  data () {
    return {
      // 普通表单
      dispose: {
        conditionRemark: '',
        stepVoList: []
      },
      disabled: false,
      current: '',
      rules: {}
    };
  },
  created () {
    let order = this.order();
    if (!this.getStatus() && order.stepList.length == 0) {
      this.getStepList();
    }
    this.dispose = {
      conditionRemark: order.conditionRemark,
      stepVoList: order.stepList
    };
    this.disabled = order.disabled;
  },
  methods: {
    // 更新数据
    updataDispose (order) {
      this.disabled = order.disabled;
    }
  }
};
</script>

<style lang="less" scoped>
  :deep(.ant-form-item) {
    width: 100%;
    display: inline-flex;
  }

  :deep(.ant-steps-vertical .ant-steps-item-description) {
    padding: 24px 0 0;
  }

  .example-btn {
    font-size: 12px !important;
    padding: 0 8px !important;
    height: 22px !important;
    margin-left: 16px;
  }

  .title-box {
    margin-top: -8px;
  }
</style>
