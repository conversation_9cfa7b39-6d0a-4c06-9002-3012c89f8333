// 到期续费类 气象服务+保险购买
// 气象服务枚举类型
export const weatherServiceTypeEnum = {
  '47': '气象服务',
  '48': '调度维保',
  '49': '等保测评'
};
// 保险购买枚举类型
export const insuranceBuyTypeEnum = {
  '44': '财产一切险',
  '45': '公众责任险',
  '46': '机器损坏险'
};
export const renewalTaskTypeEnum = {
  ...weatherServiceTypeEnum,
  ...insuranceBuyTypeEnum
};
// 清洗除草类
// 清洗枚举类型
export const cleanTypeEnum = {
  // '43': '清洗'
  '1': '清洗'
};
// 除草枚举类型
export const weedTypeEnum = {
  '41': '防遮挡除草',
  '42': '防火除草'
};
export const cleanWeedTaskTypeEnum = {
  ...cleanTypeEnum,
  ...weedTypeEnum
};

// 试验检测类
export const testDetectTaskTypeEnum = {
  '36': '预防试验',
  '37': '组件EL/IV检测',
  '38': '防雷检测',
  '39': '仪表校验',
  '40': '无人机红外检测'
};

export default {
  data () {
    return {
      renewalTaskType: Object.keys(renewalTaskTypeEnum),
      cleanWeedTaskType: Object.keys(cleanWeedTaskTypeEnum),
      testDetectTaskType: Object.keys(testDetectTaskTypeEnum),
      weatherServiceType: Object.keys(weatherServiceTypeEnum),
      insuranceBuyType: Object.keys(insuranceBuyTypeEnum),
      cleanType: Object.keys(cleanTypeEnum),
      weedType: Object.keys(weedTypeEnum)
    };
  }
};
