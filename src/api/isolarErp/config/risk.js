import { axios } from '@/utils/erp-request';

// 新增
export function insertRisk (data) {
  return axios({
    url: '/system/v1/erpRisk/insert',
    method: 'post',
    data
  });
}
// 查询
export function selectRisk (data) {
  return axios({
    url: '/system/v1/erpRisk/query',
    method: 'post',
    data
  });
}
// 编辑
export function updateRisk (data) {
  return axios({
    url: '/system/v1/erpRisk/update',
    method: 'post',
    data
  });
}
// 删除
export function deleteRisk (data) {
  return axios({
    url: '/system/v1/erpRisk/delete',
    method: 'post',
    data
  });
}

/*  应急预案 */
// 新增
export function insertEmergency (data) {
  return axios({
    url: '/system/v2/emergencyPlanSet/save',
    method: 'post',
    data
  });
}
// 查询
export function selectEmergency (data) {
  return axios({
    url: '/system/v2/emergencyPlanSet/page',
    method: 'post',
    data
  });
}
// 编辑
export function updateEmergency (data) {
  return axios({
    url: '/system/v2/emergencyPlanSet/update',
    method: 'post',
    data
  });
}
// 删除
export function deleteEmergency (data) {
  return axios({
    url: '/system/v2/emergencyPlanSet/delete',
    method: 'post',
    data
  });
}
// 获取类别下的所有正常预案
export function getPlanByCategory (data) {
  return axios({
    url: '/system/v2/emergencyPlanSet/getPlanByCategory',
    method: 'post',
    data
  });
}
