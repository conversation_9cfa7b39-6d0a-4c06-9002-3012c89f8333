<!--  @creator l<PERSON><PERSON>
      @date 2021/02/23
      @description   系统管理模块:参数配置；-->
<template>
  <div class="parameterConfig-model isolar-layout">

    <a-row :gutter="16" class="parameter-config" ref="parameter-config">
      <a-col :md="4" :sm="24">
        <Funnel @refreshData="refreshData"></Funnel>
      </a-col>
      <a-col :md="20" :sm="24">
        <div class="solar-eye-search-model">
          <a-form labelAlign="left" class="solar-eye-search-content">

            <a-row :gutter="24" type="flex" align="middle">
              <a-col :md="6" :sm="12">
                <a-form-item label="参数名称">
                  <a-input v-model="paramCN" placeholder allowClear @keypress.native.enter="searchClick()" />
                </a-form-item>
              </a-col>
              <a-col :md="6" :sm="12">
                <a-form-item>
                  <a-button @click="searchClick" class="solar-eye-btn-primary">查询</a-button>
                </a-form-item>
              </a-col>
            </a-row>
          </a-form>
        </div>
        <div class="solar-eye-gap"></div>
        <a-col class="solar-eye-main-content" :style="{height:tableHeight + 224 +'px'}">
          <div class="operation" style="height: 32px">
            <div class="operation-btn">
              <a-button @click.stop.prevent="update()" class="solar-eye-btn-primary">修改</a-button>
              <a-button type="default" :disabled="isDisabled" @click.stop.prevent="save()">保存</a-button>
            </div>
          </div>
          <a-form-model :model="form" :rules="rules" ref="form">
            <div class="table-list">
              <a-table :data-source="form.tableData" class="config-data-table" :pagination="false"
                :row-key="(r, i) => i.toString()" :row-selection="{
                  selectedRowKeys: selectedRowKeys,
                  onChange: (keys, rows) => {
                    selectedRowKeys = keys;
                    handleSelectionChange(rows);
                  },
                }" :scroll="{ x: tableWidth, y: (tableHeight - 24) + 'px' }" :loading="tableloading" size="middle"
                ref="multipleTable">
                <a-table-column title="序号" :width="80" key="index" align="center"
                  :customRender="(text, record, index) => index + 1" />
                <a-table-column key="paramCN" data-index="paramCN" title="参数名称" align="center" ellipsis />
                <a-table-column key="paramValue" data-index="paramValue" title="参数值" align="center" ellipsis>
                  <template slot-scope="text, record, index">
                    <div v-if="record.paramEN === 'opening_time' ? true : false">
                      <a-form-model-item class="table-form-item" v-if="record.status"
                        :prop="'tableData.' + index + '.paramValue'" :rules="
                          record.status
                            ? [
                                {
                                  required: true,
                                  trigger: 'change',
                                  message: `${record.paramCN}不能为空`,
                                },
                              ]
                            : []
                        ">
                        <a-time-picker v-model="record.paramValue" value-format="HH:mm:ss"
                          :disabledHours="disabledHours" :disabledMinutes="disabledMinutes" size="small">
                        </a-time-picker>
                      </a-form-model-item>
                      <span v-else>{{ record.paramValue }}</span>
                    </div>
                    <div v-else-if="
                        record.paramEN === 'realtime_algorithm_runtime'
                      ">
                      <a-form-model-item class="table-form-item" v-if="record.status">
                        <a-time-picker v-model="rangeTime1" placeholder="开始时间" size="small" value-format="HH:mm:ss"
                          @change="changeRangeTime" @openChange="handleStartOpenChange" :rules="[]"></a-time-picker>
                        至
                        <a-time-picker v-model="rangeTime2" placeholder="结束时间" size="small" value-format="HH:mm:ss"
                          @change="changeRangeTime" @openChange="handleEndOpenChange" :open="endOpen"></a-time-picker>
                      </a-form-model-item>
                      <span v-else>{{ record.paramValue }}</span>
                    </div>
                    <div v-else-if="record.paramEN == 'high_temperature_duration'">
                      <a-form-model-item class="table-form-item" v-if="record.status">
                        <a-select v-model="record.paramValue" placeholder="请选择" size="small">
                          <a-select-option v-for="item in options" :key="item.value" :label="item.label"
                            :value="item.label">
                            {{ item.label }}
                          </a-select-option>
                        </a-select>
                      </a-form-model-item>
                      <span v-else>{{ record.paramValue }}</span>
                    </div>
                    <div v-else-if="
                        record.paramEN == 'string_interrupt_duration'
                          ? true
                          : false
                      ">
                      <a-form-model-item class="table-form-item" v-if="record.status">
                        <a-select v-model="record.paramValue" placeholder="请选择" size="small">
                          <a-select-option v-for="item in optionsInterrupt" :key="item.value" :label="item.label"
                            :value="item.label">{{ item.label }}
                          </a-select-option>
                        </a-select>
                      </a-form-model-item>
                      <span v-else>{{ record.paramValue }}</span>
                    </div>
                    <div v-else-if="
                        record.paramEN == 'power_limit_duration' ? true : false
                      ">
                      <a-form-model-item class="table-form-item" v-if="record.status">
                        <a-select v-model="record.paramValue" placeholder="请选择" size="small">
                          <a-select-option v-for="item in optionsInterrupt" :key="item.value" :label="item.label"
                            :value="item.label">{{ item.label }}
                          </a-select-option>
                        </a-select>
                      </a-form-model-item>
                      <span v-else>{{ record.paramValue }}</span>
                    </div>
                    <div v-else>
                      <a-form-model-item class="table-form-item" v-if="record.status"
                        :prop="'tableData.' + index + '.paramValue'" :rules="
                          record.status
                            ? [
                                {
                                  required: true,
                                  trigger: 'blur',
                                  message: `${record.paramCN}不能为空`,
                                },
                                {
                                  validator: tip.valitate(record.paramCN),
                                  trigger: 'blur',
                                },
                              ]
                            : []
                        ">
                        <a-input size="small" v-model="record.paramValue" />
                      </a-form-model-item>
                      <span v-else>{{ record.paramValue }}</span>
                    </div>
                  </template>
                </a-table-column>
                <a-table-column :customRender="
                    (text, record, index) =>
                      record.paramUnit == '1' || record.paramUnit == '/'
                        ? ''
                        : record.paramUnit
                  " key="paramUnit" data-index="paramUnit" title="参数单位" :width="100" align="center"></a-table-column>
                <a-table-column key="paramDescribe" data-index="paramDescribe" title="参数描述" align="center" ellipsis />
              </a-table>
            </div>
          </a-form-model>
        </a-col>
      </a-col>
    </a-row>
  </div>
</template>
<script>
import Funnel from '@/components/health/funnel';
import moment from 'moment';
import {
  listAlgorithmParamByPsId,
  updateAlgorithmParamByPsId
} from '@/api/health/healthapi.js';
import tip from '@/api/health/configTip.js';
import treeHeight from '@/mixins/health/leftTreeHeight';
import tableHeight from '@/mixins/tableHeightAndSearchModel';
export default {
  components: {
    Funnel
  },
  name: 'ParameterConfig',
  mixins: [treeHeight, tableHeight],
  data () {
    return {
      rangeTime1: '',
      rangeTime2: '',
      endOpen: false,
      currentNodekey: '', // 默认选中的节点树
      rules: {},
      tip: tip,
      paramName: '',
      paramCN: '', // 参数名称
      psId: '', // 电站id
      treeLoad: true,
      paramValue: new Date(),
      currentPage1: 1,
      rowCount1: 0,
      isDisabled: true,

      accordion: '',
      // psNamesearch: {
      //   psId: "",
      //   curPage: "1",
      //   psName: "",
      //   size: "30",
      // },
      selectedRowKeys: [],
      multipleSelection: [],
      data: [],
      defaultProps: {
        label: 'psName'
      },
      form: {
        tableData: []
      },
      tempList: [], // 存放上一次接口返回的数据
      tableloading: true,
      pageList: {},
      filterStatus: true,
      filterDisabled: true,
      options: [{
        value: '1',
        label: '5'
      },
      {
        value: '2',
        label: '10'
      },
      {
        value: '3',
        label: '15'
      },
      {
        value: '4',
        label: '20'
      },
      {
        value: '5',
        label: '25'
      },
      {
        value: '6',
        label: '30'
      },
      {
        value: '7',
        label: '35'
      },
      {
        value: '8',
        label: '40'
      },
      {
        value: '9',
        label: '45'
      },
      {
        value: '10',
        label: '50'
      },
      {
        value: '11',
        label: '55'
      },
      {
        value: '12',
        label: '60'
      }
      ],
      optionsInterrupt: [{
        value: '1',
        label: '30'
      },
      {
        value: '2',
        label: '35'
      },
      {
        value: '3',
        label: '40'
      },
      {
        value: '4',
        label: '45'
      },
      {
        value: '5',
        label: '50'
      },
      {
        value: '6',
        label: '55'
      },
      {
        value: '7',
        label: '60'
      },
      {
        value: '8',
        label: '65'
      },
      {
        value: '9',
        label: '70'
      },
      {
        value: '10',
        label: '75'
      },
      {
        value: '11',
        label: '80'
      },
      {
        value: '12',
        label: '85'
      },
      {
        value: '13',
        label: '75'
      },
      {
        value: '14',
        label: '80'
      },
      {
        value: '15',
        label: '85'
      },
      {
        value: '16',
        label: '90'
      },
      {
        value: '17',
        label: '95'
      },
      {
        value: '18',
        label: '100'
      },
      {
        value: '19',
        label: '105'
      },
      {
        value: '20',
        label: '110'
      },
      {
        value: '21',
        label: '115'
      },
      {
        value: '22',
        label: '120'
      }
      ],
      value: '1',
      height: innerHeight > 700 ? (innerHeight - 64 - 16) : (800 - 64 - 16)
    };
  },
  /**
     * @creator wangliang
     * @date 2020/08/28
     * @description  初始化页面调用查询接口获取数据
     */
  created () {
    // this.getData(this.psId, this.paramCN);
    // this.getLeftData(this.psNamesearch);
  },
  methods: {
    validateRangeTime (submit = false) {
      const getDate = (time) => {
        if (!time) {
          return null;
        }
        return moment(moment().format('YYYY/MM/DD ') + time);
      };
      let startDate = getDate(this.rangeTime1);
      let endDate = getDate(this.rangeTime2);
      if (submit && (!startDate || !endDate)) {
        this.$message.error(
          '实时算法运行时间段的开始时间和结束时间必须同时输入'
        );
        return false;
      }
      if (startDate && endDate && startDate.isAfter(endDate)) {
        this.$message.error('实时算法运行时间段的开始时间不能大于结束时间');
        return false;
      }
      return true;
    },
    handleStartOpenChange (open) {
      if (!open) {
        this.endOpen = true;
      }
    },
    handleEndOpenChange (open) {
      this.endOpen = open;
    },
    disabledHours () {
      let arr = [];
      for (let h = 0; h < 18; h++) {
        // arr.push(('0' + h).slice(-2))
        arr.push(h);
      }
      return arr;
    },
    disabledMinutes (hour) {
      if (hour == 18) {
        let arr = [];
        for (let min = 0; min < 30; min++) {
          arr.push(min);
        }
        return arr;
      }
    },
    changeRangeTime () {
      this.validateRangeTime();
    },
    refreshData (obj) {
      this.psId = obj.psId;
      this.selectedRowKeys = [];
      this.getData(obj.psId, this.paramCN);
    },
    /**
       * @creator wangliang
       * @date 2020/09/24
       * @description  formatUnit參數單位的頁面顯示轉換，“/”与數字1頁面不显示
       */
    formatUnit (row, column) {
      if (row.paramUnit == '1' || row.paramUnit == '/') return '';
      return row.paramUnit;
    },
    /**
       * @creator wangliang
       * @date 2020/08/28
       * @description   改变编辑框状态 status = 0---文本，status = 1---编辑
       */
    changeStatus (row) {
      row.status = 0;
      if (row.paramEN === 'opening_time') {
        this.paramValue = row.paramValue;
      }
      this.$refs.multipleTable.$forceUpdate();
    },
    // 参数名称查询
    searchClick () {
      this.getData(this.psId, this.paramCN);
    },
    /**
       * @creator wangliang
       * @date 2020/08/28
       * @description   获取数据并加载到页面
       */
    getData (psId, paramCN) {
      // 请求查询接口
      this.tableloading = true;

      let params = {
        psId: psId, // 电站id
        paramCN: paramCN // 参数名称
      };
      listAlgorithmParamByPsId(params).then((res) => {
        // 返回的结果数据处理，循环数组，在每个对面中添加一个status属性，默认status=0，status=1参数值显示编辑框
        this.tableloading = false;
        this.form.tableData = res.result_data;
        if (res.result_data && res.result_data.length > 0) {
          let time = '';
          this.form.tableData.map((item, index) => {
            this.$set(this.form.tableData[index], 'status', 0);
            if (item.paramEN === 'realtime_algorithm_runtime') {
              time = item.paramValue;
            }
          });
          var timeList = time.split('-');
          this.rangeTime1 = timeList[0];
          this.rangeTime2 = timeList[1];
          this.tempData = JSON.stringify(res.result_data);
          this.tempList = this.form.tableData;
        }
        this.setBodyMinHeight(this.form.tableHeight.length);
      }).catch((err) => {
        this.tableloading = false;
        console.log(err);
      });
    },
    // 修改
    update () {
      let msg = '请选择要修改的参数！';
      if (this.multipleSelection.length === 0) {
        this.$message.warning(msg);
        return;
      }

      this.multipleSelection.map((item) => {
        if (!item.status) {
          item.status = 1;
        }
        return item;
      });
    },
    /**
       * @creator wangliang
       * @date 2020/08/28
       * @description   保存修改后的信息
       */
    save () {
      let obj = {};
      let psId = '';
      this.multipleSelection.forEach((item) => {
        psId = item.psId;
        if (item.paramEN == 'realtime_algorithm_runtime') {
          item.paramValue = this.rangeTime1 + '-' + this.rangeTime2;
        }
        let key = item.paramEN;
        let value = item.paramValue;

        obj[key] = value;
        obj['psId'] = item.psId;
      });

      this.$refs.form.validate((valid) => {
        let validRes = this.validateRangeTime(true);
        if (valid && validRes) {
          updateAlgorithmParamByPsId({
            algorithmMaps: obj,
            psId: psId
          })
            .then((res) => {
              if (res.result_code === '1') {
                let msgT = '修改成功';
                this.$message.success(msgT);
                this.multipleSelection = [];
                this.selectedRowKeys = [];
                this.getData(this.psId, this.paramCN);
              } else {
                this.$message.error(res.result_msg);
              }
            })
            .catch(function (err) {
              this.$message.error(err);
            });
          // 点击保存后表格参数值编辑框隐藏
          this.multipleSelection.map((item) => {
            if (item.status) {
              item.status = 0;
            }
            return item;
          });
        } else {

        }
      });
    },

    // 表格中需要修改的数据的勾选
    handleSelectionChange (val) {
      this.multipleSelection = val;
      var enNameList = [];
      val.forEach((item, index) => {
        enNameList.push(item.paramEN);
      });
      if (this.multipleSelection.length === 0) {
        this.isDisabled = true;
      } else {
        this.isDisabled = false;
      }
      this.form.tableData.map((item, index) => {
        if (enNameList.indexOf(item.paramEN) === -1) {
          item.paramValue = JSON.parse(this.tempData)[index].paramValue;
          item.status = 0;
        } else {
          item.status = 1;
        }
      });
    }
  }
};
</script>
<style lang="less" scoped>
 .config-data-table {
    width: 100%;
    .table-form-item {
      margin-bottom: 0 !important;
      display: flex;
      justify-content: center;
    }
  }
</style>
