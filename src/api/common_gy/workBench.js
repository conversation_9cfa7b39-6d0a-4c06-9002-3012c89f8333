import {
  axios
} from '@/utils/gy-request';

export function auditRecord (parameter) {
  return axios({
    url: '/planManage/auditRecord',
    method: 'post',
    data: parameter
  });
}
export function getAssignListByDepId (parameter) {
  return axios({
    url: '/common/management/getAssignListByDepId',
    method: 'post',
    data: parameter
  });
}
export function dailyTaskDetail (parameter) {
  return axios({
    url: '/theWorkbench/dailyTaskDetail',
    method: 'post',
    data: parameter
  });
}
export function handleDailyTask (parameter) {
  return axios({
    url: '/theWorkbench/handleDailyTask',
    method: 'post',
    data: parameter
  });
}
export function dailyTaskList (parameter) {
  return axios({
    url: '/theWorkbench/dailyTaskList',
    method: 'post',
    data: parameter
  });
}
export function statistics (parameter) {
  return axios({
    url: '/theWorkbench/statistics',
    method: 'post',
    data: parameter
  });
}

export function todoDataByOrder (parameter) {
  return axios({
    url: '/theWorkbench/todoDataByOrder',
    method: 'post',
    data: parameter
  });
}

export function todoDataByDateOf (paramt) {
  return axios({
    url: '/theWorkbench/todoDataByDateOf',
    method: 'post',
    data: paramt
  });
}
