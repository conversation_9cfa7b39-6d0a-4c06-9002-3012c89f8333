import moment from 'moment';
import { USER_AUTH } from '@/store/mutation-types';
// 输入框trim
export function $trim (event) {
  return event.target.value.trim();
};
// 获取当前时间-字符串 type  day:yyyy-MM-dd month:yyyy-MM yaer:yyyy 无:yyyy-MM-dd
export function getNowTime (type) {
  type = type || 'day';
  if (type == 'day') {
    return moment().format('YYYY-MM-DD');
  } else if (type == 'month') {
    return moment().format('YYYY-MM');
  } else if (type == 'year') {
    return moment().format('YYYY');
  }
  return '';
};
// 表格数据格式化format
export function tabFormatter ({ cellValue }) {
  cellValue = ((cellValue == null || cellValue == undefined) ? '' : cellValue.toString());
  if (cellValue) {
    return cellValue;
  } else {
    return '--';
  }
}
// 获取数据字典名称label
export function getLabel (code, items) {
  code = ((code == null || code == undefined) ? '' : code.toString());
  if (!code) {
    return '--';
  }
  if (items instanceof Array) {
    let label = '';
    for (let item of items) {
      if (code == item.codeValue || code == item.value) {
        label = (item.dispName || item.label);
        break;
      }
    }
    return ((label || label == '0') ? label : '--');
  } else {
    return (code || '--');
  }
}
// base64下载文件
export function $downloadFile (result_data, message) {
  if (!result_data.fileBase64Code) {
    return;
  }
  const base64ToBlob = function (data) {
    let arr = data.split(',');
    let bstr = (arr.length == 2 ? window.atob(arr[1]) : window.atob(arr[0]));
    let l = bstr.length;
    let u8Arr = new Uint8Array(l);
    while (l--) {
      u8Arr[l] = bstr.charCodeAt(l);
    }
    return new Blob([u8Arr]);
  };
    // res.data   就是后台返回的base64的 文件流
  let bloburl = base64ToBlob(result_data.fileBase64Code, result_data.fileType);
  // 兼容IE
  if (window.navigator.msSaveOrOpenBlob) {
    window.navigator.msSaveOrOpenBlob(bloburl, result_data.fileName);
  } else {
    let downloadElement = document.createElement('a');
    downloadElement.href = URL.createObjectURL(bloburl); // 防止bloburl太大超过a标签的href最大值
    downloadElement.download = result_data.fileName; // 下载后文件名
    document.body.appendChild(downloadElement);
    downloadElement.click(); // 点击下载
    document.body.removeChild(downloadElement); // 下载完成移除元素
    window.URL.revokeObjectURL(downloadElement.href); // 释放掉blob对象
  }
  setTimeout(() => {
    this.$message.destroy();
    this.$message.success(!message ? '下载成功' : message);
  }, 200);
};

// 按钮权限控制
export function showHandle (perms) {
  perms = (perms == null ? '' : perms.toString());
  if (!perms) {
    return false;
  }
  const all = JSON.parse(sessionStorage.getItem(USER_AUTH) || '[]');
  const arr = perms.split(',');
  let show = false;
  for (let item of all) {
    if (arr.includes(item.action)) {
      show = true;
      break;
    }
  }
  return show;
};

// fileBase64Code  打开新窗口预览pdf  resultData  res.result_data
export function $windowOpenPdf (resultData) {
  let arr = resultData.fileBase64Code.split(',');
  let bstr = (arr.length == 2 ? window.atob(arr[1]) : window.atob(arr[0]));
  let l = bstr.length;
  let u8Arr = new Uint8Array(l);
  while (l--) {
    u8Arr[l] = bstr.charCodeAt(l);
  }
  const blob = new Blob([u8Arr], { type: 'application/pdf' });
  let url = null;
  if (window.createObjectURL != undefined) { // basic
    url = window.createObjectURL(blob) + '#toolbar=1';
  } else if (window.webkitURL != undefined) { // webkit or chrome
    url = window.webkitURL.createObjectURL(blob) + '#toolbar=1';
  } else if (window.URL != undefined) { // mozilla(firefox)
    url = window.URL.createObjectURL(blob) + '#toolbar=1';
  }
  if (url) {
    window.open(url);
  } else {
    this.$notification.error({
      message: '系统提示',
      description: '文件加载失败...',
      duration: 3
    });
  }
}
// form表单提交自动滚至错误处
export function errorScroll (callback) {
  this.$nextTick(() => {
    this.loading = false;
    let antErrors = document.getElementsByClassName('has-error');
    let vxeErrors = document.getElementsByClassName('vxe-cell--valid-msg');
    let allErrors = [...antErrors, ...vxeErrors];
    allErrors[0].scrollIntoView({
      // 滚动到指定节点
      block: 'center',
      behavior: 'smooth'
    });
    if (callback) {
      if (typeof callback !== 'function') {
        throw new TypeError(callback + 'is not a function');
      } else {
        callback(allErrors);
      }
    }
  });
};

// select下拉框模糊搜索公共方法
export function filterOption (input, option) {
  return (
    option.componentOptions.children[0].text.toLowerCase().indexOf(input.toLowerCase()) >= 0
  );
}

// 判断是否为空，包括undefined、null、''、[]、{}
export function isEmpty (params) {
  if (params === undefined) {
    return true;
  } else if (params === null) {
    return true;
  } else if (Object.prototype.toString.call(params) === '[object String]' && params === '') {
    return true;
  } else if (Object.prototype.toString.call(params) === '[object Array]' && params.length == 0) {
    return true;
  } else if (Object.prototype.toString.call(params) === '[object Object]' && Object.keys(params).length == 0) {
    return true;
  }
  return false;
}
// 获取计算后的样式

export function getRootColor (theme) {
  const style = window.getComputedStyle(document.documentElement);
  return style.getPropertyValue(`${theme}`).trim();
}

/**
 * 操作类型常量枚举
 * @readonly
 * @enum {string}
 * @property {string} ADD 新增操作
 * @property {string} EDIT 编辑操作
 * @property {string} DETAILS 详情查看操作
 * @property {string} COPY 复制操作
 */
export const ACTION_TYPES = Object.freeze({
  ADD: '1',
  EDIT: '2',
  DETAILS: '3',
  COPY: '4'
});
/**
 * 操作类型名称映射
 * @readonly
 * @enum {string}
 * @property {string} 1 新增
 * @property {string} 2 编辑
 * @property {string} 3 详情
 * @property {string} 4 复制
 */
export const ACTION_NAMES = Object.freeze({
  [ACTION_TYPES.ADD]: '新增',
  [ACTION_TYPES.EDIT]: '编辑',
  [ACTION_TYPES.DETAILS]: '详情',
  [ACTION_TYPES.COPY]: '复制'
});
