<template>
  <div>
    <div class="view_detail_box" v-if="!disabled">
      <div class="title_box_top">
        <span class="before"></span>工作方案
      </div>
      <!-- 提交表单 -->
      <a-form-model ref="eliminateForm" :model="eliminate" :rules="rules" :labelCol="{ style: 'width: 120px' }" :wrapperCol="{ style: 'width: calc(100% - 120px)' }">
        <a-row :gutter="24">
          <a-col :span="24">
            <a-form-model-item label="工作方案" prop="uploadFileList">
              <uploadFileView
                v-model="eliminate.uploadFileList"
                :disabled="disabled"
                :maxNum="5"
                :maxSize="10"
                :multiple="true"
                tip="最多上传5个文件,且上传的附件最大不超过10MB!"
                @set="fieldChange('uploadFileList')" />
            </a-form-model-item>
          </a-col>
        </a-row>
      </a-form-model>
    </div>

    <detail-layout v-else :labelList="baseInfo" :form="eliminate" title="工作方案"></detail-layout>
  </div>
</template>

<script>
import uploadFileView from '@/components/com/fileUploadView';
export default {
  props: {
    disabled: {
      type: Boolean,
      default: false
    }
  },
  components: {
    uploadFileView
  },
  inject: ['order', 'orderSource'],
  data () {
    return {
      // 普通表单
      eliminate: {
        uploadFileList: [] // 附件
      },
      baseInfo: [{
        type: 'file:text',
        label: '工作方案',
        key: 'uploadFileList'
      }],
      rules: {
        uploadFileList: [{
          required: true,
          message: '请上传工作方案',
          trigger: 'blur'
        }]
      }
    };
  },
  created () {
    let order = this.order();
    this.eliminate = {
      uploadFileList: order.uploadWorkPlanFileList // 附件
    };
  },
  methods: {
    // 更新数据
    updataEliminate () {},
    // 保存验证
    saveEliminate () {
      return new Promise((resolve, reject) => {
        this.$refs.eliminateForm.validate((valid) => {
          if (valid) {
            resolve(this.eliminate);
          } else {
            reject(new Error());
          }
        });
      });
    },
    fieldChange (val) {
      this.$refs.eliminateForm.clearValidate(val);
    }
  }
};
</script>

<style lang="less" scoped>
  .view_detail_box {
    .title_box_top {
      font-size: 16px;
      font-weight: 500;
      color: #5b5b5b;
      margin-bottom: 16px;

      .before {
        width: 4px;
        height: 15px;
        border-left: 4px solid #FF8F33;
        margin-right: 16px;
        border-radius: 0px 2px 2px 0px;
      }
    }
  }

  :deep(.ant-form-item) {
    width: 100%;
    display: inline-flex;
  }

  :root[data-theme='dark'] {
    .title_box_top {
      color: #fff;

      .before {
        border-left-color: #60CAFE;
      }
    }
  }
</style>
