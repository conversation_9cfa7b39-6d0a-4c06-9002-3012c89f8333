<template>
  <!-- 缺陷工单表单 -->
  <div class="drawer-form-com" :class="{'unique-padding': model.type!=1 && model.type!=2}">
    <div class="drawer-form-content">
      <a-spin :spinning="loading">
        <!-- 基本信息 -->
        <template v-if="model.type!=1 && model.type!=2 && !isEdit && model.type!='10'">
          <baseDetail ref="editRegister" :type="model.type" v-show="model.type != 1 && model.type!=2  && (order.psaId || isTangoTask)" />
        </template>

        <template v-else>
          <!-- 提交表单-缺陷登记 -->
          <register-form ref="editRegister" :type="model.type" :disabled="model.type != '1'"
            v-if="model.type == '1' || (model.type == '10' && order.psaId) || (model.type != 1 && order.psaId) || order.psaId === 0" />
        </template>

        <!-- 指派 -->
        <template v-if="model.type == '5' || (order.processDefKey == 'activiti3273Workflow' && model.type == '2')">
          <a-form-model v-if="order.orderSource != '6'" ref="sendForm" :model="sendform" :rules="rules"
            :labelCol="{ style: 'width: 172px' }" :wrapperCol="{ style: 'width: calc(100% - 172px)' }">
            <a-row :gutter="24">
              <a-col :xl="8" :sm="12" :xs="24">
                <a-form-model-item :label="isTangoTask ? '执行人' :'责任人'" prop="liablePerson">
                  <a-select v-model="sendform.liablePerson" placeholder="请选择" style="width: 100%"
                    :disabled="model.type == '3' || (isTangoTask && order.flowSts !='1')">
                    <a-select-option v-for="(item, k) in users" :hidden="item.hidden" :key="k" :value="item.username">
                      {{ item.realName }}
                    </a-select-option>
                  </a-select>
                </a-form-model-item>
              </a-col>
            </a-row>
          </a-form-model>
        </template>

        <!-- 无人机巡检-指定负责人、协作人 -->
        <a-form-model v-if="order.orderSource == '6'" ref="sendForm" :model="sendform" :rules="rules"
          :labelCol="{ style:'width: 115px' }" :wrapperCol="{ style:'width: calc(100% - 115px)' }">
          <a-row :gutter="24">
            <a-col v-if="model.type == '5'" :xl='8' :sm='12' :xs='24'>
              <a-form-model-item label="协作人" prop="executorList">
                <a-select mode="multiple" v-model="sendform.executorList" placeholder="请选择" style="width: 100%;">
                  <a-select-option v-for="item in users" :key="item.id" :value="item.id">
                    {{item.realName}}
                  </a-select-option>
                </a-select>
              </a-form-model-item>
            </a-col>
            <a-col v-if="model.type == '5' || showEliminate" :xl='8' :sm='12' :xs='24'>
              <a-form-model-item label="主负责人" prop="liablePerson">
                <a-select v-model="sendform.liablePerson" placeholder="请选择" style="width: 100%;">
                  <a-select-option v-for="item in users" :key="item.username" :value="item.username">
                    {{item.realName}}
                  </a-select-option>
                </a-select>
              </a-form-model-item>
            </a-col>
          </a-row>
        </a-form-model>

        <template v-if="delayOrEndShow">
          <!-- 延期或终止 -->
          <delayOrEnd ref="orderDelayOrEnd" :delayOrEndType="delayOrEndType" :disabled="step > 2 || model.type == '3'" />
        </template>

        <template v-else>
          <div v-if="order.orderSource != '6'" v-show="step >= 1 || order.flowSts == '4' || (['2','3'].includes(order.flowSts) && isTangoTask)">
            <!-- 消缺方案 -->
            <div v-if=" (step >= 2 && isWorkOrder == 1 && isNeedWorkPlan) ||
              (order.flowSts == '4' && order.uploadWorkPlanFileList && order.uploadWorkPlanFileList.length > 0) ">
              <order-eliminate ref="orderEliminate" :disabled="step > 2 || model.type == '3'" />
            </div>

            <!-- 处理-执行信息 -->
            <div v-if=" ((step >= 5 && order.processDefKey == 'activiti4273Workflow') ||
                (step >= 5 && order.processDefKey == 'activiti4773Workflow') ||
                (step >= 2 && order.processDefKey == 'activiti4873Workflow') ||
                (step >= 1 && order.processDefKey == 'activiti5073Workflow') ||
                (['2','3'].includes(order.flowSts) && isTangoTask) ||
                order.flowSts == '4' || order.otherSts == '3' || order.otherSts == '4') &&
              isWorkOrder == 1 ">
              <order-dispose ref="orderDispose"
                :disabled="
                (step > 5 && order.processDefKey == 'activiti4273Workflow') ||
                model.type == '3' ||
                (step > 5 && order.processDefKey == 'activiti4773Workflow') ||
                (step > 1 && order.processDefKey == 'activiti5073Workflow') ||
                order.flowSts == '4' || order.otherSts == '4'
              "
                :isDiagnosis="order.orderSource"
                :order="order"
                :catalog="catalog"
                @setUploadStatus="setUploadStatus"
                @setLoading="setLoading" />
            </div>
          </div>
          <div v-else>
            <tango-order ref="tangoOrder" :disabled="disabled" :order="order" :type="model.type" />
          </div>
        </template>
      </a-spin>
    </div>

    <div @click="openFlowChart" class="flow-chart-btn">
      <svg-icon iconClass="flow"></svg-icon>
      流程图
    </div>

    <flow-chart-drawer
      v-if="showDiagram"
      ref="flowChartDrawer"
      :parentId="parentId"
      :processInstanceId="order.processInstanceId"
      :processDefinitionKey="order.processDefinitionKey"
      :flowUser="order.flowUser"
      :isWorkOrder="true" />

    <!-- 查看详情 -->
    <div v-if="model.type == '3'" class="drawer-form-foot">
      <throttle-button label="返回" type="info" @click="cancel" class="solar-eye-btn-primary-line" />
      <throttle-button v-if="showChange" v-show="!showEliminate && order.flowSts !='4'" label="变更" :loading="loading" @click="showEliminate = true" />
      <throttle-button v-if="showChange" v-show="showEliminate" label="确定" :loading="loading" @click="changeEliminate" />
    </div>
    <div v-else class="drawer-form-foot">
      <!-- 新增、编辑 -->
      <template v-if="['1', '2', '9', '10'].includes(model.type)">
        <throttle-button v-show="['1', '2'].includes(model.type)" label="保存" :loading="loading" @click="confirmFault('0')" />
        <throttle-button v-show="order.processDefKey != 'activiti3273Workflow'" label="提交" :loading="loading" @click="confirmFault('1')" />
      </template>
      <!-- 指派、作废 -->
      <template v-else-if="model.type == '5'">
        <throttle-button label="作废" v-if="order.orderSource != '6'" :loading="loading" @click="backNullifyEven('2')" />
        <throttle-button label="指派" :loading="loading" @click="orderSend" />
      </template>
      <!-- 领取 -->
      <template v-else-if="model.type == '6'">
        <throttle-button label="领取" :loading="loading" @click="receiveOrderEven" />
        <throttle-button label="取消" :disabled="loading" type="info" @click="cancel" />
      </template>
      <!-- 工单处理/执行 -->
      <template v-else-if="model.type == '7' && ((order.processDefKey == 'activiti5073Workflow' || step == 2 || step == 4 || step == 5) ||
          (['2','3','4'].includes(order.flowSts) && isTangoTask))  && !delayOrEndShow">
        <template v-if="order.isHangUp =='0'">
          <a-dropdown placement="topCenter" :trigger="['hover']" v-show="row.workbenchType =='58'">
            <a class="ant-dropdown-link" @click="e => e.preventDefault()">
              <a-button>更多
                <a-icon type="up" class="dark-black-color"/>
              </a-button>
            </a>
            <a-menu slot="overlay">
              <a-menu-item @click="delayOrEndEven('2')" v-show="order.orderSource !='6'">
                <span>挂起</span>
              </a-menu-item>
              <a-menu-item @click="delayOrEndEven('0')">
                <span>延期</span>
              </a-menu-item>
              <a-menu-item @click="delayOrEndEven('1')">
                <span>终止</span>
              </a-menu-item>
            </a-menu>
          </a-dropdown>
          <a-button v-show="order.processDefKey != 'activiti5073Workflow' && order.orderSource !='6'" :loading="loading"
            :disabled="isTangoTask && uploadStatus != 'completed'" @click="backNullifyEven('1')">退回</a-button>
          <throttle-button
            v-show=" order.orderSource !='6' && ((['2','3','4'].includes(order.flowSts) && isTangoTask) || (order.flowDetail.taskDefKey == 'act5' && order.processDefKey == 'activiti4273Workflow') || (order.flowDetail.taskDefKey == 'act5' && order.flowDetail.processDefKey == 'activiti4773Workflow') || (order.flowDetail.taskDefKey == 'act2' && order.processDefKey == 'activiti4873Workflow') || order.processDefKey == 'activiti5073Workflow' || order.otherSts == '3') "
            :disabled="isTangoTask && uploadStatus != 'completed'" label="保存" :loading="loading" @click="confirmOrderResolve('2')" />
          <throttle-button v-if="order.orderSource =='6' && showChange" v-show="!showEliminate" label="变更" :loading="loading" @click="showEliminate = true" />
          <throttle-button v-if="order.orderSource =='6' && showChange" v-show="showEliminate" label="确定" :loading="loading" @click="changeEliminate" />
          <a-button :disabled="isTangoTask && uploadStatus!='completed'" :loading="loading" @click="confirmOrderResolve('1')" class="ant-btn-primary"
            v-if="(order.orderSource =='6' && order.liablePerson == userInfo.username) || order.orderSource !='6'">提交</a-button>
        </template>
        <template v-else-if="order.isHangUp =='1'">
          <throttle-button v-show="row.workbenchType =='58'" :loading="loading" @click="delayOrEndEven('0')" label="延期" />
          <throttle-button v-show="row.workbenchType =='58'" :loading="loading" @click="delayOrEndEven('1')" label="终止" />
          <throttle-button v-show="row.workbenchType =='58' && order.orderSource !='6'" :loading="loading" @click="hangUp('0')" label="开启" />
        </template>
      </template>
      <!-- 站内审核、区域审核、站内验收、区域验收 -->
      <!-- <template v-else-if="['4', '5', '7', '8'].includes(order.taskDefKey)"> -->
      <template v-else-if="model.type == '4' || model.type == '8'">
        <throttle-button label="通过" :loading="loading" @click="checkOrder('1')" />
        <throttle-button label="退回" :loading="loading" @click="checkOrder('0')" />
        <throttle-button label="取消" :disabled="loading" type="info" @click="cancel" />
      </template>
      <template v-else-if="delayOrEndShow">
        <a-button :disabled="loading" @click="delayOrEndCancel">返回</a-button>
        <throttle-button v-show="delayOrEndType!='2'" label="提交" :loading="loading" @click="delayOrEndSubmit()" />
        <throttle-button v-show="delayOrEndType=='2'" label="提交" :loading="loading" @click="hangUpSubmit()" />
      </template>
    </div>

    <!-- 派发 -->
    <order-send ref="orderSend" @refresh="cancel" />

    <!-- 站内审核、区域审核、站内验收、区域验收 -->
    <flow-review v-model="showAll" :visible="showAll" :type="examineType" @reviewFlow="reviewFlow" />

    <!-- 退回、作废 弹窗 -->
    <backNullify v-model="backNullifyShow" :loading="loading" :visible="backNullifyShow" :type="backNullifyType" @backNullify="backConfirm" />
  </div>
</template>

<script>
import moment from 'moment';
import flowReview from '@/components/erp/activiti/review';
import baseDetail from './baseDetail';
import registerForm from './register';
import TangoOrder from './orderDispose/TangoOrder';
import initDict from '@/mixins/initDict';
import orderDispose from './orderDispose';
import orderEliminate from './orderEliminate';
import delayOrEnd from './orderDelayOrEnd.vue';
import { getPsaId } from '@/api/monitor/runMonitor';

import { addOrUpdate, getOrderDetail, postApprove, distributeTangoTask, performTangoTask, cancelUpload, changeLeader } from '@/api/operations/unPlanedWork';
import { cancelOrder, postOrderApprove, getWorkDetail, delayOrTerminateQuery, delayOrTerminateSponsor, hangUp } from '@/api/operations/digitalOrder';
import { getAssignListByPSId } from '@/api/common_gy/common.js';
import { getAssignor } from '@/api/tango/taskManage.js';
import { getCatalogue } from '@/api/tango/fileManage';
import { USER_INFO } from '@/store/mutation-types';
import { getOrderVersion, checkOrder } from '@/api/isolarErp/orderHall';
export default {
  name: 'orderForm',
  mixins: [initDict],
  components: {
    flowReview,
    baseDetail,
    registerForm,
    orderDispose,
    orderEliminate,
    delayOrEnd,
    TangoOrder
  },
  props: {
    dict: {
      type: Object,
      default: () => {
        return {};
      }
    },
    /* 抽屉挂在的DOM节点ID，必要参数 */
    parentId: {
      type: String,
      required: true,
      default: ''
    }
  },
  provide () {
    return {
      type: () => this.model.type,
      order: () => this.order,
      orderSource: () => this.order.orderSource
    };
  },
  data () {
    return {
      loading: false,
      kind: '1', // tabs页默认表单
      order: {
        id: undefined,
        processDefinitionKey: '非计划工作填报流程',
        disabled: false
      }, // 工单详情信息
      model: {
        show_model: false, // 是否显示模态框
        title: '缺陷管理新增',
        type: '' // type 新增：1  编辑：2  详情: 3  审批: 4  指派: 5  领取: 6  处理: 7  验收: 8 计划转工单的处理：10
      },
      users: [],
      sendform: {
        liablePerson: undefined, // 消除人
        executorList: [] // 协作人
      },
      rules: {
        liablePerson: [{
          required: true,
          message: undefined
        }],
        executorList: [{
          required: true,
          message: '请选择协作人'
        }]
      },
      backNullifyShow: false,
      backNullifyType: null,
      delayOrEndShow: false,
      delayOrEndType: null,
      // 审批、验收
      showAll: false,
      examineType: null,
      showDiagram: false,
      processInstanceId: '',
      step: '0',
      isWorkOrder: 0,
      catalog: undefined,
      uploadStatus: 'completed',
      disabled: true,
      showEliminate: false, // 是否展示变更负责人下拉框
      showChange: false, // 是否展示变更按钮,
      isTangoTask: false, // 是否为无人机自运维或第三方任务
      userInfo: {},
      row: {}
    };
  },
  computed: {
    isNeedWorkPlan () {
      return this.order.processDefKey == 'activiti4273Workflow' || this.order.processDefKey == 'activiti4773Workflow';
    },
    isEdit () {
      return (this.order.orderSource == '4') && (this.model.type == 5 || this.model.type == 6) && this.model.type != 3;
    }
  },
  methods: {
    moment,
    // 初始化 type 新增：1  编辑：2  详情: 3  审批: 4  指派: 5  领取: 6  处理: 7  验收: 8  9:告警转工单
    async init (type, row) {
      this.model.type = type;
      if (type == 1) {
        this.setRegister(type);
      } else if (type == 10) {
        this.order = Object.assign({}, this.order, this.dealRowFromPlan(row));
        this.setRegister(type, this.order);
      } else {
        this.loading = true;
        let detail = row.isOrder ? getWorkDetail : getOrderDetail;
        await detail({ id: !row.fromOrder ? row.id || row.businessId : row.workOrderId }).then((res) => {
          let resData = res.result_data;
          this.loading = false;
          this.order = Object.assign({}, this.order, resData);
          this.order.disabled = this.order.isHangUp == '1';
          this.order.stationEnvironment = this.order.stationEnvironment ? Array.from(new Set(this.order.stationEnvironment.split(','))) : [];
          this.order.uniqueType = type;
          if (this.order.taskType != '10') {
            this.order.processDefKey = resData.flowDetail ? resData.flowDetail.processDefKey : row.processDefKey;
          }
          if (this.order.orderSource == '6') {
            this.$nextTick(() => {
              this.$refs.tangoOrder.init(this.order.id);
            });
            this.userInfo = Vue.ls.get(USER_INFO);
            this.showChange = this.order.liablePerson == this.userInfo.username || this.userInfo.id == this.order.distributor;
          }
          this.step =
              resData.flowDetail && resData.flowDetail.taskDefKey
                ? Number(resData.flowDetail.taskDefKey.split('act')[1]) : 0;
          this.order.step = this.step;
          this.isWorkOrder = resData.isWorkOrder;
          this.isTangoTask = ['activiti3373Workflow', 'activiti3273Workflow'].includes(this.order.processDefKey);
          // 无人机巡检任务，数据处理
          if (this.isTangoTask) {
            if (['2', '3', '4'].includes(resData.flowSts)) {
              this.getCatalogue();
              this.sendform.liablePerson = resData.flyingHand;
            }
            if (resData.flowSts == '1' && this.order.processDefKey == 'activiti3273Workflow') {
              this.sendform.liablePerson = resData.flyingHand;
            }
            if (this.order.folderNames && this.order.patrolInspecList && this.order.folderNames.length > 0 && this.order.patrolInspecList.length > 0) {
              this.$nextTick(() => {
                this.$refs.orderDispose.initData(this.order);
              });
              this.uploadStatus = 'completed';
            }
          }
        });
        this.$nextTick(() => {
          this.setRegister(type, this.order);
        });
      }
      this.row = row;

      if (this.model.type == '5' || this.isTangoTask || this.showChange) {
        this.getUserList(this.order.psaId);
      }
      if (this.$refs.sendForm) {
        if (this.order.orderSource == '6') {
          this.$refs.sendForm.rules.liablePerson[0].message = '请选择主负责人';
        } else if (this.isTangoTask) {
          this.$refs.sendForm.rules.liablePerson[0].message = '请选择执行人';
        } else {
          this.$refs.sendForm.rules.liablePerson[0].message = '请选择责任人';
        }
      }
      return this.getTitleName(type, row);
    },
    dealRowFromPlan (row) {
      return {
        planId: row.planId,
        planCode: row.planCode,
        psaId: row.psaId,
        taskType: row.taskType,
        workSubclass: row.taskSubType,
        psaName: row.psaName,
        planStartTime: row.planStartTimeSchedule,
        planEndTime: row.planEndTimeSchedule,
        planNum: row.frequencyCount,
        taskNum: row.planOrder,
        cleanCap: row.cleanCap,
        smallPlanId: row.id,
        processInstanceId: row.processInstanceId,
        flowUser: row.flowUser
      };
    },
    getTitleName (type, row) {
      let title = '';
      switch (type) {
        case '1':
          title = '非工作计划填报';
          break;
        case '2':
          title = row.processDefKey == 'activiti3273Workflow' ? '第三方智能巡检编辑' : '非计划工作编辑';
          break;
        case '3':
          title = (row.isOrder ? '工单详情' : '非计划详情');
          break;
        case '4':
          title = row.isOrder ? '工单审批' : '非计划审批';
          break;
        case '5':
          title = '工单指派';
          break;
        case '6':
          title = '工单领取';
          break;
        case '7':
          this.disabled = false;
          if (row.isTangoUpload) {
            title = '上传巡检图';
          } else {
            title = '工单处理';
          }
          break;
        case '8':
          title = '工单验收';
          break;
        case '9':
          this.physicsPsId = row.physicsPsId;
          this.order.faultContentOptions = this.faultContentOptions = row.faultContentOptions;
          this.warningTurnDataReady(type, this.physicsPsId);
          title = '告警转工单';
          break;
        case '10':
          title = '工单填报';
          break;
      }
      if (['2', '3', '4', '5', '6', '7', '8', '9'].includes(type)) {
        title = title + '-' + this.order.workCode;
      }
      return title;
    },
    // 打开流程图
    openFlowChart () {
      this.showDiagram = true;
      this.$nextTick(() => {
        this.$refs.flowChartDrawer.openView();
      });
    },
    /**
       * 设置form 表单
       * params {type:String} 类型
       * params {register:Object} 表单详情
       */
    setRegister (type, register) {
      let self = this;
      this.$nextTick(() => {
        if (type != 1) {
          self.$refs.editRegister.initData(register, type);
        }
        setTimeout(() => {
          self.$refs.editRegister.initRegister(type == '1' ? null : register);
        }, 50);

        self.loading = false;
      });
    },
    // 告警转工单 获取发现人和电站档案匹配
    warningTurnDataReady (type, physicsPsId) {
      getPsaId({ psId: physicsPsId }).then((res) => {
        this.order.psId = res.result_data.psaId.toString();
        this.setRegister(type, this.order);
        this.loading = false;
      }).catch(() => {
        this.setRegister(type, this.order);
        this.loading = false;
      });
    },
    // 缺陷登记-新增、编辑  保存/提交
    confirmFault (type) {
      this.$refs.editRegister.getValidate(type).then((params) => {
        if (this.order.processDefKey == 'activiti3273Workflow') {
          this.$refs.sendForm.validate(valid => {
            if (valid) {
              params.taskSource = this.order.taskSource;
              params.assignor = this.sendform.liablePerson;
              this.doConfirmFault(type, params);
            }
          });
        } else {
          this.doConfirmFault(type, params);
        }
      }).catch(() => {
        this.loading = false;
      });
    },
    doConfirmFault (type, params) {
      let that = this;
      params.deviceTypeId = Array.isArray(params.deviceTypeId) ? params.deviceTypeId.join(',') : '';
      delete params.planTime;
      // params.orderSource = '7'
      if (this.model.type == '9') {
        // 非计划工单
        params.orderType = 0; // 非计划转工单 1 计划转工单 0
      } else if (this.model.type == '10') {
        // 计划转工单
        params.workOrderId = this.order.planId;
        params.smallPlanId = this.order.smallPlanId;
        params.planCode = this.order.planCode;
        params.orderType = 1; // 0 非计划转工单 1 计划转工单
        params.isWorkOrder = 1; // 非计划填报 0 计划转工单 1
      } else {
        params.isWorkOrder = 0;
        params.orderType = '';
      }
      if (this.order.processDefKey == 'activiti3273Workflow') {
        params.isWorkOrder = 1;
      }
      params.deviceNo = Array.isArray(params.deviceNo) ? params.deviceNo.join(',') : params.deviceNo;
      this.loading = true;
      params.buttonFlag = type;
      if (params.id) {
        params = Object.assign(params, this.getCommonParams());
      } else if (!params.id && this.order.id) {
        params.id = this.order.id;
      }
      params.auditStatusType = 2;
      addOrUpdate(params).then((res) => {
        this.loading = false;
        if (type == 0) {
          that.order.id = res.result_data;
        }
        this.$message.success(res.message ? res.message : '操作成功');
        this.cancel(type == 0); // 监控中心告警转缺陷回调
      }).catch(() => {
        this.loading = false;
      });
    },
    getCommonParams () {
      let order = this.row;
      return {
        id: order.id,
        taskId: order.taskId,
        taskDefKey: order.taskDefKey,
        processInstanceId: order.processInstanceId,
        processDefKey: order.processDefKey,
        otherSts: order.otherSts,
        updateTime: this.order.updateTime,
        auditStatusType: 4
      };
    },
    // 指派
    orderSend () {
      if (this.order.orderSource == '4') {
        this.getBaseParams().then((res) => {
          let map = { ...res };
          this.$refs.sendForm.validate((valid) => {
            if (valid) {
              map = Object.assign({}, map, {
                liablePerson: this.sendform.liablePerson,
                auditStatusType: 3,
                ...this.getCommonParams()
              });

              this.orderSendMethod(map);
            } else {
              this.loading = false;
              return false;
            }
          });
        });
      } else {
        this.$refs.sendForm.validate((valid) => {
          if (valid) {
            // 无人机巡检任务-指派
            if (this.isTangoTask) {
              this.sendTangoOrder('1');
            } else {
              let map = {
                liablePerson: this.sendform.liablePerson,
                auditStatusType: 3,
                ...this.getCommonParams()
              };
              if (this.order.orderSource == '6') {
                map.executorList = this.sendform.executorList;
              }
              this.orderSendMethod(map);
            }
          } else {
            this.loading = false;
            return false;
          }
        });
      }
    },
    orderSendMethod (map) {
      this.loading = true;
      postOrderApprove(map).then((res) => {
        this.loading = false;
        this.$message.success(res.message ? res.message : '操作成功');
        this.cancel();
      }).catch(() => {
        this.loading = false;
      });
    },
    // dialogn 参数
    getBaseParams () {
      let obj = {};
      return new Promise((resolve, reject) => {
        this.$refs.editRegister.getValidate(1).then((params) => {
          obj = params;
          resolve({
            uploadFileList: obj.uploadFileList,
            remark: obj.remark,
            deviceTypeName: obj.deviceTypeName,
            defectId: obj.defectId,
            orderSource: 4,
            defectType: obj.defectType.toString(),
            deviceTypeId: obj.deviceTypeId.join(','),
            predictRecoverTime: obj.predictRecoverTime,
            liablePerson: obj.liablePerson,
            erpSqJobRiskMappingId: obj.erpSqJobRiskMappingId,
            riskLevel: obj.riskLevel,
            riskJobDate: obj.riskJobDate
          });
        }).catch((err) => {
          reject(err);
        });
      });
    },
    // 获取指派人列表
    getUserList (psId) {
      // 无人机巡检任务-获取飞手列表
      if (this.isTangoTask) {
        getAssignor({}).then((res) => {
          this.users = res.result_data;
          this.loading = false;
        });
      } else {
        getAssignListByPSId({ psId: psId }).then((res) => {
          this.users = res.result;
          this.loading = false;
        }).catch(() => {
          this.users = [];
          this.loading = false;
        });
      }
    },
    // 显示退回、作废弹窗
    backNullifyEven (type) {
      this.backNullifyType = type;
      this.backNullifyShow = true;
    },
    // 显示延期或终止 填报字段
    delayOrEndEven (type) {
      this.loading = true;
      let map = {
        businessesId: this.order.id,
        type: type == '2' ? '1' : '0'
      };
      delayOrTerminateQuery(map).then((res) => {
        this.loading = false;
        if (res.result_data.isOperation) {
          this.delayOrEndType = type;
          this.delayOrEndShow = true;
        } else {
          this.$message.warning(res.result_data.errorMessage);
        }
      }).catch(() => {
        this.loading = false;
      });
    },
    // 返回
    delayOrEndCancel () {
      this.delayOrEndShow = false;
    },
    // 退回、作废回调
    backConfirm (message) {
      if (this.backNullifyType == '1') {
        this.returnOrderEven(message);
      } else if (this.backNullifyType == '2') {
        // 无人机巡检任务-作废
        if (this.isTangoTask) {
          this.sendTangoOrder('2', message);
        } else {
          this.cancelOrderEven(message);
        }
      }
    },
    // 作废
    cancelOrderEven (message) {
      let that = this;
      this.$confirm({
        content: '确认作废此工单任务吗？',
        okText: '确定',
        cancelText: '取消',
        onOk () {
          that.loading = true;
          let map = {
            auditOpinion: message,
            ...that.getCommonParams()
          };
          cancelOrder(map).then((res) => {
            that.backNullifyType = null;
            that.backNullifyShow = false;
            that.$nextTick(() => {
              that.$message.success(res.message ? res.message : '操作成功');
              that.cancel();
            });
          }).catch(() => {
            that.loading = false;
          });
        },
        onCancel () {
          that.$destroyAll();
        }
      });
    },
    // 退回
    returnOrderEven (message) {
      // 无人机巡检任务-退回
      if (this.isTangoTask) {
        let imageData = this.$refs.orderDispose.getImageData('all');
        let map = {
          processInstanceId: this.order.processInstanceId,
          psaId: this.order.taskSource == '0' ? this.order.psaId : 0,
          processDefKey: this.order.processDefKey,
          taskDefKey: this.order.taskDefKey,
          workFlowId: this.order.taskId,
          id: this.order.id,
          assign: this.order.liablePerson,
          taskSource: this.order.taskSource,
          performType: '2',
          catalogue: this.catalog,
          patrolInspecList: imageData.patrolInspecList,
          folderNames: imageData.folderNames,
          returnMessage: message
        };
        this.performTangoTask(map, '2');
      } else {
        this.loading = true;
        let map = {
          ...this.getCommonParams(),
          auditOpinion: message,
          auditStatus: '0',
          auditStatusType: 0
        };
        postOrderApprove(map).then((res) => {
          this.backNullifyType = null;
          this.backNullifyShow = false;
          this.$nextTick(() => {
            this.$message.success(res.message ? res.message : '操作成功');
            this.cancel();
          });
        }).catch(() => {
          this.loading = false;
        });
      }
    },
    // 领取
    receiveOrderEven () {
      let that = this;
      let map = this.getCommonParams();
      if (this.order.orderSource != 4) {
        this.$confirm({
          content: '确认要领取此工单任务吗？',
          okText: '确定',
          cancelText: '取消',
          onOk () {
            that.loading = true;
            map.auditStatusType = 4;
            postOrderApprove(map).then((res) => {
              that.$message.success(res.message ? res.message : '操作成功');
              that.cancel();
            }).catch(() => {
              that.loading = false;
            });
          },
          onCancel () {
            that.$destroyAll();
          }
        });
      } else {
        this.getBaseParams().then((res) => {
          that.loading = true;
          map = Object.assign(map, res);
          postOrderApprove(map).then((res) => {
            that.$message.success('领取成功');
            this.cancel();
          }).catch(() => {
            that.loading = false;
          });
        });
      }
    },
    // 方案上传或工单处理 status 3:方案上传  6:工单处理  type 0:保存  1:提交
    confirmOrderResolve (type) {
      if (this.step == '2' && this.isNeedWorkPlan) {
        this.$refs.orderEliminate.saveEliminate().then((params) => {
          this.dealWork(type, params);
        }).catch(() => {
          this.loading = false;
        });
      } else if (this.order.orderSource == '6') {
        let params = {
          id: this.order.id,
          updateTime: this.order.updateTime
        };
        this.dealWork(type, params);
      } else {
        if (this.isTangoTask) {
          type = type == '2' ? '0' : type;
        }
        this.$refs.orderDispose.saveOrderDispose(type).then((params) => {
          // 无人机巡检任务-处理（上传巡检地图）
          if (this.isTangoTask) {
            this.doUpload(type, params);
          } else {
            this.dealWork(type, params);
          }
        });
      }
    },
    // 工作方案或工作处理
    /**  工作方案或工作处理
       * type 0 保存 1 提交
       */
    dealWork (type, parmas) {
      let map = {
        auditStatus: type,
        ...this.getCommonParams(),
        auditStatusType: 2,
        ...parmas
      };
      if (this.step == 2) {
        map.buttonFlag = type;
      } else if (this.step == 4 || this.step == 5) {
        map.type = type;
      }
      map.handleResult = type == '1' ? '1' : null;
      this.loading = true;
      postOrderApprove(map).then((res) => {
        this.loading = false;
        this.$message.success(res.message ? res.message : '操作成功');
        this.cancel();
      }).catch(() => {
        this.loading = false;
      });
    },
    // 延期/终止 提交
    delayOrEndSubmit () {
      this.loading = true;
      this.$refs.orderDelayOrEnd.saveDelayOrEnd().then((params) => {
        let map = {
          erpPdcaWorkOrderId: this.order.erpPdcaWorkOrderId,
          workOrderProcInstId: this.order.processInstanceId,
          flowType: this.delayOrEndType == '1' ? '1' : '0',
          ...params
        };
        delayOrTerminateSponsor(map).then((res) => {
          this.loading = false;
          this.$message.success(res.message ? res.message : '操作成功');
          this.cancel();
        }).catch(() => {
          this.loading = false;
        });
      }).catch(() => {
        this.loading = false;
      });
    },
    // 挂起提交
    hangUpSubmit () {
      this.loading = true;
      this.$refs.orderDelayOrEnd.saveDelayOrEnd().then((params) => {
        this.hangUp('1', params);
      }).catch(() => {
        this.loading = false;
      });
    },
    // 挂起/开启
    hangUp (type, params) {
      this.loading = true;
      let map = {
        erpPdcaWorkOrderId: this.order.erpPdcaWorkOrderId,
        workOrderProcInstId: this.order.processInstanceId,
        hangUpType: type,
        ...params
      };
      hangUp(map).then((res) => {
        this.loading = false;
        this.$message.success(res.message ? res.message : '操作成功');
        this.delayOrEndShow = false;
        this.order.disabled = type == '1';
        this.order.isHangUp = type;

        this.$nextTick(() => {
          this.$refs.orderDispose.updataDispose(this.order);
        });
        // this.cancel();
      }).catch(() => {
        this.loading = false;
      });
    },
    // 显示审批弹窗
    checkOrder (type) {
      this.examineType = type; // 1、通过 0、退回
      this.showAll = true;
    },
    // 站内审核、区域审核、站内验收、区域验收
    async reviewFlow (message, type) {
      this.loading = true;
      let status = this.order.taskDefKey || this.model.type;
      let sceneCondition = this.order.sceneCondition ? this.order.sceneCondition : '';
      let map = {
        auditStatus: type,
        auditOpinion: message,
        auditStatusType: type,
        ...this.getCommonParams(),
        sceneCondition: sceneCondition
      };
      if (['4', '5'].includes(status)) {
        // 审核
        map.back = status == '5' ? '1' : '0';
        this.auditOrder(map);
        return;
      }
      // 验收
      map.auditStatusProcess = type;
      map.acceptanceStatus = type == '1' ? '7' : '8';
      if (status == '8') {
        // 区域验收
        map.acceptanceStatus = type == '1' ? '9' : '10';
        map.areaAcceptRemark = message;
      }
      // 验收操作检验工单版本
      const data = await getOrderVersion({ businessesId: map.id });
      if (data.result_data == 1) {
        checkOrder(map).then(res => {
          if (res.result_code === '1') {
            this.examineType = null;
            this.showAll = false;
            this.$message.success(res.message ? res.message : '操作成功');
            this.cancel();
          }
        }).catch(() => {
          this.loading = false;
        });
        return;
      }
      this.auditOrder(map);
    },
    // 审核、验收
    auditOrder (map) {
      let approve = this.isWorkOrder == 1 ? postOrderApprove : postApprove;
      approve(map).then((res) => {
        this.examineType = null;
        this.showAll = false;
        this.$nextTick(() => {
          this.$message.success(res.message ? res.message : '操作成功');
          this.cancel();
        });
      }).catch(() => {
        this.loading = false;
      });
    },
    // 无人机巡检任务-指派
    sendTangoOrder (auditStatus, message) {
      this.loading = true;
      let map = {
        auditStatus: auditStatus,
        processInstanceId: this.order.processInstanceId,
        processDefKey: this.order.processDefKey,
        taskDefKey: this.order.taskDefKey,
        workFlowId: this.row.taskId,
        id: this.order.id,
        taskSource: this.order.taskSource,
        psaId: this.order.psaId
      };
        // 作废
      if (auditStatus == '2') {
        map.auditOpinion = message;
        // 指派
      } else {
        map.assignor = this.sendform.liablePerson;
      }
      distributeTangoTask(map).then((res) => {
        this.loading = false;
        this.$message.success(res.message ? res.message : '操作成功');
        this.cancel();
      }).catch(() => {
        this.loading = false;
      });
    },
    // 无人机巡检任务-上传巡检图（保存、提交）
    doUpload (performType, params) {
      this.loading = true;
      let map = {
        id: this.order.id,
        psaId: this.order.taskSource == '0' ? this.order.psaId : 0,
        type: 'task',
        catalogue: this.catalog,
        patrolInspecList: params.patrolInspecList,
        folderNames: params.folderNames,
        performType: performType,
        processInstanceId: this.order.processInstanceId,
        processDefKey: this.order.processDefKey,
        taskDefKey: this.order.taskDefKey,
        workFlowId: this.order.taskId,
        taskSource: this.order.taskSource
      };
      this.performTangoTask(map, performType);
    },
    // 无人机巡检任务-上传巡检图调用接口
    performTangoTask (map, type) {
      this.loading = true;
      performTangoTask(map).then(res => {
        let message = type == '0' ? '保存成功！' : (type == '1' ? '提交成功！' : '退回成功！');
        this.$message.success(message);
        this.backNullifyShow = false;
        this.backNullifyType = null;
        this.loading = false;
        this.cancel();
      }).catch(() => {
        this.loading = false;
      });
    },
    // 无人机巡检任务-获取上传巡检图文件夹路径
    getCatalogue () {
      let data = {
        classify: 'svc',
        psaId: this.order.taskSource == '0' ? this.order.psaId : '0',
        taskId: this.order.id,
        type: 'task'
      };
      getCatalogue(data).then(res => {
        this.catalog = res.result_data.catalogue;
      });
    },
    // 无人机巡检任务-获取上传巡检图上传文件状态；表单页面关闭时需要判断是否有文件正在上传，并清除未保存的文件
    getUploadStatus () {
      // 上传巡检地图阶段
      if (this.order.flowSts == '2') {
        // 有文件正在上传时，则提示用户
        if (this.uploadStatus == 'uploading') {
          this.$confirm({
            title: '提交确认',
            content: '图片上传中，关闭后图片不保存，是否关闭？',
            okText: '确定',
            cancelText: '取消',
            onOk: async () => {
              await this.$refs.orderDispose.abortUpload();
              this.doCancelUpload();
              this.$emit('doClose');
            }
          });
        } else {
          this.doCancelUpload();
          this.$emit('doClose');
        }
      } else {
        this.$emit('doClose');
      }
    },
    setUploadStatus (uploadStatus) {
      this.uploadStatus = uploadStatus;
      if (uploadStatus == 'aborting') {
        this.loading = true;
      } else {
        this.loading = false;
      }
    },
    // 无人机巡检任务-上传巡检地图，未保存文件时，关闭页面则清除上传但未保存过的文件
    doCancelUpload () {
      if (this.order && this.$refs.orderDispose) {
        let map = {
          id: this.order.id,
          catalogue: this.catalog,
          uploadFileList: this.$refs.orderDispose.getImageData('new').patrolInspecList
        };
        cancelUpload(map).then(() => {});
      }
    },
    // 变更主负责人
    changeEliminate () {
      this.$refs.sendForm.validate(valid => {
        if (valid) {
          let obj = {
            orderId: this.order.id,
            userFrom: this.getLiablePerson(this.order.liablePerson),
            userTo: this.getLiablePerson(this.sendform.liablePerson),
            processInstanceId: this.order.processInstanceId
          };
          this.loading = true;
          changeLeader(obj).then(res => {
            this.loading = false;
            this.cancel();
          }).catch(() => {
            this.loading = false;
          });
        }
      });
    },
    // 无人机-获取主负责人id
    getLiablePerson (username) {
      let liablePerson = '';
      this.users.forEach(item => {
        if (item.username == username) {
          liablePerson = item.id;
        }
      });
      return liablePerson;
    },
    setLoading (loading) {
      this.loading = loading;
    },
    // 取消按钮事件
    cancel (istrue) {
      this.$emit('cancel', istrue);
      if (!istrue) {
        this.reset();
      }
    },
    // 数据重置
    reset () {
      let dict = { ...this.dict, ...this.dictMap };
      if (this.$refs.editRegister) {
        this.$refs.editRegister.clearValidate();
      }
      Object.assign(this.$data, this.$options.data());
      this.dictMap = dict;
    }
  }
};
</script>

<style lang="less" scoped>
  :deep(.ant-form-item) {
    width: 100%;
    display: inline-flex;
  }

  .unique-padding {
    padding: 12px 0 0;

    .drawer-form-content {
      padding-right: 12px;
    }
  }

  :deep(.ant-spin-nested-loading) {
    height: 100%;
  }

  .ant-dropdown-link {
    margin-right: 16px;
  }

  .ant-dropdown {
    .ant-dropdown-menu {
      margin-bottom: 10px;
      text-align: center;

      &::after,
      &::before {
        display: none !important;
      }
    }

    &::before,
    &::after {
      display: none;
    }
  }
</style>
