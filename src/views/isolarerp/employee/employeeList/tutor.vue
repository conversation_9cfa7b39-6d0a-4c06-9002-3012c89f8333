<template>
  <div class="tutor-model ant-input">
    <div class="title com-color" @click="showModel">{{tutorName}}</div>
    <span v-show="tutorName && !disabled" class="clear-icon">
      <a-icon @click.prevent="clearTutor" type="close-circle" />
    </span>
    <a-modal v-model="open" title="人员选择" :maskClosable="false" centered width="55%" @cancel="cancel">
      <a-spin :spinning="loading">
        <div class="search-model">
          <div class="search-item">
            <span class="demonstration">姓名</span>
            <a-input v-model="empName" @blur="empName = $trim($event)" allowClear size="default" placeholder="请输入姓名" class="width-140"></a-input>
          </div>
          <div class="search-item">
            <a-button size="default" type="primary" @click="pageChange(1)">查询</a-button>
          </div>
        </div>
        <vxe-table border ref="tutorTable" max-height="457" :data="tutorTable" resizable align="center" show-overflow highlight-hover-row
         size="small" row-id="empId" :radio-config="{highlight: true, checkRowKey: tutorId, checkMethod: checkRadioMethod}">
          <vxe-table-column type="radio" width="60"></vxe-table-column>
            <vxe-table-column show-overflow="title" field="empName" title="姓名"></vxe-table-column>
            <vxe-table-column show-overflow="title" field="empId" title="工号"></vxe-table-column>
            <vxe-table-column show-overflow="title" field="departName" title="所属部门"></vxe-table-column>
            <vxe-table-column show-overflow="title" title="职位">
              <template v-slot="{ row }">
                <span>{{getLabel(row.position,dictMap.position)}}</span>
              </template>
            </vxe-table-column>
            <template v-slot:empty>
              <span>查询无数据</span>
            </template>
          </vxe-table>
          <page-pagination :pageSize="size" :current="page" :total="total" @size-change="sizeChange"/>
      </a-spin>
      <template slot="footer">
        <div class="modal-footer">
          <a-button size="default" :loading="loading" type="primary" @click="submit">确定</a-button>
          <a-button size="default" :disabled="loading" @click="cancel">取消</a-button>
        </div>
      </template>
    </a-modal>
  </div>
</template>

<script>
import initDict from '@/mixins/initDict';
import { getTutorList } from '@/api/isolarErp/employee/employee';
export default {
  mixins: [initDict],
  model: {
    prop: 'value',
    event: 'change'
  },
  props: {
    value: {
      type: [String, Number],
      default: ''
    },
    name: {
      type: String,
      default: ''
    },
    account: {
      type: [String, Number],
      default: ''
    },
    disabled: {
      type: Boolean,
      default: false
    }
  },
  watch: {
    'value' (val, old) {
      this.tutorId = (val == null ? '' : val.toString());
      if (!val) {
        this.tutorName = '';
      }
    },
    'name' (val, old) {
      this.tutorName = val;
    }
  },
  data () {
    return {
      loading: false,
      open: false,
      tutorName: '',
      tutorId: '',
      empName: '',
      tutorTable: [],
      page: 1,
      size: 10,
      total: 0
    };
  },
  created () {
    // 加载数据字典
    this.getDictMap('position');
  },
  methods: {
    // 点击事件触发弹窗
    showModel () {
      // 非编辑状态不查询数据
      if (this.disabled || this.open) {
        return;
      }
      this.queryData();
      this.open = true;
    },
    // 回显不可选
    checkRadioMethod ({ row }) {
      if (!this.value) {
        return true;
      }
      return (row.empId != this.value);
    },
    // 确认
    submit () {
      let data = this.$refs.tutorTable.getRadioRecord();
      if (!data) {
        this.$message.warning('请选择人员');
        return;
      }
      this.tutorId = data.empId;
      this.tutorName = data.empName;
      this.$emit('change', data.empId);
      this.cancel();
    },
    // 数据查询
    async queryData () {
      this.loading = true;
      let $table = this.$refs.tutorTable;
      $table && await $table.clearScroll();
      let map = {
        account: this.account,
        empName: this.empName,
        curPage: this.page,
        size: this.size
      };
      getTutorList(map).then(res => {
        this.tutorTable = (res.result_code == '1' ? res.result_data.rows : []);
        this.total = (res.result_code == '1' ? res.result_data.total : 0);
        this.loading = false;
      });
    },
    // 清除
    clearTutor () {
      this.tutorName = '';
      this.tutorId = '';
      this.$emit('change', '');
    },
    // 分页事件
    pageChange (e) {
      this.page = e;
      this.queryData();
    },
    // 分页事件
    sizeChange (current, pageSize) {
      this.page = current;
      this.size = pageSize;
      this.queryData();
    },
    // 关闭回调
    cancel () {
      this.empName = '';
      this.tutorTable = [];
      this.page = 1;
      this.size = 10;
      this.total = 0;
      this.open = false;
    }
  }
};
</script>
<style lang="less" scoped>
  .tutor-model{
    width: 100%;
    height: 32px;
    position: relative;
    display: inline-block;
    background-image: none;
    border: 1px solid #d9d9d9;
    border-radius: 4px;
    transition: all 0.3s;
    overflow: hidden;
    margin-top: 4px;
  }
  .title{
    cursor: pointer;
    padding: 4px 11px;
    width: calc(100% - 22px);
    height: 32px;
    line-height: 1.5;
    font-size: 14px;
    overflow: hidden;
  }
  .tutor-model:hover{
    border-color: #1890FF;
  }
  .clear-icon{
    position: absolute;
    right: 8px;
    top: 0;
    z-index: 8;
    line-height: 32px;
    display: none;
  }
  .tutor-model:hover .clear-icon{
    display: block;
  }
  .clear-icon .anticon{
    font-size: 12px;
    cursor: pointer;
  }
</style>
