
<template>
  <div class="quality-device-list" style="position:relative">
    <a-card :bordered="false" class="search-card" :bodyStyle="{ height: '100%',padding:'16px' }">
      <!-- 搜索区域 -->
      <div class="table-page-search-wrapper">
        <a-form layout="inline" labelAlign="left">
          <a-row :gutter="10" align="middle">
            <a-col style="height: 32px" :md="8" :sm="24" :xxl="6" :xl="8" :lg="8">
              <a-form-item label="电站" :colon="false">
                <ps-tree-select @change="getChild" :isPsName="psName"  v-model="queryParams.psId"
                  :hasMaintenanceStatusParams="true" :isPsaDisable="true" :isQueryPs="1" />
              </a-form-item>
            </a-col>
            <a-col style="height: 32px" :md="8" :sm="24" :xxl="6" :xl="8" :lg="8">
              <a-form-item label="设备类型" :colon="false">
                <a-select class="type-select-multiple"
                          :getPopupContainer="(node) => node.parentNode"
                          v-model="queryParams.deviceTypes"
                          mode="multiple"
                          :maxTagCount="2"
                          :maxTagPlaceholder="queryParams.deviceTypes.length - 2 + '+...'"
                          allowClear
                          @change="changeType"
                          showArrow>
                  <a-select-opt-group>
                    <span slot="label" class="cursor-pointer" @click="psaTypeSelectAll">全部</span>
                    <a-select-option
                      :value="item.value" v-for="(item,index) in deviceTypeList" :key="index">
                      {{item.label}}
                    </a-select-option>
                  </a-select-opt-group>
                </a-select>
              </a-form-item>
            </a-col>
            <a-col style="height: 32px" :md="8" :sm="24" :xxl="6" :xl="8" :lg="8">
              <a-form-item label="日期" style="margin-left: 8px" :colon="false">
                <a-date-picker class="search-time-picker" placeholder="请选择日期"
                               @change="timeChange"
                               v-model="queryParams.date"
                               :getCalendarContainer="(node) => node.parentNode"
                               :disabled-date="disabledDate"
                               format="YYYY-MM-DD"
                               :allowClear="false"
                               valueFormat="YYYY-MM-DD">
                  <a-icon slot="suffixIcon" type="history"/>
                </a-date-picker>
              </a-form-item>
            </a-col>

            <div class="open-btn"></div>
          </a-row>
        </a-form>
      </div>
    </a-card>

    <a-card class="card-area" :style="{height: (height - 21 + (isShowMenu ? 0 : 124)) +'px'}">
      <vxe-table
        v-loading="pageloading"
        :data="dataSource"
        ref="multipleTable"
        class="my-table"
        @sort-change="handleTableSortChange"
        :sort-config="{ remote: true }"
        resizable
        show-overflow
        highlight-hover-row
        size="small"
        :height="height - 90 + (isShowMenu ? 0 : 124) + 'px'"
      >
<!--        <vxe-table-column type="checkbox" width="80" align="center"></vxe-table-column>-->
<!--        <vxe-table-column type="seq" width="80" title="序号" align="center"></vxe-table-column>-->
        <vxe-table-column show-overflow="title" :formatter="tabFormatter" title="设备名称" min-width="160" field="deviceName">
          <template slot-scope="scope">
            <span class="cursor-pointer" :class="readedPsKeys.includes(scope.row.psKey) ? 'solareye-color-off-line' : 'solareye-color-link'" @click="goDetail(scope.row)">{{scope.row.deviceName}}</span>
          </template>
        </vxe-table-column>
        <vxe-table-column show-overflow="title" :formatter="tabFormatter" title="问题测点数" sortable field="problemPointNum" min-width="160">
        </vxe-table-column>
        <vxe-table-column show-overflow="title" :formatter="tabFormatter" title="完整率" sortable min-width="160" field="intateRate">
        </vxe-table-column>
        <vxe-table-column show-overflow="title" :formatter="tabFormatter" title="缺失率" sortable field="defectRate" min-width="160">
        </vxe-table-column>
        <vxe-table-column show-overflow="title" :formatter="tabFormatter" title="恒值率" sortable field="constantRate" min-width="160"></vxe-table-column>
        <vxe-table-column show-overflow="title" :formatter="tabFormatter" title="越上限率" sortable field="exceedUpLimitRate" min-width="160"></vxe-table-column>
        <vxe-table-column show-overflow="title" :formatter="tabFormatter" title="越下限率" sortable field="exceedLowLimitRate" min-width="160">
        </vxe-table-column>
        <vxe-table-column show-overflow="title" :formatter="tabFormatter" title="突变率" sortable field="mutationRate" min-width="160">
        </vxe-table-column>
        <template v-slot:empty>
          <span class="com-color">查询无数据</span>
        </template>
      </vxe-table>
      <page-pagination
        :pageSize="queryParams.pageSize"
        :current="queryParams.currentPage"
        :total="total"
        @size-change="sizeChange"
      />
    </a-card>
    <device-analyse-chart-drawer :page-params="pageParams" ref="deviceAnalyseDrawer" ></device-analyse-chart-drawer>
  </div>
</template>

<script>
import moment from 'moment';
import { getDeviceTypeList } from '@/api/dataCenter/index';
import DeviceAnalyseChartDrawer from './DeviceAnalyseChartDrawer';
import { dataCenterListMixin } from '../../mixins/list';
export default {
  name: 'DeviceList',
  mixins: [ dataCenterListMixin ],
  components: { DeviceAnalyseChartDrawer },
  props: {
    pageParams: {
      type: Object
    }
  },
  data () {
    return {
      url: {
        list: '/qalityAnalysis/modelDataList'
      },
      deviceTypeList: [],
      readedPsKeys: [],
      firstLoad: false,
      psName: '',
      queryParams: {
        psId: '',
        deviceName: '',
        deviceTypes: [],
        date: '',
        pageSize: 10,
        currentPage: 1,
        order: ''
      }
    };
  },
  computed: {
    isShowMenu () {
      return this.$store.state.user.isShowMenu;
    }
  },
  async created () {
    this.firstLoad = true;
    setTimeout(() => {
      this.firstLoad = false;
    }, 400);
    this.queryParams.psId = this.pageParams.psId;
    this.queryParams.date = this.pageParams.defaultDate;
    this.psName = this.pageParams.psName;
    let res = await getDeviceTypeList();
    this.deviceTypeList = res.result_data.map(item => {
      return {
        value: item.code,
        label: item.name
      };
    });
    this.queryParams.deviceTypes = this.deviceTypeList.map(item => item.value);

    this.refreshData();
  },

  methods: {

    refreshData () {
      this.readedPsKeys = [];
      this.loadData(1);
    },

    getChild (val, node) {
      this.psName = node.name;
      this.queryParams.psId = node.id;
      this.$emit('changePageParams', {
        deviceName: this.pageParams.deviceName,
        defaultDate: this.pageParams.date,
        psId: node.id,
        psName: node.name
      });
      if (!this.firstLoad) {
        this.refreshData();
      }
    },
    goDetail (row) {
      this.$emit('changePageParams', {
        deviceName: row.deviceName,
        defaultDate: this.queryParams.date,
        psId: this.queryParams.psId,
        psName: this.psName
      });
      if (!this.readedPsKeys.includes(row.psKey)) {
        this.readedPsKeys.push(row.psKey);
      }
      this.$refs.deviceAnalyseDrawer.open({
        psId: this.queryParams.psId,
        psKey: row.psKey,
        psName: this.psName,
        deviceName: row.deviceName,
        deviceType: row.deviceType
      });
    },
    disabledDate (current) {
      // 不能选择今天及今天以后的
      return current && current > moment().subtract(1, 'days').endOf('day');
    },
    timeChange (val) {
      this.queryParams.date = val;
      this.refreshData();
    },
    changeType (val) {
      this.queryParams.deviceTypes = val;
      this.refreshData();
    },
    psaTypeSelectAll () {
      if (this.queryParams.deviceTypes.length === this.deviceTypeList.length) {
        this.queryParams.deviceTypes = [];
      } else {
        this.queryParams.deviceTypes = this.deviceTypeList.map(item => item.value);
      }
      this.$forceUpdate();
      this.refreshData();
    },
    goPage () {
      this.$emit('changePage', 4);
    }
  }
};
</script>
<style lang="less" scoped>
.card-area {
  background: #fff !important;
  padding: 16px;
}
.solareye-dark {
  .card-area {
    background: transparent !important;
  }
}
</style>
