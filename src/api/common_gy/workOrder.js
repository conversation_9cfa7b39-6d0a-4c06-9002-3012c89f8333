import {
  axios
} from '@/utils/gy-request';

export function getWorkOrderList (parameter) {
  return axios({
    url: '/erpSecWorkOrderManagement/list',
    method: 'post',
    data: parameter
  });
}
export function countOrder (parameter) {
  return axios({
    url: '/erpSecWorkOrderManagement/count',
    method: 'post',
    data: parameter
  });
}
// 工单流程节点处理
export function handleOrder (parameter) {
  return axios({
    url: '/erpSecWorkOrderManagement/handleOrder',
    method: 'post',
    data: parameter
  });
}
// 根据工单id查询缺陷详情信息接口
export function faultDetail (parameter) {
  return axios({
    url: '/erpSecWorkOrderManagement/faultDetail',
    method: 'post',
    data: parameter
  });
}

export function distributionOrder (parameter) {
  return axios({
    url: '/erpSecWorkOrderManagement/distributionOrder',
    method: 'post',
    data: parameter
  });
}

export function receiveOrder (parameter) {
  return axios({
    url: '/erpSecWorkOrderManagement/receiveOrder',
    method: 'post',
    data: parameter
  });
}

export function orderDetail (parameter) {
  return axios({
    url: '/erpSecWorkOrderManagement/manage/detail',
    method: 'post',
    data: parameter
  });
}

export function uploadOrderDoc (parameter) {
  return axios({
    url: '/erpSecWorkOrderManagement/uploadOrderDoc',
    method: 'post',
    data: parameter
  });
}

export function deleteOrder (parameter) {
  return axios({
    url: '/erpSecWorkOrderManagement/delete',
    method: 'post',
    data: parameter
  });
}

export function auditProgramme (parameter) {
  return axios({
    url: '/erpSecWorkOrderManagement/auditProgramme',
    method: 'post',
    data: parameter
  });
}

export function resolveOrder (parameter) {
  return axios({
    url: '/erpSecWorkOrderManagement/resolveOrder',
    method: 'post',
    data: parameter
  });
}

export function stationCheckOrder (parameter) {
  return axios({
    url: '/erpSecWorkOrderManagement/stationCheckOrder',
    method: 'post',
    data: parameter
  });
}

export function returnOrder (parameter) {
  return axios({
    url: '/erpSecWorkOrderManagement/returnOrder',
    method: 'post',
    data: parameter
  });
}

export function cancelOrder (parameter) {
  return axios({
    url: '/erpSecWorkOrderManagement/cancelOrder',
    method: 'post',
    data: parameter
  });
}

export function areaCheckOrder (parameter) {
  return axios({
    url: '/erpSecWorkOrderManagement/areaCheckOrder',
    method: 'post',
    data: parameter
  });
}
// 获取消除时间
export function getPlanTimeLimit (parameter) {
  return axios({
    url: '/default/management/faultDaily/getPlanTimeLimit',
    method: 'post',
    data: parameter
  });
}
// 工单管理--查询关联两票信息 工作内容
export function getTicketInfo (parameter) {
  return axios({
    url: '/erpSecWorkOrderManagement/ticket',
    method: 'post',
    data: parameter
  });
}
// 工单管理--关联两票信息作废
export function deleteTicket (parameter) {
  return axios({
    url: '/erpSecWorkOrderManagement/ticket/delete',
    method: 'post',
    data: parameter
  });
}
